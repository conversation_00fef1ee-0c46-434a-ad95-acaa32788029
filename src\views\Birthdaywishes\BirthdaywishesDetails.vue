<template>
  <div>
    <!-- <div style="padding: 20px;    position: absolute;    z-index: 99;    ">
      <a-button style="margin-bottom: 15px;" @click="$router.go(-1)">返回</a-button>
    </div> -->
    <a-form-model style="padding: 20px" :model="qxform" ref="qxform" :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18}">
      <a-row>
        <a-col span="12">
          <a-form-model-item label="届次">
            <a-input style="width: 360px" v-model="qxform.jcName" disabled="disabled" />
          </a-form-model-item>
        </a-col>
        <a-col span="12">
          <a-form-model-item label="年度">
            <a-input style="width: 360px" v-model="qxform.year" disabled="disabled" />
          </a-form-model-item>
        </a-col>

        <a-col span="12">
          <a-form-model-item label="代表姓名">
            <a-input style="width: 360px" v-model="qxform.userName" disabled="disabled" />
          </a-form-model-item>
        </a-col>
        <a-col span="12">
          <a-form-model-item label="代表身份">
            <a-input style="width: 360px" v-model="qxform.sfmc" disabled="disabled" />
          </a-form-model-item>
        </a-col>
                <a-col span="12">
                  <a-form-model-item label="生日时间">
                    <a-input style="width: 360px" v-model="qxform.birthday" disabled="disabled" />
                  </a-form-model-item>
                </a-col>
        <a-col span="12">
          <a-form-model-item label="发送时间">
            <a-input style="width: 360px" v-model="qxform.sendTime" disabled="disabled" />
          </a-form-model-item>
        </a-col>
        <a-col>
          <a-form-model-item label="短信内容"
          :label-col="{ span: 2 }"
      :wrapper-col="{ span: 18}"
          >
            <!-- <a-input style="width: 360px" v-model="qxform.content"   disabled="disabled"/> -->
            <a-textarea v-model="qxform.content"  disabled="disabled" rows="4" allow-clear  ></a-textarea>
          </a-form-model-item>
        </a-col>

        <!-- <a-col>
          <a-form-model-item :wrapper-col="{ span: 14, offset: 6 }">
            <a-button type="primary" @click="saveInfo">保存</a-button>
            <a-button
                      style="margin-left: 10px"
                      @click="tomenu('ContentList')"
                      >返回</a-button
                    >
          </a-form-model-item>
        </a-col> -->
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
import { BirthdayDetails } from "@/api/communityschedule";
import { instance_1 } from "@/api/axiosRq";
export default {
  // 个人信息
  data() {
    return {
      qxform: {
        jcDm: null,
        name: null,
        photoImage: null
      },
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "系统管理");
    this.$store.dispatch("navigation/breadcrumb2", "代表生日祝福详情");
    var id = this.$route.query.data;
    var data = {id: id};
    this.fetchData(data);
  },
  methods: {
    //列表
    fetchData(data) {
      instance_1({
        url: "/birthdayMsg/getByid",
        method: "get",
        params: data
      }).then((response) => {
        console.log(response);
        this.qxform = response.data.data;
      });
    },
    saveInfo() {

    },

  },
};
</script>

<style>
</style>
