import Layout from "@/layouts";

// {
//   path: "staging",
//   name: "InformStaging",
//   component: () => import("@/views/meeting/testPage/index"),
//   meta: {
//     title: "待办任务",
//     permissions: ["I_XTGLY", "I_XXFBY"],
//   },
// },
// 平台门户
export default [
  {
    path: "/",
    component: Layout,
    redirect: "/index",
    children: [
      {
        path: "/index",
        name: "Index",
        component: () => import("@/views/login/list"),
        meta: {
          title: "首页",
          icon: "database",
          affix: false,
        },
      },
    ],
  },
  {
    path: "/platformIndex",
    component: Layout,
    redirect: "noRedirect",
    name: "platformIndex",
    title: "平台门户",
    meta: {
      title: "首页",
    },
    children: [
      //   {
      //     path: "platformIndex",
      //     name: "platformIndex",
      //     // hidden: true,
      //     component: () => import("@/views/portalIndex/platformIndex"),
      //     meta: {
      //       title: "代表首页",
      //     },
      //   },
      // 换
      {
        path: "platformIndex",
        name: "platformIndex",
        // hidden: true,
        component: () => import("@/views/portalIndex/platformIndex"),
        meta: {
          title: "代表首页",
        },
      },
      {
        path: "workingPersonnelIndex",
        name: "workingPersonnelIndex",
        // hidden: true,
        component: () => import("@/views/portalIndex/workingPersonnelIndex"),
        meta: {
          title: "工作人员首页",
        },
      },
      // +
      {
        // path: "/platformIndex/PerformanceRecordIndex",//9999999
        path: "PerformanceRecordIndex",//---
        name: "PerformanceRecordIndex",
        // hidden: true,
        component: () => import("@/views/portalIndex/PerformanceRecordIndex"),
        meta: {
          title: "履职记录",
        },
        hidden: true,
      },
      {
        path: "/platformIndex/MessageDetails",
        name: "MessageDetails",
        component: () => import("@/views/portalIndex/MessageDetails"),
        meta: {
          title: "报表详情",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/portalIndex",
    component: Layout,
    name: "portalIndex",
    redirect: "/portalIndex/index",
    title: "",
    meta: {
      title: "门户首页",
    },
    children: [
      {
        path: "index",
        name: "index",
        hidden: true,
        component: () => import("@/views/portalIndex/index"),
        meta: {
          title: "首页",
        },
      },
    ],
  },

  {
    path: "/representative",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "平台门户",
    meta: {
      title: "常用服务",
      permissions: [
        "I_XTGLY",
        "I_DBSLXGLY",
        "I_XXFBY",
        "I_JSQAPFBY",
        "I_DBHDGLY",
        "I_MHWYGLY",
        "I_YJZJGLY",
        "I_DBLXYSY",
        "I_SSPGLY",
        "I_MHWY(LLY)SHY",
        "I_SSP(LLZ)SHY",
        "I_QRDGZRY",
        "I_SXXFBY",
        "I_QXXFBY",
        "I_SXXFBGLY",
        "I_QXXFBGLY",
        "I_JCYH",
      ],
    },
    alwaysShow: false,
    children: [
      {
        path: "InformList",
        name: "InformList",
        component: () => import("@/views/meeting/inform/list"),
        meta: {
          title: "通知公告列表",
        },
      },
      {
        path: "PerformanceRecordIndex",
        name: "PerformanceRecordIndex",
        component: () => import("@/views/portalIndex/PerformanceRecordIndex"),
        meta: {
          title: "aaa",
        },
        hidden: true
      },
      // 新增
      {
        path: "/convenTionList/excessive",
        name: "excessive",
        component: () => import("@/views/meeting/inform/excessive"),
        meta: {
          title: "空白页面",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "Newlist",
        name: "newInformList",
        component: () => import("@/views/meeting/inform/Newlist"),
        meta: {
          title: "区人大通知公告列表",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
      },
      {
        path: "/inform/edit",
        name: "InformEdit",
        component: () => import("@/views/meeting/inform/edit"),
        meta: {
          title: "新增通知公告",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/inform/edit2",
        name: "InformEdit",
        component: () => import("@/views/meeting/inform/edit2"),
        meta: {
          title: "新增通知公告2",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/inform/Data",
        name: "InformEditStatistics",
        component: () => import("@/views/meeting/inform/data"),
        meta: {
          title: "查看详情",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/inform/statistics",
        name: "InformStatistics",
        component: () => import("@/views/meeting/inform/statisticsList"),
        meta: {
          title: "通知统计",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },

      {
        path: "OpinionList",
        name: "OpinionList",
        component: () => import("@/views/meeting/opinion/list"),
        meta: {
          title: "意见征集列表",
          permissions: ["I_XTGLY", "I_YJZJGLY"],
        },
      },
      {
        path: "Newlist1",
        name: "NewOpinionList",
        component: () => import("@/views/meeting/opinion/Newlist"),
        meta: {
          title: "区人大意见征集列表",
          permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
        },
      },
      {
        path: "/opinion/edit",
        name: "OpinionEdit",
        component: () => import("@/views/meeting/opinion/edit"),
        meta: {
          title: "新增意见征集",
          permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/opinion/tongji",
        name: "OpinionTongji",
        component: () => import("@/views/meeting/opinion/tongji"),
        meta: {
          title: "征集统计",
          permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/opinion/data",
        name: "OpinionData",
        component: () => import("@/views/meeting/opinion/data"),
        meta: {
          title: "意见详情",
          permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "suishouList",
        name: "suishouList",
        component: () => import("@/views/meeting/suishou/index"),
        meta: {
          title: "我的随手拍",
          permissions: ["I_XTGLY", "I_SSPGLY"],
        },
      },
      {
        path: "QUsuishouList",
        name: "QUsuishouList",
        component: () => import("@/views/meeting/suishou/NewList"),
        meta: {
          title: "区人大随手拍",
          permissions: ["I_XTGLY", "I_SSPGLY", "I_QRDGZRY"],
        },
      },
      {
        path: "/suishou/details",
        name: "suishouDetails",
        component: () => import("@/views/meeting/suishou/details"),
        meta: {
          title: "随手拍详情",
        },
        hidden: true,
      },
      {
        path: "lianluo",
        name: "lianluosuishouList",
        component: () => import("@/views/meeting/suishou/lianluo_index"),
        meta: {
          title: "联络站随手拍",
          permissions: ["I_XTGLY", "I_SSPGLY", "I_SSP(LLZ)SHY"],
        },
      },
      {
        path: "callMe",
        name: "callMe",
        component: () => import("@/views/meeting/callMe/list"),
        meta: {
          title: "民呼我应列表",
          permissions: ["I_XTGLY", "I_MHWYGLY"],
        },
      },
      {
        path: "NewcallMe",
        name: "callMe",
        component: () => import("@/views/meeting/callMe/Newlist"),
        meta: {
          title: "区人大民呼我应列表",
          permissions: ["I_XTGLY", "I_MHWYGLY", "I_QRDGZRY"],
        },
      },
      {
        path: "checkList",
        name: "checkList",
        component: () => import("@/views/meeting/callMe/checkList"),
        meta: {
          title: "民呼我应预审列表",
          permissions: ["I_XTGLY", "I_MHWYGLY", "I_DBLXYSY"],
        },
      },
      {
        path: "censorList",
        name: "censorList",
        component: () => import("@/views/censor/index"),
        meta: {
          title: "敏感词过滤",
          permissions: ["I_XTGLY", "I_MHWYGLY", "I_DBLXYSY"],
        },
      },
      {
        path: "daibiaocallMe",
        name: "daibiaocallMe",
        component: () => import("@/views/meeting/callMe/daibiaolist"),
        meta: {
          title: "代表民呼我应列表",
          permissions: ["I_XTGLY", "I_MHWYGLY", "I_DBLXYSY"],
        },
        hidden: true,
      },
      {
        path: "lianluocallMe",
        name: "lianluocallMe",
        component: () => import("@/views/meeting/callMe/lianluolist"),
        meta: {
          title: "联络站民呼我应列表",
          permissions: ["I_XTGLY", "I_MHWY(LLY)SHY"],
        },
      },
      {
        path: "/statistics/callMeDetails",
        name: "callMe",
        component: () => import("@/views/meeting/callMe/details"),
        meta: {
          title: "民呼我应详情",
          permissions: [
            "I_XTGLY",
            "I_MHWYGLY",
            "I_DBLXYSY",
            "I_MHWY(LLY)SHY",
            "I_QRDGZRY",
          ],
        },
        hidden: true,
      },
      {
        path: "/statistics/checkDetails",
        name: "checkDetails",
        component: () => import("@/views/meeting/callMe/checkDetails"),
        meta: {
          title: "民呼我应审核详情",
          permissions: ["I_XTGLY", "I_MHWYGLY", "I_DBLXYSY", "I_MHWY(LLY)SHY"],
        },
        hidden: true,
      },
      // { // 去掉  改成在履职活动组织-所有活动列表
      //   path: "huoDongList",
      //   name: "huoDongList",
      //   component: () => import("@/views/meeting/huoDong/index"),
      //   meta: {
      //     title: "代表活动列表",
      //     permissions: ["I_XTGLY", "I_DBHDGLY"],
      //   },
      // },
      // { // 去掉  改成在履职活动组织-区人大所有活动列表
      //   path: "QUList",
      //   name: "QUhuoDongList",
      //   component: () => import("@/views/meeting/huoDong/QUindex"),
      //   meta: {
      //     title: "区人大代表活动列表",
      //     permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
      //   },
      // },

      {
        path: "/huoDong/baoming",
        name: "huoDongbaoming",
        component: () => import("@/views/meeting/huoDong/baoming"),
        meta: {
          title: "活动报名统计",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/add",
        name: "huoDongbaoming",
        component: () => import("@/views/meeting/huoDong/add"),
        meta: {
          title: "新增活动",
          // permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/newDetail",
        name: "huoDongbaoming2",
        component: () => import("@/views/meeting/huoDong/newDetail"),
        meta: {
          title: "新增活动",
          // permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/details",
        name: "huoDongdetails",
        component: () => import("@/views/meeting/huoDong/details"),
        meta: {
          title: "活动详情",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/Newedit",
        name: "huoDongdetails",
        component: () => import("@/views/meeting/huoDong/Newedit"),
        meta: {
          title: "活动编辑",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/edit",
        name: "huoDongdetails",
        component: () => import("@/views/meeting/huoDong/edit"),
        meta: {
          title: "活动详情",
          // permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/attendance",
        name: "attendance",
        component: () => import("@/views/meeting/huoDong/attendance"),
        meta: {
          title: "出勤情况",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "list",
        name: "communityscheduleList",
        component: () => import("@/views/communityschedule/list"),
        meta: {
          title: "代表进社区活动列表",
          icon: "database",
          permissions: ["I_XTGLY", "I_JSQAPFBY"],
        },
      },
      {
        path: "addAcitveList",
        name: "AddAcitveList",
        component: () => import("@/views/communityschedule/addAcitveList"),
        hidden: true,
        meta: {
          title: "新增年度活动列表",
          permissions: ["I_XTGLY", "I_JSQAPFBY"],
        },
      },
      {
        path: "planOverview",
        name: "PlanOverview",
        component: () => import("@/views/communityschedule/planOverview"),
        hidden: true,
        meta: {
          title: "本年年度活动计划安排总览",
          permissions: ["I_XTGLY", "I_JSQAPFBY"],
        },
      },
      {
        path: "activeDetail",
        name: "ActiveDetail",
        component: () => import("@/views/communityschedule/activeDetail"),
        hidden: true,
        meta: {
          title: "新增列表",
          permissions: ["I_XTGLY", "I_JSQAPFBY"],
        },
      },
      {
        path: "memberList",
        name: "MemberList",
        component: () => import("@/views/communityschedule/memberList"),
        hidden: true,
        meta: {
          title: "进社区代表列表",
          permissions: ["I_XTGLY", "I_JSQAPFBY"],
        },
      },
      {
        path: "dbslEdit",
        name: "DbslEdit",
        component: () => import("@/views/dbsl/rel"),
        meta: {
          title: "代表双联",
          permissions: ["I_XTGLY", "I_DBSLXGLY"],
        },
      },
      {
        path: "dbslList",
        name: "DbslList",
        component: () => import("@/views/dbsl/list"),
        meta: {
          title: "代表双联列表",
          permissions: ["I_XTGLY", "I_DBSLXGLY"],
        },
      },
      {
        path: "/dbslManage/dbslList",
        name: "DbslList",
        component: () => import("@/views/dbsl/list"),
        meta: {
          title: "代表双联列表",
          permissions: ["I_XTGLY", "I_DBSLXGLY"],
        },
        hidden: true,
      },
      {
        path: "Suggestions",
        name: "Suggestions",
        component: () => import("@/views/meeting/commonly/Suggestions"),
        meta: {
          title: "建议办理",
          permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },
      {
        path: "BudgetAuditing",
        name: "BudgetAuditing",
        component: () => import("@/views/meeting/commonly/BudgetAuditing"),
        meta: {
          title: "预算审核监督",
          permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },
      {
        path: "TrainingStudy",
        name: "TrainingStudy",
        component: () => import("@/views/meeting/commonly/TrainingStudy"),
        meta: {
          title: "在线学习培训",
          permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },
      {
        path: "Imeeting",
        name: "Imeeting",
        component: () => import("@/views/meeting/commonly/Imeeting"),
        meta: {
          title: "i会议",
          permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },
    ],
  },
  // { // 暂时隐藏
  //   path: "/serviceZone",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "serviceZone",
  //   title: "平台门户",
  //   meta: {
  //     title: "代表首页 (服务专区)",
  //   },
  //   children: [
  //     {
  //       path: "serviceZone",
  //       name: "serviceZone",
  //       component: () => import("@/views/meeting/testPage/index"),
  //       meta: {
  //         title: "立法工作专区",
  //       },
  //     },
  //     {
  //       path: "serviceZone1",
  //       name: "serviceZone1",
  //       component: () => import("@/views/meeting/testPage/index"),
  //       meta: {
  //         title: "联网监督专区",
  //       },
  //     },
  //     {
  //       path: "serviceZone2",
  //       name: "serviceZone2",
  //       component: () => import("@/views/meeting/testPage/index"),
  //       meta: {
  //         title: "个人履职专区",
  //       },
  //     },
  //     {
  //       path: "serviceZone3",
  //       name: "serviceZone3",
  //       component: () => import("@/views/meeting/testPage/index"),
  //       meta: {
  //         title: "会议服务专区",
  //       },
  //     },
  //   ],
  // },

  {
    path: "/staff",
    component: Layout,
    redirect: "noRedirect",
    name: "staff",
    title: "平台门户",
    meta: {
      title: "工作人员首页",
    },
    children: [
      {
        path: "staffIndex",
        name: "staffIndex",
        component: () => import("@/views/staff/staffIndex"),
        meta: {
          title: "首页",
        },
      },
      {
        path: "Management",
        name: "Management",
        component: () => import("@/views/staff/Management"),
        meta: {
          title: "管理",
        },
      },
      {
        path: "My",
        name: "My",
        component: () => import("@/views/staff/My"),
        meta: {
          title: "我的",
        },
      },
    ],
  },
];
