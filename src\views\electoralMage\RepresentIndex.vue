<template>
  <div>
    <a-row>
      <a-col :md="24" :sm="24">
        <a-card title>
          <a-row v-for="item in meetingList" :key="item.id" :gutter="[16, 16]">
            <a-col v-for="col in item.col" :key="col.id" :md="6">
              <a-card hoverable class="a-card-flex" @click="goTable(col.path)">
                <img slot="cover" :src="col.url" alt style="width: 50px;" />
                <a-card-meta :title="col.title"></a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
export default {
  name: "WorkIndex",
  components: {},
  data() {
    return {
      meetingList: [
        {
          id: 1,
          col: [
            {
              id: 1,
              title: "代表选举结果",
              color: "#be403d",
              url: require("@/assets/images/indexPage/preparatoryPerson.png"),
              path: "/ballot/entryElection",
            },
            {
              id: 2,
              title: "代表补选结果",
              color: "#be403d",
              url: require("@/assets/images/indexPage/candidate.png"),
              path: "/ballot/addition",
            },
            // {
            //   id: 3,
            //   title: "本人数据查看",
            //   color: "#be403d",
            //   url: require("@/assets/images/indexPage/expertLibraryMage.png"),
            // },
          ],
        },
      ],
    };
  },
  mounted() {},
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "代表首页");
  },
  methods: {
    goTable(path) {
      console.log("🤗🤗🤗, path =>", path);
      this.$router.push({ path: path });
    },
  },
};
</script>
