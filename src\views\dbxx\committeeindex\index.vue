/* eslint-disable */
<template>
  <div class="table-container">
    <!-- <a-button type="primary">
      新增
    </a-button>
    <a-button type="primary">
      批量导入
    </a-button>
    <a-table :columns="columns" :data-source="data" :scroll="{x : true}">
        <a slot="name" slot-scope="text">{{ text }}</a>
    </a-table> -->
    <ZTable :list="list"
            :columns="columns" />
  </div>
</template>

<script>
import { getList } from "@/api/dbxx";
const columns = [
  {
    title: "主键",
    ellipsis: true,
    dataIndex: "id",
    key: "id",
    width: 300,
    // customRender: (text, record, index) =>
    // `${(this.indexNum - 1) * this.indexNum + index + 1}`,
  },
  {
    title: "分组",
    ellipsis: true,
    dataIndex: "jgmc",
    key: "jgmc",
    width: 130,
  },
  {
    title: "代表证号",
    ellipsis: true,
    dataIndex: "committeeId",
    key: "committeeId",
    width: 100,
  },
  {
    title: "姓名",
    ellipsis: true,
    key: "name",
    dataIndex: "name",
    width: 100,
  },
  {
    title: "出生日期",
    ellipsis: true,
    key: "birthdayTime",
    dataIndex: "birthdayTime",
    width: 120,
  },
  {
    title: "届次",
    ellipsis: true,
    key: "thNum",
    dataIndex: "thNum",
    width: 100,
  },
  {
    title: "民族",
    ellipsis: true,
    key: "ethnicity",
    dataIndex: "ethnicity",
    width: 100,
  },
  {
    title: "籍贯",
    ellipsis: true,
    key: "nativePlace",
    dataIndex: "nativePlace",
    width: 100,
  },
  {
    title: "党派",
    ellipsis: true,
    key: "partyGroupings",
    dataIndex: "partyGroupings",
    width: 100,
  },
  {
    title: "加入日期",
    ellipsis: true,
    key: "joiningDate",
    dataIndex: "joiningDate",
    width: 100,
  },
  {
    title: "证件类型",
    ellipsis: true,
    key: "identityCardType",
    dataIndex: "identityCardType",
    width: 100,
  },
  {
    title: "身份证号",
    ellipsis: true,
    key: "identityNum",
    dataIndex: "identityNum",
    width: 100,
  },
  {
    title: "参加工作时间",
    ellipsis: true,
    key: "workStartTime",
    dataIndex: "workStartTime",
    width: 200,
  },
  {
    title: "职位",
    ellipsis: true,
    key: "position",
    dataIndex: "position",
    width: 100,
  },
  {
    title: "职位职称",
    ellipsis: true,
    key: "positionNichName",
    dataIndex: "positionNichName",
    width: 100,
  },
  {
    title: "学历",
    ellipsis: true,
    key: "education",
    dataIndex: "education",
    width: 100,
  },
  {
    title: "学位",
    ellipsis: true,
    key: "degree",
    dataIndex: "degree",
    width: 100,
  },
  {
    title: "毕业学校",
    ellipsis: true,
    key: "school",
    dataIndex: "school",
    width: 100,
  },
  {
    title: "系及专业",
    ellipsis: true,
    key: "professional",
    dataIndex: "professional",
    width: 100,
  },
  {
    title: "在职学历",
    ellipsis: true,
    key: "POSITION_EDUCATION",
    dataIndex: "positionEducation",
    width: 100,
  },
  {
    title: "在职学位",
    ellipsis: true,
    key: "positionDegree",
    dataIndex: "positionDegree",
    width: 100,
  },
  {
    title: "在职毕业学校",
    ellipsis: true,
    key: "positionSchool",
    dataIndex: "positionSchool",
    width: 200,
  },
  {
    title: "在职系及专业",
    ellipsis: true,
    key: "positionProfessional",
    dataIndex: "positionProfessional",
    width: 200,
  },
  {
    title: "单位",
    ellipsis: true,
    key: "unit",
    dataIndex: "unit",
    width: 100,
  },
  {
    title: "单位地址",
    ellipsis: true,
    key: "unitAddress",
    dataIndex: "unitAddress",
    width: 100,
  },
  {
    title: "单位电话",
    ellipsis: true,
    key: "unitPhone",
    dataIndex: "unitPhone",
    width: 100,
  },
  {
    title: "单位邮编",
    ellipsis: true,
    key: "unitPostcode",
    dataIndex: "unitPostcode",
    width: 100,
  },
  {
    title: "家庭地址",
    ellipsis: true,
    key: "familyAddress",
    dataIndex: "familyAddress",
    width: 100,
  },
  {
    title: "家庭电话",
    ellipsis: true,
    key: "familyPhone",
    dataIndex: "familyPhone",
    width: 100,
  },
  {
    title: "家庭邮编",
    ellipsis: true,
    key: "familyPostcode",
    dataIndex: "familyPostcode",
    width: 100,
  },
  {
    title: "手机集",
    ellipsis: true,
    key: "phoneList",
    dataIndex: "phoneList",
    width: 100,
  },
  {
    fixed: 'right',
    title: "操作",
    ellipsis: true,
    align: "center",
    width: 180,
    customRender: (text, record, index) => {
      let h = this.$createElement;
      return h("div", [
        h(
          "span",
          {
            attrs: {
              type: "text",
            },
            style: {
              cursor: "pointer",
              marginLeft: "14px",
              color: "#DB3046",
            },
            on: {
              click: () => {
                // this.switMeeting(record);
              },
            },
          },
          "查看"
        ),
        h(
          "span",
          {
            attrs: {
              type: "text",
            },
            style: {
              cursor: "pointer",
              marginLeft: "14px",
              color: "#DB3046",
            },
            on: {
              click: () => {
                // this.handleEdit(record);
              },
            },
          },
          "编辑"
        ),
        h(
          "span",
          {
            attrs: {
              type: "text",
            },
            style: {
              cursor: "pointer",
              marginLeft: "14px",
              color: "#DB3046",
            },
            on: {
              click: () => {
                // this.handleDelete(record);
              },
            },
          },
          "删除"
        ),
      ]);
    },
  },
];

export default {
  data () {
    return {
      TBloading: false,
      list: [],
      columns: [
        {
          title: "主键",
          ellipsis: true,
          dataIndex: "id",
          key: "id",
          width: 300,
          ellipsis: true,
          // customRender: (text, record, index) =>
          // `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "分组",
          ellipsis: true,
          dataIndex: "jgmc",
          key: "jgmc",
          width: 130,
        },
        {
          title: "代表证号",
          ellipsis: true,
          dataIndex: "committeeId",
          key: "committeeId",
          width: 100,
        },
        {
          title: "姓名",
          ellipsis: true,
          key: "name",
          dataIndex: "name",
          width: 100,
        },
        {
          title: "出生日期",
          ellipsis: true,
          key: "birthdayTime",
          dataIndex: "birthdayTime",
          width: 120,
        },
        {
          title: "届次",
          ellipsis: true,
          key: "thNum",
          dataIndex: "thNum",
          width: 100,
        },
        {
          title: "民族",
          ellipsis: true,
          key: "ethnicity",
          dataIndex: "ethnicity",
          width: 100,
        },
        {
          title: "籍贯",
          ellipsis: true,
          key: "nativePlace",
          dataIndex: "nativePlace",
          width: 100,
        },
        {
          title: "党派",
          ellipsis: true,
          key: "partyGroupings",
          dataIndex: "partyGroupings",
          width: 100,
        },
        {
          title: "加入日期",
          ellipsis: true,
          key: "joiningDate",
          dataIndex: "joiningDate",
          width: 100,
        },
        {
          title: "证件类型",
          ellipsis: true,
          key: "identityCardType",
          dataIndex: "identityCardType",
          width: 100,
        },
        {
          title: "身份证号",
          ellipsis: true,
          key: "identityNum",
          dataIndex: "identityNum",
          width: 100,
        },
        {
          title: "参加工作时间",
          ellipsis: true,
          key: "workStartTime",
          dataIndex: "workStartTime",
          width: 200,
        },
        {
          title: "职位",
          ellipsis: true,
          key: "position",
          dataIndex: "position",
          width: 100,
        },
        {
          title: "职位职称",
          ellipsis: true,
          key: "positionNichName",
          dataIndex: "positionNichName",
          width: 100,
        },
        {
          title: "学历",
          ellipsis: true,
          key: "education",
          dataIndex: "education",
          width: 100,
        },
        {
          title: "学位",
          ellipsis: true,
          key: "degree",
          dataIndex: "degree",
          width: 100,
        },
        {
          title: "毕业学校",
          ellipsis: true,
          key: "school",
          dataIndex: "school",
          width: 100,
        },
        {
          title: "系及专业",
          ellipsis: true,
          key: "professional",
          dataIndex: "professional",
          width: 100,
        },
        {
          title: "在职学历",
          ellipsis: true,
          key: "POSITION_EDUCATION",
          dataIndex: "positionEducation",
          width: 100,
        },
        {
          title: "在职学位",
          ellipsis: true,
          key: "positionDegree",
          dataIndex: "positionDegree",
          width: 100,
        },
        {
          title: "在职毕业学校",
          ellipsis: true,
          key: "positionSchool",
          dataIndex: "positionSchool",
          width: 200,
        },
        {
          title: "在职系及专业",
          ellipsis: true,
          key: "positionProfessional",
          dataIndex: "positionProfessional",
          width: 200,
        },
        {
          title: "单位",
          ellipsis: true,
          key: "unit",
          dataIndex: "unit",
          width: 100,
        },
        {
          title: "单位地址",
          ellipsis: true,
          key: "unitAddress",
          dataIndex: "unitAddress",
          width: 100,
        },
        {
          title: "单位电话",
          ellipsis: true,
          key: "unitPhone",
          dataIndex: "unitPhone",
          width: 100,
        },
        {
          title: "单位邮编",
          ellipsis: true,
          key: "unitPostcode",
          dataIndex: "unitPostcode",
          width: 100,
        },
        {
          title: "家庭地址",
          ellipsis: true,
          key: "familyAddress",
          dataIndex: "familyAddress",
          width: 100,
        },
        {
          title: "家庭电话",
          ellipsis: true,
          key: "familyPhone",
          dataIndex: "familyPhone",
          width: 100,
        },
        {
          title: "家庭邮编",
          ellipsis: true,
          key: "familyPostcode",
          dataIndex: "familyPostcode",
          width: 100,
        },
        {
          title: "手机集",
          ellipsis: true,
          key: "phoneList",
          dataIndex: "phoneList",
          width: 130,
        },
        {
          fixed: 'right',
          title: "操作",
          ellipsis: true,
          align: "center",
          width: 180,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.switMeeting(record);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleEdit(record);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "首页");
    this.$store.dispatch("navigation/breadcrumb2", "代表首页");
    this.fatchData();
  },
  methods: {
    fatchData () {
      getList().then((response) => {
        const list = response.data.records;
        list.forEach((e) => {
          e.thNum = "第十三届";
          e.ethnicity = "汉族";
        });
        this.list = list;
      });
    },
  },
};
</script>
