<template>
  <div>
    <a-card title="统计报表">
      <!-- <a-form layout="horizontal">
        <div>
          <a-row>
            <a-col :md="8"
                   :sm="24">
              <a-form-item label="届次"
                           :labelCol="{ span: 5 }"
                           :wrapperCol="{ span: 18, offset: 1 }">
                <a-select placeholder="请选择">
                  <a-select-option :value="item.value"
                                   v-for="item in periods"
                                   :key="item.id">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8"
                   :sm="24">
              <a-form-item label="年份"
                           :labelCol="{ span: 5 }"
                           :wrapperCol="{ span: 18, offset: 1 }">
                <a-select placeholder="请选择">
                  <a-select-option :value="item.value"
                                   v-for="item in years"
                                   :key="item.id">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8"
                   :sm="24">
              <span style="float: right; margin-top: 3px">
                <a-button type="primary">查询</a-button>
                <a-button style="margin-left: 12px;" class="pinkBoutton" >重置</a-button>
              </span>
            </a-col>
          </a-row>
        </div>
      </a-form> -->
    </a-card>
    <!-- 履职情况概览 start-->
    <a-card>
      <a-row :gutter="50">
        <a-col :md="0">
          <div id="column_container_left_lz"></div>
        </a-col>
        <a-col :md="24">
          <div id="column_container_right_lz"></div>
        </a-col>
      </a-row>
    </a-card>
    <!-- 履职情况概览 end-->
    <a-row :gutter="20">
      <a-col :md="0">
        <!-- 个人履职情况汇总 start-->
        <a-card title="个人履职情况汇总">
          <div id="line_container_lz"></div>
        </a-card>
        <!-- 个人履职情况汇总 end-->
      </a-col>
      <a-col :md="0">
        <!-- 汇总 start-->
        <a-card title="汇总">
          <a-descriptions title="召开会议"
                          bordered>
            <a-descriptions-item label="召开会议">8 次 8 节</a-descriptions-item>
            <a-descriptions-item label="受邀参加">0 次 0 节</a-descriptions-item>
            <a-descriptions-item label="实际参加">0 次 0 节</a-descriptions-item>
            <a-descriptions-item label="议案">0 件</a-descriptions-item>
            <a-descriptions-item label="建议">0 件</a-descriptions-item>
          </a-descriptions>
          <a-descriptions title="闭会期间举行活动"
                          style="margin-top: 60px; padding-bottom: 35px"
                          bordered>
            <a-descriptions-item label="举行活动">357 次 434 节</a-descriptions-item>
            <a-descriptions-item label="受邀参加">0 次 0 节</a-descriptions-item>
            <a-descriptions-item label="实际参加">0 次 0 节</a-descriptions-item>
            <a-descriptions-item label="请假">0 件</a-descriptions-item>
            <a-descriptions-item label="缺席">0 件</a-descriptions-item>
          </a-descriptions>
        </a-card>
        <!-- 汇总 end-->
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { Column, Line } from "@antv/g2plot";
export default {
  name: "Index",
  components: {},
  data () {
    return {
      TBloading: false,
      years: [
        // 年份
        { id: 1, name: "2020年", value: "2020" },
        { id: 2, name: "2019年", value: "2019" },
        { id: 3, name: "2018年", value: "2018" },
        { id: 4, name: "2017年", value: "2017" },
      ],
      periods: [
        // 届次
        { id: 1, name: "第十五届", value: "15" },
        { id: 2, name: "第十四届", value: "14" },
        { id: 3, name: "第十三届", value: "13" },
        { id: 4, name: "第十二届", value: "12" },
      ],
    };
  },
  mounted () {
    this.initLine();
    this.initLeftColumn();
    this.initRightColumn();
  },

  methods: {
    // 初始化代表个人履职情况概览左侧柱状图
    initLeftColumn () {
      const data = [
        { name: "总计", label: "市人民代表大会会议数", value: 12 },
        { name: "总计", label: "市人民代表大会分组会议数", value: 12 },
        { name: "个人", label: "市人民代表大会会议数", value: 9 },
        { name: "个人", label: "市人民代表大会分组会议数", value: 9 },
      ];
      const columnPlot = new Column("column_container_left_lz", {
        data,
        isGroup: true,
        xField: "label",
        yField: "value",
        seriesField: "name",
        color: ["#DB3046", "#fbeeef"],
        label: {
          position: "middle",
          layout: [
            { type: "interval-adjust-position" }, // 柱状图数据标签位置自动调整
            { type: "interval-hide-overlay" }, // 数据标签防遮挡
            { type: "adjust-color" }, // 数据标签颜色自动调整
          ],
        },
        xAxis: {
          label: {
            autoHide: false,
            autoRotate: true,
          },
        },
      });

      columnPlot.render();
    },
    // 初始化代表个人履职情况概览右侧柱状图
    initRightColumn () {
      const data = [
        { label: "民族", value: 122 },
        { label: "户籍", value: 154 },
        { label: "党派", value: 78 },
        { label: "全日制", value: 29 },
        { label: "工作单位", value: 32 },
        { label: "职业构成", value: 150 },
        { label: "综合构成", value: 90 },
        { label: "推荐单位", value: 79 },
        { label: "选举单位", value: 33 },
        { label: "所在联组", value: 58 },
        // { label: "听取工作报告数量", value: 178 },
        // { label: "专题询问会", value: 32 },
        // { label: "主任会议组成人员接待数量", value: 92 },
        // { label: "专门委员会会议数量", value: 115 },
        // { label: "其他数量", value: 78 },
        // { label: "主任会议组成人员接待代表日活动", value: 12 },
        // { label: "专门委员会会议", value: 7 },
        // { label: "其他", value: 6 },
        // { label: "议案", value: 9 },
        // { label: "建议", value: 7 },
      ];
      const columnPlot = new Column("column_container_right_lz", {
        data,
        xField: "label",
        yField: "value",
        color: "#DB3046",
        label: {
          position: "middle",
          layout: [
            { type: "interval-adjust-position" }, // 柱状图数据标签位置自动调整
            { type: "interval-hide-overlay" }, // 数据标签防遮挡
            { type: "adjust-color" }, // 数据标签颜色自动调整
          ],
        },
        xAxis: {
          label: {
            autoHide: false,
            autoRotate: true,
          },
        },
        meta: {
          label: {
            alias: "类别",
          },
          value: {
            alias: "次数",
          },
        },
      });

      columnPlot.render();
    },
    // 初始化个人履职情况汇总拆线图
    initLine () {
      const data = [
        { year: "2017", value: 1 },
        { year: "2018", value: 4 },
        { year: "2019", value: 5 },
        { year: "2020", value: 4 },
        { year: "2021", value: 8 },
      ];

      const LinePlot = new Line("line_container_lz", {
        data,
        xField: "year",
        yField: "value",
        label: {},
        color: "#be403d",
        point: {
          size: 5,
          shape: "diamond",
          style: {
            fill: "white",
            stroke: "#fbeeef",
            lineWidth: 2,
          },
        },
      });
      LinePlot.render();
    },
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "当选管理");
    this.$store.dispatch("navigation/breadcrumb2", "统计报表");
  },
};
</script>

<style></style>
