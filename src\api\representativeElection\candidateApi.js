import { instance_1  } from "@/api/axiosRq";
import requestTemp from "@/utils/requestTemp.js";
import { data } from "jquery";

// 候选人确认获取列表
export function getViewDqrhxr (data) {
  // let form = new FormData();
  // for (const key in data) {
  //   form.append(key, data[key]);
  // }
  // console.log("form", form);
  return instance_1({
    url: `/hxrqrbs/queryDqrhxr`,
    method: "get",
    params: data,
  });
}
// 议案建议列表导出EXCEL
export function ExportExcelData (form) {
  return instance_1({
    url: `/activitySignedMember/export`,
    method: "get",
    responseType: "blob",
    params: form,
  });
}
//查询所有的申报表
export function getfindAll (params) {
  return instance_1({
    url: `/candidateApplication/findAll`,
    method: "get",
    params,
  });
}
//查询所有申报表
export function getselectCandidateApplication (data) {
  return instance_1({
    url: `/candidateApplication/selectCandidateApplication`,
    method: "post",
    params: data,
  });
}
//获取当前届次下拉数据列表
export function getLevelListApi (params) {
  return instance_1({
    url: `/dictCode/common/getLevelList`,
    method: "get",
    params,
  });
}
//获取表单状态下拉数据列表
export function getFormStateListApi (data) {
  return instance_1({
    url: `/dictCode/common/getFormStateList`,
    method: "get",
    params: data,
  });
}
//登记一条候选人申报表记录
export function getsaveApi (data) {
  return instance_1({
    url: `/candidateApplication/save`,
    method: "post",
    data,
  });
}
// 登记表表单查询
export function getselectApi (params) {
  return instance_1({
    url: `/hxrdjb/CandidateApplication/select`,
    method: "post",
    params,
  });
}
// 确认表表单查询
export function getCandiDateverifyApi (data) {
  return instance_1({
    url: `/hxrqrbs/CandiDateverify/select`,
    method: "post",
    params: data,
  });
}
// 新增一条登记表记录
export function getinsertApi (data) {
  return instance_1({
    url: `/hxrdjb/insert`,
    method: "post",
    data,
  });
}

// 获取出党派下拉数据列表
export function getPoliticalApi (params) {
  return instance_1({
    url: `/dictCode/common/getPolitical`,
    method: "get",
    params,
  });
}
// 获取出民族下拉数据列表
export function getNationApi (params) {
  return instance_1({
    url: `/dictCode/common/getNation`,
    method: "get",
    params,
  });
}
//获取出职业构成下拉数据列表
export function getOccupationApi (params) {
  return instance_1({
    url: `/dictCode/common/getOccupation`,
    method: "get",
    params,
  });
}
//获取在职教育学历下拉数据列表
export function getEducationStateListApi (params) {
  return instance_1({
    url: `/dictCode/common/getEducationStateList`,
    method: "get",
    params,
  });
}
//获取在职教育学历下拉数据列表
export function getDegreeStateListApi (params) {
  return instance_1({
    url: `/dictCode/common/getDegreeStateList`,
    method: "get",
    params,
  });
}
//获取全日制教育学历
export function getFulltimeApi (params) {
  return instance_1({
    url: `/dictCode/common/getFulltime`,
    method: "get",
    params,
  });
}
// 获取全日制教育学位
export function getQualificationApi (params) {
  return instance_1({
    url: `/dictCode/common/getQualification`,
    method: "get",
    params,
  });
}
// 获取出推荐单位下拉数据列表，
export function getRecommendApi (params) {
  return instance_1({
    url: `/dictCode/common/getRecommend`,
    method: "get",
    params,
  });
}
// 根据上级获取出推荐单位下拉数据列表
export function getRecommendSonApi (params) {
  return instance_1({
    url: `/dictCode/common/getRecommendSon`,
    method: "get",
    params: { id: params },
  });
}
//获取家庭关系
export function getFamilytiesApi (params) {
  return instance_1({
    url: `/dictCode/common/getFamilyties`,
    method: "get",
    params,
  });
}
//获取模板下载
export function templateDownloadApi (params) {
  return instance_1({
    url: `/hxrdjb/templateDownload`,
    method: "get",
    params,
    responseType: "blob",
  });
}
//保存导入图片
export function leaveuploadApi (fromData, id) {
  return instance_1({
    url: `/hxrdjb/upload`,
    method: "post",
    data: fromData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    // responseType: "blob",
  });
}
//保存导入图片
export function dbleaveuploadApi (fromData, id) {
  return instance_1({
    url: `personalDetails/upload`,
    method: "post",
    data: fromData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    // responseType: "blob",
  });
}

// 导入(上传)登记表文件
export function uploadHxrdjbApi (fromData, id) {
  return instance_1({
    url: `/hxrdjb/uploadHxrdjb`,
    method: "post",
    data: fromData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
//分配表表单查询
export function gebdQueryApi (params) {
  return instance_1({
    url: `/hxrfpbs/fpbbdcx/bdQuery`,
    method: "post",
    params,
  });
}

//分页综合查询
export function getfindallselectApi (data) {
  return instance_1({
    url: `/ComprehensiveSelect/findall/select`,
    method: "post",
    params: data,
  });
}

// 登记一条候选人申报表记录
export function getcandidateApplicationtsaveApi (data) {
  return instance_1({
    url: `/candidateApplication/save`,
    method: "post",
    data,
  });
}

//模板下载
export function getDownloadApi (params) {
  return instance_1({
    url: `/candidateApplication/download`,
    method: "get",
    params,
    responseType: "blob",
  });
}

//导入申报表文件
export function uplaodHxrsbbsApi (fromData, id) {
  return instance_1({
    url: `/candidateApplication/uplaodHxrsbbs`,
    method: "post",
    data: fromData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
// 上传一份候选人申报表扫描原件
export function scanuploadApi (fromData, id) {
  return instance_1({
    url: `/scan/upload`,
    method: "post",
    data: fromData,
    // headers: {
    //   "Content-Type": "multipart/form-data",
    // },
  });
}

// //登记一条候选人分配表记录
export function getassignApi (params) {
  return instance_1({
    url: `/hxrfpbs/assign`,
    method: "post",
    params: { xzqhDm: params },
  });
}
//登记一条候选人分配表记录
// export function getassignApi(xzqhDm) {
//   return instance_1({
//     url: `/hxrfpbs/assign/${xzqhDm}`,
//     method: "post",
//   });
// }

// 下载扫描原件
export function getxzloadApi (params) {
  return instance_1({
    url: `/scan/download`,
    method: "get",
    params: { id: params },
    responseType: "blob",
  });
}
// 上传扫描原件在线预览
export function getimgPreviewApi (params) {
  return instance_1({
    url: `/scan/imgPreview/${params}`,
    method: "get",
    params: { id: params },
    responseType: "blob",
  });
}
//删除一份候选人申报表扫描原件
export function scDeleteApi (params) {
  return instance_1({
    url: `/scan/delete/${params}`,
    method: "POST",
    params: { id: params },
  });
}

//候选确定--登记页面数据（左）
export function getCandidateConfirmEditApi (data) {
  return instance_1({
    url: `/dmcs/dbhj/hxr/queryYbhxr`,
    method: "post",
    params: data,
  });
}
//待确定候选人--登记页面数据（右）
export function getWaitConfirmEditApi (data) {
  return instance_1({
    url: `/hxrqrbs/viewDqrhxr`,
    method: "post",
    params: data,
  });
}
//候选人确认 修改 左to右
export function getSavetoApi (data) {
  return instance_1({
    url: `/hxrqrbs/save`,
    method: "post",
    params: data,
  });
}
//候选人确认 修改 左to右
export function candidateConfirmation (data) {
  return instance_1({
    url: `/hxrqrbs/save`,
    method: "post",
    data,
  });
}
//候选人确认 获取流水号
export function getAWater (id) {
  // let id = form.id;
  return instance_1({
    url: `/hxrqrbs/findById/${id}`,
    method: "get",
    // data,
  });
}
//取消待确认候选人
export function cancelToConfirmCandidates (id, data) {
  return instance_1({
    url: `hxrqrbs/revert/${id}`,
    method: "post",
    data: data
  });
}
//预备人选基本情况申报送审
export function getprocessApi (params) {
  return instance_1({
    url: `/wfe/process`,
    // url: `/wfe/process/?procInstId=165001`,
    method: "get",
    params: { procInstId: params },
    // params,
  });
}
//预备人选基本情况申报查询流程审核常用联系人
export function gethistoryAssigneesApi (params) {
  return instance_1({
    url: `/wfe/historyAssignees`,
    method: "get",
    params,
  });
}
// /获取流程组织树与用户
export function getfindApprovableODUApi (params) {
  return instance_1({
    url: `/dmcs/common/ours/findApprovableODU`,
    method: "get",
    params,
  });
}
//更新或完成流程状态
export function getcompleteApi (data) {
  return instance_1({
    url: `/candidateApplication/complete`,
    method: "post",
    data,
  });
}
//获取流程候选人
export function getgetNextUsersApi (params) {
  return instance_1({
    url: `/flowAble/getNextUsers`,
    method: "get",
    params,
  });
}

//查询一条申报表记录 查看
export function getcandidateApplicationgetByIdApi (params) {
  return instance_1({
    url: `/candidateApplication/getById`,
    method: "get",
    params: { id: params },
  });
}
//查询一条登记表记录
export function getcandidateAppfindApi (id) {
  return instance_1({
    url: `/hxrdjb/find/${id}`,
    method: "get",
  });
}

//补选结果表单查询
export function getAddeDselecselectApi (data) {
  return instance_1({
    url: `/bxjg/AddeDselec/select`,
    method: "post",
    params: data,
  });
}
//获取选举单位,行政区划
export function getAdministrativeStateListApi (params) {
  return instance_1({
    url: `/dictCode/common/getAdministrativeStateList`,
    method: "get",
    params,
  });
}
//获取会议名称和时间下拉数据列表
export function getcommongetAddedselecListApi (params) {
  return instance_1({
    url: `/dictCode/common/getAddedselecList`,
    method: "get",
    params,
  });
}
//登记一条补选结果记录
export function registerApi (data) {
  return instance_1({
    url: `/bxjg/register`,
    method: "post",
    data,
  });
}

// 选举结果和补选结果的搜索列表
export function getfindByConditionPageableApi (data) {
  return instance_1({
    url: `/dmcs/dbhj/hxr/findByConditionPageable`,
    method: "post",
    params: data,
  });
}
//查询补选结果表单回显
export function getbyElectionInfogetByIdApi (data) {
  return instance_1({
    url: `/bxjg/byElectionInfo`,
    method: "get",
    params: data,
  });
}

//候选人确认表回显
//查询补选结果表单回显
export function getfindbyidgetByIdApi (id) {
  return instance_1({
    url: `/hxrqrbs/findById/${id}`,
    method: "get",
  });
}
//录入选举结果列表
export function getdbxjjgqueryApi (params) {
  return instance_1({
    url: `/hjbd/dbxjjg/xjjg/query`,
    method: "post",
    params,
  });
}
//录入选举结果列表 hjbd/dbxjjg/register
export function dbxjjgregisterApi (data) {
  return instance_1({
    url: `/hjbd/dbxjjg/register`,
    method: "post",
    data,
  });
}
// 查询补选结果表单回显
export function getdbxjjgfindbyidgetByIdApi (data) {
  return instance_1({
    url: `/hjbd/dbxjjg/voteInfo`,
    method: "post",
    params: data,
  });
}
//获取决定单位下拉数据列表
export function getgetVerifyUnitListApi (params) {
  return instance_1({
    url: `/dictCode/common/getVerifyUnitList`,
    method: "get",
    params,
  });
}
//获取选举情况下拉数据列表
export function getElectionStateListApi (params) {
  return instance_1({
    url: `/dictCode/common/getElectionStateList`,
    method: "get",
    params,
  });
}
//获取当选情况下拉数据列表
export function getdictCodeElectionStateListApi (params) {
  return instance_1({
    url: `/dictCode/common/getElectedStateList`,
    method: "get",
    params,
  });
}
// 20220225hjt改 start
//按条件和分页查询待分配候选人
export function getdfphxrApi (form) {
  let formData = new FormData();
  for (const key in form) {
    formData.append(key, form[key]);
  }
  return instance_1({
    url: `/hxrfpbs/query/dfphxr`,
    method: "post",
    data: formData,
  });
}
//按条件和分页查询已分配候选人
export function getyfphxrApi (form) {
  let formData = new FormData();
  for (const key in form) {
    formData.append(key, form[key]);
  }
  return instance_1({
    url: `/hxrfpbs/query/yfphxr`,
    method: "post",
    data: formData,
  });
}
//已有 已分配候选人调此接口
export function hxrfpbsUpdate (form) {
  return instance_1({
    url: `/hxrfpbs/update/${form.xzqhDm}`,
    method: "post",
    data: form.formData,
  });
}
//无 已分配候选人调此接口
export function hxrfpbsAssign (form) {
  return instance_1({
    url: `/hxrfpbs/assign/${form.xzqhDm}`,
    method: "post",
    data: form.formData,
  });
}
//分配候选人 送审接口
export function behalfVacantcompleteApi (data) {
  return instance_1({
    url: `/hxrfpbs/complete`,
    method: "post",
    data,
  });
}
//分配代表团 左边代表团
export function sdbAndDbtbList (form) {
  let formData = new FormData();
  for (const key in form) {
    formData.append(key, form[key]);
  }
  return instance_1({
    url: `/fpdbts/query/sdbAndDbtbList`,
    method: "post",
    data: formData,
  });
}
//分配代表团 右边代表团
export function newDbtdbList (form) {
  let formData = new FormData();
  for (const key in form) {
    formData.append(key, form[key]);
  }
  return instance_1({
    url: `/fpdbts/query/newDbtdbList`,
    method: "post",
    data: formData,
  });
}
//分配代表团 表单查询
export function bdcxQueryApi (params) {
  return instance_1({
    url: `/fpdbts/Fpdbtbdcx/select`,
    method: "post",
    params,
  });
}
//分配代表团 第一次分配代表团
export function fpdbtsCreate (form) {
  return instance_1({
    url: `/fpdbts/create/${form.dbtDm}`,
    method: "post",
    data: form.formData,
  });
}
//分配代表团 第二次分配代表团
export function fpdbtsUpdate (form) {
  return instance_1({
    url: `/fpdbts/update/${form.dbtDm}`,
    method: "post",
    data: form.formData,
  });
}
//分配代表团 送审
export function fpdbtsComplete (data) {
  return instance_1({
    url: `/fpdbts/complete`,
    method: "post",
    data,
  });
}

// 20220225hjt改 end

//待办事项
export function gettaskTwoApi (data) {
  return instance_1({
    url: `/taskTwo/todo`,
    method: "post",
    params: data,
  });
}
//修改选举结果
export function getdbxjupdateApi (id, data) {
  return instance_1({
    url: `/hjbd/dbxjjg/update/${id}`,
    method: "put",
    data,
  });
}
// export function getfindbyidgetByIdApi(id) {
//   return instance_1({
//     url: `/hxrqrbs/findById/${id}`,
//     method: "get",
//   });
// }
//查询经办事项
export function gethandledApi (data) {
  return instance_1({
    url: `/taskTwo/handled`,
    method: "post",
    params: data,
  });
}
//修改一条候选人申报表记录
export function getcandidateApplicationupdateApi (data) {
  return instance_1({
    url: `/candidateApplication/update`,
    method: "put",
    data,
  });
}
//删除一条候选人申报表记录
export function getcandidateApplicationdeleteApi (data) {
  return instance_1({
    url: `/candidateApplication/delete`,
    method: "delete",
    params: { id: data },
  });
}
//修改一条候选人登记表记录
export function gethxrdjbupdateApi (data) {
  return instance_1({
    url: `/hxrdjb/update`,
    method: "post",
    data,
  });
}
//删除一条候选人申报表记录
export function gethxrdjbdeleteApi (id) {
  return instance_1({
    url: `/hxrdjb/delete/${id}`,
    method: "delete",
    // params: { id: data },
  });
}
//  修改一条补选结果记录
export function getbxjgbupdateApi (data) {
  return instance_1({
    url: `/bxjg/update`,
    method: "post",
    data,
  });
}
//确认表回显
export function getfindByIdupdateApi (id) {
  return instance_1({
    url: `hxrqrbs/findById/${id}`,
    method: "get",
    // params: { id: data },
  });
}
// 确认表表单查询
export function gethxrqrbsqueryDqrhxrApi (data) {
  return instance_1({
    url: `hxrqrbs/queryDqrhxr`,
    method: "get",
    params: data,
  });
}
//修改候选人确认表
export function gethxrqrbsupdateApi (id, data) {
  return instance_1({
    url: `/hxrqrbs/update/${id}`,
    method: "put",
    data,
  });
}

//删除代表出缺
export function getRepresentdeleteApi (id) {
  return instance_1({
    url: `/Represent/delete/${id}`,
    method: "delete",
    // params: { id: data },
  });
}
//删除一条候选人分配表记录
export function gethxrfpbsesentdeleteApi (id) {
  return instance_1({
    url: `/hxrfpbs/delete/${id}`,
    method: "delete",
    // params: { id: data },
  });
}
//删除分配代表团
export function getfpdbtsdeleteApi (id) {
  return instance_1({
    url: `/fpdbts/delete/${id}`,
    method: "delete",
    // params: { id: data },
  });
}
//删除候选人确认表
export function gethxrqrbsdeleteApi (id) {
  return instance_1({
    url: `/hxrqrbs/delete/${id}`,
    method: "delete",
    // params: { id: data },
  });
}
//修改代表出缺
export function getRepresentupdateApi (data) {
  return instance_1({
    url: `/Represent/update`,
    method: "post",
    data,
  });
}
// 分配表表单查询
export function getnewDbtdbListApi (data) {
  return instance_1({
    url: `/fpdbts/query/newDbtdbList`,
    method: "post",
    params: data,
  });
}
// 代表团
export function getgetDelegationApi (params) {
  return instance_1({
    url: `/dictCode/common/getDelegation`,
    method: "get",
    params,
  });
}
//获取候选人分配表, 根据确认表流水号
export function gethxrfpbsfindbyidgetByIdApi (id) {
  return instance_1({
    url: `/hxrfpbs/findbyid/${id}`,
    method: "get",
  });
}
//获取分配代表团，根据分配代表团流水号
export function getfpdbtsByIdApi (id) {
  return instance_1({
    url: `/fpdbts/findById/${id}`,
    method: "get",
  });
}
//补选单位
export function getgetAdministrativeRankStateListApi (params) {
  return instance_1({
    url: `dictCode/common/getAdministrativeRankStateList`,
    method: "get",
    params,
  });
}
//修改一条候选人分配表记录
// export function gethxrfpbsupdateApi(xzqhDm, data) {
//   return instance_1({
//     url: `/hxrfpbs/update/${xzqhDm}`,
//     method: "post",
//     data,
//   });
// }
export function gethxrfpbsupdateApi (form) {
  return instance_1({
    url: `/hxrfpbs/update/${form.xzqhDm}`,
    method: "post",
    data: form.formData,
  });
}
//候选人分配表左移右
export function getcancelAssignApi (form) {
  return instance_1({
    url: `hxrfpbs/cancelAssign/${form.id}`,
    method: "delete",
    data: form.formData,
  });
}
//分配代表团左移右
export function getrevertApi (form) {
  console.log(form.allotDelegation);
  return instance_1({
    url: `fpdbts/revert`,
    method: "post",
    data: form.formData,
  });
}

//代表基本情况统计报表
// export function getrevertApi(form) {
//   return instance_1({
//     url: `fpdbts/revert`,
//     method: "post",
//     data: form.formData,
//   });
// }
//流程流转记录
export function getgetHistoryTasksApi (params) {
  return instance_1({
    url: `/wfe/getHistoryTasks`,
    method: "get",
    params: { procInstId: params },
    // params,
  });
}

//补录报名详情接口
export function getdetailsApi (params) {
  return instance_1({
    url: `/activity/details`,
    method: "get",
    params: { id: params },
    // params,
  });
}

//补录报名确定接口
export function getinsertByListApi (data) {
  return instance_1({
    url: `/activitySignedMember/insertByList`,
    method: "post",
    data,
    // params,
  });
}

// 保存社情民意信息
export function newPublicOpinionControllerSave (data) {
  return instance_1({
    url: `/newPublicOpinionController/save`,
    method: "post",
    data,
    // params,
  });
}
export function newPublicOpinionControllerUpdate (data) {
  return instance_1({
    url: `/newPublicOpinionController/update`,
    method: "post",
    data,
    // params,
  });
}
// 编辑社情民意信息
export function getByCommunityScheduleId (communityScheduleId) {
  return instance_1({
    url: `/newPublicOpinionController/getByCommunityScheduleId`,
    method: "get",
    params: { communityScheduleId },
  });
}
// 统计报名情况
export function getStatisticsNum (params) {
  return requestTemp({
    url: `/activityMember/countApplyPeople`,
    method: "get",
    params
  });
}

// 出缺送审
export function representComplete (data) {
  return instance_1({
    url: `/Represent/complete`,
    method: "post",
    data,
  });
}

export function listSignedMember(params,data) {
  return requestTemp({
    url: `/activityMember/listSignedMember`,
    method: "post",
    params,
    data
  });
}
