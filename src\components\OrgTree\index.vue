<template>
    <a-modal
      :title="orgTreeDialogTitle"
      :visible.sync="orgTreeDialogVisible"
      width="800px"
      @cancel="close"
    >
      <a-input-search
        v-model="filterText"
        placeholder="输入关键字进行过滤"
        style="width: 100%;"
        @search="onChange"
      ></a-input-search>
      <a-spin :indicator="indicator" :spinning="spinningShow">
        <a-tree
          ref="orgTree"
          v-model="userTreeDefaultKey"
          :checkable="checkable"
          selectable
          :checkStrictly="checkStrictly"
          :expanded-keys="expandedKeys"
          :replace-fields="replaceFields"
          :auto-expand-parent="autoExpandParent"
          :tree-data="orgTreeData"
          @expand="onExpand"
          @select="onSelect"
          @check="onChecked"
        >
          <template slot="title" slot-scope="{ name, orgLevel }">
            <a-icon
              :type="orgLevel == '1' ? 'apartment' : 'file'"
              style="margin-right: 10px;"
            />
            <span
              v-if="searchValue && name.indexOf(searchValue) > -1"
               style="color: #f50;background-color: #f5f5f5;padding-right:20vw;"
              class="fullName"
              >{{ name }}</span
            >
            <span v-else>{{ name }}</span>
          </template>
        </a-tree>
      </a-spin>
      <span slot="footer" class="dialog-footer">
        <a-button @click="close">取 消</a-button>
        <a-button type="primary" @click="confirm">确 定</a-button>
      </span>
    </a-modal>
  </template>
  
  <script>
  // import { getTree as getOrgTree } from "@/api/organization";
   import { findTreeByCode } from "@/api/user";
  
  export default {
    props: {
      rootOrgId: {
        type: String,
        default: "root",
      },
      orgTreeDialogTitle: {
        type: String,
        default: "选择单位",
      },
      checkStrictly:  {
        type: Boolean,
        default: false,
      },
      checkable:  {
        type: Boolean,
        default: false,
      }
    },
    data() {
      return {
        spinningShow: false,
        indicator: <a-icon type="loading" style="font-size: 24px" spin />,
        filterText: "",
        orgTreeDialogVisible: false,
        checkedShow: false,
        orgTreeData: [],
        orgTreeDefaultKey: [],
        replaceFields: {
          title: "name",
          key: "id",
          children: "children",
        },
        checked: [],
        checkIdentyData: [],
        userTreeDefaultKey: [],
        allChildData: [],
        selectedKeys: [],
        // 搜索值
        searchValue: "",
        // 展开的节点
        expandedKeys: [],
        backupsExpandedKeys: [],
  
        // 是否自动展开
        autoExpandParent: true,
        parentList: [], //缓存父节点
        halfCheckedKeys: []
      };
    },
    watch: {
      filterText(val) {},
    },
    created() {
      this.initOrgTree();
    },
    methods: {
      // 树状搜索
      onChange(e) {
        this.spinningShow = true;
        const value = e;
        this.searchValue = value;
        if (value == "") {
          this.spinningShow = false;
          this.$message.info("请输入关键字");
        } else {
          this.expandedKeys = [];
          this.parentList = [];
          this.backupsExpandedKeys = [];
          const candidateKeysList = this.getkeyList(value, this.orgTreeData, []);
          if (candidateKeysList.length == 0) {
            // 销毁提示
            this.$message.destroy();
            this.spinningShow = false;
  
            return this.$message.info("没有相关数据");
          }
          this.autoExpandParent = true;
          // 去重赋值
          this.expandedKeys = [...new Set(this.parentList)];
  
          this.$forceUpdate();
          this.spinningShow = false;
        }
         setTimeout(() => {
           document.getElementsByClassName('fullName')[0].scrollIntoView()
        },1300);
      },
      getkeyList(value, tree, keyList) {
        for (let i = 0; i < tree.length; i++) {
          const node = tree[i];
          if (node.orgName.indexOf(value) > -1) {
            keyList.push(node.orgId);
            // 缓存父级节点
            this.parentList.push(node.parentId);
          }
          if (node.children) {
            this.getkeyList(value, node.children, keyList);
          }
        }
        return keyList;
      },
      // 该递归主要用于获取key的父亲节点的key值
      getParentKey(key, tree) {
        console.log("🤗🤗🤗, key =>", key);
        console.log("🤗🤗🤗, tree =>", tree);
        let parentKey;
        let temp;
        for (let i = 0; i < tree.length; i++) {
          const node = tree[i];
          if (node.children) {
            temp = this.getParentKey(key, node.children);
            if (node.children.some((item) => item.orgId === key)) {
              parentKey = node.orgId;
            } else if (temp) {
              parentKey = temp;
            }
          }
        }
        return parentKey;
      },
  
      // 根据treeId获取父节点信息
      getParentNode(key, tree) {
        let parentNode;
        for (let i = 0; i < tree.length; i++) {
          const node = tree[i];
          if (node.children) {
            if (node.children.some(item => item.orgId === key)) {
              parentNode = node;
            } else if (this.getParentNode(key, node.children)) {
              parentNode = this.getParentNode(key, node.children);
            }
          }
        }
        return parentNode;
      },
  
      // 获取该节点的所有祖先节点
      getAllParentKey(key, tree) {
        let parentKey;
        if (key) {
          parentKey = this.getParentKey(key, tree);
          if (parentKey) {
            if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
              this.backupsExpandedKeys.push(parentKey);
            }
            this.getAllParentKey(parentKey, tree);
          }
        }
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys;
        this.autoExpandParent = false;
      },
  
      //展开点击节点下的子节点
      // onExpand (expandedKeys) {
      //   this.expandedKeys = expandedKeys;
      // },
      // 树状选择
      onSelect(item, data) {
        if(! this.checkable) {
          if (data.selected) {
          this.checked = [data.node.dataRef];
          let node = this.getParentNode(this.checked[0].orgId, this.orgTreeData)
          if(node) {
            this.checked[0].parentName = node.orgName
          }
          this.userTreeDefaultKey = [data.node.dataRef.id];
        }
        }
      },
      onChecked(keys, e) {
        this.checked = keys
        this.halfCheckedKeys = e.halfCheckedKeys
        // this.userTreeDefaultKey = keys.checked
        // this.checked = []
        // e.checkedNodes.forEach(item => {
        //   let { orgId, orgName } = item.data.props
        //   let baseItem = new Object
        //   baseItem.orgId = orgId
        //   baseItem.orgName = orgName
        //   this.checked.push(baseItem)
        // });
      },
  
      close() {
        this.checkIdentyData = [];
        this.orgTreeDialogVisible = false;
      },
      initOrgTree() {
        let data = {
          code: "dbxx-tree",
        };
        findTreeByCode(data).then((res) => {
          this.orgTreeData = res.data;
        });
      },
      open(keys) {
        //回选旧数据
        this.$nextTick(() => {
          this.userTreeDefaultKey = keys;
          this.checked = keys
        })
        this.orgTreeDialogVisible = true;
      },
      confirm() {
        this.orgTreeDialogVisible = false;
        this.$emit("confirm", this.checked, this.halfCheckedKeys);
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.orgName.indexOf(value) !== -1;
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  ::v-deep .ant-tree {
    max-height: 500px;
    width: 100%;
    overflow-y: auto;
  }
  </style>
  