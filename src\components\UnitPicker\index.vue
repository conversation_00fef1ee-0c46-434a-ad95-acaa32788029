<template>
  <div>
    <a-col :span="spanNum" :class="{ 'overLength': title.length > 5 && spanNum <= 8 }">
      <a-form-model-item  :label="title" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
       <div style="display: flex;align-items: center;">
          <a-select v-model="sjmelyDm"
                    placeholder="请选择"
                    style="width: 50%;"
                    @change="onSjmelyDm">
            <a-select-option v-for="item in getRecommendList"
                             :key="item.melyDm"
                             :value="item.melyDm">{{ item.melymc }}</a-select-option>
          </a-select>
          <a-select v-model="melyDm"
                    placeholder="请选择"
                    style="width: 50%;"
                    @change="getMelyDm">
            <a-select-option v-for="item in onSjmelyDmList"
                             :key="item.melymc"
                             :value="item.melymc">{{ item.melymc }}</a-select-option>
          </a-select>
         <!-- <a-input-number v-model="minNum" @change="getMinNum" style="width: 45%;" />
         <p style="width: 10%; text-align: center;">至</p>
         <a-input-number v-model="maxNum" @change="getMaxNum" style="width: 45%;" /> -->
       </div>
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
import {
  getRecommend,
  Secondary
} from "@/api/election.js";
export default {
  props: {
    title: {
      type: String,
      default: () => '推荐单位',
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    recommendValue: {
      type: [String, Number],
      default: '',
    },
    sjmelyValue: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      getRecommendList: [] /* 获取出推荐单位下拉数据列表 */,
      onSjmelyDmList: [],
      sjmelyDm: '',
      melyDm: '',
    };
  },
  watch: {   
    recommendValue: {  
      handler(val) {
        this.sjmelyDm = val;
      },
      deep: true,
      immediate: true,
    }, 
    sjmelyValue: {  
      handler(val) {
        this.melyDm = val;
      },
      deep: true,
      immediate: true,
    }, 
  },
  created () {
    this.getRecommed()
  },
  methods: {
    async getRecommed() {
      let res = await getRecommend();
      if (res.code == "0000") {
        this.getRecommendList = res.data;
        this.getRecommendList.unshift({ melyDm: "", melymc: "全部" });
      }
    },
    //  主下拉框
    async onSjmelyDm (id) {
      this.getSjmelyDm(id);
      let res = await Secondary({ id });
      if (res.code == "0000") {
        this.onSjmelyDmList = res.data;
        console.log(res.data,'res.datares.data');
        
        this.onSjmelyDmList.unshift({ melyDm: "", melymc: "全部" });
        this.queryForm.melymc = this.onSjmelyDmList[0].melymc;
      }
    },
    getSjmelyDm(val) {
      // this.$emit('getSjmelyDm', val)
      this.$emit('update:recommendValue', val)
    },
    getMelyDm(val) {
      this.$emit('update:sjmelyValue', val)
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
