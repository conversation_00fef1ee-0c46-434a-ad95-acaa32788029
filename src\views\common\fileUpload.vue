<template>
  <div>
    <el-dialog
      :before-close="handleClose"
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="dialogFormVisible"
      width="909px"
    >
      <div class="upload">
        <!-- <el-alert
        :closable="false"
        :title="`支持pdf格式，最多可上传${limit}个文档，每个不可大于${size}M，如果大于${size}M会自动为您过滤`"
        type="info"
      ></el-alert> -->

        <el-upload
          class="upload-demo"
          ref="upload"
          multiple
          :action="action"
          :data="data"
          :before-upload="beforeAvatarUpload"
          :file-list="uploadList"
          :show-file-list="false"
          :on-success="handleSuccess"
          accept=".pdf"
        >
          <el-button slot="trigger" size="small" type="primary"
            >选取文件</el-button
          >
          <div slot="tip" class="el-upload__tip">
            支持pdf格式，单个文件不可大于{{ size }}M，如果大于{{
              size
            }}M会自动为您过滤
          </div>
        </el-upload>
      </div>
      <br />
      <el-table
        ref="calendarTable"
        v-loading="listLoading"
        row-key="id"
        border
        :data="fileList"
        :element-loading-text="elementLoadingText"
      >
        <el-table-column label="拖动" width="80">
          <i class="el-icon-rank"></i>
        </el-table-column>
        <el-table-column label="序号" width="80">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column label="文件名" prop="name"></el-table-column>
        <el-table-column label="生效时间" prop="timingTime"></el-table-column>
        <el-table-column label="阅读时限" prop="expireTime"> </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="success"
              plain
              @click="handleTimeLimit(scope.row)"
              >定时</el-button
            >
            <el-button
              size="mini"
              type="danger"
              plain
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div
        slot="footer"
        class="dialog-footer"
        style="position: relative; padding-right: 15px; text-align: right;"
      >
        <el-button type="warning" @click="handleSave">保存</el-button>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="设置文件时限"
      :visible.sync="dialogTimeLimit"
      width="86%"
      top="2vh"
    >
      <el-form ref="timeLimit" :model="timeForm" label-width="150px">
        <a-row>
          <a-col :span="layout === 'horizontal' ? 12 : 24">
            <el-form-item label="生效时间" prop="timingTime">
              <el-date-picker
                v-model="timeForm.timingTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm"
                autocomplete="off"
                type="datetime"
                placeholder="选择生效时间"
                style="width: 100%;"
                :picker-options="pickerOptions0"
              ></el-date-picker>
            </el-form-item>
          </a-col>
          <a-col :span="layout === 'horizontal' ? 12 : 24">
            <el-form-item label="阅读时限" prop="expireTime">
              <el-date-picker
                v-model="timeForm.expireTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm"
                autocomplete="off"
                type="datetime"
                placeholder="选择阅读时限"
                style="width: 100%;"
                :picker-options="pickerOptions1"
              ></el-date-picker>
            </el-form-item>
          </a-col>
        </a-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleTimeClose">取 消</el-button>
        <el-button type="primary" @click="handleTimeSave">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getRelFileList,
  doDelete,
  doUpdate2,
  doUpdateTimeLimit,
} from "@/api/file";
import checkPermission from "@/utils/permission";
import Sortable from "sortablejs";
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
  },
  components: { Sortable },
  name: "MeetingFileUpload",
  data() {
    return {
      limit: 10,
      size: 30,
      action: process.env.VUE_APP_BASE_API + "/api/v1/meetingFile/fileUpload",
      fileList: [],
      uploadList: [],
      fileNum: 0,
      title: "会议文件上传",
      dialogFormVisible: false,
      data: {
        relId: "",
        type: 1,
        encrypted: true,
      },
      listLoading: false,
      elementLoadingText: "正在加载...",
      calendarId: "",

      //时限相关配置
      timeForm: {},
      dialogTimeLimit: false,
      pickerOptions0: {
        disabledDate: (time) => {
          const endDateVal = this.timeForm.expireTime;
          if (endDateVal) {
            return time.getTime() >= new Date(endDateVal).getTime();
          }
        },
      },
      pickerOptions1: {
        disabledDate: (time) => {
          const beginDateVal = this.timeForm.timingTime;
          if (beginDateVal) {
            return time.getTime() < new Date(beginDateVal).getTime();
          }
        },
      },
    };
  },
  mounted() {},
  methods: {
    // 行拖拽
    rowDrop() {
      const tbody = this.$refs.calendarTable.$el.querySelector(
        ".el-table__body-wrapper tbody"
      );
      const self = this;
      Sortable.create(tbody, {
        onEnd({ newIndex, oldIndex }) {
          console.log(newIndex, oldIndex);
          const targetRow = self.fileList.splice(oldIndex, 1)[0];
          self.fileList.splice(newIndex, 0, targetRow);
        },
      });
    },

    submitUpload() {
      this.$refs.upload.submit();
    },

    //上传前判断
    beforeAvatarUpload(file) {
      this.listLoading = true;

      //文件数量判断
      const isLimit = true;
      //const isLimit = this.fileList.length < this.limit;
      // if (!isLimit) {
      //   this.$baseMessage(`上传文件数量不能超过${this.limit} 个`, "error");
      // }
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const extension = testmsg === "pdf";

      if (!extension) {
        this.$message.error("上传文件只能是 pdf格式!");
        this.listLoading = false;
        return extension;
      }
      //大小判断
      const isSize = file.size / 1024 / 1024 < this.size;
      if (!isSize) {
        this.$baseMessage(`上传文件大小不能超过${this.size} MB`, "error");
      }
      return isLimit && isSize;
    },

    handleSuccess(response, file, fileList) {
      //this.fetchData();
      this.fileList.push({
        id: response.data[0].id,
        name: response.data[0].name,
        size: response.data[0].size,
        suffix: response.data[0].suffix,
        unit: response.data[0].unit,
        timingTime: "",
        expireTime: "",
      });
      this.listLoading = false;
    },

    handleDelete(file, fileList) {
      // this.listLoading = true;
      this.fileList.forEach((item, index) => {
        if (item.id == file.id) {
          this.fileList.splice(index, 1);
          return false;
        }
      });
      // doDelete(file.id).then((res) => {
      //   this.$baseMessage(`删除成功`, "success");
      //   // this.fetchData();

      //   this.fileList.forEach((item, index) => {
      //     if (item.id == file.id) {
      //       this.fileList.splice(index, 1);
      //       return false;
      //     }
      //   });
      //   this.listLoading = false;
      // });
    },

    handlePreview(file) {
      this.dialogViewUrl = this.viewUrl + file.id;
      this.dialogVisible = true;
    },

    handleShow(row) {
      this.title = "会议文件上传";
      //this.data.relId = row.id;
      this.calendarId = row.id;
      this.dialogFormVisible = true;

      this.fetchData();
    },

    fetchData() {
      this.listLoading = true;
      getRelFileList({ relId: this.calendarId, type: 1 }).then((res) => {
        this.fileNum = res.data.length;
        this.fileList = res.data;
        setTimeout(() => {
          this.rowDrop();
          this.listLoading = false;
        }, 500);
      });
    },

    handleTimeLimit(row) {
      this.timeForm = {
        id: row.id,
        timingTime: row.timingTime,
        expireTime: row.expireTime,
      };

      this.dialogTimeLimit = true;
    },

    handleTimeClose() {
      this.timeForm = {};
      this.dialogTimeLimit = false;
    },

    handleTimeSave() {
      let that = this;
      doUpdateTimeLimit(that.timeForm).then((res) => {
        that.fileList.forEach((file) => {
          if (file.id == that.timeForm.id) {
            file.timingTime = that.timeForm.timingTime;
            file.expireTime = that.timeForm.expireTime;
            return false;
          }
        });
        that.$baseMessage(`文件时限设置成功`, "success");
        setTimeout(() => {
          that.dialogTimeLimit = false;
          //this.fetchData();
        }, 500);
      });
    },

    handleSave() {
      this.listLoading = true;

      let data = [];

      this.fileList.forEach((element, index) => {
        data.push({
          id: element.id,
          sort: index,
          relId: this.calendarId,
          name: element.name,
          timingTime: element.timingTime,
          expireTime: element.expireTime,
        });
      });

      doUpdate2(data, this.calendarId).then((res) => {
        this.$baseMessage(`保存成功`, "success");
        setTimeout(() => {
          this.listLoading = false;
          this.dialogFormVisible = false;
        }, 500);
      });
    },

    handleClose() {
      this.fileList = [];
      this.uploadList = [];
      this.fileNum = 0;

      this.dialogFormVisible = false;
    },
  },

  checkPermission,
};
</script>
