<template>
  <a-modal
    :title="orgTreeDialogTitle"
    :visible.sync="orgTreeDialogVisible"
    width="50%"
    destroy-on-close
    @cancel="close"
  >
    <a-input-search
      v-model="filterText"
      style="width: 100%;"
      placeholder="输入关键字进行过滤"
      @search="onChange"
    ></a-input-search>
    <a-tree
      ref="orgTree"
      v-model="checkIdentyData"
      checkable
      :selectable="false"
        multiple
      :expanded-keys="expandedKeys"
      :replace-fields="replaceFields"
      :tree-data="orgTreeData"
       @expand="onExpand"
      @check="onCheck"
    >
      <template slot="title" slot-scope="{ name, orgLevel }">
        <a-icon
          :type="orgLevel == '1' ? 'apartment' : 'file'"
          style="margin-right: 10px;"
        />
        <span
          v-if="searchValue && name.indexOf(searchValue) > -1"
          style="color: #f50;"
          >{{ name }}</span
        >
        <span v-else>{{ name }}</span>
      </template>
    </a-tree>

    <span slot="footer" class="dialog-footer">
      <a-button @click="close">取 消</a-button>
      <a-button style="padding-left: 10px;" type="primary" @click="confirm"
        >确 定</a-button
      >
    </span>
  </a-modal>
</template>

<script>
// import { findDbOU } from "@/api/registrationMage/tableIng.js";
import {findDbOrgUserTree, findDbOrgUserTreeOrderByXsbh} from "@/api/dbxx"; //代表展示自定义的树，替换getUserTree
import { log } from "@antv/g2plot/lib/utils";
export default {
  props: {
    orgTreeDialogTitle: {
      type: String,
      default: "选择范围",
    },
    checkIdentyData: {
      type: Array,
    },
  },
  data() {
    return {
      filterText: "",

      name: "",
      expandedKeys: [],
      searchValue: "",
      allChildData: [],
      selectedKeys: [],
      autoExpandParent: true,
      checked: [],
      // checkIdentyData: [],
      orgTreeDialogVisible: false,
      orgTreeData: [],
      orgTreeDefaultKey: [],
      replaceFields: {
        title: "name",
        key: "uniqueId",
        children: "children",
      },
      // orgTreeProps: {
      //   children: 'children',
      //   label: 'orgName',
      // },
      isRadio: true,
      relType: {
        type: Number,
        default: 1,
      },
      noCheckKeys: [],
      jcDm: "3",
    };
  },
  watch: {
    orgTreeDialogVisible(newVal) {
      console.log(this.checkIdentyData, "checkIdentyData");
      if (newVal) {
        // this.initOrgTree()
      } else {
        this.expandedKeys = [];
        this.checked = [];
        // this.checkIdentyData = [];
      }
    },
  },
  methods: {
    // 树状搜索
    onChange(e) {
      const value = e;
      this.searchValue = value;
      if (value == "") {
         this.$message.info("请输入关键字");
      }else{
      this.expandedKeys = [];
      this.backupsExpandedKeys = [];
      const candidateKeysList = this.getkeyList(value, this.orgTreeData, []);
      candidateKeysList.forEach((item) => {
        const key = this.getParentKey(item, this.orgTreeData);
        if (key && !this.backupsExpandedKeys.some((item) => item === key))
          this.backupsExpandedKeys.push(key);
      });
      console.log(this.backupsExpandedKeys, "this.backupsExpandedKeys");
      const { length } = this.backupsExpandedKeys;
      for (let i = 0; i < length; i++) {
        this.getAllParentKey(this.backupsExpandedKeys[i], this.orgTreeData);
      }
      this.expandedKeys = this.backupsExpandedKeys.slice();
      if (candidateKeysList.length == 0) {
        this.$message.info("没有相关数据");
      }
      }

    },
    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.name.indexOf(value) > -1) {
          keyList.push(node.uniqueId);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey;
      let temp;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          temp = this.getParentKey(key, node.children);
          if (node.children.some((item) => item.uniqueId === key)) {
            parentKey = node.uniqueId;
          } else if (temp) {
            parentKey = temp;
          }
        }
      }
      return parentKey;
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey;
      if (key) {
        parentKey = this.getParentKey(key, tree);
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey);
          }
          this.getAllParentKey(parentKey, tree);
        }
      }
    },
    close() {
      this.orgTreeDialogVisible = false;
      this.checked = [];
    },
    initOrgTree() {
      findDbOrgUserTreeOrderByXsbh({ jcDm: this.jcDm }).then((res) => {
        this.orgTreeData = res.data;
      });
    },

    // 树状选择
    onCheck(item, data) {
      if (data.checked) {
        if (data.node.dataRef.children) {
          this.recursion(data.node.dataRef.children);
        } else {
          let value = data.node.dataRef;
          this.checked.push(value);
        }
      } else {
        //  let orgId=data.node.dataRef.orgId;
        this.checked.forEach((item, index) => {
          if (data.node.dataRef.children) {
            this.recursionDele(data.node.dataRef.children);
          } else {
            this.checked.splice(index, 1);
          }
        });
      }
    },
    recursionDele(data) {
      this.checked.forEach((cheITEM, index) => {
        data.forEach((item) => {
          if (cheITEM.orgId == item.orgId) {
            this.checked.splice(index, 1);
            if (item.children !== null) {
              this.checked.splice(index, 1);
              this.recursionDele(item.children);
            }
            this.$forceUpdate();
          }
        });
      });
    },
    // 递归找数据
    recursion(data) {
      data.forEach((item) => {
        this.checked.push(item);
        if (item.children !== null) {
          item.children.forEach((son) => {
            this.checked.push(son);
          });
          this.recursion(item.children);
        }
      });
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
    },
    confirm() {
      this.noCheckKeys = [];
      this.orgTreeDialogVisible = false;
      this.$emit("confirm", this.checked);
      // this.checkIdentyData = [];
    },
  },
};
</script>
