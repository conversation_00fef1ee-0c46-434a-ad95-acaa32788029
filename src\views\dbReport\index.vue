<template>
  <div style="padding: 20px">
    <div>
      <a-row>
        <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <SearchForm
            :noMore="true"
            :value="queryForm"
            @onReset="clearQueryForm"
            @onSearch="handleQuery"
          >
            <template v-slot:topSearch>
              <FormInput
                v-model="queryForm.userName"
                :label="'代表姓名'"
                allow-clear
                @enter="handleQuery"
              />

              <FormSelect
                v-model="queryForm.zwdb"
                label="是否职务代表"
                :options="dutyRepreList"
                allow-clear
              />

              <FormRangePicker
                v-model="queryForm"
                start-prop="startTime"
                end-prop="endTime"
                label="时间范围"
                allow-clear
              />
              <DhJcCascade v-model="queryForm" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>
            </template>
            <!-- <template v-slot:moreSearch> -->
            <!-- </template> -->
          </SearchForm>

          <a-row style="margin: 5px 0px 10px 8px">
            <a-col :span="6">
              <a-button
                type="primary"
                :loading="downloadLoading"
                style="margin-left: 12px"
                @click="download"
                >导出</a-button
              >
            </a-col>

            <P style="float: right; margin-top: 3px; color: red"
              >可点击对应的表头进行排序，对应数字可点击穿透查看详情</P
            >
          </a-row>
        </a-col>
      </a-row>
    </div>
    <a-spin :spinning="listLoading" :indicator="indicator">
      <a-table
        :columns="columns"
        :data-source="dbList"
        row-key="id"
        :pagination="pagination"
        :custom-row="clickRow"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onSelect: onSelectData,
          onChange: onSelectChange,
          onSelectAll: onSelectAll,
        }"
        :row-key="
          (record, index) => {
            return index;
          }
        "
        :scroll="{ x: 50, y: 420 }"
        @change="handleTableChange"
      ></a-table>
    </a-spin>
  </div>
</template>

<script>
import { myPagination } from "@/mixins/pagination.js";
import { getDbReport } from "@/api/dbReport/dbReport";
import { instance_1 } from "@/api/axiosRq";
import SingleSelect from "@/components/SingleSelect/index";
import SearchForm from "@/components/SearchForm/index";
import FormInput from "@/components/FormInput/index.vue";
import FormRangePicker from "@/components/FormRangePicker/index.vue";
import FormSelect from "@/components/FormSelect/index.vue";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import { DBLZ_DBJSQ } from "@/utils/enum/levelRoleMainIdentifyEnum";

export default {
  components: {
    DhJcCascade,
    FormSelect,
    FormRangePicker,
    FormInput,
    SingleSelect,
    SearchForm,
  },
  mixins: [myPagination],
  props: {},
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBJSQ,
      listLoading: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />, //转动的圈圈
      dbList: [],
      downloadLoading: false,
      selectedRowKeys: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        userName: "",
        startTime: null,
        endTime: null,
        orgName: "",
      },

      orgNames: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "越秀代表团",
          label: "越秀代表团",
        },
        {
          value: "海珠代表团",
          label: "海珠代表团",
        },
        {
          value: "荔湾代表团",
          label: "荔湾代表团",
        },
        {
          value: "天河代表团",
          label: "天河代表团",
        },
        {
          value: "白云代表团",
          label: "白云代表团",
        },
        {
          value: "黄埔代表团",
          label: "黄埔代表团",
        },
        {
          value: "花都代表团",
          label: "花都代表团",
        },
        {
          value: "番禺代表团",
          label: "番禺代表团",
        },
        {
          value: "南沙代表团",
          label: "南沙代表团",
        },
        {
          value: "从化代表团",
          label: "从化代表团",
        },
        {
          value: "增城代表团",
          label: "增城代表团",
        },
      ],
      columns: [
        {
          title: "代表姓名",
          ellipsis: true,
          align: "center",
          width: 120,
          customRender: (text, record, index) => {
            return record.userName || "/";
          },
        },
        {
          title: "所属代表团",
          ellipsis: true,
          align: "center",
          width: 120,
          customRender: (text, record, index) => {
            return record.orgName || "/";
          },
        },
        {
          title: "代表随手拍",
          ellipsis: true,
          align: "center",
          className: "dianji",
          width: 120,
          dataIndex: "ssp",
          sorter: true,
          customRender: (text, record, index) => {
            return record.ssp || "/";
          },
        },
        {
          title: "社情民意专报",
          ellipsis: true,
          align: "center",
          width: 120,
          className: "dianji",
          dataIndex: "ztc",
          sorter: true,
          customRender: (text, record, index) => {
            return record.ztc || "/";
          },
        },
        {
          title: "履职周报(已读)",
          ellipsis: true,
          align: "center",
          width: 120,
          className: "dianji",
          dataIndex: "zb",
          sorter: true,
          customRender: (text, record, index) => {
            return record.zb || "/";
          },
        },
        // {
        //   title: "籍贯",
        //   ellipsis: true,
        //   width: 50,
        //   customRender: (text, record, index) => {
        //     return record.nation || "/";
        //   },
        // },
      ],
      dutyRepreList: [
        { name: "全部", id: "" },
        { name: "职务代表", id: "1" },
        { name: "非职务代表", id: "2" },
      ],
    };
  },
  created() {
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "网上联络站");
    this.$store.dispatch("navigation/breadcrumb2", "区镇代表管理");
  },
  methods: {
    fetchData() {
      this.listLoading = true;
      getDbReport(this.queryForm).then((res) => {
        this.dbList = res.data.rows;
        this.pagination.total = res.data.total;
        setTimeout(() => {
          this.listLoading = false;
        }, 500);
      });
    },
    handleQuery() {
      this.pagination.current = 1;
      this.queryForm.pageNum = 1;
      this.fetchData();
    },
    clearQueryForm(value) {
      this.queryForm = value;
      this.pagination.current = 1;
      this.pagination.pageSize = value.pageSize;
      this.fetchData();
    },
    //  全选
    onSelectAll(state, selectedRows, data, DA) {
      this.iShow = state;
    },
    // 选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.RowKeys = selectedRowKeys;
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
    },
    onSelectData(data, state) {
      this.iShow = state;
    },
    //导出
    download() {
      this.downloadLoading = true;
      // console.log(this.queryForm);
      instance_1({
        url: "/dbReport/exportDbReport",
        method: "get",
        responseType: "blob",
        params: this.queryForm,
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        const date = new Date();
        if (this.queryForm.orgName != "" && this.queryForm.orgName != null) {
          a.download =
            this.queryForm.orgName +
            "统计分析表" +
            date.toLocaleString() +
            ".xlsx";
        } else {
          a.download = "统计分析表" + date.toLocaleString() + ".xlsx";
        }

        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000);
      });
    },
    handleTableChange(data, value, title) {
      try {
        this.queryForm.sort = title.column.dataIndex;
      } catch (error) {}
      if (title.order == "ascend") {
        this.queryForm.order = "desc";
      } else if (title.order == "descend") {
        this.queryForm.order = "asc";
      } else {
        this.queryForm.order = "";
        this.queryForm.sort = "";
      }

      this.fetchData();
    },

    clickRow(record) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            try {
              let tableHeader = event.target.__vue__.column.dataIndex;

              console.log(record);
              if (tableHeader == "ssp") {
                this.$router.push({
                  path: "/BBS/statisticalAnalysisByUser",
                  query: {
                    userId: record.dbId,
                    startTime: this.queryForm.startTime,
                    endTime: this.queryForm.endTime,
                  },
                });
              } else if (tableHeader == "ztc") {
                this.$router.push({
                  path: "/BBS/through",
                  query: {
                    userId: record.dbId,
                    startTime: this.queryForm.startTime,
                    endTime: this.queryForm.endTime,
                  },
                });
              } else if (tableHeader == "zb") {
                this.$router.push({
                  path: "/BBS/weekbook",
                  query: {
                    userId: record.dbId,
                    startTime: this.queryForm.startTime,
                    endTime: this.queryForm.endTime,
                  },
                });
              }
            } catch (error) {}
          },
        },
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 14px;
  @include add-size($font_size_16);
  font-family: pingFang-M;
}
::v-deep .ant-table-tbody {
  .dianji {
    cursor: pointer;
    color: blue;
  }
}
.conference {
  margin: 20px 0;
}
</style>
