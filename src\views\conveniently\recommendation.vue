<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="24"
             :xl="24">
        <a-row style="margin-left:1%">
          <a-form ref="queryForm"
                  :form="queryForm"
                  :model="queryForm"
                  layout="horizontal"
                  :labelCol="{ span: 5 }"
                  :wrapperCol="{ span: 18, offset: 0 }">
            <a-row>
              <a-col :span="6">
                <a-form-item label="主体">
                  <a-input placeholder="请输入主体"
                           v-model="queryForm.subject"
                           allow-clear
                           v-on:keyup.enter="handleQuery"
                           class="table-input"></a-input>
                </a-form-item>

              </a-col>
              <a-col :span="6">
                <a-form-item label="地址">
                  <a-input placeholder="请输入地址"
                           v-model="queryForm.address"
                           allow-clear
                           v-on:keyup.enter="handleQuery"
                           class="table-input"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="6"
                     :sm="24">
                <a-form-item label="内容"
                             prop="content">
                  <a-input allow-clear
                           v-model="queryForm.content"
                           placeholder="请输入内容"
                           v-on:keyup.enter="handleQuery" />
                </a-form-item>

              </a-col>

              <a-col :span="6">
                <span style="float: right; margin-top: 3px">
                  <a @click="toggleAdvanced"
                     style="margin-right: 8px">
                    {{ advanced ? "收起" : "高级搜索" }}
                    <a-icon :type="advanced ? 'up' : 'down'" />
                  </a>
                  <a-button type="primary"
                            @click="handleQuery">搜索</a-button>
                  <a-button style="margin-left: 12px;"
                            @click="reset"
                            class="pinkBoutton">重置</a-button>
                </span>
              </a-col>
            </a-row>
            <a-row v-if="advanced">
              <a-col :md="6"
                     :sm="24">
                <a-form-item label="代表姓名"
                             prop="userName">
                  <a-input v-model="queryForm.userName"
                           clearable
                           allow-clear
                           v-on:keyup.enter="handleQuery"
                           placeholder="请输入代表姓名" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="处理进度：">
                  <a-select v-model="queryForm.progressStatus"
                            placeholder="请选择处理进度"
                            allow-clear
                            class="table-input">
                    <a-select-option v-for="(item, index) in arrOptions"
                                     :key="index"
                                     :label="item.label"
                                     :value="item.value">
                      {{  item.label  }}</a-select-option>
                    <!-- <a-select-option title="全部" value>全部</a-select-option>
                <a-select-option title="联络站处理" value="2">联络站处理</a-select-option>
                <a-select-option title="12345处理" value="3">12345处理</a-select-option>
                <a-select-option title="已回复" value="4">已回复</a-select-option> -->
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6"
                     :sm="24">
                <a-form-item label="类型"
                             prop="commentCategory">
                  <a-input placeholder="请输入类型"
                           v-model="queryForm.commentCategory"
                           allow-clear
                           v-on:keyup.enter="handleQuery"></a-input>
                </a-form-item>
              </a-col>
              <!-- <a-col :md="6"
                     :sm="24">
                <a-form-item label="开始时间"
                             prop="startTime">
                  <a-date-picker style="width: 100%;"
                                 v-model="queryForm.startTime"
                                 allow-clear
                                 value-format="YYYY-MM-DD HH:mm:ss"
                                 placeholder="选择开始时间"
                                 :disabled-date="
                (current) =>
                  current && queryForm.endTime
                    ? current.valueOf() >=
                    moment(new Date(queryForm.endTime)).valueOf()
                    : false
              "></a-date-picker>
                </a-form-item>
              </a-col>
              <a-col :md="6"
                     :sm="24">
                <a-form-item label="结束时间"
                             prop="endTime">
                  <a-date-picker style="width: 100%;"
                                 v-model="queryForm.endTime"
                                 allow-clear
                                 value-format="YYYY-MM-DD"
                                 placeholder="选择结束时间"
                                 :disabled-date="
                (current) =>
                  current && queryForm.startTime
                    ? moment(new Date(queryForm.startTime)).valueOf() >=
                    current.valueOf()
                    : false
              "></a-date-picker>
                </a-form-item>
              </a-col> -->
              <a-col span="6">
                <a-form-model-item style="width: 100%;"
                                   label="时间范围">
                  <a-range-picker style="width: 100%;"
                                  :ranges="{
                  最近三个月: [
                    moment(new Date()).subtract(2, 'months'),
                    moment(),
                  ],
                  '今年1-6月': [
                    moment(moment().startOf('year')).startOf('month'),
                    moment(moment().startOf('year'))
                      .add(5, 'months')
                      .endOf('month'),
                  ],
                  '今年7-12月': [
                    moment(moment().startOf('year'))
                      .add(6, 'months')
                      .startOf('month'),
                    moment(moment().startOf('year'))
                      .add(11, 'months')
                      .endOf('month'),
                  ],
                  今年内: [
                    moment(moment().startOf('year')).startOf('month'),
                    moment(moment().startOf('year'))
                      .add(11, 'months')
                      .endOf('month'),
                  ],
                }"
                                  v-model="dateRange"
                                  format="YYYY-MM-DD"
                                  value-format="YYYY-MM-DD"
                                  @change="onTimeChange" />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form>
        </a-row>
      </a-col>
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="24"
             :xl="24">
        <!-- table -->
        <a-spin :indicator="indicator"
                :spinning="listLoading">
          <a-table :bordered="false"
                   :custom-row="clickRow"
                   class="directorySet-table"
                   ref="table"
                   size="small"
                   :columns="columns"
                   :pagination="pagination"
                   :data-source="list"
                   :rowKey="
              (record, index) => {
                return index;
              }
            "
                   :scroll="{ x: 300, y: 0 }"></a-table>
        </a-spin>
      </a-col>
    </a-row>
    <a-drawer title="高级查询"
              :visible.sync="drawer"
              @close="drawer = false"
              width="500px">
      <byui-query-form style="padding: 0 10px; overflow: auto;"
                       :style="{ height: drawerHeight }">
        <a-form ref="drawerForm"
                :model="queryForm"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 20 }">
          <a-form-item label="内容"
                       prop="content">
            <a-input allow-clear
                     v-model="queryForm.content"
                     placeholder="请输入内容"
                     v-on:keyup.enter="handleQuery" />
          </a-form-item>
          <a-form-item label="类型"
                       prop="commentCategory">
            <a-input placeholder="请输入类型"
                     v-model="queryForm.commentCategory"
                     allow-clear
                     v-on:keyup.enter="handleQuery"></a-input>
          </a-form-item>
          <a-form-item label="处理进度"
                       prop="progressStatus">
            <a-select v-model="queryForm.progressStatus"
                      placeholder="请选择处理进度"
                      allow-clear
                      show-search
                      style="width: 100%;"
                      @change="$forceUpdate()">
              <a-select-option v-for="(item, index) in statusOptions"
                               :key="index"
                               :label="item.label"
                               :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="开始时间"
                       prop="startTime">
            <a-date-picker v-model="queryForm.startTime"
                           allow-clear
                           value-format="YYYY-MM-DD HH:mm:ss"
                           placeholder="选择开始时间"
                           style="width: 100%;"
                           :disabled-date="
                (current) =>
                  current && queryForm.endTime
                    ? current.valueOf() >=
                    moment(new Date(queryForm.endTime)).valueOf()
                    : false
              "></a-date-picker>
          </a-form-item>
          <a-form-item label="结束时间"
                       prop="endTime">
            <a-date-picker v-model="queryForm.endTime"
                           allow-clear
                           value-format="YYYY-MM-DD"
                           placeholder="选择结束时间"
                           style="width: 100%;"
                           :disabled-date="
                (current) =>
                  current && queryForm.startTime
                    ? moment(new Date(queryForm.startTime)).valueOf() >=
                    current.valueOf()
                    : false
              "></a-date-picker>
          </a-form-item>
          <a-form-item>
            <a-row>
              <a-col span="12"
                     push="4">
                <a-button icon="search"
                          type="primary"
                          native-type="submit"
                          @click="handleQuery">查询</a-button>
              </a-col>
              <a-col span="12"
                     push="8">
                <a-button type="warning"
                          icon="delete"
                          @click="handleClear">清空</a-button>
              </a-col>
            </a-row>
          </a-form-item>
        </a-form>
      </byui-query-form>
    </a-drawer>
    <!-- 弹出框 -->
    <a-modal title="办理"
             :visible.sync="dialogVisible"
             width="30%"
             @close="handleClose">
      <a-form :label-position="labelPosition"
              label-width="80px"
              :model="huifuDate">
        <a-form-item label="回复意见">
          <a-input type="textarea"
                   v-model="huifuDate.value"></a-input>
        </a-form-item>
      </a-form>
      <span slot="footer"
            class="dialog-footer">
        <a-button @click="dialogVisible = false">取 消</a-button>
        <a-button type="primary"
                  @click="commitFu()">确定</a-button>
      </span>
    </a-modal>
  </div>
</template>

<script>
import { instance_1, instance_2 } from "@/api/axiosRq";
// import checkPermission from "@/utils/permission";
export default {
  name: "Table",
  components: {},
  data () {
    return {
      TBloading: false,
      dateRange: [],
      badgeHide: true,
      drawer: false,
      drawerHeight: window.innerHeight - 75 + "px",
      list: [],
      listLoading: false,
      listHeight: window.innerHeight - 360 + "px",
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      // 弹出框
      huifuId: "",
      dialogVisible: false,
      labelPosition: "right",
      huifuDate: {
        name: "",
        value: "",
        type: "",
      },
      advanced: false,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        subject: null,
        status: null,
        // progressStatus: null,
        address: null,
        commentCategory: null,
        startTime: null,
        endTime: null,
      },
      statusOptions: [
        {
          value: "",
          label: "全部",
        },
        {
          value: 2,
          label: "联络站处理",
        },
        {
          value: 3,
          label: "12345热线处理",
        },
        {
          value: 4,
          label: "已回复",
        },
      ],
      arrOptions: [
        {
          value: "",
          label: "全部",
        },
        // {
        //   value: 1,
        //   label: "草稿",
        // },
        // {
        //   value: 2,
        //   label: "联络站处理",
        // },
        // {
        //   value: 3,
        //   label: "12345处理",
        // },

        {
          value: 4,
          label: "已办结",
        },
      ],
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,

      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "日期",
          align: "center",
          width: 140,
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },

        {
          title: "类型",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "commentCategory",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "处理进度",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "progressStatus",
          customRender: (text, record, index) => {
            if (record.progressStatus == 2) {
              return "联络站处理";
            } else if (record.progressStatus == 3) {
              return "12345热线处理";
            } else if (record.progressStatus == 4) {
              return "已回复";
            }
          },
        },
        {
          title: "意见内容",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "generalComments",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "代表手机",
          align: "center",
          width: 160,
          ellipsis: true,
          dataIndex: "userPhone",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        // {
        //   title: "所属区域",
        //   align: "center",
        //   width: 100,
        //   ellipsis: true,
        //   dataIndex: "district",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "所属乡镇街道",
        //   align: "center",
        //   width: 170,
        //   ellipsis: true,
        //   dataIndex: "street",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "联络站",
        //   align: "center",
        //   width: 160,
        //   ellipsis: true,
        //   dataIndex: "interfaceLocation",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "站长姓名",
        //   align: "center",
        //   width: 100,
        //   ellipsis: true,
        //   dataIndex: "stationAgentName",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "站长电话",
        //   align: "center",
        //   width: 160,
        //   ellipsis: true,
        //   dataIndex: "stationAgentPhoneNumber",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        {
          title: "反映涉及主体",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "subject",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "地址",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "address",
          customRender: (text, record, index) => {
            return text;
          },
        },
        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            // 操作权限暂都打开
            let permission = record.progressStatus == 2;
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(record);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "inline-block" : "none",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.huifu(record.id);
                    },
                  },
                },
                "回复"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "inline-block" : "none",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.turn(record.id);
                    },
                  },
                },
                "转办"
              ),
            ]);
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created () {
    let queryForm = JSON.parse(localStorage.getItem("tongjifenzi"));
    if (queryForm) {
      this.queryForm = queryForm
      this.pagination.current = this.queryForm.pageNum;
      // 回显时间
      if (this.queryForm.startTime) {
        var dateRanglist = this.queryForm.startTime + ',' + this.queryForm.endTime;
        this.dateRange = dateRanglist.split(',');
      }
    }
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "代表随手拍");
    this.$store.dispatch("navigation/breadcrumb2", "随手拍意见建议处理");
  },
  beforeDestroy () {
    window.localStorage.setItem("tongjifenzi", JSON.stringify(this.queryForm));
  },
  mounted () {
    // 抽屉滚动区高度
    window.onresize = () => {
      return (() => {
        this.drawerHeight = window.innerHeight - 75 + "px";
        this.listHeight = window.innerHeight - 360 + "px";
      })();
    };
  },
  filters: {
    getState: function (state) {
      if (state == 2) {
        return "联络站处理";
      } else if (state == 3) {
        return "12345热线处理";
      } else if (state == 4) {
        return "已回复";
      }
    },
  },
  computed: {
    conditionsCount: function () {
      let size = 0;
      console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
      for (const key in this.queryForm) {
        if (Object.hasOwnProperty.call(this.queryForm, key)) {
          if (key == "pageNum" || key == "pageSize") {
            continue;
          }
          const element = this.queryForm[key];
          if (
            Array.isArray(element)
              ? element.length > 0
              : element !== "" && element != null
          ) {
            size++;
          }
        }
      }
      return size;
    },
  },
  watch: {
    conditionsCount (newVal, oldVal) {
      console.log("🤗🤗🤗, newVal =>", newVal);
      this.badgeHide = newVal <= 0;
    },
  },

  methods: {
    // 高级搜索
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    //重置
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        subject: null,
        status: null,
        // progressStatus: null,
        address: null,
        commentCategory: null,
        startTime: null,
        endTime: null,
      };
      this.dateRange = []
      this.fetchData();
    },
    // 时间选择
    onTimeChange (val) {
      // console.log("🤗🤗🤗, val =>", val);
      this.queryForm.startTime = val[0];
      this.queryForm.endTime = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },
    // 点击行
    clickRow (record) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            if ("__vue__" in event.target) {
              this.handleEdit(record);
            }
          },
        },
      };
    },
    // 切换页数
    changePageSize (pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange (pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    commitFu: function () {
      let self = this;
      console.log(self.huifuId);

      if (self.huifuDate.value) {
        self.dialogVisible = false;

        instance_2({
          method: "post",
          url: "/memberComment/liaisonOpinion",
          headers: {
            "Content-type": "application/x-www-form-urlencoded",
          },
          params: {
            id: self.huifuId,
            opinion: self.huifuDate.value,
            isTransform: false,
          },
        }).then((data) => {
          console.log(data);
          if (data.data.code == "0000") {
            this.$message.success("请求成功");
            self.acquireData(self.page, self.size);
            this.fetchData();
          } else {
            this.$message.error(data.data.msg);
          }
        });
      } else {
        this.$message.error("请填写回复内容及回复人");
      }
    },

    turn: function (id) {
      let self = this;
      //console.log(self.huifuId)

      instance_2({
        method: "post",
        url: "/memberComment/liaisonOpinion",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          id: id,
          opinion: null,
          isTransform: true,
        },
      }).then((data) => {
        console.log(data);
        if (data.data.msg == "成功") {
          this.$message.success("请求成功");
          self.acquireData(self.page, self.size);
          this.fetchData();
        } else {
          this.$message.error(data.data.msg);
        }
      });
    },

    //获取勾选的行数据
    setSelectRows (val) {
      this.selectRows = val;
    },
    rowClick (row, column, event) {
      if (column.label !== "操作") {
        this.handleEdit(row);
      }
    },
    //切换页面条数
    handleSizeChange (val) {
      this.queryForm.size = val;
      this.fetchData();
    },
    // 弹出层
    handleClose (done) {
      this.$baseConfirm("确认关闭？")
        .then((_) => {
          done();
          this.dialogVisible = false;
        })
        .catch((_) => { });
    },
    //查询
    handleQuery () {
      this.drawer = false;
      this.queryForm.pageNum = 1;
      this.fetchData();
    },
    //回复
    huifu: function (id) {
      this.dialogVisible = true;
      this.huifuId = id;
    },
    //重置查询参数
    handleClear () {
      if (this.$refs["drawerForm"] !== undefined) {
        this.$refs["drawerForm"].resetFields();
      }
    },

    handleEdit (row) {
      this.$router
        .push({
          path: "/suishou/details",
          query: {
            id: row.id,
          },
        })
        .catch(() => { });
    },

    //删除
    handleDelete (row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          instance_1({
            method: "get",
            url: "/memberComment/delete",
            params: {
              id: row.id,
            },
          }).then((res) => {
            if (res.data.code == "0000") {
              this.$baseMessage("删除成功", "success");
              this.fetchData();
            } else {
              this.$baseMessage(res.data.msg, "success");
            }
          });
        });
      } else {
        //批量删除，暂无接口
        if (this.selectRows.length > 0) {
          const ids = this.selectRows.map((item) => item.id);
          this.$baseConfirm("你确定要删除选中项吗", null, () => { });
        } else {
          this.$baseMessage("未选中任何行", "error");
          return false;
        }
      }
    },

    //查询列表数据
    fetchData () {
      this.listLoading = true;
      instance_1({
        method: "get",
        url: "/memberComment/listForLiaisonStation",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "200") {
          if (res.data.rows == 0) {
            //
          } else {
            this.list = res.data.rows;
            this.total = res.data.total;
          }
        }

        setTimeout(() => {
          this.listLoading = false;
        }, 500);
      });
    },

    // checkPermission,
  },
};
</script>
<style>
.tableLimit tr td .cell {
  display: -webkit-box !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  -webkit-line-clamp: 2 !important; /* 可以显示的行数，超出部分用...表示 */
  -webkit-box-orient: vertical !important;
}

.tableLimit .a-button--small {
  padding: 0 !important;
}
</style>
