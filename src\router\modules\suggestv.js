import Layout from "@/layouts";
import AppMain from "@/layouts/components/childrenAppMain/index";

// {
//   path: "staging",
//   name: "InformStaging",
//   component: () => import("@/views/meeting/testPage/index"),
//   meta: {
//     title: "待办任务",
//     permissions: ["I_XTGLY", "I_XXFBY"],
//   },
// },
// 代表建议管理
export default [
  {
    path: "/recommendedMage",
    component: Layout,
    redirect: "noRedirect",
    name: "recommendedMage",
    title: "代表建议管理",
    meta: {
      title: "首页",
    },
    children: [
      {
        path: "recommendedMageIndex",
        name: "recommendedMageIndex",
        component: () => import("@/views/recommendedMage/representativeHome"),
        meta: {
          title: "代表首页",
        },
      },
      {
        path: "recommendedMageWorkIndex",
        name: "recommendedMageWorkIndex",
        component: () =>
          import("@/views/recommendedMage/recommendedMageWorkIndex"),
        meta: {
          title: "工作人员首页",
        },
      },
      // {
      //   path: "regulatoryReadingNotice",
      //   name: "regulatoryReadingNotice",
      //   component: () =>
      //     import("@/views/meeting/policiesRegulations/regulatoryReadingNotice"),
      //   meta: {
      //     title: "各项通知",
      //   },
      // },
    ],
  },
  {
    path: "/meSuggestv",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表建议管理",
    meta: {
      title: "我的代表建议",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "addSuggestv",
        name: "addSuggestv",
        component: () => import("@/views/meeting/suggesting/addSuggestv"),
        meta: {
          title: "新增建议",
        },
      },

      {
        path: "draftBoxSuggestion",
        name: "draftBoxSuggestion",
        component: () =>
          import("@/views/meeting/suggesting/draftBoxSuggestion"),
        meta: {
          title: "草稿箱建议",
        },
      },

      // {
      //   path: "mySuggestion",
      //   name: "Comprehensive2",
      //   component: () =>
      //     import("@/views/meeting/comprehensiveAccess/mySuggestion"),
      //   meta: {
      //     title: "我提出的建议",
      //   },
      // },
      {
        path: "myLeadSuggestion",
        name: "Comprehensive3",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/myLeadSuggestion"),
        meta: {
          title: "我领衔的建议",
        },
      },
      {
        path: "mySecondedSuggestion",
        name: "Comprehensive5",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/mySecondedSuggestion"),
        meta: {
          title: "我联名的建议",
        },
      },
      {
        path: "refuseJoint",
        name: "refuseJoint",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/refuseJoint"),
        meta: {
          title: "我拒绝联名的建议",
        },
      },
      {
        path: "returnedSugg",
        name: "returnedSugg",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/myReturnedSuggestion"),
        meta: {
          title: "被退回的建议",
        },
      },
    ],
  },
  //区镇代表录入建议信息
  {
    path: "/meSuggestvQZ",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表建议管理",
    meta: {
      title: "我的代表建议（区镇）",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "addSuggestv",
        name: "addSuggestv",
        component: () => import("@/views/meeting/suggesting/addSuggestvQz"),
        meta: {
          title: "录入建议",
        },
      },
      {
        path: "myLeadSuggestion",
        name: "Comprehensive3",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/myLeadSuggestionQz"),
        meta: {
          title: "我录入的建议",
        },
      },
    ],
  },
  {
    path: "/Suggestv",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表建议管理",
    meta: {
      title: "代表建议处理",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "changeSuggesting",
        name: "changeSuggesting",
        component: () => import("@/views/meeting/suggesting/changeSuggesting"),
        meta: {
          title: "修改建议",
        },
        hidden: true,
      },
      {
        path: "suggestPending",
        name: "suggestPending",
        component: () => import("@/views/meeting/suggesting/suggestPending"),
        meta: {
          title: "待处理建议",
        },
      },
      {
        path: "processedSuggestion",
        name: "processedSuggestion",
        component: () =>
          import("@/views/meeting/suggesting/processedSuggestion"),
        meta: {
          title: "已处理建议",
        },
      },
      /*{
        path: "completedSuggesting",
        name: "completedSuggesting",
        component: () =>
          import("@/views/meeting/suggesting/completedSuggesting"),
        meta: {
          title: "已办结建议",
        },
      },*/
      {
        path: "induction",
        name: "induction",
        component: () => import("@/views/meeting/suggesting/induction"),
        meta: {
          title: "代录入建议",
        },
      },
      {
        path: "connected",
        name: "connected",
        component: () => import("@/views/meeting/suggesting/connected"),
        meta: {
          title: "代表联名",
        },
        hidden: true,
      },
      {
        path: "intendedList",
        name: "intendedList",
        component: () => import("@/views/meeting/suggesting/intendedList"),
        meta: {
          title: "代表团效验(校核)",
        },
        hidden: true,
      },
      {
        path: "backList",
        name: "backList",
        component: () => import("@/views/meeting/suggesting/backList"),
        meta: {
          title: "退回记录的建议",
        },
        hidden: true,
      },
      {
        path: "assignBackList",
        name: "assignBackList",
        component: () => import("@/views/meeting/suggesting/DesignatedReturnList"),
        meta: {
          title: "分办环节退回记录的建议",
        },
        hidden: true,
      },
      {
        path: "ChooseList",
        name: "ChooseList",
        component: () => import("@/views/meeting/suggesting/ChooseList"),
        meta: {
          title: "代表工委审查(审核)",
        },
        hidden: true,
      },
      {
        path: "ChooseListDelay",
        name: "ChooseListDelay",
        component: () => import("@/views/meeting/suggesting/ChooseListDelay"),
        meta: {
          title: "申请延期(代表工委)",
        },
        hidden: true,
      },
      {
        path: "contentList",
        name: "contentList",
        component: () => import("@/views/meeting/suggesting/contentList"),
        meta: {
          title: "内容分类",
        },
        hidden: true,
      },
      {
        path: "firstCheckList",
        name: "firstCheckList",
        component: () => import("@/views/meeting/suggesting/firstCheckList"),
        meta: {
          title: "初审",
        },
        hidden: true,
      },
      {
        path: "auditReturnList",
        name: "auditReturnList",
        component: () => import("@/views/meeting/suggesting/auditReturnList"),
        meta: {
          title: "审核退回建议",
        },
        hidden: true,
      },
      {
        path: "assignConfirmList",
        name: "assignConfirmList",
        component: () => import("@/views/meeting/suggesting/assignConfirmList"),
        meta: {
          title: "分办确认",
        },
        hidden: true,
      },
      {
        path: "assignDbcConfirmList",
        name: "assignDbcConfirmList",
        component: () => import("@/views/meeting/suggesting/assignDbcConfirmList"),
        meta: {
          title: "分办二次确认",
        },
        hidden: true,
      },
      {
        path: "bianBanConfirmList",
        name: "bianBanConfirmList",
        component: () => import("@/views/meeting/suggesting/bianBanConfirmList"),
        meta: {
          title: "编办确认",
        },
        hidden: true,
      },
      {
        path: "xlwConfirmList",
        name: "xlwConfirmList",
        component: () => import("@/views/meeting/suggesting/xlwConfirmList"),
        meta: {
          title: "建议组负责人确认",
        },
        hidden: true,
      },
      {
        path: "dbcConfirmList",
        name: "dbcConfirmList",
        component: () => import("@/views/meeting/suggesting/dbcConfirmList"),
        meta: {
          title: "交办小组长确认",
        },
        hidden: true,
      },
      {
        path: "unRegisterList",
        name: "unRegisterList",
        component: () => import("@/views/meeting/suggesting/unRegisterList"),
        meta: {
          title: "不予立案审核",
        },
        hidden: true,
      },
      {
        path: "doUnRegisterList",
        name: "doUnRegisterList",
        component: () => import("@/views/meeting/suggesting/doUnRegisterList"),
        meta: {
          title: "不予立案确认",
        },
        hidden: true,
      },
      // {
      //   path: "setKeySuggestions",
      //   name: "setKeySuggestions",
      //   component: () => import("@/views/meeting/suggesting/setKeySuggestions"),
      //   meta: {
      //     title: "设置重点建议",
      //   },
      // },
      {
        path: "Designated",
        name: "Designated",
        component: () => import("@/views/meeting/suggesting/Designated"),
        meta: {
          title: "指定办理单位(交办)",
        },
        hidden: true,
      },
      {
        path: "transactList",
        name: "transactList",
        component: () => import("@/views/meeting/suggesting/transactList"),
        meta: {
          title: "办理单位签收",
        },
        hidden: true,
      },
      {
        path: "answerList",
        name: "answerList",
        component: () => import("@/views/meeting/suggesting/answerList"),
        meta: {
          title: "办理单位答复",
        },
        hidden: true,
      },
      {
        path: "helpReplyList",
        name: "helpReplyList",
        component: () => import("@/views/meeting/suggesting/helpReplyList"),
        meta: {
          title: "代替办理单位答复",
        },
        hidden: true,
      },
      {
        path: "extensionList",
        name: "extensionList",
        component: () => import("@/views/meeting/suggesting/extensionList"),
        meta: {
          title: "办理单位申请延期",
        },
      },
      {
        path: "AdjustList",
        name: "AdjustList",
        component: () => import("@/views/meeting/suggesting/AdjustList"),
        meta: {
          title: "调整办理单位(申请调整)",
        },
      },
      {
        path: "outcomeList",
        name: "outcomeList",
        component: () => import("@/views/meeting/suggesting/outcomeList"),
        meta: {
          title: "办理结果评价(反馈)",
        },
        hidden: true,
      },
      {
        path: "undertakingSug",
        name: "undertakingSug",
        component: () => import("@/views/meeting/suggesting/undertakingSug"),
        meta: {
          title: "承办建议列表",
        },
      },
      {
        path: "expediteList",
        name: "expediteList",
        component: AppMain,
        meta: {
          title: "建议催办",
        },
        children: [
          {
            path: "unreply",
            name: "unreply",
            component: () => import("@/views/meeting/Bill/unreply"),
            meta: {
              title: "未答复列表",
            },
          },
          {
            path: "unsign",
            name: "unsign",
            component: () => import("@/views/meeting/Bill/unsign"),
            meta: {
              title: "未签收列表",
            },
          },
          {
            path: "overdue",
            name: "overdue",
            component: () => import("@/views/meeting/Bill/overdue"),
            meta: {
              title: "逾期未答复列表",
            },
          },
          {
            path: "unfeedback",
            name: "unfeedback",
            component: () => import("@/views/meeting/Bill/unfeedback"),
            meta: {
              title: "未反馈建议列表",
            },
          },
          {
            path: "overdueMonth",
            name: "overdueMonth",
            component: () => import("@/views/meeting/Bill/overdueMonth"),
            meta: {
              title: "逾期一个月未反馈",
            },
          },
          {
            path: "unsatisfy",
            name: "unsatisfy",
            component: () => import("@/views/meeting/Bill/unsatisfy"),
            meta: {
              title: "反馈不满意建议",
            },
          },
        ],
      },
      {
        path: "coerceList",
        name: "coerceList",
        component: () => import("@/views/meeting/suggesting/coerceList"),
        meta: {
          title: "强制办结",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/SuggestvMotion",
    component: Layout,
    redirect: "noRedirect",
    name: "proposal",
    title: "代表建议管理",
    meta: {
      title: "代表议案办理",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "agendaList",
        name: "agendaList",
        component: () => import("@/views/meeting/suggesting/agendaList"),
        meta: {
          title: "议案列表",
        },
      },
    ],
  },
  {
    path: "/yjsPublicManage",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表建议管理",
    meta: {
      title: "研究室意见管理",
      icon: "database",
      permissions: ["I_XTGLY", "YAJY_XCC"],
    },
    alwaysShow: false,
    children: [
      {
        path: "yjsDoPublishList",
        name: "yjsDoPublishList",
        component: () => import("@/views/meeting/suggesting/publicYjsList.vue"),
        meta: {
          title: "研究室意见",
        },
        // hidden: true,
      },
    ],
  },

  {
    path: "/leaderCheckManage",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表建议管理",
    meta: {
      title: "领导审定管理",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "leaderAuditList",
        name: "leaderAuditList",
        component: () => import("@/views/meeting/suggesting/leaderAuditList.vue"),
        meta: {
          title: "领导审定",
        },
        // hidden: true,
      },
    ],
  },

  {
    path: "/Appraisals",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "代表建议管理",
    meta: {
      title: "办理绩效考核",
    },
    children: [
      {
        path: "questionBank",
        name: "questionBank",
        component: () => import("@/views/meeting/suggesting/questionBank"),
        meta: {
          title: "试题库",
        },
      },
      {
        path: "questionBankPreview",
        name: "questionBankPreview",
        component: () =>
          import("@/views/meeting/suggesting/questionBankPreview"),
        meta: {
          title: "试题库预览",
        },
        hidden: true,
      },
      {
        path: "testManagement",
        name: "testManagement",
        component: () => import("@/views/meeting/suggesting/testManagement"),
        meta: {
          title: "试题管理",
        },
        hidden: true,
      },
      {
        path: "suggestedScoreQuery",
        name: "suggestedScoreQuery",
        component: () =>
          import("@/views/meeting/suggesting/suggestedScoreQuery"),
        meta: {
          title: "建议分数查询",
        },
      },
      {
        path: "assessmentMattersManage",
        name: "assessmentMattersManage",
        component: () =>
          import("@/views/meeting/suggesting/assessmentMattersManage"),
        meta: {
          title: "考核事项管理",
        },
      },
      {
        path: "assessmentTable",
        name: "assessmentTable",
        component: () => import("@/views/meeting/suggesting/assessmentTable"),
        meta: {
          title: "考核事项细则管理",
        },
        hidden: true,
      },
      {
        path: "workEvaluation",
        name: "workEvaluation",
        component: () => import("@/views/meeting/suggesting/workEvaluation"),
        meta: {
          title: "工作评价",
        },
      },
      {
        path: "workAnalyze",
        name: "workAnalyze",
        component: () => import("@/views/meeting/suggesting/workAnalyze"),
        meta: {
          title: "工作评价-分析整改填报页面",
        },
        hidden: true,
      },
      {
        path: "artificialAssessment",
        name: "artificialAssessment",
        component: () =>
          import("@/views/meeting/suggesting/artificialAssessment"),
        meta: {
          title: "工作评价-人工考核页面",
        },
        hidden: true,
      },
      {
        path: "workEvaluationScore",
        name: "workEvaluationScore",
        component: () =>
          import("@/views/meeting/suggesting/workEvaluationScore"),
        meta: {
          title: "工作评价-评分页面",
        },
        hidden: true,
      },
      {
        path: "onlinePublic",
        name: "onlinePublic",
        component: () => import("@/views/meeting/suggesting/onlinePublic"),
        meta: {
          title: "网上公开",
        },
      },
      {
        path: "evaluationRanking",
        name: "evaluationRanking",
        component: () => import("@/views/meeting/suggesting/evaluationRanking"),
        meta: {
          title: "评价排名表",
        },
      },
      {
        path: "governmentAffairs",
        name: "governmentAffairs",
        component: () => import("@/views/meeting/suggesting/governmentAffairs"),
        meta: {
          title: "政务协同",
        },
      },
      {
        path: "appeal",
        name: "appeal",
        component: () => import("@/views/meeting/suggesting/appeal"),
        meta: {
          title: "申诉处理",
        },
      },
      {
        path: "appealReview",
        name: "appealReview",
        component: () => import("@/views/meeting/suggesting/appealReview"),
        meta: {
          title: "申述处理审核列表",
        },
        hidden: true,
      },
      {
        path: "assessment",
        name: "assessment",
        component: () => import("@/views/meeting/suggesting/assessment"),
        meta: {
          title: "考核明细",
        },
      },
      {
        path: "attachmentList",
        name: "attachmentList",
        component: () => import("@/views/meeting/suggesting/attachmentList"),
        meta: {
          title: "附件列表",
        },
      },
      {
        path: "rectificationStatistics",
        name: "rectificationStatistics",
        component: () =>
          import("@/views/meeting/suggesting/rectificationStatistics"),
        meta: {
          title: "分析整改统计",
        },
      },
    ],
  },
  {
    path: "/RepresentsV",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "代表建议管理",
    meta: {
      title: "代表建议资源库",
    },
    children: [
      {
        path: "intelligentSearch",
        name: "intelligentSearch",
        component: () => import("@/views/meeting/intelligentSearch/index"),
        meta: {
          title: "智能搜索",
        },
      },
      {
        path: "resourseRep",
        name: "resourseRep",
        component: () => import("@/views/meeting/resourseRep/index"),
        meta: {
          title: "本会建议资源库",
        },
      },
      {
        path: "countrySourseRep",
        name: "countrySourseRep",
        component: () => import("@/views/meeting/countrySourseRep/index"),
        meta: {
          title: "全国建议资源库",
        },
      },
    ],
  },
  {
    path: "/comprehensiveAccess",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "代表建议管理",
    meta: {
      title: "综合查阅",
    },
    children: [
      {
        path: "combinationAccess",
        name: "Comprehensive1",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/combinationAccess"),
        meta: {
          title: "组合查阅",
        },
      },

      {
        path: "dbLeadAndCombinationSuggestion",
        name: "Comprehensive99",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/dbLeadAndCombinationSuggestion"),
        meta: {
          title: "代表领衔和联名建议",
        },
      },
      {
        path: "dbOtherSuggestion",
        name: "Comprehensive109",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/dbOtherSuggestion"),
        meta: {
          title: "其他代表建议",
        },
      },

      {
        path: "conventionSuggestion",
        name: "Comprehensive6",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/conventionSuggestion"),
        meta: {
          title: "大会建议",
        },
      },
      {
        path: "closingConventionSuggestion",
        name: "Comprehensive7",
        component: () =>
          import(
            "@/views/meeting/comprehensiveAccess/closingConventionSuggestion"
          ),
        meta: {
          title: "闭会建议",
        },
      },
      {
        path: "suggestionOfBeTurnReferenceOnly",
        name: "Comprehensive8",
        component: () =>
          import(
            "@/views/meeting/comprehensiveAccess/suggestionOfBeTurnReferenceOnly"
          ),
        meta: {
          title: "转为供参考的建议",
        },
      },
      {
        path: "suggestionOfOthers",
        name: "Comprehensive9",
        component: () =>
          import(
            "@/views/meeting/comprehensiveAccess/othersSuggestion.vue"
            ),
        meta: {
          title: "其他建议",
        },
      },
      {
        path: "suggestionOfHandle",
        name: "Comprehensive9",
        component: () =>
          import(
            "@/views/meeting/comprehensiveAccess/suggestionHandle.vue"
            ),
        meta: {
          title: "政府建议办理情况",
        },
      },
      // {
      //   path: "suggestionOfNotNeedDeal",
      //   name: "Comprehensive9",
      //   component: () =>
      //     import("@/views/meeting/comprehensiveAccess/suggestionOfNotNeedDeal"),
      //   meta: {
      //     title: "无需处理的建议",
      //   },
      // },
      {
        path: "SuggestionOfkeySupervision",
        name: "Comprehensive10",
        // 12-29 新
        component: () => import("@/views/meeting/Bill/emphasis"),
        meta: {
          title: "重点督办的建议",
        },
      },
      {
        path: "publicSuggestion",
        name: "Comprehensive11",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/publicSuggestion"),
        meta: {
          title: "已公开的建议",
        },
      },
      {
        name: "askList",
        path: "askList",
        component: () => import("@/views/askList/askList"),
        meta: {
          title: "询问列表管理",
        },
      },
      {
        path: "inquiries",
        name: "inquiries",
        component: () => import("@/views/inquiries/inquiries"),
        meta: {
          title: "质询列表管理",
        },
      },
      {
        path: "recommendedInquiry",
        name: "recommendedInquiry",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/recommendedInquiry"),
        meta: {
          title: "往届建议查询",
        },
      },
      {
        path: "OfficialReplyLetter",
        name: "OfficialReplyLetter",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/OfficialReplyLetter"),
        meta: {
          title: "承办单位答复函",
        },
      },
      {
        path: "nowInformationMaintain",
        name: "nowInformationMaintain",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/nowInformationMaintain"),
        meta: {
          title: "本届代表信息维护",
        },
      },
      {
        path: "successiveMaintain",
        name: "successiveMaintain",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/successiveMaintain"),
        meta: {
          title: "历届代表信息维护",
        },
      },
      {
        path: "unitAgencyContact",
        name: "unitAgencyContact",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/unitAgencyContact"),
        meta: {
          title: "单位机构联系人",
        },
      },
      // ++列表
      {
        path: "RecommendKeySupervision",
        name: "RecommendKeySupervision",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/RecommendKeySupervision"),
        meta: {
          title: "推荐重点督办",
        },
      },
      {
        path: "KeySupervisionSuggestionsUnits",
        name: "KeySupervisionSuggestionsUnits",
        component: () =>
          import(
            "@/views/meeting/comprehensiveAccess/KeySupervisionSuggestionsUnits"
          ),
        meta: {
          title: "各单位推荐重点督办建议",
        },
      },
    ],
  },
  {
    path: "/workingOnline",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "代表建议管理",
    meta: {
      title: "办理网上公开",
    },
    children: [
      {
        path: "releasedPending",
        name: "releasedPending",
        component: () => import("@/views/workingOnline/releasedPending"),
        meta: {
          title: "提出公开意见",
        },
      },
      {
        path: "released",
        name: "released",
        component: () => import("@/views/workingOnline/released"),
        meta: {
          title: "待审核发布",
        },
      },
      {
        path: "reviewedRelease",
        name: "reviewedRelease",
        component: () => import("@/views/workingOnline/reviewedRelease"),
        meta: {
          title: "已审核发布",
        },
      },
      {
        path: "reviewDoesNotRelease",
        name: "reviewDoesNotRelease",
        component: () => import("@/views/workingOnline/reviewDoesNotRelease"),
        meta: {
          title: "审核不发布",
        },
      },
      {
        path: "handmade",
        name: "handmade",
        component: () => import("@/views/workingOnline/handmade"),
        meta: {
          title: "手工添加",
        },
      },
      {
        path: "antiDynamicList",
        name: "antiDynamicList",
        component: () => import("@/views/workingOnline/antiDynamicList"),
        meta: {
          title: "异动列表",
        },
      },
      {
        path: "websiteOpen",
        name: "websiteOpen",
        component: () => import("@/views/workingOnline/websiteOpen"),
        meta: {
          title: "网站公开建议管理",
        },
      },
    ],
  },
  {
    path: "/statisticalAnalysis",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "代表建议管理",
    meta: {
      title: "统计分析",
    },
    children: [
      {
        path: "RepresentativeSuggestedScoring",
        name: "Scoring",
        component: () =>
          import(
            "@/views/meeting/statisticalAnalysis/RepresentativeSuggestedScoring"
          ),
        meta: {
          title: "代表建议评分",
        },
      },
      {
        path: "unsignedSuggestionStatistics",
        name: "statisticV1",
        component: () =>
          import(
            "@/views/meeting/statisticalAnalysis/unsignedSuggestionStatistics"
          ),
        meta: {
          title: "未签收建议统计",
        },
      },
      {
        path: "suggestedStatusOfOrganizer",
        name: "statisticV2",
        component: () =>
          import(
            "@/views/meeting/statisticalAnalysis/suggestedStatusOfOrganizer"
          ),
        meta: {
          title: "承办单位建议办理状态清单统计分析",
        },
      },
      {
        path: "proposalNumStatistics",
        name: "statisticV3",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/proposalNumStatistics"),
        meta: {
          title: "议案建议数量统计",
        },
      },
      {
        path: "submissionAndFeedback",
        name: "statisticV5",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/submissionAndFeedback"),
        meta: {
          title: "代表提交与反馈建议数目统计",
        },
      },
      {
        path: "suggestionReport",
        name: "statisticV6",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/suggestionReport"),
        meta: {
          title: "建议分类统计报表",
        },
      },
      {
        path: "organizerStatistics",
        name: "statisticV7",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/organizerStatistics"),
        meta: {
          title: "承办单位办理统计",
        },
      },
      {
        path: "scoreSheet",
        name: "statisticV8",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/scoreSheet"),
        meta: {
          title: "建议办理评分表",
        },
      },
      {
        path: "replySheet",
        name: "statisticV9",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/replySheet"),
        meta: {
          title: "承办单位答复统计",
        },
      },
      {
        path: "receiptList",
        name: "statisticV10",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/receiptList"),
        meta: {
          title: "签收一览表",
        },
      },
      {
        path: "replyification",
        name: "replyification",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/replyification"),
        meta: {
          title: "答复一览表",
        },
      },
      {
        path: "mobileFeedback",
        name: "statisticV11",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/mobileFeedback"),
        meta: {
          title: "移动端反馈联名清单",
        },
      },
      {
        path: "assignmentDetails",
        name: "statisticV12",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/assignmentDetails"),
        meta: {
          title: "交办明细",
        },
      },
      {
        path: "representativeFeedbackTime",
        name: "statisticV13",
        component: () =>
          import(
            "@/views/meeting/statisticalAnalysis/representativeFeedbackTime"
          ),
        meta: {
          title: "代表反馈时间",
        },
      },
      {
        path: "representativeSuggestionList",
        name: "statisticV14",
        component: () =>
          import(
            "@/views/meeting/statisticalAnalysis/representativeSuggestionList"
          ),
        meta: {
          title: "代表所提建议清单",
        },
      },
      {
        path: "replyClassification",
        name: "statisticV14",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/replyClassification"),
        meta: {
          title: "答复分类一览表",
        },
      },
      {
        path: "assignedList",
        name: "statisticV15",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/assignedList"),
        meta: {
          title: "交办清单",
        },
      },
      {
        path: "handlingSituation",
        name: "statisticV16",
        component: () =>
          import("@/views/meeting/statisticalAnalysis/handlingSituation"),
        meta: {
          title: "办理情况",
        },
      },
      {
        path: "suggestedSourceStatisticsTable",
        name: "statisticV16",
        component: () =>
          import(
            "@/views/meeting/statisticalAnalysis/suggestedSourceStatisticsTable"
          ),
        meta: {
          title: "建议来源统计表",
        },
      },
      {
        path: "communicationAndFeedbackStatistics",
        name: "statisticV17",
        component: () =>
          import(
            "@/views/meeting/statisticalAnalysis/communicationAndFeedbackStatistics"
          ),
        meta: {
          title: "沟通方式及反馈统计",
        },
      },
    ],
  },
  {
    path: "/policiesRegulations",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "代表建议管理",
    meta: {
      title: "政策法规",
    },
    children: [
      {
        path: "libraryManagement",
        name: "libraryManagement",
        component: () =>
          import("@/views/meeting/policiesRegulations/libraryManagement"),
        meta: {
          title: "政策法规库管理",
        },
      },
      {
        path: "regulationsClassification",
        name: "regulationsClassification",
        component: () =>
          import(
            "@/views/meeting/policiesRegulations/regulationsClassification"
          ),
        meta: {
          title: "政策法规分类",
        },
      },
      {
        path: "regulatoryReading",
        name: "regulatoryReading",
        component: () =>
          import("@/views/meeting/policiesRegulations/regulatoryReading"),
        meta: {
          title: "政策法规阅览",
        },
      },
    ],
  },
  {
    path: "/personnel",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "代表建议管理",
    meta: {
      title: "人员管理",
    },
    children: [
      {
        path: "identity",
        name: "identity",
        component: () =>
          import("@/views/convenTion/peoplement/identity"),
        meta: {
          title: "身份管理",
        },
      },
      {
        path: "category",
        name: "category",
        component: () =>
          import(
            "@/views/convenTion/peoplement/category"
            ),
        meta: {
          title: "身份类别",
        },
      },
      {
        path: "DistributionIdentity",
        name: "DistributionIdentity",
        component: () =>
          import("@/views/convenTion/peoplement/DistributionIdentity"),
        meta: {
          title: "身份分配",
        },
      },
    ],
  },
  {
    path: "/systemIndexjian",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "代表建议管理",
    meta: {
      title: "系统管理",
      permissions: ["ADMIN", "I_XTGLY"],
    },
    children: [
      {
        path: "synchronousManagement",
        name: "synchronousManagement",
        component: () => import("@/views/meeting/synchronousManagement/index"),
        meta: {
          title: "数据同步管理",
        },
      },

      {
        path: "meetingEdit",
        name: "MeetingEdit",
        component: () => import("@/views/meeting/edit"),
        meta: {
          title: "数据权限管理",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "/suggestionDetail",
        name: "suggestionDetail",
        component: () =>
          import("@/views/jy_system/suggestionDetail/suggestionDetail"),
        meta: {
          title: "建议详情页面",
        },
        hidden: true,
      },
      // {
      //   path: "session",
      //   name: "session",
      //   component: () => import("@/views/election/systemIndex/electionMage"),
      // component: () => import("@/views/meeting/session/index"),
      //   meta: {
      //     title: "届次管理",
      //   },
      //   hidden: true,
      // },
      {
        path: "suggestionManage",
        name: "suggestionManage",
        component: () =>
          import("@/views/jy_system/suggestionManage/suggestionManage"),
        meta: {
          title: "代表建议届次管理",
        },
      },
      {
        path: "conferenceManagement",
        name: "conferenceManagement",
        component: () =>
          import("@/views/jy_system/conferenceMange/conferenceMangeIndex"),
        meta: {
          title: "会议管理",
        },
      },
      {
        path: "inUserMage",
        name: "inUserMage",
        component: () => import("@/views/jy_system/inUserMage/inUserMageIndex"),
        meta: {
          title: "议案建议机构用户管理",
        },
      },
      {
        path: "contentClass",
        name: "contentClass",
        component: () =>
          import("@/views/jy_system/contentClass/contentClassIndex"),
        meta: {
          title: "内容分类关联管理",
        },
      },
      {
        path: "smsTemplateMan",
        name: "smsTemplateMan",
        component: () =>
          import("@/views/jy_system/smsTemplateMan/smsTemplateManIndex"),
        meta: {
          title: "短信模板管理",
        },
      },
      {
        path: "sendoutsms",
        name: "sendoutsms",
        component: () => import("@/views/jy_system/sendoutsms/sendoutsms"),
        meta: {
          title: "发送短信",
        },
      },
      {
        path: "journal",
        name: "journal",
        component: () => import("@/views/jy_system/journal/journal"),
        meta: {
          title: "日志管理",
        },
      },
      {
        path: "unitInstitutionalMage",
        name: "unitInstitutionalMage",
        component: () =>
          import(
            "@/views/jy_system/unitInstitutionalMage/unitInstitutionalMage"
          ),
        meta: {
          title: "单位机构管理",
        },
      },
      {
        path: "suggestedEarly",
        name: "suggestedEarly",
        component: () =>
          import("@/views/jy_system/suggestedEarly/suggestedEarly"),
        meta: {
          title: "建议预警管理",
        },
      },
      {
        path: "suggestTpl",
        name: "suggestTpl",
        component: () => import("@/views/jy_system/suggestTpl/suggestTpl"),
        meta: {
          title: "建议纸模板",
        },
      },
      {
        path: "gradingManagement",
        name: "gradingManagement",
        component: () =>
          import("@/views/election/systemIndex/gradingManagement"),
        // component: () => import("@/views/meeting/gradingManagement/index"),
        meta: {
          title: "分级管理",
        },
      },

      // {
      //   path: "menuManage",
      //   name: "MenuManage",
      //   component: () => import("@/views/menu/index"),
      //   meta: {
      //     title: "菜单管理",
      //     permissions: ["ADMIN", "I_XTGLY"],
      //   },
      // },
      // {
      //   path: "roleManage",
      //   name: "RoleManage",
      //   component: () => import("@/views/role/index"),
      //   meta: {
      //     title: "角色管理",
      //     permissions: ["ADMIN", "I_XTGLY"],
      //   },
      // },
      // {
      //   path: "userManage",
      //   name: "UserManage",
      //   component: () => import("@/views/user/index"),
      //   meta: {
      //     title: "用户管理",
      //     permissions: ["ADMIN", "I_XTGLY"],
      //   },
      // },

      // {
      //   path: "editRole",
      //   name: "EditRole",
      //   component: () => import("@/views/role/edit"),
      //   meta: {
      //     title: "编辑角色",
      //     permissions: ["ADMIN", "I_XTGLY"],
      //   },
      //   hidden: true,
      // },

      {
        path: "parameterManagement",
        name: "parameterManagement",
        component: () => import("@/views/meeting/parameterManagement/index"),
        meta: {
          title: "参数管理",
        },
      },
      {
        path: "job",
        name: "job",
        component: () => import("@/views/meeting/job/index"),
        meta: {
          title: "定时任务",
        },
      }
      // },
      // {
      //   path: "joblog",
      //   name: "joblog",
      //   component: () => import("@/views/meeting/job/log"),
      //   meta: {
      //     title: "定时任务日志",
      //   },
      // },
    ],
  },
];
