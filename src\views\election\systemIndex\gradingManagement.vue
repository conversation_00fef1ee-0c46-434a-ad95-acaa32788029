<template>
  <div class="table-container">
    <z-table :list="list"
             :columns="columns"></z-table>
  </div>
</template>
<script>
export default {
  data () {
    return {
      TBloading: false,
      columns: [
        {
          title: "角色名称",
          ellipsis: true,
          dataIndex: "index1",
        },
        {
          title: "是否启用",
          ellipsis: true,
          dataIndex: "index2",
        },
        {
          title: "代表管理范围",
          ellipsis: true,
          dataIndex: "index3",
        },
        {
          title: "用户管理范围",
          ellipsis: true,
          dataIndex: "index4",
        },
        {
          title: "拥有角色的用户",
          ellipsis: true,
          dataIndex: "index5",
        },
        {
          title: "创建者",
          ellipsis: true,
          dataIndex: "index6",
        },
        {
          title: "无操作",
        },
      ],
      list: [
        {
          index1: "admin",
          index2: "是",
          index3: "越秀代表团",
          index4: "温泉镇",
          index5: "魏振兴",
          index6: "系统管理员",
        },
        {
          index1: "测试",
          index2: "是",
          index3: "专门委员会",
          index4: "越秀代表团",
          index5: "系统管理员",
          index6: "系统管理员",
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "系统管理");
    this.$store.dispatch("navigation/breadcrumb2", "分级管理");
  },
};
</script>
