<template>
  <div :class="topBarContainer">
    <byui-main>
      <a-row>
        <!-- <a-col :lg="6" :md="6" :sm="6" :xl="6" :xs="0">
          <logo class="hidden-xs-only" />
        </a-col>-->
        <a-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
          <nav-bar />
        </a-col>
      </a-row>
    </byui-main>
  </div>
</template>

<script>
import Logo from "@/layouts/components/Logo";
import NavBar from "@/layouts/components/NavBar";
import tabItem from "./TopBarItem";
import variables from "@/styles/variables.scss";
import ByuiMain from "@/components/ByuiMain";
import { mapGetters } from "vuex";

export default {
  components: { tabItem, Logo, ByuiMain, NavBar },
  data() {
    return {
      menuTrigger: "hover",
      // topBarContainer:'top-bar-container'
    };
  },
  computed: {
    ...mapGetters(["routes", "layout"]),
    topBarContainer: {
      get: function () {
        if (this.layout === "horizontal") {
          return "top-bar-container qxj-top";
        } else if (this.layout === "theme3") {
          return "top-bar-container thr-top";
        } else {
          return "top-bar-container";
        }
      },
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    variables() {
      return variables;
    },
  },
};
</script>
<style lang="scss" scoped>
.top-bar-container {
  background: $base-color-header;

  .el-menu.el-menu--horizontal {
    border-bottom: solid 0 transparent;
  }

  ::v-deep {
    .byui-main {
      background: $base-color-header;

      .el-menu {
        &--horizontal {
          float: right;
          border-bottom: solid 0 transparent !important;
        }

        > .top-bar-item-container {
          display: inline-block;
        }
      }
    }
  }
}
.qxj-top {
  // height: 90px;
  background-color: #d8293a !important;

  background: url(./header_bgtu.png) no-repeat;
  background-size: cover;
  .byui-main {
    background: transparent !important;
  }
  .logo-container-horizontal {
    background: transparent !important;
  }
}
.thr-top {
  height: 90px;
  background-color: #e0f1fa;
  background: url(header_bg3.png) no-repeat;
  background-size: cover;
  .byui-main {
    background: transparent !important;
  }
  .logo-container-horizontal {
    background: transparent !important;
  }
}
.qxj-top .nav-bar-container {
  background: transparent;
  box-shadow: none;
}
.thr-top .nav-bar-container {
  background: transparent;
  box-shadow: none;
}

.qxj-menu .el-menu-item .is-active {
  background-color: #fff !important;
}
</style>
