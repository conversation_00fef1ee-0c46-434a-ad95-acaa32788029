import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
    return request({
        url: "/api/v1/vehicle/getVehicleList",
        method: "post",
        data,
    });
}

export function doSave(data) {
    return request({
        url: "/api/v1/vehicle/addVehicle",
        method: "post",
        data,
    });
}

export function doUpdate(data) {
    return request({
        url: "/api/v1/vehicle/updateVehicle",
        method: "post",
        data,
    });
}

export function updateAllocation(data) {
    return request({
        url: "/api/v1/vehicle/updateAllocation",
        method: "post",
        data,
    });
}

export function doDelete(data) {
    return request({
        url: "/api/v1/vehicle/delVehicle",
        method: "post",
        data
    });
}

export function doAgree(data, param) {
    return request({
        url: "/api/v1/vehicle/updateAgree/" + param,
        method: "post",
        data
    });
}

export function addVehicleList(data) {
    return request({
        url: "/api/v1/vehicle/addVehicleList",
        method: "post",
        data,
    });
}

export function getVehicleIdUser(data) {
    return request({
        url: "/api/v1/vehicle/getVehicleIdUser",
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        transformRequest: [
            function(data) {
                return qs.stringify(data);
            },
        ],
    });
}

export function getById(data) {
    return request({
        url: "/api/v1/vehicle/getById",
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        transformRequest: [
            function(data) {
                return qs.stringify(data);
            },
        ],
    });
}

export function getPage(data) {
    return request({
        url: "/api/v1/vehicleApply/getPage",
        method: "post",
        data,
    });
}

export function doApplySave(data) {
    return request({
        url: "/api/v1/vehicleApply/save",
        method: "post",
        data,
    });
}

export function doApplyUpdate(data) {
    return request({
        url: "/api/v1/vehicleApply/save",
        method: "post",
        data,
    });
}

export function getApplyById(data) {
    return request({
        url: "/api/v1/vehicleApply/getById",
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        transformRequest: [
            function(data) {
                return qs.stringify(data);
            },
        ],
    });
}

export function getVehicleTypeList() {
    return request({
      url: "/api/v1/vehicle/getVehicleTypeList",
      method: "post",
    });
  }