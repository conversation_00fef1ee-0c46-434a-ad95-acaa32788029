<template>
  <div class="table-container">
    <SearchForm :no-more="true" @onReset="reset" @onSearch="handleQuery">
      <template v-slot:topSearch>
        <SingleSearch
          :title="'标题'"
          :span-num="10"
          @onEnter="handleQuery"
          @getContent="
            (val) => {
              listQuery.title = val;
            }
          "
        />
        <SingleSelect
          :title="'是否阅读'"
          :span-num="10"
          :select-list="typeOptions"
          :show-name="'label'"
          :show-value="'value'"
          @change="
            (val) => {
              listQuery.isRead = val;
            }
          "
        />
      </template>
    </SearchForm>

    <a-row>
      <!-- <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <a-row>
          <a-form ref="listQuery" :model="listQuery" layout="horizontal" :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18, offset: 0 }">
            <div>
              <a-row style="margin-left: 1%;">
                <a-col :span="6">
                  <a-form-item label="标题" :label-col="{ span: 6 }" :wrapper-col="{ span: 18, offset: 0 }">
                    <a-input v-model="listQuery.title" placeholder="请输入标题" clearable allow-clear
                      @keyup.enter="handleQuery">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="是否阅读" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                    <a-select v-model="listQuery.isRead" clearable placeholder="请选择是否阅读" @change="handleTypeChange"
                      class="table-input">
                      <a-select-option v-for="(item, index) in typeOptions" :key="index" :label="item.label"
                        :value="item.value">{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                 <a-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6"> -->
      <!--  <a-form-model-item :label-col="{ span: 5 }" :wrapper-col="{ span: 17, offset: 0 }" label="时间范围">
                    <a-range-picker style="width: 100%" :ranges="{
                      最近三个月: [
                        moment(new Date()).subtract(2, 'months'),
                        moment(),
                      ],
                      '今年1-6月': [
                        moment(moment().startOf('year')).startOf('month'),
                        moment(moment().startOf('year'))
                          .add(5, 'months')
                          .endOf('month'),
                      ],
                      '今年7-12月': [
                        moment(moment().startOf('year'))
                          .add(6, 'months')
                          .startOf('month'),
                        moment(moment().startOf('year'))
                          .add(11, 'months')
                          .endOf('month'),
                      ],
                      今年内: [
                        moment(moment().startOf('year')).startOf('month'),
                        moment(moment().startOf('year'))
                          .add(11, 'months')
                          .endOf('month'),
                      ],
                    }" v-model="dateRange" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="onTimeChange" />
                  </a-form-model-item> -->
      <!-- </a-col>
                <a-col :span="6">
                  <span style="float: right; margin-top: 3px;">

                    <a-button type="primary" @click="handleQuery">搜索</a-button>
                    <a-button style="margin-left: 12px;" class="pinkBoutton" @click="reset">重置</a-button>
                  </span>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </a-row>
      </a-col> -->

      <div style="float: right; margin: 3px 0 0 10px; color: red">
        可点击对应的表头进行排序
      </div>

      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <!-- 更多查询条件抽屉 结束 -->
        <!-- table -->
        <a-spin :spinning="listLoading">
          <a-table
            ref="table"
            :bordered="false"
            class="tableLimit"
            :columns="columns"
            :pagination="pagination"
            :data-source="list"
            :row-key="
              (record, index) => {
                return index + record.id;
              }
            "
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            :scroll="{ x: 300, y: 0 }"
            @change="handleTableChange"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { instance_1, instance_3 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
import Vue from "vue";
import { getWeekByUserId } from "@/api/dbReport/dbReport";
import SingleSelect from "@/components/SingleSelect/index";
import SearchForm from "@/components/SearchForm/index";
import SingleSearch from "@/components/SingleSearch/index";

export default {
  name: "Table",
  components: {
    SingleSelect,
    SearchForm,
    SingleSearch,
  },
  data() {
    return {
      TBloading: false,
      selectedRowKeys: [],
      dateRange: [],
      downloadLoading: false,
      typeOptions: [
        {
          value: "0",
          label: "否",
        },
        {
          value: "1",
          label: "是",
        },
      ],
      listLoading: false,
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      filePath: "",

      list: [],
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        isRead: "1",
        userId: "",
        title: "",
        startTime: null,
        endTime: null,
      },
      indexNum: 1,
      IShow: false,
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "代表姓名",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "标题",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "title",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "周期",
          align: "center",
          width: 80,
          ellipsis: true,
          dataIndex: "num",
          sorter: true,
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "是否已读",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "isRead",
          customRender: (text, record, index) => {
            return record.isRead == "1" ? "是" : "否";
          },
        },
        {
          title: "阅读时间",
          align: "center",
          width: 200,
          ellipsis: true,
          sorter: true,
          dataIndex: "isReadTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
      ],
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created() {
    this.listQuery.startTime = this.$route.query.startTime;
    this.listQuery.endTime = this.$route.query.endTime;
    this.listQuery.userId = this.$route.query.userId;
    this.$store.dispatch("navigation/breadcrumb1", "代表随手拍");
    this.$store.dispatch("navigation/breadcrumb2", "随手拍意见建议上传");
    this.fetchData();
  },

  mounted() {
    // 抽屉滚动区高度
    window.onresize = () => {
      return (() => {
        this.drawerHeight = window.innerHeight - 75 + "px";
        this.listHeight = window.innerHeight - 360 + "px";
      })();
    };
  },

  methods: {
    // 时间选择
    onTimeChange(val) {
      // console.log("🤗🤗🤗, val =>", val);
      this.listQuery.startTime = val[0];
      this.listQuery.endTime = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },
    handleTypeChange() {
      this.fetchData();
    },

    // 高级搜索
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    //重置
    reset() {
      this.listQuery.pageNum = 1;
      this.listQuery.pageSize = 10;
      this.listQuery.isRead = "1";
      this.listQuery.title = "";
      this.listQuery.startTime = null;
      this.listQuery.endTime = null;
      this.listQuery.sort = null;
      this.listQuery.order = null;
      this.pagination.current = 1;
      this.dateRange = [];
      this.fetchData();
    },
    // 时间选择
    onTimeChangeTJ(val) {
      // console.log("🤗🤗🤗, val =>", val);
      this.listQuery.submitStartTime = val[0];
      this.listQuery.submitEndTime = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },

    // 时间选择
    onTimeChangeHF(val) {
      // console.log("🤗🤗🤗, val =>", val); receiveTime
      this.listQuery.receiveStartTime = val[0];
      this.listQuery.receiveEndTime = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },

    //获取勾选的行数据
    setSelectRows(val) {
      this.selectRows = val;
    },
    rowClick(row, column, event) {
      if (column.label !== "操作") {
        this.handleEdit(row);
      }
    },
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    // 导出
    download() {
      this.downloadLoading = true;
      var arr = [];
      if (this.selectRows.length != 0) {
        // return this.$message.warning("请选择数据！");
        arr = this.selectRows.map((item) => item.id);
      }
      let data =
        arr.length == 0
          ? { ...this.newQueryForm, ...this.listQuery, flag: 2 }
          : { ids: arr, flag: 2 };
      instance_1({
        url: "directTrain/exportWord",
        method: "post",
        responseType: "blob",
        // params: this.listQuery,
        data: data,
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        let fileName = "";
        // if (this.selectRows.length > 1) {

        // } else {
        //   // var name=null;
        //   // this.selectRows[0].members.forEach((item,index)=>{
        //   //    name? name+='、'+item.userName:name=item.userName;
        //   // // name? name+=','+item.userName.replace('*',"x") :name=item.userName.replace('*',"x");
        //   // })
        //   fileName = `社情民意专报登记表.docx`;
        // }
        fileName = `社情民意专报登记表.zip`;
        a.download = fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000);
      });
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.listQuery.pageNum = pageNum;
      this.listQuery.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.listQuery.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    //查询
    handleQuery() {
      this.drawer = false;
      this.listQuery.pageNum = 1;
      this.fetchData();
    },

    //重置查询参数
    handleClear() {
      if (this.$refs["drawerForm"] !== undefined) {
        this.$refs["drawerForm"].resetFields();
      }
    },

    //查询列表数据
    fetchData() {
      this.listLoading = true;
      this.getWeekByUserId();
    },

    getWeekByUserId() {
      console.log("------>", this.listQuery);
      let that = this;
      getWeekByUserId(this.listQuery).then((response) => {
        that.list = response.data.rows;
        that.total = response.data.total;
        that.pagination.total = response.data.total;
        that.listLoading = false;
        console.log("----list-->", that.list);
      });
    },
    handleTableChange(data, value, title) {
      try {
        this.listQuery.sort = title.column.dataIndex;
      } catch (error) {}
      if (title.order == "ascend") {
        this.listQuery.order = "desc";
      } else if (title.order == "descend") {
        this.listQuery.order = "asc";
      } else {
        this.listQuery.order = "";
        this.listQuery.sort = "";
      }
      this.fetchData();
    },
    checkPermission,
  },
};
</script>
<style lang="scss" scoped>
.tableLimit tr td .cell {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  /*可以显示的行数，超出部分用...表示 */
  -webkit-box-orient: vertical !important;
}

.tableLimit .el-button--small {
  padding: 0 !important;
}

.Data-value {
  display: flex;
  line-height: 34px;
  margin-top: 10px;

  .title {
    // font-size: 14px;
    @include add-size($font_size_16);
    font-family: PingFang-M;
  }

  .contentBox {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    height: 130px;

    .contents {
      height: 30px;
      line-height: 30px;
      margin-left: 10px;
      text-align: center;
      border-radius: 6px;
      border: 1px solid rgb(214, 208, 208);
    }
  }
}

.textarea {
  margin-left: 10px;
  width: 400px;
}

.bgColor {
  background-color: #cc3031;
  color: #fff;
}

.upload-list-inline >>> .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-right: 8px;
}

.upload-list-inline >>> .ant-upload-animate-enter {
  animation-name: uploadAnimateInlineIn;
}

.upload-list-inline >>> .ant-upload-animate-leave {
  animation-name: uploadAnimateInlineOut;
}

.ant-upload-picture-card-wrapper {
  display: flex;
}
</style>
