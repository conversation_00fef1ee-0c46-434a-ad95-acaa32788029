<template>
    <div style="padding: 20px">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
      <a-form-model-item
        label="代表"
        prop="inviteRangeDesc"
        >
          <a-button
            type="primary"
            icon="plus"
            @click="onAttendMembers"
            style="width: 66px; border-radius: 6px"
          ></a-button>
          <MultiLineText :values="form.attendMembers" />
        </a-form-model-item>
        <a-form-model-item label="时间" 
                               prop="serverTime">
              <a-date-picker style="width: 100%;"
                             disabled
                             value-format="YYYY-MM-DD" 
                             v-model="form.achieveDate"
                             placeholder="选择日期时间">
              </a-date-picker>
            </a-form-model-item>
        <a-form-model-item label="期数" prop="period" >
          <a-input
            disabled
            v-model="form.period"
          />
        </a-form-model-item>
        <a-form-model-item :wrapper-col="{ span: 18, offset: 3}">
          <a-button type="primary" @click="onSubmit"> 确 定 </a-button>
        </a-form-model-item>
      </a-form-model>

      <org-db-tree
        ref="identityRelAny"
        default-key="1"
        :levelRoleMainIdentify="levelRoleMainIdentify"
        @saveRel="onAttendMembersSubmit"
      ></org-db-tree>
    </div>
  </template>
  
  <script>
  import { getResumptionWeekUserRecordById, addItem } from "@/api/electronicArchives.js";
  import OrgDbTree from "@/components/OrgDbTree/index.vue";
  import MultiLineText from '@/components/MultiLineText'
  import { DBLZ_DBLZDJ } from '@/utils/enum/levelRoleMainIdentifyEnum'; // 导入配置文件
  export default {
    components: { 
      OrgDbTree,
      MultiLineText
    },
    data() {
      return {
        levelRoleMainIdentify : DBLZ_DBLZDJ,
        recordId: "",
        //行政区域列表
        administrativeAreas: [],
        streetTowns: [],
        loading: false,
        imageUrl: "",
        labelCol: { span: 3 },
        wrapperCol: { span: 14 },
        other: "",
        form: {
          achieveDate: null
        },
        inviteRangeDesc: "",
        defaultSelect: [], //同行代表
        showDisabled: false,
        rules: {
          achieveDate: [
            {
              required: true,
              message: "请选择日期",
              trigger: "blur",
            },
          ],
          period: [
            {
              required: true,
              message: "请输入期数",
              trigger: "blur",
            },
          ],
        },
      };
    },
    mounted() {
      let { id } = this.$route.query;
      this.recordId = id;
      this.getById()
    },
    methods: {
      getById() {
        getResumptionWeekUserRecordById({ id : this.recordId }).then(res => {
          this.form = res.data
        })
      },
      onSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            addItem(this.form).then(res => {
              if(res.code == '0000') {
                this.$message.success("修改成功");
                this.$router.push({
                    path: "/electronicArchives/viewResumptionWeekItem",
                    query: { id: this.recordId },
                });
              } else {
                this.$message.error("修改失败");
              }
              this.getById()
            })
          } else {
            this.$message.error("请完善资料");
            return false;
          }
        });
      },
      resetForm() {
        this.$refs.form.resetFields();
      },
      addMembers() {
        this.$refs["identityRelAny"] &&
        this.$refs["identityRelAny"].handleShow(
          "1",
          []
        );
      },
      onAttendMembers() {
        this.$refs["identityRelAny"] &&
          this.$refs["identityRelAny"].handleShow(
            "1",
            this.form.attendMembers
          );
      },
      onAttendMembersSubmit(type, checkedData) {
        //用户树选择赋值
        if (type == "1") {
          const list = checkedData
            .map((it) => ({
              userId: it.id,
              inviteId: it.uniqueId,
              dhDm: it.dhDm,
              jcDm: it.jcDm,
              orgId: it.orgId,
              deptId: it.deptId,
              postId: it.postId,
              orgName: it.orgName,
              deptName: it.deptName,
              postName: it.postName,
              name: it.name,
              userName: it.userName,
              sfDm: it.sfDm,
              committeeMemberTypeId: it.sfDm,
            }));
            this.$set(this.form, "attendMembers", list);
            // let form = {
            //   id: this.recordId,
            //   attendMembers: list,
            // };
            // addDutyActivityAwardItem(form).then((res) => {
            //   if (res.code == '0000') {
            //     this.$message.success("操作成功");
            //     this.fetchData();
            //   } else {
            //     this.$message.error(res.message);
            //   }
            // });
        } 
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .avatar-uploader > .ant-upload {
    width: 128px;
    height: 128px;
  }
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }
  
  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .searchStyle {
  display: flex !important;
  .ant-input {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  .ant-btn-primary {
    width: 66px;
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
  </style>
  