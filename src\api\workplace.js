import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/workplaceArrange/getWorkplaceArrangeList",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/workplaceArrange/addWorkplaceArrange",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/workplaceArrange/updateWorkplaceArrange",
    method: "post",
    data,
  });
}

export function updateAllocation(data) {
  return request({
    url: "/api/v1/workplaceArrange/updateAllocation",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/workplaceArrange/delWorkplaceArrange",
    method: "post",
    data,
  });
}

export function downloadExcel(data) {
  return request({
    url: "/api/v1/workplaceArrange/downloadExcel",
    method: "post",
    data,
    responseType: "blob",
  });
}

export function getWorkplaceArrangeUser(param) {
  return request({
    url: "/api/v1/workplaceArrange/getWorkplaceArrangeUser/" + param,
    method: "post",
  });
}

export function getWorkplaceArrange(param) {
  return request({
    url: "/api/v1/workplaceArrange/getWorkplaceArrange/" + param,
    method: "post",
  });
}

export function getWorkplaceList() {
  return request({
    url: "/api/v1/workplace/getWorkplaceList",
    method: "post",
  });
}
