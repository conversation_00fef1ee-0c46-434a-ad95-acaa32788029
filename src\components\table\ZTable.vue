<template>
  <div>
    <div v-if="empty" class="alert">
      <a-alert type="info" :show-icon="true">
        <div class="message" slot="message">
          已选择&nbsp;
          <a>{{ selectedRows.length }}</a>&nbsp;项
          <a class="clear" @click="onClear">清空</a>
        </div>
      </a-alert>
    </div>
    <a-table
      class="directorySet-table"
      ref="table"
      :columns="columns"
      :pagination="pagination"
      :row-selection="
        ischoiceBox
          ? {
            onSelect: selectDutys,
            onSelectAll: onSelectAll,
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }
          : null
      "
      :data-source="list"
      :rowKey="
        (record, index) => {
          return record.id || record.actionId || record.menuId || index;
        }
      "
      :scroll="{ x: 300, y: 0 }"
    ></a-table>
  </div>
</template>

<script>
export default {
  name: "ZTable",
  props: {
    list: Array,
    columns: Array,
    // 是否显示分页器 默认不显示
    pagination: {
      type: [Object, Boolean],
      default: () => {
        return {
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0,
          showTotal: (total) => `共 ${total} 条`,
        };
      },
    },
    // 是否显示清空栏 默认显示
    empty: {
      type: Boolean,
      default: true,
    },
    // 是否显示选择框 默认显示
    ischoiceBox: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      selectedRowKeys: [], // 选择项key
      selectedRows: [],
    };
  },
  watch: {
    list() {
      // 监视list变化时, 清空选择项
      this.onClear();
    },
  },
  computed: {
  },
  methods: {
    // 选择当个表格时
    selectDutys(item, selected, items) {
      this.selectedRows = items;
      // 批量删除
      this.$emit("deleteBatches", items);
    },
    // 选择全部表单时触发
    onSelectAll(selected, items) {
      console.log(selected);
      this.selectedRows = items;
      // 批量删除
      this.$emit("deleteBatches", items);
    },
    // 选择项变化时触发 获取已选择项Key
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    // 清空所选表单
    onClear() {
      this.selectedRows = [];
      this.selectedRowKeys = [];
    },
  },
};
</script>

<style scoped lang="scss">
.alert {
  margin-bottom: 16px;
  .message {
    a {
      font-weight: 600;
    }
  }
  .clear {
    float: right;
  }
}
</style>
