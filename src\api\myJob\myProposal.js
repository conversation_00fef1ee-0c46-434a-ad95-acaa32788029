import { instance_yajy } from "@/api/axiosRq";
// 获取建议
export function getFindPage(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/findPage`,
    method: "post",
    data: formData,
  });
}
// 联名的建议
export function getMyJointly(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/findPage`,
    method: "post",
    data: formData,
  });
}

//被退回的建议
export function getMyReturned(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/selectReturnInfo`,
    method: "post",
    data: formData,
  });
}

// 根据条件获取议案建议列表 //待处理
export function findReagovatPage(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/findPage`,
    method: "post",
    data: formData,
  });
}
// 根据条件获取议案建议列表 //已处理
export function findProcessedPage(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/findPage`,
    method: "post",
    data: formData,
  });
}
// 根据条件获取议案建议列表 //待提交
export function findMyInsteadPage(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/findPage`,
    method: "post",
    data: formData,
  });
}

// 保存建议
export function suggestionSave(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/save`,
    method: "post",
    data: formData,
  });
}

//承办单位列表
export function getProposalUndertakes(queryForm) {
  let formData = new FormData();
  for (const key in queryForm) {
    formData.append(key, queryForm[key]);
  }
  return instance_yajy({
    url: `/api/v1/system/org/yajy/getUndertakeOrgList`,
    method: "post",
    data: formData,
  });
}
//获取联名代表
export function getDeputyList(queryForm) {
  let formData = new FormData();
  for (const key in queryForm) {
    formData.append(key, queryForm[key]);
  }
  return instance_yajy({
    url: `/api/v1/proposal/jointly/getDeputyList`,
    method: "post",
    data: formData,
  });
}
export function getDeputyPage(queryForm) {
  let formData = new FormData();
  for (const key in queryForm) {
    formData.append(key, queryForm[key]);
  }
  return instance_yajy({
    url: `/api/v1/proposal/jointly/getDeputyPage`,
    method: "post",
    data: formData,
  });
}
// 根据id获取议案建议信息
export function proposalById(params) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/getById`,
    method: "post",
    params,
  });
}
// 根据id获取议案建议信息
export function suggestionUpData(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/update`,
    method: "post",
    data: formData,
  });
}
// 议案建议列表导出EXCEL
export function suggestionExportExcel(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportExcel`,
    method: "post",
    responseType: "blob",
    params: form.params,
    data: form.data,
  });
}
// 议案建议列表导出EXCEL暂时无法使用
export function proposalListExportExcel(params) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/proposalListExportExcel`,
    method: "get",
    responseType: "blob",
    params,
  });
}
// 获取届次13楼
export function findBySession() {
  return instance_yajy({
    url: `/api/v1/system/meetingMgr/findBySession`,
    method: "post",
    data: {},
  });
}

// 获取分办小组
export function getAssignGroup() {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/getAssignGroup`,
    method: "post",
    data: {},
  });
}

// 议案建议列表删除
export function deleteById(proposalId) {
  let formData = new FormData();
  formData.append("proposalId", proposalId);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/deleteById`,
    method: "post",
    data: formData,
  });
}

// 设置重点建议
export function setKeySuggestionsApi(proposalIds) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/setProposalStress`,
    method: "post",
    params: { proposalIds },
  });
}
// 交办议案建议
export function doAssign(form) {
  let undertakeList = form.undertakeList;
  let newForm = JSON.parse(JSON.stringify(form));
  delete newForm.undertakeList;
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doAssign`,
    method: "post",
    params: newForm,
    data: undertakeList,
  });
}

// 交办议案建议闭会
export function doAssignBh(form) {
  let undertakeList = form.undertakeList;
  let newForm = JSON.parse(JSON.stringify(form));
  delete newForm.undertakeList;
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doAssignBh`,
    method: "post",
    params: newForm,
    data: undertakeList,
  });
}

// 审核议案建议
export function doAudit(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doAudit`,
    method: "post",
    params: form,
  });
}
// 审核议案建议 （闭会）
export function doAuditBh(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doAuditBh`,
    method: "post",
    params: form,
  });
}

//初审议案建议
export function doFirstCheck(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doFirstCheck`,
    method: "post",
    params: form,
  });
}

//初审并分类建议（闭会期间）
export function doFirstCheckBh(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doFirstCheckBh`,
    method: "post",
    params: form,
  });
}

//查看公开意见
export function findCommentByType(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/findCommentByType`,
    method: "post",
    params: form,
  });
}

//闭会期间研究室提出公开意见
export function doYjsPublic(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doYjsPublic`,
    method: "post",
    params: form,
  });
}

//闭会期间复审环节修改公开意见
export function updatePublicComment(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/updatePublicComment`,
    method: "post",
    params: form,
  });
}


// 校核议案建议
export function doCheck(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doCheck`,
    method: "post",
    params: form,
  });
}

//不予立案审核通过-->代表工委确认
export function updateSug(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/updateSug`,
    method: "post",
    params: form,
  });
}
//
export function leaderCheckSug(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
      url: `/api/v1/proposal/suggestion/leaderCheck`,
    method: "post",
    params: form,
  });
}

// 代表工委审核退回建议驳回
export function auditReturnFalse(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/auditReturnF`,
    method: "post",
    params: form,
  });
}

// 专题分办小组组长确认
export function AssignConfirm(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doAssignConfirm`,
    method: "post",
    params: form,
  });
}
//建议分办小组组长确认分办
export function AssignDbcConfirm(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doAssignDbcConfirm`,
    method: "post",
    params: form,
  });
}

//市委编办工作人员签字
export function BbConfirm(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doBbConfirm`,
    method: "post",
    params: form,
  });
}

//建议交办小组组长签字
export function DbcConfirm(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doDbcConfirm`,
    method: "post",
    params: form,
  });
}

//议案建议负责人签字
export function XlwConfirm(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doXlwConfirm`,
    method: "post",
    params: form,
  });
}

// 不予立案代表工委确认-->转供参考
export function doUnRegister(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doUnRegister`,
    method: "post",
    params: form,
  });
}
// 不予立案代表工委确认-->转供参考(闭会)
export function doUnRegisterBh(form) {
  console.log("🤗🤗🤗, form =>", form);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doUnRegisterBh`,
    method: "post",
    params: form,
  });
}
// 详情页面 获取提出代表
export function getJointlyList(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/jointly/getJointlyList`,
    method: "post",
    params: { proposalId },
  });
}
// 详情页面 添加提出代表
export function addJointly(form) {
  return instance_yajy({
    url: `/api/v1/proposal/jointly/addJointly`,
    method: "post",
    params: form,
  });
}
// 详情页面 删除提出代表
export function delJointly(jointlyIds) {
  console.log("🤗🤗🤗, jointlyIds =>", jointlyIds);
  return instance_yajy({
    url: `/api/v1/proposal/jointly/delJointly`,
    method: "post",
    data: jointlyIds,
  });
}
// 申诉撤回
export function AppealOver(params) {
  return instance_yajy({
    url: `/api/v1/appraise/form/reject`,
    method: "post",
    params,
  });
}

// 设置公开建议
export function openSuggest(data) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/setPublicProposal`,
    method: "post",
    data,
  });
}

// 设置书面答复
export function WrittenAnswer(data) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/setWrittenProposal`,
    method: "post",
    data,
  });
}

// 设置交办日期
export function assignTime(params) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/setAssignDate`,
    method: "post",
    params,
  });
}
// 移交议案建议内容
export function doShift(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doShift`,
    method: "post",
    params: form,
  });
}
// 修改内容分类
export function doClassify(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doClassify`,
    method: "post",
    params: form,
  });
}
// 重新生成正文
export function againProduce(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/againProduce`,
    method: "post",
    data: { proposalId },
  });
}
// 签收建议
export function doSign(proposalIds) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/doSign`,
    method: "post",
    params: { proposalIds },
  });
}
// 获取办理跟踪列表
export function getLocalHistory(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/manualHistory/getLocalHistory`,
    method: "post",
    params: { proposalId },
  });
}

// 获取答复函数据
export function getDfhData(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/proposalUndertakeDfh/getInfo`,
    method: "post",
    params: { proposalId },
  });
}

export function getDfhList(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/proposalUndertakeDfh/getDfhList`,
    method: "post",
    data: formData,
  });
}


// 转参考的建议
export function referenceSug(data,referenceMarks) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/setProposalReference`,
    method: "post",
    params: { referenceMarks },
    data,
  });
}
// 获取议案建议的承办单位列表
export function getProposalUnder(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getProposalUndertakes`,
    method: "post",
    params: { proposalId },
  });
}
// 答复议案建议
export function doReply(form, proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/doReply`,
    method: "post",
    params: { proposalId },
    data: form,
  });
}
// 提交不予立案
export function saveUnRegister(form) {
  let undertakeList = form.undertakeList;
  let newForm = JSON.parse(JSON.stringify(form));
  delete newForm.undertakeList;
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/unRegister`,
    method: "post",
    params: newForm,
    data: undertakeList,
  });
}

// 提交不予立案闭会
export function saveUnRegisterBh(form) {
  let undertakeList = form.undertakeList;
  let newForm = JSON.parse(JSON.stringify(form));
  delete newForm.undertakeList;
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/unRegisterBh`,
    method: "post",
    params: newForm,
    data: undertakeList,
  });
}
// 申请调整
export function applyRevise(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/applyRevise`,
    method: "post",
    params: form,
  });
}
// 申请延期
export function applyDelay(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/applyDelay`,
    method: "post",
    params: form,
  });
}
// 结办议案建议
export function markComplete(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/markComplete`,
    method: "post",
    params: { proposalId },
  });
}
// 获取催办列表
export function queryUrgeList(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/queryUrgeList`,
    method: "post",
    data: form,
  });
}
// 获取催办列表
export function sendUrgeMsgBatch(params) {
  return instance_yajy({
    url: `/api/v1/system/smsZdy/sendUrgeMsgBatch`,
    method: "post",
    params,
  });
}
// 获取接收人列表
export function getSmsOperList(undertakeId, urgeType) {
  let formData = new FormData();
  formData.append("urgeType", urgeType);
  formData.append("undertakeId", undertakeId);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/getSmsOperList`,
    method: "post",
    data: formData,
  });
}
// 发送短信
export function sendUrgeMsg(form) {
  return instance_yajy({
    url: `/api/v1/system/smsZdy/sendUrgeMsg`,
    method: "post",
    data: form,
  });
}

// 获取申请延期承办子流程信息
export function getDelayUndertakeListById(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getDelayUndertakeListById`,
    method: "post",
    params: { proposalId },
  });
}

// 获取申请调整承办子流程信息
export function getReviseUndertakeListById(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getReviseUndertakeListById`,
    method: "post",
    params: { proposalId },
  });
}

// 获取调整会办单位流程信息（主办单位申请需要审核）
export function getAdjustMinorUndertakeListById(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getAdjustMinorUndertakeListById`,
    method: "post",
    params: { proposalId },
  });
}

// 获取复核调整会办单位流程信息（主办单位申请需要审核）
export function getReviseReviseUndertakeListById(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getReviseReviseUndertakeListById`,
    method: "post",
    params: { proposalId },
  });
}

// 处理延期
export function doDelay(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/doDelay`,
    method: "post",
    params: form,
  });
}

// 会办单位调整审核通过
export function doMinorSave(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/doMinorSave`,
    method: "post",
    params: form,
  });
}
// 会办单位单位调整审核复核通过
export function doReviseReviseSave(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/doReviseReviseSave`,
    method: "post",
    params: form,
  });
}

// 会办单位主动调整复核退回
export function doMinorReviseReturn(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/doMinorReviseReturn`,
    method: "post",
    params: form,
  });
}

// 会办单位调整审核退回
export function doMinorReturn(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/doMinorReturn`,
    method: "post",
    params: form,
  });
}



// 维持原交办
export function keepRevise(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/keepRevise`,
    method: "post",
    params: form,
  });
}

// 维持原交办
export function keepDelay(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/keepDelay`,
    method: "post",
    params: form,
  });
}

// 查看正文
export function seeText(id) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/seeText`,
    method: "post",
    params: { id },
  });
}
// 处理调整-获取承办列表INFO
export function getUndertakeInfo(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getUndertakeInfo`,
    method: "post",
    params: { proposalId },
  });
}

// 处理调整-获取复核环节承办列表INFO
export function getReviseUndertakeInfo(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getReviseUndertakeInfo`,
    method: "post",
    params: { proposalId },
  });
}
// 重新交办
export function doRevise(undertakeList) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/doRevise`,
    method: "post",
    data: undertakeList,
  });
}
//主办单位调整会办单位
export function saveMinor(form) {
  console.log("form", form);
  return instance_yajy({
    url: `/api/v1/proposal/undertake/saveMinor`,
    method: "post",
    data: form,
  });
}
// 答复时获取过往经办人信息
export function getJbrList(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getJbrList`,
    method: "post",
    params: form,
  });
}
// 答复时更新经办信息
export function updateJbr(data) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/updateJbr`,
    method: "post",
    data: data,
  });
}
// 详情-获取反馈答复列表
export function getUndertakeDfAndFeedbackList(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getUndertakeDfAndFeedbackList`,
    method: "post",
    params: { proposalId },
  });
}

// 议案建议流程-反馈建议
export function doFeedback(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/doFeedback`,
    method: "post",
    data: form,
  });
}
// 议案建议流程-反馈建议
export function getFeedUndertakeListById(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getFeedUndertakeListById`,
    method: "post",
    params: { proposalId },
  });
}
// 重新上传正文附件
export function againUploadTextFile(form) {
  let file = form.file;
  let formData = new FormData();
  formData.append("file", file);
  delete form.file;
  return instance_yajy({
    url: `api/v1/proposal/suggestion/againUploadTextFile`,
    method: "post",
    params: form,
    data: formData,
  });
}
// 下载建议纸
export function downloadProposal(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/downloadProposal`,
    method: "post",
    responseType: "blob",
    params: { type: form.type },
    data: form.id,
  });
}

// 下载建议审核分办表
export function downloadAudit(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/downloadProposalAudit`,
    method: "post",
    responseType: "blob",
    params: { type: form.type },
    data: form.id,
  });
}

// 附件列表
export function queryAttachmentListPage(form) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/queryAttachmentListPage`,
    method: "post",
    data: form,
  });
}
// 组合查询 批量下载附件
export function downloadDoc(form) {
  return instance_yajy({
    url: `api/v1/proposal/suggestion/downloadDoc`,
    method: "post",
    responseType: "blob",
    params: form,
  });
}
// 交办-保存
export function saveCBDW(form) {
  let newForm = JSON.parse(JSON.stringify(form.data));
  return instance_yajy({
    url: `/api/v1/proposal/undertake/saveCBDW`,
    method: "post",
    params: form.params,
    data: newForm,
  });
}
// 处理联名
export function doSignedSave(form) {
  return instance_yajy({
    url: `/api/v1/proposal/jointly/doSignedSave`,
    method: "post",
    params: form,
  });
}
// 新建议案建议-获取会议时间
export function isMeetingTime(form) {
  return instance_yajy({
    url: `/api/v1/system/meetingMgr/isMeetingTime`,
    method: "post",
    params: form,
  });
}
// 往届建议查询接口
export function suggestionHisFindPage(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestionHis/findPage`,
    method: "post",
    data: form,
  });
}
// 往届建议导出接口
export function suggestionHisExport(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestionHis/exportExcel`,
    method: "post",
    responseType: "blob",
    params: form,
  });
}
// 获取子流程状态值
export function getUndertakeStatus(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/getUndertakeStatusById`,
    method: "post",
    params: form,
  });
}
// 代表议案办理模块 议案列表
export function agendaListFindPage(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/findPage`,
    method: "post",
    data: form,
  });
}

// 代表议案办理模块 议案列表 新增
export function listSaveProposal(form) {
  // let formData = new FormData();
  // for (const key in form) {
  //   formData.append(key, form[key]);
  // }
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/saveProposal`,
    method: "post",
    data: form,
  });
}
// 代表议案办理模块 议案列表 新增
export function getProposalNumber(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/getProposalNumber`,
    method: "post",
    params: form,
  });
}
// 代表议案办理模块 议案列表 删除
export function suggestionDel(proposalId) {
  let formData = new FormData();
  formData.append("proposalId", proposalId);
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/deleteById`,
    method: "post",
    data: formData,
  });
}
// 代表议案办理模块 议案列表 办结
export function suggestionMark(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/markComplete`,
    method: "post",
    params: { proposalId },
  });
}
// 申请延期 批量处理申请延期
export function batchApplyDelay(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/doDelayList`,
    method: "post",
    params: form,
  });
}
// 单位机构联系人
export function queryOrgLxrs(form) {
  return instance_yajy({
    url: `/api/v1/system/org/yajy/queryOrgLxrs`,
    method: "post",
    data: form,
  });
}
// 单位机构联系人
export function getUndertakeProposalList(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/getUndertakeProposalList`,
    method: "post",
    data: form,
  });
}
// 申请延期
export function getDelayPage(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/selectDelayPage`,
    method: "post",
    data: form,
  });
}
// 导出本单位建议纸
export function downloadUnitProposal(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/downloadProposalOrg`,
    responseType: "blob",
    method: "post",
  });
}
// 获取首页数
export function countTodo(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/countTodo`,
    method: "post",
    data: form,
  });
}
// 获取代表团
export function getDbtList() {
  return instance_yajy({
    url: `/api/v1/system/org/yajy/getDbtList`,
    method: "post",
  });
}
// 答复时获取经办信息
export function getUndertakeDfInfo(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/getUndertakeDfInfo`,
    method: "post",
    params: form,
  });
}
// 批量交办
export function doBatchAssign(proposalIds) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doBatchAssign`,
    method: "post",
    params: { proposalIds },
  });
}
// 往届建议 获取届次
export function findHisMeeting() {
  return instance_yajy({
    url: `/api/v1/system/meetingMgr/findHisMeeting`,
    method: "post",
  });
}
// 往届建议 获取届次
export function periodMgrFindPage(form) {
  return instance_yajy({
    url: `/api/v1/system/periodMgr/findPage`,
    method: "post",
    data: form,
  });
}
// 修改标题
export function updateProposalTitle(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/updateProposalTitle`,
    method: "post",
    params: form,
  });
}
// 议案建议届次管理 新增
export function periodMgrSave(form) {
  return instance_yajy({
    url: `/api/v1/system/periodMgr/save`,
    method: "post",
    data: form,
  });
}
// 议案建议届次管理 删除
export function periodMgrRemove(form) {
  return instance_yajy({
    url: `/api/v1/system/periodMgr/remove`,
    method: "post",
    params: form,
  });
}
// 议案建议届次管理 修改
export function periodMgrUpdate(form) {
  return instance_yajy({
    url: `/api/v1/system/periodMgr/update`,
    method: "post",
    data: form,
  });
}
// 议案 保存备注
export function saveRemark(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/saveRemark`,
    method: "post",
    params: form,
  });
}
export function deleteByAttId(Id) {
    return instance_yajy({
      url: `/api/v1/system/attachment/deleteByAttId`,
      method: "post",
      params: {Id},
    });
  }
// 请求延期申请的数量
export function selectDelayCount(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/selectDelayCount`,
    method: "post",
    data: form,
  });
}
// 延期申请列表
export function selectDelayPage(form) {
  return instance_yajy({
    url: `/api/v1/proposal/undertake/selectDelayPage`,
    method: "post",
    data: form,
  });
}
// 询问管理列表 列表
export function askQueryList(form) {
  return instance_yajy({
    url: `/api/v1/system/inquiryMgr/queryList`,
    method: "post",
    data: form,
  });
}
// 询问列表管理 保存
export function askSaveOrUpdate(form) {
  return instance_yajy({
    url: `/api/v1/system/inquiryMgr/saveOrUpdate`,
    method: "post",
    data: form,
  });
}
// 询问列表管理 获取数据
export function askGetById(form) {
  return instance_yajy({
    url: `/api/v1/system/inquiryMgr/getById`,
    method: "post",
    params: form,
  });
}
// 询问列表管理 删除
export function askDelete(form) {
  return instance_yajy({
    url: `/api/v1/system/inquiryMgr/delete`,
    method: "post",
    params: form,
  });
}
// 询问管理列表 列表
export function inquireQueryList(form) {
  return instance_yajy({
    url: `/api/v1/system/inquiryZhiMgr/queryList`,
    method: "post",
    data: form,
  });
}
// 询问列表管理 保存
export function inquireSaveOrUpdate(form) {
  return instance_yajy({
    url: `/api/v1/system/inquiryZhiMgr/saveOrUpdate`,
    method: "post",
    data: form,
  });
}
// 询问列表管理 获取数据
export function inquireGetById(form) {
  return instance_yajy({
    url: `/api/v1/system/inquiryZhiMgr/getById`,
    method: "post",
    params: form,
  });
}
// 询问列表管理 删除
export function inquireDelete(form) {
  return instance_yajy({
    url: `/api/v1/system/inquiryZhiMgr/delete`,
    method: "post",
    params: form,
  });
}
// 往届议案建议 获取数据
export function proposalHisById(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestionHis/getInfoById`,
    method: "post",
    params: form,
  });
}
// 往届议案建议 获取文件详情
export function showContent(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestionHis/showContent`,
    method: "post",
    params: form,
  });
}
// 获取当前登陆人是否有上传文件的权限
export function isYiAnCanUpload(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/isYiAnCanUpload`,
    method: "post",
    params: form,
  });
}
// 获取多选的意见类型
export function findOpinionOption() {
  return instance_yajy({
    url: `/api/v1/system/properties/findOpinionOption`,
    method: "get",
  });
}
// 各单位推荐重点督办建议 //推荐重点督办
export function findNeedPushSConditions(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/findNeedPushSConditions`,
    method: "post",
    data: formData,
  });
}
// 推荐重点督办
export function updateRecommend(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/updateRecommend`,
    method: "post",
    // params: form.params,
    params: form,
  });
}
