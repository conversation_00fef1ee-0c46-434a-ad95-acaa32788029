<template>
  <div class="SideBar">
    <a-menu
      mode="inline"
      style="width: 100%"
      :open-keys="openKeys"
      :default-selected-keys="selectedMenuKeys"
      @openChange="onOpenChange"
    >
      <a-menu-item
        v-if="navigation.text == '平台门户'"
        key="home"
        @click="homeUrl"
      >
        <a-icon type="home"></a-icon>门户首页
      </a-menu-item>
      <!-- 第一级 -->
      <a-sub-menu
        v-for="(item, index) in Datatitle"
        v-show="item.title == navigation.text"
        :key="index"
      >
        <span slot="title" @click="skiUrl(item)"
          ><span>
            <a-icon
              :type="item.name == 'SystemManage' ? 'tool' : 'align-center'"
              @click="skiUrl(item)"
            /> </span
          >{{ item.meta.title }}</span
        >

        <template v-for="item1 in item.children">
          <!-- 第三级别 -->
          <template v-if="item1.children">
            <a-sub-menu :key="item1.meta.title">
              <span slot="title">
                <i class="iconfont"></i>
                <span> {{ item1.meta.title }}</span>
              </span>
              <a-menu-item
                v-for="item2 in item1.children"
                :key="item.path + '/' + item1.path + '/' + item2.path"
                @click="Titlechildren2(item, item1, item2)"
              >
                <!-- <i class="iconfont" :class="subitem.icon"></i> -->
                <span>{{ item2.meta.title }}</span>
              </a-menu-item>
            </a-sub-menu>
          </template>

          <!-- 第二级别 -->
          <template v-else>
            <a-menu-item
              v-show="!item1.hidden"
              :key="item.path + '/' + item1.path"
              style="margin-button: 20px"
              @click="Titlechildren(item.path, item1.path)"
            >
              {{ item1.meta.title }}
            </a-menu-item>
          </template>
        </template>
      </a-sub-menu>
    </a-menu>
  </div>
</template>
<script>
import path from "path";
// import Logo from "@/layouts/components/Logo";
// import SideBarItem from "./SideBarItem";
import variables from "@/styles/variables.scss";
import events from "@/components/events.js";
import { mapGetters } from "vuex";
import { log } from "@antv/g2plot/lib/utils";
import { allRoutes } from "@/router";
export default {
  name: "SideBar",
  // components: { SideBarItem, Logo },
  data() {
    return {
      msg: "",
      collapsed: false,
      selectedMenuKeys: [],
      deKeys: [],
      rootSubmenuKeys: ["Opinion", "huoDong", "resumption"],
      openKeys: ["resumption"],
      Datatitle: [],
    };
  },
  created() {
    if (JSON.parse(window.sessionStorage.getItem("openKeys"))) {
      this.openKeys = JSON.parse(window.sessionStorage.getItem("openKeys"));
    }
    this.selectedMenuKeys = [];
    if (window.sessionStorage.getItem("selectKey")) {
      this.selectedMenuKeys.push(window.sessionStorage.getItem("selectKey"));
    }
    console.log(this.selectedMenuKeys);
    events.$on("sendEven", (even) => {
      if (!even) return;
      // 设置打开
      this.openKeys.push(even.openKey);
      // 增加展开菜单项保存到缓存
      let newOpenKeys = JSON.parse(window.sessionStorage.getItem("openKeys"));
      newOpenKeys.push(even.openKey);
      window.sessionStorage.setItem("openKeys", JSON.stringify(newOpenKeys));
      // 设置选中
      console.log(even.path);
      this.selectedMenuKeys = [];
      // this.selectedMenuKeys=even.path;
      this.selectedMenuKeys.push(even.path);
      this.$forceUpdate();
    });

    let text1 = {
      name: "民呼我应",
      path: "/site/index",
      meta: { title: "民呼我应" },
    };
    let text2 = { name: "首页", path: "/", meta: { title: "首页" } };
    this.Datatitle.unshift(text1);
    this.Datatitle.unshift(text2);
    this.routes.filter((item) => {
      if (item.meta && item.meta.title != "代表连线") {
        this.Datatitle.push(item);
      }
    });

    console.log(this.$store.getters.routes[9]);

    for (var i = 0; i < 100; i++) {
      //
      // //取需要节点数据
      // if(this.$store.getters.routes[i] != undefined){
      //   //去掉多余垃圾数据
      //   if(this.$store.getters.routes[i].name != undefined && this.$store.getters.routes[i].title != undefined){
      //
      //     //判断是否有下一级目录
      //     if(this.$store.getters.routes[i].children != undefined){
      //       //一级菜单
      //       console.log('一级菜单: '+this.$store.getters.routes[i].name + '----'+this.$store.getters.routes[i].title+'----'+this.$store.getters.routes[i].path);
      //       var childrenId = this.$store.getters.routes[i];
      //       for(var j = 0 ; j < 100; j++){
      //
      //         if(childrenId.children[j] != undefined){
      //           console.log('二级菜单: '+'----'+childrenId.children[j].meta.title+'----'+childrenId.children[j].path);
      //         }
      //
      //
      //       }
      //     }
      //
      //
      //   }

      //取需要节点数据
      if (this.$store.getters.routes[i] != undefined) {
        //去掉多余垃圾数据
        if (
          this.$store.getters.routes[i].name != undefined &&
          this.$store.getters.routes[i].title != undefined
        ) {
          //判断是否有下一级目录
          if (this.$store.getters.routes[i].children != undefined) {
            //一级菜单
            console.log(
              "一级菜单: " +
                this.$store.getters.routes[i].name +
                "----" +
                this.$store.getters.routes[i].title +
                "----" +
                this.$store.getters.routes[i].path
            );
            var childrenId = this.$store.getters.routes[i];
            for (var j = 0; j < 100; j++) {
              if (childrenId.children[j] != undefined) {
                console.log(
                  "二级菜单: " +
                    this.$store.getters.routes[i].title +
                    "------------" +
                    this.$store.getters.routes[i].meta.title +
                    "----" +
                    childrenId.children[j].meta.title +
                    "----" +
                    childrenId.children[j].path +
                    "----" +
                    childrenId.children[j].hidden +
                    "----" +
                    childrenId.children[j].fUrl
                );
              }
            }
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters(["collapse", "routes", "layout"]),
    navigation() {
      return this.$store.state.navigation.titleText;
    },
    defaultOpen() {
      if (this.collapse) {
      }
      let arr = this.routes.map((item) => {
        return path.resolve(item.path);
      });
      /*只默认展开除了首页,登录,404,重定向以外的第一级*/
      arr = this.$baseLodash.pull(
        arr,
        "/",
        "/!*",
        "/login",
        "/404",
        "/401",
        "/redirect"
      );
      return arr;
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    variables() {
      return variables;
    },
  },
  methods: {
    homeUrl() {
      this.$router.push({ path: "/portalIndex/index" }).catch((err) => {
        console.log(err);
      });
    },
    Titlechildren2(item, item1, item2) {
      this.$router.push({
        path: item.path + "/" + item1.path + "/" + item2.path,
      });
      window.sessionStorage.setItem(
        "selectKey",
        item.path + "/" + item1.path + "/" + item2.path
      );
    },
    Titlechildren(path1, path2) {
      if (path2 == "TrainingStudy") {
        this.navigation.text = "在线学习培训";
        events.$emit("sendIndex", "8");
        this.openKeys = ["resumption", 3, 57];
        this.deKeys = ["organizationTraining"];
        this.$router.push({
          path: "/organizationIndex/organizationTraining",
        });
      } else if (path2 == "publilist") {
        this.$router.push({
          path: "/publicopinion/publilist",
        });
        this.$store.dispatch("navigation/breadcrumb1", "社情民意收集");
        this.$store.dispatch("navigation/breadcrumb2", "社情民意列表");
      } else {
        this.$router
          .push({
            path: path1 + "/" + path2,
          })
          .catch((err) => {
            console.log(err);
          });
        window.sessionStorage.setItem("selectKey", path1 + "/" + path2);
      }
    },

    skiUrl(item) {
      if (item.path == "/site/index" || item.path == "/") {
        this.$router
          .push({
            path: item.path,
          })
          .catch((err) => {
            console.log(err);
          });
      } else if (item.path == "/portalIndex") {
        this.$router
          .push({
            path: item.path + "/" + item.children[0].path,
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },

    onOpenChange(openKeys) {
      // 当菜单被展开时触发此处
      console.log(openKeys, "openKeys");
      this.openKeys = openKeys;
      window.sessionStorage.setItem("openKeys", JSON.stringify(this.openKeys));
    },
  },
};
</script>
<style lang="scss" scoped>
.side-bar-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: $base-z-index;
  width: $base-left-menu-width;
  height: 100vh;
  overflow: hidden;
  background: $base-menu-background;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  transition: all 0.2s ease-in-out;

  ::v-deep {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .el-menu {
      border: 0;
      .el-menu-item {
        height: 56px !important;
        overflow: hidden;
        line-height: 56px !important;
        // border-bottom: 1px solid #ddd;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:hover {
          color: $base-color-white !important;
          background-color: $base-menu-background-active;
        }

        &.is-active {
          color: $base-color-white !important;
          background-color: $base-menu-background-active;
        }
      }
    }

    .svg-inline {
      &--fa {
        width: 1rem;
      }
    }

    .nest-menu {
      [class*="menu"] {
        // background-color: $base-menu-background;

        // &.is-active {
        //   color: $base-color-white !important;
        //   background-color: $base-menu-background-active !important;
        // }
      }
    }
  }

  &.is-collapse {
    width: $base-left-menu-width-min;
    border-right: 0 !important;

    ::v-deep {
      .title {
        display: none;
      }

      .el-menu--collapse {
        border-right: 0 !important;

        .el-submenu__icon-arrow {
          right: 10px;
          margin-top: -3px;
        }

        .el-submenu__title {
          span {
            display: none;
          }
        }
      }
    }
  }
}
.qxj-side {
  box-shadow: none;
  -webkit-transition: width 0.28s;
  transition: width 0.28s;
  width: 200px !important;
  background-color: #fff;
  height: 83%;
  position: fixed;
  font-size: 0;
  top: 90px;
  bottom: 0;
  left: 0;
  z-index: 1000;
  overflow: hidden;
}
.SideBar {
  width: 800px;
  overflow-x: hidden;
  overflow-y: scroll;
  text-align: left;
  background-color: #ffffff;
}

::v-deep .ant-menu-submenu-selected {
  color: #db3046;
}
.SideBar::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 0px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.SideBar::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background: #d9dadc;
}
.SideBar::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px #d9dadc;
  border-radius: 10px;
  background: #ededed;
}

::v-deep .ant-menu-item:last-of-type {
  margin-bottom: -15px;
  //
}
::v-deep .ant-menu-submenu {
  // margin-bottom: 20px;
  //
}
.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  color: #c51512;
  background-color: #fbeeef;
}
.ant-menu-submenu-selected span {
  // 父级、
  // color: #c51512 !important;
}
.ant-menu-item:hover {
  color: #c51512;
}
.ant-menu-inline .ant-menu-item::after {
  border: 0px;
}
</style>
<style>
.ant-menu-inline,
.ant-menu-vertical,
.ant-menu-vertical-left {
  border: 0px !important;
}
.ant-menu-submenu-selected {
  color: #7c7c7c !important;
}
.ant-menu-submenu-active {
  color: #7c7c7c !important;
}
.ant-menu-submenu-title:hover {
  color: #7c7c7c !important;
}
.ant-menu-submenu-selected:hover {
  color: #7c7c7c !important;
}
.ant-menu-submenu:hover {
  color: #7c7c7c !important;
}
.ant-menu-submenu-inline
  > .ant-menu-submenu-title
  .ant-menu-submenu-arrow::before {
  background-image: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.65),
    rgba(0, 0, 0, 0.65)
  ) !important;
}
</style>
