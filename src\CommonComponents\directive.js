export const imgGeerror = {
    inserted(dom, options) {
    // img属性等于它的src值 或者等于imgGeerror=的值
    dom.src = dom.src || options.value
    dom.onerror = () => {
    // 图片加载失败, 出现一个碎片时
    dom.src = options.value
    }
    },
    // componentUpdated 是dom对象更新变化时触发的函数,比如翻页后会触发
    componentUpdated(dom, options) {
      dom.src = dom.src || options.value
    }
};
   

// 图片出现碎片 使用方法
{/* <img src="/img/default.png" alt="封面" class="grid-photo"  

v-imgGeerror="require('@/assets/shibai.jpg')"


/> */}


export function imgLazyLoad (){ // 图片懒加载
    var timer,
        len = $('img.lazyload').length;
    function getPos(node) {
        var scrollx = document.documentElement.scrollLeft || document.body.scrollLeft,
            scrollt = document.documentElement.scrollTop || document.body.scrollTop;
        var pos = node.getBoundingClientRect();
        return {top:pos.top + scrollt, right:pos.right + scrollx, bottom:pos.bottom + scrollt, left:pos.left + scrollx }
    }
    function loading(){
        timer && clearTimeout(timer);
        timer = setTimeout(function(){
            var scrollTop = document.documentElement.scrollTop || document.body.scrollTop,
                imgs=$('img.lazyload');
            screenHeight = document.documentElement.clientHeight;
            for(var i = 0;i < imgs.length;i++){
                var pos = getPos(imgs[i]),
                    posT = pos.top,
                    posB = pos.bottom,
                    screenTop = screenHeight+scrollTop;
                if((posT > scrollTop && posT <  screenTop) || (posB > scrollTop && posB < screenTop)){
                    imgs[i].src = imgs[i].getAttribute('data-img');
                    $(imgs[i]).removeClass('lazyload');
                }else{
                    // new Image().src = imgs[i].getAttribute('data-img');
                }
            }
        },100);
    }
    if(!len) return;
    loading();
    $(window).on('scroll resize',function(){
        if(!$('img.lazyload').length){
            return;
        }else{
            loading();
        }
    })
}
