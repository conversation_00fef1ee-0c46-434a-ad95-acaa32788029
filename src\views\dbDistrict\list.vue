<template>
  <div style="padding: 20px">
    <div>
      <SearchForm @onReset="clearQueryForm" @onSearch="handleQuery">
        <template v-slot:topSearch>
          <AdminStreetSelect
            :showInfo="['1', '2']"
            :adminValue.sync ="queryForm.administrativeAreaId"
            :streetValue.sync ="queryForm.streetTownId"
            :liaisonValue.sync ="queryForm.liaisonStationId" />
          <SingleSearch @onEnter="handleEnter" :title="'代表姓名'" :value.sync="queryForm.userName" />
        </template>
        <template v-slot:moreSearch>
          <SingleSearch @onEnter="handleEnter" :title="'电话号码'" :value.sync="queryForm.mobile" />
          <SingleSelect :title="'代表类型'" :selectList="representList" :value.sync="queryForm.sfDm" />
        </template>
        </SearchForm> 

      <a-row style="margin: 5px 0px 10px 8px">
        <a-col :span="6">
          <a-form ref="form" :form="queryForm" layout="inline">
            <a-form-model-item>
              <a-button type="primary" @click="addData">新增</a-button>
            </a-form-model-item>
          </a-form>
        </a-col>
      </a-row>

      <!-- <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <a-row>
          <a-form
            ref="form"
            :model="queryForm"
            layout="horizontal"
            labelAlign="left"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 18, offset: 0 }"
          >
            <div>
              <a-row>
                <a-col :span="6">
                  <a-form-item
                    label="行政区域"
                    :label-col="{ span: 5 }"
                    :wrapper-col="{ span: 17, offset: 1 }"
                  >
                    <a-select
                      v-model="queryForm.administrativeAreaId"
                      placeholder="请选择行政区域"
                      allow-clear
                      show-search
                      @change="handleAdministrativeAreaChange"
                    >
                      <a-select-option
                        v-for="item in administrativeAreas"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                        >{{ item.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item
                    label="镇街"
                    :label-col="{ span: 4 }"
                    :wrapper-col="{ span: 18, offset: 1 }"
                  >
                    <a-select
                      v-model="queryForm.streetTownId"
                      placeholder="请选择镇街"
                      allow-clear
                      show-search
                      :open="streetTownIdState"
                      @select="streetTownIdState = false"
                      @focus="streetTownIdFocus"
                      @change="handlestreetTownChange"
                    >
                      <a-select-option
                        v-for="item in streetTowns"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                        >{{ item.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item
                    label="代表姓名"
                    :label-col="{ span: 5 }"
                    :wrapper-col="{ span: 17, offset: 0 }"
                  >
                    <a-input
                      v-model="queryForm.userName"
                      allow-clear
                      autocomplete="off"
                      placeholder="请输入代表姓名"
                      @keyup.enter="handleQuery"
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <span style="float: right; margin-top: 3px">
                    <a style="margin-right: 8px" @click="toggleAdvanced">
                      {{ advanced ? "收起" : "高级搜索" }}
                      <a-icon :type="advanced ? 'up' : 'down'" />
                    </a>
                    <a-button type="primary" @click="handleQuery"
                      >搜索</a-button
                    >
                    <a-button
                      style="margin-left: 12px"
                      class="pinkBoutton"
                      @click="clearQueryForm"
                      >重置</a-button
                    >
                  </span>
                </a-col>
              </a-row>
              <a-row v-if="advanced">
                <a-col :span="6">
                  <a-form-item
                    label="电话号码"
                    :label-col="{ span: 4 }"
                    :wrapper-col="{ span: 18, offset: 1 }"
                  >
                    <a-input
                      v-model="queryForm.mobile"
                      allow-clear
                      autocomplete="off"
                      placeholder="请输入电话号码"
                      @keyup.enter="handleQuery"
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item
                    label="代表类型"
                    :label-col="{ span: 4 }"
                    :wrapper-col="{ span: 18, offset: 1 }"
                  >
                  <a-select v-model="queryForm.sfDm" style="width: 120px" @change="handleTypeChange">
                    <a-select-option value="">
                        全部
                      </a-select-option>
                     <a-select-option value="4">
                        区代表
                      </a-select-option>
                      <a-select-option value="5">
                        镇代表
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </a-row>
      </a-col>
    </a-row> -->
    </div>

    <a-table
      :columns="columns"
      :data-source="dbList"
      row-key="id"
      :pagination="pagination"
      :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onSelect: onSelectData,
        onChange: onSelectChange,
        onSelectAll: onSelectAll,
      }"
    ></a-table>
  </div>
</template>

<script>
import { myPagination } from "@/mixins/pagination.js";
import { getList, deleteDbById } from "@/api/dbdistrict/db.js";
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
import SingleSelect from "@/components/SingleSelect/index";
import SearchForm from "@/components/SearchForm/index";
import SingleSearch from "@/components/SingleSearch/index";
import TimeRangePicker from "@/components/TimeRangePicker/index";
import AdminStreetSelect from "@/components/AdminStreetSelect/index";

export default {
  components: {
    SingleSelect,
    SearchForm,
    SingleSearch,
    TimeRangePicker,
    AdminStreetSelect,
  },
  mixins: [myPagination],
  props: {},
  data() {
    return {
      dbList: [],
      selectedRowKeys: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      activitySections: [],
      //行政区域列表
      administrativeAreas: [],
      streetTowns: [],
      streetTownIdState: false, //街道数组是否为空
      advanced: false,
      columns: [
        {
          title: "行政区划",
          ellipsis: true,
          width: 80,
          customRender: (text, record, index) => {
            return record.ssxzqName || "/";
          },
        },
        {
          title: "镇街",
          ellipsis: true,
          width: 200,
          customRender: (text, record, index) => {
            return record.xzjdName || "/";
          },
        },
        {
          title: "用户名称",
          ellipsis: true,
          width: 80,
          customRender: (text, record, index) => {
            return record.userName || "/";
          },
        },
        {
          title: "性别",
          ellipsis: true,
          width: 50,
          customRender: (text, record, index) => {
            return record.sex || "/";
          },
        },
        {
          title: "单位及职务",
          ellipsis: true,
          width: 180,
          customRender: (text, record, index) => {
            return record.unit || "/";
          },
        },
        {
          title: "代表类型",
          ellipsis: true,
          width: 150,
          customRender: (text, record, index) => {
            return record.sfNames || "/";
          },
        },
        // {
        //   title: "籍贯",
        //   ellipsis: true,
        //   width: 50,
        //   customRender: (text, record, index) => {
        //     return record.nation || "/";
        //   },
        // },
        {
          title: "手机号码",
          ellipsis: true,
          width: 100,
          customRender: (text, record, index) => {
            return record.mobile || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 180,
          ellipsis: true,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            let permission = this.showDisabled;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "none" : "inline-block",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.editData(record);
                    },
                  },
                },
                "修改"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: "inline-block",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.delData(record.id);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      rules: {
        title: [
          { required: true, message: "分节名称不能为空", trigger: "blur" },
        ],
        title: [{ required: true, message: "发言人不能为空", trigger: "blur" }],
        title: [
          { required: true, message: "发言内容不能为空", trigger: "blur" },
        ],
      },
      representList: [
        { name: "全部", id: "" },
        { name: "区代表", id: "4" },
        { name: "镇代表", id: "5" },
      ],
      searchDebounced: _.debounce(this.handleQuery, 500),
    };
  },
  created() {
    this.listAdministrativeAreas();
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "网上联络站");
    this.$store.dispatch("navigation/breadcrumb2", "区镇代表管理");
  },
  methods: {
    handleEnter() {
      this.searchDebounced();
    },
    // 高级搜索
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    fetchData() {
      getList(this.queryForm).then((res) => {
        this.dbList = res.rows;
        this.pagination.total = res.total;
      });
    },
    handleQuery() {
      this.pagination.current = 1;
      this.queryForm.pageNum = 1;
      this.fetchData();
    },
    clearQueryForm() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        sfDm: "",
      };
      this.fetchData();
    },
    //  全选
    onSelectAll(state, selectedRows, data, DA) {
      this.iShow = state;
    },
    // 选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.RowKeys = selectedRowKeys;
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
    },
    onSelectData(data, state) {
      this.iShow = state;
    },
    showHandleCancel() {
      this.activitySections = [];
    },
    listAdministrativeAreas() {
      getAdministrativeAreas().then((response) => {
        console.log("🤗🤗🤗, response 行政区域列表=>", response);
        this.administrativeAreas = response.data;
      });
    },
    // 获取乡镇街道
    liststreetTowns(administrativeAreaId) {
      getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
        }
      );
    },
    // 选择行政区域
    handleAdministrativeAreaChange(val) {
      if (!val) return;
      this.queryForm.streetTownId = undefined;
      this.selectadministrativeAreas(val);
      this.liststreetTowns(val);
    },
    // 设置administrativeAreaId
    selectadministrativeAreas(state) {
      this.queryForm.ssxzq = state;
    },
    // 选择乡镇街道
    handlestreetTownChange(val) {
      this.queryForm.ssxzq = this.queryForm.administrativeAreaId;
      this.queryForm.xzjd = this.queryForm.streetTownId;
      this.fetchData();
    },
    //提示选择
    streetTownIdFocus() {
      if (this.queryForm.administrativeAreaId) {
        this.streetTownIdState = true;
      } else {
        this.$message.info("请选择行政区域");
        this.streetTownIdState = false;
      }
    },
    handleTypeChange(value) {
      this.queryForm.sfDm = value;
      this.fetchData();
    },
    addData() {
      this.$router.push({
        path: "/BBS/dbDistrict/add",
      });
    },
    editData(record) {
      this.$router.push({
        path: "/BBS/dbDistrict/edit",
        query: { id: record.id },
      });
    },
    delData(id) {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "确定删除",
        content: "是否确定删除？",
        onOk: () => {
          deleteDbById({ id: id }).then((res) => {
            if (res.code == "0000") {
              this.$message.success("操作成功");
              this.fetchData();
            } else {
              this.$message.error(res.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 14px;
  @include add-size($font_size_16);
  font-family: pingFang-M;
}
.conference {
  margin: 20px 0;
}
</style>
