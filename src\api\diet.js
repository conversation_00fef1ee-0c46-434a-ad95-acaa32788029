import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
    return request({
        url: "/api/v1/meetingDining/getList",
        method: "post",
        data,
    });
}

export function doSave(data) {
    return request({
        url: "/api/v1/meetingDining/addMeetingDining",
        method: "post",
        data,
    });
}

export function doUpdate(data) {
    return request({
        url: "/api/v1/meetingDining/updateMeetingDining",
        method: "post",
        data,
    });
}

export function doDelete(param) {
    return request({
        url: "/api/v1/meetingDining/delMeetingDining/" + param,
        method: "post",
    });
}

export function addDietList(data) {
    return request({
        url: "api/v1/meetingDining/addMeetingDiningList",
        method: "post",
        data,
    });
}

export function downloadExcel(data) {
    return request({
        url: "api/v1/meetingDining/downloadExcel",
        method: "post",
        data,
        responseType: "blob",
    });
}

//impVehicleInformation
// uploadExcel