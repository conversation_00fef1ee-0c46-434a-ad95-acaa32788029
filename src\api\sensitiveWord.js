import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/infoMsg/list",
    method: "post",
    data,
  });
}

export function addList(data) {
  return request({
    url: "/api/v1/infoMsg/save",
    method: "post",
    data,
  });
}

export function updateById(data) {
  return request({
    url: "/api/v1/infoMsg/updateById",
    method: "post",
    data,
  });
}

export function getById(params) {
  return request({
    url: "/api/v1/infoMsg/getById",
    method: "get",
    params,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/infoMsg/delByIds",
    method: "post",
    data,
  });
}

