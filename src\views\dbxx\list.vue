<template>
  <div class="table-container">
    <a-row :gutter="15">
      <a-col style="margin-bottom: 15px;">
        <titleBreacrumb headline="代表信息管理" title="代表信息" />
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
        <a-input
          v-model="filterText"
          placeholder="输入关键字过滤"
          class="tree-input"
          allow-clear
        />
          <a-tree
          ref="orgTree"
          :replace-fields="replaceFields"
          checkable
          :tree-data="treeData"
          @select="handleClick"
        ></a-tree>
           
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="18" :xl="18">
        <byui-query-form>
          <byui-query-form-left-panel>
            <a-select
              v-model="thNum"
              @change="onJcDmChange"
              placeholder="届次"
              class="table-input"
              style="width: 100px;"
            >
              <a-select-option
                v-for="item in thNumOptions"
                :key="item.jcDm"
                :label="item.jcmc"
                :value="item.jcDm"
                >{{ item.jcmc }}</a-select-option
              >
            </a-select>
            <a-button
              icon="search"
              type="primary"
              native-type="submit"
              @click="handleQuery"
              >高级搜索</a-button
            >
          </byui-query-form-left-panel>
          <byui-query-form-right-panel>
            <!-- <a-button
              v-if="showEide"
              type="primary"
              plain
              @click="handleRelOldEide"
              >当前代表信息修改</a-button
            > -->
            <a-button
              v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])"
              type="primary"
              plain
              @click="handleRelOldData"
              >调整代表团</a-button
            >
            <a-button
              v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])"
              type="primary"
              @click="handleDelete"
              >调入</a-button
            >
            <a-button
              v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])"
              type="danger"
              @click="handleDelete"
              plain
              >调出</a-button
            >
            <a-button
              v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])"
              icon="plus"
              type="success"
              circle
              @click="handleAdd"
            ></a-button>
          </byui-query-form-right-panel>
        </byui-query-form>
        <div v-for="item in list" :key="item.id">
          <h3 class="mb10 mt10">{{ item.name }}</h3>
          <el-checkbox-group v-model="checkedLists">
            <draggable
              v-model="item.childrenList"
              animation="300"
              @end="handleEnd"
            >
              <transition-group>
                <el-checkbox
                  v-for="db in item.childrenList"
                  :label="db.id"
                  :key="db.id"
                  @change="clickbox(db.id)"
                  border
                >
                  <span @click="clickbox(db.id)">{{ db.name }}</span>
                </el-checkbox>
              </transition-group>
            </draggable>
          </el-checkbox-group>
        </div>
      </a-col>
    </a-row>
    <userTree ref="userTree"></userTree>
  </div>
</template>

<script>
import { getList, getTree, getDuTree } from "@/api/dbxx";
import { instance_1 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
import UserTree from "../common/userTreeDialog";
//导入draggable组件
import draggable from "vuedraggable";
import { mapGetters } from "vuex";
export default {
  name: "rel",
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
  },
  filters: {},
  data() {
    return {
      noAdd: true,
      list: [],
      checkedLists: [],
      listLoading: true,
      layoutPage: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      queryForm: {},
      thNum: "",
      //机构树配置
      treeData: [],
      defaultProps: {
        children: "childrenList",
        label: "name",
      },
      loading: true,
      filterText: "",
      checkNodeKeys: [],
      defaultExpendedKeys: [],
      i: 0,
      treeFlag: 0,
      treeDialogVisible: false,

      dialogSort: false,
      showEide: false,
      sortForm: {},
      thNumOptions: [],
      replaceFields: {
        title: "name",
        key: "id",
        children: "childrenList",
      },
    };
  },
  components: {
    UserTree,
    draggable,
  },
  created() {
    this.getTree();
  },
  methods: {
    setSelectRows(val) {
      this.selectRows = val;
    },
    handleAdd() {
      if (this.$refs.tree.getCheckedKeys().length == 0) {
        this.$baseMessage("未选中身份", "error");
        return false;
      }
      this.$refs["userTree"].open();
    },
    handleSort(row) {
      this.sortForm = {
        id: row.id,
        sort: row.sort,
      };
      this.dialogSort = true;
    },
    handleEnd(data) {
      console.log(data);
      console.log(this.list);
    },
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          doDeleteRel([row.id]).then((res) => {
            this.$baseMessage(res.message, "success");
            this.fetchData();
          });
        });
      } else {
        if (this.selectRows.length > 0) {
          const ids = this.selectRows.map((item) => item.id);
          this.$baseConfirm("你确定要删除选中项吗", null, () => {
            doDeleteRel(ids).then((res) => {
              this.$baseMessage(res.message, "success");
              this.fetchData();
            });
          });
        } else {
          this.$baseMessage("未选中任何行", "error");
          return false;
        }
      }
    },
    // 代表信息修改
    clickbox(id) {
      this.showEide = true;
    },
    initJcOptions(dhDm) {
      instance_1({
        url: "/common/jcList",
        method: "get",
        params: { dhDm: dhDm },
      }).then((res) => {
        if (res.data.code == "0000") {
          this.thNumOptions = res.data.data;

          res.data.data.forEach((item) => {
            if (item.dqjc == "Y") {
              this.thNum = item.jcDm;
              this.queryForm.jcDm = item.jcDm;
              this.fetchData();
            }
          });
        }
      });
    },
    handleRelOldEide() {
      this.$router.push({
        path: "dbxxedit",
        query: {
          // communityScheduleId: this.communityScheduleId,
          // oper: 'add'
        },
      });
    },
    handleRelOldData(row) {
      let that = this;
      if (that.$refs.tree.getCheckedKeys().length == 0) {
        that.$baseMessage("未选中身份", "error");
        return false;
      } else {
        if (row.id) {
          that.$baseConfirm("你确定要为该用户关联现有数据吗", null, () => {
            doRelOldData(that.queryForm.groupId, [row]).then((res) => {
              that.$baseMessage(res.message, "success");
              that.fetchData();
            });
          });
        } else {
          if (that.selectRows.length > 0) {
            that.$baseConfirm("你确定要为选中用户关联现有数据吗", null, () => {
              doRelOldData(that.queryForm.groupId, that.selectRows).then(
                (res) => {
                  that.$baseMessage(res.message, "success");
                  that.fetchData();
                }
              );
            });
          } else {
            that.$baseMessage("未选中任何行", "error");
            return false;
          }
        }
      }
    },
    handleSizeChange(val) {
      this.queryForm.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryForm.current = val;
      this.fetchData();
    },
    handleQuery() {
      this.queryForm.current = 1;
      this.fetchData();
    },

    fetchData() {
      this.listLoading = true;
      instance_1({
        url: "/treeGroup/duTree",
        method: "post",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.list = res.data.data[0].childrenList;
          setTimeout(() => {
            this.listLoading = false;
          }, 500);
        } else {
          this.$baseMessage(res.data.msg, "error");
        }
      });
    },

    // 获取tree数据
    getTree() {
      getTree().then((res) => {
        this.treeData = res.data;
      });
    },

    // 节点过滤操作
    filterNode(value, data) {
      if (!value) return true;                                                                                                                                                                                           
      return data.name.indexOf(value) !== -1;
    },
    //单选节点
    handleClick(data, e) {
      console.log(e)
      if (data.childrenList.length == 0) {
        this.$refs.tree.selectedKeys([data.id]);
      } else {
        this.$baseMessage("请选择具体的组织机构", "error");
      }
    },

    handleCheck(data, checked, indeterminate) {
      if (checked) {
        this.$refs.tree.selectedKeys([data.id]);
        this.queryForm.orgId = data.id;
        this.initJcOptions(data.dhDm);
      }
    },

    saveRelUser(checkedNodes) {
      let relList = [];
      checkedNodes.forEach((element) => {
        if (element.itemType == 2) {
          relList.push({
            identityId: this.queryForm.groupId,
            userId: element.id,
          });
        }
      });
      doSaveRel(relList).then((res) => {
        this.$message({
          message: "保存成功",
          type: "success",
        });

        this.fetchData();
      });
    },

    handleSortClose() {
      this.sortForm = {};
      this.dialogSort = false;
    },

    handleSortSave() {
      doUpdateRel(this.sortForm).then((res) => {
        this.$baseMessage(`排序修改成功`, "success");
        setTimeout(() => {
          this.dialogSort = false;
          this.fetchData();
        }, 500);
      });
    },

    onJcDmChange(value) {
      this.queryForm.jcDm = value;
      this.fetchData();
    },
    checkPermission,
  },
};
</script>

<style>
.right-panel {
  flex-wrap: nowrap !important;
}
</style>
<style scoped>
.el-checkbox--small {
  margin-bottom: 5px;
}
</style>
