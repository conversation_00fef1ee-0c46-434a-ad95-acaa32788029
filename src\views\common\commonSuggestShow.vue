<template>
  <a-modal
    :visible="visible"
    :title="showOperate ? '处理建议' : '建议详情'"
    width="80%"
    :centered="true"
    :footer="null"
    :body-style="{ height: '600px', overflowY: 'auto' }"
    @cancel="close"
  >
    <a-spin :indicator="indicator" :spinning="listLoading">
      <div v-if="showOperate">
        <!-- 联名 -->
        <div
          v-if="
            (record.status == 11 &&
              userData.authorities[0].authority == 'YAJY_DB' &&
              userData.userId != record.createUserId) ||
            scenesStatus == '处理联名'
          "
        >
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button :loading="lmLoading" @click="changeJoin(1)"
                >同意联名</a-button
              >
              <a-button :loading="lmLoading" @click="changeJoin(0)"
                >不同意联名</a-button
              >
            </a-space>
          </div>
        </div>
        <!-- 交办 -->
        <div v-if="record.status == 40 && scenesStatus == ''">
          <div>
            <a-form-model
              ref="resourceForm"
              :model="resourceForm"
              :rules="rules"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }"
            >
              <a-row>
                <a-col span="24">
                  <a-form-model-item label="内容分类" prop="contentType">
                    <a-select
                      v-model="resourceForm.contentType"
                      allow-clear
                      style="width: 400px"
                    >
                      <a-select-option
                        v-for="item in contentTypeList"
                        :key="item.id"
                        :value="item.id"
                        >{{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                  <a-form-model-item label="主办单位" prop="enrollTime">
                    <div class="searchStyle">
                      <a-input
                        v-model="resourceForm.hostName"
                        disabled
                        enter-button
                      >
                      </a-input>
                      <a-button
                        type="primary"
                        icon="search"
                        @click="openUnitTable('host')"
                      >
                      </a-button>
                    </div>
                  </a-form-model-item>
                  <a-form-model-item
                    label="主办单位办理期限"
                    prop="mainHandleLastTimeStr"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 9 }"
                  >
                    <a-date-picker
                      v-model="resourceForm.mainHandleLastTimeStr"
                      :style="{ width: '100%' }"
                      allow-clear
                      value-format="YYYY-MM-DD"
                      placeholder="选择主办单位办理期限"
                    ></a-date-picker>
                  </a-form-model-item>
                  <a-form-model-item label="会办单位">
                    <div class="searchStyle">
                      <a-input
                        v-model="resourceForm.meetName"
                        disabled
                        enter-button
                      >
                      </a-input>
                      <a-button
                        type="primary"
                        icon="search"
                        @click="openUnitTable('meet')"
                      >
                      </a-button>
                    </div>
                  </a-form-model-item>
                  <a-form-model-item
                    label="会办单位办理期限"
                    prop="minorHandleLastTimeStr"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 9 }"
                  >
                    <a-date-picker
                      v-model="resourceForm.minorHandleLastTimeStr"
                      allow-clear
                      :style="{ width: '100%' }"
                      value-format="YYYY-MM-DD"
                      placeholder="选择会办单位办理期限"
                    ></a-date-picker>
                  </a-form-model-item>
                  <!-- 12-13-- -->
<!--                  <a-form-model-item-->
<!--                    label="是否公开"-->
<!--                    prop="ifPublice"-->
<!--                    :label-col="{ span: 6 }"-->
<!--                    :wrapper-col="{ span: 9 }"-->
<!--                  >-->
<!--                    <a-radio-group-->
<!--                      v-model="resourceForm.ifPublice"-->
<!--                      @change="changeifPublice(resourceForm.ifPublice)"-->
<!--                      :disabled="isPublicDisable"-->
<!--                    >-->
<!--                      <a-radio value="1"> 是 </a-radio>-->
<!--                      <a-radio value="0"> 否 </a-radio>-->
<!--                    </a-radio-group>-->
<!--                  </a-form-model-item>-->
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button
                :loading="jbLoading"
                :hidden="
                  userData.authorities[0].authority == 'YAJY_XLWBC' ||
                  userData.authorities[0].authority == 'YAJY_DBCBC'
                "
                @click="save"
                >保存</a-button
              >
              <a-button :loading="jbLoading" @click="manage">交办</a-button>
              <!--              <a-button :loading="jbLoading||zwCKyj"
                        :hidden="
                userData.authorities[0].authority == 'YAJY_XLWBC' ||
                userData.authorities[0].authority == 'YAJY_DBCBC'
              "
                        @click="changeRefer">转为参考建议</a-button>-->
            </a-space>
          </div>
        </div>
        <!-- 校核 -->
        <div
          v-if="
            record.status == 20 &&
            scenesStatus == '' &&
            (userData.authorities[0].authority == 'YAJY_QRD' ||
              userData.authorities[0].authority == 'YAJY_XLW')
          "
        >
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button :loading="jhLoading" @click="openModal('return')"
                >退回</a-button
              >
              <a-button :loading="jhLoading" @click="openModal('pass')"
                >校核通过</a-button
              >
            </a-space>
          </div>
          <!-- 填写备注 -->
          <a-modal
            :body-style="{ height: '200px' }"
            :visible="returnValue"
            :title="returnValueTitle"
            @ok="submitTheSubject"
            @cancel="cancelReturn"
          >
            <a-form-model
              ref="resourceForm"
              :model="resourceForm"
              :rules="rules"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }"
            >
              <a-row>
                <a-col span="24">
                  <a-form-model-item label="回复意见" prop="comment">
                    <a-textarea
                      v-model="resourceForm.comment"
                      :rows="5"
                    ></a-textarea>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </a-modal>
        </div>
        <!-- 审核 -->
        <div v-if="record.status == 30 && scenesStatus == ''">
          <div>
            <a-form-model
              ref="resourceForm"
              :model="resourceForm"
              :rules="rules"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }"
            >
              <a-row>
                <a-col span="24">
                  <a-form-model-item label="处理意见" prop="comment">
                    <!-- 注释 isOtheryj   @change="changeTEXT"-->
                    <a-checkbox-group v-model="resourceForm.text">
                      <a-row>
                        <a-col
                          v-for="(item, index) in OpinionOptionList"
                          :key="index"
                        >
                          <a-checkbox :value="item"
                            >{{ item }}<br
                          /></a-checkbox>
                        </a-col>
                      </a-row>
                      <!-- <a-checkbox :value="'其他'">其他<br></br></a-checkbox> -->
                    </a-checkbox-group>
                    <!-- -->
                    <a-textarea
                      v-model="resourceForm.qt"
                      v-show="isOtheryj"
                      :rows="5"
                    ></a-textarea>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button :loading="shLoading" @click="submitReview('return')"
                >退回</a-button
              >
              <a-button :loading="shLoading" @click="submitReview('pass')"
                >审核通过</a-button
              >
            </a-space>
          </div>
        </div>
        <!-- 答复 根据承办子流程状态为2 -->
        <div
          v-if="
            (record.status == 60 &&
              userData.authorities[0].authority == 'YAJY_XLW' &&
              !(scenesStatus == '处理调整' && record.ifObjection) &&
              !(scenesStatus == '处理延期' && record.ifDelay)) ||
            (record.undertakeStatus == 2 &&
              userData.authorities[0].authority == 'YAJY_CBDW') ||
            (scenesStatus == '答复修改' &&
              userData.authorities[0].authority == 'YAJY_CBDW')
          "
        >
          <div>
            <a-form-model
              ref="resourceForm"
              :model="resourceForm"
              :rules="rules"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }"
            >
              <a-row>
                <a-col span="24">
                  <a-form-model-item label="经办人姓名" prop="jbrName">
                    <a-input
                      v-model="resourceForm.jbrName"
                      style="width: 85%"
                    ></a-input>
                    <a-button
                      type=""
                      style="margin-left: 10px"
                      @click="showJbrTable(resourceForm)"
                      >选择经办人</a-button
                    >
                  </a-form-model-item>
                </a-col>
                <a-col span="24">
                  <a-form-model-item label="经办人部门" prop="jbrDept">
                    <a-input v-model="resourceForm.jbrDept"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col span="24">
                  <a-form-model-item label="经办人职务" prop="jbrJob">
                    <a-input v-model="resourceForm.jbrJob"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col span="24">
                  <a-form-model-item label="经办人手机" prop="jbrPhone">
                    <a-input v-model="resourceForm.jbrPhone"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col span="24">
                  <a-form-model-item label="经办人办公电话" prop="jbrOph">
                    <a-input v-model="resourceForm.jbrOph"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col
                  v-if="
                    record.ifMain == 1 ||
                    userData.authorities[0].authority == 'YAJY_XLW'
                  "
                  span="24"
                >
                  <a-form-model-item label="答复类型" prop="dflb">
                    <a-select v-model="resourceForm.dflb">
                      <a-select-option value="A"
                        >A（已解决或基本解决的问题）</a-select-option
                      >
                      <a-select-option value="B"
                        >B（列入年度计划解决的问题）</a-select-option
                      >
                      <a-select-option value="C"
                        >C（列入规划逐步解决的问题）</a-select-option
                      >
                      <a-select-option value="D"
                        >D（受法规、政策、财力等客观条件限制，难以实现）</a-select-option
                      >
                      <!-- <!-- <a-select-option value="D"
                      >D（因条件限制或其他原因无法解决及作为参考的）</a-select-option
                    >
                    <a-select-option value="E">E（协办答复）</a-select-option> -->
                      -->
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col
                  v-if="
                    record.ifMain == 1 ||
                    userData.authorities[0].authority == 'YAJY_XLW'
                  "
                  span="24"
                >
                  <a-form-model-item label="沟通方式" prop="dffs">
                    <a-checkbox-group v-model="resourceForm.dffs">
                      <a-checkbox value="4">电话 短信 邮件 微信</a-checkbox>
                      <a-checkbox value="2">座谈会 调研</a-checkbox>
                      <a-checkbox value="3"
                        >代表表示无需见面沟通（需上传相关证据）</a-checkbox
                      >
                    </a-checkbox-group>
                  </a-form-model-item>
                </a-col>
                <a-col
                  v-if="
                    (record.ifMain == 1 ||
                      userData.authorities[0].authority == 'YAJY_XLW') &&
                    resourceForm.dffs.includes('2')
                  "
                  span="24"
                >
                  <a-form-model-item label="上传座谈会调研证据">
                    <!-- 上传座谈会调研证据 入参字段不明 -->
                    <a-upload
                      action=""
                      accept=".docx,.doc,.pdf,accept,image/*,video/*,audio/*"
                      :remove="handleTextRemove8"
                      :before-upload="
                        () => {
                          return false;
                        }
                      "
                      :file-list="fileTextList_8"
                      @change="uploadChange($event, '8')"
                    >
                      <a-button
                        :disabled="fileTextList_8.length == 1"
                        type="primary"
                        >点击上传</a-button
                      >
                      <div :hidden="fileTextList_8.length == 1">
                        请上传文档、图片、音视频，且附件大小不能超过10mb
                      </div>
                    </a-upload>
                  </a-form-model-item>
                </a-col>
                <a-col
                  v-if="
                    (record.ifMain == 1 ||
                      userData.authorities[0].authority == 'YAJY_XLW') &&
                    resourceForm.dffs.includes('3')
                  "
                  span="24"
                >
                  <a-form-model-item :colon="false">
                    <div slot="label">
                      <span style="color: #f5222d">*</span>
                      上传代表无需沟通证据：
                    </div>
                    <!-- 上传代表无需沟通证据 入参字段不明 -->
                    <a-upload
                      action=""
                      accept=".docx,.doc,.pdf,accept,image/*,video/*,audio/*"
                      :remove="handleTextRemove7"
                      :before-upload="
                        () => {
                          return false;
                        }
                      "
                      :file-list="fileTextList_7"
                      @change="uploadChange($event, '7')"
                    >
                      <a-button
                        :disabled="fileTextList_7.length == 1"
                        type="primary"
                        >点击上传</a-button
                      >
                      <div :hidden="fileTextList_7.length == 1">
                        请上传文档、图片、音视频，且附件大小不能超过10mb
                      </div>
                    </a-upload>
                  </a-form-model-item>
                </a-col>
                <a-col
                  v-if="
                    record.ifMain == 1 ||
                    userData.authorities[0].authority == 'YAJY_XLW'
                  "
                  span="24"
                >
                  <a-form-model-item label="答复是否公开" prop="isPublic">
                    <a-radio-group v-model="resourceForm.isPublic">
                      <a-radio value="1">是</a-radio>
                      <a-radio value="0">否</a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                </a-col>
                <a-col
                  span="24"
                  v-if="
                    record.ifMain == 1 ||
                    userData.authorities[0].authority == 'YAJY_XLW'
                  "
                >
                  <a-form-model-item
                    label="建议内容质量评分"
                    prop="orgEvaluation"
                  >
                    <a-input-number
                      v-model="resourceForm.orgEvaluation"
                      placeholder="请输入分值"
                      size="mini"
                      :precision="1"
                      ref="optionInputALL"
                      :min="0"
                      :max="10"
                      :value="resourceForm.orgEvaluation"
                      @change="changeorgEvaluation"
                      style="width: 20%; margin-left: 15px"
                    ></a-input-number>

                    分 (评分范围:0-10分,可保留一位小数)
                  </a-form-model-item>
                </a-col>
                <a-col
                  v-if="
                    (record.ifMain == 1 ||
                      userData.authorities[0].authority == 'YAJY_XLW') &&
                    resourceForm.isPublic === '0'
                  "
                  span="24"
                >
                  <a-form-model-item label="答复不公开理由" prop="remark">
                    <a-textarea v-model="resourceForm.remark"></a-textarea>
                  </a-form-model-item>
                </a-col>
                <a-col span="24">
                  <a-form-model-item :colon="false">
                    <div slot="label">
                      <span style="color: #f5222d">*</span>
                      答复上传：
                    </div>
                    <!-- 上传 -->
                    <a-upload
                      action=""
                      accept=".docx,.doc,.pdf,accept,image/*,video/*,audio/*"
                      :remove="handleTextRemove"
                      :before-upload="beforeUpload"
                      :file-list="fileTextList"
                      @change="uploadChange($event, '2')"
                    >
                      <a-button
                        :disabled="fileTextList.length == 1"
                        type="primary"
                        >点击上传</a-button
                      >
                      <div :hidden="fileTextList.length == 1">
                        请上传文档、图片、音视频，且附件大小不能超过10mb
                      </div>
                    </a-upload>
                  </a-form-model-item>
                </a-col>
                <a-col
                  v-if="
                    record.ifMain == 1 ||
                    userData.authorities[0].authority == 'YAJY_XLW'
                  "
                  span="24"
                >
                  <a-form-model-item
                    v-if="hbDataSource.length > 0"
                    label="政务协同"
                  >
                    <div
                      v-for="item in hbDataSource"
                      :key="item.minorUndertakeId"
                    >
                      <div>
                        <span>{{ item.minorOrgName }}</span>
                        <a-input-number
                          v-model="item.score"
                          :min="1"
                          :max="100"
                          style="margin-left: 10px"
                        ></a-input-number>
                        <a-slider v-model="item.score" :min="1" :max="100" />
                      </div>
                    </div>
                  </a-form-model-item>
                  <a-col v-else span="12" push="4">
                    <p :style="{ marginBottom: '10px' }">暂无会办单位数据</p>
                  </a-col>
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button
                v-if="
                  scenesStatus != '答复修改' &&
                  ((record.undertakeStatus == 2 &&
                    userData.authorities[0].authority == 'YAJY_CBDW') ||
                    userData.authorities[0].authority == 'YAJY_XLW')
                "
                :loading="sfLoading"
                @click="
                  replyData(
                    'save',
                    userData.authorities[0].authority == 'YAJY_XLW'
                  )
                "
                >保存</a-button
              >
              <a-button
                v-if="
                  scenesStatus != '答复修改' &&
                  ((record.undertakeStatus == 2 &&
                    userData.authorities[0].authority == 'YAJY_CBDW') ||
                    userData.authorities[0].authority == 'YAJY_XLW')
                "
                :loading="sfLoading"
                @click="
                  replyData(
                    'submit',
                    userData.authorities[0].authority == 'YAJY_XLW'
                  )
                "
                >答复</a-button
              >
              <a-button
                v-if="scenesStatus == '答复修改'"
                :loading="sfLoading"
                @click="
                  replyData(
                    'submit',
                    userData.authorities[0].authority == 'YAJY_XLW'
                  )
                "
                >修改答复</a-button
              >
              <a-button
                v-if="
                  scenesStatus != '答复修改' &&
                  record.undertakeStatus == 2 &&
                  userData.authorities[0].authority == 'YAJY_CBDW'
                "
                :loading="sfLoading"
                @click="adjust"
                >申请调整</a-button
              >
              <a-button
                v-if="
                  scenesStatus != '答复修改' &&
                  record.undertakeStatus == 2 &&
                  userData.authorities[0].authority == 'YAJY_CBDW'
                "
                :loading="sfLoading"
                @click="postpone"
                >申请延期</a-button
              >
              <a-button
                v-if="
                  scenesStatus != '答复修改' &&
                  record.undertakeStatus == 2 &&
                  userData.authorities[0].authority == 'YAJY_CBDW' &&
                  record.ifMain == 1
                "
                :loading="sfLoading"
                @click="adjustMinor"
              >调整会办单位</a-button
              >
            </a-space>
          </div>
        </div>
        <!--答复状态下 处理延期/调整 -->
        <div v-if="scenesStatus == '处理延期' || scenesStatus == '处理调整' || scenesStatus == '审核调整'">
          <div>
            <standard-table
              row-key="undertakeId"
              :columns="columnsPostpone"
              :selected-rows.sync="selectedRowsPostpone"
              :data-source="dataSourcePostpone"
              :pagination="false"
            >
              <div slot="action" slot-scope="{ text, record }">
                <a-button
                  v-if="record.attName && record.attSuffix"
                  type="link"
                  @click="downFileDataDf(record, 6)"
                >
                  {{ record.attName + "." + record.attSuffix }}
                </a-button>
              </div>
            </standard-table>
          </div>
          <!-- 处理延期 -->
          <div
            v-if="scenesStatus == '处理延期' && record.ifDelay"
            style="text-align: center; margin: 10px 0"
          >
            <a-space class="operator">
              <a-button @click="treatmentDelay">处理延期</a-button>
              <a-button @click="maintainAPrimaryOffice">维持原交办</a-button>
            </a-space>
          </div>
          <!-- 处理调整 -->
          <!-- ifObjection 判断是否处理调整 -->
          <div
            v-if="scenesStatus == '处理调整' && record.ifObjection"
            style="text-align: center; margin: 10px 0"
          >
            <a-space class="operator">
              <a-button @click="openUnitModalVisible">单位调整</a-button>
              <a-button @click="maintainAPrimaryOffice">维持原交办</a-button>
              <!-- <a-button @click="resetManage">重新交办</a-button> -->
            </a-space>
          </div>
          <!-- 审核会办单位调整 -->
          <div
            v-if="scenesStatus == '审核调整' && record.ifMinor"
            style="text-align: center; margin: 10px 0"
          >
            <a-space class="operator">
              <a-button @click="openUnitModalInfoVisible">查看调整信息</a-button>
              <a-button @click="saveUnitMinor">审核通过</a-button>
              <a-button @click="returnUnitMinor">退回</a-button>
            </a-space>
          </div>

          <!-- 延期/调整 弹出框 -->
          <a-modal
            :visible="visiblePostpone"
            width="50%"
            :title="modalTitle"
            @ok="submitDelay"
            @cancel="
              () => {
                visiblePostpone = false;
              }
            "
          >
            <a-form-model
              ref="resourceForm"
              :model="resourceForm"
              :rules="rules"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }"
            >
              <a-row>
                <a-col span="24">
                  <a-form-model-item label="描述" prop="comment">
                    <a-textarea
                      v-model="resourceForm.comment"
                      row="6"
                    ></a-textarea>
                  </a-form-model-item>
                </a-col>
                <a-col v-if="modalTitle == '处理延期'" span="24">
                  <a-form-model-item label="延期天数" prop="delayDay">
                    <a-input-number
                      v-model="resourceForm.delayDay"
                      :min="0"
                    ></a-input-number>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </a-modal>
          <!-- 单位调整 弹出框 -->
          <a-modal
            :visible="unitModalVisible"
            width="50%"
            title="单位调整"
            :body-style="{ height: '460px' }"
            ok-text="交办"
            @ok="saveUnitModal"
            @cancel="cancelUnitModal"
          >
            <div :style="tabStyle">
              <a-space class="operator" v-if="scenesStatus != '审核调整'">
                <a-button type="primary" @click="openChange">新增</a-button>
                <a-button icon="delete" @click="unitModalVisibleDel"
                  >删除</a-button
                >
                <a-button type="" @click="changeIfMain">修改</a-button>
              </a-space>
              <standard-table
                row-key="orgCode"
                :columns="unitModalVisibleColumns"
                :selected-rows.sync="unitModalVisibleSelectedRows"
                :data-source="unitModalVisibleDataSource"
                :pagination="false"
                :scroll="{ x: 600, y: 300 }"
              >
              </standard-table>
            </div>
          </a-modal>

          <a-modal
            :visible="unitModalInfoVisible"
            width="50%"
            title="会办单位调整信息"
            :body-style="{ height: '460px' }"
            ok-text="确定"
            @ok="cancelUnitInfoModal"
            @cancel="cancelUnitInfoModal"
          >
            <div :style="tabStyle">
              <a-space class="operator" v-if="scenesStatus != '审核调整'">
                <a-button type="primary" @click="openChange">新增</a-button>
                <a-button icon="delete" @click="unitModalVisibleDel"
                >删除</a-button
                >
                <a-button type="" @click="changeIfMain">修改</a-button>
              </a-space>
              <standard-table
                row-key="orgCode"
                :columns="unitModalVisibleColumns"
                :selected-rows.sync="unitModalVisibleSelectedRows"
                :data-source="unitModalVisibleDataSource"
                :pagination="false"
                :scroll="{ x: 600, y: 300 }"
              >
              </standard-table>
            </div>
          </a-modal>

          <!-- 单位调整弹出框 修改主办/会办 -->
          <a-modal
            :visible="changeIfMainVisible"
            :title="changeIfMainTitle"
            @ok="saveChangeIfMain"
            @cancel="
              () => {
                changeIfMainVisible = false;
              }
            "
          >
            <a-form>
              <a-form-item
                v-if="changeIfMainTitle == '添加承办单位'"
                label="选择单位"
              >
                <div class="searchStyle">
                  <a-input v-model="changeIfMainForm.orgName" disabled>
                  </a-input>
                  <a-button
                    type="primary"
                    icon="search"
                    @click="openUnitTable('单位调整')"
                  >
                  </a-button>
                </div>
              </a-form-item>
              <a-form-item label="设置承办类别">
                <a-select v-model="changeIfMainForm.ifmain">
                  <a-select-option key="1" :value="1">主办单位</a-select-option>
                  <a-select-option key="0" :value="0">会办单位</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-modal>
        </div>
        <!-- 反馈 -->
        <div v-if="record.undertakeStatus == 3 && userName == record.headPer && record.status == 70">
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button @click="openEvaluationModal">反馈</a-button>
            </a-space>
          </div>
        </div>
        <!-- 草稿 -->
        <div
          v-if="
            (record.status == 10 || record.status == 11) &&
            userData.userId == record.createUserId
          "
        >
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button @click="formallySubmit">正式提交</a-button>
            </a-space>
          </div>
        </div>
      </div>
      <a-tabs v-model="tabsKey" type="card" @change="changeTab">
        <a-tab-pane key="1" tab="议案建议信息">
          <div :style="tabStyle">
            <a-descriptions bordered size="small">
              <a-descriptions-item label="标题">{{
                record.proposalTitle
              }}</a-descriptions-item>
              <a-descriptions-item label="届次">{{
                record.periodDesc
              }}</a-descriptions-item>
              <a-descriptions-item label="意向承办单位">{{
                record.intentOrgName || ""
              }}</a-descriptions-item>
              <a-descriptions-item label="状态">{{
                record.status | filterStatus
              }}</a-descriptions-item>
              <a-descriptions-item label="当前经办人" :span="2">
                <div v-if="record.yajyOrgList && record.yajyOrgList.length > 0">
                  <span
                    v-for="(item, index) in record.yajyOrgList"
                    :key="index"
                  >
                    {{
                      item.orgName +
                      "(" +
                      item.orgDispname +
                      item.orgDispphone +
                      ");"
                    }}
                  </span>
                </div>
                <div
                  v-if="
                    record.yajyOrgOperDTOList &&
                    record.yajyOrgOperDTOList.length > 0
                  "
                >
                  <span v-if="record.yajyOrgOperDTOList[0].orgCode == '387'">
                    <span>
                      {{
                        record.yajyOrgOperDTOList[0].orgName +
                        "(" +
                        record.yajyOrgOperDTOList[0].telNo +
                        ")"
                      }}
                    </span>
                    <br />
                  </span>
                  <span v-else>
                    <span
                      v-for="(item, index) in record.yajyOrgOperDTOList"
                      :key="index"
                    >
                      {{ item.operName + "(" + item.telNo + ")" }}
                    </span>
                  </span>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="建议号">{{
                record.proposalNum
              }}</a-descriptions-item>
              <a-descriptions-item label="议案建议类型">{{
                record.proposalType | filterProposalType
              }}</a-descriptions-item>
              <a-descriptions-item label="领衔代表">{{
                record.headPer
              }}</a-descriptions-item>
              <a-descriptions-item label="内容分类">{{
                record.proposalContentType | filterProposalContentType
              }}</a-descriptions-item>
              <a-descriptions-item label="办理情况">{{
                record.proposalHandle
              }}</a-descriptions-item>
              <a-descriptions-item label="所在代表团">{{
                record.deputyOrg
              }}</a-descriptions-item>
              <a-descriptions-item label="交办日期">{{
                record.handleDate
              }}</a-descriptions-item>
              <a-descriptions-item label="办理期限至"
                >主办：{{
                  majorLastTime ? majorLastTime.substring(0, 10) : ""
                }}
                会办：{{
                  minorLastTime ? minorLastTime.substring(0, 10) : ""
                }}</a-descriptions-item
              >
              <a-descriptions-item label="答复方式">{{
                record.codeHaveOrNo == "1"
                  ? "书面"
                  : record.codeHaveOrNo == "0"
                  ? "网上"
                  : ""
              }}</a-descriptions-item>
              <a-descriptions-item label="加强沟通">
                {{
                  record.ifContect == "1"
                    ? "需要见面座谈和调研"
                    : record.ifContect == "0"
                    ? "只需电话微信沟通"
                    : record.ifContect == "2"
                    ? "不需要沟通，直接答复"
                    : record.ifContect == "3"
                    ? "工作参考用，不需要正式书面答复"
                    : ""
                }}
              </a-descriptions-item>
              <a-descriptions-item label="多年多次提出、尚未解决">{{
                record.overTheYearsNotResolved == "1"
                  ? "是"
                  : record.overTheYearsNotResolved == "0"
                  ? "否"
                  : record.overTheYearsNotResolved == "2"
                  ? "未详"
                  : ""
              }}</a-descriptions-item>
              <a-descriptions-item label="公开情况">{{
                record.ifPublice == "0"
                  ? "不"
                  : record.ifPublice == "1"
                  ? "是"
                  : record.ifPublice == "2"
                  ? "已公开"
                  : ""
              }}</a-descriptions-item>

              <a-descriptions-item label="附件列表" :span="3"
                ><span v-if="record.existFile == true"
                  >附件：<a @click="downFileData('1', '')">附件下载</a></span
                >
              </a-descriptions-item>
              <a-descriptions-item label="建议纸" :span="3"
                ><a @click="downMotion">下载建议纸</a>
              </a-descriptions-item>
              <a-descriptions-item label="正文" :span="3">
                <a-space>
                  <a @click="lookWord">查看正文</a>
                  <a @click="downFileData('9', '')">下载正文附件</a>
                  <a @click="resetFile">重新上传正文附件</a>
                  <!-- <a @click="regenerateText">重新生成正文</a> -->
                </a-space>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="答复、反馈">
          <div :style="tabStyle">
            <a-descriptions bordered size="small">
              <a-descriptions-item label="标题" :span="3">{{
                record.proposalTitle
              }}</a-descriptions-item>
              <a-descriptions-item label="建议号">{{
                record.proposalNum
              }}</a-descriptions-item>
              <a-descriptions-item label="类别">{{
                record.proposalType | filterProposalType
              }}</a-descriptions-item>
              <a-descriptions-item label="领衔代表">{{
                record.headPer
              }}</a-descriptions-item>
              <!-- 电话 短信 邮件 微信：0或1或4      2  座谈会  调研    3   代表表示无需见面沟通 -->
              <a-descriptions-item label="答复1沟通方式">{{
                record.dffs == "1" || record.dffs == "0" || record.dffs == "4"
                  ? "电话 短信 邮件 微信"
                  : record.dffs == "2"
                  ? "座谈会  调研"
                  : record.dffs == "3"
                  ? "代表表示无需见面沟通"
                  : ""
              }}</a-descriptions-item>
            </a-descriptions>
            <a-descriptions
              v-if="majorUndertakeDfList.length > 0"
              title="主办单位"
            ></a-descriptions>
            <template
              v-for="item in majorUndertakeDfList"
              v-if="majorUndertakeDfList.length > 0"
            >
              <a-descriptions
                :key="item.undertakeId"
                size="small"
                bordered
                class="mt2"
                :column="{ xxl: 8, xl: 8, lg: 8, md: 4, sm: 4, xs: 1 }"
              >
                <a-descriptions-item label="单位名称" :span="2">{{
                  item.orgName
                }}</a-descriptions-item>
                <a-descriptions-item label="签收人" :span="4">{{
                  item.signName
                }}</a-descriptions-item>
                <a-descriptions-item label="签收人手机" :span="2"
                  ><a>{{ item.signPhone }}</a></a-descriptions-item
                >
                <a-descriptions-item label="是否签收" :span="2">{{
                  item.signTime ? item.signTime : "未签收"
                }}</a-descriptions-item>
                <a-descriptions-item label="经办人" :span="4"
                  >部门：{{ item.jbrDept }} 职务：{{ item.jbrJob }} 姓名：{{
                    item.jbrName
                  }}</a-descriptions-item
                >
                <a-descriptions-item label="经办人手机" :span="2"
                  ><a>{{ item.jbrPhone }}</a></a-descriptions-item
                >
              </a-descriptions>
              <template v-for="(itemdf, index) in item.dfList">
                <a-descriptions
                  :key="itemdf.dfId"
                  size="small"
                  bordered
                  :column="{ xxl: 8, xl: 8, lg: 8, md: 4, sm: 4, xs: 1 }"
                >
                  <a-descriptions-item
                    :label="`第${index + 1}次答复`"
                    :span="2"
                  >
                    {{ itemdf.dfrq }}
                  </a-descriptions-item>
                  <a-descriptions-item label="答复类型" :span="2"
                    ><span>{{ itemdf.dflb }}</span>
                  </a-descriptions-item>
                  <a-descriptions-item label="答复公开" :span="2"
                    ><span>{{ itemdf.isPublic == "1" ? "是" : "否" }}</span>
                  </a-descriptions-item>
                  <a-descriptions-item label="附件名称" :span="2"
                    ><a
                      v-if="itemdf.fujianList.length > 0"
                      @click="downFileDataDf(itemdf, 2)"
                      >【查看答复附件】</a
                    >
                  </a-descriptions-item>
                  <a-descriptions-item
                    v-if="itemdf.isPublic == '0'"
                    label="答复不公开理由"
                    :span="8"
                    ><span>{{ itemdf.remark }}</span>
                  </a-descriptions-item>
                  <a-descriptions-item
                    v-if="itemdf.hisEvaluation"
                    :label="`第${index + 1}次反馈`"
                    :span="2"
                  >
                    {{ itemdf.hisEvaluation.submittime }}
                  </a-descriptions-item>
                  <a-descriptions-item
                    v-if="itemdf.hisEvaluation"
                    label="反馈意见"
                    :span="4"
                    ><span v-if="itemdf.hisEvaluation.score >= 80">满意</span>
                    <span
                      v-if="
                        itemdf.hisEvaluation.score >= 60 &&
                        itemdf.hisEvaluation.score < 80
                      "
                      >基本满意</span
                    >
                    <span v-if="itemdf.hisEvaluation.score < 60">不满意</span>
                  </a-descriptions-item>
                  <a-descriptions-item
                    v-if="itemdf.hisEvaluation"
                    label="反馈内容"
                    :span="2"
                    ><a @click="viewFkEvaluation(itemdf.hisEvaluation)"
                      >【查看反馈内容】</a
                    >
                  </a-descriptions-item>
                </a-descriptions>
              </template>
            </template>
            <a-descriptions
              v-if="minorUndertakeDfList.length > 0"
              title="会办单位"
            ></a-descriptions>
            <template
              v-for="item in minorUndertakeDfList"
              v-if="minorUndertakeDfList.length > 0"
            >
              <a-descriptions
                :key="item.undertakeId"
                size="small"
                bordered
                class="mt2"
                :column="{ xxl: 8, xl: 8, lg: 8, md: 4, sm: 4, xs: 1 }"
              >
                <a-descriptions-item label="单位名称" :span="2">{{
                  item.orgName
                }}</a-descriptions-item>
                <a-descriptions-item label="签收人" :span="4">{{
                  item.signName
                }}</a-descriptions-item>
                <a-descriptions-item label="签收人手机" :span="2"
                  ><a>{{ item.signPhone }}</a></a-descriptions-item
                >
                <a-descriptions-item label="是否签收" :span="2">{{
                  item.signTime ? item.signTime : "未签收"
                }}</a-descriptions-item>
                <a-descriptions-item label="经办人" :span="4"
                  >部门：{{ item.jbrDept }} 职务：{{ item.jbrJob }} 姓名：{{
                    item.jbrName
                  }}</a-descriptions-item
                >
                <a-descriptions-item label="经办人手机" :span="2"
                  ><a>{{ item.jbrPhone }}</a></a-descriptions-item
                >
              </a-descriptions>
              <template v-for="itemdf in item.dfList">
                <a-descriptions
                  :key="itemdf.dfId"
                  size="small"
                  bordered
                  :column="{ xxl: 8, xl: 8, lg: 8, md: 4, sm: 4, xs: 1 }"
                >
                  <a-descriptions-item label="已答复" :span="3">
                    {{ itemdf.dfrq }}
                  </a-descriptions-item>
                  <a-descriptions-item label="附件名称" :span="5"
                    ><a
                      v-if="itemdf.fujianList.length > 0"
                      @click="downFileDataDf(itemdf, 2)"
                      >【查看答复附件】</a
                    >
                  </a-descriptions-item>
                </a-descriptions>
              </template>
            </template>
          </div>
        </a-tab-pane>
        <a-tab-pane key="3" tab="提出代表">
          <div :style="tabStyle">
            <!-- v-if="
              ((record.status == 10 || record.status < 20) &&
                authority == 'YAJY_QRD') ||
              authority == 'YAJY_XLW' ||
              authority == 'YAJY_DBC'
            " -->
            <a-space
              v-if="
                userData.authorities
                  ? record.status < 90 &&
                    (userData.authorities[0].authority == 'YAJY_QRD' ||
                      userData.authorities[0].authority == 'YAJY_XLW' ||
                      userData.authorities[0].authority == 'YAJY_DBC' ||
                      userData.authorities[0].authority == 'I-XTGLY' ||
                      userData.authorities[0].authority == 'YAJY_DB')
                  : false
              "
              class="operator"
            >
              <a-button
                type="primary"
                icon="plus-circle"
                @click="showUserTable('提出')"
                >新建</a-button
              >
              <a-button icon="delete" @click="delJointlyList">删除</a-button>
            </a-space>
            <standard-table
              row-key="jointlyId"
              :columns="deputationColumns"
              :selected-rows.sync="deputationSelectedRows"
              :data-source="deputationDataSource"
              :show-alert="
                userData.authorities
                  ? record.status < 90 &&
                    (userData.authorities[0].authority == 'YAJY_QRD' ||
                      userData.authorities[0].authority == 'YAJY_XLW' ||
                      userData.authorities[0].authority == 'YAJY_DBC' ||
                      userData.authorities[0].authority == 'I-XTGLY' ||
                      userData.authorities[0].authority == 'YAJY_DB')
                  : false
              "
            >
            </standard-table>
          </div>
        </a-tab-pane>
        <a-tab-pane key="4" tab="承办单位">
          <div :style="tabStyle">
            <standard-table
              row-key="undertakeId"
              :columns="unitColumns"
              :data-source="unitDataSource"
            >
            </standard-table>
          </div>
        </a-tab-pane>
        <a-tab-pane key="5" tab="流程跟踪">
          <div :style="tabStyle">
            <standard-table
              :columns="processColumns"
              row-key="historyId"
              :data-source="processDataSource"
            >
            </standard-table>
          </div>
        </a-tab-pane>
      </a-tabs>
      <commonUnitShuttleTable
        ref="commonUnitShuttleTable"
        :default-unit="defaultUnit"
        :unitType="unitType"
        :disableSelectedRowKeys="disableSelectedRowKeys"
        @emitUnitTable="getUnitTable"
      ></commonUnitShuttleTable>
      <!-- 会办 -->
      <commonUnitShuttleTableHB
        ref="commonUnitShuttleTableHB"
        :default-unit="defaultUnitHB"
        :unitType="unitType"
        :disableSelectedRowKeys="disableSelectedRowKeysHB"
        @emitUnitTable="getUnitTableHB"
      ></commonUnitShuttleTableHB>
      <commonUserTable
        ref="commonUserTable"
        :disableSelectedRowKeys="disableSelectedRowKeylist"
        @emitJoinTable="getJoinTable"
      ></commonUserTable>
      <evaluationModal
        ref="evaluationModal"
        @emitClose="close"
      ></evaluationModal>
      <!-- 重新上传正文附件 -->
      <a-modal
        class="small"
        :visible="fileVisible"
        title="重新上传正文附件"
        @ok="resetSubmit"
        @cancel="closeFileModal"
      >
        <a-form-model>
          <a-form-model-item label="正文附件">
            <!-- 上传 -->
            <a-upload
              action=""
              accept=".docx,.doc"
              :remove="handleTextRemoveReset"
              :beforeUpload="beforeUploadReset"
              @change="uploadChangeReset($event, '9')"
              :fileList="fileTextListReset"
            >
              <a-button :disabled="fileTextListReset.length == 1" type="primary"
                >点击上传</a-button
              >
              <div>
                <!-- :hidden="fileTextListReset.length == 1" -->
                只能上传doc,docx文件，且不超过20mb
              </div>
            </a-upload>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <!-- 申请延期 -->
      <a-modal
        :body-style="{}"
        :visible="delayVisible"
        title="申请延期"
        width="60%"
        @ok="adjustSubmit"
        @cancel="cancelModal"
      >
        <a-form-model
          ref="delayForm"
          :model="delayForm"
          :rules="delayFormRules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-row>
            <a-col span="24">
              <a-form-model-item label="延期描述" prop="comment">
                <a-textarea v-model="delayForm.comment" :rows="5"></a-textarea>
              </a-form-model-item>
            </a-col>
            <a-col span="24">
              <a-form-model-item label="延期天数" prop="delayDay">
                <a-input-number
                  v-model="delayForm.delayDay"
                  :min="0"
                ></a-input-number>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-modal>

      <!-- 调整会办单位 弹出框 -->
      <a-modal
        :visible="unitMinorModalVisible"
        width="50%"
        title="调整会办单位"
        :body-style="{ height: '460px' }"
        ok-text="调整"
        @ok="saveUnitMinorModal"
        @cancel="cancelUnitMinorModal"
      >
        <div :style="tabStyle">
          <a-space class="operator">
            <a-button type="primary" @click="openChangeMinor">新增</a-button>
            <a-button icon="delete" @click="unitModalMinorVisibleDel"
            >删除</a-button
            >
          </a-space>
          <standard-table
            row-key="orgCode"
            :columns="unitModalVisibleColumns"
            :selected-rows.sync="unitModalVisibleSelectedRows"
            :data-source="unitModalVisibleDataSource"
            :pagination="false"
            :scroll="{ x: 600, y: 300 }"
          >
          </standard-table>
        </div>
      </a-modal>
      <!-- 会办单位调整弹出框 修改会办 -->
      <a-modal
        :visible="changeIfMainMinorVisible"
        :title="changeIfMainTitle"
        @ok="saveChangeIfMainMinor"
        @cancel="
              () => {
                changeIfMainMinorVisible = false;
              }
            "
      >
        <a-form>
          <a-form-item
            v-if="changeIfMainTitle == '添加会办单位'"
            label="选择单位"
          >
            <div class="searchStyle">
              <a-input v-model="changeIfMainForm.orgName" disabled>
              </a-input>
              <a-button
                type="primary"
                icon="search"
                @click="openUnitTable('单位调整')"
              >
              </a-button>
            </div>
          </a-form-item>
        </a-form>
      </a-modal>

      <adjustListEdit ref="adjustListEdit" @close="close"></adjustListEdit>
      <a-modal v-model="fkVisible" title="反馈内容" @ok="closeFkVisible">
        <a-descriptions>
          <a-descriptions-item label="反馈评价" :span="3">{{
            fkEvaluation
          }}</a-descriptions-item>
          <a-descriptions-item label="反馈内容" :span="3">{{
            fkContent
          }}</a-descriptions-item>
        </a-descriptions>
      </a-modal>
      <jbrTable
        ref="jbrTable"
        :org-code="jbrTableOrgCode"
        @emitJbrTable="getJbrTable"
      ></jbrTable>

      <a-modal
        :dialog-style="{ top: '-200px' }"
        :body-style="{ minHeight: '200px', overflowY: 'auto' }"
        :visible="iShow"
        title="办理意见详情"
        width="40%"
        :centered="true"
        :footer="null"
        @cancel="iShow = !iShow"
      >
        {{ replyComment }}
      </a-modal>
    </a-spin>
  </a-modal>
</template>
<script>
// 建议详情 显示详情
import commonUnitShuttleTable from "@/views/common/commonUnitShuttleTable.vue";

import commonUnitShuttleTableHB from "@/views/common/commonUnitShuttleTableHB.vue";
import evaluationModal from "@/views/meeting/suggesting/evaluationModal.vue";
import commonUserTable from "@/views/common/commonUserTable.vue";
import jbrTable from "@/views/common/jbrTable.vue";
import { fileUpload } from "@/api/commonApi/file.js";
import adjustListEdit from "@/views/meeting/suggesting/adjustListEdit.vue";
import {
  doMinorSave,
  doMinorReturn,
  proposalById,
  doAssign,
  doAudit,
  doCheck,
  getJointlyList,
  addJointly,
  delJointly,
  againProduce,
  getLocalHistory,
  getProposalUnder,
  doReply,
  getDelayUndertakeListById,
  doDelay,
  getReviseUndertakeListById,
  keepRevise,
  keepDelay,
  seeText,
  getUndertakeInfo,
  doRevise,
  getJbrList,
  updateJbr,
  getUndertakeDfAndFeedbackList,
  againUploadTextFile,
  downloadProposal,
  saveCBDW,
  doSignedSave,
  getUndertakeStatus,
  getUndertakeDfInfo,
  applyDelay,
  findOpinionOption,
} from "@/api/myJob/myProposal.js";

import moment from "moment";
import { downFile, getUserData_yajy } from "@/api/commonApi/file.js";
import {getAdjustMinorUndertakeListById} from "../../api/myJob/myProposal";
import {instance_yajy2} from "@/api/axiosRq";
export default {
  components: {
    commonUnitShuttleTable,
    commonUnitShuttleTableHB,
    commonUserTable,
    evaluationModal,
    adjustListEdit,
    jbrTable,
  },

  watch: {
    // visible(val) {
    //   if (val) {
    //     this.getOneUser();
    //   }
    // },
    "resourceForm.text": {
      handler: function (val) {
        let TEXT = null;
        this.resourceForm.text.map((i, n) => {
          TEXT ? (TEXT += n + 1 + "." + i + "  ") : (TEXT = "1." + i + "  ");
        });
        // this.resourceForm.comment = TEXT
        if (this.resourceForm.text.toString().indexOf("其他") != -1) {
          this.isOtheryj = true;
          // this.resourceForm.comment += '(其他原因：)'
        } else {
          this.isOtheryj = false;
        }
      },
    },
  },
  filters: {
    filterStatus(text) {
      if (text === 10) {
        return "草稿";
      }
      if (text === 11) {
        return "联名中";
      }
      if (text === 20) {
        return "校核中";
      }
      if (text === 30) {
        return "复审中";
      }
      if (text === 40) {
        return "交办中";
      }
      if (text === 50) {
        return "签收中";
      }
      if (text === 60) {
        return "答复中";
      }
      if (text === 70) {
        return "待反馈";
      }
      if (text === 80) {
        return "已反馈";
      }
      if (text === 90) {
        return "办毕";
      }
    },
    filterProposalContentType(text) {
      if (text == "JYFL01") return "法制";
      if (text == "JYFL02") return "监察和司法";
      if (text == "JYFL03") return "经济";
      if (text == "JYFL04") return "城建环资";
      if (text == "JYFL05") return "农村农业";
      if (text == "JYFL06") return "教科文卫";
      if (text == "JYFL07") return "华侨外事民族宗教";
      if (text == "JYFL08") return "其他";
      if (text == "JYFL09") return "预算";
      if (text == "JYFL10") return "社会建设";
    },
    filterProposalType(text) {
      if (text == "1") return "大会议案";
      if (text == "2") return "大会建议";
      if (text == "3") return "闭会建议";
      if (text == "4") return "供参考建议";
    },
  },
  data() {
    var hongzixinxibiao = (rule, value, callback) => {
      if (
        this.resourceForm.orgEvaluation == "" ||
        this.resourceForm.orgEvaluation == undefined
      ) {
        callback(new Error("请输入建议内容质量评分"));
      } else {
        callback();
      }
    };
    return {
      userName: null,
      OpinionOptionList: [
        "内容过于简单，请加强调研，了解问题产生的根本原因，并提出针对性强，可操作性高的建议对策。",
        "代表建议内容超出广州市权限，请联系全国/省人大代表向上一级人大提出建议。",
        "建议内容涉及信访、个人事项等不适宜作为代表建议的内容。",
        "代表本人要求退回修改。",
        "其他",
      ],
      isOtheryj: false,
      isPublicDisable: false,
      listLoading: false,
      lfToken:"",
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      tabStyle: { maxHeight: "600px", overflowY: "auto" }, // 对话框中的 tab 内容高度及垂直滚动条设置
      visible: false,
      proposalId: "", //父组件直接赋值 议题id
      fkVisible: false,
      fkEvaluation: "",
      fkContent: "",
      majorUndertakeDfList: [],
      minorUndertakeDfList: [],
      record: { unIfmainList: [], ifmainList: [] },
      iShow: false,
      replyComment: "",
      userData: {},
      resourceForm: {
        meetName: "",
        meet: "",
        text: [],
        host: "",
        hostName: "",
        adjust: "",
        adjustName: "",
        isPublic: "",
        dffs: [],
        jbrName: "",
        jbrDept: "",
        jbrJob: "",
        jbrPhone: "",
        jbrOph: "",
        dflb: null,
        orgCode: "",
        mainHandleLastTimeStr: "",
        minorHandleLastTimeStr: "",
        contentType: "",
      },
      delayFormRules: {
        comment: [
          { required: true, message: "请输入延期描述", trigger: "blur" },
        ],
        delayDay: [
          { required: true, message: "请输入延期天数", trigger: "blur" },
        ],
      },
      rules: {
        jbrName: [
          { required: true, message: "请输入经办人姓名", trigger: "blur" },
        ],
        jbrDept: [
          { required: true, message: "请输入经办人部门", trigger: "blur" },
        ],
        jbrJob: [
          { required: true, message: "请输入经办人职务", trigger: "blur" },
        ],
        jbrPhone: [
          { required: true, message: "请输入经办人手机", trigger: "blur" },
        ],
        jbrOph: [
          { required: true, message: "请输入经办人办公电话", trigger: "blur" },
        ],
        dflb: [
          { required: true, message: "请输入答复类型", trigger: "change" },
        ],
        dffs: [
          { required: true, message: "请输入沟通方式", trigger: "change" },
        ],
        isPublic: [
          { required: true, message: "请输入答复是否公开", trigger: "change" },
        ],
        orgEvaluation: [
          {
            required: true,
            message: "请输入建议内容质量评分",
            trigger: "blur",
            validator: hongzixinxibiao,
          },
        ],
      },
      unitColumns: [
        // 承办单位表格头定义
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "ifmain",
          customRender: (text, record, index) =>
            text == 1 ? "主办单位" : "会办单位",
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
      ],
      unitDataSource: [
        // 承办单位表格数据
      ],
      processColumns: [
        // 流程跟踪表格头定义
        {
          title: "状态",
          ellipsis: true,
          dataIndex: "replyType",
        },
        {
          title: "处理人",
          ellipsis: true,
          dataIndex: "reply_by_name",
        },
        {
          title: "处理时间",
          ellipsis: true,
          dataIndex: "replyTime",
        },
        {
          title: "办理意见",
          ellipsis: true,
          dataIndex: "replyComment",
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#1890ff",
                  },
                  on: {
                    click: () => {
                      this.clickRowData(record);
                    },
                  },
                },
                record.replyComment
              ),
            ]);
          },
        },
      ],
      processDataSource: [
        // 流程跟踪数据
      ],
      deputationColumns: [
        // 提出代表表格头定义
        {
          title: "类别",
          ellipsis: true,
          dataIndex: "ifpublic",
          customRender: (text, record, index) => {
            if (text == 1) return "领衔";
            if (text == 0) return "联名";
          },
        },
        {
          title: "代表姓名",
          ellipsis: true,
          dataIndex: "name",
        },
        {
          title: "通讯地址",
          ellipsis: true,
          dataIndex: "unitAddress",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "unitPhone",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "mobilePhone",
        },
        {
          title: "Email",
          ellipsis: true,
          dataIndex: "",
        },
        {
          title: "邮政编码",
          ellipsis: true,
          dataIndex: "",
        },
        {
          title: "申请时间",
          ellipsis: true,
          dataIndex: "createTime",
        },
        {
          title: "反馈时间",
          ellipsis: true,
          dataIndex: "agreen",
        },
        {
          title: "联名状态",
          ellipsis: true,
          dataIndex: "ifagreen",
          customRender: (text, record, index) => {
            if (text == 1) return "同意";
            if (text == 0) return "不同意";
            if (text == null) return "未处理";
          },
        },
      ],
      // 提出列表table
      deputationDataSource: [],
      // 提出列表选中数据
      deputationSelectedRows: [],
      unitType: "", //单位类型
      returnValue: false,
      tabsKey: "1",
      fileTextList: [], //答复附件
      perType: "", //打开代表列表的场景
      scenesStatus: "", // 场景默认是 处理延期
      dataSourcePostpone: [], //处理延期数据
      selectedRowsPostpone: [], //处理延期数据-已选
      columnsPostpone: [
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "",
          customRender: (text, record, index) => {
            return record.ifmain == 1 ? "主办单位" : "会办单位";
          },
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "申请时间",
          ellipsis: true,
          dataIndex: "applyDelayTime",
        },
        {
          title: "延期时长",
          ellipsis: true,
          dataIndex: "delayTimeTo",
        },
        {
          title: "调整原因",
          ellipsis: true,
          dataIndex: "applyCommnent",
        },
        {
          title: "附件名称",
          scopedSlots: { customRender: "action" },
        },
      ], //处理延期数据/单位调整 - 表头
      unitModalVisibleColumns: [
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "ifmain",
          customRender: (text, record, index) => {
            if (text == "1") return "主办单位";
            if (text == "0") return "会办单位";
          },
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
        {
          title: "签收状态",
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            if (text == 0) return "调整中";
            if (text == 1) return "签收中";
            if (text == 2) return "答复中";
            if (text == 3) return "待反馈";
          },
        },
      ], //弹出的单位调整列表
      undertakeId: "", //子流程id
      visiblePostpone: false,
      keepType: "",
      showOperate: true, //是否显示操作 默认显示
      modalTitle: "处理延期", //延期/调整弹出框
      unitModalVisible: false, //单位调整弹出框
      unitModalVisibleSelectedRows: [], //单位调整已选数据
      unitModalVisibleDataSource: [], //单位调整列表

      unitModalInfoVisible: false, //会办单位调整信息
      changeIfMainVisible: false, //单位调整弹出框 修改主办/会办
      fileVisible: false, //重新上传文件窗口开关
      fileTextListReset: [], //重新上传文件列表
      isFileReset: undefined,
      contentTypeList: [
        // 内容分类
        { id: "JYFL01", name: "法制" },
        { id: "JYFL02", name: "监察和司法" },
        { id: "JYFL09", name: "预算" },
        { id: "JYFL03", name: "经济" },
        { id: "JYFL04", name: "城建环资" },
        { id: "JYFL05", name: "农村农业" },
        { id: "JYFL06", name: "教科文卫" },
        { id: "JYFL07", name: "华侨外事民族宗教" },
        { id: "JYFL10", name: "社会建设" },
        { id: "JYFL08", name: "其他" },
      ],
      jointlyId: "", //联名id
      undertakeStatus: false, //显示反馈的依据，获取子流程状态
      delayVisible: false,
      hbDataSource: [], //会办数据
      minorLastTime: "",
      majorLastTime: "",
      fileTextList_7: [],
      fileTextList_8: [],
      delayForm: { delayDay: "", comment: "" },
      defaultUnit: [],
      disableSelectedRowKeys: [],

      defaultUnitHB: [],
      disableSelectedRowKeysHB: [],
      // 申请调整
      changeIfMainTitle: "修改承办类别",
      changeIfMainForm: {},
      // 联名按钮loading
      lmLoading: false,
      // 交办按钮loading
      jbLoading: false,
      // 校核按钮loading
      jhLoading: false,
      // 审核按钮loading
      shLoading: false,
      // 答复按钮loading
      sfLoading: false,
      // 经办人orgCode
      jbrTableOrgCode: "",
      zwCKyj: false, //转为参考意见的转圈

      disableSelectedRowKeylist: [], //禁止添加的用户（已添加）
    };
  },
  created() {
    // this.getOpinionOption();
    if (sessionStorage.getItem("USERID")) {
      this.userName = JSON.parse(sessionStorage.getItem("USERID")).username;
    }
  },
  methods: {
    // 选择意见
    changeTEXT(val) {
      // let a = val.toString().indexOf('审核通过');
      // let b = val.toString().indexOf('审核不通过')
      // if (a > -1 && b > -1) {
      //   if (a > b) {
      //     this.resourceForm.text = this.resourceForm.text.filter((i) => { return i != '审核不通过' })
      //     return
      //   } else {
      //     this.resourceForm.text = this.resourceForm.text.filter((i) => { return i != '审核通过' })
      //     return
      //   }
      // }
    },
    // 获取多选的意见类型
    getOpinionOption() {
      findOpinionOption().then((res) => {
        if (res.data.code == "200") {
          let aa = res.data.data.split('"');
          for (let i = 0; i < aa.length; i++) {
            if (i % 2 != 0) {
              this.OpinionOptionList.push(aa[i]);
            }
          }
        }
      });
    },
    // 点击行
    clickRowData(record) {
      if (record.replyComment) {
        this.replyComment = record.replyComment;
        this.iShow = true;
      }
    },
    // 校核弹窗 关闭
    cancelReturn() {
      this.returnValue = false;
      this.resourceForm.comment = "";
    },
    // 正式提交
    formallySubmit() {
      this.$router.push({
        path: "/meSuggestv/addSuggestv",
        query: {
          showUser: false,
          proposalId: this.proposalId,
        },
      });
    },
    // 取消调整
    cancelUnitModal() {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "如果关闭当前页面，将会丢失已编辑的承办单位，确认要关闭?",
        onOk: () => {
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    //关闭查看会办单位调整信息框
    cancelUnitInfoModal() {
      this.unitModalInfoVisible = false;
      this.unitModalVisibleSelectedRows = [];
      this.unitModalVisibleDataSource = [];
    },
    // 取消会办单位调整
    cancelUnitMinorModal() {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "如果关闭当前页面，将会丢失已编辑的会办单位，确认要关闭?",
        onOk: () => {
          this.unitMinorModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    //调整会办单位
    adjustMinor(){
      let sizeData = 0;
      let huiData = 0;
      console.log("-------------",this.unitModalVisibleDataSource);
      this.unitModalVisibleDataSource.forEach((item) => {
        if (item.ifmain == 1) {
          sizeData++;
        } else {
          huiData++;
        }
      });
      console.log("🤗🤗🤗, sizeData =>", sizeData)
      console.log("🤗🤗🤗, huiData =>", huiData)

      if (sizeData >= 2) {
        return this.$message.info("该建议存在多个主办无法进行会办单位调整！");
      }
      this.unitMinorModalVisible = true;
      getUndertakeInfo(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitModalVisibleDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 申请调整
    adjust() {
      this.$refs.adjustListEdit.modalVisible = true;
      this.$refs.adjustListEdit.proposalId = this.proposalId;
    },
    // 申请延期 保存
    adjustSubmit() {
      let form = {};
      form.comment = this.delayForm.comment;
      form.delayDay = this.delayForm.delayDay;
      form.proposalId = this.proposalId;
      this.$refs["delayForm"].validate((valid) => {
        if (valid) {
          // 申请延期
          applyDelay(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.cancelModal();
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 关闭窗口
    cancelModal() {
      this.delayVisible = false;
      this.delayForm = { delayDay: "", comment: "" };
      this.delayFileTextList = [];
    },
    // 申请延期
    postpone() {
      this.delayVisible = true;
    },
    // 处理联名
    changeJoin(status) {
      this.lmLoading = true;
      let form = {};
      form.jointlyid = this.jointlyId;
      form.state = status;
      doSignedSave(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作完成");
          this.close();
        } else {
          this.$message.error(res.data.data);
        }
        this.lmLoading = false;
      });
    },
    // 下载建议纸
    downMotion() {
      let downFile = (res) => {
        let a = window.document.createElement("a");
        let resData = URL.createObjectURL(res.data);
        a.href = resData;
        let proposalNum = this.record.proposalNum
          ? this.record.proposalNum
          : "";
        let fileName = proposalNum + this.record.proposalTitle + "建议纸.doc";
        a.download = fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(resData);
      };
      downloadProposal({ id: this.proposalId, type: 10 }).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        let errmsg = "";
        if (res.headers.errmsg) {
          errmsg = this.$str2utf8(res.headers.errmsg);
          return this.$message.error(errmsg);
        } else {
          downFile(res);
        }
      });
    },

    //重新上传正文附件 关闭
    closeFileModal() {
      this.fileVisible = false;
      this.fileTextListReset = [];
      this.delayFileTextList = [];
      this.resourceForm = {
        meetName: "",
        meet: "",
        host: "",
        hostName: "",
        adjust: "",
        adjustName: "",
        isPublic: "",
        dffs: [],
        jbrName: "",
        jbrDept: "",
        jbrJob: "",
        jbrPhone: "",
        jbrOph: "",
      };
      this.fileTextList = [];
      this.fileTextList_7 = [];
      this.fileTextList_8 = [];
    },
    //重新上传正文附件 保存
    resetSubmit() {
      let form = {};
      form.proposalId = this.proposalId;
      form.type = "9";
      form.encrypted = false;
      form.file = this.resourceForm.file;
      againUploadTextFile(form).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.$message.success("操作完成");
          this.closeFileModal();
        } else {
          this.$message.error(res.data.data);
        }
      });
    },
    //重新上传正文附件 删除
    handleTextRemoveReset() {
      this.resourceForm.file = "";
      this.fileTextListReset = [];
      this.$baseMessage(`删除成功`, "success");
    },

    //重新上传正文附件 上传前
    beforeUploadReset(file) {
      console.log(file, "file");
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /^.+(docx|doc)$/;
      const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      if (!isDoc) {
        this.$message.error("上传的文件格式只能是doc或docx");
      }
      if (isLt20M && isDoc) {
        this.isFileReset = true;
        return false;
      } else {
        this.isFileReset = false;
        return true;
      }
    },
    //重新上传正文附件 上传操作
    uploadChangeReset(val, typeNum) {
      if (this.isFileReset) {
        // let formData = new FormData();
        // formData.append("file", val.file);
        // formData.append("type", typeNum);
        this.resourceForm.file = "";
        this.resourceForm.file = val.file;
        this.fileTextListReset = val.fileList;
      }
    },
    // 重新上传正文附件
    resetFile() {
      // 打开上传文件窗口
      this.fileVisible = true;
    },
    // 重新交办
    resetManage() {
      if (this.unitModalVisibleDataSource.length == 0) {
        return this.$message.error("承办单位不能为空");
      }
      let form = {};
      let undertakeList = [];
      this.unitModalVisibleDataSource.map((item) => {
        undertakeList.push({
          ifmain: item.ifmain,
          orgCode: item.orgCode,
          orgName: item.orgName,
          undertakeId: item.undertakeId || "",
          proposalId: this.proposalId,
        });
      });
      form.undertakeList = undertakeList;
      form.proposalId = this.proposalId;
      form.contentType = this.record.proposalContentType;
      form.proposalType = this.record.proposalType;
      doRevise(undertakeList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 单位调整弹出框 新增 打开
    openChange() {
      this.changeIfMainVisible = true;
      this.changeIfMainTitle = "添加承办单位";
    },
    // 单位调整弹出框 删除
    unitModalVisibleDel() {
      if (this.unitModalVisibleSelectedRows.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.unitModalVisibleSelectedRows.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      // 单位调整-删除逻辑
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定删除？",
        onOk: () => {
          this.unitModalVisibleDataSource =
            this.unitModalVisibleDataSource.filter(
              (item) =>
                item.orgCode != this.unitModalVisibleSelectedRows[0].orgCode
            );
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 单位调整弹出框 修改主办/会办 保存
    saveChangeIfMain() {
      if (this.changeIfMainTitle == "修改承办类别") {
        this.unitModalVisibleSelectedRows.forEach((item) => {
          this.unitModalVisibleDataSource.map((v) => {
            if (v.orgCode == item.orgCode) {
              v.ifmain = this.changeIfMainForm.ifmain;
            }
          });
        });

        this.changeIfMainVisible = false;
        this.$forceUpdate();
        delete this.changeIfMainForm.ifmain;
        this.unitModalVisibleSelectedRows = [];
      } else {
        // 新增
        this.changeIfMainForm.rows.forEach((item) => {
          item.ifmain = this.changeIfMainForm.ifmain;
          item.orgName = item.orgName;
          item.jbrName = item.orgDispname;
          item.jbrPhone = item.orgDispphone;
          item.jbrOph = item.orgDisptelephone;
        });
        let orgCodeList = this.unitModalVisibleDataSource.map(
          (item) => item.orgCode
        );
        let rowsOrgCode = this.changeIfMainForm.rows.map(
          (item) => item.orgCode
        );
        for (let index = 0; index < rowsOrgCode.length; index++) {
          const element = rowsOrgCode[index];
          if (orgCodeList.includes(element)) {
            return this.$message.error("单位已存在！");
          }
        }

        this.unitModalVisibleDataSource =
          this.unitModalVisibleDataSource.concat(this.changeIfMainForm.rows);
        this.changeIfMainForm = {};
        this.changeIfMainVisible = false;
      }
    },
    // 单位调整弹出框 修改主办/会办
    changeIfMain() {
      if (this.unitModalVisibleSelectedRows.length == 0) {
        return this.$message.error("请选择数据");
      }
      this.changeIfMainVisible = true;
      this.changeIfMainTitle = "修改承办类别";
    },
    // 单位调整弹出框 确定保存
    saveUnitModal() {
      // +判断
      let sizeData = 0;
      let huiData = 0;
      this.unitModalVisibleDataSource.forEach((item) => {
        if (item.ifmain == 1) {
          sizeData++;
        } else {
          huiData++;
        }
      });
      console.log("🤗🤗🤗, sizeData =>", sizeData);
      if (this.modalDataSource && this.modalDataSource.length == 0) {
        return this.$message.info("请添加数据");
      }
      // if (sizeData > 2) {
      //   return this.$message.info("主办单位最多只能俩个！");
      // }
      if (sizeData >= 2 && huiData > 0) {
        return this.$message.info("该建议存在多主办单位请删除其他会办单位！");
      }
      if (sizeData == 0) {
        return this.$message.info("请选择主办单位！");
      }
      let form = {};
      let undertakeList = [];
      this.unitModalVisibleDataSource.map((item) => {
        undertakeList.push({
          ifmain: item.ifmain,
          orgCode: item.orgCode,
          orgName: item.orgName,
          undertakeId: item.undertakeId || "",
          proposalId: this.proposalId,
        });
      });
      form.undertakeList = undertakeList;
      form.proposalId = this.proposalId;
      form.contentType = this.record.proposalContentType;
      form.proposalType = this.record.proposalType;
      doRevise(undertakeList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 会办单位调整审核通过
    saveUnitMinor() {
      let form = {};
      form.proposalId = this.proposalId;
      form.undertakeId = this.selectedRowsPostpone[0].undertakeId;
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      console.log("form.undertakeId",form.undertakeId);

      doMinorSave(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 会办单位调整审核退回
    returnUnitMinor() {
      let form = {};
      form.proposalId = this.proposalId;
      form.undertakeId = this.selectedRowsPostpone[0].undertakeId;
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      console.log("form.undertakeId",form.undertakeId);

      doMinorReturn(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 调整会办单位弹出框 删除
    unitModalMinorVisibleDel() {
      if (this.unitModalVisibleSelectedRows.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.unitModalVisibleSelectedRows.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      // 单位调整-删除逻辑
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定删除？",
        onOk: () => {
          this.unitModalVisibleDataSource =
            this.unitModalVisibleDataSource.filter(
              (item) =>
                item.orgCode != this.unitModalVisibleSelectedRows[0].orgCode
            );
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },

    // 调整会办单位弹出框 修改会办 保存
    saveChangeIfMainMinor() {
      // 新增
      this.changeIfMainForm.rows.forEach((item) => {
        item.ifmain = 0;
        item.orgName = item.orgName;
        item.jbrName = item.orgDispname;
        item.jbrPhone = item.orgDispphone;
        item.jbrOph = item.orgDisptelephone;
      });
      let orgCodeList = this.unitModalVisibleDataSource.map(
        (item) => item.orgCode
      );
      let rowsOrgCode = this.changeIfMainForm.rows.map(
        (item) => item.orgCode
      );
      for (let index = 0; index < rowsOrgCode.length; index++) {
        const element = rowsOrgCode[index];
        if (orgCodeList.includes(element)) {
          return this.$message.error("单位已存在！");
        }
      }

      this.unitModalVisibleDataSource =
        this.unitModalVisibleDataSource.concat(this.changeIfMainForm.rows);
      this.changeIfMainForm = {};
      this.changeIfMainMinorVisible = false;

    },
    // 调整会办单位弹出框 确定保存
    saveUnitMinorModal() {
      // +判断
      let sizeData = 0;
      let huiData = 0;
      this.unitModalVisibleDataSource.forEach((item) => {
        if (item.ifmain == 1) {
          sizeData++;
        } else {
          huiData++;
        }
      });
      console.log("🤗🤗🤗, sizeData =>", sizeData);
      if (this.modalDataSource && this.modalDataSource.length == 0) {
        return this.$message.info("请添加数据");
      }
      // if (sizeData > 2) {
      //   return this.$message.info("主办单位最多只能俩个！");
      // }
      if (sizeData >= 2 && huiData > 0) {
        return this.$message.info("该建议存在多主办单位请删除其他会办单位！");
      }
      if (sizeData == 0) {
        return this.$message.info("请选择主办单位！");
      }
      let form = {};
      let undertakeList = [];
      this.unitModalVisibleDataSource.map((item) => {
        undertakeList.push({
          ifmain: item.ifmain,
          orgCode: item.orgCode,
          orgName: item.orgName,
          undertakeId: item.undertakeId || "",
          proposalId: this.proposalId,
        });
      });
      form.undertakeList = undertakeList;
      form.proposalId = this.proposalId;
      form.contentType = this.record.proposalContentType;
      form.proposalType = this.record.proposalType;
      saveMinor(undertakeList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitMinorModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 下载
    downFileDataDf(itemdf, type) {
      let form = {};
      let attName = "";
      let attSuffix = "";
      if (type === 6) {
        form.relId = itemdf.undertakeId || itemdf.attId; //_______attId
        form.type = type; //应该是6待测试
        attName = itemdf.attName;
        attSuffix = itemdf.attSuffix;
      } else {
        itemdf.fujianList.forEach((item) => {
          if (item.attType === type) {
            form.relId = item.relId;
            form.type = item.attType;
            attName = item.attName;
            attSuffix = item.attSuffix;
          }
        });
      }
      console.log("🤗🤗🤗, form =>", form);
      downFile(form).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `${attName}.${attSuffix}`;

        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 下载附件
    downFileData(type, relId) {
      let form = { relIdList: [] };
      if (type == 2) {
        form.relId = relId;
      } else {
        form.relId = this.proposalId;
      }
      let name = "";
      if (type == "1") {
        name = "附件.zip";
      }
      if (type == "2") {
        name = "答复附件.pdf";
      }
      if (type == "9") {
        name = "正文附件.docx";
      }
      form.type = type;
      console.log("🤗🤗🤗, form =>", form);
      downFile(form).then((res) => {
        console.log(Object.getOwnPropertyNames(res.headers));
        console.log(
          "🤗🤗🤗, res =>",
          res,
          "headers",
          res.headers,
          res.headers?.["content-disposition"]
        );
        // let filename = res.headers?.['content-disposition']?.split(';')[1].split("filename=")[1];
        // if (type == "1" && filename) {
        //   let hz = filename?.substr(filename.lastIndexOf('.') + 1);
        //   name = "附件." + hz;
        // }
        if (type == "1") {
          let fjlis = this.record.attachmentList.filter((i) => i.attType == 1);
          console.log(fjlis, "fjlis");
          if (fjlis.length == 1) {
            name = "附件." + fjlis[0].attSuffix;
          }
        }

        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `${this.record.proposalTitle}${name}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 查看正文
    lookWord() {
      seeText(this.proposalId).then((res) => {
        if (res.data.code == 200 && res.data.data) {
          let url = this.GLOBAL.basePath_3 + "/" + res.data.data.url;
          window.open(url);
        } else {
          this.$message.error("请求出错!");
        }
      });
    },
    // 打开反馈
    openEvaluationModal() {
      this.$refs.evaluationModal.proposalId = this.proposalId;
      this.$refs.evaluationModal.getFeedUndertakeList();
      this.$refs.evaluationModal.modalVisible = true;
    },

    // 打开单位调整弹出框
    openUnitModalVisible() {
      this.unitModalVisible = true;
      getUndertakeInfo(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitModalVisibleDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    openUnitModalInfoVisible() {
      this.unitModalInfoVisible = true;
      getUndertakeInfo(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitModalVisibleDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 维持原交办
    maintainAPrimaryOffice() {
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.visiblePostpone = true;
      this.modalTitle = "维持原交办";
    },
    // 处理延期/调整/维持原交办 -弹窗保存
    submitDelay() {
      let form = {};
      form.comment = this.resourceForm.comment || "";
      form.undertakeId = this.selectedRowsPostpone[0].undertakeId;
      if (this.modalTitle == "处理延期") {
        form.delayDay = this.resourceForm.delayDay;
        doDelay(form).then((res) => {
          if (res.data.code == 200) {
            this.$message.success("操作成功");
            this.resourceForm.comment = "";
            this.getASubflow();
            this.visiblePostpone = false;
          } else {
            this.$message.error(res.data.message);
          }
        });
      } else if (this.modalTitle == "维持原交办") {
        if (this.keepType == "revise") {
          keepRevise(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.resourceForm.comment = "";
              this.getAdjust();
              this.visiblePostpone = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        }
        if (this.keepType == "delay") {
          keepDelay(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.getASubflow();
              this.visiblePostpone = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        }
      }
      this.$nextTick(() => {
        this.visible = false;
      });
    },
    // 处理延期-事件
    treatmentDelay() {
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.visiblePostpone = true;
      this.modalTitle = "处理延期";
      // this.resourceForm.delayDay = "";
      // this.resourceForm.delayDay = Number(
      //   this.selectedRowsPostpone[0].delayTimeTo
      // );
      this.$set(
        this.resourceForm,
        "delayDay",
        this.selectedRowsPostpone[0].delayTimeTo
      );
    },
    // 处理延期-获取子流程 父组件调用
    getASubflow() {
      getDelayUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "delay";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 处理调整-获取子流程 父组件调用
    getAdjust() {
      getReviseUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "revise";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 处理调整-获取子流程 父组件调用
    getAdjustMinor() {
      console.log("------------进来这里")
      console.log("scenesStatus---",this.scenesStatus)
      getAdjustMinorUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "reviseMinor";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 上传之前
    beforeUpload(file, fileList) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10M!");
      }
      // var reg = /^.+(docx|doc)$/;
      // const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      // if (!isDoc) {
      //   this.$message.error("上传的文件格式只能是doc或docx");
      //   return;
      // }
      return false;
    },
    // 答复 上传删除
    handleTextRemove(file) {
      this.resourceForm.fuJianIds = [];
      this.fileTextList = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 无需见面沟通证据 上传删除
    handleTextRemove7(file) {
      this.resourceForm.forumFile = [];
      this.fileTextList_7 = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 座谈会调研 上传删除
    handleTextRemove8(file) {
      this.resourceForm.communFile = [];
      this.fileTextList_8 = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 上传操作
    uploadChange(val, type) {
      // 1、议案附件2、回复的附件3、代表大会议附件
      // 4、常委会议附件5、市政府(两院)宙议附件6
      // 承单位退回附件7、代表无需沟通证据8、座
      // 谈会证据9、建议正文10、建议纸
      if (val.file.status == "removed") return;
      let formData = new FormData();
      formData.append("file", val.file);
      formData.append("type", type);
      fileUpload(formData).then((res) => {
        if (type == "2") {
          // 附件
          this.resourceForm.fuJianIds = [];
          this.resourceForm.fuJianIds.push(res.data.data[0].attId);
          this.fileTextList = val.fileList;
        } else if (type == "7") {
          this.resourceForm.forumFile = [];
          this.resourceForm.forumFile.push(res.data.data[0].attId);
          this.fileTextList_7 = val.fileList;
        } else if (type == "8") {
          this.resourceForm.communFile = [];
          this.resourceForm.communFile.push(res.data.data[0].attId);
          this.fileTextList_8 = val.fileList;
        }
      });
    },
    changeorgEvaluation(e) {
      this.resourceForm.orgEvaluation = e;
      this.$forceUpdate();
    },
    // 答复
    replyData(type, isXLW) {
      if (!this.fileTextList || this.fileTextList.length == 0) {
        return this.$message.error("请上传答复附件！");
      }
      if (
        (this.record.ifMain == 1 || isXLW) &&
        this.resourceForm.dffs.includes("3") &&
        (!this.fileTextList_7 || this.fileTextList_7.length == 0)
      ) {
        return this.$message.error("请上传代表无需沟通证据！");
      }
      if (!this.resourceForm.dffs.includes("3")) {
        this.resourceForm.forumFile = [];
        this.fileTextList_7 = [];
      }
      if (!this.resourceForm.dffs.includes("2")) {
        this.resourceForm.communFile = [];
        this.fileTextList_8 = [];
      }
      let forumFile = "";
      let communFile = "";
      this.$refs["resourceForm"].validate((valid) => {
        if (valid) {
          let jbrData = {};
          jbrData.jbrName = this.resourceForm.jbrName;
          jbrData.jbrDept = this.resourceForm.jbrDept;
          jbrData.jbrJob = this.resourceForm.jbrJob;
          jbrData.jbrOph = this.resourceForm.jbrOph;
          jbrData.jbrPhone = this.resourceForm.jbrPhone;
          jbrData.proposalId = this.proposalId;

          //先更新经办人信息
          updateJbr(jbrData).then((res) => {
            this.sfLoading = false;
            if (res.data.code != 200) {
              this.$message.error(res.data.message);
              return false;
            }
          });

          if (
            this.resourceForm.forumFile &&
            this.resourceForm.forumFile.length > 0
          ) {
            forumFile = this.resourceForm.forumFile.toString();
          }
          if (
            this.resourceForm.communFile &&
            this.resourceForm.communFile.length > 0
          ) {
            communFile = this.resourceForm.communFile.toString();
          }
          if (this.resourceForm.orgEvaluation > 10) {
            this.$message.error("建议内容质量评分分数不在评分范围内,请修改！");
            return;
          }
          let form = {};
          // form.dfr = this.userData.username;
          // form.dfrId = this.userData.userId;
          form.actType = type;
          form.dflb = this.resourceForm.dflb;
          form.dffs = this.resourceForm.dffs.toString();
          form.isPublic = this.resourceForm.isPublic;
          form.remark = this.resourceForm.remark;
          form.xtpfList = this.hbDataSource;
          form.dfId = this.resourceForm.dfId;
          form.orgEvaluation = this.resourceForm.orgEvaluation; //建议内容质量评分
          if (
            this.resourceForm.fuJianIds != "" &&
            this.resourceForm.fuJianIds != undefined
          ) {
            form.fuJianIds = this.resourceForm.fuJianIds.toString();
          }
          if (communFile != "" && communFile != undefined) {
            form.fuJianIds += "," + communFile;
          }
          if (forumFile != "" && forumFile != undefined) {
            form.fuJianIds += "," + forumFile;
          }
          this.sfLoading = true;
          doReply(form, this.proposalId).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.visible = false;
              this.fileTextList = [];
              this.close();
              this.sfLoading = false;
            } else {
              this.$message.error(res.data.message);
              this.sfLoading = false;
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 重新生成正文
    // regenerateText() {
    //   againProduce(this.proposalId).then((res) => {
    //     console.log("重新生成正文🤗🤗🤗, res =>", res);
    //     if (res.data.code == 200) {
    //       this.$message.success(res.data.message);
    //     } else {
    //       this.$message.error(res.data.message);
    //     }
    //   });
    // },
    // 删除提出代表
    delJointlyList() {
      if (this.deputationSelectedRows.length == 0) {
        return this.$message.error("请选择数据！");
      }
      let jointlyIdsList = [];
      var allifpublic = false; //是否为领衔代表
      this.deputationSelectedRows.map((item) => {
        if (item.ifpublic != "1") {
          jointlyIdsList.push({ jointlyIds: item.jointlyId });
        } else {
          allifpublic = true;
        }
      });

      delJointly(jointlyIdsList).then((res) => {
        if (res.data.code == 200) {
          if (allifpublic) {
            this.$message.success("领衔代表不能被删除");
          } else {
            this.$message.success(res.data.message);
          }

          this.deputationSelectedRows = [];
          this.getJointlyListData();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 接受提出代表
    getJoinTable(rows) {
      let alreadyDBli = []; //找出已添加用户数组
      let addlist = []; //可添加的代表
      let list = this.deputationDataSource.map((i) => i.operId);
      alreadyDBli = rows.filter((item) => list.includes(item.userId));
      addlist = rows.filter((item) => !list.includes(item.userId));
      // console.log(
      //   alreadyDBli,
      //   "alreadyDBli",
      //   list,
      //   "1",
      //   this.deputationDataSource
      // );
      // console.log(rows, "rows");
      // console.log(addlist, "addlist");
      // 判断当前选择人的类型
      if (this.perType == "提出") {
        let form = {
          operids: addlist.map((item) => item.userId).toString(),
          proposalId: this.proposalId,
        };
        // 新增
        addJointly(form).then((res) => {
          if (res.data.code == 200) {
            if (alreadyDBli.length != 0) {
              // 提示已经添加不可添加
              let nameli = alreadyDBli.map((i) => i.name);
              this.$message.success(`${nameli} 代表已经添加过了`);
            } else {
              this.$message.success(res.data.message);
            }
            // this.$message.success(res.data.message);
            this.getJointlyListData();
            this.disableSelectedRowKeylist = [];
          } else {
            this.$message.error(res.data.message);
          }
        });
      } else {
        console.log("🤗🤗🤗 接受提出代表-答复, rows =>", rows);
        this.resourceForm = Object.assign({}, this.resourceForm, rows[0]);
      }
    },
    // 打开提出代表进行新增/答复选择经办人
    showUserTable(type) {
      this.perType = type;
      // this.disableSelectedRowKeylist = this.deputationDataSource.map((i) => i.userId);
      this.$refs.commonUserTable.jointTableVisible = true;
    },
    // 打开选择经办人
    showJbrTable(form) {
      this.$refs.jbrTable.jbrTableVisible = true;
      // this.$refs.jbrTable.queryForm.jbrName = "";
      // this.$refs.jbrTable.queryForm.jbrPhone = "";
      this.jbrTableOrgCode = form.orgCode;
      // this.$refs.jbrTable.showDesc();
    },
    // 接受提出代表
    getJbrTable(row) {
      this.resourceForm.jbrName = row.jbrName;
      this.resourceForm.jbrDept = row.jbrDept;
      this.resourceForm.jbrJob = row.jbrJob;
      this.resourceForm.jbrOph = row.jbrOph;
      this.resourceForm.jbrPhone = row.jbrPhone;
    },
    // tab点击
    changeTab(key) {
      // 获取提出代表
      // if (key == "3") {
      // this.getJointlyListData();
      // }
      // 流程跟踪
      // if (key == "5") {
      // this.getLocalHistoryData();
      // }
      // 获取议案建议的承办单位
      // if (key == "4") {
      // this.getProposalUndertakesData();
      // }
      // 获取反馈答复列表
      // if (key == "2") {
      // this.feedbackReply();
      // }
    },
    // 获取反馈答复列表、
    feedbackReply() {
      getUndertakeDfAndFeedbackList(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitDataSource = [];
          this.majorLastTime = "";
          this.minorLastTime = "";
          this.majorUndertakeDfList = [];
          this.minorUndertakeDfList = [];

          if (res.data.data != undefined && res.data.data.length > 0) {
            res.data.data.forEach((item) => {
              this.unitDataSource.push(item);
              // 主办单位
              if (item.ifmain == 1) {
                // 答复反馈tab 显示答复方式
                if (item.dfList) {
                  this.record.dffs = item.dfList[0].dffs;
                }
                this.majorLastTime = item.handleLastTime;
                this.majorUndertakeDfList.push(item);
              } else {
                if (
                  this.minorLastTime == "" ||
                  (this.minorLastTime != "" &&
                    moment(this.minorLastTime).valueOf() <
                      moment(item.handleLastTime).valueOf())
                ) {
                  this.minorLastTime = item.handleLastTime;
                }
                this.minorUndertakeDfList.push(item);
              }
            });
          }
        } else {
        }
        this.$forceUpdate();
      });
    },
    // 获取办理跟踪列表
    getLocalHistoryData() {
      getLocalHistory(this.proposalId).then((res) => {
        // console.log("获取办理跟踪列表🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.processDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 获取提出代表
    getJointlyListData() {
      getJointlyList(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.deputationDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 提交校核 打开
    openModal(status) {
      this.returnValue = true;
      if (status == "pass") {
        this.returnValueTitle = "校核意见";
      } else {
        this.returnValueTitle = "退回意见";
      }
    },
    // 提交校核
    submitTheSubject() {
      let form = {};
      form.proposalIds = this.proposalId;
      if (this.returnValueTitle == "退回意见") {
        form.actType = "return";
      } else {
        form.actType = "pass";
      }
      form.comment = this.resourceForm.comment || "";
      //提交校核 通过
      let content = "";
      if (this.returnValueTitle == "校核意见") {
        content = "是否确定校核通过？";
      } else {
        content = "是否确定退回？";
      }
      //提交校核 退回
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: content,
        onOk: () => {
          this.jhLoading = true;
          doCheck(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment: "",
              };
              this.returnValue = false;
              this.jhLoading = false;
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 提交审核
    submitReview(status) {
      this.resourceForm.proposalIds = this.proposalId;
      this.resourceForm.actType = status;
      let TEXT = null;
      let isQT = false;
      let m = 1;
      console.log(this.resourceForm.text);
      if (this.resourceForm.text.length > 0) {
        console.log(this.resourceForm.text);

        this.resourceForm.text.map((i, n) => {
          if (i != "其他") {
            TEXT ? (TEXT += m + "." + i + "  ") : (TEXT = "1." + i + "  ");
            m++;
          }
          if (i == "其他") {
            isQT = true;
          }
        });
      }

      this.resourceForm.comment =
        isQT && this.resourceForm.qt
          ? TEXT + m + ". 其他 原因：" + this.resourceForm.qt
          : isQT
          ? TEXT + m + ". 其他"
          : TEXT;
      console.log(this.resourceForm, "this.resourceForm");
      let tips = "pass" == status ? "是否确定审核通过？" : "是否确定退回？";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: tips,
        onOk: () => {
          this.shLoading = true;
          let dataForm = { ...this.resourceForm };
          delete dataForm.text;
          doAudit(dataForm).then((res) => {
            console.log("🤗🤗🤗, res =>", res);
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                text: [],
              };
              this.shLoading = false;
              // 选联委 审核后显示交办
              // if (this.userData.authorities[0].authority == "YAJY_XLW") {
              //   this.showDesc();
              // } else {
              // this.close();
              // }
              this.close(); //11-1 改
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          // this.$message.info("您已取消操作！");
        },
      });
    },
    // 关闭窗口
    close() {
      this.$emit("parentFetchData");
      this.visible = false;
      this.showOperate = true;
      for (const key in this.$data) {
        if (!Array.isArray(this.$data[key])) {
          this[key] = this.$options.data.call(this)[key];
        }
      }
      this.resourceForm.host = [];
      this.resourceForm.meet = [];
      this.resourceForm.mainHandleLastTimeStr = "";
      (this.resourceForm.minorHandleLastTimeStr = ""),
        (this.resourceForm = {
          meetName: "",
          meet: "",
          text: [],
          host: "",
          hostName: "",
          adjust: "",
          adjustName: "",
          isPublic: "",
          dffs: [],
          jbrName: "",
          jbrDept: "",
          jbrJob: "",
          jbrPhone: "",
          jbrOph: "",
          dflb: null,
          orgCode: "",
          mainHandleLastTimeStr: "",
          minorHandleLastTimeStr: "",
          contentType: "",
        });
      this.$refs.adjustListEdit.modalVisible = false;
      this.$refs.evaluationModal.modalVisible = false;
      this.fileTextList_8 = [];
      this.fileTextList = [];
      this.fileTextList_7 = [];
      this.minorLastTime = "";
    },
    // 退回
    back() {},
    // 交办-提交交办
    manage() {
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
      if (this.resourceForm.hostName == this.resourceForm.meetName) {
        return this.$message.error("主办单位和会办单位不能相同");
      }
      console.log("🤗🤗🤗, this.resourceForm =>", this.resourceForm);
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定交办？",
        onOk: () => {
          let hostData = [];
          let meetData = [];
          this.resourceForm.host.map((item) => {
            hostData.push({
              ifmain: 1,
              orgCode: item.orgCode,
              orgName: item.orgName,
              proposalId: this.proposalId,
            });
          });
          if (this.resourceForm.meet.length > 0) {
            this.resourceForm.meet.map((item) => {
              meetData.push({
                ifmain: 0,
                orgCode: item.orgCode,
                orgName: item.orgName,
                proposalId: this.proposalId,
              });
            });
          }
          let undertakeList = [];
          undertakeList = hostData.concat(meetData);
          let newForm = {};
          newForm.undertakeList = undertakeList;
          newForm.proposalId = this.proposalId;
          newForm.contentType = this.resourceForm.contentType || "";
          newForm.proposalType = this.record.proposalType;
          newForm.minorHandleLastTimeStr =
            this.resourceForm.minorHandleLastTimeStr;
          newForm.mainHandleLastTimeStr =
            this.resourceForm.mainHandleLastTimeStr;
          newForm.ifPublice = this.resourceForm.ifPublice; //12-13
          doAssign(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("交办成功");
              // 交办后重新获取数据，显示答复 不要了
              // this.showDesc();
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
      });
    },
    //转为参考建议
    changeRefer() {
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
      let hostData = [];
      let meetData = [];
      this.resourceForm.host.map((item) => {
        hostData.push({
          ifmain: 1,
          orgCode: item.orgCode,
          orgName: item.orgName,
        });
      });
      if (this.resourceForm.meet.length > 0) {
        this.resourceForm.meet.map((item) => {
          meetData.push({
            ifmain: 0,
            orgCode: item.orgCode,
            orgName: item.orgName,
          });
        });
      }
      let undertakeList = [];
      undertakeList = hostData.concat(meetData);
      let newForm = {};
      newForm.undertakeList = undertakeList;
      newForm.proposalId = this.proposalId;
      newForm.contentType = this.resourceForm.contentType || "";
      newForm.proposalType = "4";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "确认要转为供参考建议？",
        onOk: () => {
          this.zwCKyj = true;
          doAssign(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("转为参考建议成功");
              this.visible = false;
              this.zwCKyj = false;
              this.close();
              Object.assign(this.$data, this.$options.data.call(this));
            } else {
              this.$message.error(res.data.message);
              this.zwCKyj = false;
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 交办-保存
    save() {
      this.jbLoading = true;
      let newForm = { params: {}, data: {} };
      newForm.params.contentType = this.resourceForm.contentType || "";
      newForm.params.proposalType = this.record.proposalType;
      newForm.params.proposalId = this.proposalId;
      newForm.params.mainHandleLastTimeStr =
        this.resourceForm.mainHandleLastTimeStr;
      newForm.params.minorHandleLastTimeStr =
        this.resourceForm.minorHandleLastTimeStr;
      newForm.params.ifPublice = this.resourceForm.ifPublice; //12-13
      if (!this.resourceForm.host) {
        this.jbLoading = false;
        return this.$message.error("主办单位不能为空");
      }
      let hostData = [];
      let meetData = [];
      this.resourceForm.host.map((item) => {
        hostData.push({
          ifmain: 1,
          orgCode: item.orgCode,
          orgName: item.orgName,
          proposalId: this.proposalId,
        });
      });
      if (this.resourceForm.meet.length > 0) {
        this.resourceForm.meet.map((item) => {
          meetData.push({
            ifmain: 0,
            orgCode: item.orgCode,
            orgName: item.orgName,
            proposalId: this.proposalId,
          });
        });
      }
      let data = [];
      data = hostData.concat(meetData);
      newForm.data = data;

      let conctentTS = "是否确定保存？";
      if (newForm.params.ifPublice == "0" && !this.isPublicDisable) {
        conctentTS = "选择不公开保存后不能修改,是否确定保存？";
      }
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: conctentTS,
        onOk: () => {
          saveCBDW(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("保存成功");
              this.jbLoading = false;
              this.close();
              this.resourceForm.host = [];
              this.resourceForm.meet = [];
            } else {
              this.jbLoading = false;
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.jbLoading = false;
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 打开单位table
    openUnitTable(type) {
      this.$refs.commonUnitShuttleTable.rightData = [];
      // 再次点击 设置默认值
      if (this.resourceForm[type]) {
        // this.defaultUnit = this.resourceForm[type].map((item) => item.orgCode);
      } else {
        this.defaultUnit = [];
      }
      this.unitType = type;
      if (type == "meet" && this.resourceForm.host.length > 0) {
        if (this.resourceForm["meet"] && this.resourceForm["meet"].length > 0) {
          this.defaultUnitHB = this.resourceForm["meet"].map(
            (item) => item.orgCode
          );
        }
        // this.disableSelectedRowKeys = this.resourceForm.host.map((item) => item.orgCode);//传禁用选项
        this.disableSelectedRowKeysHB = this.resourceForm.hostorgCode; //传禁用选项
        setTimeout(() => {
          this.$refs.commonUnitShuttleTableHB.rightData =
            this.resourceForm["meet"];
          this.$refs.commonUnitShuttleTableHB.queryForm.orgName = ""; // 清空搜索条件
          this.$refs.commonUnitShuttleTableHB.unitTableVisible = true;
        }, 500);
      } else if (type == "host" && this.resourceForm.meet.length > 0) {
        if (this.resourceForm["host"] && this.resourceForm["host"].length > 0) {
          this.defaultUnit = this.resourceForm["host"].map(
            (item) => item.orgCode
          );
        }
        console.log(this.resourceForm, "this.resourceForm", this.defaultUnit);
        // this.disableSelectedRowKeys = this.resourceForm.meet.map((item) => { item.orgCode });//传禁用选项
        this.disableSelectedRowKeys = this.resourceForm.meetorgCode; //传禁用选项

        setTimeout(() => {
          this.$refs.commonUnitShuttleTable.rightData =
            this.resourceForm["host"];
          this.$refs.commonUnitShuttleTable.queryForm.orgName = ""; // 清空搜索条件
          this.$refs.commonUnitShuttleTable.unitTableVisible = true;
        }, 500);
      } else {
        this.disableSelectedRowKeys = [];
        this.$refs.commonUnitShuttleTable.unitTableVisible = true;
      }
    },
    // 接受单位
    getUnitTable(data) {
      // 交办选择主/会办单位
      // 深拷贝
      let rows = JSON.parse(JSON.stringify(data));
      if (this.unitType == "meet" || this.unitType == "host") {
        let name = rows.map((item) => item.orgName).toString();
        let orgCode = rows.map((item) => item.orgCode);
        console.info("getUnitTable", rows);
        this.resourceForm[this.unitType] = rows;
        this.resourceForm[this.unitType + "Name"] = name;
        this.resourceForm[this.unitType + "orgCode"] = orgCode;
        console.info("getUnitTable", this.unitType, this.resourceForm);
        this.defaultUnit = rows.map((item) => item.orgCode);
        this.disableSelectedRowKeys = []; //清空禁用选项
      } else if (this.unitType == "单位调整") {
        console.log("🤗🤗🤗, rows =>", rows);
        this.changeIfMainForm.orgName = "";
        this.changeIfMainForm.orgName = rows
          .map((item) => item.orgName)
          .toString();
        // 储存
        this.changeIfMainForm.rows = [];
        this.changeIfMainForm.rows = rows;
        this.changeIfMainVisible = true;
        this.$forceUpdate();
      }
    },
    getUnitTableHB(data) {
      // 会办单位
      if (this.unitType == "meet" || this.unitType == "host") {
        // 深拷贝
        let rows = JSON.parse(JSON.stringify(data));
        let name = rows.map((item) => item.orgName).toString();
        let orgCode = rows.map((item) => item.orgCode);
        console.info("getUnitTableHB", rows);
        this.resourceForm[this.unitType] = rows;
        this.resourceForm[this.unitType + "Name"] = name;
        this.resourceForm[this.unitType + "orgCode"] = orgCode;
        console.info("getUnitTableHB", this.unitType, this.resourceForm);
        this.defaultUnitHB = rows.map((item) => item.orgCode);
        this.disableSelectedRowKeysHB = []; //清空禁用选项
      }
    },
    //选择是否公开
    changeifPublice(e) {
      // if (e == '0') {
      //   this.$message.success("选择不公开保存后不能修改");
      // };
      this.$forceUpdate();
    },
    // 获取信息
    async showDesc() {
      console.log("🤗🤗🤗, this.record.status =>", this.record.status);
      console.log("🤗🤗🤗,  this.resourceForm =>", this.resourceForm);
      this.listLoading = true;

      let userDataBool = await this.getOneUser();
      if (userDataBool) {
        let res = await proposalById({ proposalId: this.proposalId });
        if (res.data.code == 200) {
          this.record = res.data.data;
          // 回显是否公开
          this.resourceForm.ifPublice = this.record.ifPublice;
          if (this.resourceForm.ifPublice == "0") {
            this.isPublicDisable = true;
          }
          this.resourceForm.contentType = this.record.proposalContentType;
          // 答复时获取经办信息 userData 请求数据较慢拿不到
          if (
            this.userData.authorities[0].authority == "YAJY_XLW" ||
            (this.record.undertakeStatus == 2 &&
              this.userData.authorities[0].authority == "YAJY_CBDW") ||
            (this.scenesStatus == "答复修改" &&
              this.userData.authorities[0].authority == "YAJY_CBDW")
          ) {
            let form = {};
            form.proposalId = this.proposalId;
            if (this.scenesStatus == "答复修改") {
              form.isUpdate = "Y";
            }
            this.resourceForm.meetorgCode = null;
            this.resourceForm.hostorgCode = null;
            this.resourceForm.host = [];
            this.resourceForm.meet = [];
            this.disableSelectedRowKeys = [];
            this.defaultUnit = [];
            getUndertakeDfInfo(form).then((res) => {
              if (res.data.code == 200) {
                if (res.data.data.undertakeDf) {
                  this.resourceForm.dfId = res.data.data.undertakeDf.dfId;
                }
                //初始化经办人数据
                this.resourceForm.orgCode = res.data.data.undertake.orgCode;
                this.resourceForm.jbrName = res.data.data.undertake.jbrName;
                this.resourceForm.jbrDept = res.data.data.undertake.jbrDept;
                this.resourceForm.jbrJob = res.data.data.undertake.jbrJob;
                this.resourceForm.jbrPhone = res.data.data.undertake.jbrPhone;
                this.resourceForm.jbrOph = res.data.data.undertake.jbrOph;

                if (
                  res.data.data.undertakeDf != undefined &&
                  res.data.data.undertakeDf != null
                ) {
                  if (
                    res.data.data.undertakeDf.dflb != undefined ||
                    res.data.data.undertakeDf.dflb != null
                  ) {
                    this.resourceForm.dflb = res.data.data.undertakeDf.dflb;
                  }
                  if (
                    res.data.data.undertakeDf.dffs &&
                    res.data.data.undertakeDf.dffs.indexOf(",") >= 0
                  ) {
                    this.resourceForm.dffs =
                      res.data.data.undertakeDf.dffs.split(",");
                  }
                  if (
                    res.data.data.undertakeDf.dffs &&
                    res.data.data.undertakeDf.dffs.indexOf(",") < 0
                  ) {
                    this.resourceForm.dffs = res.data.data.undertakeDf.dffs;
                  }
                  this.resourceForm.isPublic =
                    res.data.data.undertakeDf.isPublic;
                  this.resourceForm.orgEvaluation =
                    res.data.data.undertakeDf.orgEvaluation;
                  this.resourceForm.remark = res.data.data.undertakeDf.remark;
                  this.$forceUpdate();
                }

                //政务协同
                if (
                  res.data.data.undertake.ifmain == 1 &&
                  res.data.data.xtpfList.length > 0
                ) {
                  this.hbDataSource = res.data.data.xtpfList;
                }

                //附件
                if (
                  res.data.data.fujianList != undefined &&
                  res.data.data.fujianList != null &&
                  res.data.data.fujianList.length > 0
                ) {
                  res.data.data.fujianList.forEach((item) => {
                    if (item.attType == 2) {
                      // 附件
                      this.resourceForm.fuJianIds = [];
                      this.resourceForm.fuJianIds.push(item.attId);
                      let file = {
                        uid: item.attId,
                        name: item.attName + "." + item.attSuffix,
                        status: "done",
                        url: "",
                      };
                      this.fileTextList = [];
                      this.fileTextList.push(file);
                    } else if (item.attType == 7) {
                      this.resourceForm.forumFile = [];
                      this.resourceForm.forumFile.push(item.attId);
                      let file = {
                        uid: item.attId,
                        name: item.attName + "." + item.attSuffix,
                        status: "done",
                        url: "",
                      };
                      this.fileTextList_7 = [];
                      this.fileTextList_7.push(file);
                    } else if (item.attType == 8) {
                      this.resourceForm.communFile = [];
                      this.resourceForm.communFile.push(item.attId);
                      let file = {
                        uid: item.attId,
                        name: item.attName + "." + item.attSuffix,
                        status: "done",
                        url: "",
                      };
                      this.fileTextList_8 = [];
                      this.fileTextList_8.push(file);
                    }
                  });
                }
                this.$forceUpdate();
              }
            });
          }
          //交办时获取已保存的承办单位数据
          if (this.record.status == 40) {
            this.listLoading = true;
            getProposalUnder(this.proposalId).then((res) => {
              console.log("获取存在的承办单位数据, res =>", res);
              if (res.data.code == 200) {
                let hostNames = [];
                let meetNames = [];
                let hostData = [];
                let meetData = [];
                let hostorgCode = [];
                let meetorgCode = [];
                let minorHandleLastTimeStr = "";
                let mainHandleLastTimeStr = "";
                this.resourceForm.hostName = "";
                this.resourceForm.meetName = "";
                this.resourceForm.host = "";
                this.resourceForm.meet = "";
                this.resourceForm.hostorgCode = [];
                this.resourceForm.meetorgCode = [];
                console.log("🤗🤗🤗, 我清空了吗 =>", this.resourceForm);
                if (res.data.data.length !== 0) {
                  res.data.data.forEach((item) => {
                    if (item.ifmain == 1) {
                      hostNames.push(item.orgName);
                      hostData.push({
                        orgCode: item.orgCode,
                        orgName: item.orgName,
                      });
                      hostorgCode.push(item.orgCode);
                      mainHandleLastTimeStr = item.handleLastTime
                        ? item.handleLastTime.slice(0, 10)
                        : "";
                    }
                    if (item.ifmain == 0) {
                      meetNames.push(item.orgName);
                      meetData.push({
                        orgCode: item.orgCode,
                        orgName: item.orgName,
                      });
                      meetorgCode.push(item.orgCode);
                      minorHandleLastTimeStr = item.handleLastTime
                        ? item.handleLastTime.slice(0, 10)
                        : "";
                    }
                  });
                  this.resourceForm["hostName"] = hostNames.join(",");
                  this.resourceForm["meetName"] = meetNames.join(",");
                  this.resourceForm.hostorgCode = hostorgCode;
                  this.resourceForm.meetorgCode = meetorgCode;
                  this.resourceForm.host = hostData;
                  this.resourceForm.meet = meetData;
                  this.resourceForm.mainHandleLastTimeStr = "";
                  this.resourceForm.mainHandleLastTimeStr =
                    mainHandleLastTimeStr;
                  this.resourceForm.minorHandleLastTimeStr = "";
                  this.resourceForm.minorHandleLastTimeStr =
                    minorHandleLastTimeStr;
                  this.$forceUpdate();
                  console.log(this.resourceForm, "this.resourceForm");
                }
                setTimeout(() => {
                  this.listLoading = false;
                }, 50);
              } else {
                this.$message.error(res.data.message);
              }
            });
          }
          //获取答复、反馈数据
          this.feedbackReply();

          //获取代表数据
          this.getJointlyListData();

          //获取流程跟踪数据
          this.getLocalHistoryData();
          this.listLoading = false;
        } else {
          this.listLoading = false;

          this.$message.error(res.data.message);
        }
      }

      // 获取子流程状态值
      // this.getUndertakeStatusData();
    },
    // 获取登录人数据
    async getOneUser() {

      instance_yajy2({
        url: "/api/v1/auth/userInfoData",
        method: "post",
        headers: {
          "content-type": "application/json;charset=UTF-8",
          "token": this.lfToken,
          "flag":false
        },

      }).then((res) => {
        if (res.data.code == 200) {
          // 获取登录人的权限
          this.userData = res.data.data;
          return true;
        } else {
          this.$message.error(res.data.message);
          return false;
        }
      });


    },

    // 答复时获取经办信息
    getJbrData() {
      getJbr(this.proposalId).then((res) => {
        console.log("🤗🤗🤗, res =>答复时获取经办信息", res);
        if (res.data.code == 200) {
          this.resourceForm.jbrName = res.data.data[0].jbrName;
          this.resourceForm.jbrDept = res.data.data[0].jbrDept;
          this.resourceForm.jbrJob = res.data.data[0].jbrJob;
          this.resourceForm.jbrPhone = res.data.data[0].jbrPhone;
          this.resourceForm.jbrOph = res.data.data[0].jbrOph;
        }
      });
    },
    viewFkEvaluation(data) {
      if (data) {
        this.fkContent = data.gdjy;
        if (data.evaluationValue == 1) {
          this.fkEvaluation = "不满意";
        }
        if (data.evaluationValue == 2) {
          this.fkEvaluation = "基本满意";
        }
        if (data.evaluationValue == 3) {
          this.fkEvaluation = "满意";
        }
        this.fkVisible = true;
      }
    },
    // 关闭反馈窗口
    closeFkVisible() {
      this.fkContent = "";
      this.fkEvaluation = "";
      this.fkVisible = false;
    },
  },
};
</script>
