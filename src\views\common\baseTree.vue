<template>
    <a-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
        <a-input
          style="width: 300px;"
          v-model="filterText"
          placeholder="输入关键字过滤"
          allow-clear
          @change="onChange"
        />
        <a-tree
          v-if="treeData.length>0"
          ref="orgTree"
          :replace-fields="replaceFields"
          :tree-data="treeData"
          :default-expanded-keys="keys"
          :default-selected-keys="keys"
          @select="handleClick"
        >
           <template slot="title" slot-scope="{ fullName }">
        <span
          v-if="searchValue && fullName.indexOf(searchValue) > -1"
          style="color: #f50;"
        >
          {{ fullName }}
        </span>
        <span v-else>{{ fullName }}</span>
      </template>
        
        </a-tree>
      </a-col>
</template>

<script>
import { baseData } from "@/api/dmcs";
export default {
  data() {
    return {
      keys: [],
       replaceFields: {
        title: "fullName",
        key: "id",
        children: "children",
      },
      searchValue: "",
      filterText: "",
      treeData:[],
      allChildData: [],
      treeDataInfo: [], // 修改的机构树
    }
  },
   created() {
    this.getOrgTreeRawData();
  },
  methods: {
      // 获取tree数据
    getOrgTreeRawData() {
      baseData().then((res) => {
        this.treeData = res.data;
        this.treeDataInfo = JSON.parse(JSON.stringify(this.treeData))
        this.keys = [this.treeData[2].children[0].children[2].id];
      });
    },
      handleClick(data, e){
        let entryId=data[0].substr(1)
        this.$emit('handleClick',entryId,e)
      },
        // 树状搜索
    onChange(e) {
      const value = e.target.value;
      this.treeData = this.getSearchList(this.treeDataInfo, value)
      if (this.treeData.length > 0) {
        this.keys = [this.treeData[0].id];
      }
      const expandedKeys = this.allChildData.map((item) => {
        if (item.name.indexOf(value) > -1) {
          return this.getParentKey(item.id, this.userTreeData);
        }
        return null;
      });
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      });
    },
    filterNode(node, value) {   
      if (!node.children) {      
        return node.fullName.includes(value) ? [node] : [];    
      }    
      const filteredChildren = node.children.flatMap(child => this.filterNode(child, value)); 
      return filteredChildren;    
    },
    getSearchList(tree, value) {    
      return tree.map(node => {  
        const filteredChildren = this.filterNode(node, value);  
        if (filteredChildren.length > 0 || node.fullName.includes(value)) { 
          return { ...node, children: filteredChildren.length > 0 ? filteredChildren : undefined };  
        }  
        return null;  
      }).filter(node => node !== null);  
    },
    // 获取所在的父节点
    getParentKey(id, tree) {
      let parentKey;
      for (let index = 0; index < tree.length; index++) {
        const node = tree[index];
        if (node.children) {
          if (node.children.some((item) => item.id === id)) {
            parentKey = node.id;
          } else if (this.getParentKey(id, node.children)) {
            parentKey = this.getParentKey(id, node.children);
          }
        }
      }
      return parentKey;
    },
  },
}
</script>

<style>

</style>