<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <SearchForm :value="queryForm" @onReset="reset" @onSearch="search">
          <template v-slot:topSearch>
            <AdminStreetStationCascade v-model="queryForm" allow-clear />
          </template>
          <template v-slot:moreSearch>
            <DhJcCascade v-model="queryForm" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>

            <FormInput
              v-model="queryForm.userName"
              :label="'代表姓名'"
              allow-clear
              @search="search"
            />

            <FormPicker
              v-model="queryForm.year"
              label="年度"
              type="year"
              allow-clear
            />
          </template>
        </SearchForm>

        <a-row style="margin: 5px 0px 10px 8px">
          <a-col span="8">
            <!-- <a-button @click="$router.go(-1)"> 返回</a-button> -->
            <a-button
              type="primary"
              style="margin-left: 8px"
              @click="exportExcel"
              >导出</a-button
            >
          </a-col>
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <a-table
            ref="table"
            :bordered="false"
            class="directorySet-table"
            size="small"
            :columns="columns"
            :pagination="pagination"
            :data-source="list"
            :row-key="
              (record, index) => {
                return record.userId;
              }
            "
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import {
  communityMemberList,
  communityMemberExport,
} from "@/api/communityschedule";
import SearchForm from "@/components/SearchForm/index";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import FormPicker from "@/components/FormPicker/index.vue";
import { DBLZ_DBJSQ } from "@/utils/enum/levelRoleMainIdentifyEnum";

export default {
  components: {
    FormPicker,
    FormInput,
    AdminStreetStationCascade,
    DhJcCascade,
    SearchForm,
  },
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBJSQ,
      selectedRows: [],
      selectedRowKeys: [],
      list: [],
      listLoading: false,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        dhDm: undefined,
        jcDm: undefined,
        dbtId: undefined,
        userName: undefined,
        year: undefined,
      },
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      // 列表
      columns: [
        {
          title: "序号",
          align: "center",
          width: 60,
          ellipsis: true,
          customRender: (text, record, index) => {
            return index + 1 || "/";
          },
        },
        {
          title: "活动时间",
          align: "center",
          width: 130,
          ellipsis: true,
          dataIndex: "serverTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "行政区划",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "administrativeAreaName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "乡镇街道",
          align: "center",
          width: 130,
          ellipsis: true,
          dataIndex: "streetTown",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站名称",
          align: "center",
          width: 340,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.liaisonStationName || "/";
          },
        },
        {
          align: "center",
          title: "代表类型",
          width: 150,
          dataIndex: "sfmc",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "代表姓名",
          width: 150,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          title: "进社区活动次数",
          align: "center",
          width: 140,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.num || "/";
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created() {
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "代表进社区活动次数统计");
  },
  methods: {
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    //列表
    fetchData() {
      this.listLoading = true;
      communityMemberList(this.queryForm).then((response) => {
        this.list = response.rows;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    handleSizeChange(val) {
      this.queryForm.size = val;
      this.fetchData();
    },
    reset(value) {
      this.queryForm = value;
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
    exportExcel() {
      var arr = [];
      if (this.selectedRowKeys.length != 0) {
        // return this.$message.warning("请选择数据！");
        // arr = this.selectRows.map((item) => item.userId)
        arr = this.selectedRowKeys.map((item) => item);
      }
      communityMemberExport({
        ...this.queryForm,
        ids: arr.toString(),
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `代表进社区活动次数统计.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
  },
};
</script>
