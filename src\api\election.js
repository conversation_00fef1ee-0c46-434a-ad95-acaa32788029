import request from "@/utils/requestTemp";
import qs from "qs";

export function preliminary(params) {
  return request({
    url: "/ready/register/findAll",
    method: "post",
    params,
  });
}
export function declareLis(params) {
  return request({
    url: "/ready/declare/findAll",
    method: "post",
    params,
  });
}
// 获取届次
export function getPeriod() {
  return request({
    url: "/dictCode/common/getLevelList",
    method: "get",
  });
}
// 获取性别
export function getSex() {
  return request({
    url: "/dictCode/common/getSexList",
    method: "get",
  });
}
// 获取民族
export function getNation() {
  return request({
    url: "/dictCode/common/getNation",
    method: "get",
  });
}
// 获取出党派下拉数据列表
export function getPolitical() {
  return request({
    url: "/dictCode/common/getPolitical",
    method: "get",
  });
}
// 获取出推荐单位下拉数据列表
export function getRecommend() {
  return request({
    url: "/dictCode/common/getRecommend",
    method: "get",
  });
}

// 次级下拉框
export function Secondary(params) {
  return request({
    url: "/dictCode/common/getRecommendSon",
    method: "get",
    params,
  });
}
// 获取出综合构成下拉数据列表
export function getSynthesize() {
  return request({
    url: "/dictCode/common/getSynthesize",
    method: "get",
  });
}
// 获取出职业构成下拉数据列表
export function getOccupation() {
  return request({
    url: "/dictCode/common/getOccupation",
    method: "get",
  });
}
// 获取在职教育学历、全日制教育学历下拉数据列表
export function getEducationState() {
  return request({
    url: "/dictCode/common/getEducationStateList",
    method: "get",
  });
}

// 获取出行政区划id下拉数据列表
export function getAdministrativeState() {
  return request({
    url: "/dictCode/common/getAdministrativeRankStateList",
    method: "get",
  });
}

// 获取出代表团下拉数据列表
export function getDelegation() {
  return request({
    url: "/dictCode/common/getDelegation",
    method: "get",
  });
}

// 代表基本情况明细数据——统计条件
export function queryBasicCaseDetail(params) {
  return request({
    url: "/representative/behalfStatistics/queryBasicCaseDetail",
    method: "get",
    params,
  });
}

// 获取出缺类型下拉数据列表
export function getVacancyList() {
  return request({
    url: "/dictCode/common/getAttendanceList",
    method: "get",
  });
}
//查询代表构成详细信息
export function QueryRepCompositionDetails(params) {
  return request({
    url: "/representative/behalfStatistics/queryTheShapeDetail",
    method: "get",
    params,
  });
}
// 获取 表单状态下拉数据列表
export function getFormStateList(params) {
  return request({
    url: "/dictCode/common/getFormStateList",
    method: "get",
    params,
  });
}

//////////////+
// 获取 通知
export function getnoticeList(params) {
  return request({
    url: "home/notice/noticeList",
    method: "get",
    params,
  });
}

//获取 调查问卷
export function getquestionnaireList(params) {
  return request({
    url: "home/notice/questionnaireList",
    method: "get",
    params,
  });
}
//获取 网站链接
export function getWeblinksList(params) {
  return request({
    url: "home/notice/linkList",
    method: "get",
    params,
  });
}

//获取课程学分和课程类别
export function getCreditList(params) {
  return request({
    url: "common/findSelectList",
    method: "get",
    params,
  });
}
// 获取热门数据 hotResource hotLink
export function hotList(params) {
  return request({
    url: "home/notice/notData",
    method: "get",
    params,
  });
}

// 获取已选资源
export function findStudyList(params) {
  return request({
    url: "home/study/findStudyList",
    method: "get",
    params,
  });
}

// ++++++++
//资料库 列表
export function getDatabaseList(params) {
  return request({
    // url: "/home/<USER>/findArticleList",
    url: "/contentmanager/issue/findContentList",
    method: "get",
    params: params
  });
}

// 资料库详情
export function getStudyVideoData(params) {
  return request({
    url: "/home/<USER>/chairDetails",
    method: "get",
    params,
  });
}

// 获取树类型文件（培训课堂）contentmanager/issue/connectionFind
export function getConnectionFind(params) {
  return request({
    url: "contentmanager/issue/connectionFind",
    method: "get",
    params,
  });
}
// 消息已读 home/notice/insertRead
export function insertRead(params) {
  return request({
    url: "home/notice/insertRead",
    method: "get",
    params,
  });
}

//文章 详情
export function getDatabaseData(params) {
  return request({
    url: "/home/<USER>/findArticleContent",
    method: "get",
    params,
  });
}