<template>
  <div class="table-container">
    <!-- <TJTable :list="list" :columns="columns"></TJTable> -->
    <a-row class="formBox">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline">
        <a-col span="6">
          <a-form-model-item label="届次">
            <a-select v-model="queryForm.jcDm"
                      allow-clearstyle="width: 100%"
                      placeholder="请选择届次">
              <a-select-option v-for="item in periods"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>

        <a-col span="6">
          <a-form-model-item label="姓名">
            <a-input v-model="queryForm.userName"
                     autocomplete="off"
                     allow-clear
                     placeholder="请输入姓名"
                     v-on:keyup.enter="search"></a-input>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="性别"
                             prop="xbDm">
            <a-select v-model="queryForm.xbDm"
                      style="width: 100%"
                      allow-clear
                      default-first-option>
              <a-select-option key="2"
                               value="2">女</a-select-option>
              <a-select-option key="1"
                               value="1">男</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-form-model>

      <a-button type="primary"
                @click="search">搜索</a-button>
      <a-button style="margin-left: 12px;"
                @click="reset"
                class="pinkBoutton">重置</a-button>
    </a-row>
    <a-row>
      <standard-table :bordered="bordered"
                      :columns="columns"
                      rowKey="id"
                      :dataSource="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"></standard-table>
    </a-row>
  </div>
</template>
<script>
import StandardTable from "@/components/table/StandardTable";
import { myPagination } from "@/mixins/pagination.js";
import { getLevelListApi, getfindallselectApi } from "@/api/representativeElection/candidateApi.js";
// import TJTable from "@/views/common/TJTable";

export default {
  // 引入分页器配置
  mixins: [myPagination],
  components: {
    StandardTable
  },
  data () {
    return {
      TBloading: false,
      bordered: true,
      // 届次
      periods: [],
      //表单状态
      state: '21',
      selectedRows: [],
      // pagination: [],
      queryForm: {
        jcDm: 2,
        pageNum: 1,
        pageSize: 10,
        xbDm: "",
        userName: "",
        state: '21',
      },
      columns: [
        {
          ellipsis: true,
          title: "届次",
          dataIndex: "LEVEL_NAME",
        },
        {
          ellipsis: true,
          title: "选举单位",
          dataIndex: "XZQHMC",
        },
        {
          ellipsis: true,
          title: "姓名",
          dataIndex: "USER_NAME",
        },
        {
          ellipsis: true,
          title: "性别",
          dataIndex: "SEX",
          // customRender: (text, record, index) => {
          //   if (text == '1') {
          //     return "男"
          //   } else if (text == '2') {
          //     return '女'
          //   }
          // },
        },
        {
          ellipsis: true,
          title: "出生日期",
          dataIndex: "BIRTHDAY",
        },
        {
          ellipsis: true,
          title: "候选人阶段",
          children: [
            {
              ellipsis: true,
              title: "申报表",
              dataIndex: "SBBID",
              customRender: (text, record, index) => {
                if (text == 'Y') {
                  return "已填报"
                } else if (text == 'N') {
                  return '未填报'
                }
              },
            },
            {
              ellipsis: true,
              title: "登记表",
              dataIndex: "DJBID",
              customRender: (text, record, index) => {
                if (text == 'Y') {
                  return "已填报"
                } else if (text == 'N') {
                  return '未填报'
                }
              },
            },
            {
              ellipsis: true,
              title: "预备人选",
              dataIndex: "READY_PEOPLE",
              customRender: (text, record, index) => {
                if (text == 'Y') {
                  return "是"
                } else if (text == 'N') {
                  return '否'
                }
              },
            },
            {
              ellipsis: true,
              title: "候选人",
              dataIndex: "CANDIDATE",
              customRender: (text, record, index) => {
                if (text == 'Y') {
                  return "是"
                } else if (text == 'N') {
                  return '否'
                }
              },
            },


          ]
        },

        {
          ellipsis: true,
          title: "代表阶段",
          children: [
            {
              ellipsis: true,
              title: "落选候选人",
              dataIndex: "REJECT_CAND",
              customRender: (text, record, index) => {
                if (text == 'Y') {
                  return "是"
                } else if (text == 'N') {
                  return '否'
                }
              },
            },
            {
              ellipsis: true,
              title: "当选代表",
              dataIndex: "ELEC_REPR",
              customRender: (text, record, index) => {
                if (text == 'Y') {
                  return "是"
                } else if (text == 'N') {
                  return '否'
                }
              },
            },
            {
              ellipsis: true,
              title: "代表团",
              dataIndex: "DBTMC",
            },

          ]
        },


      ],
      dataSource: [
        {
          index1: "十四届",
          index2: "越秀区人大",
          index3: "叶少阳",
          index4: "男",
          index5: "1990-07-25",
          index6: "已提交",
          index7: "已提交",
          index8: "李浩",
          index9: "李浩",
          index10: "郭丽",
          index11: "张守",
          index12: "越秀区代表团",
        },
        {
          index1: "十四届",
          index2: "天河区人大",
          index3: "张守",
          index4: "男",
          index5: "1985-06-05",
          index6: "已提交",
          index7: "已提交",
          index8: "李浩",
          index9: "李浩明",
          index10: "郭丽",
          index11: "张守",
          index12: "天河区代表团",
        },
        {
          index1: "十三届",
          index2: "越秀区人大",
          index3: "李晨",
          index4: "男",
          index5: "1977-03-15",
          index6: "已提交",
          index7: "已提交",
          index8: "李浩",
          index9: "李浩明",
          index10: "郭丽",
          index11: "张守",
          index12: "番禺区代表团",
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "预备人选管理");
    this.$store.dispatch("navigation/breadcrumb2", "综合查询");
    this.getLevelListFn()
    this.fetchData()
  },
  methods: {
    //获取数据
    fetchData () {
      this.TBloading = true;
      getfindallselectApi(this.queryForm).then(res => {
        // console.log(res.data);
        this.dataSource = res.data.rows
        this.pagination.total = res.data.total;
        this.TBloading = false;
      })
    },
    //重置
    reset () {
      this.queryForm = {
        jcDm: '2',
        pageNum: 1,
        pageSize: 10,
        xbDm: "",
        userName: "",
      }
      this.fetchData()
    },
    //搜索
    search () {
      this.queryForm.pageNum = 1
      this.pagination.current = 1
      this.fetchData()
    },

    //获取当前届次下拉数据列表
    async getLevelListFn () {
      const res = await getLevelListApi()
      if (res.data.code === "0000") {
        this.periods = res.data.data
        // console.log(this.periods[0].jcDm, '00');
        this.queryForm.jcDm = this.periods[2].jcDm
        console.log(this.queryForm.jcDm);
      }
    },
  },

};
</script>
