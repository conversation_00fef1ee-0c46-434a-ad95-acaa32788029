import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
    return request({
        url: "/api/v1/meetingDining/getList",
        method: "post",
        data,
    });
}

export function getUserInfoList(data) {
    return request({
        url: "/api/v1/meetingDining/getUserInfoList",
        method: "post",
        data,
    });
}

export function doSave(data) {
    console.log(data, 'data');
    return request({
        url: "/api/v1/meetingDining/addDiningList",
        method: "post",
        data,
    });
}

export function doUpdate(data) {
    return request({
        url: "/api/v1/meetingDining/updateMeetingDining",
        method: "post",
        data,
    });
}

export function doDelete(param) {
    return request({
        url: "/api/v1/meetingDining/delMeetingDining/" + param,
        method: "post",
    });
}

export function addDiningList(data) {
    return request({
        url: "api/v1/meetingDining/addMeetingDiningList",
        method: "post",
        data,
    });
}

export function downloadExcel(data) {
    return request({
        url: "api/v1/meetingDining/downloadExcel",
        method: "post",
        data,
        responseType: "blob",
    });
}

export function updateAllocation(data) {
    return request({
        url: "/api/v1/meetingDining/updateAllocation",
        method: "post",
        data,
    });
}

export function getUserList(data) {
    return request({
        url: "/api/v1/meetingDining/getUserList",
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        transformRequest: [
            function(data) {
                return qs.stringify(data);
            },
        ],
    });
}

export function getById(data) {
    return request({
        url: "/api/v1/meetingDining/getById",
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        transformRequest: [
            function(data) {
                return qs.stringify(data);
            },
        ],
    });
}

//impVehicleInformation
// uploadExcel