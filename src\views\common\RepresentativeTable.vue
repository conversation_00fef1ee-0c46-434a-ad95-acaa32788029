<template>
  <!-- 代表列表 -->
  <a-modal
    :visible.sync="jointTableVisible"
    width="60%"
    title="查找"
    @cancel="close"
    @ok="jointTableOk"
  >
    <div style="justify-content: center; display: flex; align-items: center;">
      <a-form layout="inline">
        <a-form-item label="单位名称">
          <a-row>
            <a-input v-model="searchValue" style="width: 400px;"></a-input>
          </a-row>
        </a-form-item>
      </a-form>
      <a-button type="primary" @click="search">搜索</a-button>
    </div>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      rowKey="userId"
      :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onChange: onSelectChange,
      }"
      :pagination="false"
    >
    </a-table>
  </a-modal>
</template>
<script>
import { getDeputyList } from "@/api/myJob/myProposal.js";
export default {
  data() {
    return {
      jointTableVisible: false,
      searchValue: "",
      selectedRows: [],
      dataSource: [],
      columns: [
        {
          title: "单位名称",
          dataIndex: "name",
        },
      ],
      selectedRowKeys: [],
    };
  },
  created() {
    this.getJointList();
  },
  methods: {
    // 保存
    jointTableOk() {
      this.$emit("emitJoinTable", this.selectedRows);
      this.jointTableVisible = false;
      this.selectedRows = [];
      this.selectedRowKeys = [];
    },
    // 选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
    },
    // 搜索
    search() {},
    // 关闭
    close() {
      this.jointTableVisible = false;
    },
    // 获取联名代表
    getJointList() {
      getDeputyList().then((res) => {
        console.log("获取联名代表, res =>", res);
        this.dataSource = res.data.data;
      });
    },
  },
};
</script>
<style scoped>
div >>> .ant-modal-body {
  height: 600px;
  overflow-y: scroll;
}
</style>
