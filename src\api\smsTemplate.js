import request from "@/utils/requestTemp";

export function save(data) {
  return request({
    url: "/smsTemplate/save",
    method: "post",
    data,
  });
}

export function update(data) {
  return request({
    url: "/smsTemplate/update",
    method: "post",
    data,
  });
}

export function list(params) {
  return request({
    url: "/smsTemplate/list",
    method: "get",
    params,
  });
}

export function deleteByIds(ids) {
  return request({
    url: "/smsTemplate/deleteByIds",
    method: "post",
    data: ids,
  });
}
