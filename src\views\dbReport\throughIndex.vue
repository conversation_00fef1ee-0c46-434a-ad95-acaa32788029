<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <a-row>
          <a-form
            ref="listQuery"
            :model="listQuery"
            layout="horizontal"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18, offset: 0 }"
          >
            <div>
              <a-row style="margin-left: 1%;">
                <a-col :span="6">
                  <a-form-item
                    label="关键词"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18, offset: 0 }"
                  >
                    <a-input
                      v-model="listQuery.keyword"
                      placeholder="请输入关键词"
                      clearable
                      allow-clear
                      @keyup.enter="handleQuery"
                    >
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item
                    label="状态"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18, offset: 0 }"
                  >
                    <a-select
                      v-model="listQuery.status"
                      placeholder="请选择状态"
                      clearable
                      allow-clear
                    >
                      <a-select-option
                        v-for="(item, index) in arrOptions"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      >
                        {{ item.label }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6">
                  <a-form-model-item
                    :label-col="{ span: 5 }"
                    :wrapper-col="{ span: 17, offset: 0 }"
                    label="时间范围"
                  >
                    <a-range-picker
                      style="width: 100%"
                      :ranges="{
                    最近三个月: [
                      moment(new Date()).subtract(2, 'months'),
                      moment(),
                    ],
                    '今年1-6月': [
                      moment(moment().startOf('year')).startOf('month'),
                      moment(moment().startOf('year'))
                        .add(5, 'months')
                        .endOf('month'),
                    ],
                    '今年7-12月': [
                      moment(moment().startOf('year'))
                        .add(6, 'months')
                        .startOf('month'),
                      moment(moment().startOf('year'))
                        .add(11, 'months')
                        .endOf('month'),
                    ],
                    今年内: [
                      moment(moment().startOf('year')).startOf('month'),
                      moment(moment().startOf('year'))
                        .add(11, 'months')
                        .endOf('month'),
                    ],
                  }"
                      v-model="dateRange"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      @change="onTimeChange"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <span style="float: right; margin-top: 3px;">
                    <a style="margin-right: 8px;" @click="toggleAdvanced">
                      {{ advanced ? "收起" : "高级搜索" }}
                      <a-icon :type="advanced ? 'up' : 'down'" />
                    </a>
                    <a-button type="primary" @click="handleQuery"
                      >搜索</a-button
                    >
                    <a-button
                      style="margin-left: 12px;"
                      class="pinkBoutton"
                      @click="reset"
                      >重置</a-button
                    >
                  </span>
                </a-col>
              </a-row>
              <a-row v-if="advanced" style="margin-left: 1%;">
                <a-col :span="6">
                  <a-form-item
                    label="编号"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18, offset: 0 }"
                  >
                    <a-input
                      v-model="listQuery.trainNo"
                      placeholder="请输入编号"
                      clearable
                      allow-clear
                      @keyup.enter="handleQuery"
                    >
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6" class="overLength">
                  <a-form-item
                    label="意见建议分类"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18, offset: 0 }"
                  >
                    <a-input
                      v-model="listQuery.orgName"
                      placeholder="请输入意见建议分类"
                      clearable
                      allow-clear
                      @keyup.enter="handleQuery"
                    >
                    </a-input>
                    <!-- <a-select v-model="newQueryForm.status"
                              placeholder="请选择意见建议分类"
                              clearable
                              allow-clear>
                      <a-select-option v-for="(item, index) in arrOptions"
                                       :key="index"
                                       :label="item.label"
                                       :value="item.value">
                        {{  item.label  }}</a-select-option>
                    </a-select> -->
                  </a-form-item>
                </a-col>

                <a-col :span="6" class="overLength">
                  <a-form-item
                    label="提交时间范围"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18, offset: 0 }"
                  >
                    <a-range-picker
                      v-model="dateRangeTJ"
                      style="width: 100%;"
                      :ranges="{
                        最近三个月: [
                          moment(new Date()).subtract(2, 'months'),
                          moment(),
                        ],
                        '今年1-6月': [
                          moment(moment().startOf('year')).startOf('month'),
                          moment(moment().startOf('year'))
                            .add(5, 'months')
                            .endOf('month'),
                        ],
                        '今年7-12月': [
                          moment(moment().startOf('year'))
                            .add(6, 'months')
                            .startOf('month'),
                          moment(moment().startOf('year'))
                            .add(11, 'months')
                            .endOf('month'),
                        ],
                        今年内: [
                          moment(moment().startOf('year')).startOf('month'),
                          moment(moment().startOf('year'))
                            .add(11, 'months')
                            .endOf('month'),
                        ],
                      }"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      @change="onTimeChangeTJ"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="6" class="overLength">
                  <a-form-item
                    label="回复时间范围"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18, offset: 0 }"
                  >
                    <a-range-picker
                      v-model="dateRangeHF"
                      style="width: 100%;"
                      :ranges="{
                        最近三个月: [
                          moment(new Date()).subtract(2, 'months'),
                          moment(),
                        ],
                        '今年1-6月': [
                          moment(moment().startOf('year')).startOf('month'),
                          moment(moment().startOf('year'))
                            .add(5, 'months')
                            .endOf('month'),
                        ],
                        '今年7-12月': [
                          moment(moment().startOf('year'))
                            .add(6, 'months')
                            .startOf('month'),
                          moment(moment().startOf('year'))
                            .add(11, 'months')
                            .endOf('month'),
                        ],
                        今年内: [
                          moment(moment().startOf('year')).startOf('month'),
                          moment(moment().startOf('year'))
                            .add(11, 'months')
                            .endOf('month'),
                        ],
                      }"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      @change="onTimeChangeHF"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item
                    label="回复人"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18, offset: 0 }"
                  >
                    <a-input
                      v-model="newQueryForm.receiveUserName"
                      placeholder="请输入回复人"
                      clearable
                      allow-clear
                      @keyup.enter="handleQuery"
                    >
                    </a-input>
                  </a-form-item>
                </a-col>
              </a-row>
<!--              <a-row v-if="isDb == '0'" style="margin: 5px 0px 10px 8px;">-->

<!--                <a-button-->
<!--                  style="margin-left: 12px;"-->
<!--                  type="primary"-->
<!--                  :loading="downloadLoading"-->
<!--                  @click="download"-->
<!--                  >导出-->
<!--                </a-button>-->
<!--                &lt;!&ndash; :loading="downloadLoading" &ndash;&gt;-->
<!--              </a-row>-->
            </div>
          </a-form>
        </a-row>
      </a-col>

      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <!-- 更多查询条件抽屉 结束 -->
        <!-- table -->
        <a-spin :spinning="listLoading" :indicator="indicator">
          <a-table
            ref="table"
            :bordered="false"
            class="tableLimit"
            :columns="columns"
            :pagination="pagination"
            :data-source="list"
            :row-key="
              (record, index) => {
                return index + record.id;
              }
            "
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            :custom-row="clickRow"
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>

  </div>
</template>

<script>
import { instance_1, instance_3 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
import Vue from "vue";
import { getUserIdZtc as directTrainList} from "@/api/dbReport/dbReport";


const action =
  Vue.prototype.GLOBAL.basePath_1 + "/api/v1/meetingFile/fileUpload";

export default {
  name: "Table",
  components: {},
  filters: {
    getState: function (state) {
      if (state == 1) {
        return "草稿";
      } else if (state == 2) {
        return "联络站处理";
      } else if (state == 3) {
        return "12345热线处理";
      } else if (state == 4) {
        return "已办结";
      }
    },
  },
  data() {
    return {
      TBloading: false,
      dateRangeTJ: [], //提交时间范围
      dateRangeHF: [], //回复时间范围
      advanced: false,
      downloadLoading: false,
      fileUrl: [],
      selectedRowKeys: [],
      admin: false,
      userName: "",
      editShow: false,
      titleText: "",
      active: "1",
      videoList: [],
      videoFujianData: [],
      fujian: [],
      imgFujian: [],
      videoFujian: [],
      qitaFujian: [],
      isDisabled: false,
      address: "",
      comment: "",
      station: "",
      content: "",
      commentCategory: "城市建设",
      progressStatus: 2,
      fileList: [],
      fileValue: [],
      imgList: [],
      action: action,
      interfaceLocation: "",
      //  action: "http://************:8080/ilz/api/v1/" + "activity/upload",
      fileData: {
        relId: "",
        type: 0,
        encrypted: true,
      },
      urlData: "",
      dialogImageUrl: "",
      formDataFU: new FormData(),
      dialogVisible: false,
      defaultfile: [],
      isColor: "0",
      dialogFormVisible: false,
      isCheck: false,
      badgeHide: true,
      drawer: false,
      drawerHeight: window.innerHeight - 75 + "px",
      list: [],
      operating: false,
      listLoading: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      listHeight: window.innerHeight - 360 + "px",
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      filePath: "",
      cityItems: [
        { id: "237", title: "法制工作委员会", status: false },
        { id: "161", title: "监察和司法工作委员会", status: false },
        { id: "248", title: "农村工作委员会", status: false },
        {
          id: "cddb0043738345889b4d4a8afdd1a5e9",
          title: "社会建设工作委员会",
          status: false,
        },
        { id: "568", title: "城乡建设环境与资源保护工作委员会", status: false },

        { id: "331", title: "预算工作委员会", status: false },
        { id: "494", title: "经济工作委员会", status: false },

        { id: "514", title: "教育科学文化卫生工作委员会", status: false },
        { id: "123", title: "华侨外事民族宗教工作委员会", status: false },

        { id: "387", title: "选举联络人事人免工作委员会", status: false },
      ],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        orgName:"",
        keyword:"",
        trainNo:"",
        status: "",
        userId:"",
        startTime: null,
        endTime: null,

      },
      newQueryForm: {
        status: "",
        receiveUserName:"",
        userName:"",
      },
      indexNum: 1,
      IShow: false,
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "状态",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            if (text == 1) {
              return "未受理";
            } else if (text == 0) {
              return "草稿";
            } else if (text == 2) {
              return "已受理";
            } else if (text == 3) {
              return "已办结";
            } else if (text == 4) {
              return "已退回";
            } else {
              return "";
            }
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "代表手机号",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "mobile",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "意见建议分类",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "orgName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "提交时间",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "submitTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "意见内容",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "opinionContent",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "回复时间",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "receiveTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "回复人名称",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "receiveUserName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "编号",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "trainNo",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },

        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            let permission = record.status == 0;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.details(record);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display:
                      permission && this.operating ? "inline-block" : "none",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(record);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display:
                      permission && this.operating ? "inline-block" : "none",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.deleData(record.id);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      formData: {
        opinionContent: "",
        orgId: "",
        orgName: "",
        status: "0",
        sfTjgllz: ["0"],
      },
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      isDb: null,
      arrOptions: [
        // {
        //   value: "",
        //   label: "全部",
        // },
        {
          value: 0,
          label: "草稿",
        },
        {
          value: 1,
          label: "未受理",
        },
        {
          value: 2,
          label: "已受理",
        },
        {
          value: 3,
          label: "已办结",
        },
        {
          value: 4,
          label: "已退回",
        },
      ],
      statusOptions: [
        {
          value: "",
          label: "全部",
        },
        {
          value: 0,
          label: "已转办",
        },
        {
          value: 1,
          label: "已回复",
        },
      ],

      // 限制结束日期不能大于开始日期
      pickerOptions0: {
        disabledDate: (time) => {
          const endDateVal = this.queryForm.endTime;
          if (endDateVal) {
            return time.getTime() >= new Date(endDateVal).getTime();
          }
        },
      },
      pickerOptions1: {
        disabledDate: (time) => {
          const beginDateVal = this.queryForm.startTime;
          if (beginDateVal) {
            return time.getTime() < new Date(beginDateVal).getTime();
          }
        },
      },
    };
  },
  computed: {
    conditionsCount: function () {
      let size = 0;
      console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
      for (const key in this.queryForm) {
        if (Object.hasOwnProperty.call(this.queryForm, key)) {
          if (key == "pageNum" || key == "pageSize") {
            continue;
          }
          const element = this.queryForm[key];
          if (
            Array.isArray(element)
              ? element.length > 0
              : element !== "" && element != null
          ) {
            size++;
          }
        }
      }
      return size;
    },
  },
  watch: {
    "formData.title": {
      handler: function () {
        if (this.formData.title.length > 30) {
          this.formData.title = this.formData.title.substring(0, 30);
          this.$message.error("最多只能输入30字");
        }
      },
    },
    "formData.opinionContent": {
      handler: function () {
        if (this.formData.opinionContent.length > 2000) {
          this.formData.opinionContent = this.formData.opinionContent.substring(
            0,
            2000
          );
          this.$message.error("最多只能输入2000字");
        }
      },
    },
    conditionsCount(newVal, oldVal) {
      this.badgeHide = newVal <= 0;
    },
  },
  created() {
    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));
    console.log(this.$route.query, "-----");
    this.listQuery.userId=this.$route.query.userId;
    this.listQuery.startTime= this.$route.query.startTime;
    this.listQuery.endTime= this.$route.query.endTime;
    let listQuery = JSON.parse(localStorage.getItem("tongjifenzi"));
    if (listQuery) {
      this.listQuery = listQuery;
      // 回显时间
      if (this.listQuery.startTime) {
        var dateRanglist =
          this.listQuery.startTime + "," + this.listQuery.endTime;
        this.dateRange = dateRanglist.split(",");
      };
      this.pagination.current = this.queryForm.pageNum;
    }
    this.listQuery.userId=this.$route.query.userId;
    this.$store.dispatch("navigation/breadcrumb1", "代表随手拍");
    this.$store.dispatch("navigation/breadcrumb2", "随手拍意见建议上传");
    this.fetchData();
    this.getMyLiaisonStation();
  },
  beforeDestroy() {
    var queryForm = { ...this.newQueryForm, ...this.listQuery };
    window.localStorage.setItem("tongjifenzi", JSON.stringify(queryForm));
  },
  mounted() {
    // 抽屉滚动区高度
    window.onresize = () => {
      return (() => {
        this.drawerHeight = window.innerHeight - 75 + "px";
        this.listHeight = window.innerHeight - 360 + "px";
      })();
    };
  },

  methods: {
    // 时间选择
    onTimeChange(val) {
      // console.log("🤗🤗🤗, val =>", val);
      this.listQuery.startTime = val[0];
      this.listQuery.endTime = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },
    // 修改此意见是否向联络站提交过状态
    onChangeSfTjgllz(e) {
      this.formData.sfTjgllz = e[e.length - 1];
    },


    getMyLiaisonStation() {
      instance_1({
        method: "get",
        url: "/common/getUserInfo",
        data: {},
        header: {
          "Content-type": "application/x-www-form-urlencoded",
        },
      }).then((res) => {
        if (res.data.code == "0000") {
          this.userName = res.data.data.userName;
        }
      });
    },

    // 高级搜索
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    //重置
    reset() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
      };
      this.pagination.current = 1;
      this.dateRangeTJ = [];
      this.dateRangeHF = [];
      this.newQueryForm.status = undefined;
      this.newQueryForm.userName = undefined;
      this.newQueryForm.receiveUserName = undefined;
      this.fetchData();
    },
    // 时间选择
    onTimeChangeTJ(val) {
      // console.log("🤗🤗🤗, val =>", val);
      this.listQuery.submitStartTime = val[0];
      this.listQuery.submitEndTime = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },

    // 时间选择
    onTimeChangeHF(val) {
      // console.log("🤗🤗🤗, val =>", val); receiveTime
      this.listQuery.receiveStartTime = val[0];
      this.listQuery.receiveEndTime = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },
    // 编辑页面时
    handlerRedact(id) {
      this.editShow = true;
      this.titleText = "编辑数据";
      instance_1({
        method: "get",
        url: "/memberComment/details",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          id: id,
        },
      }).then((data) => {
        let {
          subject,
          address,
          commentCategory,
          generalComments,
          attachmentList,
        } = data.data.data;
        // 回显
        this.address = address;
        this.commentCategory = commentCategory;
        this.comment = generalComments;
        this.content = subject;
        this.dialogFormVisible = true;
        this.cityItems.forEach((item, index) => {
          if (item.title == commentCategory) {
            this.isColor = index;
          }
        });
        if (attachmentList.length > 0) {
          attachmentList.forEach((item) => {
            if (item.type == "2") {
              this.qitaFujian.push({
                fileName: item.fileName,
                uid: item.id,
                type: "2",
                path: item.path,
              });
              this.fileValue.push({
                uid: item.id,
                name: item.fileName,
                url: item.path,
              });
            } else {
              this.getFile(item.path, item.type, item);
            }
          });
        }
      });
    },


    modalClose() {
      this.Close();
    },
    Close() {
      this.fileUrl = [];
      this.fileList = [];
      this.fujian = [];
      this.formData = {
        opinionContent: "",
        orgId: "",
        orgName: "",
        status: "0",
        id: "",
      };
      this.isColor = 0;
      this.isDisabled = false;
      this.dialogFormVisible = false;
    },

    callback(active) {
      this.active = active;
      // 切换
    },
    details(row) {
      this.$router
        .push({
          path: "/throughAdmin/details",
          query: {
            id: row.id,
            admin: true,
            isLook: true,
          },
        })
        .catch(() => {});
    },
    // 点击行
    clickRow(record) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            if ("__vue__" in event.target) {
              // this.handleEdit(record);
            }
          },
        },
      };
    },
    //获取勾选的行数据
    setSelectRows(val) {
      this.selectRows = val;
    },
    rowClick(row, column, event) {
      if (column.label !== "操作") {
        this.handleEdit(row);
      }
    },
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    // 导出
    download() {
      this.downloadLoading = true;
      var arr = [];
      if (this.selectRows.length != 0) {
        // return this.$message.warning("请选择数据！");
        arr = this.selectRows.map((item) => item.id);
      }
      let data =
        arr.length == 0
          ? { ...this.newQueryForm, ...this.listQuery, flag: 2 }
          : { ids: arr, flag: 2 };
      instance_1({
        url: "directTrain/exportWord",
        method: "post",
        responseType: "blob",
        // params: this.listQuery,
        data: data,
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        let fileName = "";
        // if (this.selectRows.length > 1) {

        // } else {
        //   // var name=null;
        //   // this.selectRows[0].members.forEach((item,index)=>{
        //   //    name? name+='、'+item.userName:name=item.userName;
        //   // // name? name+=','+item.userName.replace('*',"x") :name=item.userName.replace('*',"x");
        //   // })
        //   fileName = `社情民意专报登记表.docx`;
        // }
        fileName = `社情民意专报登记表.zip`;
        a.download = fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000);
      });
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.listQuery.pageNum = pageNum;
      this.listQuery.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.listQuery.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    //查询
    handleQuery() {
      this.drawer = false;
      this.listQuery.pageNum = 1;
      this.fetchData();
    },

    //重置查询参数
    handleClear() {
      if (this.$refs["drawerForm"] !== undefined) {
        this.$refs["drawerForm"].resetFields();
      }
    },

    handleEdit(row) {
      let that = this;
      that.editShow = true;
      //    that.formData={
      //   opinionContent: "",
      //   orgId: "",
      //   orgName: "",
      //   status: "0",
      //   sfTjgllz:['0'],
      // },
      instance_1({
        method: "get",
        url: "/directTrain/getById",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          id: row.id,
        },
      }).then((data) => {
        if (data.data.code == "0000") {
          that.titleText = "编辑数据";
          let {
            opinionContent,
            orgId,
            orgName,
            status,
            userName,
            title,
            sfTjgllz,
            directTrainAttachments,
          } = data.data.data;
          that.formData.id = row.id;
          that.formData.orgId = orgId;
          that.formData.orgName = orgName;
          that.formData.opinionContent = opinionContent;
          that.formData.status = status;
          that.userName = userName;
          that.formData.title = title;
          // var  arr=[];
          // arr[0]=sfTjgllz.toString();
          var arr = [...sfTjgllz.toString()];
          //  that.$set(that.formData,sfTjgllz,arr);
          that.formData.sfTjgllz = arr;

          that.fileList = directTrainAttachments;
          that.formData.directTrainAttachments = directTrainAttachments;
          that.$forceUpdate();
          console.log(this.fileList, this.fileList.length);

          that.cityItems.forEach((item, index) => {
            if (item.title == orgName) {
              that.isColor = index;
            }
          });
          var filePath = directTrainAttachments;
          if (filePath) {
            this.fujian = [];
            filePath.forEach((filePath) => {
              this.fileUrl.push({
                uid: "-1",
                newName: filePath.fileName,
                name: filePath.fileName,
                url: filePath.path,
              });
              this.fujian.push({
                newName: filePath.newName,
                fileName: filePath.fileName,
                path: filePath.path,
                type: 2,
              });
            });
          }
          console.log(this.fujian, this.fileUrl);
          that.IShow = true;
          that.dialogFormVisible = true;
        }
      });
    },


    //查询列表数据
    fetchData() {
      this.listLoading = true;
        this.directTrainListData();
    },

    directTrainListData() {
      console.log("------>",this.listQuery);
      let that = this;
      directTrainList(this.listQuery).then((response) => {
        that.list = response.data.rows;
        that.total = response.data.total;
        that.pagination.total = response.data.total;
        that.listLoading = false;
        console.log("----list-->",that.list);
      });
    },
    onprogressStatus(value) {
      this.progressStatus = value.target.value;
    },
    fnColor(index, item) {
      this.formData.orgId = item.id;
      this.formData.orgName = item.title;
      this.isColor = index;
    },

    checkPermission,
  },
};
</script>
<style lang="scss" scoped>
.tableLimit tr td .cell {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  /*可以显示的行数，超出部分用...表示 */
  -webkit-box-orient: vertical !important;
}

.tableLimit .el-button--small {
  padding: 0 !important;
}

.Data-value {
  display: flex;
  line-height: 34px;
  margin-top: 10px;

  .title {
    // font-size: 14px;
    @include add-size($font_size_16);
    font-family: PingFang-M;
  }

  .contentBox {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    height: 130px;

    .contents {
      height: 30px;
      line-height: 30px;
      margin-left: 10px;
      text-align: center;
      border-radius: 6px;
      border: 1px solid rgb(214, 208, 208);
    }
  }
}

.textarea {
  margin-left: 10px;
  width: 400px;
}

.bgColor {
  background-color: #cc3031;
  color: #fff;
}

.upload-list-inline >>> .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-right: 8px;
}

.upload-list-inline >>> .ant-upload-animate-enter {
  animation-name: uploadAnimateInlineIn;
}

.upload-list-inline >>> .ant-upload-animate-leave {
  animation-name: uploadAnimateInlineOut;
}

.ant-upload-picture-card-wrapper {
  display: flex;
}
</style>
