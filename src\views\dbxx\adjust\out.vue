<template>
  <div class="table table-container">
    <SearchForm @onReset="clearQueryForm" @onSearch="search" :noMore="true">
      <template v-slot:topSearch>
        <DhJcCascade v-model="queryForm" allow-clear is-dbt @load="search"  :levelRoleMainIdentify="levelRoleMainIdentify" :isShowDbt="false" />
        <SingleSearch :value.sync="queryForm.userName" :title="'代表姓名'"/>
      </template>
    </SearchForm> 
    <a-spin :spinning="listLoading" :indicator="indicator">
      <a-table
        :columns="columns"
        :data-source="data"
        :pagination="pagination"
        :scroll="{ x: 300, y: 0 }"
      >
        <template slot="name" slot-scope="name"
          >{{ name.first }} {{ name.last }}</template
        >
      </a-table>
    </a-spin>
  </div>
</template>

<script>
import { myPagination } from "@/mixins/pagination.js";
import { getDutyAdjustList } from "@/api/delegation";
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
import SingleSelect from '@/components/SingleSelect/index';
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import { DBLZ_DBXXGL } from "@/utils/enum/levelRoleMainIdentifyEnum";
export default {
  mixins: [myPagination],
  components: {
    SearchForm,
    SingleSearch,
    SingleSelect,
    DhJcCascade
  },
  data() {
    return {
      levelRoleMainIdentify: DBLZ_DBXXGL,
      listLoading: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      data: [],
      queryPage: {
        pageNum: 1,
        pageSize: 10,
      },
      queryForm: {
        adjustTypes: [3], //写死是调出的类型
        jcDm: '',
        dhDm: '',
        userName: ''
      },
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      loading: false,
      // 列表
      columns: [
        {
          title: "届次",
          ellipsis: true,
          align: "center",
          dataIndex: "levelName",
        },
        {
          title: "代表姓名",
          ellipsis: true,
          align: "center",
          dataIndex: "userName",
        },
        {
          title: "调出机构",
          ellipsis: true,
          align: "center",
          dataIndex: "orgName",
        },
        {
          title: "调出部门",
          ellipsis: true,
          align: "center",
          dataIndex: "departmentName",
        },
        {
          title: "调出职位",
          ellipsis: true,
          align: "center",
          dataIndex: "positionName",
        },
        {
          title: "调整人部门名称",
          ellipsis: true,
          align: "center",
          dataIndex: "creatorOrgName",
        },
        {
          title: "调整人名称",
          ellipsis: true,
          align: "center",
          dataIndex: "creatorName",
        },
        {
          title: "调整时间",
          ellipsis: true,
          align: "center",
          dataIndex: "createTime",
        },
      ],
      meetingCode: '',
    };
  },
  mounted() {
    this.fetchData();
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "代表调整");
    this.$store.dispatch("navigation/breadcrumb2", "代表调入");
  },
  watch: {

  },
  methods: {
    search() {
      this.queryPage.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData()
    },
    fetchData() {
      this.listLoading = true;
      getDutyAdjustList(this.queryPage, this.queryForm).then((response) => {
        this.listLoading = false;
        this.data = response.rows;
        this.pagination.total = response.total;
      });
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      // 接口的数据条数rows，page页码
      this.queryPage.pageNum = pageNum;
      this.queryPage.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryPage.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    clearQueryForm() {
      this.queryPage.pageNum = 1
      this.queryPage.pageSize = 10
      this.queryForm.jcDm = ''
      this.queryForm.dhDm = ''
      this.queryForm.userName = ''
      this.fetchData();
    },
  },
};
</script>
<style scoped></style>
