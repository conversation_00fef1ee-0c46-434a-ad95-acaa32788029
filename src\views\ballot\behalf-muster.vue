<template>
  <div class="table-container">
    <!-- <a-row style="margin-left:1%">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18 }">
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="届次">
            <a-select placeholder="请选择届次"
                      v-model="queryForm.jcDm"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in getPeriodList"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="民族">
            <a-select placeholder="请选择民族"
                      v-model="queryForm.mzDm"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in getNationList"
                               :key="item.mzDm"
                               :value="item.mzDm">{{ item.mzmc }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="姓名"
                             prop="name">
            <a-input v-model="queryForm.dbxm"
                     autocomplete="off"
                     allow-clear
                     placeholder="请输入姓名"
                     v-on:keyup.enter="fetchData"></a-input>
          </a-form-model-item>
        </a-col>
      </a-form-model>

      <a-col :md="6"
             :sm="24">
        <span style="float: right; margin-top: 3px;">
          <a @click="toggleAdvanced"
             style="margin-right: 8px;">
            {{ advanced ? "收起" : "高级搜索" }}
            <a-icon :type="advanced ? 'up' : 'down'" />
          </a>
          <a-button type="primary"
                    @click="fetchData()">搜索</a-button>
          <a-button style="margin-left: 12px;"
                    @click="reset"
                    class="pinkBoutton">重置</a-button>

        </span>
      </a-col>
    </a-row>
    <a-row v-if="advanced"
           style="margin-left:1%">
      <a-col>
        <a-row>
          <a-form-model :label-col="{ span: 6 }"
                        :wrapper-col="{ span: 18 }"
                        ref="queryForm"
                        :model="queryForm"
                        layout="inline">
            // <a-col span="6"  >
               <a-form-model-item style="width: 100%"  label="民族">
                <a-select
                  placeholder="请选择民族"
                  v-model="queryForm.mzDm"
                  allow-clear
                    style="width: 100%" 
                >
                  <a-select-option
                    v-for="item in getNationList"
                    :key="item.mzDm"
                    :value="item.mzDm"
                  >{{ item.mzmc }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col> //

            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="推荐单位">
                <div style="display: flex;">
                  <a-select placeholder="请选择"
                            @change="onSjmelyDm"
                            v-model="queryForm.sjmelyDm"
                            style="width: 50%;">
                    <a-select-option v-for="item in getRecommendList"
                                     :key="item.melyDm"
                                     :value="item.melyDm">{{
                        item.melymc
                    }}</a-select-option>
                  </a-select>
                  <a-select placeholder="请选择"
                            v-model="queryForm.melyDm"
                            style="width: 50%">
                    <a-select-option v-for="item in onSjmelyDmList"
                                     :key="item.melyDm"
                                     :value="item.melyDm">{{
                        item.melymc
                    }}</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="综合构成">
                <a-select placeholder="请选择综合构成"
                          v-model="queryForm.getSynthesizeList"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getSynthesizeList"
                                   :key="item.zhgcDm"
                                   :value="item.zhgcDm">{{
                      item.zhgcmc
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="职业构成">
                <a-select placeholder="请选择职业构成"
                          v-model="queryForm.zygcDm"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getOccupationList"
                                   :key="item.zygcDm"
                                   :value="item.zygcDm">{{
                      item.zygcmc
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="所在联组">
                <a-select placeholder="请选择所在联组"
                          v-model="queryForm.dbt"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getDelegationList"
                                   :key="item.dbtDm"
                                   :value="item.dbtDm">{{ item.dbtmc
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="6"
                   class="overLength">
              <a-form-model-item style="width: 100%"
                                 label="全日制教育学历">
                <a-select placeholder="请选择全日制教育学历"
                          v-model="queryForm.xlDm"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getEducationStateList"
                                   :key="item.xlDm"
                                   :value="item.xlDm">{{
                      item.xlmc
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-form-model>
        </a-row>
      </a-col>
    </a-row> -->

    <SearchForm @onReset="reset" @onSearch="fetchData">
        <template v-slot:topSearch>
          <SingleSelect :title="'届次'" :selectList="getPeriodList"  :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm" />
          <SingleSelect :title="'民族'" :selectList="getNationList"  :showName="'mzmc'" :showValue="'mzDm'" :value.sync="queryForm.mzDm" />
          <SingleSearch @onEnter="fetchData" :title="'姓名'" :value.sync="queryForm.dbxm" />
        </template>
        <template v-slot:moreSearch>
          <UnitPicker :recommendValue.sync="queryForm.sjmelyDm" :sjmelyValue.sync="queryForm.melyDm" />
          <SingleSelect :title="'综合构成'" :selectList="getSynthesizeList" :showName="'zhgcmc'" :showValue="'zhgcDm'" :value.sync="queryForm.getSynthesizeList" />
          <SingleSelect :title="'职业构成'" :selectList="getOccupationList" :showName="'zygcmc'" :showValue="'zygcDm'" :value.sync="queryForm.zygcDm" />
          <SingleSelect :title="'所在联组'" :selectList="getDelegationList" :showName="'dbtmc'" :showValue="'dbtDm'" :value.sync="queryForm.dbt" />
          <SingleSelect :title="'全日制教育学历'" :selectList="getEducationStateList" :showName="'xlmc'" :showValue="'xlDm'" :value.sync="queryForm.xlDm" />
        </template>
      </SearchForm>

    <a-row style="margin: 10px 0px 10px 8px;">
      <a-col :span="6">
        <a-dropdown style="margin-left: 12px;">
          <a-menu slot="overlay">
            <a-menu-item key="1">
              <span @click="download">导出</span>
            </a-menu-item>
            <a-menu-item key="2">
              <span @click="downloadAll">导出全部</span>
            </a-menu-item>
          </a-menu>
          <a-button>
            导出
            <a-icon type="down" />
          </a-button>
        </a-dropdown>
      </a-col>
    </a-row>
    <a-row style="margin-top:20px">
      <standard-table :columns="columns"
                      rowKey="DB_ID"
                      :dataSource="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"
                      @selectedRowChange="onSelectChange"
                      @change="onChange"></standard-table>
    </a-row>
  </div>
</template>
<script>
import { myPagination } from "@/mixins/pagination.js";
import {
  preliminary,
  Secondary,
  getPeriod,
  getSex,
  getNation,
  getPolitical,
  getRecommend,
  getSynthesize,
  getOccupation,
  getEducationState,
  getDelegation
} from "@/api/election.js";
import StandardTable from "@/components/table/StandardTable";
import { instance_1 } from "@/api/axiosRq";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
import NumRangePicker from '@/components/NumRangePicker/index';
import UnitPicker from '@/components/UnitPicker/index';

export default {
  // 预备人选名册
  components: { 
    StandardTable,
    SingleSelect,
    SearchForm,
    SingleSearch,
    NumRangePicker,
    UnitPicker
  },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      jcDm: '',
      tableKey: [],
      tableData: [],
      onSjmelyDmList: [],
      advanced: false,
      periods: [
        // 议案届别
        { id: 16, name: "十五届六次" },
        { id: 15, name: "十五届一次" },
        { id: 1, name: "十五届二次" },
        { id: 2, name: "十五届三次" },
        { id: 3, name: "十五届四次" },
        { id: 4, name: "十五届五次" },
      ],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        jcDm: undefined,/* 届次id */
        dbxm: undefined,/* 代表姓名 */
        mzDm: undefined,/* 民族id */
        dbt: undefined,/* 代表团 */
        sjmelyDm: undefined,/* 上级名额来源id */
        melyDm: undefined,/* 名额来源名称 */
        zhgcDm: undefined,/* 综合构成id */
        zygcDm: undefined,/* 职业构成代码 */
        xlDm: undefined,/* 全日制学历代码 */
        zzxl: undefined,/* 在职学历代码 */
      },
      columns: [
        {
          title: "届次",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "LEVEL_NAME",
        },
        {
          title: "姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "USER_NAME",
          sorter: {
            compare: (a, b) => a.USER_NAME - b.USER_NAME,
            multiple: 1,
          },
        },
        {
          title: "性别",
          align: "center",
          width: 90,
          ellipsis: true,
          dataIndex: "SEX",
        },
        {
          title: "出生日期",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "BIRTHDAY",
          customRender: (text, record, index) => {
            // return text.replace("T", " ").split("Z").join("").substr(0, 19);
            return text.slice(0, text.indexOf("T")) || '/';
          }
        },

        {
          title: "民族",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "MZMC",
        },
        {
          title: "党派",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "POLITICS_STATUS_NAME",
        },
        {
          title: "户籍",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "PLACE_OF_DOMICILE",
        },
        {
          title: "工作单位及职务",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "GZDWJZW",
        },
        {
          title: "全日制教育学历",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "XLMC",
        },

        {
          title: "综合构成",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "ZHGCMC",
        },
        {
          title: "职业构成",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "ZYGCMC",
        },
        {
          title: "推荐单位",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "MELYMC",
        },
        {
          title: "所在联组",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "DBTMC",
        },
      ],
      dataSource: [],
      selectedRows: [],
      getPeriodList: [], /* 获取届次 */
      getSexList: [],/* 获取性别 */
      getNationList: [],/* 获取民族 */
      getDelegationList: [],  /* 代表团 */
      getPoliticalList: [],/* 获取出党派下拉数据列表 */
      getRecommendList: [],/* 获取出推荐单位下拉数据列表 */
      getSynthesizeList: [],/* 获取出综合构成下拉数据列表 */
      getOccupationList: [],/* 获取出职业构成下拉数据列表 */
      getEducationStateList: [],/* 获取在职教育学历、全日制教育学历下拉数据列表 */
      selectData: [ /*  是：1     否：0 */
        { id: '', name: '全部' },
        { id: '1', name: '是' },
        { id: '0', name: '否' }
      ]
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "预备人选管理");
    this.$store.dispatch("navigation/breadcrumb2", "代表名册");
    this.selectList()
  },
  methods: {
    //筛选
    onChange (pagination, filters, sorter, extra) {
      console.log(sorter.order, 'sorter');
      this.queryForm.order = sorter.order
      // this.queryForm.sort = 'xm'
      instance_1({
        url: "/representative/behalfRoll/search",
        method: "get",
        params: this.queryForm
      }).then((res) => {
        console.log(res, 'reesrs');
        if (res.data.code == '200') {
          this.dataSource = res.data.rows
          this.pagination.total = res.data.total
        }
      })
      // let params = {
      //   jcDm: this.queryForm.jcDm,
      //   userName: this.queryForm.userName,
      //   state: this.queryForm.state,
      //   order: sorter.order,
      //   pageSize: this.queryForm.pageSize,
      //   pageNum: this.queryForm.pageNum,
      // }
      // // this.queryForm.order = sorter.order
      // getselectCandidateApplication(params).then((res) => {
      //   console.log(res.data.rows);
      //   if (res.data.code == 200) {
      //     this.dataSource = res.data.rows;
      //     this.pagination.total = res.data.total;
      //   }
      // });
    },
    async selectList () {
      let res = await getPeriod()
      if (res.code == "0000") {
        this.getPeriodList = res.data
        this.queryForm.jcDm = res.data[2].jcDm
        this.jcDm = res.data[0].jcDm
      }
      let res1 = await getSex()
      if (res1.code == "0000") {
        this.getSexList = res1.data
      }
      let res2 = await getNation()
      if (res2.code == "0000") {
        this.getNationList = res2.data
      }
      let res3 = await getPolitical()
      if (res3.code == "0000") {
        this.getPoliticalList = res3.data
      }
      let res4 = await getRecommend()
      if (res4.code == "0000") {
        this.getRecommendList = res4.data
        this.getRecommendList.unshift({ melyDm: '', melymc: '全部' })
      }
      let res5 = await getSynthesize()
      if (res5.code == "0000") {
        this.getSynthesizeList = res5.data
      }
      let res6 = await getOccupation()
      if (res6.code == "0000") {
        this.getOccupationList = res6.data
      }
      let res7 = await getEducationState()
      if (res7.code == "0000") {
        this.getEducationStateList = res7.data
      }
      let res8 = await getDelegation()
      if (res8.code == "0000") {
        this.getDelegationList = res8.data
      }


      this.fetchData();
    },
    // 获取数据
    fetchData () {
      this.TBloading = true;

      instance_1({
        url: "/representative/behalfRoll/search",
        method: "get",
        params: this.queryForm
      }).then((res) => {
        if (res.data.code == '200') {
          this.dataSource = res.data.rows
          this.pagination.total = res.data.total;
          this.TBloading = false
        }
      })
    },
    // 导出
    download () {
      if (this.tableKey.length == "0") {
        this.$message.warning('您还暂未勾选数据');
      } else {
        let { jc_dm, level_name } = this.tableData[0]
        instance_1({
          url: "/representative/behalfRoll/export",
          method: "post",
          responseType: "blob",
          data: this.tableKey
        }).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `代表名册.xls`;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
        })
      }
    },
    downloadAll () {
      instance_1({
        url: "/representative/behalfRoll/exportAllBehalfRoll",
        method: "get",
        responseType: "blob",
        params: { jcDm: this.queryForm.jcDm }
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `代表名册.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      })
    },
    //  主下拉框
    async onSjmelyDm (id) {
      let res = await Secondary({ id })
      if (res.code == "0000") {
        this.onSjmelyDmList = res.data
        this.onSjmelyDmList.unshift({ melyDm: '', melymc: '全部' })
        this.queryForm.melyDm = this.onSjmelyDmList[0].melyDm
      }
    },
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
      }
      this.queryForm.jcDm = this.jcDm
      this.fetchData();
    },
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    onSelectChange (key, data) {
      this.tableKey = key
      this.tableData = data
    }
  },
};
</script>
<style scoped>
.formBox {
  padding: 0 20px;
}
</style>
