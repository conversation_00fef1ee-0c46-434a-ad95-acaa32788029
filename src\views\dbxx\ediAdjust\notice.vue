<template>
  <div class="table-container">
    <ZTable :list="list"
            :columns="columns" />
  </div>
</template>

<script>
export default {
  data () {
    return {
      TBloading: false,
      list: [
        {
          thNum: "市级领导辖区视察",
          type: "2021.01.11",
          name: "林*强",
          noticeDate: "2021.01.11",
          sex: "男",
          birthdayTime: "1974.03.25",
          party: "中国共产党",
          education: "本科",
          unit: "xx技术有限公司 董事长",
          createTime: "2021.09.22",
        },
      ],
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "内容",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "thNum",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "创建时间",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "type",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.switMeeting(record);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleEdit(record);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "代表信息修改");
    this.$store.dispatch("navigation/breadcrumb2", "定期提醒更新");
  },
};
</script>
<style scoped></style>
