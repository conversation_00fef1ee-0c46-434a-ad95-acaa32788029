import request from "@/utils/request";
import qs from "qs";

export function getAllList() {
  return request({
    url: "/api/v1/meetingAccredit/getAllList",
    method: "post",
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/meetingAccredit/delAccredit",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/meetingAccredit/addAccredit",
    method: "post",
    data,
  });
}

export function doSavePatch(param, data) {
  return request({
    url: "/api/v1/meetingAccredit/addAccreditPatch/" + param,
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/meetingAccredit/updateAccredit",
    method: "post",
    data,
  });
}

