<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="24"
             :xl="24">
        <a-row>
          <a-form ref="queryForm"
                  :form="queryForm"
                  :model="queryForm"
                  layout="horizontal"
                  :label-col="{ span: 6 }"
                  :wrapper-col="{ span: 18, offset: 0 }">
            <!--  :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18, offset: 0 }" -->
            <div>
              <a-row style="margin-left: 1%;">
                <a-col :span="6">
                  <a-form-item label="活动名称"
                               prop="title">
                    <a-input v-model="queryForm.title"
                             allow-clear
                             placeholder="请输入活动名称"
                             @keyup.enter="handleQuery"
                             @change="$forceUpdate()" />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="活动类型"
                               prop="type">
                    <a-select v-model="queryForm.type"
                              allow-clear
                              placeholder="请选择活动类型"
                              filterable>
                      <a-select-option v-for="item in typeOptions"
                                       :key="item.type"
                                       :label="item.type"
                                       :value="item.type">{{ item.type }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="状态"
                               prop="status">
                    <a-select v-model="queryForm.status"
                              allow-clear
                              placeholder="请输入状态"
                              filterable
                              default-first-option
                              @change="$forceUpdate()">
                      <a-select-option v-for="item in stateOptions"
                                       :key="item.value"
                                       :label="item.label"
                                       :value="item.value">{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <span style="float: right; margin-top: 3px;">
                    <a-button type="primary"
                              @click="handleQuery">搜索</a-button>
                    <a-button style="margin-left: 12px;"
                              class="pinkBoutton"
                              @click="reset">重置</a-button>
                  </span>
                </a-col>
              </a-row>
            
              <a-row v-show="!isDb"
                     style="margin: 5px 0px 10px 8px;">
                <a-button type="primary"
                          native-type="submit"
                          @click="handleAdd">新增</a-button>
              </a-row>
            </div>
          </a-form>
        </a-row>

      </a-col>
    </a-row>
  </div>
</template>

<script>
import { instance_2 } from "@/api/axiosRq";
import QRCode from "qrcodejs2";
import Print from "vue-print-nb";
import Vue from "vue";
import moment from "moment";
import {
  ExportExcelData,
  getdetailsApi,
  getinsertByListApi,
  getStatisticsNum,
} from "@/api/representativeElection/candidateApi.js";
Vue.use(Print);
export default {
  name: "Table",
  components: {},

  filters: {
    // 0报名中 1待开始 2进行中 3已完成 4草稿 5初审 6终审 7退回
    getState: function (num) {
      if (num == 1) {
        return "报名中";
      } else if (num == 0) {
        return "草稿";
      } else if (num == 2) {
        return "待开始";
      } else if (num == 3) {
        return "进行中";
      } else if (num == 4) {
        return "已完成";
      } else if (num == 5) {
        return "草稿";
      } else if (num == 6) {
        return "初审";
      } else if (num == 7) {
        return "终审";
      } else if (num == 8) {
        return "退回";
      }
    },
  },
  data () {
    return {
       DateDay: new Date(new Date()),
      TBloading: false,
      orgNameList: [],//变动的组织单位列表
      dateRange: [],//时间范围
      dateRangeBM: [],//报名时间范围
      dbCQQK: false,
      columnsCQQK: [
        {
          title: "签到日期",
          dataIndex: "date_day",
          width: 200,
          align: "center",
        },
        {
          title: "时间",
          dataIndex: "data_type",
          width: 200,
          align: "center",
        },
        {
          title: "出勤情况",
          dataIndex: "isSignIn",
          width: 200,
          align: "center",
          customRender: (text, record, index) => {
            return record.isSignIn ? "已签到" : "未签到";
          },
        },
      ],
      dbBMXX: false,
      columnsBMXX: [
        {
          title: "日期",
          dataIndex: "date",
          width: 200,
          align: "center",
        },
        {
          title: "时间",
          dataIndex: "timeInterval",
          width: 200,
          align: "center",
        },
        {
          title: "出勤情况",
          dataIndex: "attend",
          width: 200,
          align: "center",
          customRender: (text, record, index) => {
            return record.attend ? "出席" : "请假";
          },
        },
      ],
      recordList: [
        { attend: 1, name: "出席" },
        { attend: 0, name: "请假" },
      ],
      meetVisibl: false,
      listPlan1: [],
      listPlan2: [],
      isTiaoZhuang: false,
      activityMemberSignedSectionList: [],
      newListPlan1: [],
      getListPlan: [],
      gethuodongId: null,
      userIdOf: [],
      meetVisible: false,
      isDb: 0,
      tongbuxx: false,
      baomingxx: false,
      baomingxxCX: false,
      chuqingqk: false,
      erweiM: false,
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,
      listLoading: false,
      columnsOne: [
        {
          title: "序号",
          width: 100,
          align: "center",
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "所在代表团",
          dataIndex: "depName",
          width: 200,
          align: "center",
        },
        {
          title: "姓名",
          width: 200,
          dataIndex: "userName",
          align: "center",
        },
      ],
      dataSourceOne: [],
      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "当前状态",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            if (text == 1) {
              return "报名中";
            } else if (text == 0) {
              return "草稿";
            } else if (text == 2) {
              return "待开始";
            } else if (text == 3) {
              return "进行中";
            } else if (text == 4) {
              return "已完成";
            } else if (text == 6) {
              return "初审";
            } else if (text == 7) {
              return "终审";
            } else if (text == 8) {
              return "退回";
            } else if (text == 9) {
              return "未开始报名";
            } else {
              return "/";
            }
          },
        },
        {
          title: "活动类型",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "type",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "活动名称",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "title",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "组织单位",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "orgName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "活动开始时间",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "startTime",
          customRender: (text, record, index) => {
            return this.$moment(text).format("YYYY-MM-DD") || "/";
          },
        },
        {
          title: "活动结束时间",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "endTime",
          customRender: (text, record, index) => {
            return this.$moment(text).format("YYYY-MM-DD") || "/";
          },
        },
        {
          title: "报名开始时间",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "signUpStart",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "报名结束时间",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "signUpEnd",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "参加人数",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "memberNumber",
          customRender: (text, record, index) => {
            return record.signInfo.signed || "/";
          },
        },
        {
          title: "录入时间",
          align: "center",
          width: 250,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.createTime || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 260,
          scopedSlots: { customRender: "operation" },
          // customRender: (text, record, index) => {
          //   let h = this.$createElement;
          //   return h("div", [
          //     h(
          //       "span",
          //       {
          //         attrs: {
          //           type: "text",
          //         },
          //         style: {
          //           cursor: "pointer",
          //           marginLeft: "14px",
          //           color: "#DB3046",
          //         },
          //         on: {
          //           click: () => {
          //             this.Details(record.id);
          //           },
          //         },
          //       },
          //       "详情"
          //     ),
          //     h(
          //       "span",
          //       {
          //         attrs: {
          //           type: "text",
          //         },
          //         style: {
          //           cursor: "pointer",
          //           marginLeft: "14px",
          //           color: "#DB3046",
          //         },
          //         on: {
          //           click: () => {
          //             this.Qiandao(record.id);
          //           },
          //         },
          //       },
          //       "报名信息"
          //     ),
          //     h(
          //       "span",
          //       {
          //         attrs: {
          //           type: "text",
          //         },
          //         style: {
          //           cursor: "pointer",
          //           marginLeft: "14px",
          //           color: "#DB3046",
          //         },
          //         on: {
          //           click: () => {
          //             this.NewDetails(record.id);
          //           },
          //         },
          //       },
          //       "编辑"
          //     ),
          //     h(
          //       "span",
          //       {
          //         attrs: {
          //           type: "text",
          //         },
          //         style: {
          //           cursor: "pointer",
          //           marginLeft: "14px",
          //           color: "#DB3046",
          //         },
          //         on: {
          //           click: () => {
          //             this.toDetailTable(record.id);
          //           },
          //         },
          //       },
          //       "出勤情况"
          //     ),
          //     h(
          //       "span",
          //       {
          //         attrs: {
          //           type: "text",
          //         },
          //         style: {
          //           cursor: "pointer",
          //           marginLeft: "14px",
          //           color: "#409eff",
          //         },
          //         on: {
          //           click: () => {
          //             this.qrCodeOpen(record.id, record.title);
          //           },
          //         },
          //       },
          //       "二维码"
          //     ),
          //   ]);
          // },
        },
      ],
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      stateOptions: [
        {
          label: "草稿",
          value: "0",
        },
        {
          label: "报名中",
          value: "1",
        },
        {
          label: "待开始",
          value: "2",
        },
        {
          label: "进行中",
          value: "3",
        },
        {
          label: "已完成",
          value: "4",
        },
        // {
        //   label: "初审",
        //   value: "6",
        // },
        // {
        //   label: "终审",
        //   value: "7",
        // },
        // {
        //   label: "退回",
        //   value: "8",
        // },
        {
          label: "未开始报名",
          value: "9",
        },
        // {
        //   label: "草稿",
        //   value: 0,
        // },
      ],
      drawer: false,
      qrCodeTitle: "",
      formData: {},
      visibleBox: false,
      titleBox: "",
      qrCodeDialogVisible: false,
      qrCode: "",
      badgeHide: 10,
      drawerHeight: window.innerHeight - 75 + "px",
      isDownload: false,
      currentCodeName: "",
      elementLoadingText: "正在加载...",
      list: [],
      advanced: false,
      queryForm: {
        addType: 1,
        pageSize: 10,
        pageNum: 1,
        type: undefined,
        title: "",
        status: undefined,
        meetingTitle: null,
        meetingStatus: null,
        startTime: null,
        endTime: null,
        enrollTime: null,
        signUpStart: null,
        signUpEnd: null,
      },
      userName: "",
      userId: "",
      typeOptions: [],
      codeList: [],
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 20,
      background: true,
      selectRows: "",
      value2: "",
      baseUrl: "",
      // 弹出框
      dialogVisible: false,
      labelPosition: "right",
      formLabelAlign: {
        name: "",
        region: "",
        type: "",
      },
      dialogVisible_2: false,
      formLabelAlign_2: {
        name: "",
        region: "",
        type: "",
      },
      upData: "",
      activityMemberSignedSectionList: [],
      activityId: "",
      listactive: {},
      activityNeedList: "",
    };
  },
  computed: {
    conditionsCount: function () {
      let size = 0;
      console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
      for (const key in this.queryForm) {
        if (Object.hasOwnProperty.call(this.queryForm, key)) {
          if (key == "pageSize" || key == "pageNum" || key == "addType") {
            continue;
          }
          const element = this.queryForm[key];
          if (
            Array.isArray(element)
              ? element.length > 0
              : element !== "" && element != null
          ) {
            size++;
          }
        }
      }
      return size;
    },
    routeList () {
      return this.$route;
    },
  },
  watch: {
    conditionsCount (newVal, oldVal) {
      // console.log("🤗🤗🤗, newVal =>", newVal);
      this.badgeHide = newVal <= 0;
    },
    routeList (newVal, oldVal) {
      if (newVal.query.STARE) {
        this.isTz();
      } else {
        //如果不是代表就去除所有限制
        if (JSON.parse(sessionStorage.getItem("isDb")) == "0") {
          this.meetVisible = false;
          this.isDb = 0;
          this.tongbuxx = false;
          this.baomingxx = false;
          this.baomingxxCX = false;
          this.chuqingqk = false;
          this.erweiM = false;
          this.isTiaoZhuang = false;
        }
      }
    },
  },
  created () {
    this.DateDay = moment(this.DateDay).format('YYYY-MM-DD HH:mm')
    console.log('🤗🤗🤗, this.DateDay  =>', this.DateDay )
    this.isTz();
    let queryForm = JSON.parse(localStorage.getItem("tongjifenzi"));
    if (queryForm) {
      this.queryForm = queryForm;
      this.pagination.current = this.queryForm.pageNum;
      // 回显时间
      if (this.queryForm.startTime) {
        var dateRanglist =
          this.queryForm.startTime + "," + this.queryForm.endTime;
        this.dateRange = dateRanglist.split(",");
      }
      // 回显时间
      if (this.queryForm.signUpStart) {
        var dateRanglistBM =
          this.queryForm.signUpStart + "," + this.queryForm.signUpEnd;
        this.dateRangeBM = dateRanglistBM.split(",");
      }

    }
    this.userId = localStorage.getItem("userId");
    this.userName = localStorage.getItem("userName");

    this.acquireData(this.page, this.size);
    this.getactivityType();
    this.$store.dispatch("navigation/breadcrumb1", "活动通知");
    this.$store.dispatch("navigation/breadcrumb2", "新建通知");
  },
  beforeDestroy () {
    window.localStorage.setItem("tongjifenzi", JSON.stringify(this.queryForm));
    this.isDb = 0;
    this.tongbuxx = false;
    this.baomingxx = false;
    this.chuqingqk = false;
    this.erweiM = false;
  },

  mounted () {
    // 抽屉滚动区高度
    window.onresize = () => {
      return (() => {
        this.drawerHeight = window.innerHeight - 75 + "px";
      })();
    };
  },

  methods: {
    isTz () {
    

      let { isDb, STARE } = this.$route.query;
      // 如果是代表跳转过来 换个接口
      if (STARE == 'cdbj') {
        this.isDb = 1;
        this.columns.splice(this.columns.length - 3, 1); //移除参加人数
        this.$forceUpdate();
      } else if (STARE == 'cgzryj') {
        this.isDb = 0;
      } else if (STARE == undefined) {
        JSON.parse(sessionStorage.getItem("isDb")) == "0" ? this.isDb = 0 : this.isDb = 1;
      }


      if (this.$route.query.Type) {
      console.log('🤗🤗🤗, this.$route.query.Type =>', this.$route.query.Type)
        var typeList = this.$route.query.Type.split(",");
        console.log('🤗🤗🤗, typeList =>', typeList)
        this.isTiaoZhuang = true;
        typeList.map((item) => {
          switch (item) {
            case "同步信息":
              this.tongbuxx = true;
              break;
            case "报名信息":
              this.baomingxx = true;
              break;
            case "报名信息查看":
              this.baomingxxCX = true;
              break;
            case "出缺情况":
              this.chuqingqk = true;
              break;
            case "二维码":
              this.erweiM = true;
              break;
            case "同步信息":
              this.tongbuxx = true;
              break;
          }
        });
      }

    },
    moment,
    // 高级搜索
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    //重置
    reset () {
      this.queryForm = {
        addType: 1,
        pageSize: 10,
        pageNum: 1,
        type: undefined,
        title: "",
        status: undefined,
        meetingTitle: null,
        meetingStatus: null,
        startTime: null,
        endTime: null,
        enrollTime: null,
        signUpStart: null,
        signUpEnd: null,
      };
      this.dateRange = [];
      this.dateRangeBM = [];
      this.handleQuery();
    },
    // 时间选择
    onTimeChange (val) {
      // console.log("🤗🤗🤗, val =>", val);
      this.queryForm.startTime = val[0];
      this.queryForm.endTime = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },
    // 时间选择
    onTimeChangeBM (val) {
      // console.log("🤗🤗🤗, val =>", val);
      this.queryForm.signUpStart = val[0];
      this.queryForm.signUpEnd = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },
    // 点击行
    clickRow (event, record) {
      try {
        let state;
        let tableHeader = event.target.__vue__.column.key;
        tableHeader ? (state = true) : (state = false);
        if (tableHeader) {
          this.Details(record.id);
        }
      } catch (error) { }
    },
    // 切换页数
    changePageSize (current, pageSize) {
      this.queryForm.pageNum = current; //queryForm.page是页码
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = current;
      this.acquireData();
    },

    // 切换页码
    handleCurrentChange (current, pageSize) {
      this.queryForm.pageNum = current;
      this.pagination.current = current;
      this.acquireData();
    },

    // 多选选择
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },

    //关闭弹框清除二维码
    qrCodeHandleClose () {
      this.qrCodeDialogVisible = false;
      this.qrCode = "";
      this.qrCodeTitle = "";
      document.getElementById("qrCode").innerHTML = "";
    },
    //创建二维码
    qrcode (url) {
      this.qrCode = new QRCode("qrCode", {
        text: url,
        width: 400,
        height: 400,
      });
    },
    qrCodeCreate () {
      let url = this.qrCode;
      this.$nextTick(() => {
        this.qrcode(url);
      });
    },
    //二维码显示
    qrCodeOpen (val, title) {
      this.qrCodeDialogVisible = true;
      this.qrCodeTitle = title;
      this.qrCode =
        // Vue.prototype.GLOBAL.basePath_2 +
        "activityMemberSignedSection/activitySignIn?activityId=" + val;
      this.qrCodeCreate();
    },
    // 同步信息
    tongbu (id) {
      instance_2({
        method: "post",
        url: "/activitySyn/selectSyn",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: { activityId: id },
      })
        .then((data) => {
          if (data.data.msg) {
            this.$message.info(data.data.msg);
          } else {
            this.visibleBox = true;
            this.formData = data.data;
            let current = this.formData.count.length;
            this.titleBox = `是否将 ${current} 位代表签到信息同步到履职登记?`;
            this.dataSourceOne = this.formData.count;
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 同步信息 确定
    tongCreate () {
      const theme = localStorage.getItem("BYUI-VUE-THEME");
      //  本地
      if (theme) {
        this.$router.push({
          path: "/delegate/newAddRegister",
          query: {
            dutyActivity: JSON.stringify(this.formData.dutyActivity),
            Ours: JSON.stringify(this.formData.Ours),
          },
        });
      } else {
        let crossSystem = {
          dutyActivity: this.formData.dutyActivity,
          Ours: this.formData.Ours,
        };
        window.localStorage.setItem("crossSystem", JSON.stringify(crossSystem));
        let locationSystem = {
          path: this.$router.history.current.path,
          systemCode: this.$router.history.current.meta.systemId,
        };
        sessionStorage.setItem(
          "locationSystem",
          JSON.stringify(locationSystem)
        );
        this.$nextTick(() => {
          window.top.postMessage(
            {
              event: "locationSystem", // 约定的消息事件
              args: {
                path: "newAddRegister", //路径
                systemCode: "lzdjgl", //子系统编码
              }, // 参数
            },
            "*"
          );
        });
      }
    },
    tongHandleClose () {
      this.visibleBox = false;
    },
    // 打开表格弹窗
    toDetailTable (id) {
      this.$router.push({
        path: "/huoDong/attendance",
        query: { id: id },
      });
    },
    // 代表查看出勤
    DbGRtoDetailTable (signedId, type) {
      instance_2({
        method: "get",
        url: "/activitySignedMember/details",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          id: signedId,
          // id: this.$route.query.id
        },
      }).then((res) => {
        let gg = res.data.data;
        if (gg.activitySignedMember.length !== 0) {
          this.upData = gg.activitySignedMember.id;
        }
        this.activityMemberSignedSectionList =
          gg.activitySignedMember.activityMemberSignedSectionList;

        this.activityMemberSignedSectionList.forEach((e, i) => {
          // let date = new Date(e.date);
          // var h = this.formatters(e.date);
          if (e.type == 0) {
            e.data_type = "上午";
          } else if (e.type == 1) {
            e.data_type = "下午";
          } else if (e.type == 2) {
            e.data_type = "晚上";
          }

          var num = e.date.indexOf(" ");
          e.date_day = e.date.substr(0, num);
        });
        this.$forceUpdate();

        this.activityId = gg.activity.id;
        this.yibao = true;
        this.listactive = {
          address: gg.activity.address,
          content: gg.activity.content,
          endTime: gg.activity.endTime,
          signUpEnd: gg.activity.signUpEnd,
          startTime: gg.activity.startTime,
          nature: gg.activity.nature,
          type: gg.activity.type,
          title: gg.activity.title,
          orgName: gg.activity.orgName,
          activitySectionList: gg.activity.activitySectionList,
          activityInviteRangeList: gg.activity.activityInviteRangeList,
          need: gg.activitySignedMember.activityMemberNeedList,
          answerNotes: gg.activitySignedMember.answerNotes,
          activityMemberSignedSectionList:
            gg.activitySignedMember.activityMemberSignedSectionList,
          attachmentList: gg.activity.attachmentList,
        };
        this.activityNeedList = gg.activitySignedMember.activityMemberNeedList;

        if (type == "代表出勤情况") {
          this.dbCQQK = true;
        } else {
          this.dbBMXX = true;
        }
      });
    },
    updateClosedbCQQK () {
      this.dbCQQK = false;
    },
    updateClosedbBMXX () {
      this.dbBMXX = false;
    },
    formatter (thistime, fmt) {
      let $this = new Date(thistime);
      let o = {
        "M+": $this.getMonth() + 1,
        "d+": $this.getDate(),
        "h+": $this.getHours(),
        "m+": $this.getMinutes(),
        "s+": $this.getSeconds(),
        "q+": Math.floor(($this.getMonth() + 3) / 3),
        S: $this.getMilliseconds(),
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          ($this.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
      }
      for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length === 1
              ? o[k]
              : ("00" + o[k]).substr(("" + o[k]).length)
          );
        }
      }
      return fmt;
    },
    tableSortChange (column) { },
    setSelectRows (val) {
      this.selectRows = val;
    },
    rowClick (row, column, event) {
      if (column.label !== "操作") {
        this.Details(row.id);
      }
    },

    handleSizeChange (val) {
      this.size = val;
      this.acquireData(this.page, this.size);
    },
    // handleCurrentChange(val) {
    //   this.page = val;
    //   this.acquireData(this.page, this.size);
    // },
    handleQuery () {
      this.drawer = false;
      this.queryForm.pageNum = 1;
      // this.fetchData();
      this.acquireData();
    },
    handleClear () {
      this.queryForm.title = "";
      this.queryForm.status = "";
      this.queryForm.startTime = "";
      this.queryForm.endTime = "";
      this.queryForm.enrollTime = "";
      this.queryForm.signUpStart = "";
      this.queryForm.signUpEnd = "";
    },
    // 获取类型
    getactivityType: function () {
      instance_2({
        method: "get",
        url: "/activityType/list",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {},
      })
        .then((data) => {
          this.typeOptions = data.data.data;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //回复
    huifu: function (id) {
      this.dialogVisible = true;
    },
    //结束活动
    EndActiviy: function (id) {
      this.dialogVisible_2 = true;
    },
    handleAdd () {
      // console.log(id)
      this.$router
        .push({
          path: "/huoDong/add",
          query: {},
        })
        .catch(() => { });
    },
    Details (id) {
      console.log(id);
      this.$router
        .push({
          path: "/huoDong/edit",
          query: {
            id: id,
          },
        })
        .catch(() => { });
    },
    NewDetails (id) {
      console.log(id);
      this.$router
        .push({
          path: "/huoDong/Newedit",
          query: {
            id: id,
          },
        })
        .catch(() => { });
    },
    Qiandao (id, baomingxxCX) {
      console.log(id);
      this.$router
        .push({
          path: "/huoDong/baoming",
          query: {
            id: id,
            baomingxxCX: baomingxxCX,
          },
        })
        .catch(() => { });
    },
    // 代表报名
    DBbaoming (id) {
      this.gethuodongId = id;
      getdetailsApi(id).then((res) => {
        if (res.data.code == "0000") {
          if (res.data.data.activitySectionList.length < 1) {
            return this.$message.warning("暂无节次!");
          }
          this.listPlan1 = res.data.data.activitySectionList;
          let listPlan2 = res.data.data.activitySectionList;
          this.listPlan2[0] = listPlan2;
          // this.listPlan2 = res.data.data.activityInviteRangeList
          this.ids = res.data.data.id;
          this.newListPlan1 = JSON.parse(
            JSON.stringify(res.data.data.activitySectionList)
          );
          this.newListPlan1 = this.newListPlan1.map((item) => {
            return {
              attend: null,
              id: item.id,
            };
          });
          this.meetVisible = true;
        }
      });
    },
    // 选择出、缺席
    changeUserIn (attend, item) {
      this.newListPlan1.forEach((son) => {
        if (son.id == item.id) {
          son.attend = attend;
          son.sectionId = item.id;
        }
      });
      this.newListPlan1 = this.newListPlan1;
      this.activityMemberSignedSectionList = this.newListPlan1;
    },
    //取消
    updateClose () {
      this.meetVisible = false;
      this.activityMemberSignedSectionList = [];
    },

    //确定
    batchConfirmUpdate () {
      let iShow = false;
      this.newListPlan1.forEach((item) => {
        if (item.attend == null) {
          iShow = false;
        } else {
          iShow = true;
        }
      });
      if (iShow) {
        let userId = {};
        userId.activityId = this.gethuodongId;
        userId.userId = this.userId;
        userId.activityMemberSignedSectionList = this.activityMemberSignedSectionList.map(
          (item) => {
            return {
              attend: item.attend,
              sectionId: item.id,
            };
          }
        );
        // console.log(this.userId, userId, this.userIdOf, "------")
        this.userIdOf.push(userId);
        getinsertByListApi(this.userIdOf).then((res) => {
          if (res.data.code == "0000") {
            this.$message.success(res.data.msg);
            this.meetVisible = false;
            this.activityMemberSignedSectionList = [];
            this.userIdOf = [];
            this.handleQuery(); //刷新
          } else {
            this.$message.warning(res.data.msg);
          }
        });
      } else {
        console.log(this.newListPlan1);
        this.$message.warning("请选择全部数据!");
      }
    },
    //删除
    handleDelete (id) {
      // console.log("----", id);
      if (id) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          instance_2({
            method: "get",
            url: "/activity/deleteByid",
            params: {
              id: id,
            },
          }).then((res) => {
            if (res.data.code == "0000") {
              this.$baseMessage("删除成功", "success");
              this.acquireData();
            } else {
              this.$baseMessage(res.data.msg, "success");
            }
          });
        });
      } else {
        //批量删除，暂无接口
        if (this.selectRows.length > 0) {
          const ids = this.selectRows.map((item) => item.id);
          this.$baseConfirm("你确定要删除选中项吗", null, () => { });
        } else {
          this.$baseMessage("未选中任何行", "error");
          return false;
        }
      }
    },

    // 获取列表data 管理员和代表
    acquireData: function (page, size) {
      this.listLoading = true;
      //代表查看到信息
      if (this.isDb == 1) {
        instance_2({
          method: "get",
          url: "/activityMember/list",
          headers: {
            "Content-type": "application/x-www-form-urlencoded",
          },
          params: {
            userId: this.userId,
            addType: 1,
            ...this.queryForm,
            isSigned: this.$route.query.isSigned,
          },
        }).then((res) => {
          // console.log(res, "代表数据---------")
          if (res.data.code == "200") {
            if (res.data.rows.length == 0) {
              this.list = [];
            } else {
              this.list = res.data.rows;
              this.total = res.data.total;
              // 设置总数
              this.pagination.total = res.data.total;
              this.FilterOrgNameArray(this.list);

            }
            this.listLoading = false;
          } else {
            this.listLoading = false;
          }
        });
      } else {
        // 管理员查看到信息
        instance_2({
          method: "get",
          url: "/activity/list",
          headers: {
            "Content-type": "application/x-www-form-urlencoded",
          },
          params: this.queryForm,
        })
          .then((data) => {
            console.log("代表活动", data);
            this.list = data.data.rows;
            this.total = data.data.total;
            this.listLoading = false;
            // 清空
            this.selectedRowKeys = [];
            // 设置总数
            this.pagination.total = data.data.total;
            this.FilterOrgNameArray(this.list);
          })
          .catch((error) => {
            console.log(error);
          });
      }
    },
    // 生成 单位组织数组
    FilterOrgNameArray (array) {
      this.orgNameList = [{ name: '全部', value: '' }];
      let arr = []
      array.map((item) => {
        arr.push(item.orgName)
      })
      let filterarr = [... new Set(arr)];
      filterarr.map((item) => {
        this.orgNameList.push({ name: item, value: item })
      })
    },
    // 弹出层
    handleClose (done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => { });
    },
    // 日期
    // dateFormat(row, column, cellValue, index) {
    //   var date = row[column.property];
    //   if (date == undefined) {
    //     return "";
    //   }
    //   return moment(date).format("YYYY-MM-DD");
    // },
  },
};
</script>
<style>
/* IE 浏览器 */
.scrollbar {
  /*三角箭头的颜色*/
  scrollbar-arrow-color: rgb(71, 69, 69);
  /*滚动条滑块按钮的颜色*/
  scrollbar-face-color: #9999;
  /*滚动条整体颜色*/
  scrollbar-highlight-color: #9999;
  /*滚动条阴影*/
  scrollbar-shadow-color: #9999;
  /*滚动条轨道颜色*/
  scrollbar-track-color: #fff;
  /*滚动条3d亮色阴影边框的外观颜色——左边和上边的阴影色*/
  scrollbar-3dlight-color: #9999;
  /*滚动条3d暗色阴影边框的外观颜色——右边和下边的阴影色*/
  scrollbar-darkshadow-color: #9999;
  /*滚动条基准颜色*/
  scrollbar-base-color: #9999;
  border: 0px;
  border-radius: 10px !important;
}
</style>
<style lang="scss" scoped>
.codeTitle {
  @include add-size($font_size_16);
}

.code_btn {
  margin-bottom: 10px;
}

.p {
  display: flex;
  width: 100%;
}

.p > span {
  flex: 1;
}
</style>
