import Layout from "@/layouts";

// {
//   path: "staging",
//   name: "InformStaging",
//   component: () => import("@/views/meeting/testPage/index"),
//   meta: {
//     title: "待办任务",
//     permissions: ["I_XTGLY", "I_XXFBY"],
//   },
// },
// 代表信息管理
export default [
  {
    path: "/informationMage",
    component: Layout,
    redirect: "noRedirect",
    name: "informationMage",
    title: "代表信息管理",
    meta: {
      title: "首页",
    },
    children: [
      // {
      //   path: "workIndex",
      //   name: "workIndex",
      //   component: () =>
      //     import("@/views/onBehalfOfTheInformation/workIndex.vue"),
      //   meta: {
      //     title: "代表首页",
      //   },
      // },
      {
        path: "workIndex",
        name: "workIndex",
        component: () =>
          import("@/views/onBehalfOfTheInformation/workPageIndex.vue"),
        meta: {
          title: "代表首页",
        },
      },
      {
        path: "workerIndex",
        name: "workerIndex",
        component: () => import("@/views/dbxx/committeeindex/workerIndex"),
        meta: {
          title: "工作人员首页",
        },
        hidden: true,
      },
      {
        path: "informationIndex",
        name: "informationIndex",
        component: () => import("@/views/dbxx/committeeindex/informationIndex"),
        meta: {
          title: "工作人员首页",
        },
      },
    ],
  },
  {
    path: "/delegate",
    component: Layout,
    redirect: "noRedirect",
    name: "delegate",
    title: "代表信息管理",
    meta: {
      title: "代表信息",
    },
    children: [
      {
        path: "manage",
        name: "manage",
        component: () => import("@/views/meeting/delegate/manage"),
        meta: {
          title: "管理代表",
        },
      },
      {
        path: "manageQZJDB",
        name: "manageQZJDB",
        component: () => import("@/views/manageQZDBInfo/manageQZJDB.vue"),
        meta: {
          title: "管理区镇街代表",
        },
      },
      // 修改代表需要从树进去，无意义的菜单
      {
        path: "dbxxedit",
        name: "Dbxxedit",
        component: () => import("@/views/dbxx/edit"),
        meta: {
          title: "修改代表",
        },
        // hidden: true,
      },
      {
        path: "backlog",
        name: "backlog",
        component: () => import("@/views/meeting/delegate/backlog"),
        meta: {
          title: "待办事项",
        },
      },
      {
        path: "Handling",
        name: "Handling",
        component: () => import("@/views/meeting/delegate/Handling"),
        meta: {
          title: "已办事项",
        },
      },
      {
        path: "dynamic",
        name: "dynamic",
        component: () => import("@/views/meeting/delegate/dynamic"),
        meta: {
          title: "代表动态",
        },
      },
      {
        path: "tableIng",
        name: "tableIng",
        component: () => import("@/views/meeting/delegate/tableIng"),
        meta: {
          title: "表单查询",
        },
      },
      {
        path: "statisticsData",
        name: "statisticsData",
        component: () => import("@/views/meeting/delegate/statisticsData"),
        meta: {
          title: "统计报表",
        },
      },
      {
        path: "dbInfoList",
        name: "newStatisticsData",
        component: () => import("@/views/meeting/registerMange/dbInfoList"),
        meta: {
          title: "代表花名册",
        },
      },
      {
        path: "statisticsBasic",
        name: "statisticsBasic",
        component: () => import("@/views/meeting/delegate/statisticsBasic"),
        meta: {
          title: "代表基本情况统计表",
        },
        hidden: true,
      },
      {
        path: "representative",
        name: "representative",
        component: () => import("@/views/meeting/delegate/representative"),
        meta: {
          title: "代表变更代表团情况统计表",
        },
        hidden: true,
      },
      {
        path: "representativeChanage",
        name: "representativeChanage",
        component: () =>
          import("@/views/meeting/delegate/representativeChanage"),
        meta: {
          title: "代表状态变更情况统计表",
        },
        hidden: true,
      },
      {
        path: "roster",
        name: "roster",
        component: () => import("@/views/meeting/delegate/roster"),
        meta: {
          title: "代表花名册统计表",
        },
        hidden: true,
      },
      {
        path: "occupation",
        name: "occupation",
        component: () => import("@/views/meeting/delegate/occupation"),
        meta: {
          title: "代表职业构成统计图",
        },
        hidden: true,
      },
      {
        path: "category",
        name: "category",
        component: () => import("@/views/meeting/delegate/category"),
        meta: {
          title: "代表类别构成统计图",
        },
        hidden: true,
      },
      {
        path: "addRegister",
        name: "addRegister",
        // component: () => import("@/views/meeting/activityRegister/addRegister"),
        component: () => import("@/views/meeting/huoDong/newDetail"),
        meta: {
          title: "活动登记新增",
        },
        hidden: true,
      },
      {
        path: "newAddRegister",
        name: "newAddRegister",
        component: () =>
          import("@/views/meeting/activityRegister/newAddRegister"),
        meta: {
          title: "同步活动登记新增",
        },
        hidden: true,
      },
      {
        path: "syncRegisterNew",
        name: "syncRegisterNew",
        component: () =>
          import("@/views/meeting/huoDong/syncRegisterNew"),
        meta: {
          title: "同步活动登记新增syp",
        },
        hidden: true,
      },
      {
        path: "createDutyActivityForTenant",
        name: "createDutyActivityForTenant",
        component: () =>
          import(
            "@/views/meeting/activityRegister/createDutyActivityForTenant"
          ),
        meta: {
          title: "跨平台系统登记新增",
        },
        hidden: true,
      },
      {
        path: "synthesize",
        name: "synthesize",
        component: () => import("@/views/meeting/delegate/synthesize"),
        meta: {
          title: "代表综合构成统计图",
        },
        hidden: true,
      },
      {
        path: "details",
        name: "details",
        component: () => import("@/views/meeting/delegate/details"),
        meta: {
          title: "查看详情",
        },
        hidden: true,
      },
    ],
  },

  // {
  //   path: "/addDlegate",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "huoDong",
  //   title: "代表信息管理",
  //   meta: {
  //     title: "代表信息",
  //     icon: "database",
  //     permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
  //   },
  //   alwaysShow: false,
  //   children: [
  //     {
  //       path: "manage",
  //       name: "manage",
  //       component: () => import("@/views/dbxx/committeeinfo/manage"),
  //       meta: {
  //         title: "管理代表",
  //       },
  //     },
  //   ],
  // },
  {
    path: "/addDlegate",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表信息管理",
    meta: {
      title: "代表新增",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      // {
      //   path: "addCommittee",
      //   name: "addCommittee",
      //   component: () => import("@/views/dbxx/committeeindex/addCommittee"),
      //   meta: {
      //     title: "手动新建代表",
      //   },
      // },
      {
        path: "addCommittee-CRUD",
        name: "addCommittee-CRUD",
        component: () => import("@/views/dbxx/committeeindex/edit"),
        meta: {
          title: "手动新建编辑代表信息页面",
        },
        hidden: true,
      },
      {
        path: "batchCommittee",
        name: "batchCommittee",
        component: () => import("@/views/dbxx/committeeindex/batchCommittee"),
        meta: {
          title: "批量导入代表",
        },
      },
      // {
      //   path: "syncCommittee",
      //   name: "syncCommittee",
      //   component: () => import("@/views/dbxx/committeeindex/syncCommittee"),
      //   meta: {
      //     title: "同步新建代表",
      //   },
      // },
    ],
  },
  {
    path: "/adjust",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表信息管理",
    meta: {
      title: "代表调整",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "in",
        name: "in",
        component: () => import("@/views/dbxx/adjust/in"),
        meta: {
          title: "代表调入",
        },
      },
      {
        path: "out",
        name: "out",
        component: () => import("@/views/dbxx/adjust/out"),
        meta: {
          title: "代表调出",
        },
      },
      {
        path: "adjust",
        name: "adjust",
        component: () => import("@/views/dbxx/adjust/adjust"),
        meta: {
          title: "调整代表团",
        },
      },
    ],
  },
  {
    path: "/ediAdjust",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表信息管理",
    meta: {
      title: "代表信息修改",
      icon: "database",
      // permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
      // permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_RDDB"],
    },
    alwaysShow: false,
    children: [
      // {
      //   path: "adminAdjust",
      //   name: "adminAdjust",
      //   component: () => import("@/views/dbxx/ediAdjust/adminAdjust"),
      //   meta: {
      //     title: "管理员修改",
      //   },
      // },
      {
        path: "myselfInfo",
        name: "myselfInfo",
        component: () =>
          import("@/views/onBehalfOfTheInformation/workIndex.vue"),
        meta: {
          title: "个人信息修改",
        },
      },
      {
        path: "addInfo",
        name: "addInfo",
        component: () => import("@/views/dbxx/add"),
        meta: {
          title: "个人信息修改",
        },
        hidden: true,
      },
      {
        path: "add",
        name: "add",
        component: () => import("@/views/dbxx/add"),
        meta: {
          title: "信息详情",
        },
        hidden: true,
      },
      {
        path: "editInfo",
        name: "editInfo",
        component: () => import("@/views/dbxx/edit"),
        // component: () => import("@/views/dbxx/ediAdjust/editInfo"),
        meta: {
          title: "个人信息修改",
        },
        hidden: true,
      },
      {
        path: "editJBXX",
        name: "editJBXX",
        component: () => import("@/views/dbxx/editJBXX"), //只改手机号
        meta: {
          title: "基本信息修改",
        },
        hidden: true,
      },
      {
        path: "editPersonal",
        name: "editPersonal",
        component: () =>
          import("@/views/onBehalfOfTheInformation/editPersonal.vue"),
        // component: () => import("@/views/dbxx/ediAdjust/editInfo"),
        meta: {
          title: "我的信息修改",
        },
        hidden: true,
      },
      {
        path: "notice",
        name: "notice",
        component: () => import("@/views/dbxx/ediAdjust/notice"),
        meta: {
          title: "定期提醒更新",
        },
        hidden: true,
      },
    ],
  },

  // {
  //   path: "/elected",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "huoDong",
  //   title: "代表信息管理",
  //   meta: {
  //     title: "当选管理",
  //     icon: "database",
  //     permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
  //   },
  //   alwaysShow: false,
  //   children: [
  //     {
  //       path: "synthesisQuery",
  //       name: "synthesisQuery",
  //       component: () => import("@/views/dbxx/elected/synthesisQuery"),
  //       meta: {
  //         title: "表单查询",
  //       },
  //     },
  //     {
  //       path: "statisticsReport",
  //       name: "statisticsReport",
  //       component: () => import("@/views/dbxx/elected/statisticsReport"),
  //       meta: {
  //         title: "统计报表",
  //       },
  //     },
  //   ],
  // },
  {
    path: "/representDta",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表信息管理",
    meta: {
      title: "代表信息查询",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "infoQuery",
        name: "infoQuery",
        component: () => import("@/views/dbxx/representDta/infoQuery"),
        meta: {
          title: "信息查询",
        },
      },
      {
        path: "dbUpdateRecord",
        name: "dbUpdateRecord",
        component: () => import("@/views/dbxx/dbupdaterecord"),
        meta: {
          title: "代表信息个人修改记录",
        },
      },
    ],
  },
  {
    path: "/statementDta",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表信息管理",
    meta: {
      title: "统计分析报表",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "statementReport",
        name: "statementReport",
        component: () => import("@/views/dbxx/statementDta/statementReport"),
        meta: {
          title: "分析报表",
        },
      },
    ],
  },
  {
    path: "/systemIndexdai",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "代表信息管理",
    meta: {
      title: "系统管理",
      permissions: ["ADMIN", "I_XTGLY"],
    },
    children: [
      {
        path: "index",
        name: "index",
        component: () => import("@/views/dbxx/systemIndex/index"),
        meta: {
          title: "代表信息推送",
          permissions: ["I_XTGLY"],
        },
        hidden: true,
      },
      {
        path: "meetingEdit",
        name: "MeetingEdit",
        component: () => import("@/views/meeting/edit"),
        meta: {
          title: "数据权限管理",
          permissions: ["I_XTGLY"],
        },
      },
      // {
      //   path: "Birthdaywishes",
      //   name: "Birthdaywishes",
      //   component: () => import("@/views/Birthdaywishes/BirthdaywishesList"),
      //   meta: {
      //     title: "代表生日祝福",
      //   },
      // },
      // {
      //   path: "BirthdaywishesDetails",
      //   name: "BirthdaywishesDetails",
      //   component: () => import("@/views/Birthdaywishes/BirthdaywishesDetails"),
      //   meta: {
      //     title: "代表生日祝福详情",
      //   },
      //   hidden: true,
      // },
      {
        path: "session",
        name: "session",
        component: () => import("@/views/election/systemIndex/electionMage"),
        // component: () => import("@/views/meeting/session/index"),
        meta: {
          title: "届次管理",
        },
      },
      {
        path: "level",
        name: "level",
        component: () => import("@/views/dbxx/system/level"),
        meta: {
          title: "分级管理",
        },
      },
      {
        path: "parameterManagement",
        name: "parameterManagement",
        component: () => import("@/views/meeting/parameterManagement/index"),
        meta: {
          title: "参数管理",
        },
      },
      {
        path: "menuManage",
        name: "MenuManage",
        component: () => import("@/views/menu/index"),
        meta: {
          title: "菜单管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "roleManage",
        name: "RoleManage",
        component: () => import("@/views/role/index"),
        meta: {
          title: "角色管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "userManage",
        name: "UserManage",
        component: () => import("@/views/user/index"),
        meta: {
          title: "用户管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "editRole",
        name: "EditRole",
        component: () => import("@/views/role/edit"),
        meta: {
          title: "编辑角色",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
      {
        path: "区级代表权限树配置",
        name: "districtDbTree",
        component: () => import("@/views/role/edit"),
        meta: {
          title: "编辑角色",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
    ],
  },
  // 给区工作人员提供的编辑个人信息入口
  {
    path: "/manageQZDBInfo",
    component: Layout,
    redirect: "noRedirect",
    name: "manageQZDBInfo",
    title: "区镇街代表信息管理",
    meta: {
      title: "区镇街代表信息管理",
      icon: "database",
      // permissions: ["I_DBLZ_QXLZ", "I_DBLZ_JDXZ", "I_DBLZ_LLZ"],
    },
    alwaysShow: false,
    children: [
      {
        path: "addQZDBInfo",
        name: "addQZDBInfo",
        component: () => import("@/views/manageQZDBInfo/addQZDBInfo.vue"),
        meta: {
          title: "添加区镇街代表个人信息表单",
        },
        hidden: true,
      },
      {
        path: "addQZDB",
        name: "addQZDB",
        component: () => import("@/views/manageQZDBInfo/addQZDB.vue"),
        meta: {
          title: "添加区镇街代表信息",
        },
        hidden: true,
      },
      {
        path: "editQZDB",
        name: "editQZDB",
        component: () => import("@/views/manageQZDBInfo/editQZDB.vue"),
        meta: {
          title: "修改区镇街代表个人信息",
        },
        hidden: true,
      },
    ],
  },
];
