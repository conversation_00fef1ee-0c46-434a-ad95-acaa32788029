<template>
  <div>
    <a-modal title="候选人确认表"
             :visible.sync="visible"
             width="96%"
             @cancel="close">
      <a-row class="steps">
        <a-steps :current="Steps">
          <a-step title="第一步：确认"></a-step>
          <a-step title="第二步：初审" />
          <a-step title="第三步：终审" />
          <a-step title="第四步：确认表审核完成" />
        </a-steps>
      </a-row>
      <a-row class="formBox">
        <a-transfer :data-source="mockData"
                    :target-keys="targetKeys"
                    :show-select-all="false"
                    @change="onChange"
                    @selectChange="handleScroll">
          <template slot="children"
                    slot-scope="{
              props: { direction, filteredItems, selectedKeys },
              on: { itemSelectAll, itemSelect },
            }">
            <!-- 左边表 -->
            <a-form v-if="direction === 'left'"
                    layout="inline"
                    :form="leftForm"
                    :label-col="{ span: 4 }"
                    :wrapper-col="{ span: 16 }">
              <div>
                <a-row>
                  <a-col :span="12">
                    <a-form-item label="届次"
                                 style="width: 100%">
                      <!-- disabled -->
                      <a-select v-model="leftForm.jcDm"
                                allow-clear
                                style="width: 100%">
                        <a-select-option v-for="item in periods"
                                         :key="item.jcDm"
                                         :value="item.jcDm">{{ item.levelName
                        }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="姓名"
                                 style="width: 100%">
                      <a-input v-model="leftForm.userName"
                               allow-clear
                               v-on:keyup.enter="search('left')"></a-input>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="12">
                    <a-form-item label="推荐单位"
                                 style="width: 100%">
                      <a-input v-model="leftForm.tjdw"
                               allow-clear
                               v-on:keyup.enter="search('left')"></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <span>
                      <a-button type="primary"
                                @click="search('left')">搜索</a-button>
                      <a-button style="margin-left: 12px;"
                                @click="reset('left')"
                                class="pinkBoutton">重置</a-button>
                    </span>
                  </a-col>
                </a-row>
              </div>
            </a-form>
            <!-- 右边表 -->
            <a-form v-if="direction === 'right'"
                    layout="inline"
                    :form="rightForm">
              <!-- <a-form-item label="届次">
              <a-select
                v-model="rightForm.jcDm"
                allow-clear
                style="width: 180px;"
              >
                <a-select-option
                  v-for="item in periods"
                  :key="item.jcDm"
                  :value="item.jcDm"
                  >{{ item.levelName }}</a-select-option
                >
              </a-select>
            </a-form-item>
            <a-form-item label="姓名">
              <a-input v-model="rightForm.userName"></a-input>
            </a-form-item>
            <a-form-item label="推荐单位">
              <a-input v-model="rightForm.tjdw"></a-input>
            </a-form-item>
            <a-form-item>
              <a-button @click="search('right')">查询</a-button>
              </a-form-item>-->
            </a-form>
            <a-table style="height: 400px"
                     :row-selection="
              getRowSelection({
                selectedKeys,
                itemSelectAll,
                itemSelect,
                filteredItems,
              })
            "
                     rowKey="key"
                     :columns="direction === 'left' ? leftColumns : rightColumns"
                     :data-source="filteredItems"
                     :pagination="direction === 'left' ? leftPagination : rightPagination"
                     size="small"
                     :scroll="{ x: '30%', y: 400 }"></a-table>
          </template>
        </a-transfer>
      </a-row>
      <a-row slot="footer">
        <a-col>
          <a-space>
            <a-button @click="save">保存</a-button>
            <a-button @click="sShen"
                      v-if="disabled">
              <a-icon type="check" />送审
            </a-button>
            <a-button @click="close">退出</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-modal>
    <!-- 流程弹出页 -->
    <submitForCensorship ref="submitForCensorship"
                         :procInstId="procInstId"
                         @complete="handleComplete"
                         :ids="ids">
    </submitForCensorship>
  </div>
</template>
<script>
// 用于获取差异化后的新数组
import { gethxrqrbscompleteApi } from "@/api/xjgw";
import submitForCensorship from "@/views/common/submitForCensorship";
import difference from "lodash/difference";
import {
  getCandidateConfirmEditApi,
  getWaitConfirmEditApi,
  getLevelListApi,
  getSavetoApi,
  candidateConfirmation,
  getAWater,
  getViewDqrhxr,
  cancelToConfirmCandidates,
} from "@/api/representativeElection/candidateApi.js";
import { log } from "@antv/g2plot/lib/utils";
export default {
  components: { submitForCensorship },

  data () {
    return {
      disabled: false,
      Steps: 0,
      newIDs: this.neWid,
      procInstId: "",
      ids: [],
      visible: false,
      mockData: [],
      newData: [],
      // 届别
      periods: [],
      //选择框
      xzkuang: [],
      //右表单
      rightForm: {
        jcDm: 2,
        xm: "",
        tjdw2: "",
        qrbId: "",
        pageSize: 10,
        pageNum: 1,
      },
      // 左表单
      leftForm: {
        pageSize: 10,
        pageNum: 1,
        state: "21",
        jcDm: 2,
      },

      leftColumns: [
        {
          title: "预备人选",
          align: "center",
          ellipsis: true,
          dataIndex: "USER_NAME",
          width: 76,
        },
        {
          title: "性别",
          align: "center",
          ellipsis: true,
          dataIndex: "SEX",
          width: 46,
        },
        {
          title: "出生日期",
          align: "center",
          ellipsis: true,
          width: 76,
          dataIndex: "BIRTHDAY",
        },
        {
          title: "工作单位及职务",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "GZDWJZW",
        },
        {
          title: "职业构成",
          align: "center",
          ellipsis: true,
          width: 100,
          dataIndex: "ZYGCMC",
        },
        {
          title: "推荐单位",
          align: "center",
          ellipsis: true,
          width: 100,
          dataIndex: "MELYMC",
        },
      ],
      rightColumns: [
        {
          title: "待确认候选人",
          align: "center",
          ellipsis: true,
          width: 100,
          dataIndex: "USER_NAME",
        },
        {
          title: "性别",
          align: "center",
          ellipsis: true,
          width: 60,
          dataIndex: "SEX",
        },
        {
          title: "出生日期",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "BIRTHDAY",
          customRender: (text, record, index) => {
            return text.slice(0, text.indexOf("T")) || '/';
          }
        },
        {
          title: "工作单位及职务",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "GZDWJZW",
        },
        {
          title: "职业构成",
          align: "center",
          ellipsis: true,
          width: 100,
          dataIndex: "ZYGCMC",
        },
        {
          title: "推荐单位",
          align: "center",
          ellipsis: true,
          width: 100,
          dataIndex: "MELYMC",
        },
      ],
      targetKeys: [], // 选中到右边的数据
      rightChangeData: [],
      // 保存成功后返回数据
      returnData: {},
      // 左分页器设置
      leftPagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize, "left"), // 改变每页数量时更新显示
        onChange: this.leftHandleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "small",
      },
      // 右分页器设置
      rightPagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize, "right"), // 改变每页数量时更新显示
        onChange: this.rightHandleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  watch: {
    visible (newVal) {
      console.log("🤗🤗🤗, newVal =>", newVal);
      if (newVal === true) {
        // 获取当前届次下拉数据列表
        this.getLevelListFn();

      } else {

        this.$parent.fetchData();//刷新页面
      }
    },
    rightChangeData () {
      if (this.rightChangeData.length == 0) {
        this.disabled = false
      }
    },
  },

  methods: {
    // 切换页数
    changePageSize (pageNum, pageSize, type) {
      // this[type + "Form"].pageNum = pageNum;
      // this[type + "Form"].pageSize = pageSize;
      // this[type + "Pagination"].pageSize = pageSize;
      // this[type + "Pagination"].current = pageNum;
      this.pageNum = pageNum;
      this.pageSize = pageSize;
      this.pageSize = pageSize;
      this.current = pageNum;
      this[type + "FetchData"]();
    },
    // 切换页码-左
    leftHandleCurrentChange (pageNum, pageSize) {
      this.leftForm.pageNum = pageNum;
      this.leftPagination.current = pageNum;
      this.leftFetchData();
    },
    // 切换页码-右
    rightHandleCurrentChange (pageNum, pageSize) {
      this.rightForm.pageNum = pageNum;
      this.rightPagination.current = pageNum;
      this.rightFetchData();
    },
    handleScroll (e) {
      // this.xzkuang = e
      // console.log(this.xzkuang, 789789);
    },

    //送审
    sShen () {
      this.$refs.submitForCensorship.visible = true;
    },

    // 保存
    save () {
      // console.log(this.xzkuang);
      if (this.rightChangeData.length == 0) {
        return this.$message.error("请选择数据");
      }
      // 调save接口
      let jc = {
        jcDm: this.leftForm.jcDm,
      };
      //保存到待确认
      let form = {};
      let hxrqrbFbs = [];

      console.log(Array.from(new Set(this.newData)));

      this.rightChangeData.forEach((item) => {
        // hxrqrbFbs.push({
        //   userId: item.DB_ID,
        //   GZDWJZW: item.GZDWJZW,
        //   MELYMC: item.MELYMC,
        //   MELY_DM: item.MELY_DM,
        //   SEX: item.SEX,
        //   USER_NAME: item.USER_NAME,
        //   XB_DM: item.XB_DM,
        //   ZYGCMC: item.ZYGCMC,
        //   key: item.key,
        // });
        hxrqrbFbs.push({
          userId: item.DB_ID,
        });
      });
      form.hxrqrbFbs = hxrqrbFbs;
      // form.jc = jc;
      let params = {
        hxrqrbFbs,
        jc,
        state: this.leftForm.state,
      };
      candidateConfirmation(params).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.data.code == "0000") {
          this.disabled = true
          this.procInstId = res.data.data.procInstId;
          this.ids.push(res.data.data.id);
          this.$message.success("保存成功");
        }
        // 保存成功 保存数据
        // this.returnData = res.data.data;
      });
    },
    //审核保存发送
    handleComplete (data) {
      // , { "tzlx": 0 }
      gethxrqrbscompleteApi(data).then((res) => {
        if (res.data.code == "0000") {
          this.$emit("handleClearId");

          this.leftFetchData();
          this.rightFetchData();
          this.ids = [];
          this.$refs.submitForCensorship.successComplete();
          this.$message.success(res.data.msg);
          this.visible = false;//成功后关闭
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 搜索
    search (type) {
      if (type == "left") {
        this.leftFetchData();
      } else {
        this.rightFetchData();
      }
    },
    reset (type) {
      if (type == "left") {
        this.leftForm = {
          pageSize: 10,
          pageNum: 1,
          jcDm: this.periods[0].jcDm,
        };
        this.leftFetchData();
      } else {
        this.rightFetchData();
      }
    },
    // 穿梭框选择
    onChange (targetKeys, direction, moveKeys) {
      console.log(direction, 789);
      this.$set(this, "targetKeys", targetKeys);
      // return;
      if (direction == "left") {
        this.leftPagination.total = this.leftPagination.total + moveKeys.length;
        console.log("右到左");
        // cancelToConfirmCandidates(form).then((res) => {
        //   console.log("🤗🤗🤗,  res =>", res);
        // });
        // 调save接口
      } else {
        // 右边有数据调save接口
        if (this.rightChangeData.length == 0) {
          // 调save接口
          let jc = {
            jcDm: this.leftForm.jcDm,
          };
          //保存到待确认
          let form = {};
          let hxrqrbFbs = [];
          moveKeys.forEach((element) => {
            hxrqrbFbs.push({
              userId: element,
            });
          });
          form.hxrqrbFbs = hxrqrbFbs;
          form.jc = jc;
          // candidateConfirmation(form).then((res) => {
          //   console.log("🤗🤗🤗, res =>", res);
          //   // 保存成功 保存数据
          //   this.returnData = res.data.data;
          // });
        } else {
          // 右边没有数据 调获取流水号接口
          let jc = {
            jcDm: this.leftForm.jcDm,
          };
          //保存到待确认
          let form = {};
          let hxrqrbFbs = [];
          // let newData = this.rightChangeData.concat(moveKeys);
          moveKeys.forEach((element) => {
            hxrqrbFbs.push({
              dbId: element,
            });
          });
          form.hxrqrbFbs = hxrqrbFbs;
          form.jc = jc;
          // form.id = this.returnData.id;
          form.dqzt = this.returnData.dqzt;
          form.yxbz = this.returnData.yxbz;
          form.procInstId = this.returnData.procInstId;
          console.log("🤗🤗🤗, 调获取流水号接口🤗🤗🤗 =>", form);
          // getAWater(form).then((res) => {
          //   console.log("调获取流水号接口🤗🤗🤗, res =>", res);
          // });
        }
        this.leftPagination.total = this.leftPagination.total - moveKeys.length;
        console.log("左到右");
      }
    },
    // 列表选择
    getRowSelection ({
      selectedKeys,
      itemSelectAll,
      itemSelect,
      filteredItems,
    }) {
      this.rightChangeData = filteredItems;
      return {
        onSelectAll (selected, selectedRows) {
          console.log(selected, 123);
          // 选中的数据
          const treeSelectedKeys = selectedRows.map(({ key }) => key);
          const diffKeys = selected
            ? difference(treeSelectedKeys, selectedKeys)
            : difference(selectedKeys, treeSelectedKeys);
          itemSelectAll(diffKeys, selected);
        },
        onSelect ({ key }, selected) {
          itemSelect(key, selected);
        },
        selectedRowKeys: selectedKeys,
        onChange: this.onChangeKey,
      };
    },
    onChangeKey (key, data, index) {
      let arr = [];
      data.forEach((item) => {
        this.rightChangeData.forEach((right) => {
          if (item.DB_ID == right.DB_ID) {
            arr.push(item);
          }
        });
      });
      this.newData = arr;
      // if(key.length!=="0"){
      //   this.rightChangeData.forEach(item=>{
      //          key.forEach(key=>{
      //           if(item &&item.DB_ID==key){
      //             if (this.newData.length=="0") {
      //                 this.newData.push(item)
      //             }else{
      //                this.newData.forEach((item1,index)=>{
      //                 if(item1.DB_ID==item.DB_ID){
      //                      console.log("删除");
      //                    this.newData= this.newData.splice(index,1)
      //                 }else{
      //                       console.log("新增");
      //                     this.newData.push(item)
      //                 }
      //               })
      //             }
      //         }
      //     })
      //   })
      // }
    },
    // 获取数据
    leftFetchData () {
      getCandidateConfirmEditApi(this.leftForm).then((res) => {
        this.mockData = [];
        if (res.data.code == 200) {
          res.data.rows.forEach((element) => {
            console.log(element);
            element.key = element.DB_ID;
            // element.key = element.MELY_DM;
          });
          this.mockData = this.mockData.concat(res.data.rows);
          this.leftPagination.total = res.data.total - this.rightChangeData.length;//减去右边数据
          if (res.data.rows.length == 10) {
            // this.leftForm.pageSize++;
            // this.leftFetchData();
          }
        }
      });
      console.log(this.mockData);
    },
    // 获取右边数据源
    rightFetchData () {
      // let params = {
      //   jcDm: this.rightForm.jcDm,
      //   xm: this.rightForm.xm,
      //   tjdw2: this.rightForm.tjdw2,
      //   qrbId: this.rightForm.qrbId,
      //   pageSize: 10,
      //   pageNum: 1,
      // }

      getViewDqrhxr(this.rightForm).then((res) => {
        console.log("res", res);
        this.rightData = [];
        if (res.data.code == 200 && res.data.rows.length > 0) {
          res.data.rows.map((item) => {
            item.sex = item.SEX;
            item.user_name = item.USER_NAME;
            item.key = item.USER_ID;
          });
          this.rightData = res.data.rows;
          // 合并
          let newArr = this.mockData.concat(this.rightData);
          this.mockData = newArr;
          this.rightPagination.total = res.data.total;
          console.log("🤗🤗🤗, this.mockData =>", this.mockData);
        }
      });
    },

    //获取当前届次下拉数据列表
    async getLevelListFn () {
      const res = await getLevelListApi();
      // console.log("🤗🤗🤗, res.data =>", res.data);
      if (res.data.code === "0000") {
        this.periods = res.data.data;
        this.leftForm.jcDm = this.periods[0].jcDm;
        this.rightForm.jcDm = this.periods[0].jcDm;

        this.leftFetchData();
      }
    },

    // 关闭
    close () {
      this.visible = false;
      this.Steps = 0;
      this.mockData = [];
      this.rightData = []
      Object.assign(this.$data, this.$options.data());
    },
  },
};
</script>
<style scoped>
.formBox {
  padding: 20px;
}
::v-deep .ant-pagination-item a {
  padding: 0 !important;
}
</style>
