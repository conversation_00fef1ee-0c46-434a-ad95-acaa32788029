<template>
  <a-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
    <a-input
      style="width: 300px;"
      v-model="filterText"
      placeholder="输入关键字过滤"
      allow-clear
      @change="onChange"
    />
    <a-tree
      v-if="treeData.length > 0"
      ref="orgTree"
      :replace-fields="replaceFields"
      :tree-data="treeData"
      :defaultExpandedKeys="currentExpandKeys"
      :defaultSelectedKeys="selectedKeys"
      @select="handleClick"
    >
      <template slot="title" slot-scope="{ name }">
        <span
          v-if="searchValue && name.indexOf(searchValue) > -1"
          style="color: #f50;"
          >{{ name }}</span
        >
        <span v-else>{{ name }}</span>
      </template>
    </a-tree>
    <div style="height: 50vh; display: flex; align-items: center; justify-content: center;">
      <a-empty v-if="treeData.length == 0"/>
    </div>
  </a-col>
</template>

<script>
// import { findDeputyOrganizations } from "@/api/dmcs";
// import { findDbOrgInfoTree } from "@/api/dmcs";
import { findDbOrgUserTreeByMainIdentifyInOne } from "@/api/levelRole/orgDbTree";
import checkPermission from "@/utils/permission";
import { DBLZ_DBXXGL } from '@/utils/enum/levelRoleMainIdentifyEnum'; // 导入配置文件
export default {
  props: {
    // 选中的节点
    levelRoleMainIdentify: {
      type: String,
      default: DBLZ_DBXXGL,
    },
  },
  data() {
    return {
      // levelRoleMainIdentify: DBLZ_DBXXGL,
      replaceFields: {
        title: "name",
        key: "id",
        // children: "",
      },
      searchValue: "",
      filterText: "",
      treeData: [],
      allChildData: [],
      keys: "",
      currentExpandKeys:[],
      selectedKeys: [],
      expandedKeys: ["R000000"],
      treeDataInfo: [], // 修改的机构树
    };
  },
  created() {
    this.getOrgTreeRawData();
  },
  methods: {
    checkPermission,
    // 获取tree数据
    getOrgTreeRawData() {
      findDbOrgUserTreeByMainIdentifyInOne({mainIdentify : this.levelRoleMainIdentify}).then((res) => {
        this.treeData = res.data;
        this.treeDataInfo = JSON.parse(JSON.stringify(this.treeData))

        let flag = true;
        res.data.forEach(element => {
          if(element.name == "市"){
            this.currentExpandKeys.push("2933")
            this.selectedKeys.push("2933");
            this.$emit("handleClickTwo","2933"); //执行父页面方法
            flag = false;
          }
        });
        if(flag){
          let orgId = this.treeData[0].children[0].id;
          this.currentExpandKeys.push(orgId)
          this.selectedKeys.push(orgId);
          this.$emit("handleClickTwo",orgId); //执行父页面方法
        }
        // if (checkPermission(["I_XTGLY"]) || checkPermission(["I_DBLZ_XLGW"])) {

        //   let orgId = this.treeData[2].children[0].children[1].orgId;
        //   this.currentExpandKeys.push(orgId);//市
        //   this.selectedKeys.push(orgId);
        //   this.$emit("handleClickTwo",orgId); //执行父页面方法
        //   return
        // }
        // if (checkPermission(["I_DBLZ_QXLZ"])) {

        //   let orgId = this.treeData[1].children[0].children[0].orgId;
        //   this.currentExpandKeys.push(orgId);//区
        //   this.selectedKeys.push(orgId);
        //   this.$emit("handleClickTwo",orgId); //执行父页面方法
        //   return
        // }

        // if (checkPermission(["I_DBLZ_JDXZ"])) {
        //     let orgId = this.treeData[0].children[0].orgId;
        //     this.currentExpandKeys.push(orgId);//镇街
        //     this.selectedKeys.push(orgId);
        //     this.$emit("handleClickTwo",orgId); //执行父页面方法
        //     return
        // }

      });
    },
    handleClick(data, e) {
      let entryId = data[0]
      this.$emit("handleClick", entryId, e);
    },
    // 树状搜索
    onChange(e) {
      const value = e.target.value;
      this.treeData = this.getSearchList(this.treeDataInfo, value)
      const expandedKeys = this.allChildData.map((item) => {
        if (item.name.indexOf(value) > -1) {
          return this.getParentKey(item.id, this.userTreeData);
        }
        return null;
      });
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      });
    },
    filterNode(node, value) {
      if (!node.children) {
        return node.name.includes(value) ? [node] : [];
      }
      const filteredChildren = node.children.flatMap(child => this.filterNode(child, value));
      return filteredChildren;
    },
    getSearchList(tree, value) {
      return tree.map(node => {
        const filteredChildren = this.filterNode(node, value);
        if (filteredChildren.length > 0 || node.name.includes(value)) {
          return { ...node, children: filteredChildren.length > 0 ? filteredChildren : undefined };
        }
        return null;
      }).filter(node => node !== null);
    },
    // 获取所在的父节点
    getParentKey(id, tree) {
      let parentKey;
      for (let index = 0; index < tree.length; index++) {
        const node = tree[index];
        if (node.children) {
          if (node.children.some((item) => item.id === id)) {
            parentKey = node.id;
          } else if (this.getParentKey(id, node.children)) {
            parentKey = this.getParentKey(id, node.children);
          }
        }
      }
      return parentKey;
    },
  },
};
</script>

<style></style>
