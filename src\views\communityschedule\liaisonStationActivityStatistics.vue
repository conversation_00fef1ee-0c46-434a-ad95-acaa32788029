<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <SearchForm :value="queryForm" @onReset="reset" @onSearch="search">
          <template v-slot:topSearch>
            <AdminStreetStationCascade v-model="queryForm" allow-clear />
          </template>
          <template v-slot:moreSearch>
            <FormPicker
              v-model="queryForm.year"
              label="年度"
              type="year"
              allow-clear
            />

            <FormSelect
              v-model="queryForm.month"
              :label="'月份'"
              :options="monthList"
              allow-clear
            />
          </template>
        </SearchForm>

        <a-row style="margin: 5px 0px 10px 8px">
          <a-col span="6">
            <!-- <a-button @click="$router.go(-1)"> 返回</a-button> -->
            <a-button
              type="primary"
              style="margin-left: 10px"
              @click="exportExcel"
              >导出</a-button
            >
          </a-col>
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <a-table
            ref="table"
            :bordered="false"
            class="directorySet-table"
            size="small"
            :columns="columns"
            :pagination="pagination"
            :data-source="list"
            :row-key="
              (record, index) => {
                return index;
              }
            "
            :row-selection="null"
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import {
  liaisonStationActivityExport2,
  liaisonStationActivityList2,
} from "@/api/communityschedule";
import SearchForm from "@/components/SearchForm/index";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormSelect from "@/components/FormSelect/index.vue";
import FormPicker from "@/components/FormPicker/index.vue";

export default {
  components: {
    FormPicker,
    FormSelect,
    AdminStreetStationCascade,
    SearchForm,
  },
  data() {
    return {
      selectedRows: [],
      selectedRowKeys: [],
      list: [],
      listLoading: false,
      //街道数据
      streetTowns: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        year: "",
        month: "",
      },
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      // 列表
      columns: [
        {
          title: "序号",
          align: "center",
          width: 60,
          ellipsis: true,
          customRender: (text, record, index) => {
            return index + 1 || "/";
          },
        },
        {
          title: "年份",
          align: "center",
          width: 130,
          ellipsis: true,
          dataIndex: "year",
          customRender: (text, record, index) => {
            return (text && `${text}年`) || "/";
          },
        },
        {
          title: "月份",
          align: "center",
          width: 130,
          ellipsis: true,
          dataIndex: "month",
          customRender: (text, record, index) => {
            return (text && `${text}月`) || "/";
          },
        },
        {
          title: "行政区划",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "administrativeAreaName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "街道名称",
          align: "center",
          width: 130,
          ellipsis: true,
          dataIndex: "streetTownName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站名称",
          align: "center",
          width: 340,
          ellipsis: true,
          dataIndex: "liaisonStationName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          align: "center",
          title: "进站代表数",
          width: 150,
          dataIndex: "num",
          customRender: (text, record, index) => {
            return text || "0";
          },
        },
        {
          align: "center",
          title: "进站代表人数",
          width: 150,
          dataIndex: "num2",
          customRender: (text, record, index) => {
            return text || "0";
          },
        },
        {
          title: "联系选民群众人次",
          align: "center",
          width: 100,
          dataIndex: "num3",
          customRender: (text, record, index) => {
            return text || "0";
          },
        },
        {
          align: "center",
          title: "收集意见建议数",
          width: 150,
          dataIndex: "num4",
          customRender: (text, record, index) => {
            return text || "0";
          },
        },
        {
          align: "center",
          title: "推动决意见建议数",
          width: 150,
          dataIndex: "num5",
          customRender: (text, record, index) => {
            return text || "0";
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      monthList: Array.from({ length: 12 }, (_, index) => ({
        name: `${index + 1} 月`,
        id: index + 1,
      })),
    };
  },
  created() {
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "联络站活动情况查询统计");
  },
  methods: {
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    //列表
    fetchData() {
      this.listLoading = true;
      liaisonStationActivityList2(this.queryForm).then((response) => {
        this.list = response.rows;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    reset(value) {
      this.queryForm = value;
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
    exportExcel() {
      const arr = this.selectedRowKeys;

      liaisonStationActivityExport2({
        ...this.queryForm,
        // ids: arr.join(),
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `联络站活动情况查询统计.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
  },
};
</script>
