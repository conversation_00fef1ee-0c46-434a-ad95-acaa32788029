<template>
  <el-container>
    <el-header class="sub-form-header">
      <span @click="goBackFront" class="back-btn">
        <i class="el-icon-back" /> 数据主题管理
      </span>
      <span class="sub-form-title">{{ title }}</span>
    </el-header>
    <el-main>
      <a-row :gutter="15">
        <a-col
          :xs="24"
          :sm="{ span: 20, offset: 2 }"
          :md="{ span: 20, offset: 2 }"
          :lg="{ span: 14, offset: 5 }"
          :xl="{ span: 12, offset: 6 }"
        >
          <el-form
            ref="dataSubjectForm"
            :model="dataSubjectForm"
            :rules="rules"
            label-width="160px"
          >
            <el-divider>基本信息</el-divider>
            <el-form-item label="主题名称" prop="subjectName">
              <el-input
                v-model="dataSubjectForm.subjectName"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <el-form-item label="主题描述" prop="subjectDesc">
              <el-input
                v-model="dataSubjectForm.subjectDesc"
                autocomplete="off"
                type="textarea"
              ></el-input>
            </el-form-item>
            <el-form-item label="主题状态" prop="subjectStatus">
              <el-select
                v-model="dataSubjectForm.subjectStatus"
                placeholder="请选择主题状态"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in subjectStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="信息共享目录编号" prop="shareDirectoryCode">
              <el-input
                v-model="dataSubjectForm.shareDirectoryCode"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <el-form-item label="数据来源" prop="dataSourceId">
              <el-select
                v-model="dataSubjectForm.dataSourceId"
                filterable
                placeholder="请选择数据来源"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in dataSourceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="上传单位" prop="sourceOrgName">
              <el-input
                v-model="dataSubjectForm.sourceOrgName"
                autocomplete="off"
                :readonly="true"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="openOrgTreeDialog('source')"
                ></el-button>
              </el-input>
            </el-form-item>
            <el-form-item label="需求单位" prop="targetOrgName">
              <el-input
                v-model="dataSubjectForm.targetOrgName"
                autocomplete="off"
                :readonly="true"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="openOrgTreeDialog('target')"
                ></el-button>
              </el-input>
            </el-form-item>
            <el-form-item label="更新频率" prop="sourceUploadFrequency">
              <el-select
                v-model="dataSubjectForm.sourceUploadFrequency"
                placeholder="请选择更新频率"
                style="width: 100%;"
                @change="switchFrequency"
              >
                <el-option
                  v-for="item in sourceUploadFrequencyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="上传截止时间范围"
              prop="deadlineTimeRange"
              v-show="dataSubjectForm.sourceUploadFrequency !== 1"
            >
              <el-select
                v-model="dataSubjectForm.deadlineTimeRange"
                placeholder="请选择上传截止时间范围"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in deadlineTimeRangeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="上传截止时间偏移天数" prop="deadlineTimeRange">
              <el-input
                placeholder="请输入偏移天数"
                v-model="dataSubjectForm.deadlineTimeValue"
                class="input-with-select"
              >
                <el-select
                  v-model="dataSubjectForm.deadlineTimeValueSymbol"
                  slot="prepend"
                  placeholder="请选择顺数倒数"
                  style="width: 130px;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="item in symbolOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <template slot="append">自然日</template>
              </el-input>
            </el-form-item>
            <el-form-item label="数据类型" prop="sourceDataType">
              <el-select
                v-model="dataSubjectForm.sourceDataType"
                placeholder="请选择数据类型"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in sourceDataTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <!-- 数据类型为“数据表” -->
            <el-form-item
              label="数据表名"
              prop="tableName"
              v-show="dataSubjectForm.sourceDataType == 0"
            >
              <el-input
                v-model="dataSubjectForm.tableName"
                autocomplete="off"
                @input="$forceUpdate()"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="“创建时间”字段名"
              prop="fieldCreateTime"
              v-show="dataSubjectForm.sourceDataType == 0"
            >
              <el-input
                v-model="dataSubjectForm.fieldCreateTime"
                autocomplete="off"
                @input="$forceUpdate()"
              >
                <template slot="append">
                  <el-tooltip placement="top-end" effect="light">
                    <div slot="content">
                      请填写数据表中记录数据“创建时间”的字段名，如“create_time”等
                    </div>
                    <byui-icon
                      :icon="['fas', 'info-circle']"
                       class="tab"
                    ></byui-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item
              label="“归属时间”字段名"
              prop="fieldDataTime"
              v-show="dataSubjectForm.sourceDataType == 0"
            >
              <el-input
                v-model="dataSubjectForm.fieldDataTime"
                autocomplete="off"
                @input="$forceUpdate()"
              >
                <template slot="append">
                  <el-tooltip placement="top-end" effect="light">
                    <div slot="content">
                      请填写数据表中记录数据“归属时间”的字段名，“归属时间”描述了某条数据归属的时间段，
                      <br />譬如用户在2020-02-01上传了一条《2020年1月外出统计总数》的数据，其中“2020-02-01”为创建时间，“2020年1月”为数据归属时间。
                    </div>
                    <byui-icon
                      :icon="['fas', 'info-circle']"
                       class="tab"
                    ></byui-icon>
                    <!-- style="color: #777; font-size: 1.25em;" -->
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
            <!-- 数据类型为“文件”或“目录“ -->
            <el-form-item
              label="文件路径"
              prop="path"
              v-show="
                dataSubjectForm.sourceDataType == 1 ||
                dataSubjectForm.sourceDataType == 2
              "
            >
              <el-input
                v-model="dataSubjectForm.path"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <!-- 数据类型为“API“ -->
            <el-form-item
              label="接口上下文"
              prop="uri"
              v-show="dataSubjectForm.sourceDataType == 3"
            >
              <el-input
                v-model="dataSubjectForm.uri"
                autocomplete="off"
              ></el-input>
            </el-form-item>

            <el-divider style="margin-top: 40px;">预警信息</el-divider>

            <el-form-item label="一级预警时间" prop="warningTimeValueOne">
              <el-input
                placeholder="请输入一级预警时间"
                v-model="dataSubjectForm.warningTimeValueOne"
                class="input-with-select"
              >
                <template slot="prepend">提前</template>
                <template slot="append">自然日</template>
              </el-input>
            </el-form-item>
            <!-- <el-form-item label="二级预警时间（橙色）" prop="warningTimeValueTwo">
              <el-input
                placeholder="请输入二级预警时间"
                v-model="dataSubjectForm.warningTimeValueTwo"
                class="input-with-select"
              >
                <template slot="prepend">提前</template>
                <template slot="append">自然日</template>
              </el-input>
            </el-form-item>-->
            <el-form-item label="二级预警时间" prop="warningTimeValueThree">
              <el-input
                placeholder="请输入二级预警时间"
                v-model="dataSubjectForm.warningTimeValueThree"
                class="input-with-select"
              >
                <template slot="prepend">提前</template>
                <template slot="append">自然日</template>
              </el-input>
            </el-form-item>

            <el-divider style="margin-top: 40px;">督办信息</el-divider>
            <!-- <el-form-item label="督办开始时间" prop="superviseStartTime">
              <el-date-picker
                v-model="dataSubjectForm.superviseStartTime"
                type="date"
                placeholder="选择督办开始时间"
                style="width:100%"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item>-->
            <el-form-item label="一级督办时间" prop="superviseTimeValueOne">
              <el-input
                placeholder="请输入一级督办时间"
                v-model="dataSubjectForm.superviseTimeValueOne"
                class="input-with-select"
              >
                <template slot="prepend">超过</template>
                <template slot="append">自然日</template>
              </el-input>
            </el-form-item>
            <el-form-item label="二级督办时间" prop="superviseTimeValueTwo">
              <el-input
                placeholder="请输入二级督办时间"
                v-model="dataSubjectForm.superviseTimeValueTwo"
                class="input-with-select"
              >
                <template slot="prepend">超过</template>
                <template slot="append">自然日</template>
              </el-input>
            </el-form-item>

            <a-row style="text-align: center;">
              <el-button type="primary" @click="save">确 定</el-button>
            </a-row>
          </el-form>
        </a-col>
      </a-row>
    </el-main>

    <orgTreeDialog ref="orgTreeDialog" @confirm="confirmOrg"></orgTreeDialog>
  </el-container>
</template>

<script>
import { getById, doSave, doUpdate } from "@/api/datasubject";
import { getList as getDataSourceList } from "@/api/datasource";
import orgTreeDialog from "@/views/common/orgTreeDialog.vue";
export default {
  name: "TableEdit",
  data() {
    var checkSubParam = (value, callback, errorMsg, isCheck) => {
      if (!isCheck) {
        callback();
      }
      if (value == undefined || value === "") {
        callback(new Error(errorMsg));
      } else {
        callback();
      }
    };
    var dataSourceIdValidator = (rule, value, callback) => {
      checkSubParam(
        value,
        callback,
        "请选择数据源",
        this.dataSubjectForm.subjectStatus == 0
      );
    };
    var sourceOrgNameValidator = (rule, value, callback) => {
      checkSubParam(
        value,
        callback,
        "请选择上传单位",
        this.dataSubjectForm.subjectStatus == 0
      );
    };
    var targetOrgNameValidator = (rule, value, callback) => {
      checkSubParam(
        value,
        callback,
        "请选择需求单位",
        this.dataSubjectForm.subjectStatus == 0
      );
    };
    var sourceUploadFrequencyValidator = (rule, value, callback) => {
      checkSubParam(
        value,
        callback,
        "请选择更新频率",
        this.dataSubjectForm.subjectStatus == 0
      );
    };
    var sourceDataTypeValidator = (rule, value, callback) => {
      checkSubParam(
        value,
        callback,
        "请选择数据类型",
        this.dataSubjectForm.subjectStatus == 0
      );
    };
    var deadlineTimeRangeValidator = (rule, value, callback) => {
      checkSubParam(
        value,
        callback,
        "请选择上传截止时间范围",
        this.dataSubjectForm.subjectStatus == 0
      );
    };
    var deadlineTimeValueValidator = (rule, value, callback) => {
      checkSubParam(
        value,
        callback,
        "请输入上传截止时间偏移天数",
        this.dataSubjectForm.subjectStatus == 0
      );
    };
    return {
      dataSubjectForm: {
        subjectStatus: 0,
        sourceUploadFrequency: 3,
        deadlineTimeValueSymbol: 1,
        deadlineTimeUnit: 0,
        warningTimeValueOne: 7,
        warningTimeValueThree: 3,
        superviseTimeValueOne: 3,
        superviseTimeValueTwo: 7,
        sourceOrgId: null,
        sourceOrgName: "",
        targetOrgId: null,
        targetOrgName: "",
      },
      title: "",
      isNewData: true,
      rules: {
        subjectName: [
          { required: true, message: "请输入主题名称", trigger: "blur" },
        ],
        subjectStatus: [
          { required: true, message: "请选择主题状态", trigger: "blur" },
        ],
        dataSourceId: [{ validator: dataSourceIdValidator, trigger: "blur" }],
        sourceOrgName: [{ validator: sourceOrgNameValidator, trigger: "blur" }],
        targetOrgName: [{ validator: targetOrgNameValidator, trigger: "blur" }],
        sourceUploadFrequency: [
          { validator: sourceUploadFrequencyValidator, trigger: "blur" },
        ],
        sourceDataType: [
          { validator: sourceDataTypeValidator, trigger: "blur" },
        ],
        deadlineTimeRange: [
          { validator: deadlineTimeRangeValidator, trigger: "blur" },
        ],
        deadlineTimeValue: [
          { validator: deadlineTimeValueValidator, trigger: "blur" },
        ],
      },
      subjectStatusOptions: [
        {
          value: 0,
          label: "启用",
        },
        {
          value: 1,
          label: "禁用",
        },
      ],
      dataSourceOptions: [],
      sourceUploadFrequencyOptions: [
        {
          value: 1,
          label: "日",
        },
        {
          value: 2,
          label: "周",
        },
        {
          value: 3,
          label: "月",
        },
        {
          value: 4,
          label: "季",
        },
        {
          value: 5,
          label: "半年",
        },
        {
          value: 6,
          label: "年",
        },
      ],
      sourceDataTypeOptions: [
        {
          value: 0,
          label: "数据表",
        },
        {
          value: 1,
          label: "文件",
        },
        {
          value: 2,
          label: "目录",
        },
        {
          value: 3,
          label: "API",
        },
      ],
      deadlineTimeRangeOptions: [
        {
          value: 0,
          label: "本月",
        },
        {
          value: 1,
          label: "下一月",
        },
      ],
      symbolOptions: [
        {
          value: 1,
          label: "顺数",
        },
        {
          value: -1,
          label: "倒数",
        },
      ],
    };
  },
  created() {
    const subjectId = this.$route.query.subjectId;
    // if(this.$refs["dataSubjectForm"]!==undefined){
    //   //清除表单验证用
    //   this.$refs["dataSubjectForm"].clearValidate();
    // }
    if (!subjectId) {
      this.title = "新增数据主题";
      this.isNewData = true;
    } else {
      this.title = "编辑数据主题";
      this.isNewData = false;
      this.initSubjectData(subjectId);
    }
    this.initDataSourceOptions();
  },
  components: {
    orgTreeDialog,
  },
  methods: {
    goBackFront() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.go(-1);
    },
    save() {
      this.$refs["dataSubjectForm"].validate((valid) => {
        if (valid) {
          // 判断来源数据类型，生成来源数据实体sourceDataEntity
          var sourceDataEntity = {};
          if (this.dataSubjectForm.sourceDataType == 0) {
            // 数据表
            sourceDataEntity.tableName = this.dataSubjectForm.tableName;
            sourceDataEntity.fieldCreateTime = this.dataSubjectForm.fieldCreateTime;
            sourceDataEntity.fieldDataTime = this.dataSubjectForm.fieldDataTime;
          } else if (
            this.dataSubjectForm.sourceDataType == 1 ||
            this.dataSubjectForm.sourceDataType == 2
          ) {
            // 文件、目录
            sourceDataEntity.path = this.dataSubjectForm.path;
          } else if (this.dataSubjectForm.sourceDataType == 3) {
            // API
            sourceDataEntity.uri = this.dataSubjectForm.uri;
          }
          this.dataSubjectForm.sourceDataEntity = JSON.stringify(
            sourceDataEntity
          );
          if (this.isNewData) {
            doSave(this.dataSubjectForm).then(() => {
              this.$message({
                message: "保存成功",
                type: "success",
              });
              this.goBackFront();
            });
          } else {
            doUpdate(this.dataSubjectForm).then(() => {
              this.$message({
                message: "保存成功",
                type: "success",
              });
              this.goBackFront();
            });
          }
        }
      });
    },
    initSubjectData(subjectId) {
      getById({ subjectId: subjectId }).then((res) => {
        this.dataSubjectForm = res.data;
        if (
          res.data.subjectStatus != undefined &&
          res.data.subjectStatus != null
        ) {
          this.dataSubjectForm.subjectStatus = res.data.subjectStatus.value;
        }
        if (
          res.data.sourceDataType != undefined &&
          res.data.sourceDataType != null
        ) {
          this.dataSubjectForm.sourceDataType = res.data.sourceDataType.value;
        }
        if (
          res.data.sourceUploadFrequency != undefined &&
          res.data.sourceUploadFrequency != null
        ) {
          this.dataSubjectForm.sourceUploadFrequency =
            res.data.sourceUploadFrequency.value;
        }
        if (
          res.data.deadlineTimeUnit != undefined &&
          res.data.deadlineTimeUnit != null
        ) {
          this.dataSubjectForm.deadlineTimeUnit =
            res.data.deadlineTimeUnit.value;
        } else {
          this.dataSubjectForm.deadlineTimeUnit = 0;
        }
        this.dataSubjectForm.deadlineTimeValueSymbol =
          res.data.deadlineTimeValue == undefined ||
          res.data.deadlineTimeValue == null ||
          res.data.deadlineTimeValue >= 0
            ? 1
            : -1;
        let sourceDataEntity = JSON.parse(res.data.sourceDataEntity);
        if (this.dataSubjectForm.sourceDataType == 0) {
          // 数据表
          this.dataSubjectForm.tableName = sourceDataEntity.tableName;
          this.dataSubjectForm.fieldCreateTime =
            sourceDataEntity.fieldCreateTime;
          this.dataSubjectForm.fieldDataTime = sourceDataEntity.fieldDataTime;
        } else if (
          this.dataSubjectForm.sourceDataType == 1 ||
          this.dataSubjectForm.sourceDataType == 2
        ) {
          // 文件、目录
          this.dataSubjectForm.path = sourceDataEntity.path;
        } else if (this.dataSubjectForm.sourceDataType == 3) {
          // API
          this.dataSubjectForm.uri = sourceDataEntity.uri;
        }

        this.switchFrequency();
      });
    },
    initDataSourceOptions() {
      getDataSourceList().then((res) => {
        this.dataSourceOptions = [];
        res.data.forEach((element) => {
          this.dataSourceOptions.push({
            value: element.dsId,
            label: element.dsName,
          });
        });
      });
    },
    // 通过更新频率动态调整预警和督办的时间范围
    switchFrequency() {
      let val = "";
      for (let index in this.sourceUploadFrequencyOptions) {
        if (
          this.dataSubjectForm.sourceUploadFrequency ===
          this.sourceUploadFrequencyOptions[index].value
        ) {
          val = this.sourceUploadFrequencyOptions[index].label;
        }
      }
      this.deadlineTimeRangeOptions[0] = {
        value: 0,
        label: "本" + val,
      };
      this.deadlineTimeRangeOptions[1] = {
        value: 1,
        label: "下一" + val,
      };
    },
    openOrgTreeDialog(target) {
      this.orgSelectTarget = target;
      let keys = [];
      if (target == "source") {
        if (this.dataSubjectForm.sourceOrgId) {
          keys = [this.dataSubjectForm.sourceOrgId];
        }
      } else if (target == "target") {
        if (this.dataSubjectForm.targetOrgId) {
          keys = [this.dataSubjectForm.targetOrgId];
        }
      }
      this.$refs.orgTreeDialog.open(keys);
    },
    confirmOrg(checkedNodes) {
      if (checkedNodes.length != 1) {
        if (this.orgSelectTarget == "source") {
          this.dataSubjectForm.sourceOrgId = null;
          this.dataSubjectForm.sourceOrgName = "";
        } else {
          this.dataSubjectForm.targetOrgId = null;
          this.dataSubjectForm.targetOrgName = "";
        }
      } else {
        let data = checkedNodes[0];
        if (this.orgSelectTarget == "source") {
          this.dataSubjectForm.sourceOrgId = data.orgId;
          this.dataSubjectForm.sourceOrgName = data.orgName;
          this.$refs.dataSubjectForm.validateField("sourceOrgName");
        } else {
          this.dataSubjectForm.targetOrgId = data.orgId;
          this.dataSubjectForm.targetOrgName = data.orgName;
          this.$refs.dataSubjectForm.validateField("targetOrgName");
        }
      }
      this.$forceUpdate();
    },
  },
};
</script>

<style lang="scss" scoped>
.tab{
  color: #777; 
  // font-size: 1.25em;
    @include add-size($font_size_16);
}
</style>