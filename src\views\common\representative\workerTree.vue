<template>
  <a-modal
    :title="orgTreeDialogTitle"
    :visible.sync="newOrgTreeDialogVisible"
    width="50%"
    destroy-on-close
    @cancel="close"
  >
    <a-input-search
      v-model="filterText"
      style="width: 100%;"
      placeholder="输入关键字进行过滤"
      @search="onChange"
    ></a-input-search>
    <a-spin :indicator="indicator" :spinning="spinningShow">
      <a-tree
        ref="orgTree"
        v-model="checkIdentyData"
        checkable
        multiple
        :expanded-keys="expandedKeys"
        :replace-fields="replaceFields"
        :tree-data="orgTreeData"
        @check="onCheck"
        @expand="onExpand"
        @select="onSelect"
      >
        <template slot="title" slot-scope="{ fullName, orgLevel }">
          <a-icon
            :type="orgLevel == '1' ? 'apartment' : 'file'"
            style="margin-right: 10px;"
          />
          <span
            v-if="searchValue && fullName.indexOf(searchValue) > -1"
            style="color: #f50;"
            >{{ fullName }}</span
          >
          <span v-else>{{ fullName }}</span>
        </template>
      </a-tree>
    </a-spin>

    <span slot="footer" class="dialog-footer">
      <a-button @click="close">取 消</a-button>
      <a-button style="padding-left: 10px;" type="primary" @click="confirm"
        >确 定</a-button
      >
    </span>
  </a-modal>
</template>

<script>
import { personnelOU } from "@/api/registrationMage/tableIng.js";
import { instance_1 } from "@/api/axiosRq";

import { log } from "@antv/g2plot/lib/utils";
export default {
  props: {
    orgTreeDialogTitle: {
      type: String,
      default: "选择单位",
    },
  },
  data() {
    return {
      spinningShow: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      filterText: "",
      name: "",
      expandedKeys: [],
      searchValue: "",
      allChildData: [],
      selectedKeys: [],
      autoExpandParent: true,
      checked: [],
      checkIdentyData: [],
      orgTreeDialogVisible: false,
      newOrgTreeDialogVisible: null,
      orgTreeData: [],
      orgTreeDefaultKey: [],
      replaceFields: {
        title: "fullName",
        key: "id",
        children: "children",
      },
      isRadio: true,
      relType: {
        type: Number,
        default: 1,
      },
      noCheckKeys: [],
    };
  },
  mounted() {
    this.initOrgTree();
  },
  watch: {
    // orgTreeDialogVisible(newVal) {
    //   if (newVal) {
    //     this.newOrgTreeDialogVisible = true;
    //   } else {
    //     this.expandedKeys = [];
    //     this.checked = [];
    //     this.checkIdentyData = [];
    //   }
    // },
  },
  methods: {
    onSelect(item, data) {
      // if (data.selected) {
      //   this.checked = [data.node.dataRef];
      //   this.checkIdentyData = [data.node.dataRef.orgId];
      // }
    },
    // 树状搜索
    onChange(e) {
      this.spinningShow = true;

      const value = e;
      this.searchValue = value;
      if (value == "") {
        this.spinningShow = false;
        this.$message.info("请输入关键字");
      } else {
        this.expandedKeys = [];
        this.backupsExpandedKeys = [];
        console.log(value, "value");
        const candidateKeysList = this.getkeyList(value, this.orgTreeData, []);
        console.log(candidateKeysList, "candidateKeysList");
        candidateKeysList.forEach((item) => {
          const key = this.getParentKey(item, this.orgTreeData);
          if (key && !this.backupsExpandedKeys.some((item) => item === key))
            this.backupsExpandedKeys.push(key);
        });
        console.log(this.backupsExpandedKeys, "this.backupsExpandedKeys");
        const { length } = this.backupsExpandedKeys;
        for (let i = 0; i < length; i++) {
          this.getAllParentKey(this.backupsExpandedKeys[i], this.orgTreeData);
        }
        this.expandedKeys = this.backupsExpandedKeys.slice();
        console.log(this.expandedKeys, " this.expandedKeys ");
        if (candidateKeysList.length == 0) {
          this.spinningShow = false;

          this.$message.info("没有相关数据");
        }
        this.spinningShow = false;
      }
    },
    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.fullName.indexOf(value) > -1) {
          keyList.push(node.id);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey;
      let temp;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          temp = this.getParentKey(key, node.children);
          if (node.children.some((item) => item.id === key)) {
            parentKey = node.id;
          } else if (temp) {
            parentKey = temp;
          }
        }
      }
      return parentKey;
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey;
      if (key) {
        parentKey = this.getParentKey(key, tree);
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey);
          }
          this.getAllParentKey(parentKey, tree);
        }
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    close() {
      this.orgTreeDialogVisible = false;
      this.newOrgTreeDialogVisible = false;
      this.checked = [];
      this.filterText = "";
    },
    initOrgTree() {
      instance_1({
        url: "/dmcs/common/ours/findOUTree",
        method: "post",
        params: {
        },
      }).then((res) => {
        this.orgTreeData = res.data.data;
        // this.newOrgTreeDialogVisible = true;
      });
      // personnelOU().then((res) => {
      //   console.log(res);
      //   this.orgTreeData = res
      // });
    },

    // 树状选择
    onCheck(item, data) {
      // if (data.checked) {
      //   let value = data.node.dataRef;
      //   this.checked.push(value);
      // } else {
      //   let orgId = data.node.dataRef.orgId;
      //   this.checked.forEach((item, index) => {
      //     if (item.orgId == orgId) {
      //       this.checked.splice(index, 1);
      //     }
      //   });
      // }
      this.checked = [];
      data.checkedNodesPositions
        .filter(
          (it) => it.node.componentOptions.propsData.dataRef.entryType == 4
        )
        .forEach((item1) => {
          this.checked.push(item1.node.componentOptions.propsData.dataRef);
        });
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
    },
    confirm() {
      this.noCheckKeys = [];
      this.orgTreeDialogVisible = false;
      this.newOrgTreeDialogVisible = false;
      console.log(this.checked);
      this.$emit("confirm", this.checked);
      this.checkIdentyData = [];
      this.filterText = "";
    },
    handleShow(checkedData) {
      let that = this;
      if (Array.isArray(checkedData) && checkedData.length > 0) {
        checkedData.forEach((item) => {
          that.checkIdentyData.push(item);
        });
      }
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-tree {
  max-height: 500px;
  width: 100%;
  overflow-y: auto;
}
</style>
