import { instance_1 } from "@/api/axiosRq";
import qs from "qs";

// 各单位相关报表 列表
export function statisticsList(params) {
  return instance_1({
    url: "/dblz/lzbb/lzqktjQuery",
    method: "post",
    params,
  });
}
// 履职评分管理 评分管理 列表
export function activityPointsGetAll(params) {
  return instance_1({
    url: "/system/activityPoints/getAll",
    method: "get",
    params,
  });
}
// 履职评分管理 评分管理 保存
export function activityPointsSave(data) {
  return instance_1({
    url: "/system/activityPoints/save",
    method: "post",
    data,
  });
}
// 市代表个人履职情况统计表列表
export function statisticsMemberCount(params) {
  return instance_1({
    url: "/dblz/lzbb/lzqktjQuery",
    method: "post",
    params,
  });
}
// 各单位相关报表
export function Related(params) {
  return instance_1({
    url: "/dblz/lzbb/gdwtjQuery",
    method: "get",
    params,
  });
}

// 各单位录入代表活动综合报表
export function RelatedReports(params) {
  return instance_1({
    url: "/dblz/lzbb/gdwlrdbhdzhtjb",
    method: "post",
    params,
  });
}

//个人履职情况详情
export function PersonaDetails(params) {
  return instance_1({
    url: "/lzbb/dbgrlzxq/statistics",
    method: "post",
    params,
  });
}

//委员会活动情况详情
export function wyhReports(params) {
  return instance_1({
    url: "/statistics/getWyhStatistics",
    method: "post",
    params,
  });
}

//代表团活动情况详情
export function dbtReports(params) {
  return instance_1({
    url: "/statistics/getDbtStatistics",
    method: "post",
    params,
  });
}

//个人履职情况详情
export function getGrlzxqtj(params) {
  return instance_1({
    url: "/lzbb/dbgrlzxq/getGrlzxqtj",
    method: "post",
    params,
  });
}

//个人履职情况明细
export function getGrlzqkmxtj(params) {
  return instance_1({
    url: "/lzbb/dbgrlzqktjbmx/getGrlzqkmxtj",
    method: "get",
    params,
  });
}

//广州市人大常委会会议出勤情况表
export function getCwhhycqqk(params) {
  return instance_1({
    url: "/dblz/lzbb/cwhzccylzsc/getCwhhycqqk",
    method: "get",
    params,
  });
}
//重要会议和活动出勤情况表
export function getZyhyhdcqgk(params) {
  return instance_1({
    url: "/dblz/lzbb/cwhzccylzsc/getZyhyhdcqgk",
    method: "get",
    params,
  });
}
//提出议案及意见、批评和建议情况一览表
export function getYajyByDb(params) {
  return instance_1({
    url: "/statistics/yajy/getYajyByDb",
    method: "get",
    params,
  });
}
//查询全国省代表履职情况列表
export function getGsStatistics(data) {
  return instance_1({
    url: "/statistics/getGsStatistics",
    method: "post",
    data,
  });
}

export function userExportExcel(params) {
  return instance_1({
    url: `/dblz/lzbb/dbgrlzqktjbexport`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function enteringTableExportExcel(params) {
  return instance_1({
    url: `/dblz/lzbb/exportGdwlrdbhdzhtjb`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function organizationExportExcel(params) {
  return instance_1({
    url: `/dblz/lzbb/gdwzzdbhdzhtjbexport`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function detailExportExcel(params) {
  return instance_1({
    url: `/lzbb/dbgrlzqktjbmx/dbgrlzqktjbmxexport`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function userDataExportExcel(params) {
  return instance_1({
    url: `/lzbb/dbgrlzxq/export`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function wyhExportExcel(params) {
  return instance_1({
    url: `/statistics/wyhExport`,
    method: "post",
    responseType: "blob",
    params: params,
  });
}

export function dbtExportExcel(params) {
  return instance_1({
    url: `/statistics/dbtExport`,
    method: "post",
    responseType: "blob",
    params: params,
  });
}

//个人履职情况明细
export function grlzqkmxExcel(params) {
  return instance_1({
    url: "/lzbb/dbgrlzqktjbmx/excelExport",
    method: "get",
    responseType: "blob",
    params,
  });
}

//个人履职详情统计
export function grlzxqtjExport(params) {
  return instance_1({
    url: `/lzbb/dbgrlzxq/grlzxqtjExport`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//个人履职详情统计
export function cwhzccylzscExport(params) {
  return instance_1({
    url: `/dblz/lzbb/cwhzccylzsc/cwhzccylzscExport`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function gsExportExcel(params) {
  return instance_1({
    url: `/statistics/gsExport`,
    method: "post",
    responseType: "blob",
    data: params,
  });
}
