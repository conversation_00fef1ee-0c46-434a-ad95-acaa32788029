<template>
  <div>
    <a-row>
      <a-col :md="24" :sm="24">
        <a-card title="统计类">
          <a-row :gutter="[16, 16]" v-for="item in meetingList" :key="item.id">
            <a-col :md="3" v-for="col in item.col" :key="col.id">
              <a-card hoverable @click="goTable(col.path)" class="a-card-flex">
                <img slot="cover" :src="col.url" alt style="width:50px" />
                <a-card-meta :title="col.title"></a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
      <!-- <a-col :md="24" :sm="24">
        <a-card title="办理类">
          <a-row :gutter="[16, 16]" v-for="item in transactionList" :key="item.id">
            <a-col :md="3" v-for="col in item.col" :key="col.id">
              <a-card hoverable @click="goTable(col.path)" class="a-card-flex">
                <img slot="cover" :src="col.url" alt style="width:50px" />
                <a-card-meta :title="col.title"></a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>-->
      <a-col :md="24" :sm="24">
        <a-card title="活动类">
          <a-row :gutter="[16, 16]" v-for="item in activityList" :key="item.id">
            <a-col :md="3" v-for="col in item.col" :key="col.id">
              <a-card hoverable @click="goTable(col.path)" class="a-card-flex">
                <img slot="cover" :src="col.url" alt style="width:50px" />
                <a-card-meta :title="col.title"></a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
export default {
  name: "WorkIndex",
  components: {},
  data() {
    return {
      meetingImgUrl: require("@/assets/images/indexPage/tongji.png"),
      activityImgUrl: require("@/assets/images/indexPage/activity.png"),
      transactionImgUrl: require("@/assets/images/indexPage/banli.png"),
      meetingList: [
        {
          id: 1,
          col: [
            {
              id: 1,
              title: "统计分析",
              color: "#be403d",
              url: require("@/assets/images/indexPage/tongji.png"),
              path: "/Analyse/organizationData13",
            },
          ],
        },
      ],
      // transactionList: [
      //   {
      //     id: 1,
      //     col: [
      //       {
      //         id: 1,
      //         title: "待办",
      //         color: "#be403d",
      //         url: require("@/assets/images/indexPage/daiban.png"),

      //       },
      //       {
      //         id: 2,
      //         title: "已办",
      //         color: "#be403d",
      //         url: require("@/assets/images/indexPage/yiban.png"),

      //       },
      //     ],
      //   },
      // ],
      activityList: [
        {
          id: 1,
          col: [
            {
              id: 1,
              title: "活动通知",
              color: "#be403d",
              url: require("@/assets/images/indexPage/tonzhi.png"),
              path: "/huoDong/huoDongList",
            },
            {
              id: 2,
              title: "活动意向征集",
              color: "#be403d",
              url: require("@/assets/images/indexPage/yixiangzhengji.png"),
              path: "/collect/organizationData3",
            },
            {
              id: 5,
              title: "活动签到",
              color: "#be403d",
              url: require("@/assets/images/indexPage/qiandao.png"),
              path: "/Registration/signedIng",
            },
            {
              id: 6,
              title: "活动查阅",
              color: "#be403d",
              url: require("@/assets/images/indexPage/chayue.png"),
              path: "/Activities/organizationData12",
            },
          ],
        },
      ],
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "工作人员首页");
  },
  methods: {
    goTable(path) {
      console.log("🤗🤗🤗, path =>", path);
      this.$router.push({ path: path });
    },
  },
};
</script>
