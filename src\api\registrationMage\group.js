import { instance_1 } from "@/api/axiosRq";
export function getAllGroup() {
  return instance_1({
    url: "/group/getAll",
    method: "get",
  });
}

export function createGroup(data) {
  return instance_1({
    url: "/group/create",
    method: "post",
    data: data
  });
}

export function updateGroup(data) {
  return instance_1({
    url: "/group/update",
    method: "post",
    data: data
  });
}

export function deleteGroup(params) {
  return instance_1({
    url: "/group/delete",
    method: "post",
    params: params
  });
}

export function getAllGroupMember(params) {
  return instance_1({
    url: "/groupMember/getAll",
    method: "get",
    params: params
  });
}

export function batchInsertGroupMember(params, data) {
  return instance_1({
    url: "/groupMember/batchInsert",
    method: "post",
    params: params,
    data: data
  });
}

export function batchDeleteGroupMember(params, data) {
  return instance_1({
    url: "/groupMember/batchDelete",
    method: "post",
    params: params,
    data: data
  });
}

