<template>
  <!-- 详情页内容 -->
  <a-modal title="统计情况"
           :visible.sync="visible"
           width="80%"
           :footer="null"
           @cancel="close">
    <div>
      <a-row>
        <a-col :span="6">用户总人数 :{{ StatisticsFrom.peopleSum }}人</a-col>
        <a-col :span="6">已学习人数:{{ StatisticsFrom.studyNum.YESSTUDY }}人</a-col>
        <a-col :span="6">未学习人数 :{{ StatisticsFrom.studyNum.NOTSTUDY }}人</a-col>
      </a-row>
      <a-row>
        <a-col :span="6">代表总人数:{{ StatisticsFrom.behalfSum }}人</a-col>
        <a-col :span="6">代表学习人数:{{ StatisticsFrom.dbStudy.DBSTUDY }}人</a-col>
        <a-col :span="6">代表未学习人数:{{ StatisticsFrom.dbStudy.DBNOTSTUDY }}人</a-col>
      </a-row>
      <a-row class="formBox">
        <a-form-model ref="queryForm"
                      :model="queryForm"
                      layout="inline">
          <!-- <a-col span="8">
            <a-form-model-item label="栏目">
                <div class="searchStyle">
                    <a-input
                    disabled
                    v-model="inviteRangeDesc"
                    enter-button
                  >
                  </a-input>
                   <a-button type="primary" icon="search" @click="openAttributionColumn">
                   </a-button>
                  </div> 
                      </a-form-model-item>
        </a-col> -->
          <a-col span="6">
            <a-form-model-item label="姓名">
              <a-input v-model="queryForm.name"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="联系方式">
              <a-input v-model="queryForm.phone"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
          <!-- <a-col span="6">
            <a-form-model-item label="完成时间">
          <a-date-picker
          v-model="queryForm.time"
          allow-clear
          value-format="YYYY-MM-DD HH:mm:ss"
          show-time
           style="width: 100%"
          > 
          </a-date-picker> 
            </a-form-model-item>
          </a-col> -->
          <a-col :span="6">
            <a-form-model-item>
              <span style="float: right;">
                <a-button type="primary"
                          style="margin-left: 12px;"
                          @click="fetchDataList">搜索</a-button>
                <a-button style="margin-left: 12px;"
                          class="pinkBoutton"
                          @click="reset">重置</a-button>
                <a-button type="primary"
                          style="margin-left: 12px;"
                          :loading="downloadLoading"
                          @click="download">导出</a-button>
              </span>
            </a-form-model-item>
          </a-col>
        </a-form-model>
      </a-row>
      <a-row>
        <standard-table :columns="columns"
                        :row-key="
            (record, index) => {
              return record.USER_ID;
            }
          "
                        :data-source="dataSource"
                        :loading="TBloading"
                        :pagination="pagination">
          <!-- :selectedRows.sync="selectedRows"
        @selectedRowChange="onSelectChange" -->
        </standard-table>
      </a-row>
    </div>
    <!-- 定义了插槽 -->
    <slot slot="footer">
      <!-- <a-button style="padding-left: 10px" type="primary" @click="confirm">确 定</a-button>  
            <a-button type="primary" @click="close">关闭</a-button>  -->
    </slot>
    <!-- 归属栏目 -->
    <!-- <attributionTree
      ref="attributionTree"
      @confirm="confirmattributionTree"
    ></attributionTree> -->
  </a-modal>
</template>
<script>
import viewArticleDetails from "@/views/common/viewArticleDetails.vue";
import attributionTree from "@/views/common/attributionTree.vue";
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
import { instance_1 } from "@/api/axiosRq";
import { log } from "@antv/g2plot/lib/utils";
export default {
  name: "LearningStatistics",
  components: { StandardTable, attributionTree, viewArticleDetails },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      StatisticsFrom: {
        peopleSum: null, //  用户总人数
        behalfSum: null, //代表总人数
        // 统计已学习人数和未学习人数
        //已学习的用户信息
        studyNum: {
          NOTSTUDY: null, //未学习人数，
          YESSTUDY: null, //已学习人数，
        },
        dbStudy: {
          DBNOTSTUDY: null, //代表未学习人数
          DBSTUDY: null, //代表学习人数，
        },
      },
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        flag: 0,
      },
      inviteRangeDesc: "",
      //选中tableKey
      tableKey: [],
      //tableData
      tableData: [],
      visible: false,
      dataSource: [],
      selectedRows: [],
      articleId: "",
      title: null,
      //列头
      columns: [
        // {
        //   title: "栏目",
        //   align: "center",
        //   ellipsis: true,
        //   dataIndex: "category.name",
        //    customRender: (text, record, index) => {
        //    let h = this.$createElement;
        //       return h("div", [
        //         h(
        //           "span",
        //           {
        //             attrs: {
        //               type: "text",
        //             },
        //             on: {
        //               click: () => {
        //                 this.toDetailed(record);
        //               },
        //             },
        //           },
        //           text
        //         ),
        //       ]);
        //   },
        // },
        {
          title: "姓名",
          align: "center",
          ellipsis: true,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联系方式",
          align: "center",
          ellipsis: true,
          dataIndex: "mobile",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "邮箱",
          align: "center",
          ellipsis: true,
          dataIndex: "email",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "学习开始时间",
          align: "center",
          ellipsis: true,
          dataIndex: "createDate",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
      ],
      downloadLoading: false,
    };
  },
  created () { },
  methods: {
    // 导出
    download (e) {
      let data = this.queryForm;
      this.downloadLoading = true;
      instance_1({
        url: "content/statistics/schedule/exportStudyPeople",
        method: "get",
        responseType: "blob",
        params: data,
      }).then((res) => {
        // console.log("🤗🤗🤗, res =>", res);
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = this.title + `统计表` + `.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000);
      });
    },
    //  confirm() {
    //   this.visible = false;
    //   // console.log(this.selectedRows)
    //   this.$emit("confirm", this.selectedRows);
    // },
    //重置
    reset () {
      var id = this.articleId;
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        articleId: id,
      };
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchDataList();
    },
    //打开归属栏目树
    openAttributionColumn () {
      this.$refs.attributionTree.initOrgTree();
      // 弹窗
    },
    //归属栏目返回的结果
    confirmattributionTree (data) {
      this.queryForm.categoryId = data.node.dataRef.id;
      this.inviteRangeDesc = data.node.dataRef.name;
      this.fetchData();
    },

    // 查看
    toDetailed () { },
    //复选框选改变
    onSelectChange (key, data) {
      this.selectedRows = data;
      this.tableKey = key;
      this.tableData = data;
    },
    // 数据渲染
    fetchData (id) {
      this.queryForm.articleId = id;
      this.articleId = id;
      instance_1({
        url: "/content/statistics/schedule/statisticsStudyPeople",
        method: "get",
        params: { articleId: id },
      }).then((res) => {
        if (res.data.code == "0000") {
          this.StatisticsFrom = res.data.data;
        }
      });
      this.fetchDataList();
    },
    fetchDataList () {
      this.TBloading = true;

      instance_1({
        url: "/content/statistics/schedule/completeUserStudy",
        method: "get",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "200") {
          this.dataSource = res.data.rows;
          this.pagination.total = res.data.total;

          this.TBloading = false;
        }
      });
    },

    // 关闭窗口
    close () {
      this.articleId = "";
      this.dataSource = [];
      this.visible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
// 自定义搜索框样式
.searchStyle {
  display: flex !important;

  .ant-input {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }

  .ant-btn-primary {
    width: 66px;
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
</style>
