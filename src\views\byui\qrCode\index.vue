<template>
  <div class="qr-code-container">
    <a-row :gutter="15">
      <a-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card shadow="hover">
          <div slot="header"><span>二维码示例</span></div>
          <a target="_blank" :href="url">
            <byui-qr-code :image-path="imagePath" :url="url"></byui-qr-code>
          </a>
        </el-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import ByuiQrCode from "@/components/ByuiQrCode";

export default {
  name: "QrCode",
  components: {
    ByuiQrCode,
  },
  data() {
    return {
      url: "https://www.baidu.com",
      imagePath: require("@/assets/qr_logo/lqr_logo.png"),
    };
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.qr-code-container {
  ::v-deep {
    .el-card__body {
      display: flex;
      align-content: center;
      justify-content: center;
    }
  }
}
</style>
