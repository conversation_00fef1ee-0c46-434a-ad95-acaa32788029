<template>
  <a-modal
    :title="title"
    :visible.sync="dialogFormVisible"
    width="1200px"
    @cancel="close"
  >
    <a-input
      placeholder="输入关键字进行过滤"
      v-model="filterText"
      @change="onChange"
    ></a-input>
    <div id="app">
      <a-row class="mt20">
        <a-col :span="24">
          <a-tabs v-model="tabActive" @tab-click="tabClick">
            <a-tab-pane key="tree" tab="机构树">
              <a-tree
                ref="userTree"
                checkable
                multiple
                @expand="onExpand"
                :replace-fields="replaceFields"
                :tree-data="userTreeData"
                :default-checked-keys="userTreeDefaultKey"
                v-model="userTreeDefaultKey"
                :expanded-keys="expandedKeys"
                :auto-expand-parent="autoExpandParent"
                @check="onCheck"
              >
                <template slot="title" slot-scope="{ name }">
                  <span
                    v-if="searchValue && name.indexOf(searchValue) > -1"
                    style="color: #f50;"
                  >
                    {{ name }}
                  </span>
                  <span v-else>{{ name }}</span>
                </template>
              </a-tree>
            </a-tab-pane>
          </a-tabs>
        </a-col>
      </a-row>
    </div>
    <div
      slot="footer"
      class="dialog-footer"
      style="position: relative; padding-right: 15px; text-align: right;"
    >
      <a-button @click="close">关闭</a-button>
      <a-button
        type="primary"
        :loading="loading"
        style="margin-left: 10px;"
        @click="confirm"
      >
        保存
      </a-button>
    </div>
  </a-modal>
</template>
<script>
import {getIdentityTypeList} from "@/api/identity";
import {getTreeOrderByXsbh} from "@/api/user";
import checkPermission from "@/utils/permission";

export default {
  props: {
    rootOrgId: {
      type: String,
      default: "root",
    },
    userTreeDefaultKey: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      max: 99,
      userKeys: ["eee745a6a3c846e5994aac7f5f15c59f"],
      tabActive: "identity",
      tabName: "参加人员",
      //选择类型：1 身份选择 0 树选择 2 发布单位选择；不显示树 3 日程关联人员不显示树
      type: "1",
      loading: false,
      checkedShow: false,
      title: "",
      filterText: "",
      dialogFormVisible: false,
      //身份选择参数
      identityTypeUserList: [],
      checked: [],
      checkIdentyData: [],
      allChildData: [],
      selectedKeys: [],
      //用户树参数
      userTreeData: [],
      treeCheckedKey: [],
      replaceFields: {
        title: "name",
        key: "id",
        children: "children",
      },
      // 搜索值
      searchValue: "",
      timer: "",
      // 展开的节点
      expandedKeys: [],
      // 是否自动展开
      autoExpandParent: true,
      copyExpandedKeys: [],
    };
  },
  created() {
    this.initIdentityList();
    this.initUserTree();
  },
  watch: {
    userKeys(data) {
      console.log(data);
    },
    filterText(val) {
      // this.$refs.userTree.filter(val);
    },
  },
  methods: {
    //展开点击节点下的子节点
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    // 树状搜索
    onChange(e) {
      // 输入框防抖
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        const value = e.target.value;
        console.log("🤗🤗🤗, value =>", value);
        if (!value) return;
        this.$message.destroy();
        this.copyExpandedKeys = [];
        const candidateKeysList = this.getkeyList(value, this.userTreeData, []);
        if (candidateKeysList.length == 0) {
          // 销毁提示
          this.$message.destroy();
          this.copyExpandedKeys = [];
          this.$message.info("没有相关数据");
        }
        Object.assign(this, {
          expandedKeys: [...new Set(this.copyExpandedKeys)],
          searchValue: value,
          autoExpandParent: true,
        });
        this.$forceUpdate();
      }, 800);
    },
    // 获取关键字的节点
    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.name.indexOf(value) > -1) {
          keyList.push(node.id);
          this.copyExpandedKeys.push(node.parentId);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    tabClick(tab, event) {
      this.tabActive = tab.name;

      if (this.type == "0" || this.type == "1") {
        if (tab.name == "tree") {
          this.type = "0";
        } else {
          this.type = "1";
        }
      }
    },
    initUserTree() {
      let data = {
        tenantId: this.$tenantId,
        rootOrgId: this.rootOrgId,
      };
      getTreeOrderByXsbh(data).then((res) => {
        this.userTreeData = res.data.children;
      });
    },
    initIdentityList() {
      getIdentityTypeList().then((res) => {
        this.identityTypeUserList = res.data;
      });
    },

    handleHide() {
      this.dialogFormVisible = false;
    },

    handleShow(type, checkedData) {
      let that = this;
      if (type == "0") {
        that.title = "选择人员(单选)";
        this.tabActive = "tree";
        this.tabName = "参加人员";
      }
      if (type == "1") {
        that.title = "选择发送范围";
        this.tabActive = "identity";
        this.tabName = "参加人员";
      }
      if (type == "2") {
        that.title = "选择发布单位";
        this.tabActive = "identity";
        this.tabName = "发布单位";
      }
      if (type == "3") {
        that.title = "选择参加人员";
        this.tabActive = "identity";
        this.tabName = "参加人员";
      }
      that.$forceUpdate();

      that.type = type;
      that.dialogFormVisible = true;
      that.checkIdentyData = [];
      that.treeCheckedKey = [];
      // that.userTreeDefaultKey = [];

      //回选旧数据
      if (type == "1" || type == "2" || type == "3") {
        if (checkedData.length > 0) {
          checkedData.forEach((item) => {
            that.checkIdentyData.push(item);
          });
        }
      }

      if (type == "0") {
        if (checkedData.length > 0) {
          checkedData.forEach((item) => {
            // console.log(item);
            // let id=  item.split(';')[1]
            console.log(item);
            // that.userTreeDefaultKey.push(item);
            that.treeCheckedKey.push(item);
          });
        }
      }

      if (type == "2") {
        this.max = 1;
      } else {
        this.max = 99;
      }
      that.initUserTree();
    },
    onSelect(data) {
      console.log(data, "onSelect");
    },
    // 树状选择
    onCheck(item, data) {
      if (data.checked && !data.node.dataRef.children) {
        //  this.checkedShow=true
        let value = data.node.dataRef;
        this.checked.push(value);
        // data.checkedNodesPositions.filter((item1) => {
        //   this.checked.push(item1.node.componentOptions.propsData.dataRef);
        // });
      } else {
        let Id = data.node.dataRef.id;
        this.checked.forEach((item, index) => {
          if (item.id == Id) {
            this.checked.splice(index, 1);
          }
        });
      }
    },

    confirm() {
      let checkedData = [];
      if (this.type == "1" || this.type == "2" || this.type == "3") {
        checkedData = this.checkIdentyData;
      }
      if (this.type == "0") {
        // checkedData = this.$refs.userTree.getCheckedNodes();
        // this.$refs.userTree.setCheckedKeys([]);
      }
      if (this.type == "0" || this.type == "1") {
        if (this.$refs.userTree != null) {
          // this.$refs.userTree.setCheckedKeys([]);
        }
      }
      // 判断是否为树状数据
      if (this.checkedShow) {
        checkedData = this.checked;
      }

      this.treeCheckedKey = [];
      this.max = 99;
      //清空树勾选节点
      //收起所有树节点
      //this.$refs.userTree.$children.forEach((item) => (item.expanded = false));
      //回调父页面数据
      if (this.type != "2") {
        if (this.checked.length == 0) {
          this.$message.error("暂未选择用户数据");
        } else {
          this.$parent.saveRel(this.type, checkedData, this.checked);
        }
      } else {
        this.$parent.savePublish(checkedData);
      }
      this.dialogFormVisible = false;
      this.checkIdentyData = [];
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    close() {
      this.userTreeDefaultKey = [];
      this.checkIdentyData = [];
      this.dialogFormVisible = false;
    },

    handleCheckedIdentity(value) {
      this.checkedShow = false;
      this.checkIdentyData = this.unique(value);
    },

    //数组去重
    unique(arr) {
      return Array.from(new Set(arr));
    },
    checkPermission,
  },
};
</script>
<style scoped>
.a-checkbox.is-bordered.a-checkbox--mini {
  margin-bottom: 5px;
  margin-left: 10px;
}
</style>
