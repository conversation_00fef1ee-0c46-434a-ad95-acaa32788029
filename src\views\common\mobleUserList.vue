<template>
  <div class="container">
    <a-row class="content">
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="24"
             :xl="24">
        <el-table ref="meetingTable"
                  :data="list"
                  border>
          <el-table-column label="帐号"
                           prop="userAccount"
                           width="150"></el-table-column>
          <el-table-column label="姓名"
                           prop="userName"
                           width="120"></el-table-column>
          <el-table-column label="手机号"
                           width="80"
                           prop="mobile"></el-table-column>
          <el-table-column prop="id"
                           label="操作"
                           width="80px"
                           fixed="right">
            <template slot-scope="scope">
              <p>
                <el-button type="text"
                           style="color: #e6a23c;"
                           @click="Details(scope.row.id)">详情</el-button>
              </p>
            </template>
          </el-table-column>
        </el-table>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getCaptcha } from "@/api/captcha";
import { doUpdata, validateAccount, sendMsg } from "@/api/mobile";
export default {
  components: {},
  name: "TableEdit",
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
  },
  data() {
    return {
      title: "请选择登录帐号",
      list: [],
      elementLoadingText: "正在加载...",
      flag: 1,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {
    this.isUpdate = 1;
    this.scrollHeight = window.innerHeight + "px";
  },
  mounted() {
    if ("production" !== process.env.NODE_ENV) {
    }
    setTimeout(() => {
      this.animateShow = true;
    });
    // 得到验证码图片
    // this.changeCode();
  },
  methods: {
    handleShow(row) {
      console.log("---->   " + JSON.stringify(row.data.userList));
      this.list = row.data.userList;
    },
    close() {
      this.dialogFormVisible = false;
    },
    validateAcc() {
      var that = this;
      var code = that.mobildForm.code;
      var key = that.mobildForm.key;
      const data2 = {
        code: that.mobildForm.code,
        key: that.mobildForm.key,
      };

      i;
    },
    handleMobileSubmit() {
      const data2 = {
        mobile: this.mobildForm.mobile,
        smscode: this.mobildForm.smscode,
      };
      this.$refs["mobildForm"].validate((valid) => {
        if (valid) {
          if (this.mobildForm.smscode == "") {
            this.$message({
              message: "手机验证码不能为空",
              type: "error",
            });
            return false;
          }

          this.$store
            .dispatch("user/mobileLogin", data2)
            .then((res) => {
              console.log(
                JSON.stringify(res) + "--------" + res.data.mobileToken
              );
              if (
                res.data.mobileToken == "" ||
                res.data.mobileToken == undefined
              ) {
                const routerPath =
                  this.redirect === "/404" ? "/" : this.redirect;
                this.$router.push({ path: "/" }).catch(() => {});
                this.loading = false;
              } else {
                this.$refs["uListFram"].handleShow("1");
              }
            })
            .catch((error) => {
              console.log(error);
              if (error.message != "验证码不匹配") {
                this.changeCode();
              }
            });
        }
      });
    },

    changeCode() {
      let that = this;
      getCaptcha().then((res) => {
        if (res.code == 200) {
          that.mobildForm.key = res.data.key;
          that.mobildForm.generateValue = res.data.generateValue;
          let file = res.data.image; // 把整个base64给file
          var type = "image/png"; // 定义图片类型（canvas转的图片一般都是png，也可以指定其他类型）
          let conversions = that.base64ToBlob(file, type); // 调用base64转图片方法----vue写法
          // window.URL = window.URL || window.webkitURL; //blob对象转换为blob-url
          var url = window.URL.createObjectURL(conversions);
          that.imgData = url;
        }
      });
    },
    /**
     * base64转blob
     * 原理：利用URL.createObjectURL为blob对象创建临时的URL
     */
    base64ToBlob(urlData, type) {
      let arr = urlData.split(",");
      let mime = arr[0].match(/:(.*?);/)[1] || type;
      // 去掉url的头，并转化为byte
      let bytes = window.atob(arr[1]);
      // 处理异常,将ascii码小于0的转换为大于0
      let ab = new ArrayBuffer(bytes.length);
      // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
      let ia = new Uint8Array(ab);
      for (let i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
      }
      return new Blob([ab], {
        type: mime,
      });
    },
    sendcodeMsg() {
      var that = this;
      var code = that.mobildForm.code;
      var mobile = that.mobildForm.mobile;

      const data2 = {
        key: that.mobildForm.key,
        mobile: that.mobildForm.mobile,
        code: that.mobildForm.code,
      };
      if (code == "") {
        this.$message({
          message: "请输入图形验证码",
          type: "error",
        });
        return false;
      }
      if (mobile.length != 11) {
        this.$message({
          message: "请输入正确的手机号",
          type: "error",
        });
        return false;
      }

      var code = that.mobildForm.code;
      var generateValue = that.mobildForm.generateValue;
      if (code != generateValue) {
        this.$message({
          message: "动态验证码不正确",
          type: "error",
        });
        return false;
      }

      var countDown = setInterval(() => {
        if (this.count < 1) {
          this.isGeting = false;
          this.disab = false;
          this.getCode = "获取验证码";
          this.count = 120;
          clearInterval(countDown);
        } else {
          this.isGeting = true;
          this.disab = true;
          this.getCode = this.count-- + "s后重发";
        }
      }, 1000);

      sendMsg(data2).then(() => {});
    },
  },
};
</script>
<style scoped>
.el-dialog__header {
  padding: 38px;
  padding-bottom: 10px;
}

.wdLabel {
  width: 80%;
  margin-right: 20px;
}

.smsButton {
  height: 43px;
  border-radius: 5px;
  background-color: #1890ff;
  color: white;
  width: 120px;
}

.el-form-item__label {
  width: 200px !important;
}

.new-fgbtn {
  width: 50% !important;
}

.wdLabel .el-form-item__error {
  left: 129px;
}

.codeGeting {
  background: #cdcdcd;
  border-color: #cdcdcd;
}
</style>
<style lang="scss" scoped>
.container {
  width: 1000px;
  height: auto;
  margin: 0 auto;
  // font-size: 16px;
   @include add-size($font_size_16);
}
.content {
  margin-top: 30px;
}
.search {
  margin-right: 20px;
}
</style>
