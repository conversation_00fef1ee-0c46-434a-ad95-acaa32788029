<template>
  <div class="represen-box">
    <div class="Steps">
      <a-steps :current="1">
        <a-step title="登记" />
        <a-step title="初审" />
        <a-step title="终审" />
        <a-step title="完成修改" />
      </a-steps>
    </div>
    <div class="represen">
      <a-form-model :model="ruleForm"
                    :rules="rules"
                    ref="ruleForm"
                    :label-col="{ span: 8 }"
                    :wrapper-col="{ span: 16 }">
        <a-collapse expandIconPosition="right"
                    v-model="activeKey">
          <a-collapse-panel header="基本信息"
                            key="k1"
                            :style="customStyle">
            <a-row :gutter="50">
              <a-col :span="18">
                <a-row type="flex">
                  <a-col :span="6">
                    <a-form-model-item label="选择地区"
                                       prop="fullName">
                      <div class="searchStyle">
                        <a-input disabled
                                 v-model="dataRef.fullName"
                                 enter-button></a-input>
                        <a-button type="primary"
                                  icon="search"
                                  @click="handleRel"></a-button>
                      </div>
                      <!-- <a-input-search
                         v-model="dataRef.fullName"
                         enter-button
                         @search="handleRel"
                       >
                      </a-input-search>-->
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="届次"
                                       prop="jcDm"
                                       class="jiec">
                      <span class="jicson">*</span>
                      <a-select v-model="jcDm">
                        <a-select-option v-for="item in thNumOptions"
                                         :key="item.termId"
                                         :label="item.termName"
                                         :value="item.termId">{{ item.termName }}</a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="代表证号">
                      <a-input v-model="ruleForm.dbzHm"
                               :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="姓名"
                                       prop="userName">
                      <a-input v-model="ruleForm.userName"
                               :disabled="oper == constat.VIEW">></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="性别"
                                       prop="xbDm">
                      <a-select v-model="ruleForm.xbDm"
                                :disabled="oper == constat.VIEW"
                                placeholder="请选择">
                        <a-select-option v-for="(item, index) in sex"
                                         :value="item.xbDm"
                                         :key="index">{{ item.sex }}
                        </a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="出生日期"
                                       prop="birthday">
                      <a-date-picker value-format="YYYY-MM-DD"
                                     allow-clear
                                     v-model="ruleForm.birthday"
                                     :disabled="oper == constat.VIEW" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="民族"
                                       prop="mzDm">
                      <a-select v-model="ruleForm.mzDm"
                                :disabled="oper == constat.VIEW"
                                placeholder="请选择">
                        <a-select-option v-for="(item, index) in nationality"
                                         :value="item.mzDm"
                                         :key="index">{{
                            item.mzmc
                        }}</a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="籍贯"
                                       prop="nativePlace">
                      <a-input v-model="ruleForm.nativePlace"
                               :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="证件类型">
                      <!--  prop="zjlxDm" 10-20改 -->
                      <a-select v-model="ruleForm.zjlxDm"
                                :disabled="oper == constat.VIEW"
                                placeholder="请选择">
                        <a-select-option v-for="(item, index) in cardType"
                                         :value="item.zjlxDm"
                                         :key="index">{{
                            item.zjlxmc
                        }}</a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="证件号">
                      <!-- prop="sfzDm" 10-20改 -->
                      <a-input v-model="ruleForm.sfzDm"
                               :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="党派"
                                       prop="zzmmDm">
                      <a-select v-model="ruleForm.zzmmDm"
                                :disabled="oper == constat.VIEW"
                                placeholder="请选择">
                        <a-select-option v-for="(item, index) in politicalAffiliation"
                                         :value="item.zzmmDm"
                                         :key="index">{{ item.politicsStatusName }}</a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <!-- show-time -->
                    <a-form-model-item label="加入日期"
                                       prop="joinTime">
                      <a-date-picker value-format="YYYY-MM-DD"
                                     allow-clear
                                     v-model="ruleForm.joinTime"
                                     :disabled="oper == constat.VIEW" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="工作单位"
                                       prop="workUnit">
                      <a-input v-model="ruleForm.workUnit"
                               :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="参加工作日期">
                      <!-- show-time -->
                      <a-date-picker value-format="YYYY-MM-DD"
                                     allow-clear
                                     v-model="ruleForm.joinWorkTime"
                                     :disabled="oper == constat.VIEW" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="职位"
                                       prop="duty">
                      <a-input v-model="ruleForm.duty"
                               :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item label="职称"
                                       prop="dutyName">
                      <a-input v-model="ruleForm.dutyName"
                               :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="4">
                <a-form-model-item label
                                   prop="name">
                  <div class="userImage">
                    <a-upload ref="uploadPic"
                              list-type="picture-card"
                              :file-list="fileList"
                              @change="handlePicChange"
                              accept="image/*"
                              :customRequest="uploadFile"
                              :before-upload="fileBeforeUpload"
                              @preview="handlePreview">
                      <!-- 设置文件上传个数限制 -->
                      <div v-if="Boolean(fileList) && fileList.length < 1">
                        <a-icon type="plus" />
                        <div>上传</div>
                      </div>
                    </a-upload>
                    <!-- <img src="../../../assets/user.gif" alt /> -->
                  </div>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel header="教育情况"
                            key="k2"
                            :style="customStyle">
            <a-row type="flex">
              <a-col :span="6">
                <a-form-model-item label="全日制教育学历"
                                   prop="qrzjyxlDm">
                  <a-select v-model="ruleForm.qrzjyxlDm"
                            :disabled="oper == constat.VIEW"
                            placeholder="请选择">
                    <a-select-option v-for="(item, index) in education"
                                     :value="item.xlDm"
                                     :key="index">{{ item.xlmc }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="全日制教育学位"
                                   prop="qrzjyxwDm">
                  <a-select v-model="ruleForm.qrzjyxwDm"
                            :disabled="oper == constat.VIEW"
                            placeholder="请选择">
                    <a-select-option v-for="(item, index) in degree"
                                     :value="item.xwDm"
                                     :key="index">{{ item.xwmc }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="毕业院校"
                                   prop="fullSchool">
                  <a-input v-model="ruleForm.fullSchool"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="系及专业"
                                   prop="fullMajor">
                  <a-input v-model="ruleForm.fullMajor"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="在职教育学历"
                                   prop="zzjyxlDm">
                  <a-select v-model="ruleForm.zzjyxlDm"
                            :disabled="oper == constat.VIEW"
                            placeholder="请选择">
                    <a-select-option v-for="(item, index) in education"
                                     :value="item.xlDm"
                                     :key="index">{{ item.xlmc }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="在职教育学位"
                                   prop="zzjyxwDm">
                  <a-select v-model="ruleForm.zzjyxwDm"
                            :disabled="oper == constat.VIEW"
                            placeholder="请选择">
                    <a-select-option v-for="(item, index) in degree"
                                     :value="item.xwDm"
                                     :key="index">{{ item.xwmc }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="毕业院校"
                                   prop="jobSchool">
                  <a-input v-model="ruleForm.jobSchool"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="系及专业"
                                   prop="serviceEducation">
                  <a-input v-model="ruleForm.serviceEducation"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel header="联系方式"
                            key="k3"
                            :style="customStyle">
            <a-row type="flex">
              <a-col :span="6">
                <a-form-model-item label="单位地址"
                                   prop="unitAddress">
                  <a-input v-model="ruleForm.unitAddress"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="单位电话">
                  <a-input v-model="ruleForm.unitPhone"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="单位邮编">
                  <a-input v-model="ruleForm.unitPostalCode"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="电子邮件">
                  <a-input v-model="ruleForm.yzbmDm"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="家庭地址">
                  <a-input v-model="ruleForm.houseAddress"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="家庭电话">
                  <a-input v-model="ruleForm.housePhone"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="家庭邮编">
                  <a-input v-model="ruleForm.housePostalCode"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <!-- 占位 -->
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="手机"
                                   prop="phone">
                  <a-input v-model="ruleForm.phone"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="手机2"
                                   prop="twoPhone">
                  <a-input v-model="ruleForm.twoPhone"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="手机3"
                                   prop="threePhone">
                  <a-input v-model="ruleForm.threePhone"
                           :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel header="选举信息"
                            key="k4"
                            :style="customStyle">
            <a-row type="flex">
              <a-col :span="6">
                <a-form-model-item label="职业构成"
                                   prop="name">
                  <a-input :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="选举单位"
                                   prop="name">
                  <a-input :disabled="oper == constat.VIEW"></a-input>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel header="其他"
                            key="k5"
                            :style="customStyle"
                            v-if="oper == constat.VIEW">
            <a-form-model-item prop="isOverseascn"
                               :label-col="{ span: 2 }">
              <a-row type="flex">
                <a-col :span="6"
                       :push="4">
                  <a-checkbox v-model="ruleForm.isOverseascn"
                              :disabled="oper == constat.VIEW">是否归侨眷属</a-checkbox>
                </a-col>
                <a-col :span="6"
                       :push="4">
                  <a-checkbox v-model="ruleForm.isPeasant"
                              :disabled="oper == constat.VIEW">是否农民工</a-checkbox>
                </a-col>
                <a-col :span="6"
                       :push="4">
                  <a-checkbox v-model="ruleForm.isCivilServant"
                              :disabled="oper == constat.VIEW">是否公务员</a-checkbox>
                </a-col>
                <a-col :span="6"
                       :push="4">
                  <a-checkbox v-model="ruleForm.isReappointment"
                              :disabled="oper == constat.VIEW">是否连任代表</a-checkbox>
                </a-col>
              </a-row>
              <a-row type="flex">
                <a-col :span="6"
                       :push="4">
                  <a-checkbox v-model="ruleForm.isRepresentative"
                              :disabled="oper == constat.VIEW">是否同任两级以上代表
                  </a-checkbox>
                </a-col>
                <a-col :span="6"
                       :push="4">
                  <a-checkbox v-model="ruleForm.isUnitLeader"
                              :disabled="oper == constat.VIEW">是否事业单位负责人</a-checkbox>
                </a-col>
                <a-col :span="6"
                       :push="4">
                  <a-checkbox v-model="ruleForm.isPartyBranch"
                              :disabled="oper == constat.VIEW">是否村委会村党支部组成人员
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-form-model-item>
          </a-collapse-panel>
        </a-collapse>
        <a-row type="flex"
               justify="center"
               v-if="oper != constat.VIEW">
          <a-form-model-item>
            <a-space>
              <!-- <a-button @click="resetForm('ruleForm')">暂存为草稿</a-button> -->
              <!-- <a-button type="primary" @click="submitForm('ruleForm')">保存并送审</a-button> -->
              <a-button type="primary"
                        @click="submitForm('ruleForm')">保存</a-button>
              <a-button>取消</a-button>
            </a-space>
          </a-form-model-item>
        </a-row>
      </a-form-model>
    </div>
    <!-- 树形选择 -->
    <a-modal :visible.sync="regionVisible"
             height="60%"
             title="选择地区单位"
             @cancel="close"
             @ok="confirm">
      <a-row>
        <manageTree @handleClick="handleClick"></manageTree>
      </a-row>
      <div slot="footer"
           class="dialog-footer">
        <a-button @click="close">关闭</a-button>
        <a-button type="primary"
                  style="margin-left: 10px"
                  @click="confirm">保存</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { checkIdNumberValid } from "@/utils/validate";
import { getLevelListApi } from "@/api/representativeElection/candidateApi.js";
import {
  groupByDepartment,
  findLevelList,
  findAllByOrganizationIdAndTermId,
} from "@/api/organization";
import { getInfo, update, getBaseInfo, insert } from "@/api/dbxx";
import { findLevelByJcDm } from "@/api/organization";
import constat from "@/utils/constat";
import manageTree from "@/views/common/manageTree";
import { Facet } from "@antv/g2plot";
import { instance_3 } from "@/api/axiosRq";
import { dbleaveuploadApi, } from "@/api/representativeElection/candidateApi.js";
export default {
  components: {
    manageTree,
  },
  filters: {},
  data () {
    const checkIdNumberValids = (rule, data, callBack) => {
      if (checkIdNumberValid(data)) {
        callBack();
      } else {
        callBack(new Error("请输入有效身份证件"));
      }
    };
    // 根据证件号校验生日
    var validateDateOfBirth = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请选择出生日期"));
      } else {
        if (this.ruleForm.birthday) {
          let year = this.ruleForm.birthday.slice(6, 10);
          let month = this.ruleForm.birthday.slice(10, 12);
          let day = this.ruleForm.birthday.slice(12, 14);
          // console.log(value);
          // let isYear = value.split("-")[0] == year;
          // let isMonth = value.split("-")[1] == month;
          // let isDay = value.split("-")[2] == day;
          let temp_date = new Date(year, parseFloat(month) - 1, parseFloat(day))
          if (temp_date) {
            callback();
          } else {
            callback(new Error("请选择正确的出生日期"));
          }
        }
      }
    };
    // 根据证件类型校验证件号
    var validateIdNum = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入证件号"));
      } else {
        if (this.ruleForm.zjlxDm === "01") {
          let reg = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
          reg.test(value)
            ? callback()
            : callback(new Error("请输入正确的证件号"));
        }
        if (this.ruleForm.zjlxDm === "02") {
          let reg = /^[\u4E00-\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$/;
          reg.test(value)
            ? callback()
            : callback(new Error("请输入正确的证件号"));
        }
      }
    };
    // 校验手机号
    var validatePhone = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入手机号"));
      } else {
        let reg = /^([1][3,4,5,6,7,8,9])\d{9}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的手机号"));
      }
    };
    return {
      regionVisible: false,
      thNumOptions: [],
      beingData: {},
      dataRef: {},
      activeKey: ["k1", "k2", "k3", "k4", "k5"],
      customStyle: "background:rgba(190,64,61,0.1);",
      active: 0,
      oper: constat.ADD,
      ruleForm: {
        fileList: [],
      },
      fileList: [],
      sex: [],
      nationality: [],
      cardType: [],
      treeData: [],
      politicalAffiliation: [],
      education: [],
      degree: [],
      rules: {
        dbzHm: [{ required: true, message: "请输入代表证号", trigger: "blur" }],
        userName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        xbDm: [{ required: true, message: "请输入性别", trigger: "change" }],
        // validator: validateDateOfBirth
        birthday: [
          { required: true, message: "请输入出生日期", trigger: "change", },
        ],
        mzDm: [{ required: true, message: "请输入民族", trigger: "change" }],
        nativePlace: [
          { required: true, message: "请输入籍贯", trigger: "blur" },
        ],
        zjlxDm: [
          { required: true, message: "请输入证件类型", trigger: "change" },
        ],
        // validator: validateIdNum
        sfzDm: [{ required: true, message: "请输入证件号", trigger: "blur", }],
        zzmmDm: [{ required: true, message: "请输入党派", trigger: "change" }],
        joinTime: [
          { required: true, message: "请输入加入日期", trigger: "change" },
        ],
        workUnit: [
          { required: true, message: "请输入工作单位", trigger: "blur" },
        ],
        joinWorkTime: [
          { required: true, message: "请输入参加工作日期", trigger: "change" },
        ],
        duty: [{ required: true, message: "请输入职位", trigger: "blur" }],
        dutyName: [{ required: true, message: "请输入职称", trigger: "blur" }],
        qrzjyxlDm: [
          {
            required: true,
            message: "请输入全日制教育学历",
            trigger: "change",
          },
        ],
        qrzjyxwDm: [
          {
            required: true,
            message: "请输入全日制教育学位",
            trigger: "change",
          },
        ],
        fullSchool: [
          { required: true, message: "请输入毕业院校历", trigger: "blur" },
        ],
        fullMajor: [
          { required: true, message: "请输入系及专业", trigger: "blur" },
        ],
        unitAddress: [
          { required: true, message: "请输入单位地址", trigger: "blur" },
        ],
        unitPhone: [
          { required: true, message: "请输入单位电话", trigger: "blur" },
        ],
        unitPostalCode: [
          { required: true, message: "请输入单位邮编", trigger: "blur" },
        ],
        yzbmDm: [
          { required: true, message: "请输入电子邮件", trigger: "blur" },
        ],
        houseAddress: [
          { required: true, message: "请输入家庭地址", trigger: "blur" },
        ],
        housePhone: [
          { required: true, message: "请输入家庭电话", trigger: "blur" },
        ],
        housePostalCode: [
          { required: true, message: "请输入家庭邮编", trigger: "blur" },
        ],
        phone: [{ required: true, trigger: "blur", validator: validatePhone, }],

        // 手机
        // twoPhone: [
        //   {
        //     required: false,
        //     message: "请输入手机",
        //     trigger: "change",
        //     validator: validatePhone,
        //   },
        // ],
        // threePhone: [
        //   {
        //     required: false,
        //     message: "请输入手机",
        //     trigger: "change",
        //     validator: validatePhone,
        //   },
        // ],
        isOverseascn: [
          { required: true, message: "请输入选举单位", trigger: "blur" },
        ],
        resume: [
          { required: true, message: "请输入选举单位", trigger: "blur" },
        ],
      },
      constat,
      jcDm: "",
      sfDm: "",
      orgId: "",
      levelOptions: [],
      periods: [],
    };
  },
  mounted () { },
  created () {
    this.getLevelListFn();
    this.$store.dispatch("navigation/breadcrumb1", "代表信息");
    this.$store.dispatch("navigation/breadcrumb2", "管理代表");
    this.$store.dispatch("navigation/breadcrumb3", "个人信息修改");
    this.oper = this.$route.query.oper;
    if (this.oper == constat.VIEW || this.oper == constat.EDIT) {
      const jcDm = this.$route.query.jcDm;
      const dbId = this.$route.query.dbId;
      const sfDm = this.$route.query.sfDm;
      this.jcDm = jcDm;
      this.sfDm = sfDm;
      this.getInfo(jcDm, dbId, sfDm);
      this.findLevelByJcDm();
    } else if (this.oper == constat.ADD) {
      // this.jcDm = this.$route.query.jcDm
      // this.sfDm = this.$route.query.sfDm
      // this.orgId = this.$route.query.orgId
      // this.getBaseInfo()
      // this.findLevelByJcDm()
    }
    this.getBaseInfo()
    this.findLevelByJcDm()
  },
  methods: {
    // 文件上传接口
    // 上传报错401
    uploadFile (options) {
      var wj = new FormData();
      wj.append("file", options.file);
      console.log("file", options.file);
      // instance_3({
      //   method: "post",
      //   url: "/personalDetails/upload",
      //   headers: {
      //     "Content-Type": "multipart/form-data",
      //   },
      //   data: wj,
      // })
      // 运用管理代表的图片上传接口，dbleaveuploadApi（有took）
      dbleaveuploadApi(wj)
        .then((data) => {
          if (data.data.code == "0000") {
            options.onSuccess(data.data, options);
            data.data.data.forEach((item) => {
              this.ruleForm.fileList.push({
                path: item.path,
                fileName: item.fileName,
                type: 0,
                uid: options.file.uid,
              });
            });
          } else {
            options.onError(data.data, options);
          }
          console.log("this.ruleForm.file", this.ruleForm.fileList);
        });
    },
    // 判断和获取文件
    fileBeforeUpload (file) {
      // console.log("1111", file);
      // 判断上传格式*****************
      const isJPG = file.type === "image/jpeg" || "image/png";
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error("上传图片只能是 JPG、JPEG、PNG 格式!");
        reject();
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    //图片上传chage
    handlePicChange ({ fileList }) {
      this.fileList = fileList;
    },
    // 图片预览
    async handlePreview (file) {
      if (!file.url && !file.preview) {
        file.preview = await URL.createObjectURL(file.originFileObj);
      }
      this.dialogImageUrl = file.url || file.preview;
      this.dialogVisible = true;
    },
    // ===========
    confirm () {
      this.dataRef = this.beingData;
      this.orgId = this.dataRef.entryId;
      findLevelList({ meetingCode: this.dataRef.meetingCode }).then(
        (response) => {
          console.log(response, "response");
          if (response.code == "0000") {
            this.thNumOptions = response.data;
            this.jcDm = this.thNumOptions[2].termId;
            this.sfDm = this.thNumOptions[2].regionLevel;
            this.getBaseInfo();
            this.findLevelByJcDm();
            this.regionVisible = false;
          }
        }
      );
    },
    handleRel () {
      this.regionVisible = true;
    },
    close () {
      this.regionVisible = false;
    },
    handleClick (data, e) {
      this.beingData = e.node.dataRef;
    },

    submitForm (formName) {
      console.log(formName, "formName");
      console.log(this.oper);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.oper == "constat.EDIT") {
            this.handleUpdate();
          }
          if (this.oper == "constat.ADD") {
            this.handleAdd();
          }
        } else {
          console.log("错误!!");
          this.$message.error('请输入完整的信息')
          this.$baseMessage("更新成功", "info");
          return false;
        }
      });
    },
    resetForm (formName) {
      // this.$refs[formName].resetFields();
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.ruleForm.sfDm = this.sfDm;
          this.ruleForm.jcDm = this.jcDm;
          this.ruleForm.orgId = this.orgId;
          insert(this.ruleForm).then((response) => {
            if (response.code == "0000") {
              this.$message.success("已存为草稿");
            }
          });
        } else {
          this.$message.error('请输入完整的信息')
          return false;
        }
      });
      // this.ruleForm.sfDm = this.sfDm;
      // this.ruleForm.jcDm = this.jcDm;
      // this.ruleForm.orgId = this.orgId;
      // insert(this.ruleForm).then((response) => {
      //   if (response.code == "0000") {
      //     this.$message.success("请求成功");
      //     // this.$router.go(-1);
      //   } else {
      //     this.$baseMessage("更新成功", "info");
      //   }
      // });
    },
    getInfo (jcDm, dbId, sfDm) {
      getInfo({ jcDm: jcDm, dbId: dbId, sfDm: sfDm, id: dbId }).then(
        (response) => {
          this.ruleForm = response.data.info;
          this.sex = response.data.sex;
          this.nationality = response.data.nationality;
          this.cardType = response.data.cardType;
          this.politicalAffiliation = response.data.politicalAffiliation;
          this.education = response.data.education;
          this.degree = response.data.degree;
        }
      );
    },
    handleUpdate () {
      update(this.ruleForm).then((response) => {
        this.$baseMessage("更新成功", "info");
      });
    },
    handleAdd () {
      // this.$refs["ruleForm"].validate(valid => {
      //   if (valid) {

      //   } else {
      //     this.$message.error("请输入完整信息")
      //   }
      // })
      this.ruleForm.sfDm = this.sfDm;
      this.ruleForm.jcDm = this.jcDm;
      this.ruleForm.orgId = this.orgId;
      insert(this.ruleForm).then((response) => {
        if (response.code == "0000") {
          this.$message.success("请求成功");
          this.$router.go(-1);
        } else {
          this.$baseMessage("更新成功", "info");
        }
      });
    },
    getBaseInfo () {
      getBaseInfo({ jcDm: this.jcDm }).then((response) => {
        this.sex = response.data.sex;
        this.nationality = response.data.nationality;
        this.cardType = response.data.cardType;
        this.politicalAffiliation = response.data.politicalAffiliation;
        this.education = response.data.education;
        this.degree = response.data.degree;
      });
    },
    findLevelByJcDm () {
      findLevelByJcDm({ jcDm: this.jcDm }).then((response) => {
        this.levelOptions = response.data;
      });
    },
    //获取当前届次下拉数据列表
    async getLevelListFn () {
      const res = await getLevelListApi();
      if (res.data.code === "0000") {
        this.periods = res.data.data;
        this.ruleForm.jcDm = this.periods[0].jcDm;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.represen-box {
  width: 100%;

  .Steps {
    width: 80%;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .represen {
    padding: 0 60px;
  }

  .userImage {
    height: 200px;
    width: 150px;
  }
}
</style>
<style lang="scss" scoped>
::v-deep .ant-upload-list-picture-card .ant-upload-list-item {
  height: 200px;
  width: 150px;
}

::v-deep .ant-upload.ant-upload-select-picture-card {
  height: 200px;
  width: 150px;
}

.jic {
  position: relative;
}

.jicson {
  color: red;
  // font-size: 2px;
  @include add-size($font_size_16);
  position: absolute;
  top: -9px;
  left: -51px;
  z-index: 999;
}
</style>
