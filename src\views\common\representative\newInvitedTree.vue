<template>
  <a-modal
    :title="orgTreeDialogTitle"
    :visible.sync="OrgTreeDialogVisible"
    width="50%"
    destroyOnClose
    @cancel="close"
  >
  <a-input-search
                v-model="filterText"
                placeholder="输入关键字进行过滤"
                @search="onChange"
              ></a-input-search>
    <a-tree
      ref="orgTree"
      :replace-fields="replaceFields"
      :tree-data="orgTreeData"
      v-model="checkIdentyData"
      :expanded-keys="expandedKeys"
      :auto-expand-parent="autoExpandParent"
      checkStrictly
      @expand="onExpand"
      @select="onSelect"
    >
      <template slot="title" slot-scope="{ name, orgLevel }">
        <a-icon
          :type="orgLevel == '1' ? 'apartment' : 'file'"
          style="margin-right: 10px;"
        />
        <span
          v-if="searchValue && name.indexOf(searchValue) > -1"
          style="color: #f50;"
          >{{ name }}</span
        >
        <span v-else>{{ name }}</span>
      </template>
    </a-tree>

    <span slot="footer" class="dialog-footer">
      <a-button @click="close">取 消</a-button>
      <a-button style="padding-left: 10px;" type="primary" @click="confirm"
        >确 定</a-button
      >
    </span>
  </a-modal>
</template>

<script>
// import { findDbOU } from "@/api/registrationMage/tableIng.js";
// import { findDbOrgUserTree } from "@/api/dbxx"; //代表展示自定义的树，替换getUserTree
import {
  findDbOrgUserTreeByMainIdentifyInOne,
} from "@/api/levelRole/orgDbTree";
// import {findDbOrgUserTree, findDbOrgUserTreeOrderByXsbh} from "@/api/dbxx"; //代表展示自定义的树，替换getUserTree
export default {
  data() {
    return {
      filterText: "",
      name: "",
      expandedKeys: [],
      searchValue: "",
      allChildData: [],
      selectedKeys: [],
      autoExpandParent: false,
      checked: [],
      checkIdentyData: [],
      orgTreeDialogVisible: false,
      orgTreeData: [],
      OrgTreeDialogVisible: false,
      newOrgTreeDialogVisible: null,
      orgTreeDefaultKey: [],
      replaceFields: {
        title: "name",
        key: "id",
        children: "children",
      },
      isRadio: true,
      relType: {
        type: Number,
        default: 1,
      },
      timer: "",
    };
  },
  props: {
    orgTreeDialogTitle: {
      type: String,
      default: "选择单位",
    },
    jcDm: {
      type: String,
      default: "",
    },
    defaultSelect: {
      type: Array,
      default: [],
    },
  },
  watch: {
    newOrgTreeDialogVisible(newVal) {
      if (newVal) {
        this.initOrgTree();
      } else {
        this.expandedKeys = [];
        this.checked = [];
        this.checkIdentyData = [];
      }
    },
  },
  methods: {
    onSelect(item, data) {
      let ojb = {};
      ojb = data.node._props.dataRef;
      this.checked.push(ojb);
    },
    // 树状搜索
    // onChange(e) {
    //   // 设置防抖
    //   clearTimeout(this.timer);
    //   this.timer = setTimeout(() => {
    //     const value = e.target.value;
    //     this.searchValue = value;
    //     if (value == "") {
    //       this.expandedKeys = [];
    //       this.backupsExpandedKeys = [];
    //       return this.$message.info("请输入关键字");
    //     }
    //     this.expandedKeys = [];
    //     this.backupsExpandedKeys = [];
    //     console.log(value, "value");
    //     const candidateKeysList = this.getkeyList(value, this.orgTreeData, []);
    //     if (candidateKeysList.length == 0) {
    //       // 销毁提示
    //       this.$message.destroy();
    //       return this.$message.info("没有相关数据");
    //     }
    //     this.autoExpandParent = true;
    //     this.expandedKeys = [...this.backupsExpandedKeys];
    //   }, 500);
    // },

    // 树状搜索
    onChange(e) {
      console.log("点击了搜索框");
      this.listLoading = true;
      const value = e;
      this.searchValue = value;
      if (value == "") {
        this.listLoading = false;
        this.$message.info("请输入关键字");
      } else {
        this.searchValue = value;
        this.expandedKeys = [];
        this.backupsExpandedKeys = [];
        const candidateKeysList = this.getkeyList(value, this.orgTreeData, []);
        if (candidateKeysList.length == 0) {
          this.$message.destroy();
          this.listLoading = false;
          this.$message.info("没有相关数据");
          return;
        }
        candidateKeysList.forEach((item) => {
          const key = this.getParentKey(item, this.orgTreeData);
          if (key && !this.backupsExpandedKeys.some((item) => item === key))
            this.backupsExpandedKeys.push(key);
        });
        const { length } = this.backupsExpandedKeys;
        for (let i = 0; i < length; i++) {
          this.getAllParentKey(this.backupsExpandedKeys[i], this.orgTreeData);
        }
        this.expandedKeys = this.backupsExpandedKeys.slice();
        this.listLoading = false;
      }
    },

    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.name.indexOf(value) > -1) {
          keyList.push(node.id);
          this.backupsExpandedKeys.push(node.pid);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey;
      let temp;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          temp = this.getParentKey(key, node.children);
          if (node.children.some((item) => item.id === key)) {
            parentKey = node.id;
          } else if (temp) {
            parentKey = temp;
          }
        }
      }
      return parentKey;
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey;
      if (key) {
        parentKey = this.getParentKey(key, tree);
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey);
          }
          this.getAllParentKey(parentKey, tree);
        }
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    close() {
      this.OrgTreeDialogVisible = false;
      this.newOrgTreeDialogVisible = false;
      this.checked = [];
      this.filterText = "";
    },
    initOrgTree() {
      let param = {
        // jcDm: this.jcDm,
      };
      findDbOrgUserTreeByMainIdentifyInOne(param).then((res) => {
      // findDbOrgUserTreeOrderByXsbh(param).then((res) => {
        this.orgTreeData = res.data;
        this.OrgTreeDialogVisible = true;
        if (this.defaultSelect.length > 0) {
          this.checkIdentyData = this.defaultSelect.map((item) => item.id);
          this.checked = this.defaultSelect;
          this.$forceUpdate();
        }
      });
    },

    // 树状选择
    onCheck(item, data) {
      console.log(data);
      if (data.checked) {
        let value = data.node.dataRef;
        this.checked.push(value);
      } else {
        let orgId = data.node.dataRef.orgId;
        this.checked.forEach((item, index) => {
          if (item.orgId == orgId) {
            this.checked.splice(index, 1);
          }
        });
      }
      if (data.checkedNodesPositions.length == "0") {
        this.checked = [];
      }
    },
    confirm() {
      this.OrgTreeDialogVisible = false;
      this.newOrgTreeDialogVisible = false;
      console.log(this.checked);
      this.$emit("confirm", this.checked);
      this.checkIdentyData = [];
      this.filterText = "";
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-tree {
  max-height: 500px;
  width: 100%;
  overflow-y: auto;
}
</style>
