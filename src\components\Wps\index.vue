<template>
  <div id="wps"></div>
</template>

<script>
export default {
  props: ["wpsUrl", "token", "isRead"],
  data() {
    return {
      simpleMode: false, // 是否开启简易模式
      isCooperUsersAvatarVisible: false, //是否显示协作用户头像
      cooperUsersColor: [
        // {
        //   userId: "xxxxxx", // 用户id
        //   color: "#F65B90", // 用户光标颜色
        // },
      ],
      wpsE: null,
    };
  },
  async mounted() {
    console.log("------1111111进入WPS-----------",this.token);
    console.log("------222222wpsUrl-----------",this.wpsUrl);
    this.openWps(this.wpsUrl, this.token);
  },
  methods: {
    async openWps(url, token) {
      console.log("-----------------",token);
      let _this = this;
      _this.wpsE = await _this.wps.config({
        mode: _this.simpleMode ? "simple" : "normal",
        mount: document.querySelector("#wps"),
        wpsUrl: url,
        // commandBars: [
        // 可以配置多个组件
        // {
        //   cmbId: "HistoryVersion",
        //   attributes: {
        //     visible: false, // 隐藏组件
        //     enable: false, // 禁用组件， 组件显示但不响应点击事件
        //   },
        // },
        // {
        //   cmbId: "HistoryVersionDivider",
        //   attributes: {
        //     visible: false, // 隐藏组件
        //     enable: false, // 禁用组件， 组件显示但不响应点击事件
        //   },
        // },
        // {
        //   cmbId: "HistoryRecord",
        //   attributes: {
        //     visible: false, // 隐藏组件
        //     enable: false, // 禁用组件， 组件显示但不响应点击事件
        //   },
        // },
        // ],
      });
      await _this.wpsE.setToken({ token: token });
      await _this.wpsE.ready();
      const app = _this.wpsE.Application;
      // 将当前文档的编辑状态切换成修订模式
      if (app.ActiveDocument) {
        app.ActiveDocument.TrackRevisions = true;
      }
      // 获取修订对象
      const revisions = await app.ActiveDocument.Revisions;
      if (_this.isRead == 1) {
        // 设置修订框显示/隐藏
        revisions.ShowRevisionsFrame = false;
      } else {
        // 设置修订框显示/隐藏
        revisions.ShowRevisionsFrame = true;
      }

      // 接受对指定文档的所有修订
      // await revisions.AcceptAll();

      // let app = wps.Application;
      // // 定制元素对象【开始 Tab】
      // const controls = await app.CommandBars("StartTab").Controls;
      // // 新增按钮型定制元素
      // const controlButton = await controls.Add(1);
      // controlButton.Caption = "按钮1";

      // // 新增下拉框型定制元素
      // const controlPopup = await controls.Add(1);
      // controlPopup.Caption = "按钮2";
    },
    async track() {
      let _this = this;
      await _this.wpsE.ready();
      const app = _this.wpsE.Application;
      // 将当前文档的编辑状态切换成修订模式
      app.ActiveDocument.TrackRevisions = true;
      return Promise.resolve();
    },
    async example() {
      let _this = this;
      await _this.wpsE.ready();

      const app = _this.wpsE.Application;

      // 获取修订对象
      const revisions = await app.ActiveDocument.Revisions;

      if (_this.isRead == 0) {
        // 获取全文修订内容
        const revisionData = await revisions.Json();
      }
    },
    async acceptAll() {
      let _this = this;
      await _this.wpsE.ready();
      const app = _this.wpsE.Application;
      // 获取修订对象
      const revisions = await app.ActiveDocument.Revisions;
      // 接受对指定文档的所有修订
      await revisions.AcceptAll();
      return revisions.AcceptAll();
    },
    async seven() {
      // let _this = this;
      // console.log(_this.wpsE);
      // await _this.wpsE.ready();
      // await _this.wpsE.on("fileStatus", (data) => {
      //   _this.$emit("status", data.status);
      // });
      // return _this.wpsE.Application.ActiveDocument.Save();
      await this.wpsE.ready();
      await this.wpsE.on("fileStatus", (data) => {
        this.$emit("status", data.status);
      });
      return this.wpsE.Application.ActiveDocument.Save();
      // if (this.wpsE?.Application?.ActiveDocument?.Save) {
      //   await this.wpsE.ready();
      //   await this.wpsE.on("fileStatus", (data) => {
      //     this.$emit("status", data.status);
      //   });
      //   return this.wpsE.Application.ActiveDocument.Save();
      // } else {
      //   await this.openWps(this.wpsUrl, this.token);
      //   await this.wpsE.on("fileStatus", (data) => {
      //     this.$emit("status", data.status);
      //   });
      //   return this.wpsE.Application.ActiveDocument.Save();
      // }
    },
  },
};
</script>

<style>
#wps {
  width: 100%;
  height: 100%;
}
</style>
