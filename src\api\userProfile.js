import request from "@/utils/requestTemp";
export function getPage(params, data) {
  return request({
    url: "/userProfile/getPage",
    method: "post",
    params: params,
    data: data,
  });
}

export function getCurrentUserProfile(params) {
  return request({
    url: "/userProfile/getCurrentUserProfile",
    method: "get",
    params: params,
  });
}

export function getUserProfileDetails(params) {
  return request({
    url: "/userProfile/getUserProfileDetails",
    method: "get",
    params: params,
  });
}

export function getTimelineData(params) {
  return request({
    url: "/userProfile/getTimelineData",
    method: "get",
    params: params,
  });
}
