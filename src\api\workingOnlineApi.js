import { instance_yajy } from "@/api/axiosRq";
// 提交公开建议 列表
export function openFindPage(form) {
  return instance_yajy({
    url: `/api/v1/website/record/openFindPage`,
    method: "post",
    data: form,
  });
}

// 待审核发布 列表
export function dshFindPage(form) {
  return instance_yajy({
    url: `/api/v1/website/record/dshFindPage`,
    method: "post",
    data: form,
  });
}
// 已审核发布 列表
export function listPass(form) {
  return instance_yajy({
    url: `/api/v1/website/record/listPass`,
    method: "post",
    data: form,
  });
}
// 审核不发布 列表
export function listNoPass(form) {
  return instance_yajy({
    url: `/api/v1/website/record/listNoPass`,
    method: "post",
    data: form,
  });
}
// 手工添加 列表
export function websiteFindPage(form) {
  return instance_yajy({
    url: `/api/v1/website/record/findPage`,
    method: "post",
    data: form,
  });
}
// 异动列表 列表
export function findChangeList(form) {
  return instance_yajy({
    url: `/api/v1/website/record/findChangeList`,
    method: "post",
    data: form,
  });
}
// 历史记录 列表
export function goHistoryList(form) {
  return instance_yajy({
    url: `/api/v1/website/record/goHistoryList`,
    method: "post",
    params: form,
  });
}
// 历史记录 列表
export function goHistoryDetail(form) {
  return instance_yajy({
    url: `/api/v1/website/record/goHistoryDetail`,
    method: "post",
    params: form,
  });
}
// 选联工委审核发布
export function updateExamineXlgw(form) {
  return instance_yajy({
    url: `/api/v1/website/record/updateExamineXlgw`,
    method: "post",
    params: form,
  });
}
// 代表工委给予建议
export function updateXlwSugg(form) {
  return instance_yajy({
    url: `/api/v1/website/record/updateXlwSugg`,
    method: "post",
    params: form,
  });
}
// 宣传处给予建议
export function updateExamineXcc(form) {
  return instance_yajy({
    url: `/api/v1/website/record/updateExamineXcc`,
    method: "post",
    params: form,
  });
}
// 研究室审核发布
export function updateExamineYjs(form) {
  return instance_yajy({
    url: `/api/v1/website/record/updateExamineYjs`,
    method: "post",
    params: form,
  });
}
// 网站公开建议管理 列表
export function onlineDisclosureList(form) {
  return instance_yajy({
    url: `/api/v1/website/record/onlineDisclosureList`,
    method: "post",
    data: form,
  });
}
// 网站公开建议管理 重新生成
export function regenerate(form) {
  return instance_yajy({
    url: `/api/v1/website/record/regenerate`,
    method: "post",
    params: form,
  });
}
// 网站公开建议管理 重新生成 获取状态
export function getLockStatus() {
  return instance_yajy({
    url: `/api/v1/website/record/getLockStatus`,
    method: "post",
  });
}
// 网站公开建议管理 立刻发布
export function runIm() {
  return instance_yajy({
    url: `/api/v1/website/record/runIm`,
    method: "post",
  });
}
// 网站公开建议管理 撤回
export function updateDisplay(form) {
  return instance_yajy({
    url: `/api/v1/website/record/updateDisplay`,
    method: "post",
    params: form,
  });
}
// 网站公开建议管理 手动添加
export function websiteSave(form) {
  return instance_yajy({
    url: `/api/v1/website/record/save`,
    method: "post",
    data: form,
  });
}
// 编辑获取数据 手动添加
export function goSaveRecord(form) {
  return instance_yajy({
    url: `/api/v1/website/record/goSaveRecord`,
    method: "post",
    params: form,
  });
}

//公开审定修改状态
export function publiclyReviewed(form) {
  return instance_yajy({
    url: `/api/v1/website/record/publiclyReviewed`,
    method: "post",
    params: form,
  });
}

//确认是否在网站公开修改状态
export function sureWebPublic(form) {
  return instance_yajy({
    url: `/api/v1/website/record/sureWebPublic`,
    method: "post",
    params: form,
  });
}

//同步常委会领导审定状态
export function syncOpinions(form) {
  return instance_yajy({
    url: `/api/v1/website/record/syncOpinions`,
    method: "post",
    params: form,
  });
}
