<template>
  <div class="table-container">
    <!-- <a-row style="margin-left:1%">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18 }">
        <a-col span="6">
          <a-form-model-item label="届次"
                             style="width:100%;">
            <a-select v-model="queryForm.jcDm"
                      allow-clear
                      style="width:100%;"
                      placeholder="请选择届次">
              <a-select-option v-for="item in periods"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="表单状态"
                             style="width:100%;">
            <a-select v-model="queryForm.dqztDm"
                      placeholder="请选择表单状态"
                      allow-clear
                      style="width:100%;">
              <a-select-option v-for="item in states"
                               :key="item.dqztDm"
                               :value="item.dqztDm">{{ item.state }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="姓名"
                             style="width:100%;">
            <a-input v-model="queryForm.userName"
                     autocomplete="off"
                     allow-clear
                     placeholder="请输入姓名"
                     v-on:keyup.enter="sousuo"></a-input>
          </a-form-model-item>
        </a-col>
      </a-form-model>
      <a-col :span="6">
        <span style="float:right">
          <a-button type="primary"
                    @click="sousuo">搜索</a-button>
          <a-button style="margin-left: 12px;"
                    @click="reset"
                    class="pinkBoutton">重置</a-button>

        </span>
      </a-col>
    </a-row> -->

    <SearchForm @onReset="reset" @onSearch="sousuo" :noMore="true">
      <template v-slot:topSearch>
        <SingleSelect :title="'届次'" :selectList="periods"  :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm" />
        <SingleSelect :title="'表单状态'" :selectList="states" :showName="'state'" :showValue="'dqztDm'" :value.sync="queryForm.dqztDm" />
        <SingleSearch @onEnter="sousuo" :title="'姓名'" :value.sync="queryForm.userName" />
      </template>
    </SearchForm>

    <a-row style="margin: 10px 0px 10px 8px;">
      <a-col :span="6">
        <a-button type="primary"
                  style="margin-left: 12px;"
                  @click="openAssignmentDelegationAdd">分配</a-button>
        <a-button type="primary"
                  style="margin-left: 12px;"
                  :loading="downloadLoading"
                  @click="download">导出</a-button>
      </a-col>
    </a-row>
    <a-row>
      <standard-table :columns="columns"
                      rowKey="ID"
                      :loading="TBloading"
                      :dataSource="dataSource"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"
                      @change="onChange"
                      @selectedRowChange="onSelectChange">
        <div class="operationStyle"
             slot="operation"
             slot-scope="{text, record}">
          <span @click="chakan(record)">查看</span>
          <span @click="xiugai(record)"
                v-if="record.STATE != '已终审'">编辑</span>
          <a-dropdown v-if="record.STATE != '已终审'">
            <a class="dropdown-link"
               @click="e => e.preventDefault()">
              更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item v-if="record.STATE != '已终审'">
                <a @click="shenhe(record)">审核</a>
              </a-menu-item>
              <a-menu-item @click="shanchu(record)"
                           v-if="(record.STATE == '已终审') ? false : true">
                删除</a-menu-item>
              <!--  v-show="record.TYPE_ID != '8'"
                            v-if="record.TYPE_ID != '3'"-->
            </a-menu>
          </a-dropdown>
        </div>
      </standard-table>
    </a-row>
    <assignmentDelegationAdd ref="assignmentDelegationAdd"></assignmentDelegationAdd>
    <!--  -->
    <assignmentDelegationsubmit ref="assignmentDelegationsubmit"
                                :neWid="neWid"
                                @handleClearId="ifhandleClearsb">
    </assignmentDelegationsubmit>
    <assignmentDelegationmodify ref="assignmentDelegationmodify"
                                :neWid="neWid"
                                @handleClearId="ifhandleClearsb">
    </assignmentDelegationmodify>
  </div>
</template>
<script>
import assignmentDelegationsubmit from "@/views/ballot/assignmentDelegationsubmit"
import assignmentDelegationmodify from "@/views/ballot/assignmentDelegationmodify"
import assignmentDelegationAdd from "./assignmentDelegationAdd.vue";
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
import {
  getLevelListApi,
  gebdQueryApi,
  getFormStateListApi,
  bdcxQueryApi,
} from "@/api/representativeElection/candidateApi.js";
import { getfpdbtsdeleteApi } from "@/api/representativeElection/candidateApi.js";
import { instance_1 } from "@/api/axiosRq";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
export default {
  components: { assignmentDelegationAdd, StandardTable, assignmentDelegationsubmit, assignmentDelegationmodify, SingleSelect,
    SearchForm,
    SingleSearch, },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,

      downloadLoading: false,
      neWid: '',
      periods: [
        // 议案届别
        { id: 16, name: "十五届六次" },
        { id: 15, name: "十五届一次" },
        { id: 1, name: "十五届二次" },
        { id: 2, name: "十五届三次" },
        { id: 3, name: "十五届四次" },
        { id: 4, name: "十五届五次" },
      ],
      queryForm: {
        jcDm: "3",
        dqztDm: undefined,
        userName: "",
        sort: 'createTime',
        order: 'descend',
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        {
          title: "届次",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "LEVEL_NAME",
        },
        {
          title: "表单状态",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "STATE",
          sorter: {
            compare: (a, b) => a.STATE - b.STATE,
            multiple: 1,
          },
        },
        {
          title: "名单",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "XM",
        },
        {
          title: "录入人",
          align: "center",
          width: 80,
          ellipsis: true,
          dataIndex: "CREATOR",
        },
        {
          title: "录入日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "CREATE_TIME",
        },
        {
          fixed: 'right',
          title: "操作",
          width: 250,
          align: "center",
          scopedSlots: { customRender: 'operation' }
        },
      ],
      dataSource: [],
      selectedRows: [],
      ids: [],
      states: [],

      leftQueryForm: {
        jcDm: "",
        xm: "",
        dbtDm: "all",
        pageNum: 1,
        pageSize: 10,
      },
      rightQueryForm: {
        jcDm: "",
        xm2: "",
        dbtDm: "",
        fpdbtId: "",
        pageNum: 1,
        pageSize: 10,
      },
      tableKey: [],
      tableData: [],
    };
  },
  created () {
    this.getDeputyListData();
    this.getFormStateListFn();
    this.fetchData();
  },
  methods: {
    // 删除
    shanchu (record) {
      var that = this
      //分配代表团
      let id = record.ID
      this.$confirm({
        title: '警告',
        content: '确定要删除吗？',
        onOk () {
          getfpdbtsdeleteApi(id).then(res => {
            if (res.data.code == '0000') {
              that.fetchData()
              that.$message.success(res.data.msg)
            } else {
              that.$message.error(res.data.msg)
            }
          })
        },
        cancelText: '取消',
      });

    },
    //审核
    shenhe (record) {
      //分配代表团
      this.neWid = record.ID
      this.$refs.assignmentDelegationsubmit.visible = true;
      this.$refs.assignmentDelegationsubmit.Istitle = "分配代表团";
    },
    //修改
    xiugai (record) {
      //分配代表团
      this.neWid = record.ID
      this.$refs.assignmentDelegationmodify.jointTableVisible = true;
      // this.$refs.assignmentDelegationmodify.jointTableVisible = "分配代表团";
    },
    //查看
    chakan (record) {
      //分配代表团
      console.log(record);
      this.neWid = record.ID
      this.$refs.assignmentDelegationsubmit.visible = true;
      this.$refs.assignmentDelegationsubmit.Istitle = "分配代表团查看";
    },
    // 导出
    download () {
      this.downloadLoading = true;
      let ids = [];
      ids = this.tableData.map((i) => i.ID)
      instance_1({
        url: "/fpdbts/export",
        method: "post",
        responseType: "blob",
        params: this.queryForm,
        data: [...ids]
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `分配代表团.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000)
      })
    },
    //筛选
    onChange (pagination, filters, sorter, extra) {
      console.log(sorter.order, 'sorter');
      if (sorter.order != undefined) {
        this.queryForm.sort = 'state'
        this.queryForm.order = sorter.order
      } else {
        this.queryForm.sort = 'createTime'
        this.queryForm.order = 'descend'
      }
      let params = {
        jcDm: this.queryForm.jcDm,
        dqztDm: this.queryForm.dqztDm,
        userName: this.queryForm.userName,
        order: this.queryForm.order,
        sort: this.queryForm.sort,
        pageNum: this.queryForm.pageNum,
        pageSize: this.queryForm.pageSize
      }
      bdcxQueryApi(params).then((res) => {
        console.log(res);
        this.dataSource = res.data.rows;
        this.pagination.total = res.data.total;
      });
    },
    ifhandleClearsb () {
      this.neWid = ''
      this.sousuo()
    },
    //搜索
    sousuo () {
      this.queryForm.pageNum = 1
      this.pagination.current = 1
      this.fetchData();
    },
    //重置
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        jcDm: "",
        dbxm: "",
        dqztDm: undefined,
      };
      this.fetchData();
    },
    // 打开
    openAssignmentDelegationAdd () {
      this.$refs.assignmentDelegationAdd.jointTableVisible = true;
    },
    // 获取数据
    getDeputyListData () {
      //获取届次下拉框
      getLevelListApi().then((res) => {
        if (res.data.code === "0000") {
          this.periods = res.data.data;
          this.queryForm.jcDm = this.periods[0].jcDm; //改过
          this.fetchData();
        }
      });
    },
    // 获取数据 
    fetchData () {
      this.TBloading = true;
      let params = {
        jcDm: this.queryForm.jcDm,
        sort: this.queryForm.sort,
        order: this.queryForm.order,
        dqztDm: this.queryForm.dqztDm,
        userName: this.queryForm.userName,
        pageNum: this.queryForm.pageNum,
        pageSize: this.queryForm.pageSize
      }
      bdcxQueryApi(params).then((res) => {
        this.dataSource = res.data.rows;
        this.pagination.total = res.data.total;
        this.TBloading = false;
      });
    },
    onSelectChange (key, data) {
      this.tableKey = key
      this.tableData = data
      console.log(this.tableData, 'tableData');
    },
    //获取表单状态下拉数据列表
    async getFormStateListFn () {
      const res = await getFormStateListApi();
      if (res.data.code === "0000") {
        this.states = res.data.data;
      }
    },
  },
};
</script>
<style scoped>
.formBox {
  padding: 20px;
}
</style>
