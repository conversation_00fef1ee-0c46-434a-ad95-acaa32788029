<template>
  <div class="batchCommittee table-container">
    <a-form layout="horizontal">

      <SearchForm @onReset="reset" @onSearch="fetchData" :noMore="true">
        <template v-slot:topSearch>
          <SingleSearch @onEnter="fetchData" :title="'代表姓名'" :value.sync="queryForm.name" />
        </template>
      </SearchForm>

      <a-row style="margin: 5px 0px 10px 8px;">
        <a-col :span="12">
          <span>
            <a-button
              style="margin-left: 12px;"
              type="primary"
              @click="download"
              >下载模板</a-button
            >
          </span>

          <span style="margin-left: 12px;">
            <a-upload
              ref="uploadFile"
              :action="upLoadUrl"
              :show-upload-list="false"
              accept=".xlsx, .xls"
              @change="uploadChange"
            >
              <a-button icon="cloud-upload" type="primary">导入数据</a-button>
              <span style="margin-left: 12px;"
                >(只能导入xlsx,xls文件，且不超过20mb)</span
              >
            </a-upload>
          </span>
        </a-col>
      </a-row>
    </a-form>

    <div class="table">
      <standard-table
        :row-key="
          (record, index) => {
            return index + record.identityCardCode;
          }
        "
        :pagination="pagination"
        :columns="columns"
        :selected-rows.sync="selectedRows"
        :loading="TBloading"
        :data-source="list"
        @tableClick="clickRow"
      >
      </standard-table>
    </div>
  </div>
</template>
<script>
import { myPagination } from "@/mixins/pagination.js";
import {
  batchCommitteeList,
  downloadElx,
  batchDelete,
  batchData,
} from "@/api/area.js";
import constat from "@/utils/constat";
import Vue from "vue";
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
export default {
  mixins: [myPagination],
  components: {
    SearchForm,
    SingleSearch, 
  },
  data() {
    return {
      TBloading: false,
      // fileList1: [],
      upLoadUrl:
      Vue.prototype.GLOBAL.basePath_1 + "/personalDetails/uploadInfoNew",
      selectedRows: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      list: [],
      columns: [
        {
          title: "届次",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "level",
          customRender: (text, record, index) => {
            return record.level.levelName || "/";
          },
        },
        {
          align: "center",
          title: "姓名",
          width: 180,
          ellipsis: true,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          align: "center",
          title: "民族",
          width: 180,
          ellipsis: true,
          dataIndex: "nationName",
        },
        {
          align: "center",
          width: 180,
          title: "出生日期",
          ellipsis: true,
          dataIndex: "birthday",
          customRender: (text, record, index) => {
            if (text) {
              return text.replace("T", " ").split("Z").join("").substr(0, 19);
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          width: 180,
          title: "籍贯",
          ellipsis: true,
          dataIndex: "nativePlace",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          align: "center",
          width: 180,
          title: "党派",
          ellipsis: true,
          dataIndex: "politicsStatusName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          align: "center",
          width: 180,
          title: "全日制学位名称",
          ellipsis: true,
          dataIndex: "fullTimeDegree",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          align: "center",
          width: 180,
          title: "在职教育系及专业",
          ellipsis: true,
          dataIndex: "inServiceMajors",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          align: "center",
          width: 180,
          title: "工作单位",
          ellipsis: true,
          dataIndex: "workUnit",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          align: "center",
          width: 180,
          title: "职位名称",
          ellipsis: true,
          dataIndex: "jobTitle",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          align: "center",
          width: 180,
          title: "职务",
          ellipsis: true,
          dataIndex: "duty",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          align: "center",
          width: 180,
          title: "手机",
          ellipsis: true,
          dataIndex: "phone",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleData(record);
                    },
                  },
                },
                "查看"
              ),

              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "代表新增");
    this.$store.dispatch("navigation/breadcrumb2", "批量导入代表");
    this.fetchData();
  },
  methods: {
    // 点击行
    clickRow(event, record) {
      try {
        let state;
        let tableHeader = event.target.__vue__.column.key;
        tableHeader ? (state = true) : (state = false);
        if (tableHeader) {
          this.handleData(record);
        }
      } catch (error) {}
    },
    //上传文件
    uploadChange(val) {
      if (val.file.status == "done") {
        if (val.file.response.code == "0000") {
          this.$baseMessage("上传完成! " + val.file.response.msg, "success");
          this.fetchData();
        } else {
          let msgData = "";
          val.file.response.data.forEach((item) => {
            msgData = msgData + item + ",";
          });
          this.$baseMessage(msgData, "error");
        }
      }
    },
    // 下载模板
    download() {
      downloadElx(this.queryForm).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `代表信息批量导入模版.xlsx`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    async handleData(record, edit) {
      console.log(record, "recordrecord");
      let id = record.userId;
      let jcDm = record.level.jcDm;
      let res = await batchData({ id, jcDm });
      console.log(res, "batchDatabatchDatabatchData");
      if (res.code == "0000") {
        let { JC_DM, SF_DM, DB_ID } = res.data;
        this.$router.push({
          path: "/ediAdjust/editInfo",
          query: {
            oper: constat.VIEW,
            jcDm: JC_DM,
            dbId: DB_ID,
            sfDm: SF_DM,
          },
        });
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleDelete(row) {
      console.log(row);
      this.$baseConfirm(
        "关联数据也将一并删除，你确定要删除选中项吗",
        null,
        () => {
          batchDelete({ id: row.userId }).then((res) => {
            this.$baseMessage(res.msg, "success");
            this.fetchData();
          });
        }
      );
    },
    reset() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.fetchData();
    },
    async fetchData() {
      this.TBloading = true;
      let res = await batchCommitteeList(this.queryForm);
      if (res.code == 200) {
        this.list = res.rows;
        this.pagination.total = res.total;
      }
      this.TBloading = false;
    },
  },
};
</script>

<style lang="scss">
.batchCommittee {
  padding: 15px !important;
}
</style>
