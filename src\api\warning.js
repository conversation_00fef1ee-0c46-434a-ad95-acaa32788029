import request from "@/utils/request";
import qs from "qs";

export function getPage(data) {
  return request({
    url: "/api/v1/display/warning/queryPage",
    method: "post",
    data,
  });
}

export function getPageBySource(data) {
  return request({
    url: "/api/v1/display/warning/queryPageBySource",
    method: "post",
    data,
  });
}

export function getPageByTarget(data) {
  return request({
    url: "/api/v1/display/warning/queryPageByTarget",
    method: "post",
    data,
  });
}
