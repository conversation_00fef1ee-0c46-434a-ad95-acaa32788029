import { instance_yajy } from "@/api/axiosRq";

// 人员管理模块
// 身份类别页面
export function getIdentityCategory(queryForm) {
  return instance_yajy({
    url: `/api/v1/status/identityType/findPage`,
    method: 'post',
    data: queryForm,
  })
}
// 编辑与新增
export function addEditIdentityCategory(formData) {
  return instance_yajy({
    url: `/api/v1/status/identityType/save`,
    method: "post",
    data: formData,
  });
}

// 删除
export function deleteEditIdentityCategory(id) {
  let formData = new FormData();
  formData.append("id", id);
  return instance_yajy({
    url: `/api/v1/status/identityType/deleteById`,
    method: "post",
    data: formData,
  });
}

// 身份管理页面
export function getIdentityManagementList(queryForm) {
  return instance_yajy({
    url: '/api/v1/status/identity/findPage',
    method: 'post',
    data: queryForm,
  })
}
// 编辑与新增
export function addEditIdentityManagement(formData) {
  return instance_yajy({
    url: `/api/v1/status/identity/save`,
    method: 'post',
    data: formData,
  });
}

// 删除
export function deleteEditIdentityManagement(id) {
  let formData = new FormData();
  formData.append("id", id);
  return instance_yajy({
    url: `/api/v1/status/identity/deleteById`,
    method: 'post',
    data: formData,
  });
}

// 身份分配页面
// 获取所有身份（下拉列表使用）
export function getDistributionIdentityList() {
  return instance_yajy({
    url: '/api/v1/identity/getAllIdentity',
    method: 'post',
  })
}
export function getDistributionIdentityList1() {
  return instance_yajy({
    url: '/api/v1/status/identity/getAllIdentity',
    method: 'post',
  })
}

/**
 * 身份管理界面下拉所有身份类别
 *
 */
export function getAllIdentityTypeList1() {
  return instance_yajy({
    url: '/api/v1/status/identityType/getAllIdentityType',
    method: 'post',
  })
}


export function insertUserIdentityApi(formData) {
  return instance_yajy({
    url: '/api/v1/status/identityAllocation',
    method: 'post',
    data: formData,
  })
}

// 更新人员岗位身份关联数据
export function updateTableIdentityList1(formData) {
  return instance_yajy({
    url: '/api/v1/status/identityAllocation/updateUserIdentity',
    method: 'post',
    data: formData,
  })
}
// 追加身份
export function insertUserIdentityApi1(formData) {
  return instance_yajy({
    url: '/api/v1/status/identityAllocation/insertUserIdentity',
    method: 'post',
    data: formData,
  })
}
