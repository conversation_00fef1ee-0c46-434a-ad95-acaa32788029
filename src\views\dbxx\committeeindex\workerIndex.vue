<template>
  <div>
    <!-- 代表信息-工作人员首页 -->
    <a-row>
      <a-col :md="24" :sm="24">
        <a-card title="">
          <a-row :gutter="[16, 16]" v-for="item in meetingList" :key="item.id">
            <a-col :md="5" v-for="col in item.col" @click="onClick(col.title)" :key="col.id">
              <a-card hoverable class="a-card-flex">
                <img slot="cover" :src="col.url" alt="" style="width:50px" />
                <a-card-meta :title="col.title"></a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script>
export default {
  name: "WorkerIndex",
  components: {},
  data() {
    return {
      meetingImgUrl: require("@/assets/images/indexPage/tongji.png"),
      meetingList: [
        {
          id: 1,
          col: [
            {
              id: 1,
              title: "代表新增",
              color: "#be403d",
              url: require("@/assets/images/indexPage/registrationMage.png"),
            },
            {
              id: 2,
              title: "代表调整",
              color: "#be403d",
              url: require("@/assets/images/indexPage/representativeAdjustment.png"),
            },
            {
              id: 3,
              title: "代表信息修改",
              color: "#be403d",
              url: require("@/assets/images/indexPage/informationChange.png"),
            },
            {
              id: 4,
              title: "当选管理",
              color: "#be403d",
              url: require("@/assets/images/indexPage/beSelected.png"),
            },
            {
              id: 5,
              title: "代表信息查询",
              color: "#be403d",
              url: require("@/assets/images/indexPage/chaxun.png"),
            },
            {
              id: 6,
              title: "统计分析报表",
              color: "#be403d",
              url: require("@/assets/images/indexPage/ranking.png"),
            },
          ],
        },
      ],
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "工作人员首页");
  },
  methods: {
    onClick(title) {
      console.log(title);
      if (title == '代表新增') {
        this.$router.push({
          path: '/addDlegate/addCommittee'
        })
      } else if (title == '代表调整') {
        this.$router.push({
          path: '/adjust/in'
        })
      } else if (title == '代表信息修改') {
        this.$router.push({
          path: '/ediAdjust/adminAdjust'
        })
      } else if (title == '当选管理') {
        this.$router.push({
          path: '/elected/synthesisQuery'
        })
      } else if (title == '代表信息查询') {
        this.$router.push({
          path: '/representDta/infoQuery'
        })
      } else if (title == '统计分析报表') {
        this.$router.push({
          path: '/statementDta/statementReport'
        })
      }
    }
  },
};
</script>
