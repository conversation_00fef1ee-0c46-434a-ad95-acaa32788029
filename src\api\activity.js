import request from "@/utils/requestTemp";

export function getActivityTypeList() {
  return request({
    url: "/activityType/list",
    method: "get",
  });
}

export function getJcList(params) {
    return request({
      url: "/session/list",
      method: "get",
      params
    });
}
export function getActivityNatureList(params) {
    return request({
      url: "/activityNature/list",
      method: "get",
      params
    });
}

export function saveDraftActivity(data) {
  return request({
    url: "/activity/saveDraft",
    method: "post",
    data
  });
}

export function activityList(params, data) {
  return request({
    url: "/activity/list",
    method: "post",
    params,
    data: data
  });
}

export function activityDetails(params) {
  return request({
    url: "/activity/details",
    method: "get",
    params,
  });
}

export function activityUpdateDraft(data) {
  return request({
    url: "/activity/activityUpdateDraft",
    method: "post",
    data,
  });
}

export function submitActivity(data) {
  return request({
    url: "/activity/submit",
    method: "post",
    data
  });
}

export function updateActivity(data) {
  return request({
    url: "/activity/update",
    method: "post",
    data
  });
}

export function activityMsgPreConfirmList(params, data) {
  return request({
    url: "/activityMsgPreConfirm/list",
    method: "post",
    params,
    data
  });
}

export function preSendMsgDetails(params) {
  return request({
    url: "/activityMsgPreConfirm/details",
    method: "get",
    params,
  });
}

export function preSendMsgDetailsView(params) {
  return request({
    url: "/activityMsgPreConfirm/detailsView",
    method: "get",
    params,
  });
}

export function confirmSengMsg(data) {
  return request({
    url: "/activityMsgPreConfirm/confirmSengMsg",
    method: "post",
    data,
  });
}

export function selectSyn(params) {
  return request({
    url: "/activitySyn/selectSyn",
    method: "post",
    params,
  });
}

