<template>
    <div>
      <a-modal
        v-model="visible"
        :title="title"
        :width="width"
        :closable="false"
        @ok="save"
        @onCancel="handleCancel"
      >
        <a-form-model
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
        >
          <a-form-model-item prop="period" :label-col="{ span: 1 }" :style="{ display: 'inline-block'}">
            <span class="h3">第</span>
            <a-input-number v-model="ruleForm.period" :min="1" autocomplete="off" style="margin: 0 5px">
            </a-input-number>
            <span class="h3">期</span>
          </a-form-model-item>
          <a-form-model-item
                    prop="achieveDate" >
                    <h4>履职周报刊登时间</h4>
              <a-date-picker style="width: 200px;"
                             v-model="ruleForm.achieveDate"
                             placeholder="选择日期时间">
              </a-date-picker>
          </a-form-model-item> 
          <a-form-model-item>
            <a-checkbox-group v-model="ruleForm.userIds" style="width: 100%;max-height:300px;overflow-y:scroll;">
              <div v-for="(users, index) in matchUsers"
              :key="index">
                  <a-row>
                    <h4>{{users[0].orgName}}</h4>
                    <a-col :span="4" :key="item.dbId" v-for="(item) in users" style="padding: 10px 0;">
                      <a-checkbox :value="item.dbId" >{{item.userName}}</a-checkbox>
                    </a-col>
                  </a-row>
              </div>
            </a-checkbox-group>
          </a-form-model-item>
        </a-form-model>
        <div style="color: #f5222d;
      padding:10px;
      background: #fff1f0;
      border-color: #ffa39e;">
          文本智能识别是一种强大的技术，但它并不能保证百分百的准确性。由于自然语言的复杂性和多义性，文本智能识别系统可能会出现误解、歧义或错误的情况。在使用该技术时，需要谨慎对待识别结果，并结合人工的干预和校对来确保准确性和可靠性。
        </div>
        <div slot="footer"
           class="dialog-footer">
        <a-button @click="handleCancel">取 消</a-button>
        <a-button type="primary"
                  @click="save">确 定</a-button>
      </div>
      </a-modal>
    </div>
</template>

<script>
  import { insertResumptionWeekUserRecordByWord } from "@/api/electronicArchives";
  export default {
    props : {
      visible: {
        type: Boolean,
        default: false
      },
    },
    data() {
      return {
        title: "导入履职事迹被《人大代表履职周报》刊登次数",
        width: "80%",
        plainOptions: [],
        checkAll: false,
        ruleForm: {
          period: null,
          userIds: [],
          achieveDate: null
        },
        indeterminate: true,
        matchUsers: {},
        rules: {
          period: [
            {
              required: true,
              message: "请选择期数",
              trigger: "blur",
            },
          ],
          achieveDate: [
            {
              required: true,
              message: "请选择日期",
              trigger: "blur",
            },
          ],
        }
      }
    },
    mounted() {

    },
    methods: {
      initData(matchResumptionWeek) {
        this.ruleForm.period = matchResumptionWeek.period
        this.ruleForm.achieveDate = matchResumptionWeek.achieveDate
        this.matchUsers = matchResumptionWeek.matchUserNames
        //默认全选
        let dbIds = []
        for (var key in this.matchUsers) {  
          this.matchUsers[key].forEach((item) => {
            dbIds.push(item.dbId)
          })
        }
        this.ruleForm.userIds = dbIds
      },
      save() {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            insertResumptionWeekUserRecordByWord(this.ruleForm).then(res => {
              if(res.code == '0000') {
                this.handleCancel()
                this.$message.success("修改成功");
              } else {
                this.$message.success("修改失败");
              }
            })
          } else {
            this.$message.error("请完善代表信息资料");
            return false;
          }
        });
      },
      onCheckAllChange() {
        
      },
      handleCancel() {
        this.$emit('cancel')
      }
    }
  }
</script>

<style>
  .h3{
    color: #333; 
    font-size: 20px;
    font-weight: bold; 
    margin-top: 30px;
    margin-bottom: 10px;
  }
</style>