<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <SearchForm :value="queryForm" @onReset="reset" @onSearch="search">
          <template v-slot:topSearch>
            <DhJcCascade v-model="queryForm" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>
          </template>
          <template v-slot:moreSearch>
            <AdminStreetStationCascade v-model="queryForm" allow-clear />

            <FormInput
              v-model="queryForm.userName"
              :label="'代表姓名'"
              allow-clear
              @enter="handleEnter"
            />

            <FormRangePicker
              v-model="queryForm"
              start-prop="startTime"
              end-prop="endTime"
              label="时间范围"
              allow-clear
            />

            <FormInput
              v-model="queryForm.contactName"
              :label="'联系人姓名'"
              allow-clear
              @enter="handleEnter"
            />
          </template>
        </SearchForm>

        <a-row style="margin: 5px 0px 10px 8px">
          <a-col span="12">
            <a-form-model-item>
              <a-button type="primary" @click="onDetail()" v-if="false">新增</a-button>
              <a-button
                type="primary"
                style="margin-left: 8px"
                @click="downloadExcel"
                >下载模版</a-button
              >
              <!-- <a-upload
                name="file"
                accept=".xls"
                :multiple="true"
                :file-list="fileList"
                :before-upload="beforeUpload"
                style="display: inline-block"
                @change="downImport"
              >
                <a-button type="primary" style="margin-left: 8px">
                  导入</a-button
                >
              </a-upload> -->
              <a-button
                type="primary"
                :loading="downloaDataLoading"
                style="margin-left: 8px"
                @click="downloaData"
                >导出</a-button
              >
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="12"
                 style="margin-bottom: 10px">

          </a-col> -->
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <a-table
            ref="table"
            :bordered="false"
            class="directorySet-table"
            size="small"
            :columns="columns"
            :pagination="pagination"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            :data-source="list"
            :row-key="
              (record, index) => {
                return record.id;
              }
            "
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import {
  downloadCommunityActivitiesExcel,
  addCommunityActivitiesExcel,
  ExportExcelValue,
} from "@/api/communityschedule";
import { list } from "@/api/communityscheduleactivity";
import SearchForm from "@/components/SearchForm/index";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import FormSelect from "@/components/FormSelect/index.vue";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormRangePicker from "@/components/FormRangePicker/index.vue";
import { DBLZ_DBJSQ } from '@/utils/enum/levelRoleMainIdentifyEnum'; // 导入配置文件

export default {
  components: {
    FormRangePicker,
    AdminStreetStationCascade,
    FormSelect,
    FormInput,
    DhJcCascade,
    SearchForm,
  },
  filters: {},
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBJSQ,
      TBloading: false,
      downloaDataLoading: false,
      //list-----
      list: [],
      listLoading: false,
      //list-----
      selectRows: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        dhDm: undefined,
        jcDm: undefined,
        dbtId: undefined,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        userName: undefined,
        startTime: "",
        endTime: "",
        contactName: "",
      },
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 60,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "当前状态",
          align: "center",
          width: 80,
          ellipsis: true,
          dataIndex: "currStateName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        // {
        //   align: "center",
        //   title: "活动编号",
        //   width: 180,
        //   dataIndex: "activityNo",
        //   customRender: (text, record, index) => {
        //     if (text) {
        //       return text;
        //     } else {
        //       return "/";
        //     }
        //   },
        // },
        {
          title: "活动时间",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "serverTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "行政区划",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "administrativeAreaName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "乡镇街道",
          align: "center",
          width: 140,
          ellipsis: true,
          dataIndex: "streetTownName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站名称",
          align: "center",
          width: 320,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.liaisonStationName;
          },
        },
        {
          title: "联系人",
          align: "center",
          width: 100,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.contactName || "/";
          },
        },
        {
          title: "联系人电话",
          align: "center",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.contactPhoneNumber || "/";
          },
        },
        {
          align: "center",
          title: "代表",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.inviteRangeDesc || "/";
          },
        },
        {
          align: "center",
          title: "备注",
          width: 160,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.mark || "/";
          },
        },
        {
          align: "center",
          title: "录入日期",
          width: 190,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.createTime || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 150,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.onDetail(record.id, "view");
                    },
                  },
                },
                "查看"
              ),
            ]);
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      fileList: [],
      searchDebounced: _.debounce(this.search, 500),
      isKeepAlive: false,
    };
  },
  created() {
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "进社区活动");
  },
  watch: {
    "queryForm.jcDm": {
      handler: function (val, oldVal) {
        if (val) {
          this.fetchData();
        }
      },
      deep: true,
    },
  },
  activated() {
    if (this.isKeepAlive) {
      this.fetchData();
    } else {
      this.isKeepAlive = true;
    }
  },
  methods: {
    handleEnter() {
      this.searchDebounced();
    },
    // 导出
    downloaData() {
      if (this.selectedRowKeys.length == 0)
        return this.$message.error("请选择数据");
      this.downloaDataLoading = true;
      ExportExcelValue({
        ids: this.selectedRowKeys.map((son) => son).join(),
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区活动数据.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloaDataLoading = false;
        }, 1000);
      });
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    // 下载
    downloadExcel() {
      downloadCommunityActivitiesExcel(this.queryForm).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区活动导入模板.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    // 上传文件限制 直接return
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },

    //导入活动表
    downImport({ file, fileList }) {
      // console.log(file);
      var data = new FormData();
      data.append("file", file);
      addCommunityActivitiesExcel(data).then((res) => {
        console.log(res, "res");
        if (res.data.code == "500") {
          return this.$message.error("上传失败！请选择正确的xls文件！");
        }
        if (res.data.code == "0000") {
          this.$baseMessage("上传成功", "success");
          this.fetchData();
        }
      });
    },
    //列表
    fetchData() {
      this.listLoading = true;
      list(this.queryForm).then((response) => {
        this.list = response.rows;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    handleSizeChange(val) {
      this.queryForm.size = val;
      this.fetchData();
    },
    // 搜索
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    onDetail(id, oper) {
      this.$router.push({
        path: "/community/intoTheCommunityActivityDetail",
        query: {
          id,
          oper,
          all: 1,
        },
      });
    },
    // 重置
    reset(value) {
      this.queryForm = value;

      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
  },
};
</script>
