<template>
  <div class="table-container">
    <!-- <a-row style="margin-left:1%">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18 }">
        <a-col span="5">
          <a-form-model-item label="届次"
                             style="width:100%;">
            <a-select v-model="queryForm.jcDm"
                      allow-clear
                      placeholder="请选择届次"
                      style="width:100%;">
              <a-select-option v-for="item in jcDmList"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="5">
          <a-form-model-item label="补选单位"
                             prop="xzqhDm"
                             style="width:100%;">
            <a-select v-model="queryForm.xzqhDm"
                      allow-clear
                      style="width:100%;"
                      placeholder="请选择补选单位">
              <a-select-option v-for="item in xzqhDmList"
                               :key="item.xzqhDm"
                               :value="item.xzqhDm">{{ item.xzqhmc }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="5">
          <a-form-model-item label="姓名"
                             prop="userName"
                             style="width:100%;">
            <a-input v-model="queryForm.userName"
                     autocomplete="off"
                     allow-clear
                     style="width:100%;"
                     v-on:keyup.enter="search"
                     placeholder="请输入姓名"></a-input>
          </a-form-model-item>
        </a-col>
        <a-col span="5">
          <a-form-model-item label="表单状态"
                             prop="state"
                             style="width:100%;">
            <a-select v-model="queryForm.state"
                      placeholder="请选择表单状态"
                      allow-clear
                      style="width:100%;">
              <a-select-option v-for="item in statesList"
                               :key="item.dqztDm"
                               :value="item.dqztDm">{{ item.state }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="4">
          <span style="float:right">
            <a-button type="primary"
                      @click="search">搜索</a-button>
            <a-button style="margin-left: 12px;"
                      @click="reset"
                      class="pinkBoutton">重置</a-button>

          </span>
        </a-col>
      </a-form-model>
    </a-row> -->

    <SearchForm @onReset="reset" @onSearch="search">
      <template v-slot:topSearch>
        <SingleSelect :title="'届次'" :selectList="jcDmList"  :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm" />
        <SingleSelect :title="'补选单位'" :selectList="xzqhDmList"  :showName="'xzqhmc'" :showValue="'xzqhDm'" :value.sync="queryForm.xzqhDm" />
        <SingleSearch @onEnter="search" :title="'姓名'" :value.sync="queryForm.userName" />
      </template>
      <template v-slot:moreSearch>
        <SingleSelect :title="'表单状态'" :selectList="statesList" :showName="'state'" :showValue="'dqztDm'" :value.sync="queryForm.state" />
      </template>
    </SearchForm>

    <a-row style="margin: 10px 0px 10px 8px;">
      <a-col :span="6">
        <a-button type="primary"
                  style="margin-left: 12px;"
                  @click="register">录入</a-button>
        <a-button type="primary"
                  style="margin-left: 12px;"
                  :loading="downloadLoading"
                  @click="download">导出</a-button>
      </a-col>
    </a-row>
    <a-row>
      <standard-table class="directorySet-table"
                      ref="table"
                      row-key="ID"
                      :columns="columns"
                      rowKey="ID"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"
                      @selectedRowChange="onSelectChange"
                      :row-selection="rowSelection"
                      :data-source="dataSource"
                      :scroll="{ x: 300, y: 0 }"
                      @change="onChange">

        <div class="operationStyle"
             slot="operation"
             slot-scope="{text, record}">
          <span @click="chakan(record)">查看</span>
          <span @click="xiugai(record)"
                v-if="record.STATE != '已终审'">编辑</span>
          <span @click="shenhe(record)"
                v-if="record.STATE != '已终审'">审核</span>
          <!-- <a-dropdown v-if="record.STATE != '已终审'">
            <a class="dropdown-link" @click="e => e.preventDefault()">
              更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item v-if="record.STATE != '已终审'">
                <a @click="shenhe(record)">审核</a>
              </a-menu-item>
            
            </a-menu>
          </a-dropdown>  -->
          <!-- <a-menu-item @click="shanchu(record)" v-if="(record.STATE == '已终审') ? false : true">
                删除</a-menu-item> -->
          <!-- 选举结果没有删除 -->
        </div>
      </standard-table>
    </a-row>
    <additionSignIn ref="additionSignIn"
                    :isRecord="isRecord"
                    @handleClearId="ifhandleClearId"></additionSignIn>
    <additionSignInmodify ref="additionSignInmodify"
                          :isRecord="isRecord"
                          @handleClearId="ifhandleClearId">
    </additionSignInmodify>
  </div>
</template>

<script>
import { getAddeDselecselectApi, getAdministrativeStateListApi, getLevelListApi, getFormStateListApi } from "@/api/representativeElection/candidateApi.js";
import additionSignIn from "@/views/ballot/additionSignIn";
import additionSignInmodify from "@/views/ballot/additionSignInmodify"
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
import { instance_1 } from "@/api/axiosRq";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
export default {
  name: "RepresentativeForm",
  // 候选人申报表
  components: { StandardTable, additionSignIn, additionSignInmodify, SingleSelect,
    SearchForm,
    SingleSearch, },

  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,

      isRecord: {},
      neWid: "",
      //获取届次
      jcDmList: [],
      //表单状态
      statesList: [],
      //补选单位
      xzqhDmList: [],
      //状态
      status: [
        { id: 1, name: "草稿" },
        { id: 2, name: "退回" },
        { id: 3, name: "待初审" },
        { id: 4, name: "待初审" },
        { id: 5, name: "待初审" },
        { id: 6, name: "待终审" },
        { id: 7, name: "已终审" },
        { id: 8, name: "已归档" },
      ],
      //单位
      be_primary: [
        { id: 0, name: "全部" },
        { id: 1, name: "解放军" },
        { id: 2, name: "越秀区" },
        { id: 3, name: "海珠区" },
        { id: 4, name: "荔湾区" },
        { id: 5, name: "天河区" },
        { id: 6, name: "白云区" },
        { id: 7, name: "黄埔区" },
        { id: 8, name: "花都区" },
        { id: 9, name: "番禺区" },
        { id: 10, name: "南沙区" },
        { id: 11, name: "从化区" },
        { id: 12, name: "增城区" },
      ],
      queryForm: {
        jcDm: '3',
        xzqhDm: undefined,
        userName: "",
        state: undefined,
        sort: 'createTime',
        order: 'descend',
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        {
          title: "表单状态",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "STATE",
          sorter: {
            compare: (a, b) => a.STATE - b.STATE,
            multiple: 1,
          },
        },
        {
          title: "姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "USER_NAME",
        },
        {
          title: "性别",
          align: "center",
          width: 90,
          ellipsis: true,
          dataIndex: "SEX",
        },
        {
          title: "补选单位",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "BXDWMC",
        },

        {
          title: "党派",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "POLITICS_STATUS_NAME",
        },
        {
          title: "市会议日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "CITY_TIME",
          customRender: (text, record, index) => {
            return text.slice(0, text.indexOf("T"));
          }
        },
        {
          title: "补选日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "SUPPLEMENT_DATE",
        },
        {
          title: "补选结果",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "RESULTS",
        },
        {
          title: "录入人",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "CREATOR",
        },
        {
          title: "录入日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "CREATE_TIME",
        },
        {
          fixed: 'right',
          title: "操作",
          width: 250,
          align: "center",
          scopedSlots: { customRender: 'operation' }
        },
        // {
        //   fixed: 'right',
        //   title: "操作",
        //   width: 250,
        //   align: "center",
        //   customRender: (text, record, index) => {
        //     let h = this.$createElement;
        //     return h("div", [
        //       h(
        //         "span",
        //         {
        //           attrs: {
        //             type: "text",
        //           },
        //           style: {
        //             display: record.STATE == '已终审' ? 'none' : 'inline',
        //             cursor: "pointer",
        //             marginLeft: "14px",
        //             color: "#1890ff",
        //           },
        //           on: {
        //             click: () => {
        //               this.shenhe(record);
        //             },
        //           },
        //         },
        //         "审核"
        //       ),
        //       h(
        //         "span",
        //         {
        //           attrs: {
        //             type: "text",
        //           },
        //           style: {
        //             display: record.STATE == '已终审' ? 'inline' : 'none',
        //             cursor: "pointer",
        //             marginLeft: "14px",
        //             color: "#1890ff",
        //           },
        //           on: {
        //             click: () => {
        //               this.chakan(record);
        //             },
        //           },
        //         },
        //         "查看"
        //       ),
        //     ]);
        //   },
        // },
      ],
      dataSource: [],
      tableData: [],
      tableKey: [],
      selectedRows: [],
      downloadLoading: false,
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "候选人管理");
    this.$store.dispatch("navigation/breadcrumb2", "补选结果");
    this.fetchData();
    this.getAdministrativeStateList()
    this.getFormStateListFn()
    this.getLevelListFn()
  },
  computed: {
    //复选框
    rowSelection () {
      return {
        onChange: (selectedRowKeys, selectedRows) => {
        },
        getCheckboxProps: record => ({
          props: {
            disabled: record.name === 'Disabled User', //  
            name: record.name,
          },
        }),
      };
    },

  },
  methods: {
    // 删除
    shanchu (record) {
      var that = this
      //候选人分配表
      let id = record.ID
      this.$confirm({
        title: '警告',
        content: '确定要删除吗？',
        onOk () {
          // gethxrfpbsesentdeleteApi(id).then(res => {
          //   if (res.data.code == '0000') {
          //     that.fetchData()
          //     that.$message.success(res.data.msg)
          //   } else {
          //     that.$message.error(res.data.msg)
          //   }
          // })
        },
        cancelText: '取消',
      });
    },
    //修改
    xiugai (record) {
      //补选结果
      this.isRecord = record
      this.$refs.additionSignInmodify.visible = true;
    },

    //审核
    shenhe (record) {
      this.$refs.additionSignIn.Istitle = "补选结果审核";
      this.isRecord = record
      this.neWid = record.ID
      this.$refs.additionSignIn.visible = true;
    },
    //查看
    chakan (record) {
      this.$refs.additionSignIn.Istitle = "补选结果详情";
      this.isRecord = record
      this.neWid = record.ID
      this.$refs.additionSignIn.visible = true;
    },
    //筛选
    onChange (pagination, filters, sorter, extra) {
      if (sorter.order != undefined) {
        this.queryForm.sort = 'state'
        this.queryForm.order = sorter.order
      } else {
        this.queryForm.sort = 'createTime'
        this.queryForm.order = 'descend'
      }
      getAddeDselecselectApi(this.queryForm).then(res => {
        this.dataSource = res.data.rows
        this.pagination.total = res.data.total;
      })
    },
    // 导出
    download () {
      this.downloadLoading = true;
      let ids = [];
      ids = this.tableData.map((i) => i.ID)
      instance_1({
        url: "/bxjg/export",
        method: "post",
        responseType: "blob",
        params: this.queryForm,
        data: [...ids]
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `录入补选结果.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000)
      })
    },
    onSelectChange (key, data) {
      this.tableKey = key
      this.tableData = data
      console.log(this.tableData, 'tableData');
    },
    ifhandleClearId () {
      this.isRecord = {};
      this.fetchData();
    },

    //录入
    register () {
      this.$refs.additionSignIn.visible = true;
    },
    //重置
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        jcDm: "",
        userName: "",
        state: undefined,
        xzqhDm: undefined,
      };
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData()
    },
    //搜索
    search () {
      this.queryForm.pageNum = 1
      this.pagination.current = 1
      this.fetchData()
    },
    // 获取数据
    fetchData () {
      this.TBloading = true;
      getAddeDselecselectApi(this.queryForm).then(res => {
        this.dataSource = res.data.rows
        this.pagination.total = res.data.total;
        this.TBloading = false;
      })
    },
    //补选单位
    async getAdministrativeStateList () {
      let res = await getAdministrativeStateListApi()
      if (res.data.code == '0000') {
        this.xzqhDmList = res.data.data
      }
    },
    //获取当前届次下拉数据列表
    async getLevelListFn () {
      const res = await getLevelListApi()
      if (res.data.code === "0000") {
        this.jcDmList = res.data.data
        // console.log(this.periods[0].jcDm, '00');
        this.queryForm.jcDm = this.jcDmList[0].jcDm //改过
      }
    },
    //获取表单状态下拉数据列表
    async getFormStateListFn () {
      const res = await getFormStateListApi()
      if (res.data.code === "0000") {
        this.statesList = res.data.data
      }
    },
  },

};
</script>

<style>
</style>