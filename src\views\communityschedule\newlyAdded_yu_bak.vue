<template>
  <!-- 社情民意的详情、修改、添加 -->
  <div class="table-container">
    <a-row class="formBox">
      <a-form-model ref="addForm"
                    :model="addForm"
                    :rules="rules"
                    :label-col="{ span: 10 }"
                    :wrapper-col="{ span: 14 }">
        <a-row>
          <a-col class="tit">
            <!-- style="margin-bottom: 15px; color: #000; font-size: 16px" -->
            [人大代表联系群众登记表]
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="代表姓名">
              <div class="searchStyle">
                <a-input disabled
                         style="width: 300px"
                         v-model="dbName_list"
                         enter-button>
                </a-input>
                <a-button type="primary"
                          icon="search"
                          @click="opendbInformation">
                </a-button>
              </div>
            </a-form-model-item>
          </a-col>
          <!-- <a-col span="6">
            <a-form-model-item label="代表姓名" prop="committeeName">
              <a-input
              :disabled="disabled"
                v-model="addForm.committeeName"
                autocomplete="off"
                allow-clear
              ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="联系电话" prop="committeeMobile">
              <a-input
               :disabled="disabled"
                v-model="addForm.committeeMobile"
                autocomplete="off"
                allow-clear
              ></a-input>
            </a-form-model-item>
          </a-col> -->
          <a-col span="6">
            <a-form-model-item label="记录人姓名"
                               prop="recorder">
              <a-input :disabled="disabled"
                       v-model="addForm.recorder"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="记录人联系电话"
                               prop="recorderMobile">
              <a-input :disabled="disabled"
                       v-model="addForm.recorderMobile"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col span="6">
            <a-form-model-item label="所联系的群众姓名"
                               prop="voterName">
              <a-input :disabled="disabled"
                       v-model="addForm.voterName"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="所联系的群众联系电话"
                               prop="voterMobile">
              <a-input :disabled="disabled"
                       v-model="addForm.voterMobile"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="身份证号码"
                               prop="voterCredentialNo">
              <a-input :disabled="disabled"
                       v-model="addForm.voterCredentialNo"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="住址"
                               prop="voterAddress">
              <a-input :disabled="disabled"
                       v-model="addForm.voterAddress"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col span="6">
            <a-form-model-item label="建议交办的办理部门"
                               prop="adviceDept">
              <a-input :disabled="disabled"
                       v-model="addForm.adviceDept"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="代表意见"
                               prop="committeeOpinionDate">
              <a-date-picker allow-clear
                             :disabled="disabled"
                             v-model="addForm.committeeOpinionDate"
                             value-format="YYYY-MM-DD"
                             placeholder="请选择时间">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px; margin-left: -30px">
            <a-form-model-item prop="committeeOpinion"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea :disabled="disabled"
                          rows="1"
                          allow-clear
                          v-model="addForm.committeeOpinion"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col span="8">
            <a-form-model-item label="镇人大、人大街道工委意见"
                               prop="committeeStreetOpinionDate">
              <a-date-picker allow-clear
                             :disabled="disabled"
                             v-model="addForm.committeeStreetOpinionDate"
                             placeholder="请选择时间"
                             value-format="YYYY-MM-DD">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px; margin-left: -110px">
            <a-form-model-item prop="committeeStreetOpinion"
                               :label-col="{ span: 2 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea :disabled="disabled"
                          rows="1"
                          allow-clear
                          v-model="addForm.committeeStreetOpinion"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col span="8">
            <a-form-model-item label="镇党委、街道党工委意见"
                               prop="partyStreetOpinionDate">
              <a-date-picker :disabled="disabled"
                             allow-clear
                             v-model="addForm.partyStreetOpinionDate"
                             placeholder="请选择时间"
                             value-format="YYYY-MM-DD">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px; margin-left: -110px">
            <a-form-model-item prop="partyStreetOpinion"
                               :label-col="{ span: 2 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea :disabled="disabled"
                          rows="1"
                          allow-clear
                          v-model="addForm.partyStreetOpinion"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col class="tit">
            [群众反映问题、意见建议的处理情况]
          </a-col>
        </a-row>
        <a-radio-group v-model="addForm.transType"
                       style="width: 100%">
          <a-row span="24">
            <a-row>
              <a-col :span="6">
                <a-radio :disabled="disabled"
                         value="1">口头解释答复</a-radio>
              </a-col>
              <a-col :span="6">
                <a-radio :disabled="disabled"
                         value="2">转镇、街有关科室处理</a-radio>
              </a-col>
              <a-col :span="6">
                <a-radio :disabled="disabled"
                         value="3">转请区政府有关职能部门处理</a-radio>
              </a-col>
              <a-col :span="6">
                <a-radio :disabled="disabled"
                         value="4">商请区代表提出建议</a-radio>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="6">
                <a-radio :disabled="disabled"
                         value="5">组织代表开展专题视察调研</a-radio>
              </a-col>
              <a-col :span="6">
                <a-radio :disabled="disabled"
                         value="6">商请上级人大代表向相应层级人大反映</a-radio>
              </a-col>
              <a-col :span="6">
                <a-radio :disabled="disabled"
                         value="7">商请上级人大代表提出建议</a-radio>
              </a-col>
              <a-col :span="6">
                <a-radio :disabled="disabled"
                         value="8">其他</a-radio>
              </a-col>
            </a-row>
          </a-row>
        </a-radio-group>

        <a-row>
          <a-col span="6">
            <a-form-model-item label="转交时间"
                               prop="transTime">
              <a-date-picker allow-clear
                             :disabled="disabled"
                             v-model="addForm.transTime"
                             placeholder="请选择反馈时间"
                             value-format="YYYY-MM-DD"></a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="接收单位"
                               prop="receiveDept">
              <a-input :disabled="disabled"
                       v-model="addForm.receiveDept"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="交转方式"
                               prop="transWay">
              <a-input :disabled="disabled"
                       v-model="addForm.transWay"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="接收时间"
                               prop="receiveTime">
              <a-date-picker allow-clear
                             :disabled="disabled"
                             v-model="addForm.receiveTime"
                             placeholder="请选择反馈时间"
                             value-format="YYYY-MM-DD"></a-date-picker>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col span="6">
            <a-form-model-item label="接收人员"
                               prop="receiveUserName">
              <a-input :disabled="disabled"
                       v-model="addForm.receiveUserName"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="联系电话"
                               prop="receiveUserMobile">
              <a-input :disabled="disabled"
                       v-model="addForm.receiveUserMobile"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col span="6">
            <a-form-model-item label="办理期限"
                               prop="dueDate">
              <!-- <a-textarea rows="2" allow-clear v-model="addForm.userName"></a-textarea> -->
              <a-date-picker allow-clear
                             :disabled="disabled"
                             v-model="addForm.dueDate"
                             value-format="YYYY-MM-DD"></a-date-picker>
            </a-form-model-item>
          </a-col>

          <a-col span="6">
            <a-form-model-item label="跟踪督办情况"
                               prop="situation">
              <a-textarea rows="2"
                          :disabled="disabled"
                          allow-clear
                          v-model="addForm.situation"></a-textarea>
            </a-form-model-item>
          </a-col>

          <a-col span="6">
            <a-form-model-item label="办理详情"
                               prop="handlingContent"
                               :wrapper-col="{ span: 14 }">
              <a-textarea rows="2"
                          allow-clear
                          :disabled="disabled"
                          v-model="addForm.handlingContent"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col span="5">
            <a-form-model-item label="答复情况"
                               prop="replyStreetPartyDate">
              <a-date-picker allow-clear
                             :disabled="disabled"
                             v-model="addForm.replyStreetPartyDate"
                             placeholder="请选择答复时间"
                             value-format="YYYY-MM-DD">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px">
            <a-form-model-item prop="replyStreetParty"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea rows="1"
                          allow-clear
                          :disabled="disabled"
                          v-model="addForm.replyStreetParty"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col :span="5">
            <p style="line-height: 40px">
              已将该事项办理情况答复镇人大、人大街道工委。
            </p>
          </a-col>
        </a-row>
        <a-row>
          <a-col span="5">
            <a-form-model-item label="反馈情况"
                               prop="replyPeopleDate">
              <!-- :disabled="disabled" :disabledDate="disabledDate" -->
              <a-date-picker allow-clear
                             :disabled="disabled"
                             v-model="addForm.replyPeopleDate"
                             placeholder="请选择反馈时间"
                             value-format="YYYY-MM-DD">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px">
            <a-form-model-item prop="replyPeople"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea rows="1"
                          allow-clear
                          :disabled="disabled"
                          v-model="addForm.replyPeople"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col span="5">
            <p style="line-height: 40px">已将该事项办理情况反馈代表和群众。</p>
          </a-col>
        </a-row>
        <a-row>
          <a-col class="tit">
            [代表评价]:
          </a-col>
        </a-row>
        <a-row>
          <a-col span="5">
            <a-form-model-item label="对办理结果">
              <a-select v-model="addForm.handlingInfo.committeeGradeForResult"
                        allow-clear
                        style="width: 175px"
                        :disabled="disabled">
                <a-select-option v-for="item in bljgclList"
                                 :key="item.id"
                                 :value="item.id">{{ item.bljgname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px">
            <a-form-model-item prop="yy"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea rows="1"
                          :disabled="disabled"
                          allow-clear
                          v-model="addForm.handlingInfo.committeeCommentForResult"></a-textarea>
            </a-form-model-item>
          </a-col>

          <a-col span="5">
            <a-form-model-item label="对办理部门">
              <!-- handlingInfo.committeeGradeForDept -->
              <a-select v-model="addForm.handlingInfo.committeeGradeForDept"
                        allow-clear
                        style="width: 175px"
                        :disabled="disabled">
                <a-select-option v-for="item in bljgclList"
                                 :key="item.id"
                                 :value="item.id">{{ item.bljgname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px">
            <a-form-model-item prop="yy"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea rows="1"
                          allow-clear
                          :disabled="disabled"
                          v-model="addForm.handlingInfo.committeeCommentForDept"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col class="tit">
            [群众评价]:
          </a-col>
        </a-row>
        <a-row>
          <a-col span="5">
            <a-form-model-item label="对办理结果">
              <a-select v-model="addForm.handlingInfo.peopleGradeForResult"
                        allow-clear
                        :disabled="disabled"
                        style="width: 175px">
                <a-select-option v-for="item in bljgclList"
                                 :key="item.id"
                                 :value="item.id">{{ item.bljgname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px">
            <a-form-model-item prop="yy"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea rows="1"
                          :disabled="disabled"
                          allow-clear
                          v-model="addForm.handlingInfo.peopleCommentForResult"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col span="5">
            <a-form-model-item label="对办理部门">
              <a-select v-model="addForm.handlingInfo.peopleGradeForDept"
                        allow-clear
                        :disabled="disabled"
                        style="width: 175px">
                <a-select-option v-for="item in bljgclList"
                                 :key="item.id"
                                 :value="item.id">{{ item.bljgname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px">
            <a-form-model-item prop="yy"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea rows="1"
                          :disabled="disabled"
                          allow-clear
                          v-model="addForm.handlingInfo.peopleCommentForDept"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
        <!--  -->
        <a-row>
          <a-col span="24">
            <a-form-model-item label="再次办理或解释情况(代表或群众不满意)"
                               prop="secondHandlingContent"
                               :label-col="{ span: 4 }"
                               :wrapper-col="{ span: 16 }">
              <a-textarea rows="2"
                          allow-clear
                          :disabled="disabled"
                          v-model="addForm.secondHandlingContent"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
        <!-- 再次 -->
        <a-row>
          <a-col class="tit">
            [代表评价]:
          </a-col>
        </a-row>
        <a-row>
          <a-col span="5">
            <a-form-model-item label="对办理结果">
              <a-select v-model="addForm.secondHandlingInfo.committeeGradeForResult"
                        allow-clear
                        :disabled="disabled"
                        style="width: 175px">
                <a-select-option v-for="item in bljgclList"
                                 :key="item.id"
                                 :value="item.id">{{ item.bljgname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px">
            <a-form-model-item prop="yy"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea rows="1"
                          allow-clear
                          :disabled="disabled"
                          v-model="addForm.secondHandlingInfo.committeeCommentForResult"></a-textarea>
            </a-form-model-item>
          </a-col>

          <a-col span="5">
            <a-form-model-item label="对办理部门">
              <!-- secondHandlingInfo.committeeGradeForDept -->
              <a-select v-model="addForm.secondHandlingInfo.committeeGradeForDept"
                        allow-clear
                        :disabled="disabled"
                        style="width: 175px">
                <a-select-option v-for="item in bljgclList"
                                 :key="item.id"
                                 :value="item.id">{{ item.bljgname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px">
            <a-form-model-item prop="yy"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea rows="1"
                          allow-clear
                          :disabled="disabled"
                          v-model="addForm.secondHandlingInfo.committeeCommentForDept"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col class="tit">
            [群众评价]:
          </a-col>
        </a-row>
        <a-row>
          <a-col span="5">
            <a-form-model-item label="对办理结果">
              <a-select :disabled="disabled"
                        v-model="addForm.secondHandlingInfo.peopleGradeForResult"
                        allow-clear
                        style="width: 175px">
                <a-select-option v-for="item in bljgclList"
                                 :key="item.id"
                                 :value="item.id">{{ item.bljgname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px">
            <a-form-model-item prop="yy"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea rows="1"
                          :disabled="disabled"
                          allow-clear
                          v-model="addForm.secondHandlingInfo.peopleCommentForResult"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col span="5">
            <a-form-model-item label="对办理部门">
              <a-select v-model="addForm.secondHandlingInfo.peopleGradeForDept"
                        allow-clear
                        :disabled="disabled"
                        style="width: 175px">
                <a-select-option v-for="item in bljgclList"
                                 :key="item.id"
                                 :value="item.id">{{ item.bljgname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="5"
                 style="margin-top: 5px">
            <a-form-model-item prop="yy"
                               :label-col="{ span: 1 }"
                               :wrapper-col="{ span: 24 }">
              <a-textarea rows="1"
                          allow-clear
                          :disabled="disabled"
                          v-model="addForm.secondHandlingInfo.peopleCommentForDept"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
        <!-- 

        <a-row>
          <a-col span="5">
            <a-form-model-item label="代表再次评价">
              <a-select v-model="addForm.jcDm" allow-clear style="width: 175px">
                <a-select-option
                  v-for="item in bljgclList"
                  :key="item.id"
                  :value="item.id"
                  >{{ item.bljgname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="5" style="margin-top: 5px">
            <a-form-model-item
              prop="yy"
              :label-col="{ span: 1 }"
              :wrapper-col="{ span: 24 }"
            >
              <a-textarea
                rows="1"
                allow-clear
                v-model="addForm.userName"
              ></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col span="5">
            <a-form-model-item label="群众再次评价">
              <a-select v-model="addForm.jcDm" allow-clear style="width: 175px">
                <a-select-option
                  v-for="item in bljgclList"
                  :key="item.id"
                  :value="item.id"
                  >{{ item.bljgname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="5" style="margin-top: 5px">
            <a-form-model-item
              prop="yy"
              :label-col="{ span: 1 }"
              :wrapper-col="{ span: 24 }"
            >
              <a-textarea
                rows="1"
                allow-clear
                v-model="addForm.userName"
              ></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row> -->
        <a-row>
          <a-col span="5">
            <a-form-model-item label="办结情况">
              <a-select v-model="addForm.isCompleted"
                        :disabled="disabled"
                        allow-clear
                        style="width: 200px"
                        @change="changerCompletedType(2)">
                <a-select-option v-for="item in bjqkList"
                                 :key="item.id"
                                 :value="item.id">{{ item.bjqkname }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="15"
                 style="margin-top: 5px"
                 v-show="isCompletedType">
            <a-form-model-item label="原因或后续处理计划等"
                               :label-col="{ span: 4 }"
                               :wrapper-col="{ span: 18 }">
              <a-textarea rows="1"
                          allow-clear
                          :disabled="disabled"
                          v-model="addForm.completeContent"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-row>
    <a-row slot="footer"
           style="margin-bottom: 50px">
      <a-col :span="8"
             push="10"
             v-if="FormType == 'add'">
        <a-space>
          <a-button @click="save"
                    type="primary">保存</a-button>
          <a-button @click="quxiao">取消</a-button>
        </a-space>
      </a-col>
      <a-col :span="8"
             push="10"
             v-else>
        <a-space>
          <a-button @click="onUpdate"
                    type="primary"
                    v-show="!disabled">修改</a-button>
          <a-button @click="quxiao">返回</a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-modal @cancel="DialogVisible = false"
             title="选择代表"
             :visible.sync="DialogVisible"
             width="50%">
      <div>
        <a-row>
          <standard-table :columns="columns"
                          :rowKey="
              (record, index) => {
                return record.userId;
              }
            "
                          :loading="TBloading"
                          :dataSource="dataSource"
                          :pagination="pagination"
                          :selectedRows.sync="selectedRows"
                          @selectedRowChange="onSelectChange"></standard-table>
        </a-row>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <a-button type="primary"
                  @click="confirm">确 定</a-button>
        <a-button @click="DialogVisible = false">取 消</a-button>
      </span>
    </a-modal>
  </div>
</template>
<script>
import Vue from "vue";
import moment from "moment";
import { myPagination } from "@/mixins/pagination.js";
import submitForCensorship from "@/views/common/submitForCensorship";
import { getcandidateApplicationtsaveApi } from "@/api/representativeElection/candidateApi.js";
import {
  OpinionControllerSave,
  OpinionControllerUpdata,
  getMembers,
} from "@/api/communityschedule";
import { log } from "@antv/g2plot/lib/utils";
export default {
  components: { submitForCensorship },
  mixins: [myPagination],
  props: {
    neWid: {
      type: String,
    },
  },

  data () {
    // 根据证件号校验生日
    var validateDateOfBirth = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请选择出生日期"));
      } else {
        if (this.addForm.birthday) {
          let year = this.addForm.birthday.slice(6, 10);
          let month = this.addForm.birthday.slice(10, 12);
          let day = this.addForm.birthday.slice(12, 14);
          let temp_date = new Date(
            year,
            parseFloat(month) - 1,
            parseFloat(day)
          );
          if (temp_date) {
            callback();
          } else {
            callback(new Error("请选择正确的出生日期"));
          }
        } else {
          this.$message.error("出生日不能大于当天");
        }
      }
    };

    //校验手机号
    var validatePhone = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入手机号"));
      } else {
        let reg = /^([1][3,4,5,6,7,8,9])\d{9}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的手机号"));
      }
    };
    //校验证件号
    var getCardTypeNumber = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入身份证号码"));
      } else {
        const reg =
          /^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确格式的身份证号码"));
      }
    };
    return {
      TBloading: false,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        {
          title: "代表姓名",
          align: "center",
          ellipsis: true,
          dataIndex: "name",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联系方式",
          align: "center",
          ellipsis: true,
          dataIndex: "mobile",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
      ],

      selectedRows: [],
      dataSource: [],
      dbName_list: "", //代表姓名
      DialogVisible: false,
      FormType: "",
      isCompletedType: false,
      disabled: false,
      state: "",
      checked: true,
      addForm: {
        adviceDept: null,
        committeeOpinion: null,
        committeeOpinionDate: null,
        committeeStreetOpinion: null,
        committeeStreetOpinionDate: null,
        communityScheduleId: null,
        completeContent: null,
        content: null,
        createTime: null,
        creator: null,
        deleteTime: null,
        deleter: null,
        dueDate: null,
        handlingContent: null,
        modifier: null,
        partyStreetOpinion: null,
        partyStreetOpinionDate: null,
        receiveDept: null,
        receiveTime: null,
        receiveUserMobile: null,
        receiveUserName: null,
        recordTime: null,
        recorder: null,
        recorderMobile: null,
        replyPeople: null,
        replyPeopleDate: null,
        replyStreetParty: null,
        replyStreetPartyDate: null,
        secondHandlingContent: null,
        isCompleted: null,
        situation: null,
        transTime: null,
        transWay: null,
        updateTime: null,
        voterAddress: null,
        voterAge: null,
        voterCredentialNo: null,
        voterGender: null,
        voterMobile: null,
        voterName: null,
        members: [],
        //
        handlingInfo: {
          committeeCommentForDept: null,
          committeeGradeForDept: null,
          committeeCommentForResult: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        },
        secondHandlingInfo: {
          committeeCommentForDept: null,
          committeeCommentForResult: null,
          committeeGradeForDept: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        },
        transType: null,
      },
      bljgclList: [
        { id: 1, bljgname: "满意" },
        { id: 2, bljgname: "基本满意" },
        { id: 3, bljgname: "不满意(原因)" },
      ],
      bjqkList: [
        { id: 1, bjqkname: "已办结" },
        { id: 0, bjqkname: "未办结" },
      ],
      checkBoxArr: [
        { id: "1", value: "是否拥有外国国籍" },
        { id: "2", value: "是否拥有国(境)外永久居留权" },
        { id: "3", value: "是否拥有国(境)外长期居留许可" },
        { id: "4", value: "是否有直系亲属担任同级人大代表" },
        { id: "5", value: "配偶子女是否移居国（境）外" },
        { id: "6", value: "是否担任无隶属关系行政区域的人大代表/政协委员" },
        { id: "7", value: "是否是香港或澳门市民" },
        { id: "8", value: "是否担任企业高管" },
        {
          id: "9",
          value:
            "是否直接或者间接接受境外机构、组织、个人提供的与选举有关的任何形式的资助",
        },
      ],

      periods: [
        { id: 16, name: "十五届六次" },
        { id: 15, name: "十五届一次" },
        { id: 1, name: "十五届二次" },
        { id: 2, name: "十五届三次" },
        { id: 3, name: "十五届四次" },
        { id: 4, name: "十五届五次" },
      ],
      rules: {
        committeeName: [
          { required: true, message: "请输入代表姓名", trigger: "blur" },
        ],
        recorder: [
          { required: true, message: "请输入记录人姓名", trigger: "blur" },
        ],
        voterName: [
          {
            required: true,
            message: "请输入所联系的群众姓名",
            trigger: "blur",
          },
        ],
        committeeMobile: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],
        recorderMobile: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],
        voterMobile: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],

        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
        voterAddress: [
          { required: true, message: "请填写住址", trigger: "blur" },
        ],

        birthday: [
          { required: true, trigger: "change", validator: validateDateOfBirth },
        ],
        voterCredentialNo: [
          { required: true, trigger: "blur", validator: getCardTypeNumber },
        ],
      },
      disabled: false,
    };
  },

  created () {
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "社情民意列表详情");
    let { data, to_type, communityScheduleId } = this.$route.query;
    this.addForm.communityScheduleId = communityScheduleId;
    this.queryForm.id = communityScheduleId;
    if (to_type) {
      // 修改
      this.disabled = false;
    } else {
      // 详情
      this.disabled = true;
    }
    if (data) {
      this.addForm = data;
      this.FormType = "update";
      let name_list = [];
      this.dbName_list = "";
      this.addForm.members.forEach((item, index) => {
        name_list.push(item.userName);
      });
      this.dbName_list = name_list.toString();
      console.log(data.transType);
      this.addForm.transType = data.transType ? data.transType.toString() : '';

      if (data.handlingInfo) {
        this.addForm.handlingInfo = data.handlingInfo;
      } else {
        this.addForm.handlingInfo = {
          committeeCommentForDept: null,
          committeeGradeForDept: null,
          committeeCommentForResult: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        };
      }
      if (data.secondHandlingInfo) {
        this.addForm.secondHandlingInfo = data.secondHandlingInfo;
      } else {
        this.addForm.secondHandlingInfo = {
          committeeCommentForDept: null,
          committeeCommentForResult: null,
          committeeGradeForDept: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        };
      }
      if (data.isCompleted == 0) {
        this.isCompletedType = true;
      }
      //; 查看详情
    } else {
      this.FormType = "add";
      this.disabled = false;
    }
  },

  methods: {
    confirm () {
      this.dbName_list = "";
      this.addForm.members = [];
      this.addForm.members = this.selectedRows;
      let name_list = [];
      this.selectedRows.forEach((item, index) => {
        name_list.push(item.name);
        this.addForm.members[index].userName = item.name;
      });
      this.dbName_list = name_list.toString();
      this.DialogVisible = false;
    },
    // 代表列表名单
    getdbList () {
      getMembers(this.queryForm).then((res) => {
        if (res.code == "0000") {
          this.dataSource = res.data;
        }
      });
    },
    onSelectChange () { },
    opendbInformation () {
      this.getdbList();
      this.DialogVisible = true;
    },
    //
    changerCompletedType () {
      console.log(this.addForm.isCompleted);
      if (this.addForm.isCompleted == 0) {
        this.isCompletedType = true;
      } else {
        this.isCompletedType = false;
      }
    },
    //不能选择今天以后的日期
    disabledDate (current) {
      return current && current > moment().endOf("day");
    },

    change (item, e) {
      console.log(item);
      if (item.target.checked) {
        this.addForm[item.target.value] = true;
      } else {
        this.addForm[item.target.value] = false;
      }
    },
    // 获取数据
    fetchData () { },
    // 修改
    onUpdate () {
      this.$refs["addForm"].validate(async (valid) => {
        if (valid) {
          let data = this.addForm;

          data.transType = Number(data.transType);
          OpinionControllerUpdata(data).then((res) => {
            if (res.code == "0000") {
              this.$message.success("修改成功");
              this.$router.go(-1);
            }
          });
        } else {
          this.$message.error("校验失败");
        }
      });
    },
    //保存
    save () {
      this.$refs["addForm"].validate(async (valid) => {
        if (valid) {
          let data = this.addForm;
          data.transType = Number(data.transType);
          OpinionControllerSave(data).then((res) => {
            // console.log(res.code);
            if (res.code == "0000") {
              this.$message.success("保存成功");
              this.$router.go(-1);
            }
          });
        } else {
          this.$message.error("校验失败");
        }
      });

      //打开送审
    },
    // 复选框
    onChanger (even) {
      console.log(even);
      console.log(this.addForm.transType);
    },
    // 关闭
    close (e) {
      this.$router.go(-1);
    },
    quxiao () {
      this.$router.go(-1);
    },
    //监听父组件传过来的值
    // watch: {
    //   neWid: {
    //     immediate: true,    // 这句重要  立即执行handler里面的方法
    //     handler(val) {
    //     },
    //   },
    // },
  },
};
</script>
<style lang="scss" scoped>
.tit {
  margin-bottom: 15px;
  color: #000;
  //  font-size: 16px;
  @include add-size($font_size_16);
}
.formBox {
  padding: 20px;
}

.shangchuang {
  margin-left: 140px;
}

.reds {
  color: red;
  margin: 10px;
}
.jic {
  position: relative;
}
.jicson {
  color: red;
  // font-size: 2px;
  @include add-size($font_size_16);
  position: absolute;
  left: -65px;
  z-index: 999;
}
</style>
