<template>
  <div class="standard-table">
    <a-spin :indicator="indicator"
            :spinning="loading">
      <div v-show="showAlert"
           class="alert">
        <a-alert v-if="selectedRows"
                 type="info"
                 :show-icon="true">
          <div slot="message"
               class="message">
            已选择&nbsp;<a>{{ selectedRows.length }}</a>&nbsp;项 <a class="clear"
               @click="onClear">清空</a>
            <template v-for="(item, index) in needTotalList">
              <div v-if="item.needTotal"
                   :key="index">
                {{ item.title }}总计&nbsp;
                <a>{{
                item.customRender ? item.customRender(item.total) : item.total
              }}</a>
              </div>
            </template>
          </div>
        </a-alert>
      </div>
      <!--
      bordered  是否有边线
      rowKey    唯一性的key
      selectedRows []   是否展示 已选择 0 项
      scroll  滚动距离
      -->

      <a-table class="StandardTable"
               :bordered="bordered"
               :columns="columns"
               :data-source="dataSource"
               :row-key="rowKey"
               :pagination="pagination"
               :expanded-row-keys="expandedRowKeys"
               :expanded-row-render="expandedRowRender"
               :custom-row="clickRow"
               :scroll="scroll"
               :row-selection="
        showAlert
          ? selectedRows
            ? { selectedRowKeys: selectedRowKeys, onChange: updateSelect }
            : undefined
          : null
      "
               @change="onChange">
        <template v-for="slot in Object.keys($scopedSlots).filter(
          (key) => key !== 'expandedRowRender'
        )"
                  :slot="slot"
                  slot-scope="text, record, index">
          <slot :name="slot"
                v-bind="{ text, record, index }"></slot>
        </template>
        <template v-for="slot in Object.keys($slots)"
                  :slot="slot">
          <slot :name="slot"></slot>
        </template>
        <template :slot="$scopedSlots.expandedRowRender ? 'expandedRowRender' : ''"
                  slot-scope="record, index, indent, expanded">
          <slot v-bind="{ record, index, indent, expanded }"
                :name="$scopedSlots.expandedRowRender ? 'expandedRowRender' : ''"></slot>
        </template>
      </a-table>
    </a-spin>
  </div>
</template>

<script>
export default {
  name: "StandardTable",
  props: {
    bordered: {
      type: [Boolean, Object],
      default: false,
    },
    loading: {
      type: [Boolean, Object],
      default: false,
    },
    columns: Array,
    dataSource: Array,
    rowKey: {
      type: [String, Function],
      default: "key",
    },
    showAlert: {
      type: Boolean,
      default: true,
    },
    scroll: {
      type: Object,
      default: () => {
        return { x: 700, y: 700 };
      },
    },
    pagination: {
      type: [Object, Boolean],
      default: () => {
        return {
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0,
          showTotal: (total) => `总共 ${total} 条`,
        };
      },
    },
    selectedRows: Array,
    expandedRowKeys: Array,
    expandedRowRender: Function,
  },
  data () {
    return {
      needTotalList: [],
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
    };
  },
  computed: {
    //  计算选中
    selectedRowKeys () {
      return this.selectedRows.map((record, index) => {
        return typeof this.rowKey === "function"
          ? this.rowKey(record, index)
          : record[this.rowKey];
      });
    },
  },
  watch: {
    // 监听选中事件的方法
    selectedRows (selectedRows) {
      this.needTotalList = this.needTotalList.map((item) => {
        return {
          ...item,
          total: selectedRows.reduce((sum, val) => {
            let v;
            try {
              v = val[item.dataIndex]
                ? val[item.dataIndex]
                : eval(`val.${item.dataIndex}`);
            } catch (_) {
              v = val[item.dataIndex];
            }
            v = !isNaN(parseFloat(v)) ? parseFloat(v) : 0;
            return sum + v;
          }, 0),
        };
      });
    },
  },
  created () {
    this.needTotalList = this.initTotalList(this.columns);
  },
  methods: {
    // 点击行
    clickRow (record) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            this.$emit("tableClick", event, record);
          },
        },
      };
    },
    // 表格的事件
    updateSelect (selectedRowKeys, selectedRows) {
      this.$emit("update:selectedRows", selectedRows);
      this.$emit("selectedRowChange", selectedRowKeys, selectedRows);
    },
    // 表头的事件
    initTotalList (columns) {
      const totalList = columns
        .filter((item) => item.needTotal)
        .map((item) => {
          return {
            ...item,
            total: 0,
          };
        });
      return totalList;
    },
    // 清空多选事件
    onClear () {
      this.updateSelect([], []);
      this.$emit("clear");
    },
    onChange (pagination, filters, sorter, { currentDataSource }) {
      this.$emit("change", pagination, filters, sorter, { currentDataSource });
    },
  },
};
</script>

<style scoped>
.standard-table .message a {
  font-weight: 600;
}
.standard-table .clear {
  float: right;
}
.standard-table .alert {
  margin-bottom: 16px;
}
</style>
