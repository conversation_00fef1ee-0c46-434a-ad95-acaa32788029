<template>
  <div class="table-container">
     <!-- <a-button   style="float: left;z-index: 99;margin-bottom: 10px;" @click="$router.go(-1)"> 返回</a-button> -->
    <a-spin :indicator="indicator" :spinning="listLoading">
      <a-table
        :data-source="list"
        bordered
        :pagination="pagination"
        :rowKey="
          (record, index) => {
            return index;
          }
        "
      >
        <!-- <a-table-column type="selection" width="55"></a-table-column> -->
        <a-table-column title="序号" width="100">
          <template slot-scope="text, record, index">{{ index + 1 }}</template>
        </a-table-column>
        <a-table-column title="标题">
          <template slot-scope="text, record">
            {{ record.title }}
          </template>
        </a-table-column>
        <a-table-column title="状态">
          <template slot-scope="text, record">
            {{ record.statusDesc }}
          </template>
        </a-table-column>
        <a-table-column title="代表团" width="200">
          <template slot-scope="text, record">
            {{ record.group }}
          </template>
        </a-table-column>
        <a-table-column title="受邀人员" width="100">
          <template slot-scope="text, record">
            {{ record.name }}
          </template>
        </a-table-column>
        <a-table-column title="受邀形式" width="180">
          <template slot-scope="text, record">
            {{ record.typeDesc }}
          </template>
        </a-table-column>
        <a-table-column title="是否出席" width="100">
          <template slot-scope="text, record">
            {{ record.isSigned == 0 ? "未出席" : "已出席" }}
          </template>
        </a-table-column>
        <a-table-column title="出席时间" width="180">
          <template slot-scope="text, record">
            {{ record.signedTime }}
          </template>
        </a-table-column>
        <!-- <a-table-column title="操作" width="180px" fixed="right" prop="id">
          <template slot-scope="text, record">
            <span
              v-show="record.status == 1"
              style="color: #e6a23c; cursor: pointer;"
              @click="publicOpinionList(record)"
              >社情民意采集</span
            >
          </template>
        </a-table-column> -->
      </a-table>
    </a-spin>
  </div>
</template>

<script>
import { getSignInfoByPage } from "@/api/communityschedule";
import events from "@/components/events.js";

export default {
  data() {
    return {
      //list-----
      elementLoadingText: "正在加载...",
      list: [],
      listQuery: {},
      listLoading: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 10,
      background: true,
      //list-----
      communityScheduleId: "",
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  mounted() {
    this.communityScheduleId = this.$route.query.communityScheduleId;
  },
  created() {
    this.fetchData();
     this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "进社区计划");
    this.$store.dispatch("navigation/breadcrumb3", "进社区代表列表");
  },
  methods: {
    fetchData() {
      let id = this.$route.query.communityScheduleId;
      this.listLoading = true;
      getSignInfoByPage({ id: id }).then((response) => {
        console.log("🤗🤗🤗, response =>", response);
        this.list = response.rows;
        this.listLoading = false;
        this.total = response.total;
        this.pagination.total = response.total;
      });
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.listQuery.current = pageNum;
      this.listQuery.size = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.listQuery.current = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },

    // handleSizeChange(val) {
    //   this.listQuery.size = val;
    //   this.fetchData();
    // },
    // handleCurrentChange(val) {
    //   this.listQuery.current = val;
    //   this.fetchData();
    // },
    publicOpinionList(row) {
      // 左侧菜单
       events.$emit("sendEven", {
                path: "/publicopinion/publilist",
                openKey: 9,
              });
      this.$router.push({
        path: "/publicopinion/publilist",
        query: {
          userId: row.userId,
          communityScheduleId: row.communityScheduleId,
          oper: "add",
        },
      });
    },
  },
};
</script>

<style></style>
