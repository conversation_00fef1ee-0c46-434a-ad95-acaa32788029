<template>
  <a-modal
    :title="orgTreeDialogTitle"
    :visible.sync="orgTreeDialogVisible"
    width="50%"
    destroyOnClose
    :mask="false"
    @cancel="close"

  >
    <!-- <el-tree
      ref="orgTree"
      :data="orgTreeData"
      :props="orgTreeProps"
      node-key="orgId"
      @check-change="orgCheckChange"
      show-checkbox
      check-strictly
      :check-on-click-node="isRadio"
      :default-checked-keys="orgTreeDefaultKey"
    ></el-tree>
  -->
    <a-tree
      ref="orgTree"
      checkable
      multiple
      :default-expanded-keys="expandedKeys"
      :replace-fields="replaceFields"
      :tree-data="orgTreeData"
      v-model="checkIdentyData"
      @check="onCheck"
      checkStrictly
    ></a-tree>

    <span slot="footer" class="dialog-footer">
      <a-button  @cancel="close">取 消</a-button>
      <a-button style="padding-left: 10px" type="primary" @click="confirm">确 定</a-button>
    </span>
  </a-modal>
</template>

<script>
import { getTree as getOrgTree } from "@/api/organization";
export default {
  data() {
    return {
      name: "",
      expandedKeys:[],
      searchValue: "",
      allChildData: [],
      selectedKeys: [],
      autoExpandParent: true,
      checked: [],
      checkIdentyData: [],
      orgTreeDialogVisible: false,
      orgTreeData: [],
      orgTreeDefaultKey: [],
      replaceFields: {
        title: "orgName",
        key: "orgId",
        children: "children",
      },
      // orgTreeProps: {
      //   children: 'children',
      //   label: 'orgName',
      // },
      isRadio: true,
      relType: {
        type: Number,
        default: 1,
      },
      noCheckKeys: [],
    };
  },
  props: {
    orgTreeDialogTitle: {
      type: String,
      default: "选择单位",
    },
  },
  created() {
    this.initOrgTree();
  },
   watch: {
  orgTreeDialogVisible () {
      if (this.orgTreeDialogVisible) {
        this.expandedKeys.forEach(item=>{
            this.expandedKeys.push(item)
        })
         this.checkIdentyData.forEach(item=>{
            this.checkIdentyData.push(item)
        })
      }else{
          this.expandedKeys=[]
           this.checked=[]
          this.checkIdentyData=[]
      }
    }
   },
  methods: {
   close() {
      this.orgTreeDialogVisible = false;
      this.expankIdentyData=[]
      this.checdedKeys=[]
      this.checked=[]
    },
    initOrgTree() {
      let data = {
        tenantId: this.$tenantId,
        rootOrgId: "root",
      };
      getOrgTree(data).then((res) => {
        this.orgTreeData = res.data.children;
      });
    },
    open(keys, noCheckKeys, radioType, relType,userForm) {
      this.relType = relType;
      //   //  checkIdentyData回显数据
      // this.checkIdentyData = keys;
      // this.expandedKeys = keys;
      if(relType==0){
         userForm.forEach(item=>{
           if(item.relType==0){
               this.checked.push(item)
           }
         })
      }else{
             userForm.forEach(item=>{
           if(item.relType==1){
               this.checked.push(item)
           }
         })
      }
   
      
      keys.forEach(item=>{
        this.expandedKeys.push(item)
        this.checkIdentyData.push(item)
      })
      this.orgTreeDialogVisible = true;
      if (this.$refs.orgTree) {
        // this.$refs.orgTree.setCheckedKeys(keys)
      } else {
        this.orgTreeDefaultKey = keys;
      }
      if (radioType != undefined) {
        this.isRadio = radioType;
      }
      if (relType != undefined) {
        this.relType = relType;
      }
      if (noCheckKeys != undefined) {
        this.noCheckKeys = noCheckKeys;
      }
    },
    orgCheckChange(data, checked, indeterminate) {
      if (this.isRadio && checked) {
        this.$refs.orgTree.setCheckedKeys([data.orgId]);
      }
      if (this.noCheckKeys.includes(data.orgId) && checked) {
        this.$refs.orgTree.setChecked(data.orgId, false);
        this.$message.error(data.orgName + "已关联,当前不可选");
      }
    },
    // 树状选择
    onCheck(item, data) {
      if(data.checked){
           //  this.checkedShow=true
      let value=data.node.dataRef
      this.checked.push(value)
      // data.checkedNodesPositions.filter((item1) => {
      //   this.checked.push(item1.node.componentOptions.propsData.dataRef);
      // });
      }else{
        let orgId= data.node.dataRef.orgId
         this.checked.forEach((item,index)=>{
            if(item.orgId==orgId){
                 this.checked.splice(index, 1)
            }
         })
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
    },
    confirm() {
      this.noCheckKeys = [];
      this.orgTreeDialogVisible = false;
      this.$emit("confirm", this.checked, this.relType);
      this.checkIdentyData = [];
    },
  },
};
</script>
