<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <byui-query-form>
          <byui-query-form-left-panel :span="8">
            <el-button
              v-if="checkPermission(['OPER_DATA_SOURCE_BASE'])"
              icon="el-icon-plus"
              type="primary"
              @click="handleAdd"
              >添加</el-button
            >
            <el-button
              v-if="checkPermission(['OPER_DATA_SOURCE_BASE'])"
              icon="el-icon-delete"
              type="danger"
              @click="handleDelete"
              >删除</el-button
            >
          </byui-query-form-left-panel>
          <byui-query-form-right-panel :span="16">
            <el-form
              ref="form"
              :model="queryForm"
              :inline="true"
              @submit.native.prevent
            >
              <el-form-item>
                <el-input
                  v-model="queryForm.dsCode"
                  placeholder="数据源编号"
                  class="table-input"
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryForm.dsName"
                  placeholder="数据源名称"
                  class="table-input"
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryForm.dsDesc"
                  placeholder="数据源描述"
                  class="table-input"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  type="primary"
                  native-type="submit"
                  @click="handleQuery"
                  >查询</el-button
                >
              </el-form-item>
            </el-form>
          </byui-query-form-right-panel>
        </byui-query-form>

        <el-table
          ref="datasourceTable"
          v-loading="listLoading"
          :data="list"
          border
          :element-loading-text="elementLoadingText"
          @selection-change="setSelectRows"
          @sort-change="tableSortChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="序号" width="95">
            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column label="数据源编号" prop="dsCode"></el-table-column>
          <el-table-column label="数据源名称" prop="dsName"></el-table-column>
          <el-table-column label="数据源描述" prop="dsDesc"></el-table-column>
          <el-table-column label="操作" width="180px" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="checkPermission(['OPER_DATA_SOURCE_BASE'])"
                type="text"
                @click="handleEdit(scope.row)"
                >编辑</el-button
              >
              <el-button
                v-if="checkPermission(['OPER_DATA_SOURCE_BASE'])"
                type="text"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :background="background"
          :current-page="queryForm.page"
          :layout="layout1"
          :page-size="queryForm.rows"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        ></el-pagination>
      </a-col>
    </a-row>
    <edit ref="edit"></edit>
  </div>
</template>

<script>
import { getPage, doDelete } from "@/api/datasource";
import Edit from "./edit";
import { mapGetters } from "vuex";
import checkPermission from "@/utils/permission";
export default {
  name: "Table",
  components: {
    Edit,
  },
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
  },
  filters: {},
  data() {
    return {
      imgShow: true,
      list: [],
      listLoading: true,
      layout1: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      queryForm: {
        page: 1,
        rows: 10,
      },
    };
  },
  created() {
    this.fetchData();
  },
  beforeDestroy() {},
  mounted() {},
  methods: {
    tableSortChange(column) {},
    setSelectRows(val) {
      this.selectRows = val;
    },
    handleAdd() {
      this.$refs["edit"].showEdit();
    },
    handleEdit(row) {
      this.$refs["edit"].showEdit(row);
    },
    handleDelete(row) {
      if (row.dsId) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          doDelete([row.dsId]).then((res) => {
            this.$baseMessage("删除成功", "success");
            this.fetchData();
          });
        });
      } else {
        if (this.selectRows.length > 0) {
          const ids = this.selectRows.map((item) => item.dsId);
          this.$baseConfirm("你确定要删除选中项吗", null, () => {
            doDelete(ids).then((res) => {
              this.$baseMessage("删除成功", "success");
              this.fetchData();
            });
          });
        } else {
          this.$baseMessage("未选中任何行", "error");
          return false;
        }
      }
    },
    handleSizeChange(val) {
      this.queryForm.rows = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryForm.page = val;
      this.fetchData();
    },
    handleQuery() {
      this.queryForm.page = 1;
      this.fetchData();
    },
    fetchData() {
      this.listLoading = true;
      getPage(this.queryForm).then((res) => {
        this.list = res.data.records;
        this.total = res.data.total;
        setTimeout(() => {
          this.listLoading = false;
        }, 500);
      });
    },
    checkPermission,
  },
};
</script>
