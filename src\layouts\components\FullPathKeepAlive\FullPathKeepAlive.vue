<template>
  <div>
    <keep-alive :max="15">
      <router-view  style="min-height: 78vh;" :key="$route.fullPath" ref="page" v-if="keepAlivePath.includes($route.path)"/>
    </keep-alive>
    <router-view  style="min-height: 78vh;" :key="$route.fullPath" ref="page" v-if="!keepAlivePath.includes($route.path)"/>
  </div>
</template>

<script>
import { EventBus } from '@/event-bus'
import { keepAlivePath } from './NeedKeepAlivePath'

export default {
  name: 'FullPathKeepAlive',
  data () {
    return {
      keepAlivePath: keepAlivePath
    }
  },
  // watch: {
  // '$route'(to, from) {
  //   // 当路由变化时执行的操作
  //   console.log('Route changed from', from.fullPath, 'to', to.fullPath);
  //   // 你可以在这里添加加载完成后的回调逻辑
  //   }
  // },
  mounted () {
    EventBus.$on('FxCleanTabCache', fullPath => {
      if (typeof this.$refs.page === 'undefined') {
        return false
      }
      this.$nextTick(() => {
        const {
          cache
        } = this.$refs.page.$vnode.parent.componentInstance
        if (cache[fullPath]) {
          delete cache[fullPath]
        }
      })
    })
  }
}
</script>