<template>
  <div  class="table-container">
    <div style="background-color: #ececec; padding: 20px;">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card title="代表总人数" :bordered="false">
            <p  class="num">2258</p>
            <!-- style="font-size: 14px;margin-left: 20px;" -->
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="全国代表" :bordered="false">
            <p  class="num">231</p>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="省代表" :bordered="false">
            <p  class="num">360</p>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="市代表" :bordered="false">
            <p  class="num">110</p>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="区代表" :bordered="false">
            <p  class="num">442</p>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="乡镇代表" :bordered="false">
            <p  class="num">144</p>
          </a-card>
        </a-col>
      </a-row>
    </div>
    <!-- <ZTable :list="list" :columns="columns" /> -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          thNum: "第十五届",
          type: "市代表",
          name: "林*强",
          noticeDate: "2021.01.11",
          sex: "男",
          birthdayTime: "1974.03.25",
          party: "中国共产党",
          education: "本科",
          unit: "xx技术有限公司 董事长",
          createTime: "2021.09.22",
        },
        {
          thNum: "第十五届",
          type: "市代表",
          name: "陈*全",
          noticeDate: "2021.02.21",
          sex: "男",
          birthdayTime: "1966.03.25",
          party: "中国共产党",
          education: "本科",
          unit: "xx技术有限公司 董事长",
          createTime: "2021.02.11",
        },
        {
          thNum: "第十五届",
          type: "市代表",
          name: "林*生",
          noticeDate: "2021.04.23",
          sex: "男",
          birthdayTime: "1973.03.25",
          party: "中国共产党",
          education: "本科",
          unit: "xx技术有限公司 董事长",
          createTime: "2021.06.21",
        },
        {
          thNum: "第十五届",
          type: "市代表",
          name: "徐*前",
          noticeDate: "2021.09.12",
          sex: "男",
          birthdayTime: "1971.03.25",
          party: "中国共产党",
          education: "本科",
          unit: "xx技术有限公司 董事长",
          createTime: "2021.02.06",
        },
        {
          thNum: "第十五届",
          type: "市代表",
          name: "肖*琳",
          noticeDate: "2021.10.19",
          sex: "女",
          birthdayTime: "1977.03.25",
          party: "中国共产党",
          education: "本科",
          unit: "xx技术有限公司 董事长",
          createTime: "2021.04.09",
        },
        {
          thNum: "第十五届",
          type: "市代表",
          name: "吴*宇",
          noticeDate: "2021.11.14",
          sex: "男",
          birthdayTime: "1969.03.25",
          party: "中国共产党",
          education: "本科",
          unit: "xx技术有限公司 董事长",
          createTime: "2021.05.01",
        },
        {
          thNum: "第十五届",
          type: "市代表",
          name: "钟*生",
          noticeDate: "2021.08.08",
          sex: "男",
          birthdayTime: "1984.03.25",
          party: "中国共产党",
          education: "本科",
          unit: "xx技术有限公司 董事长",
          createTime: "2021.05.22",
        },
      ],
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "届次",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "thNum",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "代表类型",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "type",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "name",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "提醒日期",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "noticeDate",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "性别",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "sex",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "出生日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "birthdayTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "党派",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "party",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "学历",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "education",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "工作单位和职务",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "unit",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "手机号码",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "phone",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "变更日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.switMeeting(record);
                    },
                  },
                },
                "查看"
              ),

              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "当选管理");
    this.$store.dispatch("navigation/breadcrumb2", "统计报表");
  },
};
</script>
<style lang="scss" scoped>
.num{
  // font-size: 14px;
  @include add-size($font_size_16);
  margin-left: 20px;
}
 
</style>
