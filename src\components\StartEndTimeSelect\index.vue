<template>
  <div>
    <a-col :span="spanNum">
      <a-form-model-item :label="startTitle" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-date-picker style="width: 100%;"
          :disabled="disabled"
          v-model="startTime"
          allow-clear
          value-format="YYYY-MM-DD"
          :placeholder="'请选择'+ startTitle"
          :disabled-date="
          (current) =>
            current && endTime
              ? current.valueOf() >=
              moment(new Date(endTime)).valueOf()
              : false
          " 
          @change="onStartChange"
        />
      </a-form-model-item>
    </a-col>
    <a-col :span="spanNum">
      <a-form-model-item :label="endTitle" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-date-picker style="width: 100%;"
          :disabled="disabled"
          v-model="endTime"
          allow-clear
          value-format="YYYY-MM-DD"
          :placeholder="'请选择'+ endTitle"
          :disabled-date="
          (current) =>
            current && startTime
            ? moment(new Date(startTime)).valueOf() >=
              current.valueOf()
            : false
          "
          @change="onEndChange"
        />
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
import moment from 'moment';

export default {
  props: {
    startTitle: {
      type: String,
      default: () => '开始时间',
    },
    endTitle: {
      type: String,
      default: () => '结束时间',
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    startValue: {
      type: [String, Number],
      default: () => '',
    },
    endValue: {
      type: [String, Number],
      default: () => '',
    },
  },
  data() {
    return {
      startTime: null,
      endTime: null,
    };
  },
  watch: {   
    startValue: {  
      handler(val) {
        this.startTime = val;
      },
      deep: true,
      immediate: true,
    },
    endValue: {  
      handler(val) {
        this.endTime = val;
      },
      deep: true,
      immediate: true,
    },
  }, 
  methods: {
    onStartChange(val) {
      this.startTime = moment(val).format('YYYY-MM-DD'); 
      // this.$emit('getStartTime', this.startTime)
      this.$emit('update:startValue', this.startTime)
    },
    onEndChange(val) {
      this.endTime = moment(val).format('YYYY-MM-DD'); 
      // this.$emit('getEndTime', this.endTime)
      this.$emit('update:endValue', this.endTime)
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
