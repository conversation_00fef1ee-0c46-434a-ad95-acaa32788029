import { instance_yajy } from "@/api/axiosRq";
import store from "@/store";

// 公共上传接口13楼
export function fileUpload(data) {
  return instance_yajy({
    url: "/api/v1/system/attachment/fileUpload",
    method: "post",
    headers: {
      token: store.getters.accessToken,
      "Content-type": "multipart/form-data",
    },
    data,
  });
}

// 公共预览接口
export function previewFile(id) {
  return instance_yajy({
    url: `/api/v1/system/attachment/preview/${id}`,
    method: "get",
    responseType: "blob",
  });
}

// 下载接口
export function downFile(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/downloadEnclosure`,
    method: "post",
    responseType: "blob",
    params: { type: form.type },
    data: form.relId,
  });
}
//下载答复函
export function downDfhFile(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/downloadDfhFile`,
    method: "post",
    responseType: "blob",
    params: { type: form.type },
    data: form.relId,
  });
}


// 下载接口 同个类型多个文件
export function downFileItem(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/downloadEnclosure`,
    method: "post",
    responseType: "blob",
    params: { type: form.type, attId: form.attId },
    data: form.relId,
  });
}
// 获取登录人
export function getUserData_yajy() {
  return instance_yajy({
    url: `/api/v1/auth/userInfoData`,
    method: "post",
  });
}
// 获取议案建议相关附件
export function getAttachmentList(form) {
  let formData = new FormData();
  for (const key in form) {
    formData.append(key, form[key]);
  }
  return instance_yajy({
    url: `/api/v1/system/attachment/getAttachmentList`,
    method: "post",
    data: formData,
  });
}
// 获取代表信息
export function getDBInfo() {
  return instance_yajy({
    url: `/api/v1/auth/getDBInfo`,
    method: "post",
  });
}
export function updateDBInfo(from) {
  return instance_yajy({
    url: `/deputy/updateDBInfo`,
    method: "post",
    data: from,
  });
}
// 删除附件
export function deleteById(form) {
  return instance_yajy({
    url: `/api/v1/system/attachment/deleteById`,
    method: "post",
    params: form,
  });
}
