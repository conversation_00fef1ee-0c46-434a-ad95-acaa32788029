<template>
  <div class="time_line">
    <h3 class="time_title" v-if="titleInfo">{{titleInfo}}</h3>
    <div class="time_content">
      <a-timeline>
        <a-timeline-item 
          v-for="(activity, index) in activities"
          :key="index"
          :color="activity.color">
            <div 
              v-if="isLink"
              class="icon_title" 
              :class="[  
                activeNum > index ? 'finished' : '',  
                activeNum === index ? 'pass' : ''  
              ]"
              @click="disabled ? null : changeInfo(activity, index)"
              slot="dot">
              {{index+1}}
            </div>
            <div 
              v-if="!isLink"
              class="icon_circle"
              @click="changeInfo(activity, index)"
              slot="dot">
            </div>
            {{activity.content}}
          </a-timeline-item>
      </a-timeline>
    </div>
  </div>
</template>

<script>
export default {
  name: "timeLine",
  props: {
    lineList: {
      type: Array,
      default: () => [],
    },
    tab: {
      type: Number,
      default: 0,
    },
    isLink: { // isLink 样式 点击跳转页面为true 否则为false
      type: Boolean, 
      default: true,
    },
    titleInfo: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {},
  watch: {   
    tab: {  
      handler(val) {
        this.activeNum = val;
      },
      deep: true,
      immediate: true,
    },
  }, 
  data() {
    return {
      activities: [
      {
        content: '创建代表活动',
        timestamp: '2018-04-13',
        type: '提交成功',
        status: 'unPass',
        // status: 'finished',
      }, {
        content: '参加意向征集',
        timestamp: '2018-04-11',
        status: 'unPass',
        type: '审核通过',
        color: '#0bbd87',
        remark: '备注：审核通过'
      }, {
        content: '活动通知发布',
        timestamp: '2018-04-13',
        type: '提交成功',
        status: 'unPass',
      }, {
        content: '活动报名',
        timestamp: '2018-04-13',
        type: '提交成功',
        status: 'unPass',
      }, {
        content: '活动签到',
        timestamp: '2018-04-13',
        type: '提交成功',
        status: 'unPass',
      }, {
        content: '履职活动录入',
        timestamp: '2018-04-13',
        type: '提交成功',
        status: 'unPass',
      }, {
        content: '审核',
        timestamp: '2018-04-13',
        type: '提交成功',
        status: 'unPass',
      },{
        content: '活动归档',
        timestamp: '2018-04-13',
        type: '提交成功',
        status: 'pass',
      }],
      numInfo: ['','','',''],
      activeNum: 0,
    };
  },
  mounted() {
    if (this.lineList.length > 0) {
      this.activities = this.lineList
      this.activeNum = this.tab 
    }
  },
  methods: {
    changeInfo(data, index) {
      this.activeNum = index;
      this.$emit('update:tab', index)
      // this.$emit('newIndex', index)
      // this.$emit('newData', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.time_line {
  // flex: 2;
  flex-shrink: 0;
  border-left: 1px solid #ccc;
  padding: 0 20px;
  // padding: 10px 10px;
  // padding-left: 20px;
  .time_title {
    height: 30px;
    line-height: 30px;
    font-weight: bold;
    font-size: 18px;
    box-sizing: border-box;
    // font-weight: 700;
    // color: #333;
    // margin-bottom: 15px;
  }
  .time_content {
    padding: 10px;
  }
  .icon_title {
    width: 25px;
    height: 25px;
    line-height: 25px;
    background-color: #ededed;
    color: #666;
    border-radius: 50%;
    // font-size: 12px;
    cursor: pointer;
  }
  .icon_circle {
    position: relative;
    width: 16px;
    height: 16px;
    background-color: #d92b3e;
    border-radius: 50%;
  }
  .icon_circle::after {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    content: "";
    width: 12px;
    height: 12px;
    background-color: #fff;
    border-radius: 50%;
  }
}
.time_content {
  color: #bbbbbd;
  // margin: 5px 0 10px 0;
  margin-top: 5px;
  font-size: 13px;
}
.copy_write {
  position: relative;
  margin-bottom: 15px;
  font-size: 12px;
  .dot {
    position: absolute;
    top: 50%;
    left: -23px;
    transform: translate(0, -50%);
    width: 7px;
    height: 7px;
    background-color: #fff;
    border: 2px solid transparent;
    border-radius: 100px;
    color: #1890ff;
    border-color: #1890ff;
  }
}
.finished {
  width: 30px!important;
  height: 30px!important;
  line-height: 30px!important;
  background-color: #d5f2f7!important;
  color: #6a8499!important;
}
.pass {
  width: 30px!important;
  height: 30px!important;
  line-height: 30px!important;
  background-color: #c71c33!important;
  color: #fff!important;
}
::v-deep .ant-timeline-item-content {
  margin-left: 25px;
}
</style>