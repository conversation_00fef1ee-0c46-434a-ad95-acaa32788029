<template>
  <a-row>
    <a-col :span="24">
      <a-form-model-item 
        :label="valueInfo.name"
        :prop="valueInfo.value"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 24 }">
        <a-textarea
            disabled
            v-model="valueInfo.valueInfo"
            autocomplete="off"
            type="textarea"
            :auto-size="{ minRows: 1, maxRows: 5 }"
          >
          </a-textarea>
      </a-form-model-item>
    </a-col>
  </a-row>
</template>

<script>
export default {
  name: "timeLine",
  props: ['valueInfo'],
  components: {},
  data() {
    return {
      
    };
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
</style>