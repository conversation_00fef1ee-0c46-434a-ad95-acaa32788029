<template>
  <a-modal destroyOnClose :visible.sync="jointTableVisible" width="90%" title="分配代表团" @cancel="close" @ok="unitTableOk"
    okText="送审" cancelText="取消">
    <a-row class="steps">
      <a-steps :current="Steps">
        <a-step title="第一步：分配"></a-step>
        <a-step title="第二步：初审" />
        <a-step title="第三步：终审" />
        <a-step title="分配表审核完成" />
      </a-steps>
    </a-row>
    <a-row style="margin-top: 30px;">
      <a-col :span="11">
        <a-form-model ref="leftQueryForm" layout="inline" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }"
          :model="leftQueryForm">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="届次" style="width: 100%">
                <a-select v-model="leftQueryForm.jcDm" allow-clear disabled style="width: 100%;">
                  <a-select-option v-for="item in periods" :key="item.jcDm" :value="item.jcDm">{{ item.levelName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="姓名" prop="xm" style="width: 100%">
                <a-input v-model="leftQueryForm.xm" autocomplete="off" allow-clear placeholder="请输入姓名"
                  v-on:keyup.enter="getdfphxrApiData">
                </a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="推荐单位" prop="tjdw" style="width: 100%">
                <a-input v-model="leftQueryForm.tjdw" autocomplete="off" placeholder="请输入推荐单位"
                  v-on:keyup.enter="getdfphxrApiData"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item style="width: 100%">
                <a-button type="primary" @click="getdfphxrApiData">搜索</a-button>
                <a-button style="margin-left: 12px;" @click="reset('left')" class="pinkBoutton">
                  重置</a-button>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>

        <a-table bordered :columns="columns" :dataSource="leftNowData" rowKey="DBID" :row-selection="{
          selectedRowKeys: selectedRowLeftKeys,
          onSelect: onLeftSelect,
        }" :pagination="leftPagination" :scroll="{ x: '50%', y: 450 }"></a-table>
      </a-col>
      <a-col :span="1">
        <div class="changeBtn">
          <a-button type size="small" @click="toRight">
            <a-icon type="right" />
          </a-button>
          <br />
          <a-button type size="small" @click="toLeft">
            <a-icon type="left" />
          </a-button>
        </div>
      </a-col>

      <a-col :span="11">
        <a-form-model ref="rightQueryForm" layout="inline" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }"
          :model="rightQueryForm">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="届次" style="width: 100%">
                <a-select v-model="rightQueryForm.jcDm" allow-clear disabled style="width: 100%">
                  <a-select-option v-for="item in periods" :key="item.jcDm" :value="item.jcDm">{{ item.levelName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="姓名" prop="xm" style="width: 100%">
                <a-input v-model="rightQueryForm.xm" autocomplete="off" placeholder="请输入姓名" allow-clear
                  v-on:keyup.enter="getyfphxrApiData"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">

              <a-form-model-item label="推荐单位" prop="tjdw" style="width: 100%">
                <a-input v-model="rightQueryForm.tjdw" autocomplete="off" allow-clear placeholder="请输入推荐单位"
                  v-on:keyup.enter="getyfphxrApiData">
                </a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">

              <a-form-model-item style="width: 100%">
                <a-button type="primary" @click="getyfphxrApiData">搜索</a-button>
                <a-button style="margin-left: 12px;" @click="reset('right')" class="pinkBoutton">
                  重置</a-button>
              </a-form-model-item>
            </a-col>
          </a-row>


        </a-form-model>
        <a-table bordered :dataSource="rightNowData" rowKey="DBID" :row-selection="{
          selectedRowKeys: selectedRightRowKeys,
          onSelect: onRightSelect,
        }" :columns="rightColumns" :pagination="false" :scroll="{ x: '50%', y: 450 }"></a-table>
      </a-col>
    </a-row>
    <a-modal :visible="selectElectionVisible" title="请选择代表团" @ok="selectElectionFormOk"
      @cancel="selectElectionFormCancel">
      <a-form-model>
        <a-form-model-item label="代表团">
          <a-select v-model="selectElectionForm.dbtDm" labelInValue>
            <a-select-option v-for="item in unitList" :key="item.dbtDm" :value="item.dbtDm" :label="item.dbtmc">{{
                item.dbtmc
            }}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <assignmentDelegationShip ref="assignmentDelegationShip" :procInstId="procInstId" @complete="handleComplete"
      @parentClose="close" :ids="ids"></assignmentDelegationShip>
  </a-modal>
</template>
<script>
import { getDeputyList } from "@/api/myJob/myProposal.js";
import { getDelegation } from "@/api/election.js";
import assignmentDelegationShip from "./assignmentDelegationShip";
import {
  getdfphxrApi,
  getyfphxrApi,
  hxrfpbsUpdate,
  hxrfpbsAssign,
  sdbAndDbtbList,
  newDbtdbList,
} from "@/api/representativeElection/candidateApi.js";
import {
  getLevelListApi,
  fpdbtsCreate,
  fpdbtsUpdate,
} from "@/api/representativeElection/candidateApi.js";
export default {
  components: { assignmentDelegationShip },
  props: { defaultSelectKey: Array },
  // 构造数据
  data() {
    return {
      Steps: 0,
      jointTableVisible: false,
      selectElectionVisible: false,
      selectElectionForm: { dbtDm: "" },
      searchValue: "",
      columns: [
        {
          title: "当选代表",
          dataIndex: "DBXM",
          ellipsis: true,
          align: "center",
          width: 120,
        },
        {
          title: "性别",
          dataIndex: "SEX",
          align: "center",
          ellipsis: true,
          width: 70,
        },
        {
          title: "出生日期",
          dataIndex: "BIRTHDAY",
          ellipsis: true,
          align: "center",
          width: 180,
          customRender: (text, record, index) => {

            //  return  text.replace("T", " ").split("Z").join("").substr(0, 19);
            return text.slice(0, text.indexOf("T"));
          }
        },
        // 代表团
        {
          title: "选举单位",
          dataIndex: "DBTMC",
          ellipsis: true,
          align: "center",
          width: 100,
          customRender: (text, record, index) => {
            return text.slice(0, text.indexOf("代表团")) + '区';
          }
        },
        {
          title: "推荐单位",
          dataIndex: "MELYMC",
          ellipsis: true,
          width: 100,
        },
      ],
      rightColumns: [
        {
          title: "当选代表",
          dataIndex: "DBXM",
          align: "center",
          ellipsis: true,
          width: 120,
        },
        {
          title: "性别",
          dataIndex: "SEX",
          align: "center",
          ellipsis: true,
          width: 70,
        },
        {
          title: "出生日期",
          dataIndex: "BIRTHDAY",
          ellipsis: true,
          align: "center",
          width: 180,
          customRender: (text, record, index) => {

            //  return  text.replace("T", " ").split("Z").join("").substr(0, 19);
            return text.slice(0, text.indexOf("T"));
          }
        },
        // 代表团
        {
          title: "选举单位",
          dataIndex: "DBTMC",
          ellipsis: true,
          align: "center",
          width: 100,
          customRender: (text, record, index) => {
            return text.slice(0, text.indexOf("代表团")) + '区';
          }
        },
        {
          title: "推荐单位",
          dataIndex: "MELYMC",
          ellipsis: true,
          width: 100,
        },
      ],
      multiple: true, //多选
      rightData: [],
      leftData: [],
      rightNowData: [],
      leftNowData: [],
      selectedRowLeftKeys: [],
      selectedRightRowKeys: [],
      unitList: [],
      periods: [],
      ids: [],
      waterId: "",
      procInstId: "",
      upDataForm: {},
      leftQueryForm: {
        jcDm: "",
        xm: "",
        dbtDm: "all",
        pageNum: 1,
        pageSize: 10,
      },
      rightQueryForm: {
        jcDm: 2,
        dbtDm: "",
        xm2: "",
        fpdbtId: "",
        pageNum: 1,
        pageSize: 10,
      },
      // 左分页器设置
      leftPagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSizeLeft(current, pageSize), // 改变每页数量时更新显示
        onChange: this.onChangeLeft.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  watch: {
    jointTableVisible(newVal) {
      if (newVal) {
        this.getDeputyListData();
        this.getDelegationData();
      }
    },
  },
  methods: {
    // 切换页数 左
    changePageSizeLeft(pageNum, pageSize) {
      this.leftQueryForm.pageNum = pageNum;
      this.leftQueryForm.pageSize = pageSize;
      this.leftPagination.pageSize = pageSize;
      this.leftPagination.current = pageNum;
      this.getdfphxrApiData();
    },

    // 切换页码 左
    onChangeLeft(pageNum, pageSize) {
      this.leftQueryForm.pageNum = pageNum;
      this.leftPagination.current = pageNum;
      this.getdfphxrApiData();
    },
    // 接受完成
    handleComplete() { },
    // 单位选择保存
    selectElectionFormOk() {
      let form = {};
      form.formData = {};
      form.formData.fpdbtFbs = [];
      //开始右侧没有数据
      if (this.rightNowData.length == 0) {
        console.log(
          "🤗🤗🤗, this.selectElectionForm.dbtDm =>",
          this.selectElectionForm.dbtDm
        );
        this.rightData.forEach((item) => {
          form.formData.fpdbtFbs.push({ dbId: item.DBID });
        });
        form.formData.jcDm = { jcDm: this.leftQueryForm.jcDm };
        form.dbtDm = this.selectElectionForm.dbtDm.key;
        fpdbtsCreate(form).then((res) => {
          if (res.data.code == "0000") {
            this.upDataForm = res.data.data;
            // 设置值
            this.procInstId = res.data.data.procInstId;
            this.ids = [res.data.data.id];
            this.rightData.map((item) => {
              item.DBTMC = "";
              item.DBTMC = this.selectElectionForm.dbtDm.label;
            });
            if (this.rightNowData.length > 1) {
              this.rightNowData = this.rightNowData.concat(this.rightData);
            } else {
              this.rightNowData = this.rightData;
            }
            let rightKey = this.rightData.map((item) => item.DBID);
            this.leftNowData = this.leftNowData.filter((item) => {
              if (!rightKey.includes(item.DBID)) {
                return item;
              }
            });
            this.getdfphxrApiData();
            this.selectElectionFormCancel();
          }
        });
      } else {
        form.dbtDm = this.selectElectionForm.dbtDm.key;
        // 有数据调这个
        form.formData.dqzt = this.upDataForm.dqzt;
        this.rightData.forEach((item) => {
          form.formData.fpdbtFbs.push({ dbId: item.DBID });
        });
        form.formData.id = this.upDataForm.id;
        form.formData.jcDm = this.upDataForm.jcDm;
        form.formData.procInstId = this.upDataForm.procInstId;
        form.formData.yxbz = this.upDataForm.yxbz;
        form.formData.zsbmId = "";
        form.formData.zsjgId = "";
        fpdbtsUpdate(form).then((res) => {
          if (res.data.code == "0000") {
            // 设置值
            this.procInstId = res.data.data.procInstId;
            this.ids = [res.data.data.id];
            this.rightData.map((item) => {
              item.DBTMC = "";
              item.DBTMC = this.selectElectionForm.dbtDm.label;
            });
            if (this.rightNowData.length > 0) {
              this.rightNowData = this.rightNowData.concat(this.rightData);
            } else {
              this.rightNowData = this.rightData;
            }
            let rightKey = this.rightData.map((item) => item.DBID);
            this.leftNowData = this.leftNowData.filter((item) => {
              if (!rightKey.includes(item.DBID)) {
                return item;
              }
            });
            this.getdfphxrApiData();
            this.selectedLeftRowKeys = [];
            this.selectElectionFormCancel();
          }
        });
      }
      // this.selectedRowLeftKeys = [];
      // this.selectedRightRowKeys = [];
      // this.getyfphxrApiData();
    },
    // 单位选择关闭
    selectElectionFormCancel() {
      this.selectElectionVisible = false;
      this.selectElectionForm = { name: "" };
    },
    // 右移
    toRight() {
      this.selectElectionVisible = true;
    },
    // 左移
    toLeft(row) {
      if (this.leftNowData.length > 1) {
        this.leftNowData = this.leftData.concat(this.leftNowData);
      } else {
        this.leftNowData = this.leftData;
      }
      let leftKey = this.leftData.map((item) => item.DBID);
      this.rightNowData = this.rightNowData.filter((item) => {
        if (!leftKey.includes(item.DBID)) {
          return item;
        }
      });
      this.selectedRowLeftKeys = [];
      this.selectedRightRowKeys = [];
      this.getdfphxrApiData();
    },
    // 保存
    unitTableOk() {
      this.$refs.assignmentDelegationShip.visible = true;
      // this.jointTableVisible = false;
      // Object.assign(this.$data, this.$options.data());
    },
    // 选择
    onLeftSelect(record, selected, selectedRows, nativeEvent) {
      if (selected) {
        this.rightData = selectedRows;
      } else {
        // 取消选中
        this.rightData = this.rightData.filter(
          (item) => item.DBID != record.DBID
        );
      }
      this.selectedRowLeftKeys = selectedRows.map((item) => item.DBID);
    },
    // 选择
    onRightSelect(record, selected, selectedRows, nativeEvent) {
      if (selected) {
        this.leftData = selectedRows;
      } else {
        // 取消选中
        this.leftData = this.leftData.filter(
          (item) => item.DBID != record.DBID
        );
      }
      this.selectedRightRowKeys = selectedRows.map((item) => item.DBID);
    },
    // 关闭
    close() {
      this.jointTableVisible = false;
      this.Steps = 0;
      Object.assign(this.$data, this.$options.data());
    },
    //代表团
    getDelegationData() {
      getDelegation().then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.code == "0000") {
          this.unitList = res.data;
        }
      });
    },
    // 获取数据
    getDeputyListData() {
      //获取届次下拉框
      getLevelListApi().then((res) => {
        if (res.data.code === "0000") {
          this.periods = res.data.data;
          this.leftQueryForm.jcDm = this.periods[0].jcDm; //改过
          this.rightQueryForm.jcDm = this.periods[0].jcDm;//改过
          this.getdfphxrApiData();
          this.getyfphxrApiData();
        }
      });
    },
    reset(type) {
      if (type == "left") {
        this.leftQueryForm = {
          pageSize: 10,
          pageNum: 1,
          jcDm: this.periods[0].jcDm,
          dbtDm: "all",
        };
        this.getdfphxrApiData();
      } else {
        this.rightQueryForm = {
          pageSize: 10,
          pageNum: 1,
          dbtDm: "",
          jcDm: this.periods[0].jcDm,
        };
        this.getyfphxrApiData();
      }
    },
    // 获取左边数据
    getdfphxrApiData() {
      sdbAndDbtbList(this.leftQueryForm).then((res) => {
        if (res.data.code == 200) {
          this.leftNowData = res.data.rows;
          this.leftPagination.total = res.data.total;
        }
      });
    },
    // 获取右边数据
    getyfphxrApiData() {
      let form = Object.assign({}, this.rightQueryForm);
      form.dbtDm = "";
      form.fpbId = "";
      newDbtdbList(form).then((res) => {
        if (res.data.code == 200) {
          this.rightNowData = res.data.rows;
        }
      });
    },
  },
};
</script>
<style lang="css" scoped>
.ant-modal-content .changeBtn {
  text-align: center;
  margin-top: 300%;
}
</style>
