import request from "@/utils/requestTemp";
import { instance_1 } from "@/api/axiosRq";
import qs from "qs";

export function getAppraisePage(params, data) {
  return request({
    url: "/appraise/form/getPage",
    method: "post",
    params: params,
    data: data,
  });
}

export function getAppraiseList(data) {
  return request({
      url: "/appraise/form/getList",
      method: "post",
      data: data,
  });
}
// 获取评分细则列表
export function getAppraiseSmallList(params, data) {
  return request({
      url: "/appraise/small/getPage",
      method: "post",
      params: params,
      data: data,
  });
}

export function getSmallItemById(params) {
  return request({
      url: "/appraise/small/getById",
      method: "post",
      params: params,
  });
}

export function updateSmallItemById(data) {
  return request({
      url: "/appraise/small/update",
      method: "post",
      data: data,
  });
}

export function addSmallItemById(data) {
  return request({
      url: "/appraise/small/save",
      method: "post",
      data: data,
  });
}

export function getLargeItemPage(data) {
  return request({
      url: "/appraise/large/getPage",
      method: "post",
      data: data,
  });
}

export function saveLargeItem(data) {
  return request({
      url: "/appraise/large/save",
      method: "post",
      data: data,
  });
}

export function updateLargeItem(data) {
  return request({
      url: "/appraise/large/update",
      method: "post",
      data: data,
  });
}

export function getLargeItemById(params) {
  return request({
      url: "/appraise/large/getById",
      method: "post",
      params: params,
  });
}

export function removeLargeItem(params) {
  return request({
      url: "/appraise/large/remove",
      method: "post",
      params: params,
  });
}

export function removeSmallItem(params) {
  return request({
      url: "/appraise/small/remove",
      method: "post",
      params: params,
  });
}

// --------------------------------手动录入积分项--------------------------------
export function getCustomAppraiseListVo(params) {
  return request({
    url: "/customAppraise/listVo",
    method: "get",
    params: params,
  });
}

export function getCustomAppraiseItemListVo(params) {
  return request({
    url: "/customAppraise/listItemVo",
    method: "get",
    params: params,
  });
}

export function getCustomAppraiseById(params) {
  return request({
    url: "/customAppraise/getById",
    method: "get",
    params: params,
  });
}

export function createCustomAppraise(data) {
  return request({
    url: "/customAppraise/create",
    method: "post",
    data: data,
  });
}

export function updateCustomAppraise(data) {
  return request({
    url: "/customAppraise/update",
    method: "post",
    data: data,
  });
}

export function addItem(data) {
  return request({
    url: "/customAppraise/addItem",
    method: "post",
    data: data,
  });
}

export function downloadResumptionWeekUserRecordExcelTemplete(params) {
  return instance_1({
    url: "/resumptionWeekUserRecord/downloadTemplate",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function importResumptionWeekUserRecordByExcel(data) {
  return instance_1({
    url: "/resumptionWeekUserRecord/importByExcel",
    method: "post",
    data: data,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}

export function exportResumptionWeekUserRecordExcel(params) {
  return instance_1({
    url: `/resumptionWeekUserRecord/export`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function deleteCustomAppraiseById(params) {
  return request({
    url: "/customAppraise/deleteById",
    method: "post",
    params: params
  });
}

export function deleteCustomAppraiseItemById(params) {
  return request({
    url: "/customAppraise/deleteItemById",
    method: "post",
    params: params
  });
}

export function importResumptionWeekUserRecordByWord(data) {
  return instance_1({
    url: "/resumptionWeekUserRecord/importByWord",
    method: "post",
    data: data,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}

export function insertResumptionWeekUserRecordByWord(data) {
  return request({
    url: "/resumptionWeekUserRecord/insertByWord",
    method: "post",
    data: data,
  });
}

// --------------------------------手动录入积分项--------------------------------
export function getDmCustomAppraise(params) {
  return request({
    url: "/customAppraise/getDmCustomAppraise",
    method: "get",
    params: params,
  });
}


export function updateScoreByYear(params) {
  return request({
    url: "/appraise/calc/updateScoreByYear",
    method: "post",
    params: params,
  });
}

export function getDetailListBySmallItem(params) {
  return request({
    url: "/appraise/calc/getDetailListBySmallItem",
    method: "post",
    params: params,
  });
}

export function updateScoreByYearByUserId(params) {
  return request({
    url: "/appraise/calc/updateScoreByYearByUserId",
    method: "post",
    params: params,
  });
}

export function getRangeSettingPage(params, data) {
  return request({
    url: "/appraise/rangeSetting/getPage",
    method: "post",
    params: params,
    data: data
  });
}

export function getRangeSettingList(data) {
  return request({
    url: "/appraise/rangeSetting/getList",
    method: "post",
    data: data
  });
}

export function addRangeSetting(data) {
  return request({
      url: "/appraise/rangeSetting/create",
      method: "post",
      data: data,
  });
}

export function updateRangeSetting(data) {
  return request({
      url: "/appraise/rangeSetting/update",
      method: "post",
      data: data,
  });
}

export function getAppraiseRangeSettingById(params) {
  return request({
    url: "/appraise/rangeSetting/getById",
    method: "get",
    params: params,
  });
}

export function deleteRangeSettingById(params) {
  return request({
      url: "/appraise/rangeSetting/deleteById",
      method: "post",
      params: params,
  });
}

export function enableRangeSettingById(params) {
  return request({
      url: "/appraise/rangeSetting/enableById",
      method: "post",
      params: params,
  });
}

export function getPageByCurrentUser(data) {
  return request({
      url: "/appraise/form/getPageByCurrentUser",
      method: "post",
      data: data,
  });
}


export function getRankByUserId(data) {
  return request({
      url: "/appraise/form/getRankByUserId",
      method: "post",
      data: data,
  });
}

export function editItemDetails(data,type) {
  return request({
      url: "/appraise/form/editItemDetails?type="+type,
      method: "post",
      data: data,
  });
}
