<template>
  <div>
    <a-modal title="候选人确认表"
             :visible.sync="visible"
             width="96%"
             @cancel="close">
      <a-row class="steps">
        <a-steps :current="Steps">
          <a-step title="第一步：确认"></a-step>
          <a-step title="第二步：初审" />
          <a-step title="第三步：终审" />
          <a-step title="第四步：确认表审核完成" />
        </a-steps>
      </a-row>
      <a-row class="formBox">
        <a-transfer :data-source="mockData"
                    :target-keys="targetKeys"
                    :show-select-all="false"
                    @change="onChange"
                    @selectChange="handleScroll">
          <template slot="children"
                    slot-scope="{props: { direction, filteredItems, selectedKeys },
                      on: { itemSelectAll, itemSelect },          }">
            <a-form v-if="direction === 'left'"
                    layout="inline"
                    :form="leftForm"
                    :label-col="{ span: 4 }"
                    :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col span="12">
                  <a-form-item label="届次"
                               style="width: 100%">
                    <a-select v-model="leftForm.jcDm"
                              allow-clear
                              placeholder="请选择届次"
                              style="width:100%">
                      <a-select-option v-for="item in periods"
                                       :key="item.jcDm"
                                       :value="item.jcDm">{{
                                                        item.levelName
                                                }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item label="姓名"
                               style="width: 100%">
                    <a-input v-model="leftForm.userName"
                             allow-clear
                             placeholder="请输入姓名"
                             v-on:keyup.enter="search('left')">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item label="推荐单位"
                               style="width: 100%">
                    <a-input v-model="leftForm.xmmelymc"
                             allow-clear
                             placeholder="请输入推荐单位"
                             v-on:keyup.enter="search('left')">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <span>
                    <a-button type="primary"
                              @click="search('left')">搜索</a-button>
                    <a-button style="margin-left: 12px;"
                              @click="reset('left')"
                              class="pinkBoutton">
                      重置</a-button>
                  </span>
                </a-col>
              </a-row>

            </a-form>
            <a-form v-if="direction === 'right'"
                    layout="inline"
                    :form="rightForm"
                    :label-col="{ span: 4 }"
                    :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col span="12">
                  <a-form-item label="届次"
                               style="width: 100%">
                    <a-select v-model="rightForm.jcDm"
                              allow-clear
                              placeholder="请选择届次"
                              style="width:100%">
                      <a-select-option v-for="item in periods"
                                       :key="item.jcDm"
                                       :value="item.jcDm">{{
                                                        item.levelName
                                                }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item label="姓名"
                               style="width: 100%">
                    <a-input v-model="rightForm.userName"
                             allow-clear
                             placeholder="请输入姓名"
                             v-on:keyup.enter="search('right')">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item label="推荐单位"
                               style="width: 100%">
                    <a-input v-model="rightForm.xmmelymc"
                             allow-clear
                             placeholder="请输入推荐单位"
                             v-on:keyup.enter="search('right')">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <span>
                    <a-button type="primary"
                              @click="search('right')">搜索</a-button>
                    <a-button style="margin-left: 12px;"
                              @click="reset('right')"
                              class="pinkBoutton">
                      重置</a-button>
                  </span>
                </a-col>
              </a-row>

            </a-form>
            <!-- :pagination="pagination" -->
            <a-table style="height: 400px;"
                     :row-selection="
                            getRowSelection({
                                selectedKeys,
                                itemSelectAll,
                                itemSelect,
                                filteredItems,
                            })
                        "
                     rowKey="key"
                     :columns="direction === 'left' ? rightColumns : leftColumns"
                     :data-source="filteredItems"
                     :pagination="direction === 'left' ? leftPagination : rightPagination"
                     size="small"
                     :scroll="{ x: '30%', y: 400 }"></a-table>
          </template>
        </a-transfer>
      </a-row>
      <a-row slot="footer">
        <a-col>
          <a-space>
            <!-- <a-button @click="save">保存</a-button> -->
            <a-button @click="sShen">
              <a-icon type="check" />送审
            </a-button>
            <a-button @click="close">退出</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-modal>
    <!-- 流程弹出页 -->
    <submitForCensorship ref="submitForCensorship"
                         :procInstId="procInstId"
                         @complete="handleComplete"
                         :ids="ids">
    </submitForCensorship>
  </div>
</template>
<script>
// 用于获取差异化后的新数组
import { myPagination } from "@/mixins/pagination.js";
import { gethxrqrbscompleteApi } from "@/api/xjgw";
import submitForCensorship from '@/views/common/submitForCensorship';
import difference from "lodash/difference";
import {
  getCandidateConfirmEditApi,
  getWaitConfirmEditApi,
  getLevelListApi,
  getSavetoApi,
  candidateConfirmation,
  getAWater,
  getViewDqrhxr,
  cancelToConfirmCandidates,
  getfindByIdupdateApi,
  gethxrqrbsupdateApi,
} from "@/api/representativeElection/candidateApi.js";
import { log } from "@antv/g2plot/lib/utils";
export default {
  components: { submitForCensorship },
  // 引入分页器配置
  mixins: [myPagination],
  props: {
    neWid: {
      type: String,
    },
  },
  data () {
    return {
      Steps: 0,
      currStateDm: "",
      newIDs: this.neWid,
      inids: "",
      inprocInstId: "",
      procInstId: '',
      ids: [],
      visible: false,
      mockData: [],
      newData: [],
      // 届别
      periods: [],
      //选择框
      xzkuang: [],
      //右表单
      rightForm: {
        jcDm: 2,
        xmmelymc: "",
        userName: "",
        pageSize: 10,
        pageNum: 1,
      },
      // 左表单
      leftForm: {
        pageSize: 10,
        pageNum: 1,
        state: "21",
        jcDm: 2,
      },

      leftColumns: [
        {
          title: "预备人选",
          align: "center",
          ellipsis: true,
          dataIndex: "USER_NAME",
          width: 76,
        },
        {
          title: "性别",
          align: "center",
          ellipsis: true,
          dataIndex: "SEX",
          width: 46,
        },
        {
          title: "出生日期",
          align: "center",
          ellipsis: true,
          width: 76,
          dataIndex: "BIRTHDAY",
          customRender: (text, record, index) => {
            return text.slice(0, text.indexOf("T")) || '/';
          }
        },
        {
          title: "工作单位及职务",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "GZDWJZW",
        },
        {
          title: "职业构成",
          align: "center",
          ellipsis: true,
          width: 100,
          dataIndex: "ZYGCMC",
        },
        {
          title: "推荐单位",
          align: "center",
          ellipsis: true,
          width: 100,
          dataIndex: "MELYMC",
        },
      ],
      rightColumns: [
        {
          title: "待确定候选人",
          align: "center",
          ellipsis: true,
          width: 100,
          dataIndex: "USER_NAME",
        },
        {
          title: "性别",
          align: "center",
          ellipsis: true,
          width: 60,
          dataIndex: "SEX",
        },
        {
          title: "出生日期",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "BIRTHDAY",
        },
        {
          title: "工作单位及职务",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "GZDWJZW",
        },
        {
          title: "职业构成",
          align: "center",
          ellipsis: true,
          width: 100,
          dataIndex: "ZYGCMC",
        },
        {
          title: "推荐单位",
          align: "center",
          ellipsis: true,
          width: 100,
          dataIndex: "MELYMC",
        },
      ],
      targetKeys: [], // 选中到右边的数据
      rightChangeData: [],
      // 保存成功后返回数据
      returnData: {},
      // 左分页器设置
      leftPagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize, "left"), // 改变每页数量时更新显示
        onChange: this.leftHandleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "small",
      },
      // 右分页器设置
      rightPagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize, "right"), // 改变每页数量时更新显示
        onChange: this.rightHandleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created () {
    // this.getfindByIdupdateApi()
    this.shenhe()
  },

  methods: {
    // 切换页数
    changePageSize (pageNum, pageSize, type) {
      this[type + "Form"].pageNum = pageNum;
      this[type + "Form"].pageSize = pageSize;
      this[type + "Pagination"].pageSize = pageSize;
      this[type + "Pagination"].current = pageNum;
      this[type + "FetchData"]();
    },
    // 切换页码-左
    leftHandleCurrentChange (pageNum, pageSize) {
      this.leftForm.pageNum = pageNum;
      this.leftPagination.current = pageNum;
      this.leftFetchData();
    },
    // 切换页码-右
    rightHandleCurrentChange (pageNum, pageSize) {
      this.rightForm.pageNum = pageNum;
      this.rightPagination.current = pageNum;
      this.rightFetchData();
    },
    handleScroll (e) {
      // this.xzkuang = e
      // console.log(this.xzkuang, 789789);
    },

    //送审
    sShen () {
      this.$refs.submitForCensorship.visible = true
    },


    //审核
    shenhe () {
      if (this.visible == true) {
        if (this.newIDs != '') {
          let qrbId = this.newIDs
          getAWater(qrbId).then(res => {
            if (res.data.code == '0000') {
              console.log(res.data.data, 'wwwwwww');
              this.procInstId = res.data.data.procInstId
              this.ids.push(res.data.data.id)
              this.inids = res.data.data.id
              this.inprocInstId = res.data.data.procInstId
              this.currStateDm = res.data.data.currentState.currStateDm;

              switch (res.data.data.currentState.currStateDm) {
                case '11':
                  this.Steps = 0
                  break;
                case '21':
                  this.Steps = 2
                  break;
                case '31':
                  this.Steps = 3
                  break;
                case '41':
                  this.Steps = 4
                  break;
                case '51':
                  this.Steps = 4
                  break;
              }
              // this.search('right');
            }
          })
        }
      }
    },
    // 保存
    save () {
      if (this.rightChangeData.length == 0) {
        return this.$message.error("请选择数据");
      }
      // 调save接口
      let jc = {
        jcDm: this.leftForm.jcDm,
      };
      //保存到待确定
      let form = {};
      let hxrqrbFbs = [];
      console.log(Array.from(new Set(this.newData)));

      this.rightChangeData.forEach((item) => {
        console.log(item, 'item');
        hxrqrbFbs.push({
          userId: item.key,
          qrbId: this.newIDs,
        });
      });
      form.hxrqrbFbs = hxrqrbFbs;
      // let id = this.inids
      // let procInstId = this.inprocInstId
      // let id = this.inids
      let procInstId = this.inprocInstId
      let id = this.newIDs
      let currentState = {
        currStateDm: this.currStateDm
      }
      let params = {
        // id,
        // procInstId,
        id,
        jc,
        procInstId,
        currentState,
        hxrqrbFbs,
        state: this.leftForm.state,
      };
      gethxrqrbsupdateApi(id, params).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.data.code == '0000') {
          // this.procInstId = res.data.data.procInstId
          // this.ids.push(res.data.data.id)
          this.visible = false
          this.$emit('handleClearId')
          this.$message.success("保存成功");
        } else {
          this.$message.error(res.data.msg);
        }
        // 保存成功 保存数据
        // this.returnData = res.data.data;
      });
    },
    // 保存
    saveRight (dataList) {
      if (this.rightChangeData.length == 0) {
        return this.$message.error("请选择数据");
      }
      // 调save接口
      let jc = {
        jcDm: this.leftForm.jcDm,
      };
      //保存到待确定
      let form = {};
      let hxrqrbFbs = [];
      console.log(Array.from(new Set(this.newData)));

      this.rightChangeData.forEach((item) => {
        hxrqrbFbs.push({
          userId: item.key,
          qrbId: this.newIDs,
        });
      });
      form.hxrqrbFbs = hxrqrbFbs;
      let procInstId = this.inprocInstId
      let id = this.newIDs
      let currentState = {
        currStateDm: this.currStateDm
      }

      let addlist = [];
      dataList.map((i) => {
        addlist.push({
          userId: i,
          qrbId: this.newIDs,
        })
      })
      for (let i = 0; i < addlist.length; i++) {
        gethxrqrbsupdateApi(id, {
          id,
          jc,
          procInstId,
          currentState,
          hxrqrbFbs: [addlist[i]],
          state: this.leftForm.state,
        }).then((res) => {
          if (res.data.code == '0000' && i == addlist.length - 1) {
            // this.visible = false
            this.$emit('handleClearId')
            this.$message.success("保存成功");
          } else if (res.data.code != '0000') {
            this.$message.error(res.data.msg);
            return
          }
        })
      }

      // gethxrqrbsupdateApi(id, params).then((res) => {
      //   console.log("🤗🤗🤗, res =>", res);
      //   if (res.data.code == '0000') {
      //     // this.procInstId = res.data.data.procInstId
      //     // this.ids.push(res.data.data.id)
      //     this.visible = false
      //     this.$emit('handleClearId')
      //     this.$message.success("保存成功");
      //   } else {
      //     this.$message.error(res.data.msg);
      //   }
      //   // 保存成功 保存数据
      //   // this.returnData = res.data.data;
      // });
    },
    //审核保存发送
    handleComplete (data) {
      // , { "tzlx": 0 }
      gethxrqrbscompleteApi(data).then(res => {
        if (res.data.code == '0000') {
          this.$emit('handleClearId')
          this.visible = false
          this.ids = []
          this.$refs.submitForCensorship.successComplete()
          this.$message.success(res.data.msg)
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    // 搜索
    search (type) {
      if (type == "left") {
        this.leftFetchData();
      } else {
        this.rightFetchData();
      }
    },
    reset (type) {
      if (type == "left") {
        this.leftForm = {
          pageSize: 10,
          pageNum: 1,
          jcDm: this.periods[0].jcDm,
        };
        this.leftFetchData();
      } else {
        this.rightForm = {
          pageSize: 10,
          pageNum: 1,
          jcDm: this.periods[0].jcDm,
        };
        this.rightFetchData();
      }
    },
    // 穿梭框选择
    onChange (targetKeys, direction, moveKeys) {
      console.log(direction, 789, moveKeys, 'targetKeys', targetKeys,);
      this.$set(this, "targetKeys", targetKeys);
      // return;
      if (direction == "left") {
        this.leftPagination.total = this.leftPagination.total + moveKeys.length;
        console.log("右到左", this.mockData, moveKeys);
        let movelist = [...moveKeys]
        let bianlist = [];
        for (let i = 0; i < movelist.length; i++) {
          this.mockData.map((n, m) => {
            if (n.key == movelist[i] && n.YXnum) {
              bianlist.push(n);
              return
            }
          })
        }
        if (bianlist.length > 0) {
          bianlist.map((i) => {
            // console.log(i, '左移动数据 ')
            cancelToConfirmCandidates(this.newIDs, {
              jc: { jcDm: this.leftForm.jcDm, }, hxrqrbFbs: [{ qrbId: this.newIDs, userId: i.key }]
            }).then((res) => {
              console.log("🤗🤗🤗,  res =>", res);
            });
          })
        }

        // 调save接口
      } else {
        // 右边有数据调save接口
        if (this.rightChangeData.length == 0) {
          // 调save接口
          let jc = {
            jcDm: this.leftForm.jcDm,
          };
          //保存到待确定
          let form = {};
          let hxrqrbFbs = [];
          moveKeys.forEach((element) => {
            hxrqrbFbs.push({
              userId: element,
            });
          });
          form.hxrqrbFbs = hxrqrbFbs;
          form.jc = jc;
          // candidateConfirmation(form).then((res) => {
          //   console.log("🤗🤗🤗, res =>", res);
          //   // 保存成功 保存数据
          //   this.returnData = res.data.data;
          // });
        } else {
          // 右边没有数据 调获取流水号接口
          let jc = {
            jcDm: this.leftForm.jcDm,
          };
          //保存到待确定
          let form = {};
          let hxrqrbFbs = [];
          // let newData = this.rightChangeData.concat(moveKeys);
          moveKeys.forEach((element) => {
            hxrqrbFbs.push({
              dbId: element,
            });
          });
          form.hxrqrbFbs = hxrqrbFbs;
          form.jc = jc;
          // form.id = this.returnData.id;
          form.dqzt = this.returnData.dqzt;
          form.yxbz = this.returnData.yxbz;
          form.procInstId = this.returnData.procInstId;
          console.log("🤗🤗🤗, 调获取流水号接口🤗🤗🤗 =>", form);
          // getAWater(form).then((res) => {
          //   console.log("调获取流水号接口🤗🤗🤗, res =>", res);
          // });
        }
        this.leftPagination.total = this.leftPagination.total - moveKeys.length;
        console.log("左到右");
        setTimeout(() => {
          this.saveRight(moveKeys);
        }, 100);
      }
    },
    // 列表选择
    getRowSelection ({
      selectedKeys,
      itemSelectAll,
      itemSelect,
      filteredItems,
    }) {
      this.rightChangeData = filteredItems;
      return {
        onSelectAll (selected, selectedRows) {
          console.log(selected, 123);
          // 选中的数据
          const treeSelectedKeys = selectedRows.map(({ key }) => key);
          const diffKeys = selected
            ? difference(treeSelectedKeys, selectedKeys)
            : difference(selectedKeys, treeSelectedKeys);
          itemSelectAll(diffKeys, selected);
        },
        onSelect ({ key }, selected) {
          itemSelect(key, selected);
        },
        selectedRowKeys: selectedKeys,
        onChange: this.onChangeKey,
      };
    },
    onChangeKey (key, data, index) {
      let arr = []
      data.forEach(item => {
        this.rightChangeData.forEach(right => {
          if (item.DB_ID == right.DB_ID) {
            arr.push(item)
          }
        })
      })
      this.newData = arr
      // if(key.length!=="0"){
      //   this.rightChangeData.forEach(item=>{
      //          key.forEach(key=>{
      //           if(item &&item.DB_ID==key){
      //             if (this.newData.length=="0") {
      //                 this.newData.push(item)
      //             }else{
      //                this.newData.forEach((item1,index)=>{
      //                 if(item1.DB_ID==item.DB_ID){
      //                      console.log("删除");
      //                    this.newData= this.newData.splice(index,1)
      //                 }else{
      //                       console.log("新增");
      //                     this.newData.push(item)
      //                 }
      //               })
      //             }
      //         }
      //     })
      //   })
      // }

    },
    // 获取数据
    leftFetchData () {
      getCandidateConfirmEditApi(this.leftForm).then((res) => {
        this.mockData = [];
        if (res.data.code == 200) {
          res.data.rows.forEach((element) => {
            // console.log(element);
            element.key = element.DB_ID;
            // element.key = element.MELY_DM;

          });
          this.mockData = this.mockData.concat(res.data.rows);
          this.leftPagination.total = res.data.total;
          if (res.data.rows.length == 10) {
            // this.leftForm.pageSize++;
            // this.leftFetchData();
          }
        }
      });
      console.log(this.mockData);
    },
    // 获取右边数据源
    rightFetchData () {
      let qrbId = this.newIDs
      let params = {
        jcDm: this.rightForm.jcDm,
        melymc: this.rightForm.melymc,
        userName: this.rightForm.userName,
        qrbId,
        pageSize: this.rightForm.pageSize,
        pageNum: this.rightForm.pageNum,
      }

      getViewDqrhxr(params).then((res) => {
        console.log("res", res);
        this.rightData = [];
        if (res.data.code == 200 && res.data.rows.length > 0) {
          res.data.rows.map((item) => {
            item.sex = item.SEX;
            item.user_name = item.USER_NAME;
            item.key = item.USER_ID;
            item.YXnum = true;//属于已经保存过的右边数据
          });
          this.rightData = res.data.rows;
          //分页
          // this.pagination.total = res.data.total;
          this.filteredItems = res.data.rows;
          // 合并
          // let newArr = this.mockData.concat(this.rightData);
          let newArr = this.rightData;
          this.mockData = this.mockData.concat(newArr);
          let RightKey = this.rightData.map((i) => i.USER_ID);
          this.$set(this, "targetKeys", RightKey);
          this.rightChangeData = newArr;
          this.$forceUpdate()
          this.rightPagination.total = res.data.total;
          console.log(this.targetKeys, "🤗🤗🤗, this.mockData =>", this.mockData);
        }
      });
    },

    //获取当前届次下拉数据列表
    async getLevelListFn () {
      const res = await getLevelListApi();
      // console.log("🤗🤗🤗, res.data =>", res.data);
      if (res.data.code === "0000") {
        this.periods = res.data.data;
        // this.fetchData();
        this.leftForm.jcDm = this.periods[0].jcDm;
        this.rightForm.jcDm = this.periods[0].jcDm;
        // this.leftFetchData();
        // this.$forceUpdadta();
      }
    },

    // 关闭
    close () {
      this.visible = false;
      this.$emit('handleClearId');
      this.Steps = 0;
      Object.assign(this.$data, this.$options.data());
      this.ids = []
    },

  },
  //     watch: {
  //     visible(newVal) {
  //         console.log("🤗🤗🤗, newVal =>", newVal);
  //         if (newVal === true) {
  //             // 获取当前届次下拉数据列表
  //             this.getLevelListFn();
  //         }
  //     },

  // },
  //监听父组件传过来的值
  watch: {
    neWid: {
      immediate: true,    // 这句重要  立即执行handler里面的方法
      handler (val) {
        this.newIDs = this.neWid;
        console.log("aaa", this.newIDs)
        this.shenhe()
        this.getLevelListFn();
        this.leftFetchData();
        this.rightFetchData();//搜索 
        // this.getcandidateApplicationtsaveApi() 
        this.search('left')
        this.search('right')//
      }
    },
    visible (newVal) {
      console.log("🤗🤗🤗, newVal =>", newVal);
      if (newVal === true) {
        this.search('left')
        setTimeout(() => {
          this.search('right')//
        }, 100);
      } else {
        this.rightPagination.total = 0;
        this.leftPagination.total = 0;

      }
    }
  }
};
</script>
<style scoped>
.formBox {
  padding: 20px;
}
::v-deep .ant-pagination-item a {
  padding: 0 !important;
}
</style>
