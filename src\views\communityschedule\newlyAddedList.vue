<template>
  <div class="table-container">
    <SearchForm :value="queryForm" @onReset="reset" @onSearch="search">
      <template v-slot:topSearch>
        <DhJcCascade v-model="queryForm" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>
      </template>
      <template v-slot:moreSearch>
        <AdminStreetStationCascade v-model="queryForm" allow-clear/>

        <FormInput
          v-model="queryForm.userName"
          :label="'代表姓名'"
          allow-clear
          @enter="handleEnter"
        />

        <FormRangePicker v-model="queryForm" end-prop="endTime" start-prop="startTime" label="时间范围" allow-clear />

        <FormInput
          v-model="queryForm.contactName"
          :label="'联系人姓名'"
          allow-clear
          @enter="handleEnter"
        />
      </template>
    </SearchForm>

    <a-row style="margin: 5px 0px 10px 8px">
      <a-col :xs="28" :sm="28" :md="28" :lg="28" :xl="28">
        <a-row>
          <a-col :span="12" style="margin-bottom: 10px">
            <a-button
              type="primary"
              style="margin-left: 10px"
              @click="addSocialconditions()"
              >新增社情民意</a-button
            >
            <a-button
              type="primary"
              style="margin-left: 12px"
              :loading="downloadExcelLoading"
              @click="downloadExcel"
            >
              下载模版</a-button
            >

            <a-upload
              style="display: inline-block"
              name="file"
              accept=".xls"
              :multiple="true"
              :file-list="fileList"
              :before-upload="beforeUpload"
              @change="downImport"
            >
              <a-button type="primary" style="margin-left: 12px">
                导入</a-button
              >
            </a-upload>
            <a-button
              type="primary"
              style="margin-left: 12px"
              :loading="downloadPublicOpinionLoading"
              @click="downloadPublicOpinionExcel"
              >导出</a-button
            >
          </a-col>
          <a-col :span="12">
            <a-button
              type="primary"
              style="float: right"
              :loading="downloadLoading"
              @click="handeInstructions"
            >
              社情民意操作指引</a-button
            >
          </a-col>
        </a-row>

        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <!-- 有下拉的table需要  v-if="list.length > 0"  -->
          <a-table
            ref="table"
            :bordered="false"
            class="directorySet-table"
            size="small"
            :columns="columns"
            :pagination="pagination"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            :data-source="list"
            :row-key="
              (record, index) => {
                return index;
              }
            "
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import {
  addSocialConditionsExcel,
  downloadGuide,
  downloadSocialConditionsExcel,
  exportNew,
  OpinionControllerDel,
  OpinionControllerDetails,
  OpinionControllerList,
} from "@/api/communityschedule";
import SearchForm from "@/components/SearchForm/index";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import FormSelect from "@/components/FormSelect/index.vue";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import FormRangePicker from "@/components/FormRangePicker/index.vue";
import { DBLZ_DBJSQ } from "@/utils/enum/levelRoleMainIdentifyEnum";

export default {
  components: {
    FormRangePicker,
    FormInput,
    AdminStreetStationCascade,
    FormSelect,
    DhJcCascade,
    SearchForm,
  },
  filters: {},
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBJSQ,
      downloadPublicOpinionLoading: false,
      downloadExcelLoading: false,
      list: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        dhDm: undefined,
        jcDm: undefined,
        dbtId: undefined,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        userName: undefined,
        startTime: "",
        endTime: "",
        contactName: "",
      },
      listLoading: false,
      //list-----
      selectRows: [],
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 60,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "代表姓名",
          align: "center",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            let names = "";
            if (record.members) {
              // if (record.members.length > 2) {
              //   names =
              //     record.members[0].userName +
              //     "、" +
              //     record.members[1].userName +
              //     "...";
              // } else {

              // }
              record.members.forEach((item) => {
                names += item.userName + "、";
              });
              names = names.substring(0, names.length - 1);
            }
            return names || "/";
          },
        },
        {
          title: "联系的群众姓名",
          align: "center",
          width: 140,
          ellipsis: true,
          dataIndex: "voterName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "群众意见建议",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "content",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "办理进度",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "progressStatus",
          customRender: (text, record, index) => {
            if (text == 1) {
              text = "意见录入";
            } else if (text == 2) {
              text = "交办";
            } else if (text == 3) {
              text = "答复";
            } else if (text == 4) {
              text = "评价";
            }
            return text || "/";
          },
        },
        {
          title: "行政区划",
          align: "center",
          width: 110,
          ellipsis: true,
          dataIndex: "administrativeArea",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "乡镇街道",
          align: "center",
          width: 140,
          ellipsis: true,
          dataIndex: "streetTown",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站名称",
          align: "center",
          width: 320,
          ellipsis: true,
          dataIndex: "liaisonStation",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "录入日期",
          align: "center",
          width: 190,
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            return text.substring(0, 10) || "/";
          },
        },
        // {
        //   title: "记录人联系电话",
        //   align: "center",
        //   width: 200,
        //   ellipsis: true,
        //   dataIndex: "recorderMobile",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "所联系的群众姓名电话",
        //   align: "center",
        //   width: 150,
        //   ellipsis: true,
        //   dataIndex: "voterMobile",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "状态",
        //   align: "center",
        //   width: 250,
        //   ellipsis: true,
        //   dataIndex: "isCompleted",
        //   customRender: (text, record, index) => {
        //     if (text == 0) {
        //       text = "未办结";
        //     } else if (text == 1) {
        //       text = "已办结";
        //     }
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "记录人姓名",
        //   align: "center",
        //   width: 200,
        //   ellipsis: true,
        //   dataIndex: "recorder",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },

        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.details(record);
                    },
                  },
                },
                "详情"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.details(record, "1");
                    },
                  },
                },
                "修改"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.deleNewPublicOpinionController(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      fileList: [],
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      downloadLoading: false,
      searchDebounced: _.debounce(this.search, 500),
      isKeepAlive: false,
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "社情民意列表");
    this.queryForm.communityScheduleId = this.$route.query.communityScheduleId;

    this.fetchData();
  },
  activated() {
    if (this.isKeepAlive) {
      this.fetchData();
    } else {
      this.isKeepAlive = true;
    }
  },
  methods: {
    handleEnter() {
      this.searchDebounced();
    },
    // 搜索
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    // 重置
    reset(value) {
      this.queryForm = value;
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.pagination.current = 1;
      this.fetchData();
    },
    // 操作指引下载
    handeInstructions() {
      this.downloadLoading = true;
      downloadGuide().then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `社情民意操作指引.pdf`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000);
      });
      // // 预览指引pdf文件
      // let pdfUrl =
      //   Vue.prototype.GLOBAL.basePath_1 +
      //   "/newPublicOpinionController/downloadCZZN"+
      //   "&token=" +
      //   Vue.prototype.GLOBAL.token;
      // window.open(
      //   process.env.BASE_URL +
      //     "pdfjs/web/viewer.html?file=" +
      //     encodeURIComponent(pdfUrl)
      // );
    },
    // 下载模板
    downloadExcel() {
      this.downloadExcelLoading = true;
      downloadSocialConditionsExcel(this.queryForm).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `社情民意导入模板.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadExcelLoading = false;
        }, 1000);
      });
    },
    // 导出
    downloadPublicOpinionExcel() {
      if (this.selectRows.length == 0) {
        return this.$message.warning("请选择数据！");
      }

      let id = this.selectRows.map((item) => item.id).toString();
      this.downloadPublicOpinionLoading = true;
      exportNew(id).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        let fileName = "";
        if (this.selectRows.length > 1) {
          fileName = `社情民意群众代表登记表.zip`;
        } else {
          var name = null;
          this.selectRows[0].members.forEach((item, index) => {
            name ? (name += "、" + item.userName) : (name = item.userName);
            // name? name+=','+item.userName.replace('*',"x") :name=item.userName.replace('*',"x");
          });
          fileName = name.replace(/\*/g, "x") + `群众代表登记表.docx`;
        }
        a.download = fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadPublicOpinionLoading = false;
        }, 1000);
      });
    },
    // 上传文件限制 直接return
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },

    //导入社情民意表
    downImport({ file, fileList }) {
      // console.log(file);
      var data = new FormData();
      var params = { communityScheduleId: this.queryForm.communityScheduleId };
      data.append("file", file);
      addSocialConditionsExcel(params, data).then((res) => {
        console.log(res, "res");
        if (res.data.code == "500") {
          return this.$message.error(res.data.msg);
        }
        if (res.data.code == "0000") {
          this.$baseMessage("上传成功", "success");
          this.fetchData();
        }
      });
    },
    //新增社情民意
    addSocialconditions() {
      this.$router.push({
        path: "/community/activityScheduleTupleList",
        query: {
          communityScheduleId: this.queryForm.communityScheduleId,
        },
      });
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    /**
     * 预加载数据
     */

    //列表
    fetchData() {
      this.listLoading = true;
      OpinionControllerList(this.queryForm).then((response) => {
        //   console.log(response,"列表数据")
        this.list = response.rows;
        this.total = response.total;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    details(res, to_type) {
      //  详情数据
      OpinionControllerDetails({ id: res.id }).then((response) => {
        this.$router.push({
          path: "/community/newlyAdded",
          query: {
            data: response.data,
            to_type,
            communityScheduleId: this.queryForm.communityScheduleId,
          },
        });
      });
    },
    //    删除
    async deleNewPublicOpinionController(res) {
      await this.$baseConfirm("确认要删除吗?", null, () => {
        OpinionControllerDel({ id: res.id }).then((response) => {
          if (response.code == "0000") {
            this.$message.success("删除成功");
            this.fetchData();
          }
        });
      });
    },
  },
};
</script>
