<template>
  <div class="table-container">
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      :element-loading-text="elementLoadingText"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="序号" width="100">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column label="届次" width="100">
        <template slot-scope="scope">
          {{ scope.row.session }}
        </template>
      </el-table-column>
      <el-table-column label="年度" width="180">
        <template slot-scope="scope">
          {{ scope.row.year }}
        </template>
      </el-table-column>
      <el-table-column label="标题">
        <template slot-scope="scope">
          {{ scope.row.title }}
        </template>
      </el-table-column>
      <el-table-column label="受邀代表" width="120">
        <template slot-scope="scope">
          {{ scope.row.committeeMemberName }}
        </template>
      </el-table-column>
      <el-table-column label="受邀形式" width="180">
        <template slot-scope="scope">
          {{ scope.row.typeDesc }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :background="background"
      :current-page="page"
      :layout="layout"
      :page-size="size"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    ></el-pagination>
  </div>
</template>

<script>
import { listReady } from "@/api/communityschedule";

export default {
  data() {
    return {
      //list-----
      elementLoadingText: "正在加载...",
      list: [],
      listQuery: {},
      listLoading: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 10,
      background: true,
      //list-----
    }
  },
  created() {
    this.fetchData()
  },
  methods: {

    fetchData() {
      this.listLoading = true;
      listReady().then((response) => {
        this.list = response.data.records;
        this.listLoading = false;
        this.total = response.data.total;
      });
    },
    handleSizeChange(val) {
      this.listQuery.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.listQuery.current = val
      this.fetchData();
    },
  }
}
</script>

<style>
</style>