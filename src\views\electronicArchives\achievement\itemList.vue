<template>
    <div class="table-container">
      <a-row>
        <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <a-row>
            <a-form
              ref="form"
              :model="queryForm"
              layout="horizontal"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18, offset: 0 }"
            >
              <div>
                <a-row>
                  <a-col :span="6">
                    <a-form-item
                      label="代表名称"
                      :label-col="{ span: 6 }"
                      :wrapper-col="{ span: 18, offset: 0 }"
                    >
                      <a-input
                        v-model="queryForm.userName"
                        allow-clear
                        autocomplete="off"
                        placeholder="请输入代表姓名"
                        @keyup.enter="handleQuery"
                      ></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col :span="6" :offset="12">
                    <span style="float: right; margin-top: 3px">
                      <a-button type="primary" @click="handleQuery"
                        >搜索</a-button
                      >
                      <a-button
                        style="margin-left: 12px"
                        class="pinkBoutton"
                        @click="clearListQuery"
                        >重置</a-button
                      >
                    </span>
                  </a-col>
                </a-row>
                <a-row style="margin: 5px 0px 10px 8px">
                  <a-button
                    type="primary"
                    style="margin-right: 10px"
                    @click="addMembers()"
                    >新增代表</a-button
                  >
                </a-row>
              </div>
            </a-form>
          </a-row>
  
          <standard-table
            ref="noticeTable"
            :loading="listLoading"
            :selected-rows.sync="selectedRowKeys"
            @onChange="onSelectChange"
            rowKey="id"
            :pagination="pagination"
            :columns="columns"
            :data-source="dataSource"
          ></standard-table>
        </a-col>
      </a-row>

      <org-db-tree
        ref="identityRelAny"
        default-key="1"
        :levelRoleMainIdentify="levelRoleMainIdentify"
        @saveRel="onAttendMembersSubmit"
      ></org-db-tree>
    </div>
  </template>
  
  <script>
  import { 
    getAchievementItemListVo, 
    addAchievementItem,
    deleteAchievementItemById
  } from "@/api/electronicArchives.js";
  import { myPagination } from "@/mixins/pagination.js";
  import OrgDbTree from "@/components/OrgDbTree/index.vue";
  import { DBLZ_DBLZDJ } from '@/utils/enum/levelRoleMainIdentifyEnum'; // 导入配置文件
  export default {
    name: "Table",
    components: {
      OrgDbTree,
    },
    mixins: [myPagination],
    filters: {},
    data() {
      return {
        levelRoleMainIdentify : DBLZ_DBLZDJ,
        listLoading: true,
        dataSource: [],
        dateRange: [],
        selectedRowKeys: [],
        selectRows: [],
        advanced: false,
        loadings: false,
  
        indicator: <a-icon type="loading" style="font-size: 24px" spin />,
        queryForm: {
          startTime: "",
          endTime: "",
          pageNum: 1,
          pageSize: 10,
          title: "",
          content:"",
          status:"",
        },
        indexNum: 1,
        fileList: [],
        recordId: null,
        // 列表
        columns: [
          {
            fixed: "left",
            title: "序号",
            key: "index",
            align: "center",
            width: 10,
            ellipsis: true,
            customRender: (text, record, index) =>
              `${(this.indexNum - 1) * this.indexNum + index + 1}`,
          },
          {
            title: "代表团/机构",
            align: "center",
            width: 180,
            ellipsis: true,
            dataIndex: "orgNames",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "代表姓名",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "userName",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            fixed: "right",
            title: "操作",
            align: "center",
            width: 200,
            customRender: (text, record, index) => {
              let h = this.$createElement;
              return h("div", [
                h(
                  "span",
                  {
                    attrs: {
                      type: "text",
                    },
                    style: {
                      cursor: "pointer",
                      marginLeft: "14px",
                      color: "#DB3046",
                    },
                    on: {
                      click: () => {
                        this.delete(record.id);
                      },
                    },
                  },
                  "删除"
                ),
              ]);
            },
          },
        ],
      };
    },
    mounted() {
      let { id } = this.$route.query;
      this.recordId = id;
      this.fetchData();
    },
    created() {

    },
    methods: {
      // 多选选择
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys;
        this.selectRows = selectedRows;
      },
      onTimeChange(val) {
        this.queryForm.startTime = val[0];
        this.queryForm.endTime = val[1];
      },
      // 高级搜索
      toggleAdvanced() {
        this.advanced = !this.advanced;
      },
      // 重置
      clearListQuery() {
        this.queryForm = {
          pageNum: 1,
          pageSize: 10,
        };
        this.fetchData();
      },
      fetchData() {
        this.listLoading = true;
        this.queryForm.parentId = this.recordId
        getAchievementItemListVo(this.queryForm).then((res) => {
          this.dataSource = res.rows;
          this.listLoading = false;
          this.total = res.total;
          this.pagination.total = res.total;
        });
      },
      handleQuery() {
        this.fetchData();
      },
      handleCurrent(row) {
        console.log("row", row);
      },
      addMembers() {
        this.$refs["identityRelAny"] &&
        this.$refs["identityRelAny"].handleShow(
          "1",
          []
        );
      },
      add() {
        this.$router.push({
          path: "/grade/addCustomAppraiseItem",
        });
      },
      delete(id) {
        this.$confirm({
          cancelText: "取消",
          okType: "danger",
          okText: "确定",
          title: "确定删除",
          content: "是否确定删除？",
          onOk: () => {
            deleteAchievementItemById({id:id}).then((res) => {
              if (res.code == '0000') {
                this.$message.success("操作成功");
                this.fetchData();
              } else {
                this.$message.error(res.message);
              }
            });
          },
          onCancel: () => {
            this.$message.info("您已取消操作！");
          },
        });
      },
      handleCancel() {
        this.visibleMatchUserName = false
        this.fetchData()
      },
      onAttendMembersSubmit(type, checkedData) {
        //用户树选择赋值
        if (type == "1") {
          const list = checkedData
            .map((it) => ({
              userId: it.id,
              inviteId: it.uniqueId,
              dhDm: it.dhDm,
              jcDm: it.jcDm,
              orgId: it.orgId,
              deptId: it.deptId,
              postId: it.postId,
              orgName: it.orgName,
              deptName: it.deptName,
              postName: it.postName,
              name: it.name,
              userName: it.userName,
              sfDm: it.sfDm,
              committeeMemberTypeId: it.sfDm,
            }));
            let form = {
              id: this.recordId,
              attendMembers: list,
            };
            addAchievementItem(form).then((res) => {
              if (res.code == '0000') {
                this.$message.success("操作成功");
                this.fetchData();
              } else {
                this.$message.error(res.message);
              }
            });
        } 
      },
    },
  };
  </script>
  <style scoped>
  .code_btn {
    margin-bottom: 10px;
  }
  
  .p {
    display: flex;
    width: 100%;
  }
  
  .p > span {
    flex: 1;
  }
  </style>
  