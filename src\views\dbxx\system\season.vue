<template>
  <div class="signData table-container">
    <a-form layout="horizontal">
      <a-row>
        <a-col :md="6"
               :sm="24">
          <a-button style="margin: 10px;"
                    type="primary"
                    native-type="submit"
                    @click="handleAdd">新增</a-button>
        </a-col>

        <a-col :md="6"
               :sm="24">
          <a-form-item label="设备编号"
                       :labelCol="{ span: 6 }"
                       :wrapperCol="{ span: 18, offset: 0 }">
            <a-input placeholder="请输入设备编号"
                     allow-clear
                     v-on:keyup.enter="search"
                     v-model="queryForm.facilityNum"></a-input>
          </a-form-item>
        </a-col>
        <a-col :md="6"
               :sm="24">
          <a-form-item label="设备型号"
                       :labelCol="{ span: 6 }"
                       :wrapperCol="{ span: 18, offset: 0 }">
            <a-input placeholder="请输入设备型号"
                     allow-clear
                     v-on:keyup.enter="search"
                     v-model="queryForm.modelNumber"></a-input>
          </a-form-item>
        </a-col>
        <a-col :md="6"
               :sm="24">
          <span style="float: right; margin-top: 3px;">
            <a @click="toggleAdvanced">
              {{ advanced ? "收起" : "高级搜索" }}
              <a-icon :type="advanced ? 'up' : 'down'" />
            </a>
            <a-button type="primary"
                      style="margin-left: 12px;"
                      @click="search()">搜索</a-button>
            <a-button style="margin-left: 12px;"
                      @click="reset"
                      class="pinkBoutton">重置</a-button>

          </span>
        </a-col>

        <!-- <a-col :md="3"
               :sm="24">
          <span style="float: right; margin-top: 3px;">
            <a-button @click="search"
                      type="primary">搜索</a-button>
            <a-button style="margin-left: 8px;"
                      @click="reset"
                      class="pinkBoutton">重置</a-button>
          </span>
        </a-col> -->
      </a-row>
      <a-row style="margin-left:1%"
             v-if="advanced">
        <a-col :md="6"
               :sm="24">
          <a-form-item label="设备名称"
                       :labelCol="{ span: 6 }"
                       :wrapperCol="{ span: 18, offset: 0 }">
            <a-input placeholder="请输入设备名称"
                     allow-clear
                     v-on:keyup.enter="search"
                     v-model="queryForm.name"></a-input>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <standard-table :columns="columns"
                    :dataSource="list"
                    rowKey="id"
                    :loading="TBloading"
                    :pagination="pagination">
    </standard-table>

    <a-modal :title="title"
             destroyOnClose
             :visible.sync="visibleShow"
             width="600px"
             @cancel="close"
             @ok="confirm">
      <div slot="footer"
           class="dialog-footer"
           style="position: relative; padding-right: 15px; text-align: right;">
        <a-button @click="close">关闭</a-button>
        <a-button :disabled="iShow"
                  type="primary"
                  style="margin-left: 10px;"
                  @click="confirm">
          确定
        </a-button>
      </div>

      <a-form-model ref="noticeForm"
                    :label-col="{ span: 5 }"
                    :rules="rules"
                    :wrapper-col="{ span: 16 }"
                    :model="forValue">
        <a-form-model-item label="设备编号"
                           prop="facilityNum">
          <a-input :disabled="iShow"
                   placeholder="请输入设备编号"
                   v-model="forValue.facilityNum"></a-input>
        </a-form-model-item>
        <a-form-model-item label="设备型号"
                           prop="modelNumber">
          <a-input :disabled="iShow"
                   placeholder="请输入设备型号"
                   v-model="forValue.modelNumber"></a-input>
        </a-form-model-item>
        <a-form-model-item label="设备名称"
                           prop="name">
          <a-input :disabled="iShow"
                   placeholder="请输入设备名称"
                   v-model="forValue.name"></a-input>
        </a-form-model-item>
        <a-form-model-item label="备注"
                           prop="remark">
          <a-input :disabled="iShow"
                   placeholder="请输入备注"
                   v-model="forValue.remark"></a-input>
        </a-form-model-item>
        <a-form-model-item label="状态">
          <a-radio-group :disabled="iShow"
                         v-model="forValue.state"
                         @change="onRadio">
            <a-radio :value="1">
              正常
            </a-radio>
            <a-radio :value="2">
              故障
            </a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { myPagination } from "@/mixins/pagination.js";
import {
  signList,
  addSignList,
  SignData,
  editSignList,
  deleSignList,
} from "@/api/area.js";
export default {
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      visibleShow: false,
      advanced: false,
      iShow: false,
      list: [],
      title: "",
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "状态",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "satae",
          customRender: (text, record, index) => {
            return text == "1" ? (text = "正常") : (text = "故障");
          },
        },
        {
          title: "设备编号",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "facilityNum",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "设备型号",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "modelNumber",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "设备名称",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "name",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "备注",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "remark",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },

        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleData(record.id);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(record.id);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleDelete(record.id);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      forValue: {
        facilityNum: "" /* 设备编号 */,
        modelNumber: "" /* 设备型号 */,
        id: "" /* id */,
        name: "" /* 设备名称 */,
        remark: "" /* 备注 */,
        state: 1 /* 状态 */,
      },
      rules: {
        facilityNum: [
          { required: true, message: "设备编号不能为空", trigger: "blur" },
        ],
        modelNumber: [
          { required: true, message: "设备型号不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "设备名称不能为空", trigger: "blur" },
        ],
        remark: [{ required: true, message: "备注不能为空", trigger: "blur" }],
      },
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        facilityNum: "",
        modelNumber: "",
        name: "",
      },
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "系统管理");
    this.$store.dispatch("navigation/breadcrumb2", "届次管理");
    this.fetchData();
  },
  methods: {
    // 高级搜索
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    search () {
      this.queryForm.pageNum = 1;
      this.fetchData();
    },
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.fetchData();
    },
    handleAdd () {
      this.title = "新增数据";
      this.visibleShow = true;
      this.iShow = false;
    },
    async confirm () {
      this.$refs["noticeForm"].validate(async (valid) => {
        if (valid) {
          if (this.title == "新增数据") {
            let res = await addSignList(this.forValue);
            if (res.code == "0000") {
              this.$message.success("请求成功");
              this.fetchData();
              this.visibleShow = false;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          } else {
            let res = await editSignList(this.forValue);
            if (res.code == "0000") {
              this.$message.success("请求成功");
              this.fetchData();
              this.visibleShow = false;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          }
        }
      });
    },
    close () {
      this.forValue = {
        facilityNum: "" /* 设备编号 */,
        modelNumber: "" /* 设备型号 */,
        id: "" /* id */,
        name: "" /* 设备名称 */,
        remark: "" /* 备注 */,
        state: 1 /* 状态 */,
      };
      this.title = "";
      this.visibleShow = false;
      this.iShow = false;
    },
    async handleDelete (id) {
      this.$baseConfirm(
        "关联数据也将一并删除，你确定要删除选中项吗",
        null,
        () => {
          deleSignList(id).then((res) => {
            if (res.code == "0000") {
              this.$baseMessage(res.msg, "success");
              this.fetchData();
            } else {
              this.$baseMessage(res.msg, "error");
            }
          });
        }
      );
    },
    handleEdit (id) {
      this.title = "编辑数据";
      this.handleData(id, "edit");
    },
    async handleData (id, edit) {
      edit ? (this.title = "编辑数据") : (this.title = "查看数据");
      edit ? (this.iShow = false) : (this.iShow = true);

      let res = await SignData(id);
      if (res.code == "0000") {
        // 回显
        let { facilityNum, modelNumber, name, remark, state, id } = res.data;
        this.forValue.facilityNum = facilityNum;
        this.forValue.modelNumber = modelNumber;
        this.forValue.name = name;
        this.forValue.remark = remark;
        state == 1 ? (state = 1) : (state = 2);
        this.forValue.state = state;
        this.forValue.id = id;
        this.visibleShow = true;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    async fetchData () {
      let res = await signList(this.queryForm);
      if (res.code == "200") {
        this.list = res.rows;
        this.pagination.total = res.total;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    onRadio (value) {
      this.forValue.state = value.target.value;
    },
  },
};
</script>
<style scoped>
.signData {
  padding: 15px;
}
</style>
