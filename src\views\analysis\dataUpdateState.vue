<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <byui-query-form>
          <el-form
            ref="form"
            :model="queryForm"
            :inline="true"
            @submit.native.prevent
          >
            <el-form-item>
              <el-input
                v-model="queryForm.subjectName"
                placeholder="主题名称"
                class="table-input"
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="queryForm.subjectStatus"
                clearable
                placeholder="请选择主题状态"
                class="table-input"
              >
                <el-option
                  v-for="item in subjectStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="statisticsStatusList">
              <el-select
                v-model="queryForm.statisticsStatusList"
                multiple
                placeholder="请选择当前状态"
                style="width: 310px;"
                class="table-input"
              >
                <el-option
                  v-for="item in statisticsStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                icon="el-icon-search"
                type="primary"
                native-type="submit"
                @click="handleQuery"
                >查询</el-button
              >
            </el-form-item>
            <el-form-item>
              <el-badge
                :value="conditionsCount"
                class="item"
                style="margin-top: -2px;"
                :hidden="badgeHide"
              >
                <el-button type="primary" plain @click="drawer = true"
                  >更多查询条件</el-button
                >
              </el-badge>
            </el-form-item>
          </el-form>
        </byui-query-form>
        <!-- 更多查询条件抽屉 -->
        <el-drawer
          title="更多查询条件"
          :visible.sync="drawer"
          direction="rtl"
          :before-close="handleClose"
        >
          <byui-query-form
            style="padding: 0 10px; overflow: auto;"
            :style="{ height: drawerHeight }"
          >
            <el-form
              ref="drawerForm"
              :model="queryForm"
              @submit.native.prevent
              label-width="100px"
            >
              <el-form-item label="主题编号" prop="subjectCode">
                <el-input
                  v-model="queryForm.subjectCode"
                  @change="$forceUpdate()"
                />
              </el-form-item>
              <el-form-item label="主题名称" prop="subjectName">
                <el-input
                  v-model="queryForm.subjectName"
                  @change="$forceUpdate()"
                />
              </el-form-item>
              <el-form-item label="当前状态" prop="statisticsStatusList">
                <el-select
                  v-model="queryForm.statisticsStatusList"
                  multiple
                  placeholder="请选择当前状态"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="(item, index) in statisticsStatusOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="主题状态" prop="subjectStatus">
                <el-select
                  v-model="queryForm.subjectStatus"
                  clearable
                  placeholder="请选择主题状态"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="(item, index) in subjectStatusOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="数据来源" prop="dataSourceName">
                <el-select
                  v-model="queryForm.dataSourceName"
                  filterable
                  placeholder="请选择数据来源"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="(item, index) in dataSourceOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="上传单位" prop="sourceOrgName">
                <el-select
                  v-model="queryForm.sourceOrgName"
                  filterable
                  placeholder="请选择数据来源"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="item in sourceOrgOptions"
                    :key="item.orgName"
                    :label="item.orgName"
                    :value="item.orgName"
                  ></el-option>
                </el-select>
                <!-- <el-input
              v-model="queryForm.sourceOrgName"
              placeholder="上传单位"
              @change="$forceUpdate()"
                />-->
              </el-form-item>
              <el-form-item label="更新频率" prop="sourceUploadFrequency">
                <el-select
                  v-model="queryForm.sourceUploadFrequency"
                  filterable
                  placeholder="请选择更新频率"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="item in sourceUploadFrequencyOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="数据类型" prop="sourceDataType">
                <el-select
                  v-model="queryForm.sourceDataType"
                  filterable
                  placeholder="请选择数据类型"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="item in sourceDataTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="需求单位" prop="targetOrgName">
                <el-select
                  v-model="queryForm.targetOrgName"
                  filterable
                  placeholder="请选择需求单位"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="(item, index) in targetOrgOptions"
                    :key="index"
                    :label="item.orgName"
                    :value="item.orgName"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-divider>预警督办</el-divider>
              <el-form-item label="督办状态" prop="superviseStatus">
                <el-select
                  v-model="queryForm.superviseStatus"
                  filterable
                  placeholder="请选择督办状态"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="item in superviseStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  type="primary"
                  native-type="submit"
                  @click="handleQuery"
                  >查询</el-button
                >
                <el-button icon="el-icon-delete" @click="handleClear"
                  >清空</el-button
                >
              </el-form-item>
            </el-form>
          </byui-query-form>
        </el-drawer>
        <a-row class="mb25 tagCard">
          <div>
            <span class="card-title-word mr10">标注:</span>

            <span class="card-tip">
              <el-tag size="mini" class="has-warning-two mr10" type="danger"
                >督办中</el-tag
              >
            </span>
            <span class="card-tip">
              <el-tag size="mini" class="has-warning-one mr10" type="warning"
                >预警中</el-tag
              >
            </span>
            <span class="card-tip">
              <el-tag size="mini" class="normal-row mr10" type="success"
                >数据上报正常</el-tag
              >
            </span>
            <!-- <el-button
              style="float: right; padding: 3px 0"
              type="text"
              @click="openWarningList"
            >查看更多</el-button>-->
          </div>
        </a-row>
        <el-table
          ref="datasourceTable"
          v-loading="listLoading"
          :data="list"
          border
          :element-loading-text="elementLoadingText"
          @selection-change="setSelectRows"
          @sort-change="tableSortChange"
          :row-class-name="tableRowClassName"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="序号" width="80">
            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column
            label="需求单位"
            width="150"
            prop="targetOrgName"
          ></el-table-column>
          <el-table-column
            label="上传单位"
            width="150"
            prop="sourceOrgName"
          ></el-table-column>
          <el-table-column
            label="主题序号"
            v-if="
              checkPermission([
                'DATA_DATA_UPDATE_STATE_ALL_1',
                'DATA_DATA_UPDATE_STATE_ALL_2',
              ])
            "
            width="120"
            prop="shareDirectoryCode"
          ></el-table-column>
          <el-table-column
            label="主题名称"
            prop="subjectName"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            label="更新频率"
            width="100"
            prop="sourceUploadFrequency.desc"
          ></el-table-column>
          <el-table-column
            label="数据表名"
            prop="sourceDataEntity"
            :formatter="sourDataEntityFormat"
            v-if="checkPermission(['DATA_DATA_UPDATE_STATE_ALL_1'])"
          ></el-table-column>
          <el-table-column
            label="最后更新时间"
            width="120"
            prop="lastUpdateTime"
            :formatter="dateFormat"
          ></el-table-column>
          <el-table-column
            label="数据周期"
            width="120"
            prop="lastDataDesc"
          ></el-table-column>
          <el-table-column
            label="数据来源"
            width="120"
            prop="dataSourceName"
          ></el-table-column>
          <el-table-column
            label="行数"
            width="120"
            prop="lastDataCount"
            v-if="checkPermission(['DATA_DATA_UPDATE_STATE_ALL_1'])"
          ></el-table-column>
        </el-table>
        <el-pagination
          :background="background"
          :current-page="queryForm.page"
          :layout="layout"
          :page-size="queryForm.rows"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        ></el-pagination>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { getDisplayPage, doDelete } from "@/api/datasubject";
import { getList as getDataSourceList } from "@/api/datasource";
import { getSourceOrgList, getTargetOrgList } from "@/api/organization";
import moment from "moment";
import checkPermission from "@/utils/permission";
export default {
  name: "Table",
  components: {},
  filters: {},
  data() {
    return {
      list: [],
      listLoading: true,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      queryForm: {
        showHasData: true,
        page: 1,
        rows: 10,
      },
      drawer: false,
      drawerHeight: window.innerHeight - 75 + "px",
      badgeHide: true,
      subjectStatusOptions: [
        {
          value: 0,
          label: "启用",
        },
        {
          value: 1,
          label: "禁用",
        },
      ],
      statisticsStatusOptions: [
        {
          value: 4,
          label: "督办中",
        },
        {
          value: 5,
          label: "预警中",
        },
        {
          value: 6,
          label: "数据上报正常",
        },
      ],
      superviseStatusOptions: [
        {
          value: 0,
          label: "启用",
        },
        {
          value: 1,
          label: "禁用",
        },
      ],
      sourceUploadFrequencyOptions: [
        {
          value: 1,
          label: "日",
        },
        {
          value: 2,
          label: "周",
        },
        {
          value: 3,
          label: "月",
        },
        {
          value: 4,
          label: "季",
        },
        {
          value: 5,
          label: "半年",
        },
        {
          value: 6,
          label: "年",
        },
      ],
      sourceDataTypeOptions: [
        {
          value: 0,
          label: "数据表",
        },
        {
          value: 1,
          label: "文件",
        },
        {
          value: 2,
          label: "目录",
        },
        {
          value: 3,
          label: "API",
        },
      ],
      dataSourceOptions: [],
      sourceOrgOptions: [],
      targetOrgOptions: [],
    };
  },
  created() {
    this.fetchData();
    this.initDataSource();
    this.initOptions();
  },
  beforeDestroy() {},
  mounted() {
    // 抽屉滚动区高度
    window.onresize = () => {
      return (() => {
        this.drawerHeight = window.innerHeight - 75 + "px";
      })();
    };
  },
  computed: {
    conditionsCount: function () {
      let size = 0;
      for (let key in this.queryForm) {
        if (
          this.queryForm[key] !== undefined &&
          this.queryForm[key] !== null &&
          this.queryForm[key] !== ""
        ) {
          size++;
        }
      }
      const defaultSize = 5;
      this.badgeHide = size - defaultSize <= 0;
      return size - defaultSize;
    },
  },
  methods: {
    tableSortChange(column) {},
    setSelectRows(val) {
      this.selectRows = val;
    },

    handleSizeChange(val) {
      this.queryForm.rows = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryForm.page = val;
      this.fetchData();
    },
    handleQuery() {
      this.drawer = false;
      this.queryForm.page = 1;
      this.fetchData();
    },
    handleClear() {
      if (this.$refs["drawerForm"] !== undefined) {
        this.$refs["drawerForm"].resetFields();
      }
    },
    fetchData() {
      this.listLoading = true;
      getDisplayPage(this.queryForm).then((res) => {
        this.list = res.data.records;
        this.total = res.data.total;
        this.queryForm.page = res.data.current;
        this.queryForm.rows = res.data.size;
        setTimeout(() => {
          this.listLoading = false;
        }, 500);
      });
    },
    handleClose(done) {
      done();
      // this.$confirm('确认关闭？')
      //   .then(_ => {
      //     done();
      //   })
      //   .catch(_ => {});
    },
    initDataSource() {
      getDataSourceList().then((res) => {
        this.dataSourceOptions = [];
        res.data.forEach((element) => {
          this.dataSourceOptions.push({
            value: element.dsId,
            label: element.dsName,
          });
        });
      });
    },
    initOptions() {
      getSourceOrgList().then((res) => {
        this.sourceOrgOptions = res.data;
      });
      getTargetOrgList().then((res) => {
        this.targetOrgOptions = res.data;
      });
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.statisticsStatus == 4) {
        return "has-warning-two";
      } else if (row.statisticsStatus == 5) {
        return "has-warning-one";
      } else {
        return "normal-row";
      }
      return "";
    },
    dateFormat(row, column, cellValue, index) {
      var date = row[column.property];
      if (date == undefined) {
        return "";
      }
      return moment(date).format("YYYY-MM-DD");
    },
    sourDataEntityFormat(row, column, cellValue, index) {
      var entity = row[column.property];
      if (entity == undefined) {
        return "";
      }
      return JSON.parse(entity).tableName;
    },
    checkPermission,
  },
};
</script>
