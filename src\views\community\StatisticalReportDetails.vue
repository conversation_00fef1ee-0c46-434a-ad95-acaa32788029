<template>
  <div class="table-container">
    <!--  <a-button style="float: left;z-index: 99;" @click="$router.go(-1)"> 返回</a-button> -->
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <div>
          <a-form-model ref="form" :model="queryForm" layout="inline" :labelCol="{ span: 6 }"
            :wrapperCol="{ span: 18 }">
            <div>
              <a-row>
                <a-col :span="6">
                  <a-form-item label="届次">
                    <a-select v-model="queryForm.session" placeholder="请选择届次" allow-clear show-search
                      style="width: 200px; margin-right: 10px;">
                      <a-select-option v-for="item in SessionList" :key="item.id" :label="item.name" :value="item.name">
                        {{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="行政区划">
                    <a-select v-model="queryForm.administrativeAreaId" @change="handleAdministrativeAreaChange"
                      placeholder="请选择行政区划" allow-clear show-search style="width: 200px; margin-right: 10px;">
                      <a-select-option v-for="item in administrativeAreas" :key="item.id" :label="item.name"
                        :value="item.id">
                        {{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item label="街道乡镇" prop="streetTownId">
                    <a-select v-model="queryForm.streetTownId" placeholder="请选择街道乡镇"
                      style="width: 200px; margin-right: 10px;">
                      <a-select-option v-for="item in streetTowns" :key="item.id" :label="item.name" :value="item.id">{{
                          item.name
                      }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <span style="float: right; margin-top: 3px">
                    <a @click="toggleAdvanced" style="margin-right: 8px">
                      {{ advanced ? "收起" : "高级搜索" }}
                      <a-icon :type="advanced ? 'up' : 'down'" />
                    </a>
                    <a-button type="primary" @click="acquireData()">统计</a-button>
                    <a-button style="margin-left: 12px;" @click="reset" class="pinkBoutton">重置</a-button>
                  </span>
                </a-col>
              </a-row>
              <a-row v-if="advanced">
                <a-col :span="6">
                  <a-form-model-item label="时间范围">
                    <a-select placeholder="请选择时间范围" allow-clear style="width: 200px;" @change="handleTime"
                      v-model="queryForm.timeRange">
                      <a-select-option v-for="item in timeScope" :key="item.name" :value="item.name">{{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="开始时间" prop="startTime">
                    <a-date-picker :disabled="showDisabled" v-model="queryForm.startTime" allow-clear
                      value-format="YYYY-MM-DD HH:mm:ss" placeholder="选择开始时间" style="width: 100%;" :disabled-date="
                        (current) =>
                          current && queryForm.endTime
                            ? current.valueOf() >=
                            moment(new Date(queryForm.endTime)).valueOf()
                            : false
                      "></a-date-picker>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="结束时间" prop="endTime">
                    <a-date-picker v-model="queryForm.endTime" allow-clear :disabled="showDisabled"
                      value-format="YYYY-MM-DD" placeholder="选择结束时间" style="width: 100%;" :disabled-date="
                        (current) =>
                          current && queryForm.startTime
                            ? moment(new Date(queryForm.startTime)).valueOf() >=
                            current.valueOf()
                            : false
                      "></a-date-picker>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form-model>
        </div>

        <div>
          <div>
            <a-button @click="() => { $router.go(-1); }">返回</a-button>
          </div>
          <div id="tiaoxingtu_xq" style="width: 100%; height: 400px"></div>
        </div>

      </a-col>
    </a-row>

  </div>
</template>

<script>
// import { communityScheduleList } from "@/api/intoCommunityOrg.js";
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
import moment from "moment";
export default {
  name: "IntoCommunityOrg",
  components: {},
  filters: {},
  data() {
    return {
      shows: false,
      advanced: false,
      timeScope: [
        { id: 1, name: "本届" },
        { id: 2, name: "最近三个月" },
        { id: 3, name: "今年1~6月" },
        { id: 4, name: "今年7~12月" },
        { id: 5, name: "今年内" },
        { id: 6, name: "自定义" },
      ],
      showDisabled: true,
      list: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        type: "",
        timeRange: '本届',
        streetTownId: undefined
      },
      listLoading: false,
      openYear: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 10,
      background: true,
      //list-----
      selectRows: "",
      centerDialogVisible: false,
      formLabelWidth: "120px",
      //行政区域列表
      SessionList: [
        { name: "第十五届", key: 1 },
        { name: "第十六届", key: 2 },
      ],
      administrativeAreas: [],
      streetTowns: [],
      indexNum: 1,

    };
  },
  created() {
    this.listAdministrativeAreas();
    this.fetchData();
    this.initRightColumn();
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "统计报表详情");
    // 意见分类统计图
  },
  mounted() {
    this.initRightColumn();
  },
  methods: {

    initRightColumn() {
      //  this.BarChart();
      //   this.PieChart();
      this.BarChart_XQ();
    },
    // 广州市意见建议饼图
    PieChart() {
      var myChart = echarts.init(document.getElementById("bingtu"));
      myChart.setOption({
        title: {
          text: '广州市意见建议饼图',
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "right",
        },
        series: [
          {
            name: "Access From",
            type: "pie",
            radius: "50%",
            data: [{ value: 50, name: "未回复" },
            { value: 100, name: "已回复" }],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      });

    },
    // 广州市意见建议柱状图
    BarChart() {
      var myChart = echarts.init(document.getElementById("tiaoxingtu"));
      var option;
      option = {
        // title: {
        //     text: '广州市意见建议柱状图',
        //     subtext: "点击柱型可查看相应详情", 
        //      left: "center",
        //   },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: { left: "right", },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: ['海珠区', '天河区', '南沙区'],
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [

          {
            name: '已回复',
            type: 'bar',
            stack: 'Ad',
            barWidth: 50,
            emphasis: {
              focus: 'series'
            },
            data: [120, 132, 101,]
          },
          {
            name: '未回复',
            type: 'bar',
            stack: 'Ad',
            barWidth: 50,
            emphasis: {
              focus: 'series'
            },
            data: [220, 182, 191,]
          },
        ]
      };
      option && myChart.setOption(option);

    },
    // 广州市意见建议详细柱状图
    BarChart_XQ() {
      var myChart = echarts.init(document.getElementById("tiaoxingtu_xq"));
      var option;
      option = {
        title: { text: '广州市意见建议详细柱状图', left: "center", },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: { left: "right", },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: ['海珠区', '天河区', '南沙区'],
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '代表已回复',
            type: 'bar',
            stack: 'db',
            barWidth: 50,
            data: [120, 132, 101,]
          },
          {
            name: '站长已回复',
            type: 'bar',
            stack: 'zz',
            barWidth: 50,
            data: [220, 182, 191,]
          },
          {
            name: '系统已回复',
            type: 'bar',
            stack: 'xt',
            barWidth: 50,
            data: [220, 182, 191,]
          },
          {
            name: '未回复',
            type: 'bar',
            stack: 'weihuif',
            barWidth: 50,
            data: [220, 182, 191,]
          },
        ]
      };
      option && myChart.setOption(option);
    },
    // 修改时间范围
    handleTime(name) {
      var time = new Date();
      let Year = time.getFullYear() /* 当前年份 */
      if (name == "最近三个月") {
        let startTime = time.toLocaleDateString()    /* 当前时间  */
        this.queryForm.startTime = startTime
        time.setMonth(time.getMonth() + 3);
        this.queryForm.endTime = time.toLocaleDateString()
        this.showDisabled = true
      } else if (name == "今年1~6月") {
        this.queryForm.startTime = `${Year}-01-01`
        this.queryForm.endTime = `${Year}-06-30`
        this.showDisabled = true
      }
      else if (name == "今年7~12月") {
        this.queryForm.startTime = `${Year}-07-01`
        this.queryForm.endTime = `${Year}-12-31`
        this.showDisabled = true
      } else if (name == "今年内") {
        this.queryForm.startTime = `${Year}-01-01`
        this.queryForm.endTime = `${Year}-12-31`
        this.showDisabled = true
      } else if (name == "自定义") {
        this.queryForm.startTime = ''
        this.queryForm.endTime = ''
        this.showDisabled = false
      } else if (name == "本届") {
        this.queryForm.startTime = ''
        this.queryForm.endTime = ''
        this.showDisabled = true
      }
    },
    // 高级搜索
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    // 重置
    reset() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        timeRange: '本届'
      };
      this.showDisabled = true;
      this.fetchData();
    },
    moment,
    // 设置administrativeAreaId
    selectadministrativeAreas(state) {
      console.log("🤗🤗🤗, state =>", state);
      this.administrativeAreas.map((item) => {
        if (item.value === state) {
          this.listQuery.administrativeAreaId = item.value;
        }
      });
    },
    listAdministrativeAreas() {
      // 获取行政区县
      getAdministrativeAreas().then((response) => {
        console.log(response);
        this.administrativeAreas = response.data;
      });
    },
    // 获取乡镇街道
    liststreetTowns(administrativeAreaId) {
      getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
        }
      );
    },
    // 选择行政区域
    handleAdministrativeAreaChange(val) {
      // if (!val) return;
      this.queryForm.streetTownId = undefined;
      // this.selectadministrativeAreas(val);
      this.liststreetTowns(val);
    },
    acquireData() {
      console.log(this.queryForm);
    },
    fetchData() {
      this.listLoading = true;
      // 获取数据
      // communityScheduleList(this.queryForm).then((res) => {
      //   console.log("🤗🤗🤗, res =>", res);
      //   this.list = res.rows;
      //   this.listLoading = false;
      //   this.total = res.total;
      //   this.pagination.total = res.total;
      // }); 
      this.initRightColumn();
    },


    // 切换页数
    changePageSize(current, pageSize) {
      this.queryForm.pageNum = current;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = current;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(current, pageSize) {
      this.queryForm.pageNum = current;
      this.pagination.current = current;
      this.fetchData();
    },



    handleCurrent(row) {
      console.log("row", row);
    },
  },
};
</script>
<style scoped>
#tiaoxingtu,
#bingtu,
#tiaoxingtu_xq {
  display: flex;
  justify-content: space-around;
}

.code_btn {
  margin-bottom: 10px;
}

.bar_title {
  margin: 0 auto;
  margin-bottom: -10px;
}

.bar_title p {
  font-weight: bolder;
  text-align: center;
}

.bar_title span {
  text-align: center;
  display: block;
  width: 100%;
}

.p {
  display: flex;
  width: 100%;
}

.p>span {
  flex: 1;
}
</style>
