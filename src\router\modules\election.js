import Layout from "@/layouts";

// {
//   path: "staging",
//   name: "InformStaging",
//   component: () => import("@/views/meeting/testPage/index"),
//   meta: {
//     title: "待办任务",
//     permissions: ["I_XTGLY", "I_XXFBY"],
//   },
// },
// 代表选举管理
export default [
  {
    path: "/electoralMage",
    component: Layout,
    redirect: "noRedirect",
    name: "electoralMage",
    title: "代表选举管理",
    meta: {
      // title:"首页",
      title: "工作人员首页",
    },
    children: [
      // {
      //   // 暂时隐藏别动晓得不
      //   path: "organizationIndex111",
      //   name: "organizationIndex111",
      //   component: () => import("@/views/meeting/testPage/index"),
      //   meta: {
      //     title: "代表首页",
      //   },
      // },
      // {
      //   path: "RepresentIndex",
      //   name: "RepresentIndex",
      //   component: () => import("@/views/electoralMage/RepresentIndex"),
      //   meta: {
      //     title: "代表首页",
      //   },
      //   // hidden: true,
      // },
      {
        path: "electionIndex",
        name: "electionIndex",
        component: () => import("@/views/electoralMage/electionIndex"),
        meta: {
          title: "代表首页",
        },
        hidden: true,
      },

      {
        path: "electoralMage",
        name: "electoralMage",
        component: () => import("@/views/electoralMage/index"),
        meta: {
          title: "工作人员首页",
        },
        hidden: true,
      },
      {
        path: "RepresentIndex",
        name: "RepresentIndex",
        component: () =>
          import("@/views/electoralMage/electionmanagementIndex"),
        meta: {
          // title: "工作人员首页",

          title: "首页",
        },
      },
      {
        path: "information",
        name: "information",
        component: () =>
          import("@/views/representativeInformation/information"),
        meta: {
          title: "待办事项",
        },
      },
      {
        path: "matters",
        name: "matters",
        component: () => import("@/views/representativeInformation/matters"),
        meta: {
          title: "经办事项",
        },
      },
    ],
  },
  // {
  //   path: "/RepresentativeInformation",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "huoDong",
  //   title: "代表选举管理",
  //   meta: {
  //     title: "代表信息",
  //     icon: "database",
  //     permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
  //   },
  //   alwaysShow: false,
  //   children: [
  //     {
  //       path: "information",
  //       name: "information",
  //       component: () =>
  //         import("@/views/representativeInformation/information"),
  //       meta: {
  //         title: "待办事项",
  //       },
  //     },
  //     {
  //       path: "matters",
  //       name: "matters",
  //       component: () => import("@/views/representativeInformation/matters"),
  //       meta: {
  //         title: "经办事项",
  //       },
  //     },
  //   ],
  // },

  {
    path: "/ready",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表选举管理",
    meta: {
      title: "预备人选管理",
      icon: "database",
    },
    alwaysShow: false,
    children: [
      {
        path: "candidates",
        name: "candidates",
        component: () => import("@/views/preparatoryPerson/candidates"),
        meta: {
          title: "预备人选基本情况申报",
        },
      },
      {
        path: "information",
        name: "information",
        component: () => import("@/views/preparatoryPerson/information"),
        meta: {
          title: "预备人选信息登记",
        },
      },
      {
        path: "integratedQuery",
        name: "integratedQuery",
        component: () => import("@/views/preparatoryPerson/integratedQuery"),
        meta: {
          title: "综合查询",
        },
        hidden: true,
      },
      {
        path: "preparatoryPersonReport",
        name: "preparatoryPersonReport",
        component: () =>
          import("@/views/preparatoryPerson/preparatoryPersonReport"),
        meta: {
          title: "统计报表",
        },
        hidden: true,
      },
      {
        path: "declarationReport",
        name: "declarationReport",
        component: () => import("@/views/preparatoryPerson/declarationReport"),
        meta: {
          title: "预备人选申报统计报表",
        },
        hidden: true,
      },
      {
        path: "registerReport",
        name: "registerReport",
        component: () => import("@/views/preparatoryPerson/registerReport"),
        meta: {
          title: "预备人选登记统计报表",
        },
        hidden: true,
      },
      {
        path: "rosterReport",
        name: "rosterReport",
        component: () => import("@/views/preparatoryPerson/rosterReport"),
        meta: {
          title: "预备人选名册",
        },
        hidden: true,
      },
      {
        path: "candidate/declare",
        name: "rosterReport",
        component: () => import("@/views/preparatoryPerson/declare"),
        meta: {
          title: "候选人申报表",
        },
        hidden: true,
      },
      {
        path: "candidate/check",
        name: "rosterReport",
        component: () => import("@/views/preparatoryPerson/check"),
        meta: {
          title: "候选人登记表",
        },
        hidden: true,
      },
      {
        path: "candidate/muster",
        name: "rosterReport",
        component: () => import("@/views/preparatoryPerson/muster"),
        meta: {
          title: "候选人名册",
        },
        hidden: true,
      },
    ],
  },

  {
    path: "/candidate",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表选举管理",
    meta: {
      title: "候选人管理",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "candidateConfirm",
        name: "candidateConfirm",
        component: () => import("@/views/candidate/candidateConfirm"),
        meta: {
          title: "候选人确认",
        },
      },
      {
        path: "candidateDistribute",
        name: "candidateDistribute",
        component: () => import("@/views/candidate/candidateDistribute"),
        meta: {
          title: "候选人分配",
        },
      },
      {
        path: "declare",
        name: "declare",
        component: () => import("@/views/preparatoryPerson/declare"),
        meta: {
          title: "候选人申报表",
        },
        // hidden: true,
      },
      {
        path: "check",
        name: "check",
        component: () => import("@/views/preparatoryPerson/check"),
        meta: {
          title: "候选人登记表",
        },
        // hidden: true,
      },
      {
        path: "candidateInquire",
        name: "candidateInquire",
        component: () => import("@/views/candidate/candidateInquire"),
        meta: {
          title: "综合查询",
        },
        hidden: true,
      },
      {
        path: "candidateReport",
        name: "candidateReport",
        component: () => import("@/views/candidate/candidateReport"),
        meta: {
          title: "统计报表",
        },
        hidden: true,
      },
      // 统计报表下的模块
      {
        path: "candidateForm",
        name: "candidateForm",
        component: () => import("@/views/candidate/candidateForm"),
        meta: {
          title: "候选人申报表",
        },
        hidden: true,
      },
      {
        path: "candidateRegForm",
        name: "candidateRegForm",
        component: () => import("@/views/candidate/candidateRegForm"),
        meta: {
          title: "候选人登记表",
        },
        hidden: true,
      },
      {
        path: "candidateSelection",
        name: "candidateSelection",
        component: () => import("@/views/candidate/candidateSelection"),
        meta: {
          title: "候选人选名册",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/ballot",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表选举管理",
    meta: {
      title: "投票选举管理",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "entryElection",
        name: "entryElection",
        component: () => import("@/views/ballot/entryElection"),
        meta: {
          title: "录入选举结果",
        },
      },
      {
        path: "addition",
        name: "addition",
        component: () => import("@/views/ballot/addition"),
        meta: {
          title: "录入补选结果",
        },
      },
      {
        path: "assignmentDelegation",
        name: "assignmentDelegation",
        component: () => import("@/views/ballot/assignmentDelegation"),
        meta: {
          title: "分配代表团",
        },
      },
      {
        path: "enrollInquire",
        name: "enrollInquire",
        component: () => import("@/views/ballot/enrollInquire"),
        meta: {
          title: "综合查询",
        },
        hidden: true,
      },
      {
        path: "enrollReport",
        name: "enrollReport",
        component: () => import("@/views/ballot/enrollReport"),
        meta: {
          title: "统计报表",
        },
        hidden: true,
      },
      {
        path: "behalfDeclare",
        name: "rosterReport",
        component: () => import("@/views/ballot/behalfDeclare"),
        meta: {
          title: "代表申报表",
        },
        hidden: true,
      },
      {
        path: "behalf-check",
        name: "rosterReport",
        component: () => import("@/views/ballot/behalf-check"),
        meta: {
          title: "代表登记表",
        },
        hidden: true,
      },
      {
        path: "behalf-muster",
        name: "rosterReport",
        component: () => import("@/views/ballot/behalf-muster"),
        meta: {
          title: "代表名册",
        },
        hidden: true,
      },
      // 统计报表下的模块=》
      {
        path: "RepresentativeForm",
        name: "RepresentativeForm",
        component: () =>
          import(
            "@/views/preparatoryPerson/preparatoryPersonRepresentativeForm"
          ),
        meta: {
          title: "代表选举结果",
        },
        hidden: true,
      },
      {
        path: "by-election",
        name: "rosterReport",
        component: () => import("@/views/ballot/by-election"),
        meta: {
          title: "代表补选结果报表",
        },
        hidden: true,
      },
      {
        path: "behalf-base",
        name: "rosterReport",
        component: () => import("@/views/ballot/behalf-base"),
        meta: {
          title: "代表基本情况统计报表",
        },
        hidden: true,
      },
      {
        path: "Composition",
        name: "Composition",
        component: () =>
          import(
            "@/views/preparatoryPerson/preparatoryPersonRepresentativeComposition"
          ),
        meta: {
          title: "代表构成情况统计报表",
        },
        hidden: true,
      },
      {
        path: "Distribution",
        name: "Distribution",
        component: () =>
          import(
            "@/views/preparatoryPerson/preparatoryPersonRepresentativeDistribution"
          ),
        meta: {
          title: "代表名额分配及构成表",
        },
        hidden: true,
      },
      {
        path: "VacancyStatistics",
        name: "VacancyStatistics",
        component: () =>
          import(
            "@/views/preparatoryPerson/preparatoryPersonVacancyStatistics"
          ),
        meta: {
          title: "出缺情况统计",
        },
        hidden: true,
      },
      {
        path: "By_electionForm",
        name: "By_electionForm",
        component: () =>
          import("@/views/preparatoryPerson/preparatoryPersonBy_electionForm"),
        meta: {
          title: "补选结果",
        },
        hidden: true,
      },
      {
        path: "Vacancy",
        name: "Vacancy",
        component: () =>
          import("@/views/preparatoryPerson/preparatoryPersonVacancyForm"),
        meta: {
          title: "出缺结果",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/elected2",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表选举管理",
    meta: {
      title: "当选管理",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "electedInquire",
        name: "electedInquire",
        component: () => import("@/views/elected/electedInquire"),
        meta: {
          title: "综合查询",
        },
        hidden: true,
      },
      {
        path: "electedStatistics",
        name: "electedStatistics",
        component: () => import("@/views/elected/electedStatistics"),
        meta: {
          title: "统计报表",
        },
      },
    ],
  },
  {
    path: "/comprehensivequery",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表选举管理",
    meta: {
      title: "统计查询",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "Comprehensivequery",
        name: "Comprehensivequery",
        component: () => import("@/views/elected/Comprehensivequery"),
        meta: {
          title: "综合查询",
        },
        // hidden: true,
      },
      {
        path: "StatisticalReport",
        name: "StatisticalReport",
        component: () => import("@/views/elected/StatisticalReport"),
        meta: {
          title: "统计报表",
        },
        // hidden: true,
      },
    ],
  },
  {
    path: "/lackData",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "代表选举管理",
    meta: {
      title: "出缺管理",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "depositDeclaration",
        name: "depositDeclaration",
        component: () => import("@/views/lackData/depositDeclaration"),
        meta: {
          title: "出缺登记",
        },
      },
      {
        path: "distributioReport",
        name: "distributioReport",
        // component: () => import("@/views/lackData/distributioReport"),
        component: () =>
          import(
            "@/views/preparatoryPerson/preparatoryPersonVacancyStatistics"
          ),

        // component: () =>
        //   import("@/views/preparatoryPerson/preparatoryPersonVacancyForm"),
        meta: {
          title: "统计报表",
        },
      },
      // 代表统计报表 暂时放在这里
      {
        path: "repStatistics",
        name: "repStatistics",
        component: () => import("@/views/lackData/repStatistics"),
        meta: {
          title: "代表统计报表",
        },
      },
      {
        path: "repStatisticsBase",
        name: "repStatisticsBase",
        component: () => import("@/views/lackData/repStatisticsBase"),
        meta: {
          title: "代表基本情况统计报表",
        },
        hidden: true,
      },
      {
        path: "repStatisticsConstitute",
        name: "repStatisticsConstitute",
        component: () => import("@/views/lackData/repStatisticsConstitute"),
        meta: {
          title: "代表构成情况统计报表",
        },
        hidden: true,
      },
      {
        path: "quotaAllocation",
        name: "quotaAllocation",
        component: () => import("@/views/lackData/quotaAllocation"),
        meta: {
          title: "代表名额分配及构成表",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/systemIndex",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "代表选举管理",
    meta: {
      title: "系统管理",
      permissions: ["ADMIN", "I_XTGLY"],
    },
    children: [
      {
        path: "representativePush",
        name: "representativePush",
        component: () => import("@/views/candidate/candidateInquire"),
        meta: {
          title: "代表信息推送",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "meetingEdit",
        name: "MeetingEdit",
        component: () => import("@/views/meeting/edit"),
        meta: {
          title: "数据权限管理",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "session",
        name: "session",
        component: () => import("@/views/election/systemIndex/electionMage"),
        // component: () => import("@/views/meeting/session/index"),
        meta: {
          title: "届次管理",
        },
      },
      {
        path: "gradingManagement",
        name: "gradingManagement",
        component: () =>
          import("@/views/election/systemIndex/gradingManagement"),
        meta: {
          title: "分级管理",
        },
      },

      {
        path: "menuManage",
        name: "MenuManage",
        component: () => import("@/views/menu/index"),
        meta: {
          title: "菜单管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "roleManage",
        name: "RoleManage",
        component: () => import("@/views/role/index"),
        meta: {
          title: "角色管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "userManage",
        name: "UserManage",
        component: () => import("@/views/user/index"),
        meta: {
          title: "用户管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },

      {
        path: "editRole",
        name: "EditRole",
        component: () => import("@/views/role/edit"),
        meta: {
          title: "编辑角色",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
    ],
  },
];
