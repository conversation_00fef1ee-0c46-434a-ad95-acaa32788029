<template>
  <div>
    <div>
      <a-alert v-if="alert" :message="alert" banner />
    </div>
  </div>
</template>

<script>
export default {
  name: "ActivitySchedule",
  model: {
    prop: "form",
    event: "change",
  },
  props: {
    // title: {
    //   type: String,
    //   required: true,
    // },
    alert: {
      type: String,
      default: "",
    },
    // create | view | update | audit
    // oper: {
    //   type: String,
    //   required: true,
    // },
  },
  computed: {
    operTitle() {
      if (this.oper == "create") return "添加" + this.title;

      if (this.oper == "update") return "编辑" + this.title;

      if (this.oper == "audit") return "送审、审核" + this.title;

      return "查看" + this.title;
    },
  },
  mounted() {
    console.log(this.$slots);
  },
};
</script>

<style lang="scss" scoped>
.EnterContent {
  display: flex;
  padding: 0 1px;

  .content {
    flex: 1;

    .content-item {
      margin-top: -3px;
      > h3 {
        height: 30px;
        line-height: 30px;
        border-left: 4px solid #c71c33;
        padding-left: 18px;
        font-weight: bold;
        font-size: 18px;
        box-sizing: border-box;
      }

      > div {
        border-radius: 8px;
        border: 1px solid #dcdfe6;
        padding: 20px;
      }
    }

    ::v-deep .actions {
      padding: 20px 0;
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }

  .right {
    flex-shrink: 0;
    padding: 0 20px;

    > h3 {
      height: 30px;
      line-height: 30px;
      font-weight: bold;
      font-size: 18px;
      box-sizing: border-box;
    }
  }
}
</style>
