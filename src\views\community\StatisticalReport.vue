<template>
  <div class="table-container">
    <!--  <a-button style="float: left;z-index: 99;" @click="$router.go(-1)"> 返回</a-button> -->
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <!-- <div>
          <a-form-model
            ref="form"
            :model="queryForm"
            layout="inline"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
          >
            <div>
              <a-row>
                <a-col :span="6">
                  <a-form-item label="届次">
                    <a-select
                      v-model="queryForm.session"
                      placeholder="请选择届次"
                      allow-clear
                      show-search
                      style="width: 200px; margin-right: 10px;"
                    >
                      <a-select-option
                        v-for="item in SessionList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                      >
                        {{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item></a-col
                >
                <a-col :span="6">
                  <a-form-item label="行政区划">
                    <a-select
                      v-model="queryForm.administrativeAreaId"
                      placeholder="请选择行政区划"
                      allow-clear
                      show-search
                      style="width: 200px; margin-right: 10px;"
                      @change="handleAdministrativeAreaChange"
                    >
                      <a-select-option
                        v-for="item in administrativeAreas"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      >
                        {{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item label="街道乡镇" prop="streetTownId">
                    <a-select
                      v-model="queryForm.streetTownId"
                      placeholder="请选择"
                      style="width: 200px; margin-right: 10px;"
                    >
                      <a-select-option
                        v-for="item in streetTowns"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                        >{{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <span style="float: right; margin-top: 3px;">
                    <a style="margin-right: 8px;" @click="toggleAdvanced">
                      {{ advanced ? "收起" : "高级搜索" }}
                      <a-icon :type="advanced ? 'up' : 'down'" />
                    </a>
                    <a-button type="primary" @click="acquireData()"
                      >统计</a-button
                    >
                    <a-button style="margin-left: 8px;" @click="reset"
                       class="pinkBoutton" >重置</a-button
                    >
                  </span>
                </a-col>
              </a-row>
              <a-row v-if="advanced">
                <a-col :span="6">
                  <a-form-model-item label="时间范围">
                    <a-select
                      v-model="queryForm.timeRange"
                      placeholder="请选择"
                      allow-clear
                      style="width: 200px;"
                      @change="handleTime"
                    >
                      <a-select-option
                        v-for="item in timeScope"
                        :key="item.name"
                        :value="item.name"
                        >{{ item.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="开始时间" prop="startTime">
                    <a-date-picker
                      v-model="queryForm.startTime"
                      :disabled="showDisabled"
                      allow-clear
                      value-format="YYYY-MM-DD "
                      placeholder="选择开始时间"
                      style="width: 100%;"
                      :disabled-date="
                        (current) =>
                          current && queryForm.endTime
                            ? current.valueOf() >=
                              moment(new Date(queryForm.endTime)).valueOf()
                            : false
                      "
                    ></a-date-picker>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="结束时间" prop="endTime">
                    <a-date-picker
                      v-model="queryForm.endTime"
                      allow-clear
                      :disabled="showDisabled"
                      value-format="YYYY-MM-DD"
                      placeholder="选择结束时间"
                      style="width: 100%;"
                      :disabled-date="
                        (current) =>
                          current && queryForm.startTime
                            ? moment(new Date(queryForm.startTime)).valueOf() >=
                              current.valueOf()
                            : false
                      "
                    ></a-date-picker>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form-model>
        </div> -->
        <div>
          <div class="bar_title">
            <p>广州市意见建议柱状图</p>
            <span @click="to_details"> 点击柱型可查看相应详情</span>
          </div>
          <div id="tiaoxingtu" style="width: 100%; height: 400px;"></div>
        </div>

        <div id="bingtu" style="width: 100%; height: 400px;"></div>

        <div id="tiaoxingtu_xq" style="width: 100%; height: 400px;"></div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { instance_1 } from "@/api/axiosRq";
import { communityScheduleList } from "@/api/intoCommunityOrg.js";
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
import moment from "moment";
// 引入基本模板
let echarts = require("echarts/lib/echarts");
// 引入饼状图组件
require("echarts/lib/chart/pie");
// 引入提示框和title组件
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");

// 引入柱状图组件
require("echarts/lib/chart/bar");
export default {
  name: "IntoCommunityOrg",
  components: {},
  filters: {},
  data() {
    return {
      shows: false,
      advanced: false,
      timeScope: [
        { id: 1, name: "本届" },
        { id: 2, name: "最近三个月" },
        { id: 3, name: "今年1~6月" },
        { id: 4, name: "今年7~12月" },
        { id: 5, name: "今年内" },
        { id: 6, name: "自定义" },
      ],
      showDisabled: true,
      list: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        type: "",
        timeRange: "本届",
         streetTownId: undefined,
      },
      listLoading: false,
      openYear: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 10,
      background: true,
      //list-----
      selectRows: "",
      centerDialogVisible: false,
      formLabelWidth: "120px",
      //行政区域列表
      SessionList: [
        { name: "第十五届", key: 1 },
        { name: "第十六届", key: 2 },
      ],
      administrativeAreas: [],
      streetTowns: [],
      indexNum: 1,
      titleData: ["海珠区", "天河区", "南沙区"],
      echartsData: [
        [60, 70, 60] /*代表 已回复 */,
        [30, 70, 100] /*站长 已回复 */,
        [30, 60, 100] /*系统 已回复 */,

        [120, 200, 260] /* 已回复 */,
        [160, 120, 240] /* 未回复 */,

        [580] /* 已回复总数 */,
        [520] /* 未回复总数 */,
      ],
    };
  },
  created() {
    this.listAdministrativeAreas();
    this.myChartData();

    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "统计报表");
    // 意见分类统计图
  },
  mounted() {
    // this.initRightColumn();
  },
  methods: {
    myChartData() {
      instance_1({
        method: "get",
        url: "/peopleResponse/report",
      }).then((res) => {
        if (res.data.code == "0000") {
          this.echartsData = res.data.data;
          this.initRightColumn();
        }
      });
    },
    to_details() {
      this.shows = true;
      // this.$router.push({
      //   path: "/BBS/StatisticalReportDetails",
      //   // query: {
      //   //   data: res.data.data,

      //   // },
      // });
    },
    initRightColumn() {
      this.$nextTick(() => {
        this.BarChart();
        this.PieChart();
        this.BarChart_XQ();
      });
    },
    // 广州市意见建议饼图
    PieChart() {
      var myChart = echarts.init(document.getElementById("bingtu"));
      myChart.setOption({
        title: {
          text: "广州市意见建议饼图",
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "right",
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: "50%",
            data: [
              { value: this.echartsData.replied.yiNum, name: "已回复" },
              { value: this.echartsData.replied.weiNum, name: "未回复" },
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      });
    },
    // 广州市意见建议柱状图
    BarChart() {
      let titleBox = [];
      let num = [];
      let num1 = [];
      this.echartsData.districtReply.forEach((item) => {
        titleBox.push(item.name);
        num.push(item.num);
        num1.push(item.num1);
      });
      var myChart = echarts.init(document.getElementById("tiaoxingtu"));
      var option;
      option = {
        // title: {
        //     text: '广州市意见建议柱状图',
        //     subtext: "点击柱型可查看相应详情",
        //      left: "center",
        //   },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: { left: "right" },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: titleBox,
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            name: "已回复",
            type: "bar",
            stack: "Ad",
            barWidth: 40,
            emphasis: {
              focus: "series",
            },
            data: num1,
          },
          {
            name: "未回复",
            type: "bar",
            stack: "Ad",
            barWidth: 40,
            emphasis: {
              focus: "series",
            },
            data: num,
          },
        ],
      };
      option && myChart.setOption(option);
    },
    // 广州市意见建议详细柱状图
    BarChart_XQ() {
      let titleBox = [];
      let weiNum = [];
      let llz = [];
      let xitong = [];
      let db = [];
      this.echartsData.replyClassList.forEach((item) => {
        titleBox.push(item.district);
        weiNum.push(item.weiNum);
        llz.push(item.llz);
        xitong.push(item.xitong);
        db.push(item.db);
      });
      var myChart = echarts.init(document.getElementById("tiaoxingtu_xq"));
      var option;
      option = {
        title: { text: "广州市意见建议详细柱状图", left: "center" },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: { left: "right" },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: titleBox,
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            name: "代表已回复",
            type: "bar",
            stack: "db",
            barWidth: 20,
            data: db,
          },
          {
            name: "站长已回复",
            type: "bar",
            stack: "zz",
            barWidth: 20,
            data: llz,
          },
          {
            name: "系统已回复",
            type: "bar",
            stack: "xt",
            barWidth: 20,
            data: xitong,
          },
          {
            name: "未回复",
            type: "bar",
            stack: "weihuif",
            barWidth: 20,
            data: weiNum,
          },
        ],
      };
      option && myChart.setOption(option);
    },
    // 修改时间范围
    handleTime(name) {
      var time = new Date();
      let Year = time.getFullYear(); /* 当前年份 */
      if (name == "最近三个月") {
        let startTime = time.toLocaleDateString(); /* 当前时间  */
        this.queryForm.startTime = startTime;
        time.setMonth(time.getMonth() + 3);
        this.queryForm.endTime = time.toLocaleDateString();
        this.showDisabled = true;
      } else if (name == "今年1~6月") {
        this.queryForm.startTime = `${Year}-01-01`;
        this.queryForm.endTime = `${Year}-06-30`;
        this.showDisabled = true;
      } else if (name == "今年7~12月") {
        this.queryForm.startTime = `${Year}-07-01`;
        this.queryForm.endTime = `${Year}-12-31`;
        this.showDisabled = true;
      } else if (name == "今年内") {
        this.queryForm.startTime = `${Year}-01-01`;
        this.queryForm.endTime = `${Year}-12-31`;
        this.showDisabled = true;
      } else if (name == "自定义") {
        this.queryForm.startTime = "";
        this.queryForm.endTime = "";
        this.showDisabled = false;
      } else if (name == "本届") {
        this.queryForm.startTime = "";
        this.queryForm.endTime = "";
        this.showDisabled = true;
      }
    },
    // 高级搜索
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    // 重置
    reset() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        timeRange: "本届",
      };
      this.showDisabled = true;
      this.fetchData();
    },
    moment,
    // 设置administrativeAreaId
    selectadministrativeAreas(state) {
      console.log("🤗🤗🤗, state =>", state);
      this.administrativeAreas.map((item) => {
        if (item.value === state) {
          this.listQuery.administrativeAreaId = item.value;
        }
      });
    },
    listAdministrativeAreas() {
      // 获取行政区县
      getAdministrativeAreas().then((response) => {
        this.administrativeAreas = response.data;
      });
    },
    // 获取乡镇街道
    liststreetTowns(administrativeAreaId) {
      getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
        }
      );
    },
    // 选择行政区域
    handleAdministrativeAreaChange(val) {
      // if (!val) return;
      this.queryForm.streetTownId = undefined;
      // this.selectadministrativeAreas(val);
      this.liststreetTowns(val);
    },
    acquireData() {
      if (this.queryForm.session == "第十五届") {
        this.echartsData = [
          [60, 70, 60] /*代表 已回复 */,
          [30, 70, 100] /*站长 已回复 */,
          [30, 60, 100] /*系统 已回复 */,

          [120, 200, 260] /* 已回复 */,
          [160, 120, 220] /* 未回复 */,

          [580] /* 已回复总数 */,
          [520] /* 未回复总数 */,
        ];
      }
      if (this.queryForm.session == "第十六届") {
        this.echartsData = [
          [80, 90, 60] /*代表 已回复 */,
          [50, 70, 40] /*站长 已回复 */,
          [30, 120, 100] /*系统 已回复 */,

          [160, 280, 200] /* 已回复 */,
          [100, 80, 120] /* 未回复 */,

          [640] /* 已回复总数 */,
          [300] /* 未回复总数 */,
        ];
      }
      if (this.queryForm.timeRange == "最近三个月") {
        this.echartsData = [
          [20, 10, 10] /*代表 已回复 */,
          [10, 30, 20] /*站长 已回复 */,
          [5, 10, 30] /*系统 已回复 */,

          [35, 50, 60] /* 已回复 */,
          [10, 25, 20] /* 未回复 */,

          [145] /* 已回复总数 */,
          [55] /* 未回复总数 */,
        ];
      }
      if (this.queryForm.timeRange == "今年1~6月") {
        this.echartsData = [
          [40, 60, 50] /*代表 已回复 */,
          [20, 20, 20] /*站长 已回复 */,
          [20, 10, 30] /*系统 已回复 */,

          [80, 90, 100] /* 已回复 */,
          [10, 40, 80] /* 未回复 */,

          [270] /* 已回复总数 */,
          [120] /* 未回复总数 */,
        ];
      }
      if (this.queryForm.streetTownId) {
        this.echartsData = [
          [80, 60, 60] /*代表 已回复 */,
          [80, 60, 20] /*站长 已回复 */,
          [80, 50, 30] /*系统 已回复 */,

          [240, 170, 110] /* 已回复 */,
          [120, 70, 80] /* 未回复 */,

          [520] /* 已回复总数 */,
          [270] /* 未回复总数 */,
        ];
      }

      // else {
      //   this.echartsData = [
      //     [5, 20, 50] /*代表 已回复 */,
      //     [15, 30, 60] /*站长 已回复 */,
      //     [20, 40, 70] /*系统 已回复 */,

      //     [40, 90, 180] /* 已回复 */,
      //     [10, 5, 20] /* 未回复 */,

      //     [270] /* 已回复总数 */,
      //     [35] /* 未回复总数 */,
      //   ];
      // }
      this.initRightColumn();
    },
    fetchData() {
      this.listLoading = true;
      // 获取数据
      // communityScheduleList(this.queryForm).then((res) => {
      //   console.log("🤗🤗🤗, res =>", res);
      //   this.list = res.rows;
      //   this.listLoading = false;
      //   this.total = res.total;
      //   this.pagination.total = res.total;
      // });
      this.initRightColumn();
    },

    // 切换页数
    changePageSize(current, pageSize) {
      this.queryForm.pageNum = current;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = current;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(current, pageSize) {
      this.queryForm.pageNum = current;
      this.pagination.current = current;
      this.fetchData();
    },

    handleCurrent(row) {
      console.log("row", row);
    },
  },
};
</script>
<style scoped>
#tiaoxingtu,
#bingtu,
#tiaoxingtu_xq {
  display: flex;
  justify-content: space-around;
}
.code_btn {
  margin-bottom: 10px;
}
.bar_title {
  margin: 0 auto;
  margin-bottom: -10px;
  z-index: 99;
}
.bar_title p {
  font-weight: bolder;
  text-align: center;
}
.bar_title span {
  text-align: center;
  display: block;
  width: 100%;
}
.p {
  display: flex;
  width: 100%;
}
.p > span {
  flex: 1;
}
</style>
