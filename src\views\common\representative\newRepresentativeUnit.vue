<template>
  <a-modal
    :title="orgTreeDialogTitle"
    :visible.sync="orgTreeDialogVisible"
    width="50%"
    destroyOnClose
    @cancel="close"
  >
    <a-tree
      ref="orgTree"
      :default-expanded-keys="expandedKeys"
      :replace-fields="replaceFields"
      :tree-data="orgTreeData"
      @select="onCheck"
      checkStrictly
    ></a-tree>

    <span slot="footer" class="dialog-footer">
      <a-button @click="close">取 消</a-button>
      <a-button style="padding-left: 10px;" type="primary" @click="confirm">确 定</a-button>
    </span>
  </a-modal>
</template>

<script>
import { findOOData } from "@/api/registrationMage/tableIng.js";
import { log } from "@antv/g2plot/lib/utils";
export default {
  data() {
    return {
      name: "",
      expandedKeys: [],
      searchValue: "",
      allChildData: [],
      selectedKeys: [],
      autoExpandParent: true,
      checked: [],
      orgTreeDialogVisible: false,
      orgTreeData: [],
      orgTreeDefaultKey: [],
      replaceFields: {
        title: "fullName",
        key: "id",
        children: "children",
      },
      isRadio: true,
      relType: {
        type: Number,
        default: 1,
      },
      noCheckKeys: [],
    };
  },
  props: {
    // checkIdentyData: {
    //   type: Array,
    //   required: true,
    // },
    orgTreeDialogTitle: {
      type: String,
      default: "选择单位",
    },
  },
  watch: {
    orgTreeDialogVisible(newVal) {
      if (newVal) {
        this.initOrgTree();
      } else {
        this.expandedKeys = [];
        this.checked = [];
        // this.checkIdentyData = [];
      }
    },
  },
  methods: {
    close() {
      this.orgTreeDialogVisible = false;
      this.checked = [];
    },
    initOrgTree() {
      findOOData().then((res) => {
        this.orgTreeData = res.data.data;
      });
    },

    // 树状选择
    onCheck(item, data) {
      if (data.node) {
        this.checked=[]
        let value = data.node._props.dataRef;
        this.checked.push(value);
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
    },
    confirm() {
      this.noCheckKeys = [];
      this.orgTreeDialogVisible = false;
      console.log(this.checked);
      this.$emit("confirm", this.checked);
      // this.checkIdentyData = [];
    },
  },
};
</script>
