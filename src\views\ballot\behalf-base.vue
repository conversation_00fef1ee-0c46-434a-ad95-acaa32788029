<template>
  <div class="table-container">

    <!-- <a-row style="margin-left:1%">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18 }">
        <a-col span="6">
          <a-form-model-item label="届次"
                             style="width:100%;">
            <a-select placeholder="请选择届次"
                      v-model="queryForm.jcDm"
                      allow-clear
                      style="width:100%;"
                      @change="search()">
              <a-select-option v-for="item in getPeriodList"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="行政区划"
                             prop="xzqhDm"
                             style="width:100%;">
            <a-select placeholder="请选择行政区划"
                      v-model="queryForm.xzqhDm"
                      allow-clear
                      style="width:100%;"
                      @change="search()">
              <a-select-option v-for="item in getAdministrativeStateList"
                               :key="item.xzqhDm"
                               :value="item.xzqhDm">{{
                  item.xzqhmc
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-form-model>
      <a-col :md="6"
             :sm="24"
             style="float: right;">
        <span style="float: right; margin-top: 3px;">
          <a-button type="primary"
                    @click="search()">搜索</a-button>
          <a-button style="margin-left: 12px;"
                    @click="reset"
                    class="pinkBoutton">重置</a-button>
        </span>
      </a-col>
    </a-row> -->

    <SearchForm @onReset="reset" @onSearch="search" :noMore="true">
      <template v-slot:topSearch>
        <SingleSelect :title="'届次'" :selectList="getPeriodList"  :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm" />
        <SingleSelect :title="'行政区划'" :selectList="getAdministrativeStateList" :showName="'xzqhmc'" :showValue="'xzqhDm'" :value.sync="queryForm.xzqhDm" />
      </template>
    </SearchForm>

    <a-row style="margin: 10px 0px 10px 8px;">
      <a-col :span="6">
        <a-button type="primary"
                  style="margin-left: 12px;"
                  :loading="downloadLoading"
                  @click="download">导出</a-button>
      </a-col>
    </a-row>
    <a-row style="margin-top:20px">
      <standard-table :columns="columns"
                      rowKey="项目"
                      :bordered="true"
                      :dataSource="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"
                      @selectedRowChange="onSelectChange"
                      @tableClick="tableClick"></standard-table>
    </a-row>
    <!-- 详情 -->
    <a-modal :title="modalTitle"
             :visible.sync="dialogFormVisible"
             width="1000px"
             @cancel="close"
             destroyOnClose>
      <a-spin :spinning="listLoading"
              :indicator="indicator">
        <standard-table :columns="columnsData"
                        rowKey="db_id"
                        :dataSource="dataSourceData"
                        :pagination="newPagination">
        </standard-table>
      </a-spin>
      <!-- 定义了插槽 -->
      <slot slot="footer"
            name="userFooter">
        <a-button type="primary"
                  @click="close">关闭</a-button>
      </slot>
    </a-modal>
  </div>
</template>
<script>
import { myPagination } from "@/mixins/pagination.js";
import {
  queryBasicCaseDetail,
  getPeriod,
  getSex,
  getAdministrativeState
} from "@/api/election.js";
import StandardTable from "@/components/table/StandardTable";
import { instance_1 } from "@/api/axiosRq";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
export default {
  // 预备人选名册
  components: { 
    StandardTable, 
    SingleSelect,
    SearchForm,
    SingleSearch, 
  },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />, //转动的圈圈
      downloadLoading: false,
      listLoading: false,
      jcDm: '',
      modalTitle: '',
      tableKey: [],
      tableData: [],
      advanced: false,
      dialogFormVisible: false,
      periods: [
        // 议案届别
        { id: 16, name: "十五届六次" },
        { id: 15, name: "十五届一次" },
        { id: 1, name: "十五届二次" },
        { id: 2, name: "十五届三次" },
        { id: 3, name: "十五届四次" },
        { id: 4, name: "十五届五次" },
      ],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        jcDm: '',/* 届次id */
        xzqhDm: ''/* 行政区划id */
      },
      // 分页器设置
      newPagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      queryFormData: {
        pageNum: 1,
        pageSize: 10,
        jcDm: undefined,/* 届次id */
      },
      columns: [
        {
          title: "项目",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "项目",
        },
        {
          title: "实选人数",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "实选人数",
        },
        {
          title: "性别",
          align: "center",
          width: 90,
          ellipsis: true,
          children: [
            {
              title: '男',
              align: "center",
              dataIndex: '男',
              width: 100,
            },
            {
              title: '女',
              align: "center",
              dataIndex: '女',
              width: 100,
            },
          ],
        },
        {
          title: "年龄(周岁)",
          align: "center",
          width: 320,
          ellipsis: true,
          children: [
            {
              title: '35岁以下',
              align: "center",
              dataIndex: '35以下',
              width: 100,
            },
            {
              title: '36岁至55岁',
              align: "center",
              dataIndex: '36至55',
              width: 120,
            },
            {
              title: '56岁以上',
              align: "center",
              dataIndex: '56以上',
              width: 100,
            },
          ],
        },
        {
          title: "政治面貌",
          align: "center",
          width: 300,
          ellipsis: true,
          children: [
            {
              title: '中共党员',
              align: "center",
              dataIndex: '中共党员',
              width: 100,
            },
            {
              title: '民主党派',
              align: "center",
              dataIndex: '民主党派',
              width: 100,
            },
            {
              title: '群众',
              align: "center",
              dataIndex: '群众',
              width: 100,
            },
          ],
        },
        {
          title: "民族",
          align: "center",
          width: 200,
          ellipsis: true,
          children: [
            {
              title: '汉族',
              align: "center",
              dataIndex: '汉族',
              width: 100,
            },
            {
              title: '少数民族',
              align: "center",
              dataIndex: '少数民族',
              width: 100,
            },
          ],
        },
        {
          title: "文化程度",
          align: "center",
          width: 500,
          ellipsis: true,
          children: [
            {
              title: '研究生及以上',
              align: "center",
              dataIndex: '研究生及以上',
              width: 100,
            },
            {
              title: '大学本科',
              align: "center",
              dataIndex: '大学本科',
              width: 100,
            },
            {
              title: '大专及高职',
              align: "center",
              dataIndex: '大专及高职',
              width: 100,
            },
            {
              title: '中专，职高及高中',
              align: "center",
              dataIndex: '中专职高及高中',
              width: 100,
            },
            {
              title: '初中及以下',
              align: "center",
              dataIndex: '初中及以下',
              width: 100,
            },
          ],
        },
      ],
      columnsData: [
        {
          title: "姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "DBXM",
          customRender: (text, record, index) => {
            return text || '/'
          }
        },
        {
          title: "性别",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "SEX",
          customRender: (text, record, index) => {
            return text || '/'
          }
        },
        {
          title: "出生日期",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "CSRQ",
          customRender: (text, record, index) => {
            return text || '/'
          }
        },
        {
          title: "政治面貌",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "PSN",
          customRender: (text, record, index) => {
            return text || '/'
          }
        },
        {
          title: "民族",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "MZMC",
          customRender: (text, record, index) => {
            return text || '/'
          }
        },
        {
          title: "学历",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "XLMC",
          customRender: (text, record, index) => {
            return text || '/'
          }
        },
        {
          title: "所在联组",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "DBTMC",
          customRender: (text, record, index) => {
            return text || '/'
          }
        },
      ],
      dataSource: [],
      dataSourceData: [],
      selectedRows: [],
      getPeriodList: [], /* 获取届次 */
      getSexList: [],/* 获取性别 */
      getNationList: [],/* 获取民族 */
      getAdministrativeStateList: [],/* 行政区划下拉数据 */
      getPoliticalList: [],/* 获取出党派下拉数据列表 */
      getRecommendList: [],/* 获取出推荐单位下拉数据列表 */
      getSynthesizeList: [],/* 获取出综合构成下拉数据列表 */
      getOccupationList: [],/* 获取出职业构成下拉数据列表 */
      getEducationStateList: [],/* 获取在职教育学历、全日制教育学历下拉数据列表 */
      selectData: [ /*  是：1     否：0 */
        { id: '', name: '全部' },
        { id: '1', name: '是' },
        { id: '0', name: '否' }
      ]
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "投票选举管理");
    this.$store.dispatch("navigation/breadcrumb2", "代表基本情况统记表");
    this.selectList()
  },
  methods: {
    async selectList () {
      let res = await getPeriod()
      if (res.code == "0000") {
        this.getPeriodList = res.data
        this.queryForm.jcDm = res.data[0].jcDm //改过
        this.queryFormData.jcDm = res.data[0].jcDm
        this.jcDm = res.data[0].jcDm
      }
      let res1 = await getSex()
      if (res1.code == "0000") {
        this.getSexList = res1.data
      }
      // let res2=await getNation()
      //   if(res2.code=="0000"){
      //     this.getNationList=res2.data
      //   }
      //  let res3=await getPolitical()
      //   if(res3.code=="0000"){
      //     this.getPoliticalList=res3.data
      //   }  
      // let res4=await getRecommend()
      //   if(res4.code=="0000"){
      //     this.getRecommendList=res4.data
      //   } 
      //  let res5=await getSynthesize()
      //   if(res5.code=="0000"){
      //     this.getSynthesizeList=res5.data
      //   }  
      //  let res6=await getOccupation()
      //   if(res6.code=="0000"){
      //     this.getOccupationList=res6.data
      //   }  
      //  let res7=await getEducationState()
      //   if(res7.code=="0000"){
      //     this.getEducationStateList=res7.data
      //   }     
      let res8 = await getAdministrativeState()
      if (res8.code == "0000") {
        this.getAdministrativeStateList = res8.data
        this.getAdministrativeStateList.unshift({ xzqhmc: '全部', xzqhDm: '' })
      }
      this.fetchData();
    },
    // 获取数据
    fetchData () {
      this.TBloading = true;
      instance_1({
        url: "/representative/behalfStatistics/queryBasicCase",
        method: "get",
        params: this.queryForm
      }).then((res) => {
        if (res.data.code == '0000') {
          this.dataSource = res.data.data.slice(1);

          this.pagination.total = res.data.total;
          this.TBloading = false;
        }
      })
    },
    // 导出
    download () {
      this.downloadLoading = true;
      instance_1({
        url: "/representative/behalfStatistics/exportBasicCase",
        method: "get",
        responseType: "blob",
        params: {
          jcDm: this.queryForm.jcDm,
          xzqhDm: this.queryForm.xzqhDm,
        }
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `代表基本情况统记表.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000)
      })
    },
    close () {
      this.dialogFormVisible = false;
    },
    search () {
      this.jcDm = this.queryForm.jcDm
      this.fetchData()
    },
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
      }
      //  this.queryForm.jcDm=this.jcDm
      //  this.queryFormData.jcDm=this.jcDm
      this.fetchData();
    },
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    async tableClick (event, record) {
      let tableHeader = event.target.__vue__._props.column.dataIndex
      let formdata = this.queryFormData;
      this.queryFormData = {
        pageNum: 1,
        pageSize: 10,
        jcDm: formdata.jcDm,/* 届次id */
        xzqhDm: formdata.xzqhDm, //
      },
        this.queryFormData.pageNum = 1
      this.queryFormData.pageSize = 10
      this.newPagination.current = 1;
      this.newPagination.pageSize = 10;

      if (record.项目 == "本级人大代表") {
        this.modalTitle = "代表基本情况明细数据——统计条件：" + tableHeader
        this.dialogFormVisible = true;
        if (tableHeader == "实选人数") {
        } else if (tableHeader == "男") {
          this.queryFormData.xbDm = '1'
        } else if (tableHeader == "女") {
          this.queryFormData.xbDm = '2'
        } else if (tableHeader == "35以下") {
          this.queryFormData.nl = '35'
        } else if (tableHeader == "36至55") {
          this.queryFormData.nl = '40'
        } else if (tableHeader == "56以上") {
          this.queryFormData.nl = '56'
        } else if (tableHeader == "中共党员") {
          this.queryFormData.zzmmDm = '01'
        } else if (tableHeader == "民主党派") {
          this.queryFormData.zzmmDm = '10'
        } else if (tableHeader == "群众") {
          this.queryFormData.zzmmDm = '13'
        } else if (tableHeader == "汉族") {
          this.queryFormData.mzDm = '01'
        } else if (tableHeader == "少数民族") {
          this.queryFormData.mzDm = '10'
        } else if (tableHeader == "研究生及以上") {
          this.queryFormData.xlDm = '10'
        } else if (tableHeader == "大学本科") {
          this.queryFormData.xlDm = '20'
        } else if (tableHeader == "大专及高职") {
          this.queryFormData.xlDm = '30'
        } else if (tableHeader == "中专职高及高中") {
          this.queryFormData.xlDm = '50'
        } else if (tableHeader == "初中及以下") {
          this.queryFormData.xlDm = '70'
        }
        if (this.jcDm !== "") {
          this.queryFormData.jcDm = this.jcDm
        }

      }
      this.newFetchData()
    },
    async newFetchData () {
      this.listLoading = true;
      let res = await queryBasicCaseDetail(this.queryFormData)
      if (res.code == '200') {
        this.dataSourceData = res.rows
        this.newPagination.total = res.total;
        this.listLoading = false;
      }
    },

    // 切换页数
    changePageSize (pageNum, pageSize) {
      this.queryFormData.pageNum = pageNum;
      this.queryFormData.pageSize = pageSize;
      this.newPagination.pageSize = pageSize;
      this.newPagination.current = pageNum;
      this.newFetchData();
    },

    // 切换页码
    handleCurrentChange (pageNum, pageSize) {
      this.queryFormData.pageNum = pageNum;
      this.newPagination.current = pageNum;
      this.newFetchData();
    },
    onSelectChange (key, data) {
      this.tableKey = key
      this.tableData = data
    }
  },
};
</script>
<style scoped>
.formBox {
  padding: 10px;
}
</style>
