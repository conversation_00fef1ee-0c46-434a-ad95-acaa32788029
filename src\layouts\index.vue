<template>
  <div class="app-wrapper" :class="classObj">
    <div v-if="'horizontal' === layout || 'theme3' === layout" class="layout-container-horizontal" :class="{
      fixed: header === 'fixed',
      'no-tags-view': tagsView === 'false' || tagsView === false,
    }">
      <div :class="header === 'fixed' ? 'fixed-header' : ''">
        <top-bar />
        <div v-if="tagsView === 'true' || tagsView === true" :class="{ 'tag-view-show': tagsView }">
          <byui-main :class="classObjMain">
            <tags-view />
          </byui-main>
        </div>
      </div>
      <byui-main class="main-padding" :class="classObjMain">
        <!-- 左侧 -->
        <side-bar class="qxj-side" />

        <!-- 右侧顶部面包屑 -->
        <!-- <div class="breadCrumbs">
          <a-col class="breadcrumb-box">
            <a-breadcrumb>
              <a-breadcrumb-item>
                <router-link :to="{ path: '/' }">{{navigation.text}}</router-link>
              </a-breadcrumb-item>
              <a-breadcrumb-item v-if="navigation.text1">{{
                navigation.text1
              }}</a-breadcrumb-item>
              <a-breadcrumb-item v-if="navigation.text2">{{
                navigation.text2
              }}</a-breadcrumb-item>
              <a-breadcrumb-item v-if="navigation.text3">{{
                navigation.text3
              }}</a-breadcrumb-item>
              <a-breadcrumb-item v-if="navigation.text4">{{
                navigation.text4
              }}</a-breadcrumb-item>  
                <a-breadcrumb-item> 
                    <a @click="goBack">返回上一页 </a>
            </a-breadcrumb-item> 
            </a-breadcrumb>
            
          </a-col>
        </div> -->
        <!-- 右侧顶部面包屑 -->
        <!-- <div class="breadCrumbs">
          <a-col class="breadcrumb-box">
            <div class="breadcrumbBox">
              <div class="breadcrumb-item">
                <a @click="goFirstRouter">{{ navigation.text }}</a>
              </div>
              <div class="breadcrumb-item">
                <span v-for="(item, index) in barList.matched" :key="index + item.path" v-show=" item.meta.title">
                   {{ item.meta.title }}</span> 
              </div> 
              <div class="breadcrumb-item">
                <a @click="goBack" style="color:rgb(24, 144, 255)">«返回上一页 </a>
              </div>

            </div>
          </a-col>
        </div> -->
        <!-- 页签 -->
        <Tabs></Tabs>
        <!-- 右侧主要部分 -->
        <app-main  class="app-main" />
      </byui-main>
    </div>
    <div v-else class="layout-container-vertical" :class="{
      fixed: header === 'fixed',
      'no-tags-view': tagsView === 'false' || tagsView === false,
    }">
      <div v-if="device === 'mobile' && collapse === false" class="mask" @click="handleFoldSideBar" />
      <side-bar />
      <byui-main :class="collapse ? 'is-collapse-main' : ''">
        <div :class="header === 'fixed' ? 'fixed-header' : ''">
          <nav-bar />
          <tags-view v-if="tagsView === 'true' || tagsView === true" />
        </div>
        <app-main />
      </byui-main>
    </div>
    <byui-back-to-top transition-name="fade" />
  </div>
</template>

<script>
import Tabs from "@/layouts/components/Tabs/Tabs.vue";

import { AppMain, NavBar, SideBar, TagsView, TopBar } from "./components";
import ByuiMain from "@/components/ByuiMain";
import ByuiBackToTop from "@/components/ByuiBackToTop";
import { mapGetters } from "vuex";
import { tokenName } from "@/config/settings";
import ResizeMixin from "./mixin/Resize";
import getters from "../store/getters";

export default {
  name: "Layout",
  components: {
    TopBar,
    NavBar,
    SideBar,
    AppMain,
    ByuiMain,
    TagsView,
    ByuiBackToTop,
    Tabs,
  },
  mixins: [ResizeMixin],
  data() {
    return { OldRoute: '' };
  },
  computed: {
    ...mapGetters(["layout", "tagsView", "collapse", "header", "device"]),
    navigation() {
      return this.$store.state.navigation.titleText;
    },
    barList() {
      return this.$route;
    },
    classObj() {
      return {
        mobile: this.device === "mobile",
        qxjTheme: this.layout === "horizontal",
        eleTheme: this.layout === "vertical",
        thrTheme: this.layout === "theme3",
      };
    },
    classObjMain() {
      return {
        qxj_main: this.layout === "horizontal",
        thr_main: this.layout === "theme3",
      };
    },
  },
  created() {},
  watch: {
    barList(newVal, oldVal) {
      this.OldRoute = oldVal;
    }
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener(
        "storage",
        (e) => {
          if (e.key === tokenName || e.key === null) window.location.reload();
          if (e.key === tokenName && e.value === null) window.location.reload();
        },
        false
      );
    });
  },
  methods: {
    handleFoldSideBar() {
      this.$store.dispatch("settings/foldSideBar");
    },
    goBack() {
       this.$router.go(-1); 
    },
    goFirstRouter() {
      var Indexpath = '';
      if (this.navigation.text == '平台门户') {
        Indexpath = '/platformIndex/platformIndex'
      } else if (this.navigation.text == '联系人民群众') {
        Indexpath = '/Proelemasses/site/index'
      } else if (this.navigation.text == '联系人大常委会') {
        Indexpath = '/inform1/InformList'
      } else if (this.navigation.text == '履职活动组织') {
        Indexpath = '/dutyActivityIndex/index'
      } else if (this.navigation.text == '代表建议管理') {
        Indexpath = '/recommendedMage/recommendedMageIndex'
      } else if (this.navigation.text == '履职登记管理') {
        Indexpath = '/registrationMage/registrationMage'
      } else if (this.navigation.text == '代表选举管理') {
        Indexpath = '/electoralMage/RepresentIndex'
      } else if (this.navigation.text == '代表信息管理') {
        Indexpath = '/informationMage/workIndex'
      } else if (this.navigation.text == '在线学习培训') {
        Indexpath = '/organizationIndex/organizationTraining'
      } else if (this.navigation.text == '数据驾驶舱') {
        Indexpath = '/region/region'
      }
      this.$router.push({
        path: Indexpath //指定模块首页
      });

    }
  },
};
</script>

<style lang="scss" scoped>
@mixin fix-header {
  // height: 70px;
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: $base-z-index - 2;
  width: 100%;
  // overflow: hidden;
}

.app-wrapper {
  position: relative;
  width: 100%;
  height: 100%;

  .layout-container-horizontal {
    position: relative;

    &.fixed {
      padding-top: 70px;
    }

    &.fixed.no-tags-view {
      padding-top: 56px;
    }

    ::v-deep {
      .byui-main {
        // width: 88%;
      }

      .fixed-header {
        @include fix-header;
      }

      .tag-view-show {
        background: $base-color-white;
        box-shadow: $base-box-shadow;
      }

      .nav-bar-container {
        .fold-unfold {
          display: none;
        }
      }

      .main-padding {
        margin-top: 48px;
        margin-left: 210px;
        .app-main {
          flex: 1;
        }

        .qxj-side {
          top: 118px;
          z-index: 1;
        }
      }
    }
  }

  .layout-container-vertical {
    position: relative;

    .mask {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: $base-z-index - 1;
      width: 100%;
      height: 100vh;
      overflow: hidden;
      background: #000;
      opacity: 0.5;
    }

    &.fixed {
      padding-top: 96px;
    }

    &.fixed.no-tags-view {
      padding-top: 56px;
    }

    .byui-main {
      position: relative;
      min-height: 100%;
      margin-left: $base-left-menu-width;
      background: #f6f8f9;
      transition: all 0.2s ease-in-out;

      ::v-deep {
        .fixed-header {
          @include fix-header;

          left: $base-left-menu-width;
          width: $base-right-content-width;
          box-shadow: $base-box-shadow;
          transition: all 0.2s ease-in-out;
        }

        .nav-bar-container {
          position: relative;
          box-sizing: border-box;
        }

        .tags-view-container {
          box-sizing: border-box;
          padding-right: 5px;
          padding-left: 5px;
        }
      }

      &.is-collapse-main {
        margin-left: $base-left-menu-width-min;

        ::v-deep {
          .fixed-header {
            left: $base-left-menu-width-min;
            width: calc(100% - 65px);
          }
        }
      }
    }
  }

  /* 手机端开始 */
  &.mobile {
    ::v-deep {

      .el-pager,
      .el-pagination__jump {
        display: none;
      }

      .layout-container-vertical {
        .el-scrollbar.side-bar-container.is-collapse {
          width: 0;
        }

        .byui-main {
          width: 100%;
          margin-left: 0;
        }
      }

      .byui-main {
        .fixed-header {
          left: 0 !important;
          width: 100% !important;
        }
      }
    }
  }

  /* 手机端结束 */
  .qxj_main {
    width: auto;
    margin-left: 210px;
  }

  .thr_main {
    width: auto;
    margin-left: 225px;
    margin-right: 0;
    // margin-top: 60px;
    background: transparent;
  }
}

.breadCrumbs {
  height: 40px;
  position: absolute;
  left: 210px;
  top: 118px;
  width: auto;
}

.breadcrumb-box {
  z-index: 99;
  background-color: #fff;
  width: 100%;
  position: fixed;
  display: block;
  height: 36px;
  line-height: 36px;
  box-sizing: border-box;
  font-family: pingFang-R;
}

.breadcrumb-box .breadcrumbBox {
  display: flex;
}

.breadcrumb-item a,
span {
  color: rgba(0, 0, 0, 0.45);
  padding: 0 10px;
  border-right: 1px solid rgba(0, 0, 0, 0.45);
}

// .breadcrumb-item span{
//   color:rgba(0, 0, 0, 0.45);
//     padding:0 10px;
//   border-right:1px solid rgba(0, 0, 0, 0.45);
// }
// .ant-breadcrumb:last-child {
//   color: #db3046;
// }
.ant-breadcrumb {
  line-height: 36px;
  // font-size: 14px;
  @include add-size($font_size_16);
  text-indent: 10px;
  font-weight: 400;
}

/* 公共字体类 */
.font-R {
  font-family: pingFang-R;
}

.font-M {
  font-family: pingFang-M;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #d92b3e;
  border-color: #d92b3e;
}

.el-button--primary {
  background-color: #d92b3e;
  border-color: #d92b3e;
}

// 全局禁用按钮
::v-deep .ant-btn-primary[disabled] {
  color: rgba(0, 0, 0, 0.65) !important;
  background-color: #aaa !important;
  border-color: #aaa !important;
}
</style>
