<template>
  <div class="table-container">
    <a-collapse-panel
      :header="headerTitle"
      style="
        margin: 0px 20px 40px 20px;
        padding: 10px 0px;
        background: rgba(190, 64, 61, 0.1);
        margin-top: -20px;
        padding-bottom: 12px;
        border-bottom: 1px solid #cfcece;
      "
    />
    <activityFormModel
      :form="form"
      @close="$router.go(-1)"
      :oper="oper"
      :fromType="fromType"
    ></activityFormModel>
  </div>
</template>

<script>
import { getById } from "@/api/communityscheduleactivity";
import activityFormModel from "./activityFormModel";
export default {
  components: { activityFormModel },
  data() {
    return {
      headerTitle: "待办事项",
      form: {},
      oper: "view",
      fromType: null,
    };
  },
  created() {
    const id = this.$route.query.id;
    this.getById(id);
    const oper = this.$route.query.oper;
    if (oper != null && oper != undefined) {
      this.oper = oper;
      if (oper == "view") {
        this.headerTitle = "查看待办事项";
      } else if (oper == "update") {
        this.headerTitle = "编辑待办事项";
      } else if (oper == "audit") {
        this.headerTitle = "送审、审核待办事项";
      }
    }
    this.$route.query.fromType
      ? (this.fromType = this.$route.query.fromType)
      : (this.fromType = null);
  },
  methods: {
    getById(id) {
      getById({ id: id }).then((res) => {
        this.form = res.data;
      });
    },
  },
};
</script>

<style></style>
