import { instance_yajy } from "@/api/axiosRq";
// 系统管理-内容分类关联管理
// 获取数据
export function findPageOrgContentTypes(form) {
  return instance_yajy({
    url: `/api/v1/system/orgContenttype/findPageOrgContentTypes`,
    method: "post",
    data: form,
  });
}
// 新增
export function addContentType(form) {
  return instance_yajy({
    url: `/api/v1/system/orgContenttype/addContentType`,
    method: "post",
    data: form,
  });
}
// 删除
export function removeContentType(form) {
  return instance_yajy({
    url: `/api/v1/system/orgContenttype/remove`,
    method: "post",
    params: form,
  });
}
