import request from "@/utils/requestTemp";

export function getOrgTreeByAdministrativeAreaId(query) {
  return request({
    url: '/administrativeAreaDbTree/getOrgTreeByAdministrativeAreaId',
    method: 'get',
    params: query
  })
}

export function settingAdministrativeAreaOrgTree(data) {
  return request({
    url: '/administrativeAreaDbTree/settingOrgTree',
    method: 'POST',
    data: data
  })
}
