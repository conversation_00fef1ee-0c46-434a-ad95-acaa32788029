<template>
  <div class="table table-container">
    <SearchForm @onReset="reset" @onSearch="search" :noMore="true">
      <template v-slot:topSearch>
        <SingleSearch @onEnter="handleEnter" :title="'代表姓名'" :value.sync="queryForm.userName" />
        <SingleSearch @onEnter="handleEnter" :title="'关键字'" :value.sync="queryForm.keyword" />
        <TimeRangePicker :startTimeValue.sync="queryForm.startTime" :endTimeValue.sync="queryForm.endTime" :title="'时间范围'" />
      </template>
    </SearchForm>

    <a-spin :indicator="indicator"
            :spinning="loading">
      <standard-table :pagination="pagination"
                      row-key="id"
                      :columns="columns"
                      :loading="TBloading"
                      :data-source="list">
      </standard-table>
    </a-spin>
  </div>
</template>

<script>
import { myPagination } from "@/mixins/pagination.js";
import {
  dbBaseInfoUpdateLogList,
} from "@/api/dbxx";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
import TimeRangePicker from '@/components/TimeRangePicker/index';

export default {
  mixins: [myPagination],
  components: {
    SingleSelect,
    SearchForm,
    SingleSearch,
    TimeRangePicker,
  },
  data() {
        return {
            list: [],
            indicator: <a-icon type="loading" style="font-size: 24px" spin />,
            loading: false, // 高级搜索展开标记，默认为 false 不展开
            TBloading: false,
            dateRange: [],
            queryForm: {
              startTime: '',
              endTime: '',
              pageNum: 1,
              pageSize: 10,
              userName: ''
            },
            columns: [
              {
                title: "代表姓名",
                align: "center",
                width: 180,
                ellipsis: true,
                dataIndex: "userName",
                customRender: (text, record, index) => {
                    if (text) {
                    return text;
                    } else {
                    return "/";
                    }
                },
              },
              {
                align: "center",
                title: "修改内容",
                width: 180,
                ellipsis: true,
                dataIndex: "content",
                customRender: (text, record, index) => {
                  if (text) {
                    return text;
                  } else {
                    return "/";
                  }
                },
              },
              {
                align: "center",
                title: "修改时间",
                width: 180,
                ellipsis: true,
                dataIndex: "createTime",
                customRender: (text, record, index) => {
                  if (text) {
                    return text;
                  } else {
                    return "/";
                  }
                },
              }
            ],
            searchDebounced: _.debounce(this.search, 500),
        }
  },
  created() {
      this.fetchData()
  },
  methods: {
      handleEnter() {  
        this.searchDebounced();   
      },
      fetchData () {
        this.loading = true;
        dbBaseInfoUpdateLogList(this.queryForm).then(res => {
          this.loading = false;
          this.list = res.rows;
          this.pagination.total = res.total;
        })
      },
      search () {
        this.queryForm.pageNum = 1
        this.pagination.current = 1
        this.fetchData()
      },
      reset () {
        this.queryForm = {
          startTime: '',
          endTime: '',
          pageNum: 1,
          pageSize: 10,
          userName: ''
        };
        this.dateRange = [];
        this.pagination.current = 1;
        this.pagination.pageSize = 10;
        this.fetchData()
      },
  }
}
</script>

<style>

</style>
