<template>
  <div class="sticky-container">
    <byui-sticky :sticky-top="100">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="150">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="200">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="250">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="300">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="350">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="400">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="450">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="500">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="550">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="600">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <byui-sticky :sticky-top="650">
      <el-alert title="我会吸附" type="success" :closable="false"></el-alert>
    </byui-sticky>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
    <div>sticky</div>
  </div>
</template>

<script>
import ByuiSticky from "@/components/ByuiSticky";

export default {
  name: "Sticky",
  components: { ByuiSticky },
  data() {
    return {
      time: "",
      url: "",
      platforms: ["a-platform"],
      platformsOptions: [
        { key: "a-platform", name: "platformA" },
        { key: "b-platform", name: "platformB" },
        { key: "c-platform", name: "platformC" },
      ],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
};
</script>
<style lang="scss" scoped>
.sticky-container {
  div {
    height: 40px;
    line-height: 40px;
  }
}
</style>
