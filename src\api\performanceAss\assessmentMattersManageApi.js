import { instance_yajy } from "@/api/axiosRq";
// 考核事项管理列表
// queryForm 请求搜索数据
export function assQueryListPage(queryForm) {
  return instance_yajy({
    url: `/api/v1/appraise/largeItem/queryListPage`,
    method: "post",
    data: queryForm,
  });
}
// 删除数据
export function deleteByIdData(largeItemId) {
  let formData = new FormData();
  formData.append("largeItemId", largeItemId);
  return instance_yajy({
    url: `/api/v1/appraise/largeItem/deleteById`,
    method: "post",
    data: formData,
  });
}
// 新增数据
export function saveOrUpdate(form) {
  return instance_yajy({
    url: `/api/v1/appraise/largeItem/saveOrUpdate`,
    method: "post",
    data: form,
  });
}
// 设置截止时间
export function updateEndDate(form) {
  return instance_yajy({
    url: `/api/v1/appraise/smallItem/updateEndDate`,
    method: "post",
    params: form,
  });
}
// 获取工作细项管理列表
export function queryListByLargeItemId(form) {
  return instance_yajy({
    url: `/api/v1/appraise/smallItem/queryListByLargeItemId`,
    method: "post",
    data: form,
  });
}
// 细项新增/修改
export function smallItemSaveOrUpdate(form) {
  return instance_yajy({
    url: `/api/v1/appraise/smallItem/saveOrUpdate`,
    method: "post",
    data: form,
  });
}
// 删除获取数据
export function smallItemDeleteById(id) {
  return instance_yajy({
    url: `/api/v1/appraise/smallItem/deleteById`,
    method: "post",
    params: { id },
  });
}
// 获取细则数据
export function smallItemGetById(id) {
  let form = new FormData();
  form.append("id", id);
  return instance_yajy({
    url: `/api/v1/appraise/smallItem/getById`,
    method: "post",
    data: form,
  });
}
// 获取截止时间，申述截止时间
export function getEndDate(period) {
  return instance_yajy({
    url: `/api/v1/appraise/smallItem/getEndDate`,
    method: "post",
    params: { period },
  });
}
