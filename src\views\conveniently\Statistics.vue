<template>

  <!-- 详情页内容 -->
  <a-modal title="随手拍分类统计结果"
           :visible.sync="visible"
           width="80%"
           @cancel="close"
           :footer="null">
    <div>
      <a-row class="formBox">
        <a-form-model ref="Forms"
                      :model="queryForm"
                      :rules="rules"
                      layout="inline">
          <a-col span="6"
                 offset="0">
            <a-form-model-item label="时间范围">
              <a-select style="width:200px"
                        v-model="queryForm.timeRange"
                        placeholder="请选择"
                        @change="handleTime">
                <a-select-option v-for="item in timeScope"
                                 :key="item.name"
                                 :value="item.name">{{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-item label="开始时间"
                         prop="beginTime">
              <a-date-picker v-model="queryForm.beginTime"
                             :disabled="showDisabled"
                             allow-clear
                             value-format="YYYY-MM-DD "
                             placeholder="选择开始时间"
                             style="width: 100%;"
                             :disabled-date="
                  (current) =>
                    current && queryForm.endTime
                      ? current.valueOf() >=
                      moment(new Date(queryForm.endTime)).valueOf()
                      : false
                "></a-date-picker>
            </a-form-item>
          </a-col>
          <a-col span="6">
            <a-form-item label="结束时间"
                         prop="endTime">
              <a-date-picker v-model="queryForm.endTime"
                             allow-clear
                             :disabled="showDisabled"
                             value-format="YYYY-MM-DD"
                             placeholder="选择结束时间"
                             style="width: 100%;"
                             :disabled-date="
                  (current) =>
                    current && queryForm.beginTime
                      ? moment(new Date(queryForm.beginTime)).valueOf() >=
                      current.valueOf()
                      : false
                "></a-date-picker>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item>
              <a-button type="primary"
                        style="margin-left: 12px;"
                        @click="fetchData">搜索</a-button>
              <a-button style="margin-left: 12px;"
                        @click="reset"
                        class="pinkBoutton">重置</a-button>
              <a-button type="primary"
                        style="margin-left: 12px; margin-top: 2px"
                        @click="download"
                        :loading="downloadLoading">导出</a-button>
            </a-form-model-item>
          </a-col>

        </a-form-model>
      </a-row>
      <a-row>
        <a-spin :spinning="listLoading">
          <standard-table :columns="columns"
                          :rowKey="(record, index) => { return record.USER_ID; }"
                          :loading="TBloading"
                          :dataSource="dataSource"
                          :pagination="pagination">
            <!-- :selectedRows.sync="selectedRows"
        @selectedRowChange="onSelectChange" -->
          </standard-table>
        </a-spin>
      </a-row>
    </div>
    <!-- 定义了插槽 -->
    <slot slot="footer">
      <!-- <a-button style="padding-left: 10px" type="primary" @click="confirm">确 定</a-button>  
            <a-button type="primary" @click="close">关闭</a-button>  -->
    </slot>

  </a-modal>
</template>
<script>
import viewArticleDetails from "@/views/common/viewArticleDetails.vue";
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
import { instance_1 } from "@/api/axiosRq";
import { log } from '@antv/g2plot/lib/utils';
export default {
  components: { StandardTable, viewArticleDetails },
  // 引入分页器配置
  mixins: [myPagination],
  name: "Statistics",
  data () {
    return {
      TBloading: false,
      listLoading: false,
      rules: {
        beginTime: [{ required: true, message: "请选择开始时间", trigger: "blur" }],
        endTime: [{ required: true, message: "请选择结束时间", trigger: "blur" }],
      },
      timeScope: [
        // 议案届别 
        // { id: 2, name: "最近三个月" },
        { id: 3, name: "今年1~6月" },
        { id: 4, name: "今年7~12月" },
        { id: 5, name: "今年内" },
        { id: 6, name: "自定义" },
      ],
      showDisabled: false,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        timeRange: "今年1~6月",
        endTime: null,
        beginTime: null
      },
      inviteRangeDesc: '',
      //选中tableKey
      tableKey: [],
      //tableData
      tableData: [],
      visible: false,
      dataSource: [],
      selectedRows: [],
      articleId: '',
      indexNum: 1,
      //列头 
      columns: [
        // {
        //   title: "时间",
        //   align: "center",
        //   ellipsis: true,
        //   dataIndex: "USERNAME",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        {
          title: "序号",
          key: "index",
          align: "center",
          // width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            // `${(this.indexNum - 1) * this.indexNum + index + 1}`
            `${(this.queryForm.pageNum - 1) * this.queryForm.pageSize + index + 1}`,
        },
        {
          title: "意见分类",
          align: "center",
          ellipsis: true,
          dataIndex: "commentCategory",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "数量",
          align: "center",
          ellipsis: true,
          dataIndex: "count",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        //      {
        //   title: "学习开始时间",
        //   align: "center",
        //   ellipsis: true,
        //   dataIndex: "CREATE_DATE",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
      ],
      downloadLoading: false,
    };
  },
  created () {

    // 随手拍分类统计结果
    // 统计表单
    this.handleTime("今年1~6月");
    this.fetchData();
  },
  methods: {
    handleTime (name) {
      var time = new Date();
      let Year = time.getFullYear(); /* 当前年份 */
      if (name == "最近三个月") {
        let endTime = time.toLocaleDateString(); /* 当前时间  */
        this.queryForm.endTime = endTime;
        this.queryForm.endTime = this.queryForm.endTime.replace(/\//g, '-');
        time.setMonth(time.getMonth() - 3);
        this.queryForm.beginTime = time.toLocaleDateString();
        this.queryForm.beginTime = this.queryForm.beginTime.replace(/\//g, "-");
        this.showDisabled = true;
      } else if (name == "今年1~6月") {
        this.queryForm.beginTime = `${Year}-01-01`;
        this.queryForm.endTime = `${Year}-06-30`;
        this.showDisabled = true;
      } else if (name == "今年7~12月") {
        this.queryForm.beginTime = `${Year}-07-01`;
        this.queryForm.endTime = `${Year}-12-31`;
        this.showDisabled = true;
      } else if (name == "今年内") {
        this.queryForm.beginTime = `${Year}-01-01`;
        this.queryForm.endTime = `${Year}-12-31`;
        this.showDisabled = true;
      } else if (name == "自定义") {
        this.queryForm.beginTime = "";
        this.queryForm.endTime = "";
        this.showDisabled = false;
      } else if (name == "本届") {
        this.queryForm.beginTime = "";
        this.queryForm.endTime = "";
        this.showDisabled = true;
      }
      if (name != "自定义") {
        this.fetchData();
      }
    },
    // 导出
    download (e) {
      let data = this.queryForm;
      this.downloadLoading = true;
      instance_1({
        url: "/memberComment/countCommentCategoryExport",
        method: "get",
        responseType: "blob",
        params: data,
      }).then((res) => {
        // console.log("🤗🤗🤗, res =>", res);
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `随手拍分类统计表` + `.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000)
      });
    },
    //重置
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        timeRange: "今年1~6月",
        endTime: null,
        beginTime: null
      };
      this.handleTime("今年1~6月");
      this.fetchData();
    },
    // 查看
    toDetailed () {

    },
    //复选框选改变
    onSelectChange (key, data) {
      this.selectedRows = data;
      this.tableKey = key;
      this.tableData = data;
    },
    // 数据渲染 
    fetchData () {

      if (this.queryForm.beginTime == '' || this.queryForm.endTime == "") {
        this.$message.warning("请输入开始时间和结束时间");
      } else {
        this.listLoading = true;
        instance_1({
          url: "/memberComment/CountcommentCategory",
          method: "get",
          params: this.queryForm,
        }).then((res) => {
          if (res.data.code == "200") {
            this.listLoading = false;
            this.dataSource = res.data.rows;
            this.pagination.total = res.data.total;

          }
        });
      }
      // this.$refs.Forms.validate((valid) => { 
      //   if(valid){


      //   }
      // })

    },


    // 关闭窗口
    close () {
      this.articleId = '';
      this.dataSource = [];
      this.visible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
// 自定义搜索框样式
.searchStyle {
  display: flex !important;

  .ant-input {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }

  .ant-btn-primary {
    width: 66px;
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
</style>
