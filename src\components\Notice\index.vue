<template>
  <EnterContent
    :title="title"
    :oper="oper"
    alert="点击“草稿”按钮后，可预览公告内容。"
    class="ActivitySchedule"
  >
    <div slot="default">
      <slot name="default" />
    </div>

    <template slot="actions">
      <slot name="actions" />
    </template>

    <template slot="right">
      <h3>代表进社区活动流程</h3>
      <div class="timeline">
        <a-timeline>
          <a-timeline-item v-for="(it, index) in list" :key="index">
            <div
              slot="dot"
              class="dot"
              :class="[
                (index == current && 'active-dot') ||
                  (index < current && 'done-dot'),
              ]"
            >
              {{ index + 1 }}
            </div>
            <p
              class="title"
              :class="[
                it.disabled && 'disabled',
                (index == next || index == previous) && 'active',
              ]"
              @click="onClick(index)"
            >
              {{ it.title }}
            </p>
          </a-timeline-item>
        </a-timeline>
      </div>
    </template>
  </EnterContent>
</template>

<script>
import moment from "moment";
import EnterContent from "@/components/EnterContent/index.vue";
import { getLiaisonStation } from "@/api/listNotice";
import { getMembers } from "@/api/station";

export default {
  name: "ActivitySchedule",
  components: { EnterContent },
  model: {
    prop: "form",
    event: "change",
  },
  props: {
    form: {
      type: Object,
      required: true,
      default: null,
    },
    current: {
      type: Number,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    // create | view | update | audit
    oper: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      list: [
        {
          title: "年度活动计划",
          toNext() {
            sessionStorage.setItem(
              "ActivitySchedule",
              JSON.stringify({
                content: this.form.mark,
                dateTime: this.form.serverTime
                  ? moment(this.form.serverTime).format("YYYY-MM-DD HH:mm:ss")
                  : undefined,
                mobile: this.form.contactPhoneNumber,
                liaisonStationId: this.form.liaisonStationId,
                checkedList: this.form.attendMembers?.map((it) => it.userId),
              })
            );
            this.$router.push({
              path: "/BBS/BBS_ListNoticeDetail",
            });
          },
        },
        {
          title: "进社区活动公告",
          async toNext() {
            const data = {
              mark: this.form.title,
            };
            if (this.form.liaisonStationId) {
              const res = await getLiaisonStation();
              const find = res.data.find(
                (it) => it.id == this.form.liaisonStationId
              );
              if (find) {
                data.administrativeAreaId = find.administrativeAreaId;
                data.streetTownId = find.streetTownId;
                data.liaisonStationId = this.form.liaisonStationId;
                data.contactPhoneNumber = find.contactPhoneNumber;
                data.contactName = find.contactName;
              }
            }

            if (this.form.sfjd == 1) {
              if (!data.contactPhoneNumber && this.form.mobile) {
                data.contactPhoneNumber = this.form.mobile;
              }

              if (this.form.dateTime) {
                data.serverTime = moment(this.form.dateTime).format(
                  "YYYY-MM-DD"
                );
              }

              if (this.form.liaisonStationId && this.form.checkedList?.length) {
                const res = await getMembers({
                  id: this.form.liaisonStationId,
                });
                data.attendMembers = res.data
                  .filter((it) => this.form.checkedList.includes(it.userId))
                  .map((it) => ({
                    userId: it.id,
                    inviteId: it.treeId || it.postId,
                    dhDm: it.dhDm,
                    jcDm: it.jcDm,
                    orgId: it.orgId,
                    deptId: it.deptId,
                    postId: it.treeId || it.postId,
                    name: it.name,
                    userName: it.name,
                    sfDm: it.committeeMemberTypeId,
                    committeeMemberTypeId: it.committeeMemberTypeId,
                  }));
              }
            }
            sessionStorage.setItem("ActivitySchedule", JSON.stringify(data));
            await this.$router.push({
              path: "/community/intoTheCommunityActivityDetail",
            });
          },
          toPrevious() {
            this.$router.push({
              path: `/community/intoTheCommunityPlanDetail?id=${this.previousId}&oper=view`,
            });
          },
        },
        { title: "进社区活动签到（线下）", disabled: true },
        {
          title: "进社区活动登记",
          toNext() {
            this.$router.push({
              path: "/community/activityScheduleTupleList",
            });
          },
          toPrevious() {
            this.$router.push({
              path: `/BBS/BBS_ListNoticeDetail?id=${this.previousId}&oper=view`,
            });
          },
        },
        { title: "录入社情民意" },
        { title: "进社区活动归档" },
      ],
      previousId: sessionStorage.getItem("ActivitySchedulePreviousId") || "",
    };
  },
  computed: {
    next() {
      return this.list.findIndex(
        (it, index) => index > this.current && !it.disabled
      );
    },
    previous() {
      if (!this.previousId) return -1;

      for (let i = this.current - 1; i >= 0; i--) {
        const it = this.list[i];
        if (!it.disabled) return i;
      }
      return -1;
    },
  },
  methods: {
    onClick(index) {
      const it = this.list[this.current];
      if (!it || !this.form) return;

      if (index == this.next) {
        it.toNext && it.toNext.call(this);
        sessionStorage.setItem(
          "ActivitySchedulePreviousId",
          this.form.id || ""
        );
      } else if (index == this.previous && this.previousId) {
        it.toPrevious && it.toPrevious.call(this);
        sessionStorage.removeItem("ActivitySchedulePreviousId");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ActivitySchedule {
  padding: 0 !important;

  .timeline {
    padding: 10px;

    .dot {
      height: 24px;
      width: 24px;
      line-height: 24px;
      border-radius: 50%;
      color: #666666;
      background: #f4f4f4;
    }

    .done-dot {
      background-color: #d1f6f7;
      color: #202020;
    }

    .active-dot {
      background-color: #c71c33;
      color: #fff;
    }

    .title {
      line-height: 24px;
      border-radius: 2px;
      padding-left: 6px;
    }
    .disabled {
      cursor: not-allowed !important;
    }
    .active {
      cursor: pointer;
    }
  }
}
</style>
