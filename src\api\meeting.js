import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/meeting/getMeetingList",
    method: "post",
    data,
  });
}
export function GET_CODE_LIST(data) {
  return request({
    url: `/api/v1/meetingIdentity/getDBTIdentity/${data}` ,
    method: "post",
  });
}

export function AddMeetingInfo() {
  return request({
    url: "/api/v1/meeting/meetingAddPage",
    method: "post",
  });
}

export function initMeetingInfo(param) {
  return request({
    url: "/api/v1/meeting/meetingInfo/" + param,
    method: "post",
  });
}

export function changeCurrent(param) {
  return request({
    url: "/api/v1/meeting/changeCurrent/" + param,
    method: "post",
  });
}

export function changeMeeting(param) {
  return request({
    url: "/api/v1/meeting/changeMeeting/" + param,
    method: "post",
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/meeting/delMeeting",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/meeting/addMeeting",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/meeting/updateMeeting",
    method: "post",
    data,
  });
}

export function getCurrent() {
  return request({
    url: "/api/v1/meeting/recently",
    method: "post",
  });
}
