<template>
  <div class="table table-container">
    <SearchForm :value="queryForm" @onReset="reset" @onSearch="search">
      <template v-slot:topSearch>
        <DhJcCascade v-model="queryForm" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>
      </template>
      <template v-slot:moreSearch>
        <AdminStreetStationCascade v-model="queryForm" allow-clear all-perm />

        <FormInput
          v-model="queryForm.userName"
          :label="'代表姓名'"
          allow-clear
          @enter="handleEnter"
        />

        <FormRangePicker
          v-model="queryForm"
          start-prop="startTime"
          end-prop="endTime"
          label="时间范围"
          allow-clear
        />

        <FormInput
          v-model="queryForm.contactName"
          :label="'联系人姓名'"
          allow-clear
          @enter="handleEnter"
        />
      </template>
    </SearchForm>

    <a-row style="margin: 5px 0px 10px 8px">
      <a-col :span="12" style="margin-bottom: 10px">
        <a-button
          type="primary"
          :loading="downloaDataLoading"
          style="margin-left: 8px"
          @click="downloaData"
          >导出</a-button
        >
      </a-col>
    </a-row>
    <a-spin :indicator="indicator" :spinning="loading">
      <standard-table
        :pagination="pagination"
        row-key="id"
        :columns="columns"
        :data-source="list"
        :loading="TBloading"
        :selected-rows.sync="selectedRowKeys"
        @tableClick="clickRow"
        @onChange="onSelectChange"
      >
      </standard-table>
    </a-spin>
    <a-modal
      :visible="batchAuditVisible"
      title="批量审核"
      @ok="batchAudit()"
      @cancel="batchAuditVisible = false"
    >
      <a-textarea
        v-model="comment"
        placeholder="请输入审核意见"
        :auto-size="{ minRows: 2, maxRows: 5 }"
      />
    </a-modal>
  </div>
</template>
<script>
import { ExportExcelValue } from "@/api/communityschedule";
import { myPagination } from "@/mixins/pagination.js";
import { handled, taskReject } from "@/api/communityschedule.js";
import { instance_1 } from "@/api/axiosRq";
import StandardTable from "@/components/table/StandardTable";
import SearchForm from "@/components/SearchForm/index";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormRangePicker from "@/components/FormRangePicker/index.vue";
import { DBLZ_DBJSQ } from "@/utils/enum/levelRoleMainIdentifyEnum";

export default {
  components: {
    FormRangePicker,
    AdminStreetStationCascade,
    FormInput,
    DhJcCascade,
    StandardTable,
    SearchForm,
  },
  mixins: [myPagination],
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBJSQ,
      TBloading: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      advanced: false, // 高级搜索展开标记，默认为 false 不展开
      loading: false, // 高级搜索展开标记，默认为 false 不展开
      batchAuditVisible: false,
      downloaDataLoading: false,
      comment: "",
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        dhDm: undefined,
        jcDm: undefined,
        dbtId: undefined,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        userName: undefined,
        startTime: "",
        endTime: "",
        contactName: "",
      },
      selectedRowKeys: [],
      list: [],
      indexNum: 1,
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 60,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "当前状态",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "currStateName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "活动编号",
          width: 180,
          ellipsis: true,
          dataIndex: "activityNo",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "活动时间",
          width: 130,
          ellipsis: true,
          dataIndex: "serverTime",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "行政区划",
          width: 100,
          ellipsis: true,
          dataIndex: "administrativeAreaName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "街道名称",
          width: 110,
          ellipsis: true,
          dataIndex: "streetTownName",
          customRender: (text, record, index) => {
            if (text) {
              return text.replace("T", " ").split("Z").join("").substr(0, 19);
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "联络站名称",
          width: 300,
          ellipsis: true,
          dataIndex: "liaisonStationName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "联系人",
          width: 110,
          ellipsis: true,
          dataIndex: "contactName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "电话",
          width: 150,
          ellipsis: true,
          dataIndex: "contactPhoneNumber",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "代表",
          width: 180,
          ellipsis: true,
          dataIndex: "inviteRangeDesc",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "备注",
          width: 110,
          ellipsis: true,
          dataIndex: "mark",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "录入日期",
          width: 180,
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            if (text) {
              return text.replace("T", " ").split("Z").join("").substr(0, 19);
            } else {
              return "/";
            }
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 180,
          scopedSlots: { customRender: "operation" },
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.$router.push({
                      //   path: "/community/intoTheCommunityActivityWrapper",
                      //   query: { id: record.id, oper: "view" },
                      // });
                      this.$router.push({
                        path: "/community/intoTheCommunityActivityDetail",
                        query: {
                          id: record.id,
                          oper: "view",
                          title: this.$route.meta.title || "已办事项",
                        },
                      });
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                    display:
                      record.currStateDm == "21" || record.currStateDm == "31"
                        ? "inline"
                        : "none",
                  },
                  on: {
                    click: () => {
                      this.$confirm({
                        cancelText: "取消",
                        okType: "danger",
                        okText: "确定",
                        title: "任务撤回提醒",
                        content: "是否确定撤回该次送审？",
                        onOk: () => {
                          taskReject({ id: record.id }).then((res) => {
                            if (res.code == "0000") {
                              this.$message.success("操作成功");
                              this.fetchData();
                            } else {
                              this.$message.error(res.msg);
                            }
                          });
                        },
                        onCancel: () => {
                          this.$message.info("您已取消操作！");
                        },
                      });
                    },
                  },
                },
                "撤回"
              ),
            ]);
          },
        },
      ],
      searchDebounced: _.debounce(this.search, 500),
      isKeepAlive: false,
    };
  },
  created() {
    this.fetchData();
  },
  activated() {
    if (this.isKeepAlive) {
      this.fetchData();
    } else {
      this.isKeepAlive = true;
    }
  },
  methods: {
    handleEnter() {
      this.searchDebounced();
    },
    // 导出
    downloaData() {
      if (this.selectedRowKeys.length == 0)
        return this.$message.error("请选择数据");
      this.downloaDataLoading = true;
      ExportExcelValue({
        ids: this.selectedRowKeys.map((son) => son.id).join(),
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区活动数据.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloaDataLoading = false;
        }, 1000);
      });
    },
    // 点击行
    clickRow(event, record) {
      try {
        let state;
        let tableHeader = event.target.__vue__.column.key;
        // tableHeader ? state = true : state = false
        if (tableHeader == "activityName") {
          this.seedata(record);
        }
      } catch (error) {}
    },
    async fetchData() {
      this.loading = true;
      let res = await handled(this.queryForm, this.form);
      if (res.code == 200) {
        this.loading = false;
        this.list = res.rows;
        this.pagination.total = res.total;
      }
    },
    // 查看
    seedata(record) {
      this.$router.push({
        path: "/delegate/addRegister",
        query: {
          areaId: record.id,
          title: "查看",
          titleBox: "查看所有",
          seeTitle: true,
          queryForm: this.queryForm,
          form: this.form,
        },
      });
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    batchAudit() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.error("请选择需要审核的数据");
        return;
      }
      let ids = [];
      this.selectedRowKeys.forEach((item) => {
        ids.push(item.id);
      });
      let comment = this.comment;
      this.loading = true;
      this.batchAuditVisible = false;
      instance_1({
        url: "/dutyActive/batchAudit",
        method: "post",
        data: { ids: ids, comment: comment },
      }).then((res) => {
        if (res.data.code == "0000") {
          let msg = res.data.msg;
          this.$message.success(msg);
        } else {
          this.$message.error(res.data.msg);
        }
        this.loading = false;
        this.fetchData();
      });
    },
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    // 重置
    reset(value) {
      this.queryForm = value;

      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
  margin-bottom: 10px;
}

.table {
  padding: 15px;
}
</style>
