<template>
    <div style="padding: 20px">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="时间"
                               prop="serverTime">
              <a-date-picker style="width: 100%;"
                             value-format="YYYY-MM-DD" 
                             v-model="form.achieveDate"
                             placeholder="选择日期时间">
              </a-date-picker>
            </a-form-model-item>
        <a-form-model-item label="内容" prop="userName">
          <a-textarea
            :autoSize="{ minRows: 6, maxRows: 6 }"
            v-model="form.content"
          />
        </a-form-model-item>
        <a-form-model-item :wrapper-col="{ span: 18, offset: 3}">
          <a-button type="primary" @click="onSubmit"> 修  改 </a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
  </template>
  
  <script>
  import { getAchievementById, updateAchievement } from "@/api/electronicArchives.js";
  export default {
    data() {
      return {
        recordId: "",
        //行政区域列表
        administrativeAreas: [],
        streetTowns: [],
        loading: false,
        imageUrl: "",
        labelCol: { span: 3 },
        wrapperCol: { span: 14 },
        other: "",
        form: {
          achieveDate: null
        },
        inviteRangeDesc: "",
        defaultSelect: [], //同行代表
        showDisabled: false,
        rules: {
          achieveDate: [
            {
              required: true,
              message: "请选择日期",
              trigger: "blur",
            },
          ],
          content: [
            {
              required: true,
              message: "请输入内容",
              trigger: "blur",
            },
          ],
        },
      };
    },
    mounted() {
      let { id } = this.$route.query;
      this.recordId = id;
      this.getById()
    },
    methods: {
      getById() {
        getAchievementById({ id : this.recordId }).then(res => {
          this.form = res.data
        })
      },
      onSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            updateAchievement(this.form).then(res => {
              if(res.code == '0000') {
                this.$message.success("修改成功");
              } else {
                this.$message.error("修改失败");
              }
              this.getById()
            })
          } else {
            this.$message.error("请完善资料");
            return false;
          }
        });
      },
      resetForm() {
        this.$refs.form.resetFields();
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .avatar-uploader > .ant-upload {
    width: 128px;
    height: 128px;
  }
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }
  
  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .searchStyle {
  display: flex !important;
  .ant-input {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  .ant-btn-primary {
    width: 66px;
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
  </style>
  