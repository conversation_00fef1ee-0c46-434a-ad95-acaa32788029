<template>
  <a-row>
    <a-col :span="24">
      <a-form-model-item 
      :label="fujianInfo.name" 
      :prop="fujianInfo.value"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 24 }">
        <div style="display: flex;flex-wrap: wrap;padding-left: 5%;">
          <div class="pdf_style" v-for="(item, index) in fujianInfo.fileUrl" :key="index" @click="handlePdfView(item)">
            <img class="img_icon" v-if="item.name.includes('pdf')" src="@/assets/pdf.png" />
            <img class="img_icon" v-if="item.name.includes('ofd')" src="@/assets/ofd.png" />
            <div>{{item.name}}</div>
          </div>
        </div>
      </a-form-model-item>
    </a-col>
  </a-row>
</template>

<script>
export default {
  name: "timeLine",
  props: ['fujianInfo'],
  components: {},
  data() {
    return {
      
    };
  },
  methods: {
    handlePdfView(file) {
      this.$emit('preview', file)
    },
  }
}
</script>

<style lang="scss" scoped>
.pdf_style {
  position: relative;
  // display: flex;
  align-items: center;
  margin-right: 50px;
  margin-bottom: 10px;
  text-align: center;
  cursor: pointer;
  .img_icon {
    width: 40px;
    height: fit-content;
    margin-right: 10px;
  }
  .close_icon {
    position: absolute;
    top: -5px;
    right: 0;
    cursor: pointer;
  }
}
</style>