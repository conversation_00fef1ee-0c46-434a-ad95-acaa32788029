<template>
    <div class="table-container">
      <a-row>
        <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <SearchForm @onReset="clearListQuery" @onSearch="search" :noMore="true">
            <template v-slot:topSearch>
              <DhJcCascade v-model="queryForm" allow-clear is-dbt @load="search"/>
              <SingleSearch @onEnter="fetchData" :title="'代表姓名'" :value.sync="queryForm.userName" />
              <SingleSelect :title="'排名类型'" :selectList="rankList" :value.sync="queryForm.dutyTag" />
              <TimeRangePicker :startTimeValue.sync="queryForm.startTime" :endTimeValue.sync="queryForm.endTime" :title="'活动时间'" />
            </template>
          </SearchForm>

          <a-row style="margin: 5px 0px 10px 8px">
            <a-button type="primary"
              style="margin-left: 8px;"
              @click="downloadExcel()">导出</a-button>
          </a-row>

          <!-- table -->
          <a-spin :indicator="indicator" :spinning="listLoading">
            <a-table
              ref="noticeTable"
              class="tableLimit"
              :columns="columns"
              :pagination="pagination"
              :data-source="list"
              :scroll="{ x: 1300 }"
              @change="handleTableChange"
            ></a-table>
          </a-spin>
        </a-col>
      </a-row>
    </div>
  </template>
  
  <script>
  import { getAllRank, exportExcel} from "@/api/electronicArchives.js";
  import SingleSelect from '@/components/SingleSelect/index';
  import SearchForm from '@/components/SearchForm/index';
  import SingleSearch from '@/components/SingleSearch/index';
  import TimeRangePicker from '@/components/TimeRangePicker/index';
  import DhJcCascade from "@/components/DhJcCascade/index.vue";
  export default {
    name: "Table",
    components: {
      SingleSelect,
      SearchForm,
      SingleSearch,
      TimeRangePicker,
      DhJcCascade
    },
    filters: {},
    data() {
      return {
        liaisonStationId: "",
        dateRange: [],
        advanced: false,
        //list-----
        list: [],
        queryForm: {
          pageNum: 1,
          pageSize: 10,
        },
        listLoading: false,
        indicator: <a-icon type="loading" style="font-size: 24px" spin />,
        layout: "total, sizes, prev, pager, next, jumper",
        total: 0,
        page: 1,
        size: 10,
        background: true,
        //list-----
        selectRows: "",
        indexNum: 1,
        // 列表
        columns: [
          {
            fixed: "left",
            title: "序号",
            key: "index",
            align: "center",
            width: 100,
            customRender: (text, record, index) =>
              `${(this.indexNum - 1) * this.indexNum + index + 1}`,
          },
          {
            title: "代表姓名",
            align: "center",
            width: 150,
            ellipsis: true,
            dataIndex: "userName",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "代表大会",
            align: "center",
            width: 100,
            dataIndex: "meetingNum",
          },
          {
            title: "代表大会排名",
            align: "center",
            width: 120,
            sorter: true,
            dataIndex: "meetingNumRank",
          },
          {
            title: "领衔提出议案建议情况",
            align: "center",
            width: 100,
            dataIndex: "proposalSuggestionNum",
          },
          {
            title: "领衔提出议案建议情况排名",
            align: "center",
            width: 120,
            sorter: true,
            dataIndex: "proposalSuggestionNumRank",
          },
          {
            title: "参加进社区活动情况",
            align: "center",
            width: 100,
            dataIndex: "communityScheduleNum",
          },
          {
            title: "参加进社区活动情况排名",
            align: "center",
            width: 120,
            sorter: true,
            dataIndex: "communityScheduleNumRank",
          },
          {
            title: "参加代表培训活动情况会",
            align: "center",
            width: 100,
            dataIndex: "studyNum",
          },
          {
            title: "参加代表培训活动情况会排名",
            align: "center",
            width: 150,
            sorter: true,
            dataIndex: "studyNumRank",
          },
          {
            title: "参加代表集中视察情况",
            align: "center",
            width: 100,
            dataIndex: "investigationNum",
          },
          {
            title: "参加代表集中视察情况排名",
            align: "center",
            width: 130,
            sorter: true,
            dataIndex: "investigationNumRank",
          },
          {
            title: "参加“更好发挥人大代表作用”主题活动情况",
            align: "center",
            width: 130,
            dataIndex: "themeMonthNum",
          },
          {
            title: "参加“更好发挥人大代表作用”主题活动情况排名",
            align: "center",
            width: 150,
            sorter: true,
            dataIndex: "themeMonthNumRank",
          },
          {
            title: "意见建议被《人大代表情况反映》情况",
            align: "center",
            width: 130,
            dataIndex: "achievementNum",
          },
          {
            title: "意见建议被《人大代表情况反映》情况排名",
            align: "center",
            width: 150,
            sorter: true,
            dataIndex: "achievementNumRank",
          },
          {
            title: "履职事迹被《人大代表履职周报》刊登次数",
            align: "center",
            width: 150,
            dataIndex: "resumptionWeekUserRecordNum",
          },
          {
            title: "履职事迹被《人大代表履职周报》刊登次数排名",
            align: "center",
            width: 170,
            sorter: true,
            dataIndex: "resumptionWeekUserRecordNumRank",
          },
          {
            title: "使用代表“随手拍”反映问题次数",
            align: "center",
            width: 120,
            dataIndex: "memberCommentNum",
          },
          {
            title: "使用代表“随手拍”反映问题次数排名",
            align: "center",
            width: 140,
            sorter: true,
            dataIndex: "memberCommentNumRank",
          },
          {
            title: "参加其他履职活动情况",
            align: "center",
            width: 100,
            dataIndex: "otherNum",
          },
          {
            title: "参加其他履职活动情况排名",
            align: "center",
            width: 130,
            sorter: true,
            dataIndex: "otherNumRank",
          },
          {
            title: "履职活动总数(除代表大会)",
            align: "center",
            width: 100,
            dataIndex: "allNum",
          },
          {
            title: "履职活动总数排名(除代表大会)",
            align: "center",
            width: 130,
            sorter: true,
            dataIndex: "allNumRank",
          },
          {
            title: "获得奖励情况",
            align: "center",
            width: 100,
            dataIndex: "dutyActivityAwardNum",
          },
          {
            title: "获得奖励情况排名",
            align: "center",
            width: 130,
            sorter: true,
            dataIndex: "dutyActivityAwardNumRank",
          },
        ],
        // 分页器设置
        pagination: {
          pageNo: 1,
          pageSize: 10, // 默认每页显示数量
          showSizeChanger: true, // 显示可改变每页数量
          showQuickJumper: true, // 显示跳转功能
          pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
          showTotal: (total) => `总共 ${total} 条`, // 显示总数
          onShowSizeChange: (current, pageSize) =>
            this.changePageSize(current, pageSize), // 改变每页数量时更新显示
          onChange: this.handleCurrentChange.bind(this), // 点击页码事件
          total: 0, // 总条数
          current: 0, // 当前页数
          buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
          size: "middle",
        },
        rankList: [
          {name: '全部代表', id: '0'},
          {name: '非职务代表', id: '1'},
          {name: '职务代表', id: '2'},
        ]
      };
    },
    created() {
      this.fetchData();
  
      this.$store.dispatch("navigation/breadcrumb1", "网上联络站");
      this.$store.dispatch("navigation/breadcrumb2", "联络站列表");
    },
    beforeDestroy() {
      // window.localStorage.setItem("tongjifenzi", JSON.stringify(this.queryForm));
    },
    methods: {
      // getTime(val) {
      //   this.queryForm.startTime = val[0];
      //   this.queryForm.endTime = val[1];
      // },
      // 高级搜索
      toggleAdvanced() {
        this.advanced = !this.advanced;
      },
      // 重置
      clearListQuery() {
        this.queryForm = {
          pageNum: 1,
          pageSize: 10,
        };
        this.dateRange = []; //清空时间
        this.fetchData();
      },
      search() {
        this.queryForm.pageNum = 1;
        this.pagination.current = 1;
        this.fetchData();
      },
      // 时间选择
      onTimeChange(val) {
        this.queryForm.startTime = val[0];
        this.queryForm.endTime = val[1];
      },
      setSelectRows(val) {
        this.selectRows = val;
      },
  
      fetchData() {
        this.listLoading = true;
        let { pageNum, pageSize } = this.queryForm
        let params = {}
        params.pageNum = pageNum
        params.pageSize = pageSize
        getAllRank(this.queryForm, params).then((res) => {
          this.list = res.rows;
          this.listLoading = false;
          this.total = res.total;
          this.pagination.total = res.total;
        });
      },
      handleSizeChange(val) {
        this.queryForm.size = val;
        this.fetchData();
      },
  
      // 切换页数
      changePageSize(current, pageSize) {
        this.queryForm.pageNum = current;
        this.queryForm.pageSize = pageSize;
        this.pagination.pageSize = pageSize;
        this.pagination.current = current;
        this.fetchData();
      },
  
      // 切换页码
      handleCurrentChange(current, pageSize) {
        this.queryForm.pageNum = current;
        this.pagination.current = current;
        this.fetchData();
      },
      // handleCurrentChange(val) {
      //   this.queryForm.current = val;
      //   this.fetchData();
      // },
      // 删除
      deleteItem(id) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          delNotice(id).then((response) => {
            this.$message.success("删除成功");
            this.fetchData();
          });
        });
      },
      handleAdd(id) {
        console.log(id);
        this.$router.push({
          path: "/BBS/ListNotice_add",
          query: {
            oper: "add",
          },
        });
      },
      amend(id) {
        console.log(id);
        this.$router
          .push({
            path: "/BBS/ListNotice_add",
            query: {
              id: id,
              oper: "update",
            },
          })
          .catch(() => {});
      },
      details(id) {
        console.log(id);
        this.$router
          .push({
            path: "/BBS/ListNotice_add",
            query: {
              id: id,
              oper: "details",
            },
          })
          .catch(() => {});
      },
      handleQuery() {
        this.fetchData();
      },
      handleCurrent(row) {
        console.log("row", row);
      },
      handleTableChange(data, value, title) {
        try {
          this.queryForm.sort = title.column.dataIndex;
        } catch (error) {}
        if (title.order == "ascend") {
          this.queryForm.order = "asc";
        } else if (title.order == "descend") {
          this.queryForm.order = "desc";
        } else {
          this.queryForm.order = "";
          this.queryForm.sort = "";
        }
        this.fetchData();
      },
      // 下载
      downloadExcel() {
        this.downloadExcelLoading = true;
        exportExcel(this.queryForm).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `代表履职排名.xlsx`;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          setTimeout(() => {
            this.downloadExcelLoading = false;
          }, 1000);
        });
      },
      handleRankTypeChange(value) {
        this.queryForm.dutyTag = value
        this.fetchData()
      }
    },
  };
  </script>
  <style scoped>

  </style>
  