import { instance_yajy } from "@/api/axiosRq";

// 系统管理-单位机构管理
// 列表
export function findUnitPage(form) {
  return instance_yajy({
    url: `/api/v1/system/org/yajy/findPage`,
    method: "post",
    data: form,
  });
}
// 导出单位机构联系人
export function exportOrgList(form) {
  return instance_yajy({
    url: `/api/v1/system/org/yajy/expOrg`,
    method: "post",
    data: form,
    responseType: "blob",
  });
}
//导出承办单位信息
export function exportOrgExcelList(form) {
  return instance_yajy({
    url: `/api/v1/system/org/yajy/exportExcel`,
    method: "post",
    data: form,
    responseType: "blob",
  });
}
// 系统管理-保存议案建议系统组织机构
// 保存
export function unitSave(form) {
  return instance_yajy({
    url: `/api/v1/system/org/yajy/save`,
    method: "post",
    data: form,
  });
}
// 列表 关联用户 获取关联用户列表
// 保存
export function queryOperatorList(form) {
  return instance_yajy({
    url: `/api/v1/system/operator/queryOperatorList`,
    method: "post",
    data: form,
  });
}
// 删除
export function removeBatch(form) {
  return instance_yajy({
    url: `/api/v1/system/org/yajy/removeBatch`,
    method: "post",
    data: form,
  });
}
// 回显
export function findById(orgId) {
  return instance_yajy({
    url: `/api/v1/system/org/yajy/findById`,
    method: "post",
    params: orgId,
  });
}
