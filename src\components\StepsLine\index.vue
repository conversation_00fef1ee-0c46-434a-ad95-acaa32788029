<template>
  <div class="step_line">
    <a-steps :current="0">
      <a-step :title="activity.type" v-for="(activity, index) in stepInfoList" :key="index">
        <span slot="description">
          <div class="card_content">
          <h4 class="card_top">
            <i style="margin-right: 5px;color: #eab657;" class="el-icon-document"></i>
            {{activity.name}}
            <div>{{activity.position}}</div>
          </h4>
          <div class="card_bottom">
            <div style="margin-bottom: 5px;">
              <span :class="activity.status === 'pass' ? 'passed' : ''" class="dot_icon" />
              {{activity.type}}
            </div>
          </div>
        </div>
        </span>
      </a-step>
    </a-steps>
  </div>
</template>

<script>
export default {
  name: "timeLine",
  props: ['stepInfoList'],
  components: {},
  data() {
    return {
      // activities: [
      // //   {
      // //   content: '活动按期开始',
      // //   timestamp: '2018-04-15',
      // //   type: '提交成功',
      // //   status: 'normal',
      // // }, 
      // {
      //   name: '杨*',
      //   position: '（中心联络站）',
      //   timestamp: '2018-04-13',
      //   type: '提交成功',
      //   status: 'pass',
      // }, {
      //   name: '陈*雨',
      //   position: '（区人大代表工委）',
      //   timestamp: '2018-04-11',
      //   status: 'normal',
      //   type: '审核通过',
      //   color: '#0bbd87',
      //   remark: '备注：审核通过'
      // }, {
      //   name: '陈*雨',
      //   position: '（区人大代表工委）',
      //   timestamp: '2018-04-11',
      //   status: 'normal',
      //   type: '审核通过',
      //   color: '#0bbd87',
      //   remark: '备注：审核通过'
      // }, {
      //   name: '陈*雨',
      //   position: '（区人大代表工委）',
      //   timestamp: '2018-04-11',
      //   status: 'normal',
      //   type: '审核通过',
      //   color: '#0bbd87',
      //   remark: '备注：审核通过'
      // }]
    };
  },
}
</script>

<style lang="scss" scoped>
::v-deep .ant-steps-item-process .ant-steps-item-icon {
  background-color: #d92b3e;
  border-color: #d92b3e;
}
.step_line {
  // flex: 2;
  // border-left: 1px solid #ccc;
  // padding: 10px 10px;
  .card_content {
    border-radius: 4px;
    border: 1px solid #ebeef5;
    background-color: #fff;
    overflow: hidden;
    color: #303133;
    transition: 0.3s;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    font-size: 12px;
    .card_top{
      background-color: #f5f7f6;
      padding: 10px;
      font-weight: 700;
    }
    .card_bottom {
      padding: 5px 10px 10px 15px;
    }
  }
}
.dot_icon {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: #3a73e7;
  border-radius: 50%;
  margin-right: 5px;
}
.passed {
  background-color: #288362;
}
</style>