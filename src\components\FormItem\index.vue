<template>
  <a-col
    :span="layout.span"
    :xs="layout.xs"
    :sm="layout.sm"
    :md="layout.md"
    :lg="layout.lg"
    :xl="layout.xl"
    :xxl="layout.xxl"
    :class="{ overLength: label.length > 5 && layout.span <= 8 }"
  >
    <a-form-model-item :label="label" :prop="prop">
      <slot name="default" />
    </a-form-model-item>
  </a-col>
</template>

<script>
/**
 * 表单项组件
 *  1.参考 <FormInput />
 */
export default {
  name: "FormItem",
  props: {
    // <a-form-model-item prop="" />
    prop: {
      type: String,
      required: false,
      default: undefined,
    },
    // <a-form-model-item label="" />
    label: {
      type: String,
      required: true,
    },
    // <a-col span="" />
    span: {
      type: [Number, String],
      default: undefined,
    },
    xs: {
      type: [Number, String],
      default: undefined,
    },
    sm: {
      type: [Number, String],
      default: undefined,
    },
    md: {
      type: [Number, String],
      default: undefined,
    },
    lg: {
      type: [Number, String],
      default: undefined,
    },
    xl: {
      type: [Number, String],
      default: undefined,
    },
    xxl: {
      type: [Number, String],
      default: undefined,
    },
  },
  computed: {
    layout() {
      if (!this.span) {
        return {
          span: 24,
          lg: 12,
          xl: 8,
        };
      }
      return {
        span: this.span,
        xs: this.xs,
        sm: this.sm,
        md: this.md,
        lg: this.lg,
        xl: this.xl,
        xxl: this.xxl,
      };
    },
  },
};
</script>

<style lang="scss" scoped></style>
