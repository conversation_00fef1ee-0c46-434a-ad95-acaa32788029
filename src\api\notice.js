import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/meetingNotice/getNoticeList",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/meetingNotice/addNotice",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/meetingNotice/updateNotice",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/meetingNotice/deleteNotice",
    method: "post",
    data
  });
}

export function addNoticeInfo() {
  return request({
    url: "/api/v1/meetingNotice/initNoticeId",
    method: "post",
  });
}

export function getNoticeInfo(param) {
  return request({
    url: "/api/v1/meetingNotice/getNotice/" + param,
    method: "post",
  });
}

export function getNoticeIdentityList(param) {
  return request({
    url: "/api/v1/meetingNotice/getIdentityList/" + param,
    method: "post",
  });
}
