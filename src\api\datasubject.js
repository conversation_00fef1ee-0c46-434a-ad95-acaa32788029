import request from "@/utils/request";
import qs from "qs";

export function getPage(data) {
  return request({
    url: "/api/v1/manage/datasubject/queryPage",
    method: "post",
    data,
  });
}

export function getDisplayPage(data) {
  return request({
    url: "/api/v1/display/datasubject/queryPage",
    method: "post",
    data,
  });
}

export function getList(data) {
  return request({
    url: "/api/v1/manage/datasubject/queryList",
    method: "post",
    data,
  });
}

export function getById(data) {
  return request({
    url: "/api/v1/manage/datasubject/queryById",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/manage/datasubject/add",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/manage/datasubject/update",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/manage/datasubject/deleteByIds",
    method: "post",
    data,
  });
}
