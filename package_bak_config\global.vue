<script type="text/javascript">
import store from "./store";

// const basePath_1 = "http://************:8080/api/v1"; //服务器 程贵
// const basePath_2 = "http://************:8080/api/v1"; //服务器 程贵

// const basePath_1 = "http://localhost:8080/api/v1"; //服务器
// const basePath_2 = "http://localhost:8080/api/v1"; //服务器

// const basePath_1 = "http://127.0.0.1:8080/ilz/api/v1"; //服务器 小曹
// const basePath_2 = "http://127.0.0.1:8080/ilz/api/v1"; //服务器 小曹




const basePath_1 = "http://**********:4888/ilz/api/v1"; // 小曹服务器
const basePath_2 = "http://**********:4888/ilz/api/v1"; // 小曹服务器

// const basePath_1 = "http://127.0.0.1:8080/ilz/api/v1"; //服务器 小曹
// const basePath_2 = "http://127.0.0.1:8080/ilz/api/v1"; //服务器 小曹




// const basePath_1='http://127.0.0.1:8086/ilz/api/v1';//本地
// const basePath_2='http://127.0.0.1:8086/ilz/api/v1';//本地

// const basePath_1 = "https://ihui.ihui.top/ilz/api/v1"; //服务器
// const basePath_2 = "https://ihui.ihui.top/ilz/api/v1"; //服务器

// const token= store.getters.accessToken;//获取token
// http://**************:8088/
// const basePath_2='http://exiangwu.cn.utools.club/api/v1';//远程对面
// const basePath_2='http://idblz.cn.utools.club/ilz/api/v1';//李工远程
// const basePath_2='http://************:8086/ilz/api/v1';//代表活动
//    const basePath_2='http://************:8086/ilz/api/v1';//代表活动的同事

// const token='6e9ef25f6b9a49d89cfc9e2da6b2f1d9'
const token = store.getters.accessToken;
console.log("🤗🤗🤗, token =>", token);

export default {
  basePath_1,
  basePath_2,
  token,
};
</script>
