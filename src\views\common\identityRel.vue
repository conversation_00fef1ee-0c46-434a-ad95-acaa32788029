<template>
  <a-modal
    :title="title"
    :visible.sync="dialogFormVisible"
    width="1200px"
    @cancel="close"
  >
    <a-input-search
      v-model="filterText"
      placeholder="输入关键字进行过滤"
      @search="onChange"
    ></a-input-search>
    <a-spin :indicator="indicator" :spinning="spinningShow">
      <div id="app">
        <a-tree
          ref="userTree"
          v-model="userTreeDefaultKey"
          checkable
          :expanded-keys="expandedKeys"
          multiple
          :replace-fields="replaceFields"
          :tree-data="userTreeData"
          @expand="onExpand"
          @check="onCheck"
        >
          <template slot="title" slot-scope="{ name }">
            <span
              v-if="searchValue && name.indexOf(searchValue) > -1"
              style="color: #f50"
              >{{ name }}</span
            >
            <span v-else>{{ name }}</span>
          </template>
        </a-tree>
      </div>
    </a-spin>

    <div
      slot="footer"
      class="dialog-footer"
      style="position: relative; padding-right: 15px; text-align: right"
    >
      <a-button @click="close">关闭</a-button>
      <a-button
        type="primary"
        :loading="loading"
        style="margin-left: 10px"
        @click="confirm"
        >保存</a-button
      >
    </div>
  </a-modal>
</template>
<script>
import {getTreeOrderByXsbh} from "@/api/user";
import { getIdentityTypeList } from "@/api/identity";
import checkPermission from "@/utils/permission";

export default {
  props: {
    rootOrgId: {
      type: String,
      default: "root",
    },
  },
  data() {
    return {
      spinningShow: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      max: 99,
      tabActive: "identity",
      tabName: "参加人员",
      //选择类型：1 身份选择 0 树选择 2 发布单位选择；不显示树 3 日程关联人员不显示树
      type: "1",
      loading: false,
      checkedShow: false,
      title: "",
      filterText: "",
      dialogFormVisible: false,
      //身份选择参数
      identityTypeUserList: [],
      checked: [],
      checkIdentyData: [],
      allChildData: [],
      selectedKeys: [],
      //用户树参数
      userTreeData: [],
      treeCheckedKey: [],
      userTreeDefaultKey: [],
      replaceFields: {
        title: "name",
        key: "uniqueId",
        children: "children",
      },
      // 搜索值
      searchValue: "",
      // 展开的节点
      expandedKeys: [],
      backupsExpandedKeys: [],
      // 是否自动展开
      autoExpandParent: true,
    };
  },
  watch: {
    filterText(val) {
      // this.$refs.userTree.filter(val);
    },
  },
  created() {
    this.initIdentityList();
    this.initUserTree();
  },
  methods: {
    initTreeFormValue() {
      this.filterText = '';
      this.expandedKeys = [];
      this.userTreeDefaultKey = [];
    },
    // 树状搜索
    onChange(e) {
      this.spinningShow = true;
      const value = e;
      this.searchValue = value;
      if (value == "") {
        this.$message.info("请输入关键字");
        this.spinningShow = false;
      } else {
        this.searchValue = value;
        this.expandedKeys = [];
        this.backupsExpandedKeys = [];
        const candidateKeysList = this.getkeyList(value, this.userTreeData, []);
        candidateKeysList.forEach((item) => {
          const key = this.getParentKey(item, this.userTreeData);
          if (key && !this.backupsExpandedKeys.some((item) => item === key))
            this.backupsExpandedKeys.push(key);
        });
        if (candidateKeysList.length == 0) {
          // 销毁提示
          this.$message.destroy();
          this.spinningShow = false;
          return this.$message.info("没有相关数据");
        }
        const { length } = this.backupsExpandedKeys;
        for (let i = 0; i < length; i++) {
          this.getAllParentKey(this.backupsExpandedKeys[i], this.userTreeData);
        }
        this.expandedKeys = this.backupsExpandedKeys.slice();
        this.spinningShow = false;
      }
    },
    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.name.indexOf(value) > -1) {
          keyList.push(node.uniqueId);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey;
      let temp;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          temp = this.getParentKey(key, node.children);
          if (node.children.some((item) => item.uniqueId === key)) {
            parentKey = node.uniqueId;
          } else if (temp) {
            parentKey = temp;
          }
        }
      }
      return parentKey;
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey;
      if (key) {
        parentKey = this.getParentKey(key, tree);
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey);
          }
          this.getAllParentKey(parentKey, tree);
        }
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    //展开点击节点下的子节点
    // onExpand (expandedKeys) {
    //   this.expandedKeys = expandedKeys;
    // },
    // 树状搜索

    // onChange (e) {
    //   const value = e.target.value;
    //   const expandedKeys = this.allChildData.map((item) => {
    //     if (item.name.indexOf(value) > -1) {
    //       return this.getParentKey(item.id, this.userTreeData);
    //     }
    //     return null;
    //   });
    //   Object.assign(this, {
    //     expandedKeys,
    //     searchValue: value,
    //     autoExpandParent: true,
    //   });
    // },
    // // 获取所在的父节点
    // getParentKey (id, tree) {
    //   let parentKey;
    //   for (let index = 0; index < tree.length; index++) {
    //     const node = tree[index];
    //     if (node.children) {
    //       if (node.children.some((item) => item.id === id)) {
    //         parentKey = node.id;
    //       } else if (this.getParentKey(id, node.children)) {
    //         parentKey = this.getParentKey(id, node.children);
    //       }
    //     }
    //   }
    //   return parentKey;
    // },

    submitUpload() {},

    tabClick(tab, event) {
      this.tabActive = tab.name;

      if (this.type == "0" || this.type == "1") {
        if (tab.name == "tree") {
          this.type = "0";
        } else {
          this.type = "1";
        }
      }
    },
    initUserTree() {
      let data = {
        tenantId: this.$tenantId,
        rootOrgId: this.rootOrgId,
      };
      getTreeOrderByXsbh(data).then((res) => {
        this.allChildData = [
          ...this.allChildData,
          ...(res.data.children || []),
        ];
        this.userTreeData = res.data.children;
      });
    },
    initIdentityList() {
      getIdentityTypeList().then((res) => {
        this.identityTypeUserList = res.data;
      });
    },

    handleHide() {
      this.dialogFormVisible = false;
    },

    handleShow(type, checkedData) {
      let that = this;

      if (type == "0") {
        that.title = "选择受邀范围";
        this.tabActive = "tree";
        this.tabName = "参加人员";
      }
      if (type == "1") {
        that.title = "选择发送范围";
        this.tabActive = "identity";
        this.tabName = "参加人员";
      }
      if (type == "2") {
        that.title = "选择发布单位";
        this.tabActive = "identity";
        this.tabName = "发布单位";
      }
      if (type == "3") {
        that.title = "选择参加人员";
        this.tabActive = "identity";
        this.tabName = "参加人员";
      }
      that.$forceUpdate();

      that.type = type;
      that.dialogFormVisible = true;
      that.checkIdentyData = [];
      that.treeCheckedKey = [];
      that.userTreeDefaultKey = [];

      //回选旧数据
      if (type == "1" || type == "2" || type == "3") {
        if (checkedData.length > 0) {
          checkedData.forEach((item) => {
            that.checkIdentyData.push(item);
          });
        }
      }

      if (type == "0") {
        if (checkedData.length > 0) {
          checkedData.forEach((item) => {
            // console.log(item);
            // let id=  item.split(';')[1]
            that.userTreeDefaultKey.push(item);
            that.treeCheckedKey.push(item);
          });
          if (that.$refs.userTree != null) {
            // that.$refs.userTree.setCheckedKeys(this.treeCheckedKey);
            // let checkedNodes = that.$refs.userTree.getCheckedNodes(false, true);
            checkedNodes.forEach((item) => (item.expanded = true));
          }
        }
      }

      if (type == "2") {
        this.max = 1;
      } else {
        this.max = 99;
      }
    },
    onSelect(data) {
      console.log(data, "onSelect");
    },
    // 树状选择
    onCheck(item, data) {
      this.checkedShow = true;
      this.checked = [];
      data.checkedNodesPositions.filter((item1) => {
        // console.log(item1,'ITEM1');
        this.checked.push(item1.node.componentOptions.propsData.dataRef);
      });
    },

    confirm() {
      let checkedData = [];
      if (this.type == "1" || this.type == "2" || this.type == "3") {
        checkedData = this.checkIdentyData;
      }
      if (this.type == "0") {
        // checkedData = this.$refs.userTree.getCheckedNodes();
        // this.$refs.userTree.setCheckedKeys([]);
      }
      if (this.type == "0" || this.type == "1") {
        if (this.$refs.userTree != null) {
          // this.$refs.userTree.setCheckedKeys([]);
        }
      }
      // 判断是否为树状数据
      if (this.checkedShow) {
        checkedData = this.checked;
      }

      this.treeCheckedKey = [];
      this.max = 99;
      //清空树勾选节点
      //收起所有树节点
      //this.$refs.userTree.$children.forEach((item) => (item.expanded = false));
      //回调父页面数据
      if (this.type != "2") {
        this.$parent.saveRel(this.type, checkedData);
      } else {
        this.$parent.savePublish(checkedData);
      }
      this.dialogFormVisible = false;
      this.checkIdentyData = [];
      this.filterText = '';
      this.expandedKeys = [];
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    close() {
      this.checkIdentyData = [];
      this.dialogFormVisible = false;
      this.filterText = '';
      this.expandedKeys = [];
    },

    handleCheckedIdentity(value) {
      this.checkedShow = false;
      this.checkIdentyData = this.unique(value);
    },

    //数组去重
    unique(arr) {
      return Array.from(new Set(arr));
    },
    checkPermission,
  },
};
</script>
<style scoped>
.el-checkbox.is-bordered.el-checkbox--mini {
  margin-bottom: 5px;
  margin-left: 10px;
}
::v-deep .ant-tree {
  max-height: 500px;
  width: 100%;
  overflow-y: auto;
}
</style>
