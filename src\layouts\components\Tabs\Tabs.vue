<template>
  <div class="yexian">
    <div style=" display: flex;">
        <a-tabs default-active-key="1"
            :tab-position="mode"
            v-model="activeKey"
            @contextmenu.prevent="show"
            @edit="onEdit"
            type="editable-card"
            @change="onChange"
            @tabClick="callback"
          >
      <a-tab-pane v-for="(item,index) in tabsList"
                  :key="item.path+item.title">
        <template slot="tab">
          <span @mouseleave="mouseleave "
                @mouseover="mouseover">{{item.name  }}</span>
          <span style="display: none">{{ index  }}</span>
          <span style="display: none">{{ item.path+item.title  }}</span>
        </template>
      </a-tab-pane>
     

    </a-tabs>
       <div
          style="color: rgb(24, 144, 255);cursor: pointer;
          width: 330px;
          line-height: 35px;
          color: rgb(24, 144, 255);
          cursor: pointer;
          padding-left: 6px;
          " 
          @click="callback('返回上一页')" >
        <div>
          <span >« 返回上页</span>
       </div>
    </div>
    </div>
  
    <a-menu v-model="selectedKeys"
            @mouseleave="mouseLeaveMenu"
            @mouseover="mouseOverMenu"
            ref="yexian_gn"
            class="yexian_gn">
      <a-menu-item @click="onClearLeft"
                   key="1">
        关闭左侧
      </a-menu-item>
      <a-menu-item @click="onClearRight"
                   key="2">
        关闭右侧
      </a-menu-item>
      <a-menu-item @click="onClear"
                   key="3">
        关闭其他
      </a-menu-item>
      <a-menu-item @click="refresh"
                   key="4">
        刷新
      </a-menu-item>
    </a-menu>
  </div>
</template>

<script>
import events from "@/components/events.js";
import { EventBus } from '@/event-bus'
export default {
  data () {
    return {
      mode: 'top',
      tabsList: [],
      currentPath: '',
      activeKey: '',
      gn: '',
      key: true,
      index: '',
      selectedKeys: [],
      newActiveKey: ''
    }
  },

  // watch: {
  // '$route'(to, from) {
  //   // 当路由变化时执行的操作
  //   console.log('Route changed from', from.fullPath, 'to', to.fullPath);
  //   // 你可以在这里添加加载完成后的回调逻辑
  //   }
  // },
  created() {
    // sessionStorage.setItem("currentTab", null);
    // var obj = {
    //       name: this.$router.history.current.name,
    //       path: this.$router.history.current.path,
    //       title: this.$router.history.current.name,
    //       iScolor: "",
    //       isKey:
    //         this.$router.history.current.path + this.$router.history.current.name,
    //       openKeys: [],
    //     };
    //     sessionStorage.setItem("currentTab", JSON.stringify(obj));
        this.tabsList = []
        // if (JSON.parse(sessionStorage.getItem("currentTab")) != null) {
        // this.tabsList.push(JSON.parse(sessionStorage.getItem("currentTab")));
        // this.activeKey = sessionStorage.getItem("activeKey");
      // }
    // 设置页签
    events.$on("onTabs", (key, name, title, iScolor, openKeys, isMenu) => {
      sessionStorage.setItem("currentTab", JSON.stringify(obj));
      // key 跳转的地址
      // name 菜单名字
      // title 系统名字
      // iScolor 一级tab指向
      // openKeys  打开的菜单
      // isMenu 判断是否是点击一级tabs跳转
      if (name == '会议通知管理' || name == '会议报道查询') return
      var obj = {
        name: name,
        path: key,
        title: title,
        iScolor: iScolor,
        isKey: key + title,
        openKeys: openKeys
      }
      // console.log(isMenu, 'isMenu');
      if (isMenu != undefined) {
        //  菜单栏定位到对应的页面
        events.$emit("sendEvenLeadig", {
          path: key,
          item: obj
        });
      }
      // 找到就返回这一项
      var tab = this.tabsList.find(item => {
        return item.isKey == obj.isKey
      })
      // 找不到就push
      if (tab == undefined) {
        this.tabsList.push(obj)
      }
      // 设置选中的tabs
      this.activeKey = key + title
      sessionStorage.setItem('activeKey', this.activeKey)
      sessionStorage.setItem('currentTab', JSON.stringify(obj))
      console.log(this.tabsList);
    });
    // console.log(JSON.parse(sessionStorage.getItem('tabsList')), '11');
    // console.log(this.activeKey, 'activeKey');
    // if (JSON.parse(sessionStorage.getItem("currentTab")) != null) {
    //     this.tabsList.push(JSON.parse(sessionStorage.getItem("currentTab")));
    //     this.activeKey = sessionStorage.getItem("activeKey");
    //   }
  },
  beforeDestroy () {
    events.$off('onTabs')
  },
  mounted() {
    this.gn = this.$refs.yexian_gn;
      // var obj = {
      //     name: this.$router.history.current.name,
      //     path: this.$router.history.current.path,
      //     title: this.$router.history.current.name,
      //     iScolor: "",
      //     isKey:
      //       this.$router.history.current.path + this.$router.history.current.name,
      //     openKeys: [],
      //   };
      //   sessionStorage.setItem("currentTab", JSON.stringify(obj));
      //   if (JSON.parse(sessionStorage.getItem("currentTab")) != null) {
      //   this.tabsList.push(JSON.parse(sessionStorage.getItem("currentTab")));
      //   this.activeKey = sessionStorage.getItem("activeKey");
  },
  methods: {
     goBack() {
       this.$router.go(-1); 
    },
    onClearLeft () {
      // console.log(this.tabsList, ' this.tabsList');
      // console.log(this.index);
      // console.log(this.activeKey);
      if (this.tabsList.length == 1 || this.index == 0) {
        this.gn.$el.style.display = 'none'
        this.selectedKeys = []
        return this.$message.info('已经是最左侧了哦')
      } else {
        var currentActiveKey = this.activeKey
        var key = this.tabsList.some((item, index) => {
          if (item.isKey == currentActiveKey) {
            return this.index >= index
          }
        })
        console.log(key);
        this.tabsList = this.tabsList.splice(this.index);
        if (key) {
          this.activeKey = this.tabsList[0].path + this.tabsList[0].title
        }
        if (currentActiveKey != this.activeKey && key) {
          this.onChange(this.activeKey)
        }
        this.gn.$el.style.display = 'none'
        this.selectedKeys = []
      }
    },
    onClearRight () {
      // console.log(this.tabsList, ' this.tabsList');
      // console.log(this.index);
      var index = Number(this.index) + 1
      if (this.tabsList.length == 1 || index == this.tabsList.length) {
        this.gn.$el.style.display = 'none'
        this.selectedKeys = []
        return this.$message.info('已经是最右侧了哦')
      } else {
        var currentActiveKey = this.activeKey
        var key = this.tabsList.some((item, index) => {
          if (item.isKey == currentActiveKey) {
            return this.index < index
          }
        })
        this.tabsList.splice(index);
        this.gn.$el.style.display = 'none'
        var length = this.tabsList.length - 1
        if (key) {
          this.activeKey = this.tabsList[length].path + this.tabsList[length].title
        }
        if (currentActiveKey != this.activeKey && key) {
          this.onChange(this.activeKey)
        }
        this.selectedKeys = []
      }
    },
    onClear () {
      if (this.tabsList.length == 1) {
        this.gn.$el.style.display = 'none'
        this.selectedKeys = []
        return this.$message.info('没有其他页面可以关闭了哦')
      } else {

        var nueArr = []
        nueArr.push(this.tabsList[this.index])
        this.tabsList = nueArr
        this.gn.$el.style.display = 'none'
        var currentActiveKey = this.activeKey
        this.activeKey = this.tabsList[0].path + this.tabsList[0].title
        if (currentActiveKey != this.activeKey) {
          this.onChange(this.activeKey)
        }
        this.selectedKeys = []
      }
    },
    refresh () {
      // this.activeKey = this.tabsList[this.index].path + this.tabsList[this.index].title
      if (this.newActiveKey == this.activeKey) {
        // this.onChange(this.activeKey)
        this.$router.push({
          path: '/convenTionList/excessive',
        })
        setTimeout(() => {
          this.$router.back()
        });
      }
      this.selectedKeys = []
      this.gn.$el.style.display = 'none'
    },
    mouseover () {
      // console.log('离开tab');
      this.gn.$el.style.display = 'none'
      this.selectedKeys = []
    },
    mouseleave () {
      // console.log('离开tab');
      var that = this
      window.setTimeout(function () {
        if (that.key) {
          that.gn.$el.style.display = 'none'
          that.selectedKeys = []
        }
      }, 500)
    },
    mouseLeaveMenu () {
      // console.log('离开功能');
      this.gn.$el.style.display = 'none'
      this.selectedKeys = []

      this.key = true
    },
    mouseOverMenu () {
      // console.log('进来');
      this.key = false
      this.gn.$el.style.display = 'inline-block'
    },
    callback (val) {
      if (val=='返回上一页') {
           let menuIndex = JSON.parse(window.sessionStorage.getItem("menuIndex"));
        if (menuIndex > 1) {
        let menuSize = JSON.parse(window.sessionStorage.getItem("menuIndex"))-2;
          window.sessionStorage.setItem("menuIndex",  menuSize);
         this.$router.go(-1)
        }
      
      }
    },
    //tabs切换
    onChange (path) {
      // 页面切换
      if (this.currentPath != path&&path!='返回上一页') {
        console.log(path);
        this.currentPath = path
        //切换侧边栏并且更换页面和对应的系统数据
        this.tabsList.forEach(item => {
          if (item.path + item.title == path) {
            events.$emit('onSideBar', item.iScolor, item)
            sessionStorage.setItem('activeKey', path)
            sessionStorage.setItem('currentTab', JSON.stringify(item))
          }
        })
      }
    },
    // 删除按钮
    onEdit (path) {
      var onIndex = 0
      if (this.tabsList.length == 1) {
        return this.$message.info('这是最后一页，不能删除了哦')
      } else {
        this.tabsList = this.tabsList.filter((item, index) => {
          if (item.path + item.title == path) {
            onIndex = index
            // EventBus.$emit('FxCleanTabCache', item.path)
          }
          return item.path + item.title != path
        })
      }
      // 设置删除后选中项
      // 如果删除项和当前选中项一致
      if (path == this.activeKey) {
        // 如果是最后一项
        if (this.tabsList.length == onIndex) {
          this.activeKey = this.tabsList[onIndex - 1].path + this.tabsList[onIndex - 1].title
        } else {
          // 不是最后一项
          this.activeKey = this.tabsList[onIndex].path + this.tabsList[onIndex].title
        }
        // 切换路由
        this.onChange(this.activeKey)
      }

    },
    show (item) {
      if (item.target.parentNode.childNodes[1]) {
        this.index = item.target.parentNode.childNodes[1].innerHTML
        this.newActiveKey = item.target.parentNode.childNodes[2].innerHTML
        console.log(this.newActiveKey);
        this.gn.$el.style.left = item.screenX / 900 * 100 + 'vh'
        this.gn.$el.style.display = 'inline-block'
      }
    }
  },

}
</script>

<style lang="scss" scoped>
// 页签
.yexian_gn {
  width: 100px;
  z-index: 999;
  display: none;
  text-align: center;
  // background-color: red;
  position: absolute;
  :hover {
    color: #3296fa !important;
  }
}
::v-deep .ant-menu-item-active {
  background-color: #e6f7ff !important;
}
::v-deep .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: #e6f7ff !important;
  color: #3296fa !important;
}

// 页签
.yexian {
  background-color: #fff !important;
  width: 100%;
  // margin-left: 200px;
  // margin-right: 200px;
  ::v-deep .ant-tabs-bar {
    margin: 0 !important;
  }
  .ant-tabs-nav .ant-tabs-tab {
    padding: 10px 10px !important;
  }
  .ant-tabs {
    top: -2px;
  }
  ::v-deep .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
    background: #fff !important;
  }
}
.ant-tabs-tab-prev-icon-target {
  // font-size: 20px !important;
   @include add-size($font_size_16);
  margin-top: 4px;
  // color: rgba(0, 0, 0, 0.65) !important;
}
.ant-tabs-tab-next-icon-target {
  // font-size: 20px !important;
   @include add-size($font_size_16);
  margin-top: 4px;
  // color: rgba(0, 0, 0, 0.65) !important;
}
::v-deep .ant-tabs.ant-tabs-card .ant-tabs-extra-content {
  display: none !important;
}
</style>
