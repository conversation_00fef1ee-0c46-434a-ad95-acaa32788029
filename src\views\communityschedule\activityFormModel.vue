<template>
  <div>
    <!-- <a-button   style="float: left;z-index: 99;" @click="$router.go(-1)"> 返回</a-button> -->
    <a-spin :spinning="listLoading">
      <a-form-model
        ref="form"
        :model="form"
        style="padding-top: 50px"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="代表大会">
              <a-select
                v-model="form.dhDm"
                placeholder="请选择代表大会"
                allow-clear
                show-search
                :disabled="isDisabled"
                @change="handleDbdhChange"
              >
                <a-select-option
                  v-for="item in dbdhList"
                  :key="item.id"
                  :label="item.dhmc"
                  :value="item.dhDm"
                >
                  {{ item.dhmc }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="10" :offset="2">
            <a-form-model-item label="届次" prop="jcDm">
              <a-select
                v-model="form.jcDm"
                placeholder="请选择届次"
                allow-clear
                show-search
                :disabled="isDisabled"
              >
                <a-select-option
                  v-for="item in SessionList"
                  :key="item.jcDm"
                  :label="item.levelName"
                  :value="item.jcDm"
                >
                  {{ item.levelName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="时间" prop="serverTime">
              <a-date-picker
                v-model="form.serverTime"
                style="width: 100%"
                placeholder="选择日期时间"
                :disabled="isDisabled"
              >
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col :span="10" :offset="2">
            <a-form-model-item label="行政区县" prop="administrativeAreaId">
              <a-select
                v-model="form.administrativeAreaId"
                style="width: 100%"
                placeholder="请选择行政区县"
                :disabled="isDisabled"
                @change="handleAdministrativeAreaChange"
              >
                <a-select-option
                  v-for="item in administrativeAreas"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="街道乡镇" prop="streetTownId">
              <a-select
                v-model="form.streetTownId"
                style="width: 100%"
                placeholder="请选择街道乡镇"
                :disabled="isDisabled"
                @change="handlestreetTownChange"
              >
                <a-select-option
                  v-for="item in streetTowns"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  >{{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="10" :offset="2">
            <a-form-model-item label="联络站" prop="liaisonStationId">
              <a-select
                v-model="form.liaisonStationId"
                style="width: 100%"
                placeholder="请选择联络站"
                :disabled="isDisabled"
                @change="handleListLiaisonStationChange"
              >
                <a-select-option
                  v-for="item in liaisonStations"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  >{{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="联系人名称" prop="contactName">
              <a-input
                v-model="form.contactName"
                auto-complete="off"
                :disabled="isDisabled"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="10" :offset="2">
            <a-form-model-item label="联系人电话" prop="contactPhoneNumber">
              <a-input
                v-model="form.contactPhoneNumber"
                auto-complete="off"
                :disabled="isDisabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="受邀范围" prop="inviteRangeDesc">
              <div class="searchStyle">
                <a-input
                  v-model="form.inviteRangeDesc"
                  disabled
                  enter-button
                ></a-input>
                <a-button
                  :disabled="oper == 'view'"
                  type="primary"
                  icon="search"
                  style="z-index: 999"
                  @click="openattendMembers"
                ></a-button>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="10" :offset="2">
            <a-form-model-item label="接待群众人数" prop="peopleNum">
              <a-input
                v-model="form.peopleNum"
                auto-complete="off"
                :disabled="isDisabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="活动内容" prop="mark">
              <a-input
                v-model="form.mark"
                type="textarea"
                auto-complete="off"
                :disabled="isDisabled"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="10" :offset="2">
            <a-form-model-item label="群众意见" prop="content">
              <a-input
                v-model="form.content"
                type="textarea"
                placeholder="请输入群众反映的意见建议或问题"
                auto-complete="off"
                :disabled="isDisabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item
              style="text-align: center"
              :label-col="{ span: 0 }"
              :wrapper-col="{ span: 24 }"
            >
              <a-button
                v-if="oper == 'update'"
                type="primary"
                style="margin-left: 10px"
                @click="handUpdate"
                >更 新 </a-button
              ><!-- 编辑 -->
              <a-button
                v-if="oper == 'create'"
                type="primary"
                style="margin-left: 10px"
                @click="confirm(false)"
                >暂存为草稿
              </a-button>
              <a-button
                v-if="oper == 'create'"
                type="primary"
                style="margin-left: 10px"
                @click="confirm(true)"
                >保存并送审
              </a-button>
              <a-button
                v-if="oper == 'audit'"
                type="primary"
                style="margin-left: 10px"
                @click="audit"
                >审 核</a-button
              >
              <a-button
                v-if="oper != 'create'"
                style="margin-left: 10px"
                @click="lczzjl"
                >流程流转记录</a-button
              >
              <a-button style="margin-left: 10px" @click="handleClose()"
                >取 消</a-button
              >
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>

    <!-- 受邀范围树 -->
    <!-- <invitedTree
      ref="ShowinvitedTree"
      :jc-dm="form.jcDm"
      :default-select="defaultSelect"
      @confirm="confirminvitedTree"
    >
    </invitedTree> -->
    <identity-rel-any-with-db
      ref="identityRelAny"
      default-key="1"
      @saveRel="saveRel"
    ></identity-rel-any-with-db>
    <!-- 流程弹出页 -->
    <submitForCensorship
      ref="submitForCensorship"
      :proc-inst-id="procInstId"
      :ids="ids"
      @complete="handleComplete"
    >
    </submitForCensorship>
    <!-- 流程流转记录 -->
    <lczzjl ref="lczzjl" :proc-inst-id="procInstId"></lczzjl>
  </div>
</template>

<script>
import invitedTree from "@/views/common/representative/invitedTree.vue";
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
import { listForAdmin as getStations } from "@/api/station";
import {
  save,
  update,
  getById,
  checkIsExistLiaisonStationIdAndUserIds,
} from "@/api/communityscheduleactivity";
import submitForCensorship from "@/views/common/submitForCensorship";
import lczzjl from "@/views/common/lczzjl";
import { communityScheduleComplete } from "@/api/xjgw";
import events from "@/components/events.js";
import { getLevelList } from "@/api/registrationMage/tableIng";
import { getMyDbdhList } from "@/api/system/dbdh";
import { levelList } from "@/api/area";
import identityRelAnyWithDb from "@/views/common/identityRelAnyWithDb";

export default {
  components: {
    invitedTree,
    submitForCensorship,
    lczzjl,
    identityRelAnyWithDb
  },
  props: {
    form: {
      type: Object,
      default: null,
    },
    oper: {
      type: String,
      default: "view",
    },
    fromType: {
      type: String,
      default: "no_dbz", //非待办中
    },
  },
  data() {
    var validateSession = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请填写届次信息"));
      }
      callback();
    };
    var validateServerTime = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请选择时间"));
      }
      callback();
    };
    var validateAdministrativeArea = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请选择行政区县"));
      }
      callback();
    };
    var validateStreetTown = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请选择乡镇街道"));
      }
      callback();
    };
    var validateLiaisonStation = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请选择联络站"));
      }
      callback();
    };
    var validateContactName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请填写联系人名称"));
      }
      callback();
    };
    var validateContactPhoneNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请填写联系人电话"));
      } else {
        let reg =
          /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
        let tel_reg = /^0\d{2,3}-?\d{7,8}$/;
        reg.test(value) || tel_reg.test(value)
          ? callback()
          : callback(new Error("请填写联系人电话"));
      }
      // callback();
    };
    var validateMember = (rule, value, callback) => {
      if (value.length == 0) {
        callback(new Error("请选择受邀代表"));
      }
      callback();
    };
    var validatemark = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请填写活动内容"));
      }
      callback();
    };
    var validatecontent = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请填写群众反映的意见建议或问题"));
      }
      callback();
    };
    var validatepeopleNum = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请填写接待群众人数"));
      } else {
        // value=Number(value)
        const reg = /^\d+$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请填写接待群众人数"));
      }
      callback();
    };
    return {
      defaultSelect: [], //受邀范围
      rules: {
        session: [{ validator: validateSession, trigger: "blur" }],
        serverTime: [
          { required: true, validator: validateServerTime, trigger: "change" },
        ],
        administrativeAreaId: [
          {
            required: true,
            validator: validateAdministrativeArea,
            trigger: "blur",
          },
        ],
        streetTownId: [
          { required: true, validator: validateStreetTown, trigger: "change" },
        ],
        liaisonStationId: [
          {
            required: true,
            validator: validateLiaisonStation,
            trigger: "change",
          },
        ],
        contactName: [
          { required: true, validator: validateContactName, trigger: "blur" },
        ],
        contactPhoneNumber: [
          {
            required: true,
            validator: validateContactPhoneNumber,
            trigger: "blur",
          },
        ],
        countryMemberIds: [
          { required: true, validator: validateMember, trigger: "blur" },
        ],
        mark: [{ required: true, validator: validatemark, trigger: "blur" }],
        content: [
          { required: true, validator: validatecontent, trigger: "blur" },
        ],
        peopleNum: [
          { required: true, validator: validatepeopleNum, trigger: "blur" },
        ],
      },
      dbdhList: [],
      SessionList: [],
      //行政区域列表
      administrativeAreas: [],
      //街道数据
      streetTowns: [],
      //联络站数据
      liaisonStations: [],
      //是否只读
      isDisabled: true,
      procInstId: null,
      // activityId: null,

      //流程id
      ids: [],
      listLoading: false,
      LCid: "",
      // 组件临时demo对象
      temp: {},
      receiverTreeNodeList: []
    };
  },
  watch: {
    "form.id": {
      deep: true,
      handler: function (newVal, oldVal) {
        this.$nextTick(() => {
          if (this.form.administrativeAreaId) {
            this.liststreetTowns(this.form.administrativeAreaId);
          }
          if (this.form.streetTownId) {
            this.listLiaisonStations(this.form.streetTownId);
          }
        });
      },
    },
    "form.dhDm": {
      handler: function (newVal, oldVal) {
        console.log("watch", newVal, oldVal);
        this.getLevelList();
      },
    },
    ids: {
      deep: true,
      handler: function (newVal, oldVal) {
        console.log(newVal, "newVal");
        console.log(this.ids, "newValnewValnewVal");
      },
    },
    oper: {
      handler: function (newVal, oldVal) {
        if (newVal == "view") {
          this.isDisabled = true;
        } else {
          this.isDisabled = false;
        }
      },
    },
    "form.attendMembers": {
      handler: function (newVal) {
        if(! newVal) {
          return
        }
        let relName = ""
        this.receiverTreeNodeList = []
        newVal.forEach((item) => {
          relName += (item.name || item.userName)  + ",";
          this.receiverTreeNodeList.push(item.inviteId);
        });
        this.$set(this.form, 'inviteRangeDesc',  relName.substring(0, relName.length - 1))
      },
      deep: true,
      immediate: true
    },
  },
  created() {
    if (this.oper == "view") {
      this.isDisabled = true;
    } else {
      this.isDisabled = false;
    }
    this.listAdministrativeAreas();

    this.getMyDbdhList();
    this.getById(this.$route.query.id);
  },
  methods: {
    /**
     * 预加载数据
     */
    listAdministrativeAreas() {
      // 获取行政区县
      getAdministrativeAreas().then((response) => {
        this.administrativeAreas = response.data;
      });
    },
    liststreetTowns(administrativeAreaId) {
      getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
        }
      );
    },
    // 获取联络站
    listLiaisonStations(streetTownId) {
      getStations({ streetTownId: streetTownId }).then((response) => {
        this.liaisonStations = response.data;
      });
    },
    getById(id) {
      if (id) {
        getById({ id: id }).then((res1) => {
          if (res1.code == "0000") {
            // 暂做回显
            let checkIdentyData = [];
            let checked = [];
            if (res1.data.attendMembers.length > 0) {
              res1.data.attendMembers.forEach((item) => {
                if (item.postId) {
                  checkIdentyData.push("U" + item.postId);
                  item.id = "U" + item.postId;
                  item.fullName = item.userName;
                  checked.push(item);
                } else {
                  checkIdentyData = [];
                  checked = [];
                }
              });
            }
            this.$refs.ShowinvitedTree.checkIdentyData = checkIdentyData;
            this.$refs.ShowinvitedTree.checked = checked;
            this.$forceUpdate();
          }
        });
      }
    },
    openattendMembers() {
      if (this.groupg == 1) {
        // 用户组
        this.$refs["identityRelAny"].handleShow(
          this.noticeForm.userGroupId
        );
      } else {
        // 机构树
        this.$refs["identityRelAny"].handleShow("1", this.receiverTreeNodeList);
      }
      // this.$refs.ShowinvitedTree.newOrgTreeDialogVisible = true;
      // this.$refs.ShowinvitedTree.orgTreeDialogTitle = "选择受邀范围";
    },
    // confirminvitedTree(data) {
    //   if (data) {
    //     this.defaultSelect = data;
    //     this.form.attendMembers = [];
    //     this.form.inviteRangeDesc = "";
    //     this.form.attendMembers = this.recursionChild(data);
    //     this.form.attendMembers.forEach((item, index) => {
    //       if (index !== this.form.attendMembers.length - 1) {
    //         this.form.inviteRangeDesc =
    //           this.form.inviteRangeDesc + item.fullName + "、";
    //       } else {
    //         this.form.inviteRangeDesc =
    //           this.form.inviteRangeDesc + item.fullName;
    //       }
    //     });
    //   }
    // },
    saveRel(type, checkedData) {
      //清空原有的接收范围
      // this.receiverTreeNodeList = [];
      let beInvited = [];

      //用户树选择赋值
      if (type == "1") {
        if (checkedData.length > 0) {
          //遍历返回的身份id列表
          checkedData.forEach((treeNode) => {
            if (treeNode.itemType == "2") {
                beInvited.push({
                  userId: treeNode.id,
                  inviteId: treeNode.uniqueId,
                  dhDm: treeNode.dhDm,
                  jcDm: treeNode.jcDm,
                  orgId: treeNode.orgId,
                  deptId: treeNode.deptId,
                  postId: treeNode.postId,
                  name: treeNode.name
                });
                // this.receiverTreeNodeList.push(treeNode.uniqueId);
            }
          });
          this.form.attendMembers = beInvited
        }
      }

      if (type == "2") {
        let relName = ""
        let userGroupId = [];
        checkedData.forEach((treeNode) => {
          relName += treeNode.name + ",";
          userGroupId.push(treeNode.id);
        });
        this.temp.userGroupId = userGroupId.toString();
        this.temp.userGroupName = relName.substring(0, relName.length - 1);
      }
    },
    recursionChild(arr) {
      var res = [];
      arr.map((item) => {
        if (item.children && Array.isArray(item.children)) {
          // res = res.concat(this.recursionChild(item.children));
        } else {
          res.push(item);
        }
      });
      return res;
    },
    // 选择街道乡镇
    handlestreetTownChange(val) {
      this.form.liaisonStationId = undefined;
      this.listLiaisonStations(val);
    },
    handleListLiaisonStationChange(id) {
      for (let index = 0; index < this.liaisonStations.length; index++) {
        const element = this.liaisonStations[index];
        if (element.id == id) {
          if (element.contactPhoneNumber) {
            this.form.contactPhoneNumber = element.contactPhoneNumber;
          }
          if (element.contactName) {
            this.form.contactName = element.contactName;
          }
          if (element) {
            this.form.liaisonStation = element;
          }
          this.$forceUpdate();
          break;
        }
      }
    },
    handleAdministrativeAreaChange(val) {
      this.form.streetTownId = undefined;
      this.liststreetTowns(val);
    },
    confirm(isAudit) {
      // TODO 删除旧的保存代表逻辑
      if (
        this.form.attendMembers == null ||
        this.form.attendMembers.length <= 0
      ) {
        this.$message.warning("请选择受邀代表");
        reject("参数检验错误");
      }
      this.form.attendMembers.forEach((item) => {
        item.userName = item.fullName;
      });
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.listLoading = true; //菊花转呀转

          //去后端检查是否需要弹出提示,检查用户id与日期的冲突，提醒录入员
          let userIds = [];
          this.form.attendMembers.forEach((item) => {
            userIds.push(item.userId);
          });
          let checkData = {
            serverTime: this.form.serverTime,
            userIds: userIds,
          };
          this.form.peopleNum = Number(this.form.peopleNum);
          checkIsExistLiaisonStationIdAndUserIds(checkData).then((res) => {
            if (res.data) {
              this.$confirm({
                cancelText: "取消",
                okType: "danger",
                okText: "确定，继续录入",
                title: "温馨提示",
                content: "该代表该日已录入活动，请确认是否重复？",
                onOk: () => {
                  saveFun();
                },
                onCancel: () => {
                  this.$message.info("您已取消操作！");
                  this.listLoading = false;
                },
              });
            } else {
              saveFun();
            }
          });
          let vm = this;

          function saveFun() {
            save(vm.form)
              .then((res) => {
                vm.$message.success("保存成功");
                vm.handleClose();
                vm.$emit("handleOk");
                vm.$refs.form.resetFields();
                if (isAudit) {
                  if (res.code == "0000") {
                    vm.listLoading = false;
                    let id = res.data;
                    //获取流程id
                    setTimeout(() => {
                      // 这里就是处理的事件
                      getById({ id: id }).then((res1) => {
                        if (res1.code == "0000") {
                          vm.ids.push(res1.data.activityId);
                          vm.procInstId = res1.data.procInstId;
                          // 暂做回显
                          let checkIdentyData = [];
                          let checked = [];
                          if (res1.data.attendMembers.length > 0) {
                            res1.data.attendMembers.forEach((item) => {
                              if (item.postId) {
                                checkIdentyData.push("U" + item.postId);
                                item.id = "U" + item.postId;
                                item.fullName = item.userName;
                                checked.push(item);
                              } else {
                                checkIdentyData = [];
                                checked = [];
                              }
                            });
                          }
                          this.$refs.ShowinvitedTree.checkIdentyData =
                            checkIdentyData;
                          this.$refs.ShowinvitedTree.checked = checked;
                          this.$forceUpdate();
                        }
                      });
                    }, 2000);
                    vm.$refs.submitForCensorship.visible = true;
                  } else {
                    vm.$message.error(res.msg);
                  }
                }
              })
              .catch((error) => {
                vm.listLoading = false;
              });
          }
        } else {
          this.$message.warning("请完善必填项信息");
          reject("参数检验错误");
        }
      });
    },
    handUpdate() {
      // TODO 删除旧的保存代表逻辑
      if (
        this.form.attendMembers == null ||
        this.form.attendMembers.length <= 0
      ) {
        this.$message.warning("请选择受邀代表");
        reject("参数检验错误");
      }
      this.form.attendMembers.forEach((item) => {
        if (!item.userName) {
          item.userName = item.fullName;
        }
      });
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.peopleNum = Number(this.form.peopleNum);
          // 编辑接口
          update(this.form).then((response) => {
            this.$message.success("保存成功");
            this.handleClose();
            this.$emit("handleOk");
            this.$refs.form.resetFields();
          });
        } else {
          this.$message.warning("请完善必填项信息");
          reject("参数检验错误");
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
    //审核
    audit() {
      this.ids.push(this.form.activityId);
      this.procInstId = this.form.procInstId;
      this.$refs.submitForCensorship.visible = true;
    },
    //审核保存发送
    handleComplete(data) {
      communityScheduleComplete(data).then((res) => {
        if (res.data.code == "0000") {
          this.ids = [];
          this.$refs.submitForCensorship.successComplete();
          // 判断是否是待办事项跳转过来
          if (this.fromType == "dbz") {
            // 跳转待办事项中
            this.$router.push({
              path: "/community/todo",
            });
          } else {
            this.$router.push({
              path: "/community/handled",
            });
            events.$emit("sendEven", {
              path: "/community/handled",
              openKey: "代表进社区",
            });
          }

          this.$message.success(res.data.msg);
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    lczzjl() {
      this.$refs.lczzjl.xuanze(this.form.procInstId);
    },
    getLevelList() {
      this.SessionList = [];
      if (!this.form.dhDm) return;
      levelList({ pageNum: 1, pageSize: 999, dhDm: this.form.dhDm }).then(
        (res) => {
          this.SessionList = res.rows;
          if (!this.form.jcDm) {
            const find = this.SessionList.find((it) => it.currentLevel == "1");
            if (find) this.form.jcDm = find.jcDm;
          }
        }
      );
    },
    handleDbdhChange() {
      const item = this.dbdhList.find((it) => it.dhDm === this.form.dhDm);
      this.form.sfDm = item ? item.sfDm : "";
      this.form.jcDm = "";
      this.getLevelList();
    },
    getMyDbdhList() {
      getMyDbdhList({}).then((res) => {
        this.dbdhList = res.data;
        if (this.dbdhList.length && !this.form.dhDm) {
          this.form.dhDm = this.dbdhList[0].dhDm;
          this.form.sfDm = this.dbdhList[0].sfDm;
          this.form.jcDm = "";
          this.getLevelList();
        }
      });
    },
  },
};
</script>

<style></style>
