!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e=e||self).WebOfficeSDK={})}(this,function(e){"use strict";var n=function(){return(n=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var a in n=arguments[t])Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a]);return e}).apply(this,arguments)};function t(e,n,t,r){return new(t||(t=Promise))(function(a,i){function o(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var n;e.done?a(e.value):(n=e.value,n instanceof t?n:new t(function(e){e(n)})).then(o,s)}c((r=r.apply(e,n||[])).next())})}function r(e,n){var t,r,a,i,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,r&&(a=2&i[0]?r.return:i[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,i[1])).done)return a;switch(r=0,a&&(i=[2&i[0],a.value]),i[0]){case 0:case 1:a=i;break;case 4:return o.label++,{value:i[1],done:!1};case 5:o.label++,r=i[1],i=[0];continue;case 7:i=o.ops.pop(),o.trys.pop();continue;default:if(!(a=(a=o.trys).length>0&&a[a.length-1])&&(6===i[0]||2===i[0])){o=0;continue}if(3===i[0]&&(!a||i[1]>a[0]&&i[1]<a[3])){o.label=i[1];break}if(6===i[0]&&o.label<a[1]){o.label=a[1],a=i;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(i);break}a[2]&&o.ops.pop(),o.trys.pop();continue}i=n.call(e,o)}catch(e){i=[6,e],r=0}finally{t=a=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}var a=function(){function e(){}return e.add=function(n){e.HANDLE_LIST.push(n),window.addEventListener("message",n,!1)},e.remove=function(n){var t=e.HANDLE_LIST.indexOf(n);t>=0&&e.HANDLE_LIST.splice(t,1),window.removeEventListener("message",n,!1)},e.empty=function(){for(;e.HANDLE_LIST.length;)window.removeEventListener("message",e.HANDLE_LIST.shift(),!1)},e.parse=function(e){try{return"object"==typeof e?e:e?JSON.parse(e):e}catch(n){return console.log("Message.parse Error:",n),e}},e.HANDLE_LIST=[],e}();function i(e){return"[object Function]"==={}.toString.call(e)}var o,s,c,u,l={origin:""};function d(e,n){l[e]=n}function f(e){return l[e]}function p(e){var n=f("origin");return!!function(e,n){return e!==n&&(e.replace(/www\./i,"").toLowerCase()!==n.replace(/www\./i,"").toLowerCase()||(e.match("www.")?void 0:(d("origin",n),!1)))}(n,e.origin)&&(console.warn("postMessage 域名检查不通过",{safeOrigin:n,eventOrigin:e.origin}),!0)}!function(e){e.unknown="unknown",e.spreadsheet="s",e.writer="w",e.presentation="p",e.pdf="f"}(o||(o={})),function(e){e.wps="w",e.et="s",e.presentation="p",e.pdf="f"}(s||(s={})),function(e){e.nomal="nomal",e.simple="simple"}(c||(c={})),function(e){e[e.requestFullscreen=1]="requestFullscreen",e[e.exitFullscreen=0]="exitFullscreen"}(u||(u={}));var v,b,h,m=(v=0,function(){return v+=1}),g=function(e,n,t){void 0===t&&(t=!0);var r=n;if(!b){var a=function e(n){var t=n.clientHeight;var r=n.clientWidth;0!==t||0!==r||h?0===t&&0===r||!h||(h.disconnect(),h=null):window.ResizeObserver&&(h=new ResizeObserver(function(t){e(n)})).observe(n);b.style.cssText+="height: "+t+"px; width: "+r+"px"}.bind(null,r);(b=document.createElement("iframe")).classList.add("web-office-iframe");var i={id:"office-iframe",src:e,scrolling:"no",frameborder:"0",allowfullscreen:"allowfullscreen",webkitallowfullscreen:"true",mozallowfullscreen:"true"};for(var o in r?(i.style="width: "+r.clientWidth+"px; height: "+r.clientHeight+"px;",t&&window.addEventListener("resize",a)):((r=document.createElement("div")).classList.add("web-office-default-container"),function(e){var n=document.createElement("style");document.head.appendChild(n);var t=n.sheet;t.insertRule(e,t.cssRules.length)}(".web-office-default-container {position: absolute; padding: 0;  margin: 0; width: 100%; height: 100%; left: 0; top: 0;}"),document.body.appendChild(r),i.style="position: fixed; top: 0; right: 0; bottom: 0; left: 0; width: 100%; height: 100%;"),i)b.setAttribute(o,i[o]);r.appendChild(b),b.destroy=function(){b.parentNode.removeChild(b),b=null,window.removeEventListener("resize",a),h&&(h.disconnect(),h=null)}}return b};var w=function(e){g().contentWindow.postMessage(JSON.stringify(e),f("origin"))};function y(e,n,t){return new Promise(function(r){var i=m(),o=function(e){if(!p(e)){var n=a.parse(e.data);n.eventName===t&&n.msgId===i&&(r(n.data),a.remove(o))}};a.add(o),w({data:e,msgId:i,eventName:n})})}var k=function(e){return y(e,"wps.jssdk.api","wps.api.reply")},j=function(e){return y(e,"api.basic","api.basic.reply")},I={idMap:{}};function O(e){return t(this,void 0,void 0,function(){var n,t,i,o,s,c,u,l,d,f;return r(this,function(r){switch(r.label){case 0:return p(e)?[2]:(n=a.parse(e.data),t=n.eventName,i=n.callbackId,o=n.data,i&&(s=I.idMap[i])?(c=s.split(":"),u=c[0],l=c[1],"api.callback"===t&&I[u]&&I[u][l]?[4,(f=I[u][l]).callback.apply(f,o.args)]:[3,2]):[3,2]);case 1:d=r.sent(),w({result:d,callbackId:i,eventName:"api.callback.reply"}),r.label=2;case 2:return[2]}})})}var x=function(e){return t(void 0,void 0,void 0,function(){function n(){return Object.keys(I.idMap).find(function(e){return I.idMap[e]===i+":"+t})}var t,i,o,s,c,u,l,d,f;return r(this,function(r){switch(r.label){case 0:return t=e.prop,i=e.parentObjId,[4,E([o=e.value])];case 1:return s=r.sent(),c=s[0],u=s[1],e.value=c[0],l=Object.keys(u)[0],d=I[i],null===o&&d&&d[t]&&((f=n())&&delete I.idMap[f],delete d[t],Object.keys(d).length||delete I[i],Object.keys(I.idMap).length||a.remove(O)),l&&(Object.keys(I.idMap).length||a.add(O),I[i]||(I[i]={}),I[i][t]={callbackId:l,callback:u[l]},(f=n())&&delete I.idMap[f],I.idMap[l]=i+":"+t),[2]}})})},_=function(e,i,o,s){return t(void 0,void 0,void 0,function(){var c,u,l,d,f,v,b,h;return r(this,function(g){switch(g.label){case 0:return c=m(),d=new Promise(function(e,n){u=e,l=n}),f={},i.args?[4,E(i.args)]:[3,2];case 1:v=g.sent(),b=v[0],h=v[1],i.args=b,f=h,g.label=2;case 2:return"api.setter"!==e?[3,4]:[4,x(i)];case 3:g.sent(),g.label=4;case 4:return function(e){var t=e[0],r=e[1];"function"==typeof(t=n({},t)).data&&(t.data=t.data());r(),w(t)}([{eventName:e,data:i,msgId:c},function(){var n=this,i=function(d){return t(n,void 0,void 0,function(){var n,t,v;return r(this,function(r){switch(r.label){case 0:return p(d)?[2]:"api.callback"===(n=a.parse(d.data)).eventName&&n.callbackId&&f[n.callbackId]?[4,f[n.callbackId].apply(f,n.data.args)]:[3,2];case 1:t=r.sent(),w({result:t,eventName:"api.callback.reply",callbackId:n.callbackId}),r.label=2;case 2:return n.eventName===e+".reply"&&n.msgId===c&&(n.error?((v=new Error("")).stack=n.error+"\n"+o,s&&s(),l(v)):u(n.result),a.remove(i)),[2]}})})};return a.add(i),d}]),[2,d]}})})};function E(e){return t(this,void 0,void 0,function(){var n,t,a,i,o,s,c,u,l,d,f;return r(this,function(r){switch(r.label){case 0:n={},t=[],a=e.slice(0),r.label=1;case 1:return a.length?(i=void 0,[4,a.shift()]):[3,13];case 2:return(o=r.sent())&&o.done?[4,o.done()]:[3,4];case 3:r.sent(),r.label=4;case 4:if(!function(e){if(!e)return!1;for(var n=e;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return Object.getPrototypeOf(e)===n}(i))return[3,11];for(c in i={},s=[],o)s.push(c);u=0,r.label=5;case 5:return u<s.length?(l=s[u],d=o[l],/^[A-Z]/.test(l)?d&&d.done?[4,d.done()]:[3,7]:[3,8]):[3,10];case 6:r.sent(),r.label=7;case 7:d&&d.objId?d={objId:d.objId}:"function"==typeof d&&(f=m(),n[f]=d,d={callbackId:f}),r.label=8;case 8:i[l]=d,r.label=9;case 9:return u++,[3,5];case 10:return[3,12];case 11:o&&o.objId?i={objId:o.objId}:"function"==typeof o&&void 0===o.objId?(f=m(),n[f]=o,i={callbackId:f}):i=o,r.label=12;case 12:return t.push(i),[3,1];case 13:return[2,[t,n]]}})})}var S=function(e,t){void 0===t&&(t=!0);var r=n({},e),a=r.headers,i=void 0===a?{}:a,o=r.subscriptions,s=void 0===o?{}:o,u=r.mode,l=void 0===u?c.nomal:u,d=r.commonOptions,f=i.backBtn,p=void 0===f?{}:f,v=i.shareBtn,b=void 0===v?{}:v,h=i.otherMenuBtn,m=void 0===h?{}:h,g=function(e,n){e.subscribe&&"function"==typeof e.subscribe&&(e.callback=n,s[n]=e.subscribe,t&&delete e.subscribe)};if(g(p,"wpsconfig_back_btn"),g(b,"wpsconfig_share_btn"),g(m,"wpsconfig_other_menu_btn"),m.items&&Array.isArray(m.items)){var w=[];m.items.forEach(function(e,n){switch(void 0===e&&(e={}),e.type){case"export_img":e.type=1,e.callback="export_img";break;case"export_pdf":e.type=1,e.callback="export_pdf";break;case"save_version":e.type=1,e.callback="save_version";break;case"about_wps":e.type=1,e.callback="about_wps";break;case"split_line":e.type=2;break;case"custom":e.type=3,g(e,"wpsconfig_other_menu_btn_"+n),w.push(e)}}),w.length&&(L||A)&&(m.items=w)}r.url=r.url||r.wpsUrl;var y=[];if((l===c.simple||d&&!1===d.isShowTopArea)&&y.push("simple","hidecmb"),r.debug&&y.push("debugger"),r.url&&y.length&&(r.url=r.url+(r.url.indexOf("?")>=0?"&":"?")+y.join("&")),d&&(d.isParentFullscreen||d.isBrowserViewFullscreen)&&(document.addEventListener("fullscreenchange",P),document.addEventListener("webkitfullscreenchange",P),document.addEventListener("mozfullscreenchange",P)),r.wordOptions&&(r.wpsOptions=r.wordOptions),r.excelOptions&&(r.etOptions=r.excelOptions),r.pptOptions&&(r.wppOptions=r.pptOptions),"object"==typeof s.print){var k="wpsconfig_print";"function"==typeof s.print.subscribe&&(s[k]=s.print.subscribe,r.print={callback:k},void 0!==s.print.custom&&(r.print.custom=s.print.custom)),delete s.print}"function"==typeof s.exportPdf&&(s[k="wpsconfig_export_pdf"]=s.exportPdf,r.exportPdf={callback:k},delete s.exportPdf);return r.commandBars&&N(r.commandBars,!1),n(n({},r),{subscriptions:s})},C=function(e){void 0===e&&(e="");var n="";if(!n&&e){var t=e.toLowerCase();-1!==t.indexOf("/office/s/")&&(n=o.spreadsheet),-1!==t.indexOf("/office/w/")&&(n=o.writer),-1!==t.indexOf("/office/p/")&&(n=o.presentation),-1!==t.indexOf("/office/f/")&&(n=o.pdf)}if(!n){var r=e.match(/[\?&]type=([a-z]+)/)||[];n=s[r[1]]||""}return n};function N(e,n){void 0===n&&(n=!0);var t=e.map(function(e){var n=e.attributes;if(!Array.isArray(n)){var t=[];for(var r in n)if(n.hasOwnProperty(r)){var a={name:r,value:n[r]};t.push(a)}e.attributes=t}return e});return n&&w({data:t,eventName:"setCommandBars"}),t}var T=window.navigator.userAgent.toLowerCase(),L=/Android|webOS|iPhone|iPod|BlackBerry|iPad/i.test(T),A=function(){try{return-1!==window._parent.location.search.indexOf("from=wxminiprogram")}catch(e){return!1}}();function P(){var e={status:u.requestFullscreen},n=document,t=n.fullscreenElement||n.webkitFullscreenElement||n.mozFullScreenElement;e.status=t?u.requestFullscreen:u.exitFullscreen,w({data:e,eventName:"fullscreenchange"})}var F=function(){I.idMap={}};function D(){console.group("JSSDK 事件机制调整说明"),console.warn("jssdk.on、jssdk.off 和 jssdk.Application.Sub 将在后续版本中被弃用，建议使用改进后的 ApiEvent"),console.warn("具体请参考：https://wwo.wps.cn/docs/front-end/basic-usage/events/intro/"),console.groupEnd()}var B=0,V=new Set;function R(e){return B+=1,!e&&function(e){V.forEach(function(n){return n(e)})}(B),B}function H(){var e=new Error("");return(e.stack||e.message||"").split("\n").slice(2).join("\n")}function M(e,i){var s,c=this,u=i.Events,l=i.Enum,d=i.Props,f=d[0],v=d[1],b={objId:B};switch(function e(t,r,a){var i=r.slice(0);var o=function(){var r=i.shift();!r.alias&&~z.indexOf(r.prop)&&i.push(n(n({},r),{alias:r.prop+"Async"})),Object.defineProperty(t,r.alias||r.prop,{get:function(){var i=this,o=1===r.cache,s=o&&this["__"+r.prop+"CacheValue"];if(!s){var c=H(),u=R(o),l=function(){for(var e,i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];void 0!==r.caller?function e(t,r,a){var i=r.slice(0);var o=function(){var r=i.shift();!r.alias&&~z.indexOf(r.prop)&&i.push(n(n({},r),{alias:r.prop+"Async"})),Object.defineProperty(t,r.alias||r.prop,{get:function(){var n=this,i=1===r.cache,o=i&&this["__"+r.prop+"CacheValue"];if(!o){var s=H(),c=R(i),u=function(){for(var n,i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];void 0!==r.caller?e(n={objId:R()},a[r.caller],a):n={};return q(u,n,"api.caller",{obj:u,args:i,parentObjId:t.objId,objId:n.objId,prop:r.prop},s),n};return u.objId=-1,void 0!==r.getter&&(u.objId=c,e(u,a[r.getter],a)),q(t,u,"api.getter",{parentObjId:t.objId,objId:u.objId,prop:r.prop},s,function(){delete n["__"+r.prop+"CacheValue"]}),i&&(this["__"+r.prop+"CacheValue"]=u),u}return o},set:function(e){var n=H();return q(t,{},"api.setter",{value:e,parentObjId:t.objId,objId:-1,prop:r.prop},n)}})};for(;i.length;)o()}(e={objId:R()},a[r.caller],a):e={};return q(l,e,"api.caller",{obj:l,args:i,parentObjId:t.objId,objId:e.objId,prop:r.prop},c),e};return l.objId=-1,void 0!==r.getter&&(l.objId=u,e(l,a[r.getter],a)),q(t,l,"api.getter",{parentObjId:t.objId,objId:l.objId,prop:r.prop},c,function(){delete i["__"+r.prop+"CacheValue"]}),o&&(this["__"+r.prop+"CacheValue"]=l),l}return s},set:function(e){var n=H();return q(t,{},"api.setter",{value:e,parentObjId:t.objId,objId:-1,prop:r.prop},n)}})};for(;i.length;)o()}(b,f,v),b.Events=u,b.Enum=l,e.Enum=b.Enum,e.Events=b.Events,e.Props=d,C(e.url)){case o.writer:e.WordApplication=e.WpsApplication=function(){return b};break;case o.spreadsheet:e.ExcelApplication=e.EtApplication=function(){return b};break;case o.presentation:e.PPTApplication=e.WppApplication=function(){return b};break;case o.pdf:e.PDFApplication=function(){return b}}e.Application=b,e.Free=function(e){return _("api.free",{objId:e},"")},e.Stack=b.Stack=(s=function(n){e&&e.Free(n)},function(){var e=[],n=function(n){e.push(n)};return V.add(n),{End:function(){s(e),V.delete(n)}}});var h={};a.add(function(e){return t(c,void 0,void 0,function(){var n,t,i,o,s;return r(this,function(r){switch(r.label){case 0:return p(e)?[2]:"api.event"===(n=a.parse(e.data)).eventName&&n.data?(t=n.data,i=t.eventName,o=t.data,(s=h[i])?[4,s(o)]:[3,2]):[3,2];case 1:r.sent(),r.label=2;case 2:return[2]}})})}),b.Sub={};var m=function(e){var n=u[e];Object.defineProperty(b.Sub,n,{set:function(e){D(),h[n]=e,w({eventName:"api.event.register",data:{eventName:n,register:!!e,objId:B+=1}})}})};for(var g in u)m(g)}var z=["ExportAsFixedFormat","GetOperatorsInfo","ImportDataIntoFields","ReplaceText","ReplaceBookmark","GetBookmarkText","GetComments"];function W(e,t,r){var a=t.slice(0);var i=function(){var t=a.shift();if(!t.alias&&~z.indexOf(t.prop)){a.push(n(n({},t),{alias:t.prop+"Async"}))}Object.defineProperty(e,t.alias||t.prop,{get:function(){var n=this;var a=t.cache===1;var i=a&&this["__"+t.prop+"CacheValue"];if(!i){var o=H();var s=R(a);var c=function(){for(var n=[],a=0,i;a<arguments.length;a++){n[a]=arguments[a]}if(t.caller!==undefined){i={objId:R()};W(i,r[t.caller],r)}else{i={}}return q(u,i,"api.caller",{obj:u,args:n,parentObjId:e.objId,objId:i.objId,prop:t.prop},o),i};var u=c;u.objId=-1;if(t.getter!==undefined){u.objId=s;W(u,r[t.getter],r)}q(e,u,"api.getter",{parentObjId:e.objId,objId:u.objId,prop:t.prop},o,function(){delete n["__"+t.prop+"CacheValue"]});if(a){this["__"+t.prop+"CacheValue"]=u}return u}return i},set:function(n){var r=H();return q(e,{},"api.setter",{value:n,parentObjId:e.objId,objId:-1,prop:t.prop},r)}})};while(a.length){i()}}function q(e,n,t,r,a,i){var o,s=(e.done?e.done():Promise.resolve()).then(function(){return o||(o=_(t,r,a,i)),o});n.done=function(){return s},n.then=function(e,t){return r.objId>=0?(n.then=null,n.catch=null,s.then(function(){e(n)}).catch(function(e){return t(e)})):s.then(e,t)},n.catch=function(e){return s.catch(e)},n.Destroy=function(){return _("api.free",{objId:n.objId},"")}}var K=[],G=null,J={fileOpen:"fileOpen",tabSwitch:"tabSwitch",fileSaved:"fileSaved",fileStatus:"fileStatus",fullscreenChange:"fullscreenChange",error:"error",stage:"stage"},U={getToken:"api.getToken",onToast:"event.toast",onHyperLinkOpen:"event.hyperLinkOpen",getClipboardData:"api.getClipboardData"};function Z(e,i,o,s,c,u,l){var d=this;void 0===o&&(o={});a.add(function(f){return t(d,void 0,void 0,function(){var t,d,v,b,h,m,g,y,k,j,I,O,x,_,E,S,C,N,T;return r(this,function(r){switch(r.label){case 0:return p(f)?[2]:(t=a.parse(f.data),d=t.eventName,v=void 0===d?"":d,b=t.data,h=void 0===b?null:b,m=t.url,g=void 0===m?null:m,-1!==["wps.jssdk.api"].indexOf(v)?[2]:"ready"!==v?[3,1]:(w({eventName:"setConfig",data:n(n({},o),{version:e.version})}),e.tokenData&&e.setToken(n(n({},e.tokenData),{hasRefreshTokenConfig:!!o.refreshToken})),c.apiReadySended&&w({eventName:"api.ready"}),K.forEach(function(n){return e.on(n.eventName,n.handle)}),e.iframeReady=!0,[3,15]));case 1:return"error"!==v?[3,2]:(i.emit(J.error,h),[3,15]);case 2:return"open.result"!==v?[3,3]:(void 0!==(null===(C=null==h?void 0:h.fileInfo)||void 0===C?void 0:C.officeVersion)&&(e.mainVersion=h.fileInfo.officeVersion,console.log("WebOfficeSDK Main Version: V"+e.mainVersion)),i.emit(J.fileOpen,h),[3,15]);case 3:return"api.scroll"!==v?[3,4]:(window.scrollTo(h.x,h.y),[3,15]);case 4:if(v!==U.getToken)return[3,9];y={token:!1},r.label=5;case 5:return r.trys.push([5,7,,8]),[4,c.refreshToken()];case 6:return y=r.sent(),[3,8];case 7:return k=r.sent(),console.error("refreshToken: "+(k||"fail to get")),[3,8];case 8:return w({eventName:U.getToken+".reply",data:y}),[3,15];case 9:if(v!==U.getClipboardData)return[3,14];j={text:"",html:""},r.label=10;case 10:return r.trys.push([10,12,,13]),[4,c.getClipboardData()];case 11:return j=r.sent(),[3,13];case 12:return I=r.sent(),console.error("getClipboardData: "+(I||"fail to get")),[3,13];case 13:return w({eventName:U.getClipboardData+".reply",data:j}),[3,15];case 14:v===U.onToast?c.onToast(h):v===U.onHyperLinkOpen?c.onHyperLinkOpen(h):"stage"===v?i.emit(J.stage,h):"event.callback"===v?(O=h.eventName,x=h.data,_=O,"fullScreenChange"===O&&(_=J.fullscreenChange),"file.saved"===O&&(_=J.fileStatus),((null===(N=o.commonOptions)||void 0===N?void 0:N.isBrowserViewFullscreen)||(null===(T=o.commonOptions)||void 0===T?void 0:T.isParentFullscreen))&&"fullscreenchange"===_&&(E=x.status,S=x.isDispatchEvent,o.commonOptions.isBrowserViewFullscreen?function(e,n,t,r){0===e?n.style="position: static; width: "+t.width+"; height: "+t.height:1===e&&(n.style="position: absolute; width: 100%; height: 100%"),r&&function(e){["fullscreen","fullscreenElement"].forEach(function(n){Object.defineProperty(document,n,{get:function(){return!!e.status},configurable:!0})});var n=new CustomEvent("fullscreenchange");document.dispatchEvent(n)}({status:e})}(E,u,l,S):o.commonOptions.isParentFullscreen&&function(e,n,t){var r=document.querySelector(t),a=r&&1===r.nodeType?r:n;if(0===e){var i=document,o=i.exitFullscreen||i.mozCancelFullScreen||i.msExitFullscreen||i.webkitCancelFullScreen||i.webkitExitFullscreen;o.call(document)}else if(1===e){var s=a.requestFullscreen||a.mozRequestFullScreen||a.msRequestFullscreen||a.webkitRequestFullscreen;s.call(a)}}(E,u,o.commonOptions.isParentFullscreen)),i.emit(_,x)):"api.ready"===v&&M(e,h),r.label=15;case 15:return"function"==typeof s[v]&&s[v](e,g||h),[2]}})})})}function Q(e){return new Promise(function(n){var t=function(r){p(r)||a.parse(r.data).eventName===e&&(n(),a.remove(t))};a.add(t)})}function X(e){void 0===e&&(e={}),G&&G.destroy();try{var n=S(e),o=n.subscriptions,s=void 0===o?{}:o,c=n.mount,u=void 0===c?null:c,l=n.url,f=n.refreshToken,p=n.onToast,v=n.onHyperLinkOpen,b=n.getClipboardData;d("origin",(l.match(/https*:\/\/[^\/]+/g)||[])[0]);var h=g(l,u),m=Q("ready"),y=Q("open.result"),I=Q("api.ready"),O=u?{width:u.clientWidth+"px",height:u.clientHeight+"px"}:{width:"100vw",height:"100vh"};delete n.mount,l&&delete n.url,delete n.subscriptions;var x=(E=E||Object.create(null),{on:function(e,n){(E[e]||(E[e]=[])).push(n)},off:function(e,n){E[e]&&E[e].splice(E[e].indexOf(n)>>>0,1)},emit:function(e,n){(E[e]||[]).slice().map(function(e){e(n)}),(E["*"]||[]).slice().map(function(t){t(e,n)})}}),_={apiReadySended:!1};return G={url:l,iframe:h,version:"1.1.15",iframeReady:!1,tokenData:null,commandBars:null,tabs:{getTabs:function(){return t(this,void 0,void 0,function(){return r(this,function(e){switch(e.label){case 0:return[4,m];case 1:return e.sent(),[2,j({api:"tab.getTabs"})]}})})},switchTab:function(e){return t(this,void 0,void 0,function(){return r(this,function(n){switch(n.label){case 0:return[4,m];case 1:return n.sent(),[2,j({api:"tab.switchTab",args:{tabKey:e}})]}})})}},setCooperUserColor:function(e){return t(this,void 0,void 0,function(){return r(this,function(n){switch(n.label){case 0:return[4,m];case 1:return n.sent(),[2,j({api:"setCooperUserColor",args:e})]}})})},setToken:function(e){return t(this,void 0,void 0,function(){return r(this,function(n){switch(n.label){case 0:return[4,m];case 1:return n.sent(),G.tokenData=e,w({eventName:"setToken",data:e}),[2]}})})},ready:function(){return t(this,void 0,void 0,function(){return r(this,function(e){switch(e.label){case 0:return _.apiReadySended?[3,2]:[4,y];case 1:e.sent(),_.apiReadySended=!0,w({eventName:"api.ready"}),e.label=2;case 2:return[4,I];case 3:return e.sent(),[2,new Promise(function(e){return setTimeout(function(){return e(null==G?void 0:G.Application)},0)})]}})})},destroy:function(){h.destroy(),a.empty(),G=null,K=[],V=new Set,B=0,document.removeEventListener("fullscreenchange",P),F()},save:function(){return t(this,void 0,void 0,function(){return r(this,function(e){switch(e.label){case 0:return[4,m];case 1:return e.sent(),[2,k({api:"save"})]}})})},setCommandBars:function(e){return t(this,void 0,void 0,function(){return r(this,function(n){switch(n.label){case 0:return[4,m];case 1:return n.sent(),N(e),[2]}})})},updateConfig:function(e){return void 0===e&&(e={}),t(this,void 0,void 0,function(){return r(this,function(n){switch(n.label){case 0:return[4,m];case 1:return n.sent(),e.commandBars?(console.warn("Deprecated: `updateConfig()` 方法即将废弃，请使用`setCommandBars()`代替`updateConfig()`更新`commandBars`配置。"),[4,N(e.commandBars)]):[3,3];case 2:n.sent(),n.label=3;case 3:return[2]}})})},executeCommandBar:function(e){return t(this,void 0,void 0,function(){return r(this,function(n){switch(n.label){case 0:return[4,m];case 1:return n.sent(),N([{cmbId:e,attributes:[{name:"click",value:!0}]}]),[2]}})})},on:function(e,n){return t(this,void 0,void 0,function(){var t,a;return r(this,function(r){switch(r.label){case 0:return D(),-1===(t=K.findIndex(function(n){return n.eventName===e}))?K.push({eventName:e,handle:n}):K[t]={eventName:e,handle:n},[4,m];case 1:return r.sent(),a=e,e===J.fileSaved&&console.warn("fileSaved事件监听即将弃用， 推荐使用fileStatus进行文件状态的监听"),e===J.fullscreenChange&&(a="fullscreenchange"),Y(a,"on"),x.on(e,n),[2]}})})},off:function(e,n){return t(this,void 0,void 0,function(){return r(this,function(t){switch(t.label){case 0:return D(),K=K.filter(function(n){n.eventName!==e&&n.handle}),[4,m];case 1:return t.sent(),Y(e,"off"),x.off(e,n),[2]}})})},ApiEvent:{AddApiEventListener:function(e,n){return t(this,void 0,void 0,function(){return r(this,function(t){switch(t.label){case 0:return[4,m];case 1:return t.sent(),w({eventName:"basic.event",data:{eventName:e,action:"on"}}),x.on(e,n),[2]}})})},RemoveApiEventListener:function(e,n){return t(this,void 0,void 0,function(){return r(this,function(t){switch(t.label){case 0:return[4,m];case 1:return t.sent(),w({eventName:"basic.event",data:{eventName:e,action:"off"}}),x.off(e,n),[2]}})})}}},function(e,n,t,r,a,o){n&&i(n)&&(a.refreshToken=n,e.refreshToken={eventName:U.getToken});o&&i(o)&&(a.getClipboardData=o,e.getClipboardData={eventName:U.getClipboardData});t&&i(t)&&(a.onToast=t,e.onToast={eventName:U.onToast});r&&i(r)&&(a.onHyperLinkOpen=r,e.onHyperLinkOpen={eventName:U.onHyperLinkOpen})}(n,f,p,v,_,b),Z(G,x,n,s,_,h,O),G}catch(e){console.error(e)}var E}function Y(e,n){var t=e;["error","fileOpen"].includes(t)||("fileSaved"===t&&(t="fileStatus"),w({eventName:"basic.event",data:{eventName:t,action:n}}))}console.log("WebOfficeSDK JS-SDK V1.1.15");var $=Object.freeze({__proto__:null,listener:Z,config:X});window.WPS=$;var ee=X,ne={config:ee};e.config=ee,e.default=ne,Object.defineProperty(e,"__esModule",{value:!0})});
