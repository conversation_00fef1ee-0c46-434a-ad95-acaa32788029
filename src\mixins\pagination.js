export const myPagination = {
  data() {
    return {
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "50", "100", "200", "500"],
        // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created() {
    window.addEventListener("keyup", this.enterSearch);
  },
  methods: {
    // 回车搜索
    enterSearch: function (e) {
      if (e.keyCode === 13) {
        this.fetchData();
      }
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      // 接口的数据条数rows，page页码
      if (this.queryForm.page) {
        this.queryForm.page = pageNum;
      }
      if (this.queryForm.rows) {
        this.queryForm.rows = pageSize;
      }
      if (this.queryForm.pageNum) {
        this.queryForm.pageNum = pageNum;
      }
      if (this.queryForm.pageSize) {
        this.queryForm.pageSize = pageSize;
      }
      if (this.queryPage) {
        this.queryPage.pageSize = pageSize;
      }
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      if (this.queryForm.page) {
        this.queryForm.page = pageNum;
      }
      if (this.queryForm.pageNum) {
        this.queryForm.pageNum = pageNum;
      }
      if (this.queryPage) {
        this.queryPage.pageNum = pageNum;
      }
      this.pagination.current = pageNum;
      this.fetchData();
    },
  },
};
