<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="800px"
    @close="close"
  >
    <el-form
      ref="dataSourceForm"
      :model="dataSourceForm"
      :rules="rules"
      label-width="150px"
    >
      <a-row>
        <a-col :span="layout === 'horizontal' ? 12 : 24">
          <el-form-item label="数据源编号" prop="dsCode">
            <el-input
              v-model="dataSourceForm.dsCode"
              :readonly="!isNewData"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </a-col>
        <a-col :span="layout === 'horizontal' ? 12 : 24">
          <el-form-item label="数据源名称" prop="dsName">
            <el-input
              v-model="dataSourceForm.dsName"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </a-col>
        <a-col :span="layout === 'horizontal' ? 12 : 24">
          <el-form-item label="数据源描述" prop="dsDesc">
            <el-input
              v-model="dataSourceForm.dsDesc"
              autocomplete="off"
              type="textarea"
            ></el-input>
          </el-form-item>
        </a-col>
      </a-row>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="数据库连接配置" name="first">
          <a-row>
            <a-col :span="layout === 'horizontal' ? 12 : 24">
              <el-form-item label="数据库连接用户名" prop="dsDbConfig.username">
                <el-input
                  v-model="dataSourceForm.dsDbConfig.username"
                  autocomplete="off"
                  @input="$forceUpdate()"
                ></el-input>
              </el-form-item>
            </a-col>

            <a-col :span="layout === 'horizontal' ? 12 : 24">
              <el-form-item label="数据库连接密码" prop="password">
                <el-input
                  v-model="dataSourceForm.dsDbConfig.password"
                  autocomplete="off"
                  show-password
                  @input="$forceUpdate()"
                ></el-input>
              </el-form-item>
            </a-col>
            <a-col :span="layout === 'horizontal' ? 12 : 24">
              <el-form-item label="数据库连接URL" prop="url">
                <el-input
                  v-model="dataSourceForm.dsDbConfig.url"
                  autocomplete="off"
                  type="textarea"
                  @input="$forceUpdate()"
                ></el-input>
                <a-row class="tip-row"
                  >参考模板：jdbc:mysql://{ip地址}:3306/{数据库名}?useUnicode=true&useJDBCCompliantTimezoneShift=true&serverTimezone=GMT%2B8</a-row
                >
              </el-form-item>
            </a-col>
            <a-col
              :span="layout === 'horizontal' ? 12 : 24"
              style="text-align: center;"
            >
              <el-button
                type="primary"
                :loading="connectTesting"
                @click="handleConnectTest"
                >测试连接</el-button
              >
            </a-col>
          </a-row>
        </el-tab-pane>
        <el-tab-pane label="文件获取配置" name="second"
          >暂不支持文件获取配置</el-tab-pane
        >
        <el-tab-pane label="接口调用配置" name="third"
          >暂不支持接口调用配置</el-tab-pane
        >
      </el-tabs>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.tip-row {
  line-height: 18px;
  color: #888;
  // font-size: 10px;
    @include add-size($font_size_16);
  padding: 5px 0;
}
</style>

<script>
import { doSave, doUpdate, doConnectTest } from "@/api/datasource";
import { mapGetters } from "vuex";
export default {
  name: "TableEdit",
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
  },
  data() {
    return {
      dataSourceForm: {
        dsCode: "",
        dsName: "",
        dsDesc: "",
        dsDbConfig: {
          url: "",
          username: "",
          password: "",
        },
        dsFileConfig: {},
        dsApiConfig: {},
      },
      title: "",
      dialogFormVisible: false,
      isNewData: true,
      activeName: "first",
      connectTesting: false,
      rules: {
        dsCode: [
          { required: true, message: "请输入数据源编号", trigger: "blur" },
        ],
        dsName: [
          { required: true, message: "请输入数据源名称", trigger: "blur" },
        ],
      },
    };
  },
  created() {},
  methods: {
    showEdit(row) {
      if (this.$refs["dataSourceForm"] !== undefined) {
        //清除表单验证用
        this.$refs["dataSourceForm"].clearValidate();
      }
      if (!row) {
        this.title = "新增数据源";
        this.isNewData = true;
      } else {
        this.title = "编辑数据源";
        if (row.dsDbConfig == undefined) {
          row.dsDbConfig = {};
        }
        this.dataSourceForm = row;
        this.isNewData = false;
      }
      this.dialogFormVisible = true;
    },
    close() {
      this.dataSourceForm = this.$options.data().dataSourceForm;
      this.dialogFormVisible = false;
    },
    save() {
      this.$refs["dataSourceForm"].validate((valid) => {
        if (valid) {
          if (this.isNewData) {
            doSave(this.dataSourceForm).then(() => {
              this.dialogFormVisible = false;
              this.$parent.fetchData();
            });
          } else {
            doUpdate(this.dataSourceForm).then(() => {
              this.dialogFormVisible = false;
              this.$parent.fetchData();
            });
          }
        }
      });
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    handleConnectTest() {
      if (this.dataSourceForm.dsDbConfig.username == "") {
        this.$message.error("请输入数据库连接用户名");
        return;
      }
      if (this.dataSourceForm.dsDbConfig.password == "") {
        this.$message.error("请输入数据库连接密码");
        return;
      }
      if (this.dataSourceForm.dsDbConfig.url == "") {
        this.$message.error("请输入数据库连接URL");
        return;
      }
      this.connectTesting = true;
      doConnectTest(this.dataSourceForm.dsDbConfig).then((resp) => {
        if (resp.data.connectResult == 1) {
          this.$message({
            message: "连接成功！",
            type: "success",
          });
        } else {
          this.$message({
            dangerouslyUseHTMLString: true,
            message: "连接失败！<br/>" + resp.data.connectFailReason,
            type: "error",
          });
        }
        this.connectTesting = false;
      });
    },
  },
};
</script>
