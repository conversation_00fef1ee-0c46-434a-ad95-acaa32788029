import request from "@/utils/requestTemp";
import { instance_3 } from "@/api/axiosRq";
import { instance_1 } from "@/api/axiosRq";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/liaisonStation/list",
    method: "get",
    params: data,
  });
}

export function isQZJWORKER() {
  return request({
    url: "/liaisonStation/isQZJWORKER",
    method: "get",
  });
}
export function listSub(data) {
  return request({
    url: "/liaisonStation/listSub",
    method: "get",
    params: data,
  });
}

export function getMySub(data) {
  return request({
    url: "/liaisonStation/getMySub",
    method: "get",
    params: data,
  });
}

export function setSubLiaison(data) {
  return request({
    url: "/liaisonStation/setSubLiaison",
    method: "post",
    data: data,
  });
}

// 获取联络站
export function listForAdmin(data) {
  return request({
    url: "/liaisonStation/listForAdmin",
    method: "get",
    params: data,
  });
}

export function doSave(data) {
  return request({
    url: "/liaisonStation/save",
    method: "post",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    }, // 请求头
    transformRequest: [
      (data) => {
        return JSON.stringify(data);
      },
    ],
    data: data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/liaisonStation/update",
    method: "post",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    }, // 请求头
    transformRequest: [
      (data) => {
        return JSON.stringify(data);
      },
    ],
    data: data,
  });
}

export function doDelete(data) {
  return request({
    url: "/liaisonStation/del",
    method: "get",
    params: data,
  });
}

export function saveOrUpdateWorker(data) {
  return request({
    url: "/liaisonStationWorker/saveOrUpdate",
    method: "post",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    }, // 请求头
    transformRequest: [
      (data) => {
        return JSON.stringify(data);
      },
    ],
    data: data,
  });
}

export function getById(data) {
  return request({
    url: "/liaisonStation/getById",
    method: "get",
    params: data,
  });
}
export function getTopNo(data) {
  return request({
    url: "/liaisonStation/getTopNo",
    method: "get",
    params: data,
  });
}

export function addOrUpdateCommitteeMember(data) {
  return request({
    url: "/liaisonStation/addOrUpdateCommitteeMember",
    method: "post",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    }, // 请求头
    transformRequest: [
      (data) => {
        return JSON.stringify(data);
      },
    ],
    data: data,
  });
}

export function getMembers(data) {
  return request({
    url: "/liaisonStation/listMembers",
    method: "get",
    params: data,
  });
}

export function countNumInfo(data) {
  return instance_3({
    url: "/liaisonStation/countNumInfo",
    method: "get",
    params: data,
  });
}

export function getSessionList(data) {
  return request({
    url: "/session/list",
    method: "get",
    params: data,
  });
}

export function getUserInfo(data) {
  return request({
    url: "/liaisonStation/getUserInfo",
    method: "get",
    params: data,
  });
}

export function listMemberByUserIds(data) {
  return request({
    url: "/liaisonStation/listMemberByUserIds",
    method: "get",
    params: data,
  });
}

export function directTrainList(value, data) {
  return request({
    url: "/directTrain/list",
    method: "post",
    params: value,
    data: data,
  });
}
export function userDirectTrainList(value, data) {
  return request({
    url: "/directTrain/listByUserId",
    method: "post",
    params: value,
    data: data,
  });
}

export function directTrainListBacklogBylist(value, data) {
  return request({
    url: "/directTrain/backlogBylist",
    method: "post",
    params: value,
    data: data,
  });
}

//联络站导入模版下载
export function liaisonStationDownload(params) {
  return instance_1({
    url: "/liaisonStation/download",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function qrcodeDownload(data) {
  return instance_1({
    url: "/liaisonStation/batchDownloadQrCode",
    method: "post",
    responseType: "blob",
    data: data,
  });
}

//联络站导入
export function liaisonStationUpload(data) {
  return instance_1({
    url: "/liaisonStation/uplaodLlzs",
    method: "post",
    headers: {
      "Content-type": "multipart/form-data",
    },
    data: data,
  });
}

//驻站代表导入模版下载
export function memberDownload(params) {
  return instance_1({
    url: "/liaisonStation/downloadMember",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//驻站代表导入
export function memberUpload(params, data) {
  return instance_1({
    url: "/liaisonStation/uplaodZapb",
    method: "post",
    headers: {
      "Content-type": "multipart/form-data",
    },
    data: data,
    params: params,
  });
}

//联络站回复
export function stationReplay(data) {
  return instance_1({
    url: "/onlineLiaisonMember/stationReplay",
    method: "post",

    data: data,
  });
}

// add by 2024-2-26
export function getUserInfoList(data) {
  return request({
    url: "/liaisonStation/getUserInfoList",
    method: "get",
    params: data,
  });
}

export function getDirectTrainBaseInfo() {
  return request({
    url: "/directTrain/getBaseInfo",
    method: "post",
  });
}

export function getDirectTrainLiaisonStations() {
  return request({
    url: "/directTrain/getMyLiaisonStations",
    method: "post",
  });
}

export function getStatistics(params, data) {
  return request({
    url: "/station/getStatistics",
    method: "post",
    params: params,
    data: data,
  });
}
export function statisticsAreaData(data) {
  return request({
    url: "/liaisonStation/statisticsAreaData",
    method: "get",
    params: data,
  });
}

export function setPoiAddress(data) {
  return request({
    url: "/liaisonStation/setPoiAddress",
    method: "post",
    data: data,
  });
}

export function liaisonStationListToMap(data) {
  return request({
    url: "/liaisonStation/liaisonStationListToMap",
    method: "get",
    params: data
  });
}

export function liaisonStationAdminList(params, data) {
  return request({
    url: "/liaisonStation/adminList",
    method: "post",
    params: params,
    data: data
  });
}