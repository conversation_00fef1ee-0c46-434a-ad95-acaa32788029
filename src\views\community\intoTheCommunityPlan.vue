<template>
  <div class="table-container">
    <z-table :list="list"
             :columns="columns"></z-table>
  </div>
</template>
<script>
export default {
  data () {
    return {
      TBloading: false,
      columns: [
        {
          title: "标题",
          ellipsis: true,
          dataIndex: "title",
        },
        {
          title: "内容",
          ellipsis: true,
          dataIndex: "content",
        },
        {
          title: "类型",
          ellipsis: true,
          dataIndex: "type",
        },
        {
          title: "时间",
          ellipsis: true,
          dataIndex: "time",
        },
        {
          title: "活动内容",
          ellipsis: true,
          dataIndex: "remark",
        },
      ],
      list: [
        {
          title: "“美丽乡村”助农惠农志愿服务进乡村",
          content:
            "开展庆祝中国农民丰收节系列活动；组织送科技、送文化、送教育、送健康等惠农服务；宣传推广当地农产品；组织看望慰问贫困户；推广文明节俭操办婚丧喜庆事宜、殡葬改革、公筷公勺、垃圾分类等政策知识。",
          type: "志愿服务",
          time: "2021-09-11",
          remark: "资金不足",
        },
        {
          title: "文明旅游志愿服务进社区",
          content:
            "开展株洲旅游景点宣传；组织文明旅游、文明礼仪宣传普及；推广“动力株洲文旅通”综合性消费年卡；开展红色研学之旅宣传推广",
          type: "志愿服务",
          time: "2021-11-11",
          remark: "电视台采访",
        },
        {
          title: "“诚信文化志愿服务进社区",
          content:
            "开展庆祝中国农民丰收节系列活动；组织送科技、送文化、送教育、送健康等惠农服务；宣传推广当地农产品；组织看望慰问贫困户；推广文明节俭操办婚丧喜庆事宜、殡葬改革、公筷公勺、垃圾分类等政策知识。",
          type: "志愿服务",
          time: "2021-10-11",
          remark: "村委书记到访",
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "进社区计划");
  },
};
</script>
