<template>
  <!-- 13楼代表列表接口 -->
  <a-modal :visible.sync="jointTableVisible"
           width="60%"
           title="查找"
           @cancel="close"
           @ok="jointTableOk">
    <div style="justify-content: center; display: flex; align-items: center;">
      <a-form layout="inline">
        <a-form-item label="代表姓名">
          <a-row>
            <a-input v-model="queryForm.name"
                     allow-clear
                     @keyup.enter="getJointList"
                     style="width: 400px;"></a-input>
          </a-row>
        </a-form-item>
      </a-form>
      <a-button type="primary"
                @click="getJointList">查询</a-button>
    </div>
    <a-table :columns="columns"
             :dataSource="dataSource"
             rowKey="userId"
             :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio',
           getCheckboxProps: (record) => ({
          props: {
            // disabled: true,
            disabled: record.disabledXUANX == '0',

          },
        })
      }"
            >
    </a-table>
  </a-modal>
</template>
<script>
import { getDeputyList } from "@/api/myJob/myProposal.js";
export default {
  props: {

    disableSelectedRowKeys: Array,
  },
  data () {
    return {
      jointTableVisible: false,
      searchValue: "",
      selectedRows: [],
      dataSource: [],
      multiple: true,
      columns: [
        {
          title: "代表姓名",
          dataIndex: "name",
          customRender: (text, record, index) =>{
          return text+"（"+record.streetArea+"）"
            }
        },

      ],
      selectedRowKeys: [],
      queryForm: {
        name: "",
        proposalId: "",
        telNum: "",
      },
    };
  },
  created () {
    this.getJointList();
  },
  methods: {
    // 保存
    jointTableOk () {
      this.$emit("emitJoinTable", this.selectedRows);
      this.jointTableVisible = false;
      this.selectedRows = [];
      this.selectedRowKeys = [];
    },
    // 选择
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
      this.$forceUpdate();
    },
    // 关闭
    close () {
      this.jointTableVisible = false;
    },
    // 获取联名代表
    getJointList () {
      getDeputyList(this.queryForm).then((res) => {
        this.dataSource = res.data.data;
        // 将不可选的选项进行禁用
        if (this.disableSelectedRowKeys && this.disableSelectedRowKeys.length > 0) {
          this.dataSource.map(item => {
            this.disableSelectedRowKeys.map(i => {
              if (i == item.orgCode) {
                item.disabledXUANX = "0";
              }
            });
          });
        }
      });
    },
  },
};
</script>
<style scoped>
div >>> .ant-modal-body {
  height: 600px;
  overflow-y: scroll;
}
</style>
