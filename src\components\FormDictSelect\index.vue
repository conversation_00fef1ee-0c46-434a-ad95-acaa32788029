<template>
  <FormSelect
    :value="input"
    :label="labelPrimary"
    :prop="propPrimary"
    :span="span"
    :xs="xs"
    :sm="sm"
    :md="md"
    :lg="lg"
    :xl="xl"
    :xxl="xxl"
    :options="options"
    :options-label="optionsLabel"
    :options-value="optionsValue"
    @change="onChange"
  />
</template>

<script>
import FormSelect from "@/components/FormSelect/index.vue";
import dict from "./dict";

/**
 * 字典选择器
 *
 * dict="字典项，请查看 dict.js[dict]"
 *
 * 1. 基础使用 <FormDictSelect v-model="queryForm.dbtId" dict="dbt" />
 */
export default {
  name: "FormDictSelect",
  components: { FormSelect },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    // 字典项，请查看 dict.js[dict]
    dict: {
      type: String,
      required: true,
    },
    // <a-input v-model="" />
    value: {
      type: [Number, String],
      required: true,
      default: "",
    },
    // <a-form-model-item prop="" />
    prop: {
      type: String,
      default: undefined,
    },
    // <a-form-model-item label="" />
    label: {
      type: String,
      default: undefined,
    },
    // <a-col span="" />
    span: {
      type: [Number, String],
      default: undefined,
    },
    xs: {
      type: [Number, String],
      default: undefined,
    },
    sm: {
      type: [Number, String],
      default: undefined,
    },
    md: {
      type: [Number, String],
      default: undefined,
    },
    lg: {
      type: [Number, String],
      default: undefined,
    },
    xl: {
      type: [Number, String],
      default: undefined,
    },
    xxl: {
      type: [Number, String],
      default: undefined,
    },
    // 级联标题
    cascadeLabel: {
      type: [String, Boolean],
      required: false,
      default: false,
    },
    // <a-input :allow-clear="false" />
    allowClear: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      options: [],
    };
  },
  computed: {
    input() {
      return typeof this.value === "string" && !this.value
        ? undefined
        : this.value;
    },
    labelPrimary() {
      return this.label || dict[this.dict]?.title;
    },
    propPrimary() {
      return this.prop || this.dict;
    },
    optionsLabel() {
      return dict[this.dict]?.label;
    },
    optionsValue() {
      return dict[this.dict]?.value;
    },
  },
  created() {
    this.load();
  },
  methods: {
    async load() {
      const load = dict[this.dict]?.load?.();
      if (!load) {
        this.options = [];
        return;
      }
      this.options = await Promise.resolve(load);
    },
    onChange(value) {
      this.$emit("update:value", value);
      this.$emit("change", value);
    },
  },
};
</script>

<style lang="scss" scoped></style>
