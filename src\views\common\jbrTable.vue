<template>
  <a-modal
    :visible.sync="jbrTableVisible"
    width="60%"
    title="查找"
    @cancel="close"
    @ok="close"
  >
    <div style="justify-content: center; display: flex; align-items: center;">
      <a-form layout="inline">
        <a-form-item label="用户名称">
          <a-row>
            <a-input
              v-model="queryForm.jbrName"
              allow-clear
              style="width: 150px;"
            ></a-input>
          </a-row>
        </a-form-item>
        <a-form-item label="手机号">
          <a-row>
            <a-input
              v-model="queryForm.jbrPhone"
              allow-clear
              style="width: 150px;"
            ></a-input>
          </a-row>
        </a-form-item>
      </a-form>
      <a-button type="primary" @click="getJbrList">查询</a-button>
    </div>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      rowKey="userId"
      :pagination="false"
    >
      <div slot="action" slot-scope="record">
        <a-button
          type="link"
          style="color: #d92b3e;"
          @click="jbrTableOk(record)"
          >选择</a-button
        >
      </div>
    </a-table>
  </a-modal>
</template>
<script>
import { getJbrList } from "@/api/myJob/myProposal.js";
export default {
  props: {},
  data() {
    return {
      jbrTableVisible: false,
      dataSource: [],
      columns: [
        {
          title: "经办人姓名",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "经办人手机号",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
        {
          title: "经办人职务",
          ellipsis: true,
          dataIndex: "jbrJob",
        },
        {
          title: "经办人部门",
          ellipsis: true,
          dataIndex: "jbrDept",
        },
        {
          title: "经办人办公电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          width: 90,
          align: "center",
          fixed: "right",
          title: "操作",
          scopedSlots: { customRender: "action" },
        },
      ],
      selectedRowKeys: [],
      queryForm: {
        orgCode: "",
        jbrName: "",
        jbrPhone: "",
      },
    };
  },
  props: {
    orgCode: {
      type: String,
      default: "",
    },
  },
  watch: {
    jbrTableVisible(val) {
      if (val) {
        this.queryForm.orgCode = this.orgCode;
        this.queryForm.jbrPhone = "";
        this.queryForm.jbrPhone = "";
        this.showDesc();
      }
    },
  },
  methods: {
    // 保存
    jbrTableOk(row) {
      this.$emit("emitJbrTable", row);
      this.jbrTableVisible = false;
    },
    // 关闭
    close() {
      this.jbrTableVisible = false;
      this.dataSource = [];
    },
    // 获取经办人数据
    getJbrList() {
      getJbrList(this.queryForm).then((res) => {
        this.dataSource = res.data.data;
      });
    },
    async showDesc() {
      this.getJbrList();
    },
  },
};
</script>
<style scoped>
div >>> .ant-modal-body {
  height: 600px;
  overflow-y: scroll;
}
</style>
