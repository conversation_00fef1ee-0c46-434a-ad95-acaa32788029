<template>
  <a-modal
    :title="computedTitle"
    :visible.sync="OrgTreeDialogVisible"
    width="860px"
    :dialog-style="{ top: '20px', height: '627.4px', }"
    destroy-on-close
    @cancel="close"
    dialogClass="modal_content"
  >
    <div class="modal_session">请选择代表届次：</div>
    <div>
      <a-col span="8" style="margin-bottom: 10px;">
        <a-form-model-item
          label="市代表"
          prop="title"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
        >
          <a-select class="select_style" style="width: 100%;" :default-value="citySessionInfo[0]" @change="handleChange">
            <a-select-option v-for="(item, index1) in citySessionInfo" :key="index1" :value="item">
              {{item}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col span="8" style="margin-bottom: 10px;">
        <a-form-model-item
          label="全国代表"
          prop="title"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
        >
          <a-select class="select_style" style="width: 100%;" :default-value="nationSessionList[0]" @change="handleChange">
            <a-select-option v-for="(item, index1) in nationSessionList" :key="index1" :value="item">
              {{item}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col span="8" style="margin-bottom: 10px;">
        <a-form-model-item
          label="省代表"
          prop="title"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
        >
          <a-select class="select_style" style="width: 100%;" :default-value="provinceSessionInfo[0]" @change="handleChange">
            <a-select-option v-for="(item, index1) in provinceSessionInfo" :key="index1" :value="item">
              {{item}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      
      <a-col span="8" style="margin-bottom: 10px;">
        <a-form-model-item
          label="区代表"
          prop="title"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
        >
          <a-select class="select_style" style="width: 50%;" :default-value="areaList[0]" @change="handleChange">
            <a-select-option v-for="(item, index1) in areaList" :key="index1" :value="item">
              {{item}}
            </a-select-option>
          </a-select>
          <a-select class="select_style" style="width: 50%;" :default-value="areaSessionInfo[0]" @change="handleChange">
            <a-select-option v-for="(item, index1) in areaSessionInfo" :key="index1" :value="item">
              {{item}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col span="8" style="margin-bottom: 10px;">
        <a-form-model-item
          label="镇街代表"
          prop="title"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
        >
          <a-select class="select_style" style="width: 50%;" :default-value="townList[0]" @change="handleChange">
            <a-select-option v-for="(item, index1) in townList" :key="index1" :value="item">
              {{item}}
            </a-select-option>
          </a-select>
          <a-select class="select_style" style="width: 50%;" :default-value="townSessionInfo[0]" @change="handleChange">
            <a-select-option v-for="(item, index1) in townSessionInfo" :key="index1" :value="item">
              {{item}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col span="8" style="padding: 4px 0px 0 30px;">
        <a-button type="primary" @click="onSessionSearch">搜索</a-button>
      </a-col>
      <a-modal
      title="导入结果"
      :visible="dialogVisible"
      @ok="userConfirm"
      @cancel="userClose"
      okText="下一步"
      :width="700"
      >
        <div v-if="isLoading" class="loading-indicator">  
          <!-- 这里添加你的 loading 指示器，如一个旋转的图标 -->  
          <a-icon type="loading" />  导入中...
        </div> 
        <template v-else>
          <a-tabs :default-active-key="importDefaultKey" v-model="importDefaultKey">
            <a-tab-pane key="1" tab="正常用户">
              <a-table
                :columns="columns"
                :data-source="normalUserList"
                :pagination="false"
                :scroll="{ y: 290 }"
              />
            </a-tab-pane>
            <a-tab-pane key="2" tab="重复用户">
              <a-table
                :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                :columns="columns"
                :data-source="repeatUserList"
                :pagination="false"
                :scroll="{ y: 290 }"
              />
            </a-tab-pane>
            <a-tab-pane key="3" tab="不存在用户">
              <a-table
                :columns="columns"
                :data-source="absentUserList"
                :pagination="false"
                :scroll="{ y: 290 }"
              />
            </a-tab-pane>
          </a-tabs>
        </template>
      </a-modal>

      <a-modal
          title="实际导入结果"
          :visible="realityVisible"
          @ok="userRealityConfirm" 
          @cancel="userRealityClose"
          :width="700"
          >
            <div class="all_Num">  
              <span>总条数: {{ realityCheckList.length }}</span>  
            </div>  
            <a-table
              :columns="columns"
              :data-source="realityCheckList"
              :pagination="false"
              :scroll="{ y: 290 }"
            />
        </a-modal>
    </div>
    <div class="table_container">
      <div class="table_left">
        <a-col span="23" offset="0">
          <a-input-search
            v-model="filterText"
            placeholder="输入关键字进行过滤"
            @search="onChange"
            style="width: 100%;"
          ></a-input-search>
        </a-col>
        <!-- <div v-loading="isLoading" element-loading-text="导入中..." element-loading-background="rgba(255, 255, 255, 0.7)"></div> -->
        <!-- :expanded-keys="expandedKeys" -->
        <a-spin :indicator="indicator" :spinning="spinningShow">
          <a-tree
            v-if="orgTreeData.length > 0"
            ref="orgTree"
            v-model="checkIdentyData"
            checkable
            :selectable="false"
            multiple
            :defaultExpandedKeys="[orgTreeData[0].id]"
            :replace-fields="replaceFields"
            :tree-data="orgTreeData"
            @check="onCheck"
            @expand="onExpand"
            @select="onSelect"
            style="overflow-x: hidden;width: 100%;"
          >

            <template slot="title" slot-scope="{ fullName, orgLevel }">
              <a-icon
                :type="orgLevel == '1' ? 'apartment' : 'file'"
                style="margin-right: 10px;"
              />
              <span
                v-if="searchValue && fullName.indexOf(searchValue) > -1"
                style="color: #f50;background-color: #f5f5f5;"
                class="fullName"
                >{{ fullName }}</span
              >
              <span v-else>{{ fullName }}</span>
            </template>
          </a-tree>
        </a-spin>
      </div>
      <div class="table_icon">
      </div>
      <div class="choose_list">
        <a-input-search placeholder="请输入" @search="onSearch" />
        <div class="list_table">
          <div class="list_all" v-for="(item, key) in changeCheckList" :key="key">
            <div class="list_right">
              <div>{{item.fullName}} {{item.jobName}}</div>
              <div>{{item.office}} {{item.address}}</div>
            </div>
          </div>
        </div>
      </div>
      
    </div>
    
    <span slot="footer" class="dialog-footer">
      <a-button @click="close">取 消</a-button>
      <a-button style="padding-left: 10px;" type="primary" @click="confirm"
        >确 定</a-button
      >
    </span>
  </a-modal>
</template>

<script>
import { findDbOU } from "@/api/registrationMage/tableIng.js";
import { log } from "@antv/g2plot/lib/utils";
import XLSX from 'xlsx';
import { Dialog, Button, Tabs, TabPane } from 'element-ui';
import {importRepresentation, downloadUserImportExcel} from "@/api/communityschedule";
export default {
  props: {
    orgTreeDialogTitle: {
      type: String,
      default: "选择单位",
    },
    jcDm: {
      type: String,
      default: "",
    },
    defaultSelect: {
      type: Array,
      default: [],
    },
    components: {
      'el-button': Button,
      'el-dialog': Dialog,
      'el-tabs': Tabs,
      'el-tab-pane': TabPane,
    },
  },
  data() {
    return {
      dialogVisible: false,
      activeTabName: 'normalUsers',
      normalUserList:[],
      repeatUserList:[],
      absentUserList:[],
      checkedUserList:[],
      confirmUserList:[],
      isLoading: false,
      timer:null,
      downloadExcelLoading: false,
      spinningShow: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      filterText: "", // 当前搜索文本
      lastFilterText: '', // 上一次搜索文本
      name: "",
      userName: [],
      expandedKeys: [],
      searchValue: "",
      allChildData: [],
      selectedKeys: [],
      autoExpandParent: true,
      checked: [],
      checkIdentyData: [],
      orgTreeDialogVisible: false,
      orgTreeData: [],
      OrgTreeDialogVisible: false,
      newOrgTreeDialogVisible: null,
      orgTreeDefaultKey: [],
      fullName: "",
      replaceFields: {
        title: "fullName",
        key: "id",
        children: "children",
      },
      isRadio: true,
      relType: {
        type: Number,
        default: 1,
      },
      columns: [
        {
          title: '序号',
          dataIndex: 'id',
        },
        {
          title: '姓名',
          dataIndex: 'userName',
        },
        {
          title: '岗位名称',
          dataIndex: 'orgName',
        },
      ],
      selectedRowKeys: [],
      fileList: [],
      realityVisible: false,
      realityCheckList: [],
      importDefaultKey: '1',
      nationSessionList: [
        '第十四届',
        '第十五届',
        '第十六届',
        '第十七届',
        '第十八届',
        '第十九届',
      ],
      provinceSessionInfo: [
        '第十四届',
        '第十五届',
        '第十六届',
        '第十七届',
        '第十八届',
        '第十九届',
      ],
      citySessionInfo: [
        '第十三届',
        '第十四届',
        '第十五届',
        '第十六届',
        '第十七届',
        '第十八届',
        '第十九届',
      ],
      areaSessionInfo: [
        '第十届',
        '第十一届',
        '第十二届',
        '第十三届',
        '第十四届',
        '第十五届',
      ],
      townSessionInfo: [
        '第五届',
        '第六届',
        '第七届',
        '第八届',
        '第九届',
      ],
      areaList: [
        '荔湾区','越秀区','海珠区','天河区','白云区','黄埔区','番禺区','花都区','南沙区','从化区','增城区',
      ],
      townList: [
        '洪桥街道','北京街','六榕','流花','光塔','人民'
      ],
      checkList: [],
      treeData: [], // 修改的机构树
      selectedNodes: [], // 选中的节点
      changeCheckList: [],
    };
  },
  computed: {  
    // 使用计算属性来返回prop的值  
    computedTitle() {  
      return this.orgTreeDialogTitle;  
    }  
  },
  watch: {
    newOrgTreeDialogVisible(newVal) {
      if (newVal) {
        this.initOrgTree();
      }
    },
  },
  mounted() {
  },
  methods: {
    handleChange(e) {

    },
    // 届次搜索
    onSessionSearch() {

    },
    onSelect(item, data) {
      //   console.log(data);
      // if (data.selected) {
      //   this.checked = [data.node.dataRef];
      //   this.checkIdentyData = [data.node.dataRef.orgId];
      // }
    },
    // 树状搜索
    onChange(e) {
      this.spinningShow = true;
      const value = e;
      this.searchValue = value;
      // 重置机构树
      this.orgTreeData = this.getSearchList(this.treeData, value)
      if (!this.lastFilterText || this.filterText === this.lastFilterText) {  
        // 在这里处理文本没有变化或上一次为空的情况  
        this.getChangeInfo(value)
      } else {  
        // 将之前选中的check节点全部取消
        this.checkIdentyData = [];
        if (this.changeCheckList.length > 0) {
          this.checkIdentyData = this.checkAllTree(this.orgTreeData, this.changeCheckList)
        }
        this.getChangeInfo(value)
      }  
      // 每次都更新 lastFilterText 为当前值，以便下次比较  
      this.lastFilterText = this.filterText;  
      setTimeout(() => {
         document.getElementsByClassName('fullName')[0].scrollIntoView()
      },1000);
    },
    getChangeInfo(value) {
      if (value == "") {
        this.spinningShow = false;
        // this.$message.info("请输入关键字");
        this.applySelectedState(this.treeData, this.changeCheckList)
        // 将之前所有选中的id加入到checkIdentyData中
        this.changeCheckList.forEach(item => {  
        if (!this.checkIdentyData.some(data => data === item.id)) {  
            this.checkIdentyData.push(item.id);  
          }  
        });
      } else {
        this.expandedKeys = [];
        this.backupsExpandedKeys = [];
        const candidateKeysList = this.getkeyList(value, this.treeData, []);
        candidateKeysList.forEach((item) => {
          const key = this.getParentKey(item, this.treeData);
          if (key && !this.backupsExpandedKeys.some((item) => item === key))
            this.backupsExpandedKeys.push(key);
        });
        const { length } = this.backupsExpandedKeys;
        for (let i = 0; i < length; i++) {
          this.getAllParentKey(this.backupsExpandedKeys[i], this.treeData);
        }
        this.expandedKeys = this.backupsExpandedKeys.slice();
        if (candidateKeysList.length == 0) {
          this.spinningShow = false;
          this.$message.info("没有相关数据");
          // 重置机构树
          this.orgTreeData = this.treeData;
          // 将之前所有选中的id加入到checkIdentyData中
        this.changeCheckList.forEach(item => {  
        if (!this.checkIdentyData.some(data => data === item.id)) {  
            this.checkIdentyData.push(item.id);  
          }  
        });
        }
        this.spinningShow = false;
      }
    },
    checkAllTree(checkTree, checkList) {  
      function checkNodeRecursive(node, checkList, result = []) {
        if (checkList.some(item => item.id === node.id)) {  
          result.push(node.id);  
        }  
       
        if (Array.isArray(node.children)) {  
          node.children.forEach(child => {  
            checkNodeRecursive(child, checkList, result); 
          });  
        }  
      }  
      
      let matchedIds = []; 
      checkTree.forEach(rootNode => {  
        checkNodeRecursive(rootNode, checkList, matchedIds);  
      });    
      
      // 返回匹配的ID数组  
      return matchedIds;  
    },
    applySelectedState(tree, checkList) {  
      for (let item of checkList) {  
        if (this.changeCheckList.findIndex(existingItem => existingItem.id === item.id) === -1) {  
          this.changeCheckList.push(item);  
        }  
      }
    },
    onSearch() {

    },
    // 获取筛选
    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.fullName.indexOf(value) > -1) {
          keyList.push(node.id);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    getSearchList(tree, value) {
      function filterTree(nodes) {  
        return nodes.reduce((acc, node) => {  
          const nodeMatches = node.fullName.includes(value);  
          const filteredChildren = node.children ? filterTree(node.children) : [];
          if (nodeMatches || filteredChildren.length > 0) {  
            acc.push({ ...node, children: filteredChildren.length > 0 ? filteredChildren : undefined });  
          }  
          return acc;  
        }, []);  
      }  
      
      return filterTree(tree);  
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey;
      let temp;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          temp = this.getParentKey(key, node.children);
          
          if (node.children.some((item) => item.id === key)) {
            parentKey = node.id;
          } else if (temp) {
            parentKey = temp;
          }
        }
      }
      return parentKey;
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey;
      if (key) {
        parentKey = this.getParentKey(key, tree);
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey);
          }
          this.getAllParentKey(parentKey, tree);
        }
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    close() {
      this.OrgTreeDialogVisible = false;
      this.newOrgTreeDialogVisible = false;
      this.searchValue = '';
      this.filterText = "";
      // 重置机构树
      this.orgTreeData = this.treeData;
    },
    initOrgTree() {
      findDbOU(this.jcDm).then((res) => {
        this.orgTreeData = res.data.data;
        // console.log(this.orgTreeData,'this.orgTreeData');
        
        this.treeData = JSON.parse(JSON.stringify(this.orgTreeData))
        this.OrgTreeDialogVisible = true;
        if (this.defaultSelect.length > 0) {
          let arrData = [];
          this.defaultSelect.map((item) => {
            if (item.children) {
              item.children.map((son) => {
                arrData.push(son.id);
              });
            }
            if (item.entryType != "1") {
              arrData.push(item.id);
            }
          });
          this.checkIdentyData = arrData;
          this.checked = this.defaultSelect;
          this.$forceUpdate();
        }
      });
    },
    onTreeShowList(data) {
      // console.log(data,'data');
      let list = data.filter(item => !item.children || (item.children && item.children.length === 0));
      return list.map((item => {
        return {
          ...item,
          jobName: '处长',
          office: '办公厅',
          address: '人事处',
        }
      }))
    },
    // 树状选择
    onCheck(checkedKeys, info) {  
      this.checked = [];
      if (info.checked) {
        // 选中
        info.checkedNodes.forEach((item) => {
          if (item.data.props.dataRef.children === null) {
            this.checked.push(item.data.props.dataRef);
            // this.selectedNodes.push(item.data.props.dataRef);
          }
        });
        this.checkList = this.onTreeShowList(this.checked);
        // 重新应用选中状态  
        this.applySelectedState(this.orgTreeData, this.checkList); 
      } else {
        // 取消选中
        // 判断是否点击取消全选的按钮
        const item = info.node.$vnode.data.props
        if (item.dataRef.children && item.dataRef.children.length > 0) {
          // 选中了大节点取消 把里面的id全部清空
          const childIds = new Set(item.dataRef.children.map(child => child.id));
          this.changeCheckList = this.changeCheckList.filter(itemInList => !childIds.has(itemInList.id));
        } else {
          let cancelId = info.node.$vnode.data.props.id
          // 只取消了一个 将对应的id去掉就行
          this.changeCheckList = this.changeCheckList.filter(existingItem => existingItem.id !== cancelId);
        }
      }
    }, 
    findData(userId, item, key) {
      item.forEach((son, index) => {
        if (son.userId == userId && userId) {
          this.checked[key].children.splice(index, 1);
        } else {
          if (son.children) {
            this.findData(userId, son.children, key);
          }
        }
      });
    },
    // onExpand(expandedKeys) {
    //   this.expandedKeys = expandedKeys;
    // },
    confirm() {
      this.OrgTreeDialogVisible = false;
      this.newOrgTreeDialogVisible = false;
      // console.log('最终传给父组件的值:', this.changeCheckList);
      
      this.$emit("confirm", this.changeCheckList);
      // this.checkIdentyData = [];
      // this.checkIdentyData = null;
      // this.checkList = [];
      this.filterText = "";
      this.searchValue = '';
    },
    // 上传文件限制 直接return
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xlsx|xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xlsx或.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },
    //导入代表姓名
    importRepresentationList({ file, fileList}) {
      // console.log("----导入代表用户----")
      var data = new FormData();
      data.append("file",file);
      importRepresentation(data).then((res) => {
        this.normalUserList=[];
        this.repeatUserList = [];
        this.absentUserList = [];
        let normalIndex = 1;
        let repeatIndex = 1;
        let absentIndex = 1;
        res.data.data.forEach((val, index) => {
          if(val.userType === 0 ){
            // 正常用户
            this.normalUserList.push({
              id: normalIndex++,
              userName: val.userName, //代表姓名
              orgName: val.orgName, //代表岗位名称
              userId: val.userId, //用户Id
              parentId: val.parentId //机构父Id
            });
          } else if (val.userType === 1) {
            // 重复用户
            this.repeatUserList.push({
              id: repeatIndex++,
              userName: val.userName, //代表姓名
              orgName: val.orgName, //代表岗位名称
              userId: val.userId, //用户Id
              parentId: val.parentId //机构父Id
            });
          } else if (val.userType === 2) {
            // 不存在用户
            this.absentUserList.push({
              id: absentIndex++,
              userName: val.userName, //代表姓名
              orgName: val.orgName, //代表岗位名称
              userId: val.userId, //用户Id
              parentId: val.parentId //机构父Id
            });
          }
        });
        // console.log(this.normalUserList, "----正常用户----",JSON.stringify(this.normalUserList));
        // console.log("----重复用户----",JSON.stringify(this.repeatUserList));
        // console.log("----不存在用户----",JSON.stringify(this.absentUserList));
        if (res.data.code != "0000") {
          return this.$message.error(res.data.msg);
        }
        if (res.data.code == "0000") {
          // console.log("---接口调用返回成功----")
          //判断如果返回列表长度大于0
          if(res.data.data.length > 0 ){
            this.$alert('导入成功','提示',{
              confirmButtonText:'确定',
              callback: action => {
                this.dialogVisible = true;
                this.searchValue = '';
                this.filterText = "";
             }
            });
          } else {
            this.$message.error("没有解析到数据，请检查该Excel文件是否存在数据");
          }
        }
      })
    },
    // 下载
    downloadExcel() {
      this.downloadExcelLoading = true;
      downloadUserImportExcel(this.orgTree).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `批量用户导入模板.xlsx`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadExcelLoading = false;
        }, 2500);
      });
    },
    //选择重复代表用户
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.checkedUserList = selectedRows
    },
    // handleSelectionChange(selection) {
    //   // 当勾选项变化时会触发
    //   this.checkedUserList = selection;
    //   console.log('选中的用户：', JSON.stringify(selection));
    //   return this.checkedUserList;
    // },
    indexMethod(index) {
      return index + 1;
    },
    async userConfirm() {
      this.realityCheckList = [];
      this.confirmUserList = []; 
      this.checkedUserList = [
        ...this.normalUserList,
        ...this.checkedUserList,
      ]
      if (this.checkedUserList.length === 0) {
        this.dialogVisible = false;
      } else {
        this.isLoading = true;
        this.checked = [];
        const dbResults = await findDbOU(this.jcDm).then(res => res.data.data);
        this.checkedUserList.forEach((userVal, index) => {  
          this.findAndPushUserIds(dbResults, userVal, this.confirmUserList, this.realityCheckList);  
        });
        this.realityCheckList = this.realityCheckList.map((item, index)=> {
          return {
            id: index + 1,
            ...item,
          }
        })
        this.isLoading = false;
        this.dialogVisible = false;
        this.importDefaultKey = '1';
        this.realityVisible = true;
      }
    },
    async userRealityConfirm() {
      this.$nextTick(() => {
        this.checkIdentyData = this.confirmUserList;
        this.normalUserList = [];
        this.repeatUserList = [];
        this.checkedUserList = [];
        this.selectedRowKeys = [];
        this.absentUserList = [];
        this.realityCheckList = [];
        this.importDefaultKey = '1';
        this.realityVisible = false;
      })
    },
    // 取消功能
    userClose(){
      this.normalUserList = [];
      this.repeatUserList = [];
      this.checkedUserList = [];
      this.absentUserList = [];
      this.selectedRowKeys = [];
      this.realityCheckList = [];
      this.importDefaultKey = '1';
      this.dialogVisible = false;
    },
    userRealityClose() {
      // console.log("-------取消功能--------")
      this.normalUserList = [];
      this.repeatUserList = [];
      this.checkedUserList = [];
      this.absentUserList = [];
      this.selectedRowKeys = [];
      this.importDefaultKey = '1';
      this.realityVisible = false
    },
    // 封装成方法  
    findAndPushUserIds(data, userVal, confirmUserList, realityCheckList) {  
      data.forEach((val) => {  
        this.traverseChildren(val.children, userVal, confirmUserList, realityCheckList);  
      });  
    },
    // 递归遍历 children 的辅助函数  
    traverseChildren(children, userVal, confirmUserList, realityCheckList) {  
      if (!Array.isArray(children)) {  
        return; // 如果 children 不是数组，直接返回  
      } 
      children.forEach((cval) => {  
        // 递归调用，检查下一层，但不进行匹配判断  
        if (cval.children) {  
          this.traverseChildren(cval.children, userVal, confirmUserList, realityCheckList);  
        }  
        // 只有在没有下一层 children 时，才进行匹配判断  
        if (!cval.children) {  
          if (userVal.userId === cval.userId && userVal.parentId === cval.orgId) {  
            if (!confirmUserList.some(item => item === cval.id)) {
              confirmUserList.push(cval.id);  
              realityCheckList.push({
                userName: userVal.userName,
                orgName: userVal.orgName,
              }) 
              this.checked.push(cval);  
            }
          }  
        }  
      });  
    },
  },
};
</script>
<style lang="scss" scoped>
.modal_session {
  margin-bottom: 5px;
}
::v-deep .ant-form-item {
  display: block!important;
}
::v-deep .ant-form-item-label {
  text-align: right!important;
}
::v-deep .ant-spin-nested-loading {
  // margin-top: 135px;
}
::v-deep .modal_content .ant-modal-content {
     height: 627.4px!important;
}
.select_style {
  width: 18%;
}
.table_container {
  display: flex;
  width: 100%;
  height: 390px;
  padding-bottom: 10px;

  .table_left {
    // max-width: 400px;
    flex: 45%;
    // flex: 4.5;
    border: 1px solid #e8e8e8;
    // height: 500px;
    padding: 10px;
    padding-right: 0;
  }
  .table_icon {
    flex: 1%;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 30px;
    justify-content: space-evenly;
    padding: 0 10px;
  }
  ::v-deep .ant-spin-nested-loading,
  .choose_list {
    padding: 10px;
    flex: 45%;
    // flex: 5;
  }
  ::v-deep .ant-spin-nested-loading {
    width: 100%;
    height: 93%;
    // height: 325px;
    overflow: auto;
  }
  // ::v-deep .ant-tree li .ant-tree-node-content-wrapper {
  //   max-width: 200px;
  // }
}
.choose_list {
    border: 1px solid #e8e8e8;
  // padding: 0 20px;
  .list_table {
    width: 100%;
    // height: 325px;
    height: 93%;
    overflow: auto;
    // border: 1px solid #e8e8e8;
    .list_all {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e8e8e8;
      padding: 5px 10px;
      .list_left {
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 50%;
        font-size: 12px;
        background-color: #d8293a;
        color: #fff;
        margin-right: 15px;
      }
      .list_right {
        color: #000;
      }
    }
  }
}
::v-deep .ant-spin-nested-loading {
  padding: 0!important;
}
.all_Num {
  font-size: 16px;
  font-weight: 700;
  /* color: #d92b3e; */
  text-align: left;
}
.loading-indicator {
  text-align: center;
}
::v-deep .ant-spin-container {
  height: 100%;
  max-width: 380px;
  overflow: auto;
}
::v-deep .ant-tree {
  // max-height: 500px;
  width: 100%;
  overflow: auto;
}
::v-deep .el-button {
  /* 按钮文字居中 */
  position: relative;
  span {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
::v-deep .ant-modal-body {
  padding-top: 5px;
}
/* 表格斑马样式 */  
::v-deep .ant-table-tbody tr:nth-child(2n) {  
  background-color: #fafafa;  
}
::v-deep .ant-modal-footer {
  position: absolute;
  width: 100%;
  bottom: 0;
  text-align: right;
}
</style>
