import { instance_yajy } from "@/api/axiosRq";
// 获取工作评价列表
// queryForm 请求搜索数据
export function queryListPage (queryForm) {
  return instance_yajy({
    url: `/api/v1/appraise/form/queryListPage`,
    method: "post",
    data: queryForm,
  });
}
// 生成短信 获取接收人列表
export function queryUserList (form) {
  return instance_yajy({
    url: `/api/v1/appraise/operInfo/queryList`,
    method: "post",
    data: form,
  });
}
// 生成短信 提交生成短信
export function sendScoreMsg (form) {
  return instance_yajy({
    url: `/api/v1/appraise/operInfo/sendScoreMsg`,
    method: "post",
    // data: form,
    params: form,
  });
}
// 生成短信 导出附件
export function downloadZip (form) {
  return instance_yajy({
    url: `/api/v1/appraise/form/downloadZip`,
    method: "post",
    params: form,
  });
}

//导出评分附件信息
export function downloadZipNew(form) {
  return instance_yajy({
    url: `/api/v1/appraise/form/downloadZipNew`,
    method: "post",
    responseType: "blob",
    params: { meeting: form.meeting },
    data: form.id,
  });
}

export function ifWorkFile(form) {
  return instance_yajy({
    url: `/api/v1/appraise/form/ifWorkFile`,
    method: "post",
    params: { meeting: form.meeting },
    data: form.id,
  });
}

// 分析整改填报列表
export function getRectifyList (form) {
  return instance_yajy({
    url: `/api/v1/appraise/form/getRectifyList`,
    method: "post",
    params: form,
  });
}
// 工作评分信息
export function showForm (id) {
  let formData = new FormData();
  formData.append("formId", id);
  return instance_yajy({
    url: `/api/v1/appraise/form/showForm`,
    method: "post",
    data: formData,
  });
}
// 新增或更新扣分反馈信息(分析整改填报)
export function saveOrUpdate (formData) {
  return instance_yajy({
    url: `/api/v1/appraise/deductFeedback/saveOrUpdate`,
    method: "post",
    data: formData,
  });
}
// 新增或更新扣分反馈信息(分析整改填报)
export function getDeductFeedback (form) {
  let formData = new FormData();
  for (const key in form) {
    formData.append(key, form[key]);
  }
  return instance_yajy({
    url: `/api/v1/appraise/deductFeedback/getDeductFeedback`,
    method: "post",
    data: formData,
  });
}
// 工作评价 列表导出pdf
export function exportScorePdf (form) {
  let formData = new FormData();
  for (const key in form) {
    formData.append(key, form[key]);
  }
  return instance_yajy({
    url: `/api/v1/appraise/form/exportScorePdf`,
    method: "post",
    data: formData,
    responseType: "blob",
  });
}
// 工作评价 工作评价表 明细列表
export function systemListPage (form) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/systemListPage`,
    method: "post",
    data: form,
  });
}
// 工作评价 工作评价表 点击人工审核 获取列表
export function detailQueryListPage (form) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/queryListPage`,
    method: "post",
    data: form,
  });
}
// 工作评价 工作评价表 点击人工审核列表界面 考核
export function detailSaveOrUpdate (form) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/saveOrUpdate`,
    method: "post",
    data: form,
  });
}
// 工作评价 工作评价表 点击人工审核列表界面 删除
export function detailDelete (form) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/delete`,
    method: "post",
    params: form,
  });
}
// 工作评价 工作评价表 点击人工审核列表界面 考核获取数据
export function formDetailForm (form) {
  let formData = new FormData();
  for (const key in form) {
    formData.append(key, form[key]);
  }
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/form`,
    method: "post",
    data: formData,
  });
}
// 工作评价 重新刷新各个承办单位的分数
export function formInitScore (form) {
  let formData = new FormData();
  for (const key in form) {
    formData.append(key, form[key]);
  }
  return instance_yajy({
    url: `/api/v1/appraise/form/initScore`,
    method: "post",
    data: formData,
  });
}

// 三级联系人编辑界面
export function toEditSJLXR (form) {
  let formData = new FormData();
  for (const key in form) {
    formData.append(key, form[key]);
  }
  return instance_yajy({
    url: `/api/v1/system/org/yajy/toEditSJLXR`,
    method: "post",
    data: formData,
  });
}
// 三级联系人编辑界面 保存
export function doSaveSJLXR (form) {
  return instance_yajy({
    url: `/api/v1/system/org/yajy/doSaveSJLXR`,
    method: "post",
    data: form,
  });
}
// 工作评价 提交申述 提交
export function submitAppeal (form) {
  return instance_yajy({
    url: `/api/v1/appraise/form/submitAppeal`,
    method: "post",
    data: form,
  });
}
//
// 工作评价 生成短信 中获取最新数据
export function updateOperInfo (form) {
  return instance_yajy({
    url: `/api/v1/appraise/operInfo/updateOperInfo`,
    method: "post",
    params: form,
  });
}
// 工作评价 工作评价表 获取自定义下拉选项
export function queryAttrOption (smallItemId) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/queryAttrOption?smallItemId=` + smallItemId,
    method: "get",
  });
}
