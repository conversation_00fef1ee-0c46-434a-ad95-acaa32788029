<template>
  <div>
    <a-col :span="spanNum" :class="{ 'overLength': title.length > 5 && spanNum <= 8 }">
      <a-form-item :label="title"
                   :label-col="{ span: 6 }"
                   :wrapper-col="{ span: 18 }">
        <div class="searchStyle">
          <a-input v-model="content"
                   allow-clear
                   :disabled="disabled"
                   :placeholder="'请选择'+ title"
                   enter-button
                   @change="search">
          </a-input>
          <a-button type="primary"
                    icon="search"
                    @click="onInquire">
          </a-button>
        </div>
      </a-form-item>
    </a-col>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: () => "",
      required: true,
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
    value: {
      type: [String, Number],
      default: '',
    },
  },
  watch: {   
    value: {  
      handler(val) {
        this.content = val == '' ? undefined : val;
      },
      deep: true,
      immediate: true,
    }, 
  },
  data() {
    return {
      content: "",
    };
  },
  methods: {
    search() {
      this.$emit("update:value", this.content);
    },
    onInquire() {
      this.$emit('onInquire', true)
    }
  },
  // watch: {
  //   newData(newVal) {  
  //     this.content = newVal;  
  //   }  
  // }
};
</script>

<style lang="scss" scoped>
// ::v-deep .ant-form-item {
//   display: flex;
//   align-items: center;
// }
// ::v-deep .ant-form-item-children {
//   display: flex;
// }
// ::v-deep .ant-btn {
//   width: 25%;
// }
</style>
