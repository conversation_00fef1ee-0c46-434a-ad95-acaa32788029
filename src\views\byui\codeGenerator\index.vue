<template>
  <div class="code-generator-container">
    <a-row :gutter="15">
      <a-col :xs="24" :sm="24" :md="4" :lg="6" :xl="6">
        <TableEditor @change="setTableData" />
      </a-col>
      <a-col :xs="24" :sm="24" :md="20" :lg="18" :xl="18">
        <TableExhibition :table-data="tableData" />
      </a-col>
    </a-row>
  </div>
</template>

<script>
import TableEditor from "./components/TableEditor";
import TableExhibition from "./components/TableExhibition";

export default {
  name: "Index",
  components: {
    TableEditor,
    TableExhibition,
  },
  data() {
    return {
      tableData: {},
      getTableAPI: "",
    };
  },
  methods: {
    setTableData(val) {
      this.tableData = JSON.parse(val);
    },
  },
};
</script>
