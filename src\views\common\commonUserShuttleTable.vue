<template>
  <!-- 13楼 代表数据列表穿梭框 -->
  <a-modal destroyOnClose
           :visible.sync="jointTableVisible"
           width="60%"
           title="查找"
           @cancel="close"
           @ok="unitTableOk"
           :zIndex="99">
    <div style="justify-content: center; display: flex; align-items: center;">
      <a-form layout="inline">
        <a-form-item label="代表姓名">
          <a-row>
            <a-input @keyup.enter.native="search"
                     v-model="queryForm.name"
                     allow-clear
                     style="width: 400px;"></a-input>
          </a-row>
        </a-form-item>
      </a-form>
      <a-space>
        <a-button type="primary"
                  @click="search">查询</a-button>
        <a-button type="primary"
                  @click="
            () => {
              selectedRowKeys = [];
              rightData = [];
              rightLength = 0;
            }
          ">清空已选</a-button>
      </a-space>
    </div>
    <a-row>提示：用户名称用英文逗号分割查询（例如：张三,李四）</a-row>
    <a-row>
      <a-col :span="12">
        <a-spin :spinning="leftLoading">
          <a-table bordered
                   :columns="columns"
                   :dataSource="dataSource"
                   rowKey="userId"
                   :customRow="clickRow"
                   :pagination="pagination"
                   :row-selection="{
              onSelectAll: onSelectAll,
              selectedRowKeys: selectedRowKeys,
              onSelect: onSelect,
              type: multiple ? 'checkbox' : 'radio',
                getCheckboxProps: (record) => ({
          props: {
            // disabled: true,
            disabled: record.disabledXUANX == '0',//禁止选择的id

          },
        })
            }"
                   :scroll="{ x: '50%', y: 345 }">
          </a-table>
          <!--    :pagination="pagination" -->
        </a-spin>
      </a-col>
      <a-col :span="12">
        <a-spin :spinning="rightLoading">
          <a-table bordered
                   :dataSource="rightData"
                   rowKey="userId"
                   :columns="rightColumns"
                   :pagination="false"
                   :scroll="{ x: '50%', y: 400 }">
            <div slot="customTitle">
              <span v-show="multiple == true"
                    style="color:#d92b3e ">已选{{ rightLength }}条 (最多只能选择30位联名代表)</span>
              <span v-show="multiple == false">代表姓名</span>
            </div>
            <div slot="action"
                 slot-scope="text, record">
              <span style="color: #d92b3e; cursor: pointer;"
                    @click="delRightData(record)">删除</span>
            </div>
          </a-table>
        </a-spin>
      </a-col>
    </a-row>
  </a-modal>
</template>
<script>
import { getDeputyList, getDeputyPage } from "@/api/myJob/myProposal.js";
export default {
  props: {
    defaultSelectKey: { type: Array, default: () => [] },
    excludeOperIds: String,

    disableSelectedRowKeys: { type: Array, default: () => [] },//禁止选择的id
  },
  data () {
    return {
      isShowCustomTitle: true,
      jointTableVisible: false,
      searchValue: "",
      dataSource: [],
      columns: [
        {
          title: "代表姓名",
          dataIndex: "name",
          customRender: (text, record, index) => {
            let streetArea = '(' + record.streetArea + ')'
            return (streetArea ? record.name + streetArea : record.name) || "/";
          },
        },
      ],
      rightColumns: [
        {
          dataIndex: "name",
          slots: { title: "customTitle" },
          customRender: (text, record, index) => {
            let streetArea = '(' + record.streetArea + ')'
            return (streetArea ? record.name + streetArea : record.name) || "/";
          },
        },
        {
          title: "操作",
          width: 100,
          align: "center",
          scopedSlots: { customRender: "action" },
        },
      ],
      queryForm: {
        pageNo: 1,
        rows: 30,
        name: "",
        proposalId: "",
        telNum: "",
        excludeOperIds: "",
      },
      multiple: true, //多选
      rightData: [],
      selectedRowKeys: [],
      rightLength: 0, //右边数据长度
      rightLoading: false,
      leftLoading: false,
      pagination: {
        pageNo: 1,
        pageSize: 30, // 默认每页显示数量
        showSizeChanger: false, // 显示可改变每页数量
        showQuickJumper: false, // 显示跳转功能
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        size: "middle",
        isNOADD: true,//是否搜索右边接口
      },
    };
  },
  watch: {
    jointTableVisible (newVal) {
      if (newVal) {
        if (this.excludeOperIds) {
          this.queryForm.excludeOperIds = this.excludeOperIds;
        }
        // console.log(this.isNOADD, this.selectedRowKeys, 'disableSelectedRowKeys', this.rightData)
        this.getDeputyListData();
        setTimeout(() => {
          // 回显右边数据
          if (this.isNOADD || this.selectedRowKeys.length > 0) { this.getDefaultDeputyListData(); }
          else { this.rightLength = this.rightData.length; }
        }, 500)
      } else {
        this.disableSelectedRowKeys = [];
        this.rightData = [];

      }
    },
  },
  created () { },
  methods: {
    // 点击行
    clickRow (record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            if (!this.multiple) {
              this.rightData = [];
            }
            // 阻止禁止选择的添加到右边
            if (record.disabledXUANX != '0') {
              this.rightData.push(record);
            }

            // 存在即取消选中，不存在即选中
            if (this.selectedRowKeys.includes(record.userId)) {
              this.onSelect(record, false, this.rightData);
            } else {
              this.onSelect(record, true, this.rightData);
            }
          },
        },
      };
    },
    // 右边table 删除
    delRightData (row) {
      this.rightData = this.rightData.filter(
        (item) => item.userId != row.userId
      );
      this.selectedRowKeys = this.rightData.map((item) => item.userId);
      this.rightLength = this.rightData.length;
    },
    // 保存
    unitTableOk () {
      if (this.selectedRowKeys.length > 30) {
        return this.$message.error("最多只能选择30位");
      }
      this.$emit("emitJoinTable", this.rightData);
      this.jointTableVisible = false;
      // Object.assign(this.$data, this.$options.data());
      this.rightData = [];
    },
    // 全选
    onSelectAll (selected, selectedRows, changeRows) {
      if (selected) {
        // 如果有搜索值
        if (this.queryForm.name) {
          // 去重
          let newData = [...this.rightData, ...selectedRows];
          newData = newData.reduce(function (prev, cur) {
            let has = false;
            prev.map((v) => {
              if (v.userId == cur.userId) {
                has = true;
              }
            });
            if (!has) {
              prev.push(cur);
            }
            return prev;
          }, []);
          this.rightData = newData;
        } else {
          this.rightData = selectedRows;
        }
        this.selectedRowKeys = this.rightData.map((item) => item.userId);
        this.rightLength = this.rightData.length;
      } else {
        this.rightData = [];
        this.selectedRowKeys = [];
        this.rightLength = 0;
      }
      this.rightData.reverse();
    },
    // 选择
    onSelect (record, selected, selectedRows, nativeEvent) {
      // 如果有搜索值
      if (this.queryForm.name != "") {
        // 选中
        if (selected) {
          // 去重
          let newData = [...this.rightData, ...selectedRows];
          newData = newData.reduce(function (prev, cur) {
            let has = false;
            prev.map((v) => {
              if (v.userId == cur.userId) {
                has = true;
              }
            });
            if (!has) {
              prev.push(cur);
            }
            return prev;
          }, []);
          this.rightData = newData.reverse();
        } else {
          // 取消选中
          this.rightData = this.rightData.filter(
            (item) => item.userId != record.userId
          );
        }
      } else {
        if (selected) {
          // 去重
          let newData = [...this.rightData, ...selectedRows];
          newData = newData.reduce(function (prev, cur) {
            let has = false;
            prev.map((v) => {
              if (v.userId == cur.userId) {
                has = true;
              }
            });
            if (!has) {
              prev.push(cur);
            }
            return prev;
          }, []);
          this.rightData = JSON.parse(JSON.stringify(newData.reverse()));
        } else {
          // 取消选中
          this.rightData = this.rightData.filter(
            (item) => item.userId != record.userId
          );
          this.rightData.reverse();
        }
      }
      // 设置选中值
      this.selectedRowKeys = this.rightData.map((item) => item.userId);
      this.rightLength = this.rightData.length;
      if (selected) {
        for (let index = 0; index < this.rightData.length; index++) {
          const element = this.rightData[index];
          if (element.userId == record.userId) {
            this.rightData.splice(index, 1);
            break;
          }
        }
        this.rightData.unshift(record);
      }
    },
    // 关闭
    close () {
      this.jointTableVisible = false;
      // Object.assign(this.$data, this.$options.data());
    },
    search () {
      this.queryForm.pageNo = 1;
      this.pagination.current = 1;
      this.getDeputyListData();
    },
    // 获取用户列表
    getDeputyListData () {
      this.leftLoading = true;
      // this.rightLoading = true;
      let nameList = [];
      if (this.queryForm.name && this.queryForm.name?.indexOf(" ")) {
        this.queryForm.name = this.queryForm.name
          .toString()
          .replace(/\s/g, "，");
      }
      if (this.queryForm.name && this.queryForm.name?.indexOf(",")) {
        this.queryForm.name = this.queryForm.name
          .toString()
          .replace(/,/gi, "，");
        nameList = this.queryForm.name.split("，");
      } else {
        this.queryForm.name = "";
      }
      nameList = nameList.filter(i => {
        return i && i != null && i.trim();
      });
      this.queryForm.name = nameList.toString() || [];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
      // getDeputyPage
      // getDeputyList
      getDeputyPage(this.queryForm).then((res) => {
        if (res.data.code == 200) {
          this.dataSource = res.data.data.records;
          this.pagination.total = res.data.data.total;
          // 将不可选的选项进行禁用
          if (this.disableSelectedRowKeys && this.disableSelectedRowKeys.length > 0) {
            this.dataSource.map(item => {
              this.disableSelectedRowKeys.map(i => {
                if (i == item.orgCode) {
                  item.disabledXUANX = "0";
                }
              });
            });
          }
          // setTimeout(() => {
          //   // 回显右边数据
          //   if (this.selectedRowKeys.length > 0) {
          //     this.rightData = this.dataSource.filter((item) => {
          //       return this.selectedRowKeys.includes(item.userId)
          //     })
          //   }
          //   this.rightLength = this.rightData.length;
          // }, 500)
          this.leftLoading = false;
          // this.rightLoading = false;
        }
      });
    },
    // 回显右侧列表
    getDefaultDeputyListData () {
      if (!this.defaultSelectKey || this.defaultSelectKey.length == 0) {
        return;
      }
      this.rightLoading = true;
      let param = {
        includeOperIds: this.defaultSelectKey.join(',')
      };
      getDeputyList(param).then((res) => {
        if (res.data.code == 200) {

          this.selectedRowKeys = this.defaultSelectKey;
          this.rightData = res.data.data;
          this.rightLength = res.data.data.length;
          this.$forceUpdate();
          console.log(this.rightData, 'this.rightData1 ')
          this.rightLoading = false;
        }
      });
    },
    handleCurrentChange (pageNum, pageSize) {
      this.queryForm.pageNo = pageNum;
      this.pagination.current = pageNum;
      this.getDeputyListData();
    },
    allClear () {
      Object.assign(this.$data, this.$options.data.call(this));
    }
  },
};
</script>
<style scoped>
/* :v-deep .ant-table-pagination .ant-pagination-item-active a {
  width: 32px !important;
  height: 32px !important;
} */
div >>> .ant-modal-body {
  height: 550px;
  overflow-y: scroll;
}
:v-deep .messageIndex {
  z-index: 9999 !important;
}
:v-deep .global-messageZindex {
  z-index: 3000 !important;
}
</style>
