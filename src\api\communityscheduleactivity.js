
import request from "@/utils/requestTemp";

export function save(data) {
  return request({
    url: "/communityScheduleActivity/save",
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }, // 请求头
    transformRequest: [(data) => {
      return JSON.stringify(data)
    }],
    data: data,
  });
}

export function list(params) {
  return request({
      url: "/communityScheduleActivity/list",
      method: 'get',
      params: params,
  });
}

export function mineList(params) {
  return request({
      url: "/communityScheduleActivity/mineList",
      method: 'get',
      params: params,
  });
}

export function getById(params) {
  return request({
      url: "/communityScheduleActivity/getById",
      method: 'get',
      params: params,
  });
}
// 编辑
export function update(data) {
  return request({
    url: "/communityScheduleActivity/update",
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }, // 请求头
    transformRequest: [(data) => {
      return JSON.stringify(data)
    }],
    data: data,
  });
}
//检查用户id与日期的冲突，提醒录入员
export function checkIsExistLiaisonStationIdAndUserIds(data) {
  return request({
      url: "/communityScheduleActivity/checkIsExistLiaisonStationIdAndUserIds",
      method: 'post',
      data: data,
  });
}