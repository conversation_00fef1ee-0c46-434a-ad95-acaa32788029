import { instance_yajy } from "@/api/axiosRq";

// 查询字典数据列表
export function listData(query) {
  return instance_yajy({
    url: '/api/v1/system/dict/data/list',
    method: 'get',
    params: query
  })
}

// 查询字典数据详细
export function getData(dictCode) {
  return instance_yajy({
    url: '/api/v1/system/dict/data/' + dictCode,
    method: 'get'
  })
}

// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  return instance_yajy({
    url: '/api/v1/system/dict/data/type/' + dictType,
    method: 'get'
  })
}

// 新增字典数据
export function addData(data) {
  return instance_yajy({
    url: '/api/v1/system/dict/data',
    method: 'post',
    data: data
  })
}

// 修改字典数据
export function updateData(data) {
  return instance_yajy({
    url: '/api/v1/system/dict/data',
    method: 'put',
    data: data
  })
}

// 删除字典数据
export function delData(dictCode) {
  return instance_yajy({
    url: '/api/v1/system/dict/data/del/'+dictCode,
    method: 'get'
  })
}

// 导出字典数据 (新未)
export function exportData(query) {
  return instance_yajy({
    url: '/api/v1/system/dict/data/export',
    method: 'get',
    params: query
  })
}
