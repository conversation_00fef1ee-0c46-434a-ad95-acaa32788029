import { instance_yajy } from "@/api/axiosRq";

// 根据条件获取建议纸模板列表
export function zhiDemotPage(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/zhiDemo/findPage`,
    method: "post",
    data: formData,
  });
}
//删除纸模板列表
export function deleteFile(id) {
  return instance_yajy({
    url: `/api/v1/proposal/zhiDemo/deleteFile`,
    method: "post",
    params: { id },
  });
}
//批量下载/导出
export function batchExport(ids) {
  return instance_yajy({
    url: `/api/v1/proposal/zhiDemo/batchExport`,
    method: "post",
    responseType: "blob",
    params: { id: ids },
  });
}
// 新增
export function zhiDemoSave(form) {
  return instance_yajy({
    url: `/api/v1/proposal/zhiDemo/save`,
    method: "post",
    data: form,
  });
}
