<template>
  <div class="table-container">
    <a-row class="formBox">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :labelCol="{ span: 6}"
                    :wrapperCol="{ span: 18, offset: 0 }">
        <a-col span="6">
          <a-form-model-item label="届次">
            <a-select placeholder="请选择届次"
                      v-model="queryForm.period"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in periods"
                               :key="item.id"
                               :value="item.id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="性别"
                             prop="sex">
            <a-select v-model="queryForm.sex"
                      allow-clear
                      style="width: 100%">
              <a-select-option key="0"
                               value="0">女</a-select-option>
              <a-select-option key="1"
                               value="1">男</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="姓名"
                             prop="name">
            <a-input v-model="queryForm.name"
                     autocomplete="off"
                     allow-clear
                     placeholder="请输入姓名"></a-input>
          </a-form-model-item>
        </a-col>
      </a-form-model>
      <a-button type="primary">搜索</a-button>
      <a-button style="margin-left: 12px;"
                class="pinkBoutton">重置</a-button>
    </a-row>
    <a-row>
      <standard-table :columns="columns"
                      rowKey="id"
                      :dataSource="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"></standard-table>
    </a-row>
  </div>
</template>
<script>
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
export default {
  // 候选人选名册
  components: { StandardTable },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      periods: [
        // 议案届别
        { id: 16, name: "十五届六次" },
        { id: 15, name: "十五届一次" },
        { id: 1, name: "十五届二次" },
        { id: 2, name: "十五届三次" },
        { id: 3, name: "十五届四次" },
        { id: 4, name: "十五届五次" },
      ],
      queryForm: {},
      columns: [
        {
          title: "姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "name",
        },
        {
          title: "性别",
          align: "center",
          width: 90,
          ellipsis: true,
          dataIndex: "sex",
        },
        {
          title: "出生日期",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "birthDay",
        },
        {
          title: "籍贯",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "phone",
        },
        {
          title: "党派",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "address",
        },
        {
          title: "全日制教育学历",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "unit",
        },
        {
          title: "工作单位及职务",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "job",
        },
        {
          title: "职业构成",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "isOutC",
        },
        {
          title: "综合构成",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "isOutAddress",
        },
        {
          title: "推荐单位",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "isOutLongAddress",
        },
      ],
      dataSource: [],
      selectedRows: [],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "候选人管理");
    this.$store.dispatch("navigation/breadcrumb2", "候选人选名册");
    this.fetchData();
    // 创建假数据
    let data = {};
    this.columns.map((item) => {
      data[item.dataIndex] = "假数据";
    });
    this.dataSource.push(data);
  },
  methods: {
    // 获取数据
    fetchData () { },
  },
};
</script>
<style scoped>
.formBox {
  padding: 20px;
}
</style>
