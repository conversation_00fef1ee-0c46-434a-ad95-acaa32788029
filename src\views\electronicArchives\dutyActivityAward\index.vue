<template>
    <div class="table-container">
      <a-row>
        <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <a-row>
            <a-form
              ref="form"
              :model="queryForm"
              layout="horizontal"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18, offset: 0 }"
            >
              <div>
                <SearchForm @onReset="clearListQuery" @onSearch="handleQuery" :noMore="true">
                  <template v-slot:topSearch>
                    <DhJcCascade v-model="queryForm" allow-clear is-dbt @load="search" :isDefaultLoad="false" />
                    <SingleSearch @onEnter="handleEnter" :title="'内容'" :value.sync="queryForm.content" />
                    <TimeRangePicker :startTimeValue.sync="queryForm.startTime" :endTimeValue.sync="queryForm.endTime" :title="'时间范围'" />
                  </template>
                </SearchForm>

                <a-row style="margin: 5px 0px 10px 8px">
                  <a-button
                    type="primary"
                    style="margin-right: 10px"
                    @click="add()"
                    >新增</a-button
                  >
                  <a-button
                    type="primary"
                    style="margin-right: 10px"
                    :loading="downloadExcelLoading"
                    @click="downloadExcel()"
                    >下载模版</a-button
                  >
                  <div style="display: inline-block;margin-right: 10px">
                    <a-upload
                      name="file"
                      accept=".xls,.xlsx"
                      :multiple="true"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      @change="downImport"
                    >
                      <a-button type="primary">
                        导入</a-button
                      >
                    </a-upload>
                  </div>
                  <a-button
                    :loading="loadings"
                    type="primary"
                    @click="exportExcel"
                    >导出</a-button
                  >
                </a-row>
              </div>
            </a-form>
          </a-row>

          <standard-table
            ref="noticeTable"
            :loading="listLoading"
            :selected-rows.sync="selectedRowKeys"
            @onChange="onSelectChange"
            rowKey="id"
            :pagination="pagination"
            :columns="columns"
            :data-source="dataSource"
          ></standard-table>
        </a-col>
      </a-row>
    </div>
  </template>

  <script>
  import { getDutyActivityAwardListVo, downloadDutyActivityAwardExcelTemplete, importDutyActivityAwardByExcel, exportDutyActivityAwardExcel, deleteDutyActivityAwardById } from "@/api/electronicArchives";
  import { myPagination } from "@/mixins/pagination.js";
  import SingleSelect from '@/components/SingleSelect/index';
  import SearchForm from '@/components/SearchForm/index';
  import SingleSearch from '@/components/SingleSearch/index';
  import TimeRangePicker from '@/components/TimeRangePicker/index';
  import DhJcCascade from "@/components/DhJcCascade/index.vue";
  export default {
    name: "Table",
    components: {
      SingleSelect,
      SearchForm,
      SingleSearch,
      TimeRangePicker,
      DhJcCascade
    },
    mixins: [myPagination],
    filters: {},
    data() {
      return {
        listLoading: true,
        dataSource: [],
        dateRange: [],
        selectedRowKeys: [],
        selectRows: [],
        advanced: false,
        loadings: false,

        indicator: <a-icon type="loading" style="font-size: 24px" spin />,
        queryForm: {
          startTime: "",
          endTime: "",
          pageNum: 1,
          pageSize: 10,
          liaisonStationName: "",
          title: "",
          content:"",
          status:"",
        },
        indexNum: 1,
        downloadExcelLoading: false,
        fileList: [],
        // 列表
        columns: [
          {
            fixed: "left",
            title: "序号",
            key: "index",
            align: "center",
            width: 80,
            ellipsis: true,
            customRender: (text, record, index) =>
              `${(this.indexNum - 1) * this.indexNum + index + 1}`,
          },
          {
            title: "时间",
            align: "center",
            width: 250,
            ellipsis: true,
            dataIndex: "achieveDate",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "获奖情况",
            align: "center",
            ellipsis: true,
            dataIndex: "content",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            fixed: "right",
            title: "操作",
            align: "center",
            width: 200,
            customRender: (text, record, index) => {
              let h = this.$createElement;
              return h("div", [
              h(
                  "span",
                  {
                    attrs: {
                      type: "text",
                    },
                    style: {
                      cursor: "pointer",
                      marginLeft: "14px",
                      color: "#DB3046",
                    },
                    on: {
                      click: () => {
                        this.toItemList(record.id);
                      },
                    },
                  },
                  "新增代表"
                ),
                h(
                  "span",
                  {
                    attrs: {
                      type: "text",
                    },
                    style: {
                      cursor: "pointer",
                      marginLeft: "14px",
                      color: "#DB3046",
                    },
                    on: {
                      click: () => {
                        this.edit(record.id);
                      },
                    },
                  },
                  "修改"
                ),
                h(
                  "span",
                  {
                    attrs: {
                      type: "text",
                    },
                    style: {
                      cursor: "pointer",
                      marginLeft: "14px",
                      color: "#DB3046",
                    },
                    on: {
                      click: () => {
                        this.delete(record.id);
                      },
                    },
                  },
                  "删除"
                ),
              ]);
            },
          },
        ],
        searchDebounced: _.debounce(this.handleQuery, 500),
      };
    },
    created() {
      this.fetchData();
    },
    activated() {
      this.fetchData();
    },
    methods: {
      handleEnter() {  
        this.searchDebounced();   
      },
      getTime(val) {
        this.queryForm.startTime = val[0];
        this.queryForm.endTime = val[1];
      },
      // 多选选择
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys;
        this.selectRows = selectedRows;
      },
      onTimeChange(val) {
        this.queryForm.startTime = val[0];
        this.queryForm.endTime = val[1];
      },
      exportExcel() {
        this.loadings = true;
        // if (this.selectedRowKeys.length == 0) {
        //   return this.$message.info("请至少选择一条数据");
        // }
        let ids = this.selectedRowKeys.map((son) => son.id).toString();
        let { userName, content, startTime, endTime } = this.queryForm;
        let data = {};
        data.ids = ids;
        data.userName = userName;
        data.content = content;
        data.startTime = startTime;
        data.endTime = endTime;
        exportDutyActivityAwardExcel(data).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `获奖情况 `  + new Date().getTime() +  `.xls`;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          this.loadings = false;
        });
      },
      // 高级搜索
      toggleAdvanced() {
        this.advanced = !this.advanced;
      },
      // 点击行
      clickRow(record) {
        return {
          props: {},
          on: {
            // 事件
            click: (event) => {
              if ("__vue__" in event.target) {
                this.details(record.id);
              }
            },
          },
        };
      },
      // 重置
      clearListQuery() {
        this.queryForm = {
          pageNum: 1,
          pageSize: 10,
        };
        this.fetchData();
      },
      fetchData() {
        this.listLoading = true;
        getDutyActivityAwardListVo(this.queryForm).then((res) => {
          this.dataSource = res.rows;
          this.listLoading = false;
          this.total = res.total;
          this.pagination.total = res.total;
        });
      },

      details(id) {
        this.$router
          .push({
            path: "/BBS/appealData-tong",
            query: {
              id: id,
              manageShow: true,
              tong_ListNotice: true,
            },
          })
          .catch(() => {});
      },
      search() {
        this.queryForm.pageNum = 1;
        this.pagination.current = 1;
        this.fetchData();
      },
      handleQuery() {
        this.queryForm.pageNum = 1;
        this.pagination.current = 1;
        this.fetchData();
      },
      handleCurrent(row) {
        console.log("row", row);
      },
      edit(id) {
        this.$router.push({
          path: "/electronicArchives/editDutyActivityAward",
          query: { id: id },
        });
      },
      add() {
        this.$router.push({
          path: "/electronicArchives/addDutyActivityAward",
        });
      },
      // 下载
      downloadExcel() {
        this.downloadExcelLoading = true;
        downloadDutyActivityAwardExcelTemplete(this.queryForm).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `获奖模版.xlsx`;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          setTimeout(() => {
            this.downloadExcelLoading = false;
          }, 1000);
        });
      },
      // 上传文件限制 直接return
      beforeUpload(file, fileList) {
        const isLt20M = file.size / 1024 / 1024 < 20;
        if (!isLt20M) {
          this.$message.error("上传文件大小不能超过 20M!");
        }
        var reg = /(xls)$/i;
        var reg2 = /(xlsx)$/i;
        const isPDF = reg.test(file.name) || reg2.test(file.name);
        if (!isPDF) {
          this.$message.error("只能选择后缀名为.xls或.xlsx的文件");
        }
        if (isLt20M && isPDF) return false;
      },

      //导入活动表
      downImport({ file, fileList }) {
        var data = new FormData();
        data.append("file", file);
        importDutyActivityAwardByExcel(data).then((res) => {
          console.log(res, "res");
          if (res.data.code != "0000") {
            return this.$message.error(res.data.msg);
          }
          if (res.data.code == "0000") {
            this.$baseMessage("上传成功", "success");
            this.fetchData();
          }
        });
      },
      delete(id) {
        this.$confirm({
          cancelText: "取消",
          okType: "danger",
          okText: "确定",
          title: "确定删除",
          content: "是否确定删除？",
          onOk: () => {
            deleteDutyActivityAwardById({id:id}).then((res) => {
              if (res.code == '0000') {
                this.$message.success("操作成功");
                this.fetchData();
              } else {
                this.$message.error(res.message);
              }
            });
          },
          onCancel: () => {
            this.$message.info("您已取消操作！");
          },
        });
      },
      toItemList(id) {
        this.$router.push({
          path: "/electronicArchives/dutyActivityAwardItem",
          query: { id: id },
        });
      },
    },
  };
  </script>
  <style scoped>
  .code_btn {
    margin-bottom: 10px;
  }

  .p {
    display: flex;
    width: 100%;
  }

  .p > span {
    flex: 1;
  }
  </style>
