<template>
  <a-col :xs="24" :sm="24" :md="24" :lg="span" :xl="span">
    <div class="left-panel">
      <slot></slot>
    </div>
  </a-col>
</template>

<script>
export default {
  name: "ByuiQueryFormLeftPanel",
  props: {
    span: {
      type: Number,
      default: 14,
    },
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="css" scoped>
.left-panel >>> button + button,
.left-panel >>> div + button {
  margin-left: 10px;
}
</style>
