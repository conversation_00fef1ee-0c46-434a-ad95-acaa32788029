import Vue from "vue";
import VueRouter from "vue-router";
import Layout from "@/layouts";
import EmptyLayout from "@/layouts/EmptyLayout";

Vue.use(VueRouter);

export const constantRoutes = [
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/yzy",
    component: () => import("@/views/login/yzy"),
    hidden: true,
  },
  {
    path: "/casLogin",
    component: () => import("@/views/login/cas"),
    hidden: true,
  },
  {
    path: "/site/index1",
    name: "/siteIndex",
    component: () => import("@/views/proelemasses/index/index"),
    meta: {
      title: "人民群众首页",
    },
  },
  {
    path: "/indexEwm",
    component: () => import("@/views/proelemasses/index/indexEwm"),
    hidden: true,
  },
  {
    path: "/indexEwmNoBorder",
    component: () => import("@/views/proelemasses/index/indexEwmNoBorder"),
    hidden: true,
  },
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index"),
      },
    ],
  },
  {
    path: "/401",
    component: () => import("@/views/401"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true,
  },

  {
    path: "/site/index2",
    component: EmptyLayout,
    redirect: "/site/index",
    children: [
      {
        path: "/site/index",
        name: "index",
        component: () => import("@/views/site/index/index"),
        meta: {
          title: "民呼我应",
          icon: "database",
          affix: false,
        },
      },
      {
        path: "/site/message",
        name: "message",
        hidden: true,
        component: () => import("@/views/site/message/index"),
        meta: {
          title: "留言管理",
          icon: "database",
          affix: false,
        },
      },
      {
        path: "/site/neWindex",
        name: "siteneWindex",
        component: () => import("@/views/proelemasses/index/neWindex"),
        meta: {
          title: "人民群众首页",
          affix: false,
        },
      },
      {
        path: "/site/message/details",
        name: "message",
        hidden: true,
        component: () => import("@/views/site/message/details"),
        meta: {
          title: "查看详情",
          icon: "database",
          affix: false,
        },
      },
    ],

  },
];


import platformPortalRouter from "./modules/platformPortal";
import masseslRouter from "./modules/Proelemasses";
import jointMeetingRouter from "./modules/jointMeeting";
import organizationRouter from "./modules/organization";
import suggestvRouter from "./modules/suggestv";
import BillmanageRouter from "./modules/Billmanage";
import registerRouter from "./modules/register";
import electionRouter from "./modules/election";
import informationRouter from "./modules/information";
import studyDataRouter from "./modules/studyData";
import sjjsc from "./modules/sjjsc";

// 这里是动态路由
export const asyncRoutes = [
  ...platformPortalRouter,
  ...masseslRouter,
  ...jointMeetingRouter,
  ...organizationRouter,
  ...suggestvRouter,
  ...BillmanageRouter,
  ...registerRouter,
  ...electionRouter,
  ...informationRouter,
  ...studyDataRouter,
  ...sjjsc,
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  scrollBehavior: () => ({
    y: 0,
  }),
  routes: constantRoutes,
});

export function resetRouter () {
  router.matcher = new VueRouter({
    mode: "history",
    base: process.env.BASE_URL,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: constantRoutes,
  }).matcher;
}

const routerPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push (location) {
  return routerPush.call(this, location).catch((error) => error);
};
export default router;
