import request from '@/utils/requestTemp'
import { instance_1 } from "@/api/axiosRq";

export function getFindPageQz(formData) {
  return request({
    url: `/proposalQz/proposalSuggestionQz/findMyCreated`,
    method: "post",
    data: formData,
  });
}

// 保存建议(区镇数据)
export function suggestionQzSave(formData) {
  return request({
    url: `/proposalQz/proposalSuggestionQz/save`,
    method: "post",
    data: formData,
  });
}

// 根据id获取议案建议信息
export function proposalQzById(params) {
  return request({
    url: `/proposalQz/proposalSuggestionQz/getById`,
    method: "post",
    params,
  });
}

// 议案建议列表删除
export function deleteByIdQz(proposalId) {
  let formData = new FormData();
  formData.append("proposalId", proposalId);
  return request({
    url: `/proposalQz/proposalSuggestionQz/deleteByIdQz`,
    method: "post",
    data: formData,
  });
}


//获取导入模板
export function getImportTemplate() {
  return instance_1({
    url: '/proposalQz/proposalSuggestionQz/getImportTemplate',
    method: 'get',
    responseType: 'blob'
    })
}

//批量导入建议
export function importSuggest(data) {
  return request({
    url: '/proposalQz/proposalSuggestionQz/importSuggest',
    method: 'post',
    data: data,
     headers: {
      "Content-Type": "multipart/form-data",
    },

  })
}
