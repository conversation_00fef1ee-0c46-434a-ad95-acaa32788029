<template>
  <el-dialog
    :before-close="close"
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="dialogFormVisible"
    width="909px"
  >
    <div id="app">
      <a-row class="mt20">
        <a-col :span="24">
          <el-tabs>
            <el-tab-pane label="参加人员">
              <a-col
                v-for="(item, index) in identityList"
                :key="index"
                :span="24"
              >
                <template v-if="item.userList.length > 0">
                  <h3 class="mb10 mt10">{{ item.name }}</h3>

                  <el-checkbox-group
                    ref="checkIdentyRef"
                    @change="handleCheckedIdentity"
                    v-model="checkIdentyData"
                    size="mini"
                  >
                    <el-checkbox
                      v-for="itemCheckBox in item.userList"
                      :key="itemCheckBox.id"
                      :label="itemCheckBox.id"
                      border
                      >{{ itemCheckBox.userName }}</el-checkbox
                    >
                  </el-checkbox-group>
                </template>
              </a-col>
            </el-tab-pane>
            <el-tab-pane label="机构树">
              <el-tree
                ref="userTree"
                show-checkbox
                node-key="uniqueId"
                :data="userTreeData"
                :props="userTreeProps"
                :check-on-click-node="true"
                :filter-node-method="filterNode"
                :expand-on-click-node="true"
                @check-change="checkChange"
              ></el-tree>
            </el-tab-pane>
          </el-tabs>
        </a-col>
      </a-row>
    </div>
    <div
      slot="footer"
      class="dialog-footer"
      style="position: relative; padding-right: 15px; text-align: right;"
    >
      <el-button type="primary" @click="close">关闭</el-button>
      <el-button
        :loading="loading"
        size="small"
        style="margin-left: 10px;"
        type="success"
        @click="confirm"
      >
        保存
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import {getTreeOrderByXsbh} from "@/api/user";
import { getIndentityUserList } from "@/api/identity";
import { getCalendarUserList } from "@/api/calendar";
export default {
  data() {
    return {
      loading: false,
      title: "关联人员",
      calendarId: "",
      filterText: "",
      dialogFormVisible: false,
      identityList: [],
      identityListRes: [],
      checkIdentyData: [],
      userTreeData: [],
      treeCheckedKey: [],
      userTreeProps: {
        children: "children",
        label: "name",
      },
      isRadio: {
        type: Boolean,
        default: true,
      },
      relType: {
        type: Number,
        default: 1,
      },
    };
  },
  created() {
    this.initIdentityList();
    this.initUserTree();
  },
  watch: {
    filterText(val) {
      this.$refs.userTree.filter(val);
    },
  },
  methods: {
    submitUpload() {},
    initUserTree() {
      let data = {
        tenantId: this.$tenantId,
        rootOrgId: "root",
      };
      getTreeOrderByXsbh(data).then((res) => {
        this.userTreeData = res.data.children;
      });
    },
    initIdentityList() {
      getIndentityUserList().then((res) => {
        this.identityList = res.data;
        this.identityListRes = res.data;
      });
    },
    handleHide() {
      this.dialogFormVisible = false;
    },
    handleShow(row) {
      this.calendarId = row.id;
      this.dialogFormVisible = true;
      //回选旧数据
      getCalendarUserList(row.id).then((res) => {
        if (res.data.identityList.length > 0) {
          res.data.identityList.forEach((item) => {
            this.checkIdentyData.push(item.identityUserId);
          });
        }

        if (res.data.treeList.length > 0) {
          res.data.treeList.forEach((item) => {
            this.treeCheckedKey.push(item.identityUserId);
          });
          this.$refs.userTree.setCheckedKeys(this.treeCheckedKey);
          let checkedNodes = this.$refs.userTree.getCheckedNodes(false, true);
          checkedNodes.forEach((item) => (item.expanded = true));
        }

        console.log("身份回选", this.checkIdentyData);
        console.log("树回选", this.treeCheckedKey);
      });
    },

    checkChange(data, checked, node) {
      // if (checked) {
      //   this.$refs.userTree.setCheckedKeys(data.uniqueId);
      // }
    },
    confirm() {
      const checkedNodes = this.$refs.userTree.getCheckedNodes();
      //处理完再丢过去父页面
      // 原userList数组
      let arr = this.identityList;
      // userList选中数组
      let yrr = this.checkIdentyData;
      //新数组
      let nrr = [];
      yrr.forEach((item) => {
        arr.forEach((ytem) => {
          ytem.userList.forEach((ztem) => {
            if (item == ztem.id) {
              let obj = {
                identityUserId: ztem.id,
                userId: ztem.userId,
                userName: ztem.userName,
                identityId: ztem.identityId,
                identityName: ztem.identityName,
                type: 0,
              };
              nrr.push(obj);

              return false;
            }
          });
        });
      });

      this.checkIdentyData = [];
      this.treeCheckedKey = [];
      this.$refs.userTree.setCheckedKeys(this.treeCheckedKey);
      //收起所有节点
      this.$refs.userTree.$children.forEach((item) => (item.expanded = false));
      this.$parent.saveRel(checkedNodes, nrr);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    close() {
      this.checkIdentyData = [];
      this.treeCheckedKey = [];
      this.$refs.userTree.setCheckedKeys(this.treeCheckedKey);
      //收起所有节点
      this.$refs.userTree.$children.forEach((item) => (item.expanded = false));
      this.dialogFormVisible = false;
    },

    // 多选绑定事件
    handleCheckIdenty(val) {
      console.log(this.checkIdentyData);
    },
    handleCheckedIdentity(value) {
      this.checkIdentyData = this.unique(value);
      console.log(this.checkIdentyData);
    },
    //数组去重
    unique(arr) {
      return Array.from(new Set(arr));
    },
  },
};
</script>
