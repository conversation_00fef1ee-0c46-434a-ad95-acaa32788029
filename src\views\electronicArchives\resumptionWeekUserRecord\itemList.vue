<template>
    <div class="table-container">
      <a-row>
        <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <!-- <a-row>
            <a-form
              ref="form"
              :model="queryForm"
              layout="horizontal"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18, offset: 0 }"
            > -->
              <!-- <div> -->
                <!-- <a-row>
                  <DhJcCascade v-model="queryForm" allow-clear is-dbt @load="search"/>
                  <a-col :span="6">
                    <a-form-item
                      label="代表名称"
                      :label-col="{ span: 6 }"
                      :wrapper-col="{ span: 18, offset: 0 }"
                    >
                      <a-input
                        v-model="queryForm.userName"
                        allow-clear
                        autocomplete="off"
                        placeholder="请输入代表姓名"
                        @keyup.enter="handleQuery"
                      ></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col :span="6" :offset="6">
                    <span style="float: right; margin-top: 3px">
                      <a-button type="primary" @click="search"
                        >搜索</a-button
                      >
                      <a-button
                        style="margin-left: 12px"
                        class="pinkBoutton"
                        @click="clearListQuery"
                        >重置</a-button
                      >
                    </span>
                  </a-col>
                </a-row> -->
                <SearchForm @onReset="clearListQuery" @onSearch="search" :noMore="true">
                  <template v-slot:topSearch>
                    <DhJcCascade v-model="queryForm" allow-clear is-dbt @load="search"/>
                    <SingleSearch @onEnter="handleQuery" :title="'代表姓名'" :value.sync="queryForm.userName" />
                  </template>
                </SearchForm>

                <a-row style="margin: 5px 0px 10px 8px">
                  <a-button
                    type="primary"
                    style="margin-right: 10px"
                    @click="edit()"
                    >新增代表</a-button
                  >
                  <a-button
                    :loading="loadings"
                    type="primary"
                    @click="exportExcel"
                    >导出</a-button
                  >
                </a-row>
              <!-- </div> -->
            <!-- </a-form> -->
          <!-- </a-row> -->
  
          <standard-table
            ref="noticeTable"
            :loading="listLoading"
            :selected-rows.sync="selectedRowKeys"
            @onChange="onSelectChange"
            rowKey="id"
            :pagination="pagination"
            :columns="columns"
            :data-source="dataSource"
          ></standard-table>
        </a-col>
      </a-row>
    </div>
  </template>
  
  <script>
  import { getResumptionWeekItemListVo, deleteResumptionWeekUserRecordById, exportResumptionWeekUserRecordExcel} from "@/api/electronicArchives";
  import { myPagination } from "@/mixins/pagination.js";
  import SearchForm from '@/components/SearchForm/index';
  import DhJcCascade from "@/components/DhJcCascade/index.vue";
  import SingleSearch from '@/components/SingleSearch/index';
  export default {
    name: "Table",
    mixins: [myPagination],
    components: {
      SearchForm,
      DhJcCascade,
      SingleSearch
    },
    filters: {},
    data() {
      return {
        listLoading: true,
        dataSource: [],
        dateRange: [],
        selectedRowKeys: [],
        selectRows: [],
        advanced: false,
        loadings: false,
  
        indicator: <a-icon type="loading" style="font-size: 24px" spin />,
        queryForm: {
          startTime: "",
          endTime: "",
          pageNum: 1,
          pageSize: 10,
          title: "",
          content:"",
          status:"",
        },
        indexNum: 1,
        fileList: [],
        recordId: null,
        // 列表
        columns: [
          {
            fixed: "left",
            title: "序号",
            key: "index",
            align: "center",
            width: 10,
            ellipsis: true,
            customRender: (text, record, index) =>
              `${(this.indexNum - 1) * this.indexNum + index + 1}`,
          },
          {
            title: "代表团/机构",
            align: "center",
            width: 180,
            ellipsis: true,
            dataIndex: "orgNames",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "代表姓名",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "userName",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            fixed: "right",
            title: "操作",
            align: "center",
            width: 200,
            customRender: (text, record, index) => {
              let h = this.$createElement;
              return h("div", [
                h(
                  "span",
                  {
                    attrs: {
                      type: "text",
                    },
                    style: {
                      cursor: "pointer",
                      marginLeft: "14px",
                      color: "#DB3046",
                    },
                    on: {
                      click: () => {
                        this.delete(record.id);
                      },
                    },
                  },
                  "删除"
                ),
              ]);
            },
          },
        ],
      };
    },
    mounted() {
      let { id } = this.$route.query;
      this.recordId = id;
      this.fetchData();
    },
    created() {

    },
    methods: {
      // 多选选择
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys;
        this.selectRows = selectedRows;
      },
      onTimeChange(val) {
        this.queryForm.startTime = val[0];
        this.queryForm.endTime = val[1];
      },
      exportExcel() {
        this.loadings = true;
        let data = {};
        data.parentId = this.recordId;
        exportResumptionWeekUserRecordExcel(data).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `意见建议被《人大代表情况反映》情况 `  + new Date().getTime() +  `.xls`;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          this.loadings = false;
        });
      },
      // 高级搜索
      toggleAdvanced() {
        this.advanced = !this.advanced;
      },
      // 重置
      clearListQuery() {
        this.queryForm = {
          pageNum: 1,
          pageSize: 10,
        };
        this.fetchData();
      },
      fetchData() {
        this.listLoading = true;
        this.queryForm.parentId = this.recordId
        getResumptionWeekItemListVo(this.queryForm).then((res) => {
          this.dataSource = res.rows;
          this.listLoading = false;
          this.total = res.total;
          this.pagination.total = res.total;
        });
      },
      search() {
        this.pagination.current = 1;
        this.queryForm.pageNum = 1;
        this.fetchData();
      },
      handleQuery() {
        this.fetchData();
      },
      handleCurrent(row) {
        console.log("row", row);
      },
      edit() {
        this.$router.push({
          path: "/electronicArchives/addResumptionWeekItem",
          query: { id: this.recordId },
        });
      },
      add() {
        this.$router.push({
          path: "/electronicArchives/addResumptionWeekItem",
        });
      },
      delete(id) {
        this.$confirm({
          cancelText: "取消",
          okType: "danger",
          okText: "确定",
          title: "确定删除",
          content: "是否确定删除？",
          onOk: () => {
            deleteResumptionWeekUserRecordById({id:id}).then((res) => {
              if (res.code == '0000') {
                this.$message.success("操作成功");
                this.fetchData();
              } else {
                this.$message.error(res.message);
              }
            });
          },
          onCancel: () => {
            this.$message.info("您已取消操作！");
          },
        });
      },
      handleCancel() {
        this.visibleMatchUserName = false
        this.fetchData()
      }
    },
  };
  </script>
  <style scoped>
  .code_btn {
    margin-bottom: 10px;
  }
  
  .p {
    display: flex;
    width: 100%;
  }
  
  .p > span {
    flex: 1;
  }
  </style>
  