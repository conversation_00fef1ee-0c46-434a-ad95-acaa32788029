import request from '@/utils/requestTemp'
import { param } from 'jquery'

// 获取代表信息列表
export function getList(query,queryParams) {
  return request({
    url: '/cockpit/jscQuRepresentativeSituation/getAll',
    method: 'post',
    params: query,
    data: queryParams
  })
}

// 新增代表信息
export function add(data) {
  return request({
    url: '/cockpit/jscQuRepresentativeSituation/add',
    method: 'post',
    data: data
  })
}

// 修改代表信息
export function update(data) {
  return request({
    url: '/cockpit/jscQuRepresentativeSituation/update',
    method: 'post',
    data: data
  })
}

// 删除代表信息
export function remove(id) {
  return request({
    url: '/cockpit/jscQuRepresentativeSituation/delete/'+id,
    method: 'get'
  })
}
