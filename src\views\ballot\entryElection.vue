<template>
  <div class="table-container">
    <!-- <a-form-model ref="queryForm"
                  :model="queryForm"
                  layout="inline"
                  :label-col="{ span: 6 }"
                  :wrapper-col="{ span: 18 }">
      <a-row>
        <a-col span="6">
          <a-form-model-item label="届次"
                             style="width:100%;">
            <a-select v-model="queryForm.jcDm"
                      allow-clear
                      placeholder="请选择届次"
                      style="width:100%;">
              <a-select-option v-for="item in jcDmList"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="行政区划"
                             prop="xzqhDm"
                             style="width:100%;">
            <a-select v-model="queryForm.xzqhDm"
                      allow-clear
                      style="width:100%;"
                      placeholder="请选择行政区划">
              <a-select-option v-for="item in xzqhDmList"
                               :key="item.xzqhDm"
                               :value="item.xzqhDm">{{ item.xzqhmc }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="姓名"
                             prop="name"
                             style="width:100%;">
            <a-input v-model="queryForm.userName"
                     autocomplete="off"
                     style="width:100%;"
                     placeholder="请输入姓名"
                     allow-clear
                     v-on:keyup.enter="search"></a-input>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <span style="float:right">
            <a @click="toggleAdvanced"
               style="margin-right: 8px">
              {{ advanced ? "收起" : "高级搜索" }}
              <a-icon :type="advanced ? 'up' : 'down'" />
            </a>
            <a-button type="primary"
                      style="margin-left: 12px;"
                      @click="search">搜索</a-button>
            <a-button style="margin-left: 12px;"
                      @click="reset"
                      class="pinkBoutton">重置</a-button>
          </span>
        </a-col>
      </a-row>
      <a-row v-if="advanced">
        <a-col span="6"
               v-if="advanced">
          <a-form-model-item label="当选情况"
                             prop="primary"
                             style="width:100%;">
            <a-select v-model="queryForm.dxqkDm"
                      allow-clear
                      placeholder="请选择当选情况"
                      style="width:100%;">
              <a-select-option key="0"
                               value="0">候选人当选</a-select-option>
              <a-select-option key="1"
                               value="1">代表联名</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6"
               v-if="advanced">
          <a-form-model-item label="选举情况"
                             prop="e_situation"
                             style="width:100%;">
            <a-select v-model="queryForm.xjqkDm"
                      allow-clear
                      placeholder="请选择选举情况"
                      style="width:100%;">
              <a-select-option key="0"
                               value="0">当选</a-select-option>
              <a-select-option key="1"
                               value="1">落选</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6"
               v-if="advanced">
          <a-form-model-item label="决定单位"
                             prop="company"
                             style="width:100%;">
            <a-select v-model="queryForm.jddwDm"
                      allow-clear
                      placeholder="请选择决定单位"
                      style="width:100%;">
              // <a-select-option key="0" value="0">越秀区人大</a-select-option>
              <a-select-option key="1" value="1">番禺区人大</a-select-option>
              <a-select-option key="2" value="2">南沙区人大</a-select-option>
              <a-select-option key="3" value="3">花都区人大</a-select-option> //
              <a-select-option v-for="item in jddwDmList"
                               :key="item.verifyUnitDm"
                               :value="item.verifyUnitDm">{{
                  item.verifyUnitName
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6"
               v-if="advanced">
          <a-form-model-item label="表单状态"
                             prop="state"
                             style="width:100%;">
            <a-select v-model="queryForm.state"
                      placeholder="请选择表单状态"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in statesList"
                               :key="item.dqztDm"
                               :value="item.dqztDm">{{ item.state }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>

      <a-row style="margin: 10px 0px 10px 8px;">
        <a-col :span="6">
          <a-button type="primary"
                    style="margin-left: 12px;"
                    @click="dengji">录入</a-button>
          <a-button type="primary"
                    style="margin-left: 12px;"
                    @click="download">导出</a-button>
        </a-col>
      </a-row>
    </a-form-model> -->

    <SearchForm @onReset="reset" @onSearch="search">
      <template v-slot:topSearch>
        <SingleSelect :title="'届次'" :selectList="jcDmList"  :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm" />
        <SingleSelect :title="'行政区划'" :selectList="xzqhDmList"  :showName="'xzqhmc'" :showValue="'xzqhDm'" :value.sync="queryForm.xzqhDm" />
        <SingleSearch @onEnter="search" :title="'姓名'" :value.sync="queryForm.userName" />
      </template>
      <template v-slot:moreSearch>
        <SingleSelect :title="'当选情况'" :selectList="electList" :value.sync="queryForm.dxqkDm" />
        <SingleSelect :title="'选举情况'" :selectList="electSituation" :value.sync="queryForm.xjqkDm" />
        <SingleSelect :title="'决定单位'" :selectList="jddwDmList" :showName="'verifyUnitName'" :showValue="'verifyUnitDm'" :value.sync="queryForm.jddwDm" />
        <SingleSelect :title="'表单状态'" :selectList="statesList" :showName="'state'" :showValue="'dqztDm'" :value.sync="queryForm.state" />
      </template>
    </SearchForm>

      <a-row style="margin: 10px 0px 10px 8px;">
        <a-col :span="6">
          <a-button type="primary"
                    style="margin-left: 12px;"
                    @click="dengji">录入</a-button>
          <a-button type="primary"
                    style="margin-left: 12px;"
                    @click="download">导出</a-button>
        </a-col>
      </a-row>

    <a-row>
      <!-- 引用组件，复选框选中了，图标不显示 -->
      <!-- <StandardTable
        :columns="columns"
        rowKey="id"
        :dataSource="dataSource"
        :pagination="pagination"
        :selectedRows.sync="selectedRows"
      ></StandardTable>-->
      <standard-table :columns="columns"
                      :pagination="pagination"
                      :row-selection="rowSelection"
                      rowKey="ID"
                      :loading="TBloading"
                      :selectedRows.sync="selectedRows"
                      @selectedRowChange="onSelectChange"
                      :data-source="dataSource"
                      @change="onChange">
        <div class="operationStyle"
             slot="operation"
             slot-scope="{text, record}">
          <span @click="chakan(record)">查看</span>
          <span @click="xiugai(record)"
                v-if="record.state != '已终审'">编辑</span>
          <span @click="shenhe(record)"
                v-if="record.state != '已终审'">审核</span>
          <!-- <a-dropdown v-if="record.state != '已终审'">
            <a class="dropdown-link" @click="e => e.preventDefault()">
              更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item v-if="record.state != '已终审'">
                <a @click="shenhe(record)">审核</a>
              </a-menu-item>
            
            </a-menu>
          </a-dropdown>  -->
          <!-- <a-menu-item @click="shanchu(record)" v-if="(record.state == '已终审') ? false : true">
                删除</a-menu-item> -->
          <!-- 选举结果没有删除 -->
        </div>
      </standard-table>
    </a-row>
    <entryElectionIn ref="entryElectionIn"
                     :neWid="neWid"
                     :isRecord="isRecord"
                     @handleClearId="ifhandleClearId"
                     :newList="newList"></entryElectionIn>
    <entryElectionModify ref="entryElectionModify"
                         :isRecord="isRecord"
                         @handleClearId="ifhandleClearId"
                         :newList="newList">
    </entryElectionModify>
  </div>
</template>

<script>
import { getAddeDselecselectApi, getAdministrativeStateListApi, getLevelListApi, getFormStateListApi, getdbxjjgqueryApi, getgetVerifyUnitListApi } from "@/api/representativeElection/candidateApi.js";
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
import entryElectionIn from './entryElectionIn.vue';
import entryElectionModify from '@/views/ballot/entryElectionModify.vue'
import { right } from "@antv/g2plot/lib/plots/sankey/sankey";
import { instance_1 } from "@/api/axiosRq";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
export default {
  name: "RepresentativeForm",
  // 表
  components: { StandardTable, entryElectionIn, entryElectionModify, SingleSelect,
    SearchForm,
    SingleSearch, },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      selectedRows: [],
      neWid: '',
      //表单状态
      statesList: [],
      //行政区划
      xzqhDmList: [],
      //获取届次
      jcDmList: [],
      //决定单位
      jddwDmList: [],
      // 议案届别
      periods: [
        { id: 16, name: "十六届" },
        { id: 15, name: "十五届" },
      ],
      //行政
      administratives: [
        { id: 0, name: "全部" },
        { id: 1, name: "越秀区" },
        { id: 2, name: "海珠区" },
        { id: 3, name: "荔湾区" },
        { id: 4, name: "天河区" },
        { id: 5, name: "白云区" },
        { id: 6, name: "黄埔区" },
        { id: 7, name: "花都区" },
        { id: 8, name: "番禺区" },
        { id: 9, name: "南沙区" },
        { id: 10, name: "从化区" },
        { id: 11, name: "增城区" },
      ],
      //状态
      status: [
        { id: 1, name: "草稿" },
        { id: 2, name: "退回" },
        { id: 3, name: "待初审" },
        { id: 4, name: "待初审" },
        { id: 5, name: "待初审" },
        { id: 6, name: "待终审" },
        { id: 7, name: "已终审" },
        { id: 8, name: "已归档" },
      ],
      advanced: false,
      queryForm: {
        jcDm: '3',
        xzqhDm: undefined,
        state: undefined,
        userName: '',
        dxqkDm: undefined,
        xjqkDm: undefined,
        jddwDm: undefined,
        creatorOrgId: '',
        sort: 'createTime',
        order: 'descend',
        pageSize: 10,
        pageNum: 1,
      },
      columns: [
        {
          title: "表单状态",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "state",
          sorter: {
            compare: (a, b) => a.state - b.state,
            multiple: 1,
          },
        },
        {
          title: "姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "userName",
        },
        {
          title: "性别",
          align: "center",
          width: 90,
          ellipsis: true,
          dataIndex: "sex",
        },
        {
          title: "预选当选情况",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "electedState",
        },
        {
          title: "预选日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "preElectionTime",
        },
        {
          title: "当选情况",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "preElectedStateName",
        },
        {
          title: "选举情况",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "electionStateName",
        },
        {
          title: "决定单位",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "verifyUnit",
        },
        {
          title: "选举日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "electionDate",
        },
        {
          title: "选举结果",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "electionResult",
        },
        {
          title: "录入人",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "creator",
        },
        {
          title: "录入日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createTime",
        },
        {
          fixed: 'right',
          title: "操作",
          width: 250,
          align: "center",
          scopedSlots: { customRender: 'operation' }
        },
        // {
        //   fixed: 'right',
        //   title: "操作",
        //   width: 250,
        //   align: "center",
        //   customRender: (text, record, index) => {
        //     let h = this.$createElement;
        //     return h("div", [
        //       h(
        //         "span",
        //         {
        //           attrs: {
        //             type: "text",
        //           },
        //           style: {
        //             display: record.state == '已终审' ? 'none' : 'inline',
        //             cursor: "pointer",
        //             marginLeft: "14px",
        //             color: "#1890ff",
        //           },
        //           on: {
        //             click: () => {
        //               this.shenhe(record);
        //             },
        //           },
        //         },
        //         "审核"
        //       ),
        //       h(
        //         "span",
        //         {
        //           attrs: {
        //             type: "text",
        //           },
        //           style: {
        //             display: record.state == '已终审' ? 'inline' : 'none',
        //             cursor: "pointer",
        //             marginLeft: "14px",
        //             color: "#1890ff",
        //           },
        //           on: {
        //             click: () => {
        //               this.chakan(record);
        //             },
        //           },
        //         },
        //         "查看"
        //       ),
        //     ]);
        //   },
        // },
      ],
      dataSource: [
      ],
      selectedRows: [],
      newList: [],
      isRecord: {},
      tableData: [],
      tableKey: [],
      electList: [
        {name: '候选人当选', id: '0'},
        {name: '代表联名', id: '1'},
      ],
      electSituation: [
        {name: '当选', id: '0'},
        {name: '落选', id: '1'},
      ]
    };
  },
  computed: {
    rowSelection () {
      return {
        onChange: (selectedRowKeys, selectedRows) => {
          console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        },
        getCheckboxProps: record => ({
          props: {
            disabled: record.name === 'Disabled User', // Column configuration not to be checked
            name: record.name,
          },
        }),
      };
    },
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "预备人选管理");
    this.$store.dispatch("navigation/breadcrumb2", "代表选举结果");
    this.getgetVerifyUnitList();
    this.getAdministrativeStateList()
    this.getFormStateListFn()
    this.getLevelListFn()
    this.fetchData()
  },
  methods: {
    // 删除
    shanchu (record) {
      var that = this
      //候选人分配表
      let id = record.ID
      this.$confirm({
        title: '警告',
        content: '确定要删除吗？',
        onOk () {
          // gethxrfpbsesentdeleteApi(id).then(res => {
          //   if (res.data.code == '0000') {
          //     that.fetchData()
          //     that.$message.success(res.data.msg)
          //   } else {
          //     that.$message.error(res.data.msg)
          //   }
          // })
        },
        cancelText: '取消',
      });
    },
    //修改
    xiugai (record) {
      //选举结果
      this.isRecord = record
      this.$refs.entryElectionModify.visible = true;
    },
    //审核
    shenhe (record) {
      //选举结果
      this.isRecord = record
      this.newList.push(record)
      this.$refs.entryElectionIn.visible = true;
      this.$refs.entryElectionIn.Istitle = '选举结果'
    },
    //查看
    chakan (record) {
      //选举结果
      console.log(record, "kkkk");
      this.isRecord = record
      this.$refs.entryElectionIn.visible = true;
    },
    //筛选
    onChange (pagination, filters, sorter, extra) {
      console.log(sorter.order, 'sorter');
      if (sorter.order != undefined) {
        this.queryForm.sort = 'state'
        this.queryForm.order = sorter.order
      } else {
        this.queryForm.sort = 'createTime'
        this.queryForm.order = 'descend'
      }
      getdbxjjgqueryApi(this.queryForm).then(res => {
        if (res.data.code == 200) {
          this.dataSource = res.data.rows
          this.pagination.total = res.data.total;
        }
      })
    },
    // 导出
    download () {
      console.log(this.tableKey, this.tableData)
      let ids = [];
      ids = this.tableData.map((i) => i.ID)
      instance_1({
        url: "/hjbd/dbxjjg/export",
        method: "post",
        responseType: "blob",
        params: this.queryForm,
        data: [...ids],
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `录入人选举结果.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      })
    },
    onSelectChange (key, data) {
      console.log(key, 'key');
      this.tableKey = key
      this.tableData = data
    },
    ifhandleClearId () {
      this.isRecord = {}
      this.fetchData()
    },
    //重置
    reset () {
      this.queryForm = {
        jcDm: this.jcDmList[0].jcDm,
        xzqhDm: undefined,
        state: undefined,
        userName: '',
        dxqkDm: undefined,
        xjqkDm: undefined,
        jddwDm: undefined,
        creatorOrgId: '',
        pageSize: 10,
        pageNum: 1,
      }
      this.fetchData()
    },
    //搜索
    search () {
      this.queryForm.pageNum = 1
      this.pagination.current = 1
      this.fetchData()
    },
    //录入
    dengji () {
      this.$refs.entryElectionIn.visible = true
    },
    // 获取数据
    fetchData () {
      this.TBloading = true;
      getdbxjjgqueryApi(this.queryForm).then(res => {
        if (res.data.code == 200) {
          this.dataSource = res.data.rows
          this.pagination.total = res.data.total;
          this.TBloading = false;
        }
      })
    },
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    //获取当前届次下拉数据列表
    async getLevelListFn () {
      const res = await getLevelListApi()
      if (res.data.code === "0000") {
        this.jcDmList = res.data.data
        // console.log(this.periods[0].jcDm, '00'); 
        this.queryForm.jcDm = this.jcDmList[0].jcDm //又改为16届
      }
    },
    //补选单位
    async getAdministrativeStateList () {
      let res = await getAdministrativeStateListApi()
      if (res.data.code == '0000') {
        this.xzqhDmList = res.data.data
      }
    },
    // 决定单位
    async getgetVerifyUnitList () {
      let res = await getgetVerifyUnitListApi()
      if (res.data.code == '0000') {
        this.jddwDmList = res.data.data
      }
    },

    //获取表单状态下拉数据列表
    async getFormStateListFn () {
      const res = await getFormStateListApi()
      if (res.data.code === "0000") {
        // console.log(res, 123);
        this.statesList = res.data.data
      }
    },
  },
};
</script>
<style  >
.RepresentativeForm .ant-form-inline .ant-form-item > .ant-form-item-label {
  min-width: 80px;
  text-align: right;
}
</style>
