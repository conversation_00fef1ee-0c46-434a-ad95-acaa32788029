
import qs from "qs";
import { instance_1, instance_3 } from "@/api/axiosRq";
import request from "@/utils/request";
export function getList(data) {
  return instance_1({
    url: "/meetingIdentity/getIdentityList",
    method: "post",
    data,
  });
}

export function getIdentityTypeList() {
  return instance_1({
    url: "/meetingIdentity/getIdentityTypeList",
    method: "post",
  });
}

export function getAllTypeList() {
  return instance_1({
    url: "/meetingIdentity/getAllIdentityType",
    method: "post",
  });
}

export function getIndentityUserList() {
  return instance_1({
    url: "/meetingIdentity/getIdentityUserList",
    method: "post",
  });
}

export function getRelList(data,params) {
  return instance_1({
    url: "/meetingIdentity/getUserIdentityList",
    method: "post",
    data,
    params: params
  });
}

export function doSave(data) {
  return instance_1({
    url: "/meetingIdentity/addIdentity",
    method: "post",
    data,

  });
}

export function doSaveRel(data) {
  return instance_1({
    url: "/meetingIdentity/addUserIdentity",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return instance_1({
    url: "/api/v1/meetingIdentity/updateIdentity",
    method: "post",
    data,
  });
}

export function doUpdateRel(data) {
  return instance_1({
    url: "/api/v1/meetingIdentity/updateUserIdentity",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return instance_1({
    url: "/meetingIdentity/delIdentity",
    method: "post",
    data,
  });
}

export function doDeleteRel(data) {
  return instance_1({
    url: "/meetingIdentity/delUserIdentity",
    method: "post",
    data,
  });
}

export function getAuthList(param) {
  return instance_1({
    url: "/meetingIdentityAuth/getAuthIdentityList/" + param,
    method: "post",
  });
}

export function doAuth(data) {
  return instance_1({
    url: "/meetingIdentityAuth/addIdentityAuth",
    method: "post",
    data,
  });
}

export function getAllResourceIdentityUser(data) {
  return instance_1({
    url: "/meetingIdentity/getAllResourceIdentityUser",
    method: "post",
    data,
  });
}

export function generateCode(data) {
  return instance_1({
    url: "/meetingIdentity/codeGenerate",
    method: "post",
    data,
  });
}

export function doRelOldData(param, data) {
  return instance_1({
    url: "/meetingIdentity/doRelOldData/" + param,
    method: "post",
    data,
  });

}
export function getListByTypes(data) {
  return instance_1({
    url: "/meetingIdentity/getListByTypes",
    method: "post",
    data,
  });
}
