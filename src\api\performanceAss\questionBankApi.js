import { instance_yajy } from "@/api/axiosRq";
// 办理绩效考核-试题库
// queryForm 请求搜索数据 获取列表数据
export function questionBankList(queryForm) {
  return instance_yajy({
    url: `/api/v1/courses/courses/findPage`,
    method: "post",
    data: queryForm,
  });
}
// 新增试题
export function questionBankSave(addForm) {
  return instance_yajy({
    url: `/api/v1/courses/courses/save`,
    method: "post",
    data: addForm,
  });
}
// 设置为考题
export function setQuestion(form) {
  return instance_yajy({
    url: `/api/v1/courses/courses/setUp`,
    method: "post",
    data: form,
  });
}
// 删除考题
export function delQuestion(Id) {
  return instance_yajy({
    url: `/api/v1/courses/courses/delete`,
    method: "post",
    params: { id: Id },
  });
}
// 修改考题
export function updateQuestion(form) {
  return instance_yajy({
    url: `/api/v1/courses/courses/update`,
    method: "post",
    data: form,
  });
}
// 获取试题管理列表
export function testQuestionsList(form) {
  return instance_yajy({
    url: `/api/v1/courses/item/findPage`,
    method: "post",
    data: form,
  });
}
// 新增试题
export function testQuestionsAdd(form) {
  return instance_yajy({
    url: `/api/v1/courses/item/save`,
    method: "post",
    data: form,
  });
}
// 编辑试题
export function testQuestionsUpData(form) {
  return instance_yajy({
    url: `/api/v1/courses/item/update`,
    method: "post",
    data: form,
  });
}
// 编辑试题
export function testQuestionsDel(Id) {
  return instance_yajy({
    url: `/api/v1/courses/item/delete`,
    method: "post",
    params: { Id },
  });
}
// 获取选项管理列表
export function optionList(form) {
  return instance_yajy({
    url: `/api/v1/courses/option/findPage`,
    method: "post",
    data: form,
  });
}
// 新增选项
export function optionAdd(form) {
  return instance_yajy({
    url: `/api/v1/courses/option/save`,
    method: "post",
    data: form,
  });
}
// 编辑选项
export function optionUpData(form) {
  return instance_yajy({
    url: `/api/v1/courses/option/update`,
    method: "post",
    data: form,
  });
}
// 编辑选项
export function optionDel(Id) {
  return instance_yajy({
    url: `/api/v1/courses/option/delete`,
    method: "post",
    params: { Id },
  });
}
// 预览界面
export function previewFile(Id) {
  return instance_yajy({
    url: `/api/v1/courses/courses/preview`,
    method: "post",
    params: { Id },
  });
}
// 试题提交
export function coursesAnswerSave(form) {
  return instance_yajy({
    url: `/api/v1/courses/coursesAnswer/save`,
    method: "post",
    data: form,
  });
}
// 试题提交 获取答案并预览
export function toCoursesView(form) {
  return instance_yajy({
    url: `/api/v1/courses/courses/toCoursesView`,
    method: "post",
    params: form,
  });
}
