import axios from 'axios'
const instance = axios.create({
    baseURL: this.GLOBAL.requestBasePath,
    timeout: 10000,
    //设置请求头，解决获取不到参数
    // headers:{'Content-Type':'application/x-www-form-urlencoded'},
    headers: {
        "Content-type": "application/json;charset=UTF-8",
      },
})
const instance_2 = axios.create({
  baseURL: this.GLOBAL.basePath_2,
  timeout: 10000,
  //设置请求头，解决获取不到参数
  // headers:{'Content-Type':'application/x-www-form-urlencoded'},
  headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
})
export default{
  instance_2,
  instance
}