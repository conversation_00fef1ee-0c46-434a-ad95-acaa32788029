import { instance_1 } from "@/api/axiosRq";
// 代表出缺列表
export function DbcqbdcxList(params) {
  return instance_1({
    url: `Represent/Dbcqbdcx/select`,
    method: "post",
    params,
  });
}
// 代表出缺保存
export function createSave(data) {
  return instance_1({
    url: `/Represent/create`,
    method: "post",
    data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
// 代表出缺保存表单附件
export function createSaveFj_q(data) {
  return instance_1({
    url: `/Represent/saveFj_q`,
    method: "post",
    data,
    // headers: {
    //   "Content-Type": "multipart/form-data",
    // },
  });
}
// 代表出缺上传
export function dbcqUpload(data) {
  return instance_1({
    url: `/Represent/upload`,
    method: "post",
    data,
  });
}
// 代表出缺文件删除
export function dbcqDeleteFile(params) {
  return instance_1({
    url: `/Represent/deleteFile`,
    method: "delete",
    params,
  });
}
// 代表出缺文件删除
// export function behalfVacantFind(params) {
//   return instance_1({
//     url: `/behalfVacant/find`,
//     method: "get",
//     params,
//   });
// }
// 代表出缺 出缺详情
export function behalfVacantFind(id) {
  return instance_1({
    url: `/Represent/find/${id}`,
    method: "get",
  });
}
// 获取出缺 中选择的代表信息
export function getGrxxfind(params) {
  return instance_1({
    url: `dmcs/dbhj/grxx/find`,
    method: "get",
    params
  });
}
