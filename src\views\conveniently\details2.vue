<template>
<div style="padding: 0 20px;">
    <!-- <ViewTips :tipValue="tipValue" /> -->
    <div style="display: flex;">
      <div class="table-content">
        <!-- <a-collapse-panel
          header="随手拍详情"
          style="
            margin: 0px 0px 40px 0px;
            padding: 10px 0px;
            background: rgba(190, 64, 61, 0.1);
            margin-top: -20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #cfcece;
          "
        /> -->
        <el-container>
          <el-main>
            <a-row :gutter="15">
              <!-- <a-col>
                <a-button
                  style="margin-left: 8px"
                  :disabled="downloadPublicOpinionLoading"
                  @click="downloadWord(dataForm.id)"
                  >导出</a-button
                >
              </a-col> -->
              <a-col
                :xs="24"
                :sm="{ span: 22, offset: 1 }"
                :md="{ span: 22, offset: 1 }"
                :lg="{ span: 22, offset: 1 }"
                :xl="{ span: 22, offset: 1 }"
              >
                <a-form
                  ref="dataForm"
                  :form="dataForm"
                  :label-col="{ span: 15 }"
                  :wrapper-col="{ span: 23 }"
                >
                  <div class="title_all">
                    <span class="title_icon"></span>
                    <a-collapse-panel
                      header="代表基本信息"
                      class="title_style"
                    />
                  </div>
                  <a-row>
                    <!-- <a-col v-if="dataForm.issueNum" :span="8"> -->
                    <a-col :span="8">
                      <a-form-item
                        label="随手拍编号"
                      >
                        <a-input
                          v-model="dataForm.issueNum"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <!-- <a-col v-if="dataForm.proWoCode" :span="8" > -->
                    <a-col :span="8" >
                      <a-form-item
                        label="12345热线事项编号"
                      >
                        <a-input
                          v-model="dataForm.proWoCode"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        label="反映时间"
                        :label-col="{ span: 15 }"
                        :wrapper-col="{ span: 24 }"
                      >
                        <a-input
                          v-model="dataForm.reflectTime"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        label="处理进度"
                      >
                        <a-input
                          v-model="dataForm.processProgress"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        label="代表姓名"
                      >
                        <a-input
                          v-model="dataForm.userName"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        label="代表联系电话"
                        :label-col="{ span: 15 }"
                        :wrapper-col="{ span: 24 }"
                      >
                        <a-input
                          v-model="dataForm.userPhone"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        label="代表驻站联络站站长姓名"
                      >
                        <a-input
                          v-model="dataForm.stationAgentName"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        label="代表驻站联络站站长联系电话"
                      >
                        <a-input
                          v-model="dataForm.stationAgentPhoneNumber"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        label="所反映意见建议所属行政区划"
                        :label-col="{ span: 15 }"
                        :wrapper-col="{ span: 24 }"
                      >
                        <a-input
                          v-model="dataForm.district"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        label="所反映意见建议所属乡镇街道"
                      >
                        <a-input
                          v-model="dataForm.street"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item
                        label="所反映意见建议涉及的地址"
                      >
                        <a-input
                          v-model="dataForm.address"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        label="问题类型"
                        :label-col="{ span: 15 }"
                        :wrapper-col="{ span: 24 }"
                      >
                        <a-input
                          v-model="dataForm.commentCategory"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <!-- <a-col :span="8"  :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
                      <a-form-item label="联络站">
                    <a-input disabled v-model="dataForm.interfaceLocation" autocomplete="off" readonly></a-input>
                  </a-form-item>
                    </a-col> -->
                                    
                    <a-col :span="24">
                      <a-form-item
                        label="涉事主体"
                        :label-col="{ span: 6 }"
                        :wrapper-col="{ span: 24 }"
                      >
                        <a-input
                          v-model="dataForm.subject"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item 
                      label="概括性意见"
                      :label-col="{ span: 9 }"
                      :wrapper-col="{ span: 24 }">
                        <a-textarea
                          v-model="dataForm.generalComments"
                          disabled
                          autocomplete="off"
                          readonly
                          :rows="5"
                        >
                        </a-textarea>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item v-if="imageFujian.length > 0" label="图片文件">
                        <div class="fujian-image">
                          <!-- <div
                        class="image"
                        v-for="(image, index) in imageFujian"
                        :key="index"
                      >
                        {{ image }}
                        <el-image
                          fit="cover"
                          style="width: 100px; height: 100px;"
                          :src="image"
                          :preview-src-list="imageFujian"
                        ></el-image>
                      </div>-->

                          <a-upload
                            ref="uploadPic"
                            disabled
                            :action="action"
                            list-type="picture-card"
                            :file-list="imageFujian"
                            accept="image/*"
                            @preview="handlePreview_1"
                          ></a-upload>
                          <a-modal
                            :visible.sync="dialogVisible_1"
                            :footer="null"
                            width="60%"
                            @cancel="
                              () => {
                                dialogVisible_1 = false;
                              }
                            "
                          >
                            <img width="100%" :src="dialogImageUrl_1" alt />
                          </a-modal>
                        </div>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item v-if="videoFujian.length > 0" label="视频文件">
                        <div v-for="(video, index) in videoFujian" :key="index">
                          <video
                            ref="video"
                            :src="video"
                            :controls="true"
                            class="video-js vjs-big-play-centered vjs-fluid"
                            webkit-playsinline="true"
                            playsinline="true"
                            x-webkit-airplay="allow"
                            x5-playsinline
                            style="height: 200px; width: 100%"
                          ></video>
                        </div>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                  <div class="title_all">
                    <span class="title_icon"></span>
                    <a-collapse-panel
                      header="12345热线回复"
                      class="title_style"
                    />
                  </div>
                  
                        <!-- 
                        :class="[  
                              time.status === 'finished' ? 'finished' : '',  
                              time.status === 'pass' ? 'pass' : ''  
                            ]"
                        -->
                  <div style="padding: 20px 0;">
                    <a-timeline>
                      <a-timeline-item 
                        v-for="(item, index) in replyList"
                        :key="index"
                        :color="item.color">
                          <div 
                            class="icon_title"
                            @click="changeInfo(item, index)"
                            slot="dot">
                            <a-icon style="color: #d92b3e;font-size: 24px;" type="check-circle" />
                          </div>
                          <div class="reply_content">
                            <div class="reply_title">{{item.type}}</div>
                            <div class="reply_info" v-if="item.time">时间：{{item.time}}</div>
                            <div class="reply_info" v-if="item.status">{{item.status}}</div>
                            <div class="reply_info" v-if="item.unit">单位：{{item.unit}}</div>
                            <div class="reply_info" v-if="item.content">回复内容：{{item.content}}</div>
                          </div>
                        </a-timeline-item>
                    </a-timeline>
                  </div>

                  <div class="title_all">
                    <span class="title_icon"></span>
                    <a-collapse-panel
                      header="代表满意度评价"
                      class="title_style"
                    />
                  </div>

                  <div class="evaluate_all" v-for="(item, index) in evaluateList" :key="index">
                    <div class="evaluate_title">第{{index+1}}次评价</div>
                    <a-row>
                      <a-col :span="8" >
                        <a-form-item
                          label="评价结果"
                        >
                          <a-input
                            v-model="item.answer"
                            disabled
                            autocomplete="off"
                            readonly
                          ></a-input>
                        </a-form-item>
                      </a-col>
                      <a-col :span="8" >
                        <a-form-item
                          label="评价时间"
                        >
                          <a-input
                            v-model="item.time"
                            disabled
                            autocomplete="off"
                            readonly
                          ></a-input>
                        </a-form-item>
                      </a-col>
                      <a-col :span="8" v-if="item.content">
                        <a-form-item
                          label="评价内容"
                          :label-col="{ span: 15 }"
                          :wrapper-col="{ span: 24 }"
                        >
                          <a-input
                            v-model="item.content"
                            disabled
                            autocomplete="off"
                            readonly
                          ></a-input>
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <!-- <div class="evaluate_content">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div> -->
                  </div>

                  <div class="title_all">
                      <span class="title_icon"></span>
                      <a-collapse-panel
                        header="录入人信息"
                        class="title_style"
                      />
                    </div>
                  <a-row>
                    <a-col :span="8">
                      <a-form-model-item
                        label="层级"
                        prop="instituLevel"
                        :label-col="{ span: 7 }"
                        :wrapper-col="{ span: 23 }"
                        class="person_info"
                      >
                            <a-input
                              class="input_style"
                              v-model="personInfo1"
                              autocomplete="off"
                              placeholder="请输入处理人信息"
                              disabled
                            ></a-input>
                      </a-form-model-item>
                    </a-col> 
                    <a-col :span="8">
                      <a-form-model-item
                        label="机构"
                        prop="instituLevel"
                        :label-col="{ span: 7 }"
                        :wrapper-col="{ span: 23 }"
                        class="person_info"
                      >
                      <a-input
                        class="input_style"
                        v-model="personInfo2"
                        autocomplete="off"
                        placeholder="请输入处理人信息"
                        disabled
                      ></a-input>
                      </a-form-model-item>
                    </a-col> 
                    <a-col :span="8">
                      <a-form-model-item
                        label="姓名"
                        prop="instituLevel"
                        :label-col="{ span: 15 }"
                        :wrapper-col="{ span: 24 }"
                        class="person_info"
                      >
                        <a-input
                          class="input_style"
                          v-model="personInfo3"
                          autocomplete="off"
                          placeholder="请输入处理人信息"
                          disabled
                        ></a-input>
                      </a-form-model-item>
                    </a-col> 
                  </a-row>
                    
                    
                    <!-- <a-col :span="24">
                      <a-form-item v-if="qitaFujian.length > 0" label="附件">
                        <div
                          v-for="(qita, index) in qitaFujian"
                          :key="index"
                          @click="download(qita.path, qita.fileName)"
                        >
                          <p
                            style="
                              color: #1990ff;
                              cursor: pointer;
                              word-break: break-all;
                            "
                          >
                            {{ qita.fileName }}
                          </p>
                        </div>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item label="处理状态">
                        <a-input
                          v-model="dataForm.progressStatusDesc"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        v-if="dataForm.appraise"
                        label="第一次评价"
                        :label-col="{ span: 6 }"
                        :wrapper-col="{ span: 18 }"
                      >
                        <a-input
                          v-model="dataForm.appraise"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        v-if="dataForm.appraiseTime"
                        label="第一次评价时间"
                        :label-col="{ span: 6 }"
                        :wrapper-col="{ span: 18 }"
                      >
                        <a-input
                          v-model="dataForm.appraiseTime"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item label="不满意原因"    v-if="dataForm.reason">
                        <a-input
                          v-model="dataForm.reason"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item label="自助录入不满意原因"    v-if="dataForm.firstOtherReason">
                        <a-input
                          v-model="dataForm.firstOtherReason"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        v-if="dataForm.secondAppraise"
                        label="第二次评价"
                        :label-col="{ span: 6 }"
                        :wrapper-col="{ span: 18 }"
                      >
                        <a-input
                          v-model="dataForm.secondAppraise"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8" >
                      <a-form-item
                        v-if="dataForm.secondAppraiseTime"
                        label="第二次评价时间"
                        :label-col="{ span: 6 }"
                        :wrapper-col="{ span: 18 }"
                      >
                        <a-input
                          v-model="dataForm.secondAppraiseTime"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item label="第二次不满意原因"    v-if="dataForm.secondReason">
                        <a-input
                          v-model="dataForm.secondReason"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item label="第二次自助录入不满意原因"    v-if="dataForm.secondOtherReason">
                        <a-input
                          v-model="dataForm.secondOtherReason"
                          disabled
                          autocomplete="off"
                          readonly
                        ></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item
                        v-if="dataForm.liaisonOpinion"
                        label="联络站答复"
                      >
                        <a-textarea
                          v-model="dataForm.liaisonOpinion"
                          disabled
                          autocomplete="off"
                          readonly
                          :rows="5"
                        >
                        </a-textarea>
                      </a-form-item>
                    </a-col> -->
    <!--                <a-col :span="24">-->
    <!--                  <a-form-item-->
    <!--                    v-if="dataForm.progressStatus > 2"-->
    <!--                    label="12345状态"-->
    <!--                  >-->
    <!--                    <a-input-->
    <!--                      v-model="dataForm.statusDesc"-->
    <!--                      disabled-->
    <!--                      autocomplete="off"-->
    <!--                      readonly-->
    <!--                    ></a-input>-->
    <!--                  </a-form-item>-->
    <!--                </a-col>-->
                    <!-- <a-col :span="24">
                      <a-form-item v-if="dataForm.replys" label="12345回复">
                        <div v-if="dataForm.replys">
                          <div
                            v-for="(item, index) in dataForm.replys"
                            :key="index"
                          >
                            <p>
                              <span>时间：</span>
                              <span>{{ item.endTime }}</span>
                            </p>
                            <p>
                              <span>单位：</span>
                              <span>{{ item.handleOrgName }}</span>
                            </p>
                            <p v-if="item.operName">
                              <span>审批方式：</span>
                              <span>{{ item.operName }}</span>
                            </p>
                            <p v-if="item.handleResult">
                              <span>回复内容：</span>
                              <span>{{ item.handleResult }}</span>
                            </p>
                            <p v-if="item.images.length > 0 ||item.attachments.length > 0||item.pdfs.length > 0">
                              <span>附件：</span>
                              <a-upload
                                ref="uploadPic"
                                disabled
                                :action="action"
                                list-type="picture-card"
                                :file-list="imageList"
                                accept="image/*"
                                @preview="handlePreview_2"
                              ></a-upload>
                              <a-modal
                                :visible.sync="dialogVisible_2"
                                :footer="null"
                                width="60%"
                                @cancel="
                                () => {
                                  dialogVisible_2 = false;
                                }
                              "
                              >
                                <img width="100%" :src="dialogImageUrl_2" alt />
                              </a-modal>
                            </p>
                            <span v-if="item.pdfs.length > 0">
                              <div
                                v-for="(qita, index) in item.pdfs"
                                :key="index"
                                @click="pdfView(qita.path)"
                              >
                                <p
                                  style="
                                    color: #e5e9f5;
                                    cursor: pointer;
                                    word-break: break-all;
                                  "
                                >
                                  {{ qita.fileName }}
                                </p>
                              </div>
                            </span>
                            <span v-if="item.attachments.length > 0">
                              <div
                                v-for="(qita, index) in item.attachments"
                                :key="index"
                                @click="download(qita.path, qita.fileName)"
                              >
                                <a-upload
                                  v-if="qita.type == 'img'"
                                  ref="uploadPic"
                                  disabled
                                  :action="action"
                                  list-type="picture-card"
                                  :file-list="qita.imgUrlList"
                                  accept="image/*"
                                  @preview="handlePreview_3(qita)"
                                ></a-upload>
                                <p
                                  v-if="qita.type !== 'img'"
                                  style="
                                    color: #1990ff;
                                    cursor: pointer;
                                    word-break: break-all;
                                  "
                                >
                                  {{ qita.fileName }}
                                </p>
                              </div>
                            </span>
                            <a-modal
                              :visible.sync="dialogVisible_3"
                              :footer="null"
                              width="60%"
                              @cancel="
                                () => {
                                  dialogVisible_3 = false;
                                }
                              "
                            >
                              <img width="100%" :src="dialogImageUrl_3" alt />
                            </a-modal>
                            <a-divider style="margin: 0"></a-divider>
                          </div>
                        </div>
                      </a-form-item>
                    </a-col> -->
                  </a-row>
                </a-form>
              </a-col>
            </a-row>
          </el-main>
        </el-container>
      </div>
      <!-- 时间线 -->
      <TimeLine :lineList="lineList" :tab.sync="tab" :titleInfo="titleInfo" :isLink="false" />
    </div>
</div>
</template>

<script>
import { instance_1, instance_2, instance_3 } from "@/api/axiosRq";
import { exportDetail } from "@/api/communityschedule";
import TimeLine from '@/components/TimeLine/index';
import ViewTips from '@/components/ViewTips/index';

import Vue from "vue";
export default {
  name: "TableEdit",
  components: {
    TimeLine,
    ViewTips
  },
  data() {
    return {
      TBloading: false,
      downloadPublicOpinionLoading: false,
      action: process.env.VUE_APP_BASE_API + "/api/v1/meetingFile/fileUpload",
      imageFujian: [],
      NewimageFujian: [],
      videoFujian: [],
      qitaFujian: [],
      imageList: [],
      imgUrlList: [],
      // imgUrl: [],
      dataForm: {
        processProgress: '', // 处理进度
        address: "",
        attachments: [],
        userId: "", //代表ID
        committeeName: "", //来访人信息
        content: "", //反映内容
        id: "",
        openId: "",
        status: 1, //12345状态 1处理中 2处理完毕 3待完善 4已失效
        statusDesc: "",
        progressStatus: 1, //处理状态 1:初始状态 2:联络站处理 3:12345处理 4:结束
        progressStatusDesc: "",
        answer: "", //回复意见-来自12345
        title: "", //反映主题
        liaisonOpinion: "", //联络站答复
        replys: [], //回复意见-来自12345
        appraise:"",
        appraiseTime:"",
        reason:"",
        secondAppraise:"",
        secondAppraiseTime:"",
        secondReason:"",
      },

      dialogImageUrl_1: "",
      dialogVisible_1: false,
      dialogImageUrl_2: "",
      dialogVisible_2: false,
      dialogImageUrl_3: "",
      dialogVisible_3: false,
      lineList: [
        {
          content: '代表提交随手拍',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
          // status: 'finished',
        }, {
          content: '12345热线受理中',
          timestamp: '2018-04-11',
          status: 'unPass',
          type: '审核通过',
          color: '#0bbd87',
          remark: '备注：审核通过'
        }, {
          content: '12345热线已受理',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '承办单位办理',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '12345热线中心复核',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '代表满意度评价',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '工单办结',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }
      ],
      replyList: [
        {
          type: '受理中',
          time: '2024-07-15 12:00:00',
          status: '您的事项已提交',
          unit: '',
          content: ''
        },
        {
          type: '已受理',
          time: '2024-07-15 14:20:29',
          status: '热线管理机构已审核',
          unit: '',
          content: ''
        },
        {
          type: '办理中',
          time: '',
          status: '注:办理过程中,具体承办单位可能会与您电话沟通办理情况。您的事项经由【黄埔区政府】办理',
          unit: '',
          content: ''
        },
        {
          type: '拟交办/转办处理',
          time: '2024-07-15 14:20:29',
          status: '',
          unit: '热线中心_运营',
          content: '请贵单位尽快处理，按处理期限答复市民井将处理结果反馈给我单位。'
        },
        {
          type: '处理完成，转热线管理机构复核',
          time: '2024-07-15 14:20:29',
          status: '',
          unit: '黄埔区政府-南岗街道办事处',
          content: '您好，您反映的建议事项已知悉，谢谢您的宝贵建议。尊敬的人大代表，您好。接收到工单后，我街高度重视'
        },
        {
          type: '审核通过，工单办结',
          time: '2024-07-15 14:20:29',
          status: '',
          unit: '热线中心_运营',
          content: '审核通过，提交到自动回访'
        },
        {
          type: '已评价',
          time: '2024-07-15 14:20:29',
          status: '',
          unit: '',
          content: ''
        },
      ],
      evaluateList: [
        {
          answer: '不满意',
          time: '2024-07-21 10:31:22',
          content: '对承办单位工作人员的态度和效率不满意',
        },
        {
          answer: '满意',
          time: '2024-07-21 10:31:22',
          content: '',
        },
      ],
      personInfo1: '联络站',
      personInfo2: '广州市越秀区北京街道中心联络站',
      personInfo3: '扬*',
      tab: 0,
      titleInfo: '随手拍流程',
      // tipValue: '【测试展示内容】表单标签告诉用户该表单需要填写什么类型的信息，也涉及到对用户输入内容进行校验与反馈，保证用户信息填写的完整度。单域由文本框、密码框、隐藏域、多行文本框、 复选框、单选框、下拉选择框和文件上传框等组成。',
    };
  },
  created() {
    let id = this.$route.query.id;
    if (id) {
      this.acquireData(id);
    }
    this.$store.dispatch("navigation/breadcrumb1", "随手拍");
    this.$store.dispatch("navigation/breadcrumb2", "随手拍详情");
  },
  methods: {
    // 时间线点击
    changeInfo() {

    },
    //返回上一级
    previouspage() {
      this.$router.go(-1);
    },
    // 图片预览
    async handlePreview_1(file) {
      if (!file.url && !file.preview) {
        file.preview = await URL.createObjectURL(file.originFileObj);
      }
      this.dialogImageUrl_1 = file.url || file.preview;
      this.dialogVisible_1 = true;
    },
    async handlePreview_2(file) {
      if (!file.url && !file.preview) {
        file.preview = await URL.createObjectURL(file.originFileObj);
      }
      this.dialogImageUrl_2 = file.url || file.preview;
      this.dialogVisible_2 = true;
    },
    async handlePreview_3(item) {
      console.log("🤗🤗🤗, item =>", item);
      this.dialogImageUrl_3 = "";
      this.dialogVisible_3 = false;
      this.dialogImageUrl_3 = item.imgUrlList[0].url;
      this.dialogVisible_3 = true;
    },
    handlePreviewImg(file) {
      console.log(file, "UINM");
    },
    // 获取详情
    acquireData: function (id) {
      let that = this;

      instance_2({
        method: "get",
        url: "/memberComment/details",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          id: id,
        },
      }).then((data) => {
        that.dataForm = data.data.data;
        console.log(data);

        if (that.dataForm.progressStatus == 1) {
          that.dataForm.progressStatusDesc = "草稿";
        }else if (that.dataForm.progressStatus == 2) {
          that.dataForm.progressStatusDesc = "联络站处理";
        }else  if (that.dataForm.progressStatus == 3) {
          that.dataForm.progressStatusDesc = "12345处理";
        }else if (that.dataForm.progressStatus == 4) {
          that.dataForm.progressStatusDesc = "结束";
        }
        if (that.dataForm.appraise == 1) {
          that.dataForm.appraise = "满意";
        }else if (that.dataForm.appraise == 2) {
          that.dataForm.appraise = "基本满意";
        }else  if (that.dataForm.appraise == 14) {
          that.dataForm.appraise = "不满意";
        }else  if (that.dataForm.appraise == 9) {
          that.dataForm.appraise = "未评价";
        }
        if (that.dataForm.secondAppraise == 1) {
          that.dataForm.secondAppraise = "满意";
        }else if (that.dataForm.secondAppraise == 2) {
          that.dataForm.secondAppraise = "基本满意";
        }else  if (that.dataForm.secondAppraise == 14) {
          that.dataForm.secondAppraise = "不满意";
        }else  if (that.dataForm.secondAppraise == 9) {
          that.dataForm.secondAppraise = "未评价";
        }
        if(that.dataForm.reason){
          that.dataForm.reason =that.reasonfunction(that.dataForm.reason);
        }
        if( that.dataForm.secondReason){
          that.dataForm.secondReason =that.reasonfunction(that.dataForm.secondReason);
        }

        if (this.dataForm.replys) {
          this.dataForm.replys.forEach((item) => {
            if (item.images.length > 0) {
              item.images.forEach((item1) => {
                instance_3({
                  url: "/file/view",
                  method: "get",
                  responseType: "blob",
                  params: { file: item1.path },
                }).then((res) => {
                  const dataInfo = res.data;
                  let reader = new window.FileReader();
                  // 使用readAsArrayBuffer读取文件, result属性中将包含一个 ArrayBuffer 对象以表示所读取文件的数据
                  reader.readAsArrayBuffer(dataInfo);
                  reader.onload = (e) => {
                    const result = e.target.result;
                    let contentType = dataInfo.type;
                    console.log("that.imageFujian--", dataInfo);
                    // 生成blob图片,需要参数(字节数组, 文件类型)
                    const blob = new Blob([result], { type: contentType });
                    // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
                    const url = window.URL.createObjectURL(blob);
                    this.$set(item1, "path", url);
                    this.$set(item1, "url", url);
                    //构造数据显示图片
                    this.imageList.push({
                      uid: item1.path,
                      name: item1.fileName,
                      url,
                    });
                  };
                });
              });
            }
          });
          this.dataForm.replys.forEach((item) => {
            if (item.attachments.length > 0) {
              item.attachments.filter((item) => {
                var index = item.path.lastIndexOf(".");
                var Types = item.path.substr(index + 1);
                if (
                  Types == "jpg" ||
                  Types == "jpeg" ||
                  Types == "png" ||
                  Types == "gif" ||
                  Types == "bmp"
                ) {
                  const url =
                    Vue.prototype.GLOBAL.basePath_1 +
                    "/file/view?file=" +
                    item.path;
                  item.imgUrlList = [];
                  item.imgUrlList.push({
                    status: "done",
                    uid: "-1",
                    name: item.fileName,
                    url,
                  });
                  item.type = "img";
                  item.dialogVisible_3 = false;
                  item.dialogImageUrl_3 = "url";
                }
              });
            }
          });
        }
        if (that.dataForm.attachmentList.length > 0) {
          that.dataForm.attachmentList.forEach((item) => {
            if (item.type == "2") {
              that.qitaFujian.push({
                fileName: item.fileName,
                id: item.id,
                type: "2",
                path: item.path,
              });
            } else {
              this.getFile(item.id, item.type, item);
            }
          });
        }
      });
    },

    getFile: function (id, type, item) {
      let that = this;
      instance_3({
        url: "/file/view",
        method: "get",
        responseType: "blob",
        params: { file: item.path },
      }).then((res) => {
        console.log("🤗🤗🤗xxxxxxxx, res =>", res);
        const dataInfo = res.data;
        let reader = new window.FileReader();
        // 使用readAsArrayBuffer读取文件, result属性中将包含一个 ArrayBuffer 对象以表示所读取文件的数据
        reader.readAsArrayBuffer(dataInfo);
        reader.onload = function (e) {
          const result = e.target.result;
          let contentType = dataInfo.type;

          //判断文件类型
          if (type == 1) {
            contentType = "video/mp4";
          }
          // 生成blob图片,需要参数(字节数组, 文件类型)
          const blob = new Blob([result], { type: contentType });
          // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
          const url = window.URL.createObjectURL(blob);
          if (url != "" && contentType != "video/mp4") {
            //构造数据显示图片
            that.imageFujian.push({
              url: url,
              nameL: item.fileName,
              uid: item.id,
            });
          } else if (contentType == "video/mp4") {
            console.log(url);
            that.videoFujian.push(url);
          }
        };
      });
    },
    reasonfunction(reason){
      if(reason==1){
        return "对承办单位工作人员的态度和效率不满意";
      }else if(reason==2){
        return "对承办单位未回复不满意";
      }else if(reason==3){
        return "对回复结果不满意";
      }else if(reason==4){
        return "问题仍然未解决/改善";
      }else if(reason==5){
        return "建议未被采纳";
      }else if(reason==11){
        return "承办单位回复情况与实际情况不一致";
      }else if(reason==12){
        return "回复结果解答/指引不清晰";
      }else if(reason==13){
        return "按回复指引操作但问题仍未解决";
      }else if(reason==14){
        return "建议采纳但暂未实施";
      }else if(reason==15){
        return "承办单位回复情况与办理结果不一致";
      }else{
        return '对回复结果不满意';
      }
    },

    download(filePath, fileName) {
      //获取文件类型
      var index = fileName.lastIndexOf(".");
      var Type = fileName.substr(index + 1);
      if (Type == "pdf") {
        this.pdfView(filePath);
      } else if (
        Type == "jpg" ||
        Type == "jpeg" ||
        Type == "png" ||
        Type == "gif" ||
        Type == "bmp"
      ) {
        //  this.NewgetFile(filePath,'0')
      } else {
        let that = this;
        instance_2({
          url: "/file/download",
          method: "get",
          responseType: "blob",
          params: { file: filePath, fileName: fileName },
        }).then((res) => {
          window.open(res.request.responseURL);
        });
      }
    },
    pdfView: function (file) {
      console.log("🤗🤗🤗, file =>", file);
      let pdfUrl = Vue.prototype.GLOBAL.basePath_1 + "/file/view?file=" + file;
      window.open("/pdfjs/web/viewer.html?file=" + encodeURIComponent(pdfUrl));
    },
    // 导出 原先
    downloadexcel(id) {
      console.log(id);
      // if (this.selectRows.length == 0) {
      //   return this.$message.warning("请选择数据！");
      // }

      this.downloadPublicOpinionLoading = true;
      exportDetail(id).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        let fileName = "";
        fileName = `群众代表登记表.docx`;
        a.download = fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadPublicOpinionLoading = false;
        }, 1000);
      });
    },
    // 导出压缩包
    downloadWord(id) {
      // console.log(this.dataForm.district+this.dataForm.street+this.dataForm.userName);
      this.downloadPublicOpinionLoading = true;
      instance_1({
        url: "/memberComment/exportWord",
        method: "post",
        responseType: "blob",
        params: { id },
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        let fileName = "";
        fileName =
          this.dataForm.district +
          this.dataForm.street +
          this.dataForm.userName +
          `随手拍意见建议处理详情.zip`;
        a.download = fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadPublicOpinionLoading = false;
        }, 1000);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-main {
  padding-top: 0;
}
.table-content {
  flex: 8;
}
.title_all {
   position: relative;
   margin-left: -15px;
   margin-right: -15px;

  .title_style {
    padding: 10px 0px 0 10px;
    background: rgba(190, 64, 61, 0.1);
    padding-bottom: 12px;
    border-bottom: 1px solid #cfcece;
  }
  .title_icon {
    position: absolute;
    top: 50%;
    transform: translate(0, -6px);
    display: inline-block;
    width: 6px;
    height: 10px;
    background-color: #d92b3e;
  }
}
::v-deep .ant-form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
}
::v-deep .ant-form-item-label {
  text-align: left;
}
::v-deep .ant-select-disabled,
::v-deep .ant-select-disabled .ant-select-selection,
::v-deep .ant-input[disabled],
::v-deep .ant-btn-primary[disabled],
::v-deep .ant-btn-primary[disabled] {
  background-color: #f7f7f9!important;
  background: #f7f7f9!important;
  border-color: #f7f7f9!important;
}
.reply_content {
  padding-left: 10px;
  .reply_title {
    font-size: 16px;
    font-weight: 700;
    color: #333333;
  }
  .reply_info {
    position: relative;
    font-size: 12px;
    color: #97989a;
    padding-left: 15px;
    margin-top: 5px;
  }
  .reply_info::before {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translate(0, -5px);
    content: "";
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #97989a;
  }
}
.evaluate_title {
  font-weight: 700;
  color: #333333;
  padding-top: 10px;
  margin-left: -8px;
}


.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  // font-size: 28px;
  @include add-size($font_size_16);
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.fujian-image {
  display: inline-block;
}

.fujian-image .image {
  padding-right: 15px;
  display: inline-block;
}
</style>
