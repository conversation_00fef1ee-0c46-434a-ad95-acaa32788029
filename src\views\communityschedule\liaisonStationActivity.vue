<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <SearchForm :value="queryForm" @onReset="reset" @onSearch="search">
          <template v-slot:topSearch>
            <AdminStreetStationCascade v-model="queryForm" allow-clear />
          </template>
          <template v-slot:moreSearch>
            <FormPicker
              v-model="queryForm.year"
              label="年度"
              type="year"
              allow-clear
            />

            <FormRangePicker
              v-model="queryForm"
              end-prop="endTime"
              start-prop="startTime"
              label="统计时间"
              allow-clear
            />

            <FormSelect
              v-model="queryForm.statisticType"
              :label="'统计方式'"
              :options="statisticTypeList"
            />
          </template>
        </SearchForm>

        <a-row style="margin: 5px 0px 10px 8px">
          <a-col span="8">
            <!-- <a-button @click="$router.go(-1)"> 返回</a-button> -->
            <a-button
              type="primary"
              style="margin-left: 8px"
              @click="exportExcel"
              >导出</a-button
            >
          </a-col>
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <a-table
            ref="table"
            :bordered="false"
            class="directorySet-table"
            size="small"
            :columns="columns"
            :data-source="list"
            :pagination="false"
            :row-key="
              (record, index) => {
                return index;
              }
            "
            :row-selection="null"
            :scroll="{ x: 300, y: 750 }"
          ></a-table>

          <div style="display: flex; justify-content: end; padding: 12px 0">
            <a-pagination
              :current="pagination.current"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              :show-size-changer="pagination.showSizeChanger"
              :show-quick-jumper="pagination.showQuickJumper"
              :page-size-options="pagination.pageSizeOptions"
              :show-total="pagination.showTotal"
              @showSizeChange="pagination.onShowSizeChange"
              @change="pagination.onChange"
            />
          </div>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import {
  liaisonStationActivityList,
  liaisonStationActivityExport,
} from "@/api/communityschedule";
import SearchForm from "@/components/SearchForm/index";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormSelect from "@/components/FormSelect/index.vue";
import FormRangePicker from "@/components/FormRangePicker/index.vue";
import FormPicker from "@/components/FormPicker/index.vue";
import { sumBy } from "lodash";

export default {
  components: {
    FormPicker,
    FormRangePicker,
    FormSelect,
    AdminStreetStationCascade,
    SearchForm,
  },
  data() {
    return {
      selectedRows: [],
      selectedRowKeys: [],
      list: [],
      listLoading: false,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        year: undefined,
        startTime: undefined,
        endTime: undefined,
        statisticType: 1,
      },
      // 统计方式（1-按镇街数量统计，2-按镇街名称统计，2按联络站名称统计）
      statisticTypeList: [
        { id: 1, name: "按镇街数量统计" },
        { id: 2, name: "按镇街名称统计" },
        { id: 3, name: "按联络站名称统计" },
      ],
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      // 列表
      columns: [],
      columnsGroup: {
        1: [
          {
            title: "序号",
            align: "center",
            width: 100,
            ellipsis: true,
            fixed: "left",
            customRender: (text, record, index) => {
              return index + 1 || "/";
            },
          },
          // {
          //   title: "年/月度",
          //   width: 100,
          //   align: "center",
          //   ellipsis: true,
          //   dataIndex: "度",
          //   customRender: (text, record, index) => {
          //     return text || "/";
          //   },
          // },
          {
            title: "行政区划",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "行政区划",
            fixed: "left",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },

          {
            title: "镇街数量",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "镇街数量",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "人大代表社区联络站数量",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "联络站总数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "开展活动次数",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "活动次数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "应参加活动的各级人大代表总人数",
            width: 180,
            dataIndex: "应参加人数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "实际参加活动的各级人大代表总人数",
            width: 180,
            dataIndex: "实际参加人数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "参加活动的各级人大代表总人次",
            align: "center",
            width: 150,
            dataIndex: "参加人次",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "接待选民群众人次",
            width: 150,
            dataIndex: "接待选民群众人次",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "收集的意见数量",
            width: 150,
            dataIndex: "收集的意见数量",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "办理中",
            width: 150,
            dataIndex: "办理中",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "已办结",
            width: 150,
            dataIndex: "已办结",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "代表评价",
            width: 150,
            children: [
              {
                align: "center",
                title: "办理部门",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "代表办理部门满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "代表办理部门基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "代表办理部门不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
              {
                align: "center",
                title: "办理结果",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "代表办理结果满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "代表办理结果基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "代表办理结果不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
            ],
          },
          {
            align: "center",
            title: "群众评价",
            width: 150,
            children: [
              {
                align: "center",
                title: "办理部门",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "群众办理部门满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "群众办理部门基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "群众办理部门不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
              {
                align: "center",
                title: "办理结果",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "群众办理结果满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "群众办理结果基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "群众办理结果不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
            ],
          },
        ],
        2: [
          {
            title: "序号",
            align: "center",
            width: 100,
            ellipsis: true,
            fixed: "left",
            customRender: (text, record, index) => {
              return index + 1 || "/";
            },
          },
          // {
          //   title: "年/月度",
          //   width: 100,
          //   align: "center",
          //   ellipsis: true,
          //   dataIndex: "度",
          //   customRender: (text, record, index) => {
          //     return text || "/";
          //   },
          // },
          {
            title: "行政区划",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "行政区划",
            fixed: "left",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "镇街名称",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "镇街名称",
            fixed: "left",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "人大代表社区联络站数量",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "联络站总数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "开展活动次数",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "活动次数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "应参加活动的各级人大代表总人数",
            width: 180,
            dataIndex: "应参加人数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "实际参加活动的各级人大代表总人数",
            width: 180,
            dataIndex: "实际参加人数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "参加活动的各级人大代表总人次",
            align: "center",
            width: 150,
            dataIndex: "参加人次",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "接待选民群众人次",
            width: 150,
            dataIndex: "接待选民群众人次",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "收集的意见数量",
            width: 150,
            dataIndex: "收集的意见数量",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "办理中",
            width: 150,
            dataIndex: "办理中",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "已办结",
            width: 150,
            dataIndex: "已办结",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "代表评价",
            width: 150,
            children: [
              {
                align: "center",
                title: "办理部门",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "代表办理部门满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "代表办理部门基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "代表办理部门不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
              {
                align: "center",
                title: "办理结果",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "代表办理结果满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "代表办理结果基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "代表办理结果不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
            ],
          },
          {
            align: "center",
            title: "群众评价",
            width: 150,
            children: [
              {
                align: "center",
                title: "办理部门",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "群众办理部门满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "群众办理部门基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "群众办理部门不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
              {
                align: "center",
                title: "办理结果",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "群众办理结果满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "群众办理结果基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "群众办理结果不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
            ],
          },
        ],
        3: [
          {
            title: "序号",
            align: "center",
            width: 100,
            ellipsis: true,
            fixed: "left",
            customRender: (text, record, index) => {
              return index + 1 || "/";
            },
          },
          {
            title: "行政区划",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "行政区划",
            fixed: "left",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "镇街名称",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "镇街名称",
            fixed: "left",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "联络站名称",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "联络站名称",
            fixed: "left",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "开展活动次数",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "活动次数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "应参加活动的各级人大代表总人数",
            width: 180,
            dataIndex: "应参加人数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "实际参加活动的各级人大代表总人数",
            width: 180,
            dataIndex: "实际参加人数",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "参加活动的各级人大代表总人次",
            align: "center",
            width: 150,
            dataIndex: "参加人次",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "接待选民群众人次",
            width: 150,
            dataIndex: "接待选民群众人次",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "收集的意见数量",
            width: 150,
            dataIndex: "收集的意见数量",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "办理中",
            width: 150,
            dataIndex: "办理中",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "已办结",
            width: 150,
            dataIndex: "已办结",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            align: "center",
            title: "代表评价",
            width: 150,
            children: [
              {
                align: "center",
                title: "办理部门",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "代表办理部门满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "代表办理部门基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "代表办理部门不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
              {
                align: "center",
                title: "办理结果",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "代表办理结果满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "代表办理结果基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "代表办理结果不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
            ],
          },
          {
            align: "center",
            title: "群众评价",
            width: 150,
            children: [
              {
                align: "center",
                title: "办理部门",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "群众办理部门满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "群众办理部门基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "群众办理部门不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
              {
                align: "center",
                title: "办理结果",
                width: 150,
                children: [
                  {
                    align: "center",
                    title: "满意",
                    width: 150,
                    dataIndex: "群众办理结果满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "基本满意",
                    width: 150,
                    dataIndex: "群众办理结果基本满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                  {
                    align: "center",
                    title: "不满意",
                    width: 150,
                    dataIndex: "群众办理结果不满意",
                    customRender: (text, record, index) => {
                      return text || "/";
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 1, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created() {
    const permissions =
      JSON.parse(sessionStorage.getItem("USERID"))?.userIdentityList?.map(
        (it) => it.code
      ) || [];

    if (permissions.includes("I_DBLZ_JDXZ")) {
      this.statisticTypeList.splice(0, 1);
      this.queryForm.statisticType = this.statisticTypeList[0].id;
    }
    console.log();
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "进社区活动");
  },
  methods: {
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    statisticsCol(list) {
      function flatChildren(cols) {
        return cols.map((col) => {
          if (col.children?.length) {
            return flatChildren(col.children);
          }
          return col;
        });
      }

      const flat = flatChildren(this.columns.slice(1)).flat(Infinity);
      const res = flat.map((col, index) => {
        if (col.title === "行政区划") {
          return [col.dataIndex, "合计"];
        }
        if (["镇街名称", "联络站名称"].includes(col.title)) {
          return [col.dataIndex, list.length];
        }
        return [
          col.dataIndex,
          sumBy(list, (it) => Number(it[col.dataIndex] || 0)) || "0",
        ];
      });
      return Object.fromEntries(res);
    },
    //列表
    fetchData() {
      this.listLoading = true;
      liaisonStationActivityList(this.queryForm).then((response) => {
        this.columns = this.columnsGroup[this.queryForm.statisticType];
        this.list = [response.rows, this.statisticsCol(response.rows)].flat();
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    reset(value) {
      this.queryForm = value;
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
    exportExcel() {
      //  var arr = []
      // if (this.selectRows.length != 0) {
      //   // return this.$message.warning("请选择数据！");
      //   arr = this.selectRows.map((item) => item.id)
      // }
      liaisonStationActivityExport({
        ...this.queryForm,
        ids: this.selectedRowKeys.join(),
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `活动情况.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
  },
};
</script>
