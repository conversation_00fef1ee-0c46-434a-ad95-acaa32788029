<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="24"
             :xl="24">
        <a-row class="formBox">
          <a-form-model ref="queryForm"
                        :model="queryForm"
                        :labelCol="{ span: 5 }"
                        :wrapperCol="{ span: 18 }">
            <a-col span="5">
              <a-form-model-item label="届次"
                                 prop="jcName">
                <a-input v-model="queryForm.jcName"
                         placeholder="请选择届次"
                         autocomplete="off"
                         allow-clear
                         v-on:keyup.enter="search"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col span="5">
              <a-form-model-item label="代表身份">
                <a-select placeholder="请选择代表身份"
                          v-model="queryForm.sfmc"
                          allow-clear>
                  <a-select-option v-for="item in sfDmList"
                                   :key="item.name"
                                   :value="item.name">{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="5">
              <a-form-model-item label="代表姓名"
                                 prop="userName">
                <a-input v-model="queryForm.userName"
                         placeholder="请输入代表姓名"
                         autocomplete="off"
                         allow-clear
                         v-on:keyup.enter="search"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item label="年度">
                <a-input v-model="queryForm.year"
                         placeholder="请输入年度"
                         allow-clear
                         auto-complete="off"
                         v-on:keyup.enter="search" />
              </a-form-model-item>
            </a-col>
            <a-col span="4">
              <span style="float: right;  ">
                <!-- <a @click="toggleAdvanced" style="margin-right: 8px;">
            {{ advanced ? "收起" : "高级搜索" }}
            <a-icon :type="advanced ? 'up' : 'down'" />
          </a> -->
                <a-button type="primary"
                          @click="search()">搜索</a-button>
                <a-button style="margin-left: 12px;"
                          @click="reset"
                          class="pinkBoutton">重置</a-button>
              </span>
            </a-col>
          </a-form-model>
        </a-row>
        <!-- <a-row v-if="advanced">
      <a-form-model ref="queryForm" :model="queryForm"
          :labelCol="{span: 5}"
          :wrapperCol="{span: 18}"
        >
        <a-col :span="6" :offset="1">
            <a-form-model-item label="行政区县" prop="administrativeAreaId">
              <a-select
                v-model="queryForm.administrativeAreaId"
                @change="queryAdministrativeAreaChange"
                placeholder="请选择"
              >
                <a-select-option
                  v-for="item in administrativeAreas"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  >{{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="街道乡镇" prop="streetTownId">
              <a-select
                v-model="queryForm.streetTownId"
                placeholder="请选择"
              >
                <a-select-option
                  v-for="item in streetTowns"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  >{{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
        </a-col>
      </a-form-model>
    </a-row> -->
        <!-- table -->
        <a-spin :indicator="indicator"
                :spinning="listLoading">
          <a-table :bordered="false"
                   class="directorySet-table"
                   ref="table"
                   size="small"
                   :columns="columns"
                   :pagination="pagination"
                   :data-source="list"
                   :customRow="clickRow"
                   :rowKey="
              (record, index) => {
                return record.id;
              }
            "
                   :scroll="{ x: 300, y: 0 }"></a-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { instance_1 } from "@/api/axiosRq";
import { getPeriod } from "@/api/election.js";
import { stationMemberList, stationMemberExport, BirthdayList, BirthdayDetails } from "@/api/communityschedule";
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
export default {
  data () {
    return {
      SessionList: [
        { name: "第十五届", key: 1, id: "2" },
        { name: "第十六届", key: 2, id: "3" },
      ],
      openYear: false,
      //list-----
      elementLoadingText: "正在加载...",
      list: [],
      listLoading: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 10,
      background: true,
      //list-----
      timeScope: [
        // 议案届别
        { id: 1, name: "本届" },
        { id: 2, name: "最近三个月" },
        { id: 3, name: "今年1~6月" },
        { id: 4, name: "今年7~12月" },
        { id: 5, name: "今年内" },
        { id: 6, name: "自定义" },
      ],
      sfDmList: [
        { id: 1, name: "全国代表" },
        { id: 2, name: "省代表" },
        { id: 3, name: "市代表" },
        { id: 4, name: "区代表" },
        { id: 5, name: "镇代表" },
      ],
      //行政区域列表
      administrativeAreas: [],
      //街道数据
      streetTowns: [],
      queryForm: {
        startTime: "",
        endTime: "",
        streetTownId: undefined,
        pageNum: 1,
        pageSize: 10,
        timeRange: '',
        year: '',
        sfmc: undefined,
        jcName: ''
      },
      periods: [],
      periodList: [] /* 获取届次 */,
      showDisabled: false,
      advanced: false,
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />, //转动的圈圈
      // 列表
      columns: [
        // {
        //   align: "center",
        //   title: "代表id",
        //   width: 180,
        //   dataIndex: "userId",
        //   customRender: (text, record, index) => {
        //     if (text) {
        //       return text;
        //     } else {
        //       return "/";
        //     }
        //   },
        // },
        {
          title: "年度",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "year",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          align: "center",
          title: "代表姓名",
          width: 120,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          title: "代表身份",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "sfmc",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "届次",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "jcName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "短信内容",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "content",
        },
        {
          title: "生日",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "birthday",

        },
        {
          title: "发送时间",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "sendTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },

      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created () {
    this.selectList();
    this.fetchData();
    this.listAdministrativeAreas();
    this.$store.dispatch("navigation/breadcrumb1", "系统管理");
    this.$store.dispatch("navigation/breadcrumb2", "代表生日祝福");
  },
  methods: {
    //获取列表数据
    async selectList () {
      //届别
      let res = await getPeriod();
      if (res.code == "0000") {
        this.periods = res.data;
        console.log(this.periods);
        // this.queryForm.jcDm = res.data[2].jcDm;
        // this.jcDm = res.data[0].jcDm;
      }
    },
    // 点击行
    clickRow (record) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            this.$router.push({
              path: "/systemIndex/BirthdaywishesDetails",
              query: {
                data: record.id
              },
            });
          },
        },
      };
    },
    // 切换页数
    changePageSize (pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange (pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    /**
     * 预加载数据
     */
    listAdministrativeAreas () {
      // 获取行政区县
      getAdministrativeAreas().then((response) => {
        this.administrativeAreas = response.data;
      });
    },
    liststreetTowns (administrativeAreaId) {
      getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
          this.form.streetTownId = undefined;
          this.form.liaisonStation = "";
        }
      );
    },
    //列表
    fetchData () {
      this.listLoading = true;
      instance_1({
        url: "/birthdayMsg/list",
        method: "get",
        params: this.queryForm
      }).then((response) => {
        this.list = response.data.rows;
        // response.data.rows.forEach((item,index) => {

        // });
        this.total = response.data.total;
        this.pagination.total = response.data.total;
        this.listLoading = false;
      });
    },
    handleSizeChange (val) {
      this.queryForm.size = val;
      this.fetchData();
    },
    handleAdministrativeAreaChange (val) {
      this.form.streetTownId = undefined;
      this.liststreetTowns(val);
    },
    queryAdministrativeAreaChange (val) {
      this.queryForm.streetTownId = undefined;
      this.liststreetTowns(val);
    },
    // 选择街道乡镇
    handlestreetTownChange (val) {
      this.form.liaisonStationId = undefined;
    },
    reset () {
      this.queryForm = {
        startTime: "",
        endTime: "",
        streetTownId: undefined,
        pageNum: 1,
        pageSize: 10,
        year: ''
      };
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData()
    },
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },

    search () {
      this.queryForm.pageNum = 1
      this.queryForm.pageSize = 10
      this.fetchData()
    }
  },
};
</script>
