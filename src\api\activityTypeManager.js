import request from "@/utils/requestTemp";

export function signList(params) {
  return request({
    url: "/activityType/listPage",
    method: "get",
    params
  });
}

export function SignData(params) {
  return request({
    url: "/activityType/findByIdActivity?id=" + params,
    method: "get",
    params
  });
}

export function addSignList(data) {
  return request({
    url: "/activityType/insert",
    method: "post",
    data
  });
}

export function editSignList(data) {
  console.log(data, "修改数据")
  return request({
    url: "/activityType/update",
    method: "POST",
    data
  });
}


export function deleSignList(data) {
  let id = data
  return request({
    url: "activityType/delete?id=" + id,
    method: "GET",
  });
}