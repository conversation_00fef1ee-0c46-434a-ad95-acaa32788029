<template>
  <div class="Table">
    <div class="operation">
      <div class="operation-left">
        <!-- <el-select
              v-model="thNum"
              @change="onJcDmChange"
              placeholder="届次"
              class="table-input"
              style="width: 100px;"
            >
              <el-option
                v-for="item in thNumOptions"
                :key="item.jcDm"
                :label="item.jcmc"
                :value="item.jcDm"
              ></el-option>
            </el-select> -->
        <el-button icon="el-icon-search" type="primary" native-type="submit" @click="handleQuery">高级搜索</el-button>
      </div>
      <div class="operation-right">
        <!-- <el-button
              v-if="showEide"
              type="primary"
              plain
              @click="handleRelOldEide"
              >当前代表信息修改</el-button
            > -->
        <el-button v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])" type="primary" plain @click="handleRelOldData">
          调整代表团</el-button>
        <el-button v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])" type="primary" @click="handleDelete">调入
        </el-button>
        <el-button v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])" type="danger" @click="handleDelete" plain>调出
        </el-button>
        <el-button v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])" icon="el-icon-plus" type="success" circle
          @click="handleAdd"></el-button>
      </div>
    </div>

    <div>
      <el-table ref="meetingTable" v-loading="listLoading" :data="list" border @selection-change="setSelectRows"
        :element-loading-text="elementLoadingText">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="序号" width="80">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column label="行政区划" prop="administrativeAreaName">
          <template slot-scope="scope">
            {{ scope.row.stationAgentJobName }}
          </template>
        </el-table-column>
        <el-table-column label="街道乡镇" width="120" prop="streetTownName">
        </el-table-column>
        <el-table-column label="联络站名称" prop="name"></el-table-column>
        <el-table-column label="是否增设网上联络站" width="80" prop="isAddOnline">
        </el-table-column>
        <el-table-column label="开放时长" width="80" prop="hoursOfService">
        </el-table-column>
        <el-table-column label="录入日期" prop="createTime" width="120"></el-table-column>
        <el-table-column label="操作" width="160px" prop="id">
          <template slot-scope="scope">
            <p class="p">
              <span style="color:#DB3046" @click="Details(scope.row.id)">编辑</span>
              <span style="color:#DB3046" @click="huifu(scope.row.id)">设置</span>
              <span style="color:#DB3046" @click="handleDelete(scope.row)">删除</span>
            </p>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination :background="background" :current-page="queryForm.current" :layout="layout"
        :page-size="queryForm.size" :total="total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange"></el-pagination>
    </div>
  </div>
</template>

<script>
import { instance_2 } from '@/api/axiosRq'
import checkPermission from "@/utils/permission";
export default {
  name: "Table",
  components: {},
  filters: {},
  data() {
    return {
      thNum: '2021',
      badgeHide: true,
      drawer: false,
      listLoading: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 10,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      list: [],
      queryForm: {
        current: 1,
        size: 10,
        title: null,
        state: null,
        type: null,
        startTime: null,
        endTime: null,
      },
      stateOptions: [
        {
          value: 0,
          label: "草稿",
        },
        {
          value: 1,
          label: "发布",
        },
        {
          value: 2,
          label: "归档",
        },
      ],
      typeOptions: [
        {
          value: "立法工作",
          label: "立法工作",
        },
        {
          value: "咨询服务",
          label: "咨询服务",
        },
      ],
      // 限制结束日期不能大于开始日期
      pickerOptions0: {
        disabledDate: (time) => {
          const endDateVal = this.queryForm.endTime;
          if (endDateVal) {
            return time.getTime() >= new Date(endDateVal).getTime();
          }
        },
      },
      pickerOptions1: {
        disabledDate: (time) => {
          const beginDateVal = this.queryForm.startTime;
          if (beginDateVal) {
            return time.getTime() < new Date(beginDateVal).getTime();
          }
        },
      },
    };
  },
  computed: {
    conditionsCount: function () {
      let size = 0;
      for (let key in this.queryForm) {
        if (
          this.queryForm[key] !== undefined &&
          this.queryForm[key] !== null &&
          this.queryForm[key] !== ""
        ) {
          size++;
        }
      }
      const defaultSize = 5;
      this.badgeHide = size - defaultSize <= 0;
      return size - defaultSize;
    },
  },
  created() {
    this.acquireData(this.page, this.size);
  },

  methods: {
    handleAdd() {

    },
    handleDelete() {

    },
    handleRelOldData() {

    },
    handleQuery() {

    },
    onJcDmChange() {

    },
    checkPermission,

    //获取勾选的行数据
    setSelectRows(val) {
      this.selectRows = val;
    },
    //切换页面条数
    handleSizeChange(val) {
      this.queryForm.size = val;
      this.acquireData(this.queryForm.page, val)

      this.fetchData();
    },
    //切换页码
    handleCurrentChange(val) {
      this.queryForm.page = val;
      this.acquireData(val, this.queryForm.size)
      // this.fetchData();
    },
    //搜索
    handleQuery() {
      this.drawer = false;
      this.queryForm.current = 1;
      // this.fetchData();
    },

    //重置搜索参数
    handleClear() {
      if (this.$refs["drawerForm"] !== undefined) {
        this.$refs["drawerForm"].resetFields();
      }
    },

    handleAdd() {
      this.$router
        .push({
          path: "/BBS/BBS_add",
          query: {
            id: null,
            status: 1
          },
        })
        .catch(() => { });
    },

    handleEdit(row) {
      this.$router
        .push({
          path: "/inform/edit",
          query: {
            id: row.id,
          },
        })
        .catch(() => { });
    },
    //回复
    huifu: function (id) {
      this.$router
        .push({
          path: "/BBS/BBS_add",
          query: {
            id: id,
            status: 2
          },
        })
        .catch(() => { });
    },
    //删除
    //     handleDelete(row) {
    //       console.log('ttt',row)
    //       if (row.id) {
    //         this.$baseConfirm("你确定要删除当前项吗", null, () => {
    //      instance_2({
    //       method: "get",
    //       url:"/liaisonStation/del",
    //      headers: {
    //         "Content-type": "application/x-www-form-urlencoded",
    //       },
    //       params:{
    //        id:row.id
    //         }
    //     }).then(data=>{
    //             if (data.data.code == "0000") {
    //               this.$baseMessage("删除成功", "success");
    //              this.acquireData(this.page,this.size);
    //             } else {
    //               this.$baseMessage(data.data.msg, "success");
    //             }
    //           }).catch(error=>{
    //             console.log(error)
    //           })
    //         });
    //       } else {
    //         //批量删除，暂无接口
    //         if (this.selectRows.length > 0) {
    //           const ids = this.selectRows.map((item) => item.id);
    //           this.$baseConfirm("你确定要删除选中项吗", null, () => {});
    //         } else {
    //           this.$baseMessage("未选中任何行", "error");
    //           return false;
    //         }
    //       }
    //     },
    Details(id) {
      // this.$router
      // .push({
      //   path: "/BBS/BBS_Details",
      //   query: {
      //     id: id,
      //   },
      // })
      // .catch(() => {});
    },
    // 获取列表data
    acquireData: function (page, size) {
      instance_2({
        method: "get",
        url: "/liaisonStation/list",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          "current": page,//页码
          "size": size,//条数"
          //         "openId":'openId',
        }
      }).then(data => {
        console.log('联络站', data);
        this.list = data.data.data.records;
        this.total = data.data.data.total
      })
    },

    checkPermission,
  },
}
</script>
<style>
.tableLimit tr td .cell {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  /*可以显示的行数，超出部分用...表示 */
  -webkit-box-orient: vertical !important;
}

.p {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
</style>

<style lang="scss" scoped>
.Table {
  .operation {
    width: 100%;
    padding: 30px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
