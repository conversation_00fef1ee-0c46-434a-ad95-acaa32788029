import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
    return request({
        url: "/api/v1/doctor/getDoctorList",
        method: "post",
        data,
    });
}

export function doSave(data) {
    return request({
        url: "/api/v1/doctor/addDoctor",
        method: "post",
        data,
    });
}

export function doUpdate(data) {
    return request({
        url: "/api/v1/doctor/updateDoctor",
        method: "post",
        data,
    });
}

export function doDelete(param) {
    return request({
        url: "/api/v1/doctor/deleteDoctor/" + param,
        method: "post",
    });
}

export function getUserDoctorList(data) {
    return request({
        url: "/api/v1/doctor/getUserDoctorList",
        method: "post",
        data,
    });
}


export function isDoctor(param) {
    return request({
        url: "/api/v1/doctor/isDoctor/" + param,
        method: "post",
    });
}