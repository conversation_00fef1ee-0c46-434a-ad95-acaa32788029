<template>
  <el-dialog
    :title="accreditTreeDialogTitle"
    :visible.sync="accreditTreeDialogVisible"
    width="30%"
  >
    <el-input placeholder="输入关键字进行过滤" v-model="filterText">
      <el-button slot="append" icon="el-icon-search"></el-button>
    </el-input>
    <el-tree
      ref="accreditTree"
      :data="accreditTreeData"
      :props="accreditTreeProps"
      node-key="id"
      @check-change="accreditCheckChange"
      show-checkbox
      :check-on-click-node="true"
      :default-checked-keys="accreditTreeDefaultKey"
      :default-expanded-keys="accreditTreeExpandedKey"
      :filter-node-method="filterNode"
    ></el-tree>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getAllList as getAccreditList } from "@/api/accredit";
import { getAllList as getRoleList } from "@/api/meetingRole";
import {
  getCalendarAccreditList,
  doSaveCalendarAccredit,
} from "@/api/calendar";

export default {
  data() {
    return {
      filterText: "",
      accreditTreeDialogVisible: false,
      treeCheckedKey: [],
      accreditTreeData: [],
      accreditTreeDefaultKey: [],
      accreditTreeExpandedKey: [],
      accreditTreeProps: {
        children: "children",
        label: "userName",
      },
      isRadio: false,
      calendarId: "",
    };
  },
  props: {
    accreditTreeDialogTitle: {
      type: String,
      default: "选择授权人员",
    },
  },
  created() {
    this.initAccreditTree();
  },
  watch: {
    filterText(val) {
      this.$refs.accreditTree.filter(val);
    },
  },
  methods: {
    initAccreditTree() {
      //获取所有身份
      getRoleList().then((res1) => {
        getAccreditList().then((res2) => {
          res1.data.forEach((role) => {
            let children = [];
            res2.data.forEach((accredit) => {
              if (role.id == accredit.roleId) {
                children.push(accredit);
              }
            });

            this.accreditTreeData.push({
              id: role.id,
              userName: role.roleName,
              children: children,
            });
          });
        });
      });
    },
    open(row) {
      //回选树节点
      if (row) {
        this.calendarId = row.id;
        //清空旧数据
        this.treeCheckedKey = [];
        this.accreditTreeDefaultKey = [];

        getCalendarAccreditList(row.id).then((res) => {
          if (res.data.length > 0) {
            res.data.forEach((accredit) => {
              this.accreditTreeDefaultKey.push(accredit.accreditId);
              this.treeCheckedKey.push(accredit.accreditId);
            });

            if (this.$refs.accreditTree != null) {
              this.$refs.accreditTree.setCheckedKeys(this.treeCheckedKey);
              let checkedNodes = this.$refs.accreditTree.getCheckedNodes(
                false,
                true
              );
              checkedNodes.forEach((item) => (item.expanded = true));
            }
          } else {
            if (this.$refs.accreditTree != null) {
              this.$refs.accreditTree.setCheckedKeys([]);
            }
          }
        });
      }

      this.accreditTreeDialogVisible = true;
    },
    accreditCheckChange(data, checked, indeterminate) {
      if (this.isRadio && checked) {
        this.$refs.accreditTree.setCheckedKeys([data.id]);
      }
    },
    confirm() {
      const that = this;
      let checkedNodes = that.$refs.accreditTree.getCheckedNodes();
      let calendarAccredit = [];
      checkedNodes.forEach((node) => {
        if (node.uniqueId) {
          calendarAccredit.push({
            accreditId: node.id,
          });
        }
      });

      doSaveCalendarAccredit(that.calendarId, calendarAccredit).then((res) => {
        if (res.code == 200) {
          that.$message({
            message: "授权成功",
            type: "success",
          });
        } else {
          that.$message({
            message: "授权出错",
            type: "error",
          });
        }
      });

      this.$refs.accreditTree.setCheckedKeys([]);

      this.treeCheckedKey = [];
      this.accreditTreeDefaultKey = [];

      this.accreditTreeDialogVisible = false;
    },

    close() {
      this.$refs.accreditTree.setCheckedKeys([]);
      this.treeCheckedKey = [];
      this.accreditTreeDefaultKey = [];
      this.accreditTreeDialogVisible = false;
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.userName.indexOf(value) !== -1;
    },

    onSearch() {
      this.$refs.accreditTree.filter(this.filterText);
    },
  },
};
</script>