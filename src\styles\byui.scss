@charset "utf-8";
@import "./transition.scss";
@import "./loading.scss";
@import "./quill.scss";
$base: ".byui";
@mixin scrollbar {
  max-height: 88vh;
  margin-bottom: 0.5vh;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 10px;
    height: 0px;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144, 147, 153, 0.3);
  }
}

@mixin base-scrollbar {
  &::-webkit-scrollbar {
    width: 10px;
    height: 0px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.4);
    background-clip: padding-box;
    border: 3px solid transparent;
    border-radius: 7px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
  &::-webkit-scrollbar-track:hover {
    background-color: #f8fafc;
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  outline: none;
}

#{$base}-clearfix::after {
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
  content: ".";
}

img {
  // object-fit: cover;
}

a {
  color: $base-color-blue;
  text-decoration: none;
  cursor: pointer;
}

html {
  body {
    position: relative;
    height: 100vh;
    padding: 0;
    font-family: Normal;
    margin: 0;
    font-weight: 400;
    background: #f4f4f4;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    @include base-scrollbar;
    svg,
    i {
      &:hover {
        opacity: 0.8;
      }
    }
    /* markdown编辑器开始 */
    .editor-toolbar {
      .no-mobile,
      .fa-question-circle {
        display: none;
      }
    }
    /* markdown编辑器结束 */
    /* 大图展示开始 */
    .el-image-viewer {
      &__close {
        .el-icon-circle-close {
          color: $base-color-white;
        }
      }
    }
    /* 大图展示结束 */
    .app-wrapper {
      .app-main-container {
        @include base-scrollbar;
        > [class*="-container"] {
          padding: 15px;
          background: $base-color-white;
        }
      }
    }
    /* 进度条开始 */
    #nprogress {
      .bar {
        background: $base-color-blue !important;
      }
      .peg {
        box-shadow: 0 0 10px $base-color-blue, 0 0 5px $base-color-blue !important;
      }
    }
    /* 进度条结束 */
    /* 表格开始 */
    .qxj-table {
      th {
        background: #ffffff !important;
        height: 32px;
        line-height: 32px;
        color: #909399 !important;
        font-weight: 500 !important;
      }
    }
    .el-table {
      .el-table__body-wrapper {
        @include base-scrollbar;
      }
      th {
        background: #f5f7fa;
      }
      td,
      th {
        position: relative;
        box-sizing: border-box;
        padding: 7.5px 0;
        text-align: center;
        text-overflow: ellipsis;
        vertical-align: middle;
        .cell {
          overflow: hidden;
          font-size: $base-font-size-default;
          font-weight: normal;
          color: #606266;
          text-overflow: ellipsis;
          white-space: pre-line !important;
          .el-image {
            width: 50px;
            height: 50px;
            border-radius: $base-border-radius;
          }
        }
      }
    }
    /* 表格结束 */
    /* 分页开始 */
    .el-pagination {
      padding: 2px 5px;
      margin: 15px 0 0 0;
      font-weight: normal;
      color: $base-color-black;
      text-align: center;
      white-space: nowrap;
    }
    /* 分页结束 */
    /* 菜单开始 */
    .el-menu.el-menu--popup.el-menu--popup-right-start {
      @include scrollbar;
    }
    .el-menu.el-menu--popup.el-menu--popup-bottom-start {
      @include scrollbar;
    }
    .el-menu--vertical {
      .el-menu-item {
        height: 46px !important;
        line-height: 46px !important;
        &.is-active {
          background-color: $base-menu-background-active !important;
        }
      }
    }
    .side-bar-container .el-menu .el-menu-item.is-active {
      background-color: rgb(0, 17, 33) !important;
    }
    .side-bar-container .nest-menu [class*="menu"].is-active {
      color: #fff !important;
      background-color: rgb(0, 17, 33) !important;
    }
    /* 菜单结束 */
    /* tag开始 */
    .el-tag {
      &--small {
        height: $base-input-height;
        margin-right: 10px;
        line-height: $base-input-height;
        border-radius: $base-border-radius;
      }
    }
    /* tag结束 */
    /* 按钮开始 */
    // .el-button--small {
    //   padding: 8px 20px;
    //   font-size: $base-font-size-default;
    //   border-radius: $base-border-radius;
    //   button:hover::before {
    //     transform: scaleX(1);
    //     transform-origin: center;
    //   }
    // }
    /* 按钮结束 */
    /* 弹窗开始 */
    .el-dialog,
    .el-message-box {
      &__body {
        border-top: 1px solid $base-border-color;
        .el-form {
          padding-right: 30px;
        }
      }
      &__footer {
        padding: $base-padding;
        text-align: right;
        border-top: 1px solid $base-border-color;
      }
      &__content {
        padding: 20px 20px 20px 20px;
      }
    }
    /* 弹窗结束 */
    #{$base}-form-main {
      width: $base-form-width;
      margin-right: auto;
      margin-left: auto;
    }
    #{$base}-pull-left {
      float: left;
    }
    #{$base}-pull-right {
      float: right;
    }
    .el-submenu__title i {
      color: $base-color-white;
    }
    .el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
      color: $base-color-white;
      background-color: $base-color-blue;
      outline: 0;
    }
    /* 卡片开始 */
    .el-card {
      margin-bottom: 15px;
      box-shadow: none;
      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
      &__header {
        padding: $base-padding;
        & > div > span {
          width: 60%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      &__body {
        padding: $base-padding;
      }
    }
    /* 卡片结束 */
    /* 下拉树样式-----------开始 */
    .select-tree-popper {
      .el-scrollbar {
        .el-scrollbar__view {
          .el-select-dropdown__item {
            height: auto;
            max-height: 274px;
            padding: 0;
            overflow-y: auto;
            line-height: 26px;
          }
        }
      }
    }
    /* 下拉树样式-----------结束 */
  }
}

.fk-tree .el-checkbox__input.is-disabled .el-checkbox__inner {
  background-color: #edf2fc;
  border-color: #dcdfe6;
  display: none;
  cursor: not-allowed;
}

.fk-tree .is-disabled {
  margin-right: 0px !important;
}

.fk-tree {
  height: 100%;
  overflow-y: auto;
}

.el-table {
  height: 100%;
}

html body .el-pagination {
  text-align: left;
}

.el-dropdown {
  color: #1890ff;
  cursor: pointer;
}

// .el-dialog {
//   margin-top: 5vh !important;
//   width: 60% !important;
// }

.el-table .cell {
  white-space: pre-line !important;
  word-break: keep-all;
  /*不换行*/
  white-space: nowrap;
  /*不换行*/
  overflow: hidden;
  /*内容超出宽度时隐藏超出部分的内容*/
  text-overflow: ellipsis;
}

.qxj-menu .el-submenu__title {
  // border-bottom: 1px solid #ddd !important
}

.qxj-menu .el-menu-item.is-active {
  background-color: #ffffff !important;
}

.qxj-top .breadcrumb-container .no-redirect {
  color: #ffffff !important;
}

.qxj-top .nav-bar-container .right-panel svg {
  color: #ffffff !important;
}

.thr-top .breadcrumb-container .no-redirect {
  color: #ffffff !important;
}

.thr-top .nav-bar-container .right-panel svg {
  color: #ffffff !important;
}

.index-container .table th,
.index-container .table td {
  text-align: center !important;
}

.qxj-top .nav-bar-container {
  height: initial;
}

.thr-top .nav-bar-container {
  height: initial;
}

.qxj-head-logo {
  margin-left: 40px;
  // margin-top: 8px;
}

.qxj-head-right .right-panel .user-name {
  font-size: 14px !important;
  font-weight: 400 !important;
}

.qxj-user {
  color: #fff !important;
  padding: 0 20px;
}

.br1 {
  border-right: 1px solid #ffffff;
}

// .qxj-menu .el-submenu__title,
// .el-menu-item * {
//   font-weight: 900;
// }
html body .thrTheme .app-main-container > [class*="-container"] {
  padding: 15px;
  background: #fff;
  padding-left: 9px;
  padding-right: 21px;
}

html body .thrTheme .app-main-container .table-container {
  padding: 0px;
  padding-left: 16px;
  padding-right: 18px;
}

.qxj-tags .tags-view-item {
  // display: block !important;
  // height: 26px !important;
  // line-height: 26px !important;
  // width: 105px;
  // text-align: center !important;
  // display: inline-block !important;
  border-radius: 0 !important;
}

.qxj-tags .tags-view-item.router-link-exact-active.router-link-active.active {
  color: #fff;
  background-color: #4990e2 !important;
  border: 1px solid #4990e2 !important;
}

.qxj-main .el-table th {
  background: #ffffff;
}

.thr_main .el-table th {
  background: #ffffff;
}

.qxj-head-ls-lo {
  margin: auto;
}

.qxj-main .tags-view-container .tags-content .tags-view-item .el-icon-close {
  display: initial !important;
}

.thr_main .tags-view-container .tags-content .tags-view-item .el-icon-close {
  display: initial !important;
}

.cp {
  cursor: pointer;
}

.el-table td.is-left,
.el-table th.is-left {
  text-align: left;
}

.el-table td.is-center,
.el-table th.is-center {
  text-align: center;
}

.el-table td.is-right,
.el-table th.is-right {
  text-align: right;
}

.qxj-main .el-submenu__title {
  background-color: #ffffff !important;
  border-top: 1px solid #ddd !important;
}

.qxj-main .el-menu-item {
  background-color: #ffffff !important;
  border-top: 1px solid #ddd !important;
}

.qxj-main .el-menu-item {
  background-color: #ffffff !important;
}

.thr_main .el-submenu__title {
  background-color: #ffffff !important;
  border-top: none !important;
}

.thr_main .el-menu-item {
  background-color: #ffffff !important;
  border-top: none !important;
}

.thr_main .el-menu-item {
  background-color: #ffffff !important;
}

.el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: auto !important;
  height: calc(100vh - 400px) !important;
}

.el-table__fixed-right-patch {
  position: absolute;
  top: -1px;
  right: 0;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.qxj-main .el-table__fixed-right-patch {
  position: absolute;
  top: -1px;
  right: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #ebeef5;
}

.thr_main .el-table__fixed-right-patch {
  position: absolute;
  top: -1px;
  right: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #ebeef5;
}

.sub-form-header {
  text-align: center;
  height: 30px !important;
}

.sub-form-header .back-btn {
  position: absolute;
  left: 35px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  line-height: 28px;
}

.sub-form-header .sub-form-title {
  font-size: 20px;
  color: #303133;
}

.sub-form-header .title-btn {
  float: right;
  margin-left: 10px;
}

.qxj-main .el-button--warning {
  color: #ffc833;
  background: #e8f4ff;
  border-color: #f7ef83;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.qxj-main .el-button--primary {
  color: #1890ff;
  background: #e8f4ff;
  border-color: #a3d3ff;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.qxj-main .el-button--primary:focus,
.qxj-main .el-button--primary:hover {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.qxj-main .tags-view-container {
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12),
    0 0 3px 0 rgba(0, 0, 0, 0.04);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
}

.qxj-main .tags-view-container .tags-content .tags-view-item {
  width: 130px;
  overflow: hidden;
  display: inline-block;
  text-align: center;
}

.qxj-main .tags-view-container .tags-content .tags-view-item {
  word-break: keep-all;
  /* 不换行 */
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 内容超出宽度时隐藏超出部分的内容 */
  text-overflow: ellipsis;
}

.thr_main .el-button--warning {
  color: #ffc833;
  background: #fffee5;
  border-color: #f7ef83;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.thr_main .el-button--warning:focus,
.thr_main .el-button--warning:hover {
  background: #ffc833;
  border-color: #f7ef83;
  color: #fff;
}

.thr_main .el-button--primary {
  color: #3e92ec;
  background: #e8f4ff;
  border-color: #a3d3ff;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.thr_main .el-button--primary:focus,
.thr_main .el-button--primary:hover {
  background: #3e92ec;
  border-color: #3e92ec;
  color: #fff;
}

.thr_main .tags-view-container {
  background: transparent;
  border-bottom: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  margin-left: 10px;
}

.thr_main .tags-view-container .tags-content .tags-view-item {
  width: 130px;
  overflow: hidden;
  display: inline-block;
  text-align: center;
  padding-left: 5px;
}

.thr_main .tags-view-container .tags-content .tags-view-item {
  word-break: keep-all;
  /* 不换行 */
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 内容超出宽度时隐藏超出部分的内容 */
  text-overflow: ellipsis;
}

.thr_main {
  background: #f8f9fd;
}

.thr_main .app-main-container {
  background: transparent !important;
}

.thrTheme .tag-view-show {
  background: transparent !important;
}

.thr_main .index-container {
  // border-radius: 10px;
}

.thr_main .index-container {
  background: transparent !important;
}

.thr_main .gzrd-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  min-height: 47vh;
}

.thr_main .gzrd-card .el-card__header {
  border-bottom: none;
  font-size: 16px;
  font-weight: 600;
  color: #1f2e4c;
}

.thr_main .side-bar-container {
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.thr_main .nest-menu {
  // box-shadow: 2px 0 3px -rgba(0, 0, 0, 0.1) !important;
}

.thr_main .side-bar-container .el-menu .el-menu-item.is-active {
  background-color: #f7f9fc !important;
}

.thr_main .side-bar-container .el-menu .el-menu-item.is-active span,
.thr_main .side-bar-container .el-menu .el-menu-item.is-active svg {
  color: #1c83f1 !important;
  font-family: Normal;
  font-size: 16px;
  font-weight: 500;
}

.thr_main .el-submenu__title {
  font-family: Normal;
  @include add-size($font_size_16);
  font-weight: 500 !important;
}

.thr_main .index-container .table th {
  background: #f8f8fa;
}

.thr_main .tags-view-container svg {
  // margin-top: 24px !important;
  display: none;
}

.thr_main .tags-view-item {
  margin-top: 0 !important;
}

.thr_main .tags-view-container .tags-content {
  float: left;
  width: calc(100% - 22px);
  background: #fff;
  margin-top: 10px;
  border-radius: 10px;
  padding-left: 5px;
  height: 38px;
  // margin-left: 0.3%;
}

.thrTheme .tag-view-show {
  box-shadow: none !important;
}

.thr_main .tags-view-item {
  border-radius: 10px;
}

.thrTheme .tags-view-container .tags-content .tags-view-item.active {
  background-color: #f7f9fc !important;
  border: 1px solid #f7f9fc !important;
  color: #1f2e4c !important;
}

.thrTheme .tags-view-container .tags-content .tags-view-item {
  height: 38px;
  line-height: 38px;
  border: 1px solid #ffffff !important;
  display: block;
  float: left;
}

.thrTheme .el-menu-item {
  font-family: Normal;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.thrTheme .table-container {
  background: transparent !important;
  margin-top: 10px;
}

.thrTheme .table-container .a-col-xl-5 {
  padding-left: 7.5px;
  padding-right: 7.5px;
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 19%;
  margin-right: 1%;
  min-height: 75vh;
}

.thrTheme .table-container .a-col-xl-19 {
  padding-left: 7.5px;
  padding-right: 7.5px;
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: 75vh;
}

.thrTheme .table-container .a-col-xl-24 {
  padding-left: 7.5px;
  padding-right: 7.5px;
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: 75vh;
}

.el-table .normal-row,
.normal-row {
  background: #e1f3d8;
}

.el-table .has-warning-one,
.has-warning-one {
  background: #faecd8;
}

.el-table .has-warning-two,
.has-warning-two {
  background: #fde2e2;
}

.el-table .subject-disabled,
.subject-disabled {
  background: #909399;
  border-color: #909399;
}

.el-table .subject-no-config,
.subject-no-config {
  background: #e9e9eb;
}

.el-table .subject-no-data,
.subject-no-data {
  background: #c6e2ff;
}

.el-tag.subject-disabled {
  color: #fff;
}

.el-tag.subject-no-config {
  color: #909399;
}

.el-table .subject-disabled .cell,
.el-table .subject-no-config .cell {
  color: white;
}

.el-table .subject-disabled:hover .cell,
.el-table .subject-no-config:hover .cell {
  color: #606266;
}

.mb25 {
  margin-bottom: 25px;
}

.ml10 {
  margin-left: 10px;
}

.tagCard .el-tag {
  width: 90px;
  text-align: center;
}

th {
  @include add-size($font_size_16);
}

td {
  @include add-size($font_size_16);
}

.thr_main .con_menu {
  margin: 0 20px;
  height: 74%;
  line-height: 52px;
  background-color: transparent !important;
}

.thr_main .top_menu {
  height: 10px;
  background: #fff;
  border-bottom-right-radius: 10px;
}

.thr_main .bom_menu {
  height: 10px;
  background: #fff;
  border-top-right-radius: 10px;
}

.thrTheme .side-bar-container .el-menu .el-menu-item {
  height: 76px !important;
}

// .thr_menu ul li {
//   padding: 0px;
//   color: rgba(255, 255, 255, 0.95);
//   background-color: rgb(40, 44, 52);
// }
.thrTheme .el-submenu__icon-arrow {
  position: absolute;
  top: 68%;
  right: 20px;
  font-weight: 900;
  margin-top: -7px;
  transition: transform 0.3s;
  font-size: 12px;
}

.thr_main .el-menu li {
  padding: 0 !important;
}

html body .qxj_main .side-bar-container .el-menu .el-menu-item.is-active {
  color: #fff !important;
  background-color: #f9f9f9 !important;
}

.side-bar-container .nest-menu [class*="menu"].is-active {
  color: #fff !important;
  background-color: #000d19 !important;
}

.tree-input input {
  margin: 7px;
  width: 95.5%;
  border-radius: 4px;
  height: 36px !important;
}

.table-input input {
  // margin: 0;
  // width: 97%;
  // border-radius: 4px;
  // height: 36px !important;
}

.thr_main .el-button--danger {
  color: #fff;
  background: #e63725;
  border-color: #e63725;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.thr_main .pt20 .el-input__suffix {
  right: 20px !important;
}

.index-container {
  ::v-deep {
    .el-card__body {
      .echarts {
        width: 100%;
        height: 140px;
      }
    }
  }
  .card {
    ::v-deep {
      .el-card__body {
        .echarts {
          width: 100%;
          height: 305px;
        }
      }
    }
  }
  .card .card-title-word {
    font-size: 17px;
    font-weight: bold;
  }
  .card .card-tip {
    margin-left: 20px;
  }
  .card .card-tip .el-tag {
    color: white;
    padding: 0 6px;
  }
  .table {
    width: 100%;
    color: #666;
    border-collapse: collapse;
    background-color: #fff;
    th,
    td {
      position: relative;
      min-height: 20px;
      padding: 9px 15px;
      font-size: 14px;
      line-height: 20px;
      border-color: #e6e6e6;
      border-style: solid;
      border-width: 1px;
      &:first-child {
        // width: 50%;
        text-align: right;
      }
    }
    th {
      font-weight: bold;
    }
  }
  .circle {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
    background-color: #909399;
  }
  .warning_one {
    background-color: #e6a23c;
  }
  .warning_two {
    background-color: #f56c6c;
  }
  .supervise_one {
    background-color: #dbc64b;
  }
  .supervise_two {
    background-color: #e6a23c;
  }
  .supervise_three {
    background-color: #f56c6c;
  }
}

.mr10 {
  margin-right: 10px;
}

.thr_main .el-table th {
  background-color: #f8f8fa !important;
}

.thr_main .el-table th div.cell {
  font-weight: bold;
  font-size: 16px;
}

.dataCenterRouter {
  background: #4990e2;
  border: none;
  font-size: 12px !important;
  border-radius: 10px !important;
  color: #fff;
}

.dataCenterRouter:hover {
  background: #d2ebff;
  color: #409ef6;
}

.mb10 {
  margin-bottom: 10px;
}

.mt10 {
  margin-top: 10px;
}

.new-btn input {
  height: 45px !important;
  line-height: 45px;
  background: #fff;
  border-radius: 5px !important;
}

.sumbit-btn {
  width: inherit;
  height: 45px;
  border-radius: 5px !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition-delay: 99999s;
  -webkit-transition: color 99999s ease-out, background-color 99999s ease-out;
}

.big-icon .el-icon-circle-close {
  // font-size: 16px;
}

.myx-calendar .el-calendar-day {
  overflow: hidden;
  overflow-y: auto;
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/

.myx-calendar ::-webkit-scrollbar {
  width: 5px;
  background-color: #fff;
}

/*定义滚动条轨道 内阴影+圆角*/

.myx-calendar ::-webkit-scrollbar-track {
  border-radius: 0;
  background-color: #fff;
}

/*定义滑块 内阴影+圆角*/

.myx-calendar ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  width: 5px;
  // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: #bbbbbb;
}

.big-icon .el-icon-circle-close {
  font-size: 28px !important;
  color: #606266 !important;
}
/*定义最上方和最下方的按钮*/

.myx-calendar ::-webkit-scrollbar-button {
  background-color: #fff;
  // border: 1px solid yellow;
}
//某一列加了fixed操作后，固定列出现高度变小的情况，导致显示不全；
.tableClass {
  ::v-deep .el-table__fixed {
    height: 100% !important;
  }
}
// tr,td{
//      height: 80px;
//      overflow: hidden;
//  }
// 重置按钮粉红色
.pinkBoutton{
  color: rgba(212, 48, 48, 1);
  background: rgba(212, 48, 48, 0.1);
  border: 1px solid rgba(212, 48, 48, 1);
}