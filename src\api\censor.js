import request from "@/utils/request";

// 查询文本审核（敏感词管理）列表
export function listCensor(query) {
  return request({
    url: '/api/v1/censor/list',
    method: 'get',
    params: query
  })
}

// 查询文本审核（敏感词管理）详细
export function getCensor(id) {
  return request({
    url: '/api/v1/censor/' + id,
    method: 'get'
  })
}

// 新增文本审核（敏感词管理）
export function addCensor(data) {
  return request({
    url: '/api/v1/censor',
    method: 'post',
    data: data
  })
}

// 修改文本审核（敏感词管理）
export function updateCensor(data) {
  return request({
    url: '/api/v1/censor',
    method: 'put',
    data: data
  })
}

// 删除文本审核（敏感词管理）
export function delCensor(id) {
  return request({
    url: '/api/v1/censor/' + id,
    method: 'delete'
  })
}

// 导出文本审核（敏感词管理）
export function exportCensor(query) {
  return request({
    url: '/api/v1/censor/export',
    method: 'get',
    params: query
  })
}
