<template>
  <div class="table-container">
    <ZTable :list="list"
            :columns="columns" />
  </div>
</template>

<script>
export default {
  data () {
    return {
      TBloading: false,
      list: [
        {
          menu: "代表新增",
          type: "一级菜单",
          description: "	菜单",
          sort: "1",
          createTime: "2021.09.21",
        },
        {
          menu: "手动新建代表",
          type: "二级菜单",
          description: "	菜单",
          sort: "1",
          createTime: "2021.09.21",
        },
        {
          menu: "批量导入代表",
          type: "二级菜单",
          description: "	菜单",
          sort: "1",
          createTime: "2021.09.21",
        },
        {
          menu: "同步新建代表",
          type: "二级菜单",
          description: "	菜单",
          sort: "1",
          createTime: "2021.09.21",
        },
        {
          menu: "代表调整",
          type: "一级菜单",
          description: "	菜单",
          sort: "2",
          createTime: "2021.09.21",
        },
        {
          menu: "代表调入",
          type: "二级菜单",
          description: "	菜单",
          sort: "3",
          createTime: "2021.09.21",
        },
        {
          menu: "代表调出",
          type: "二级菜单",
          description: "	菜单",
          sort: "4",
          createTime: "2021.09.21",
        },
        {
          menu: "调整代表团",
          type: "二级菜单",
          description: "	菜单",
          sort: "5",
          createTime: "2021.09.21",
        },
      ],
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },

        {
          title: "菜单",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "menu",
          customRender: (text, record, index) => {
            return record.menu;
          },
        },
        {
          title: "类型",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "type",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "名称",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "name",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },

        {
          title: "描述",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "description",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "排序",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "sort",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "创建时间",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.switMeeting(record);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleEdit(record);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "系统管理");
    this.$store.dispatch("navigation/breadcrumb2", "分级管理");
  },
};
</script>
<style scoped></style>
