<template>
  <div>
    <a-col :span="spanNum" :class="{ 'overLength': title.length > 5 && spanNum <= 8 }">
      <a-form-model-item :label="title" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-input
          v-model="content"
          :placeholder="'请输入'+ title"
          allow-clear
          @change="search"
          v-on:keyup.enter="handleEnter"
          @input="handleInput($event)"
        />
        <!-- <a-button v-if="showIcon"
                  type="primary"
                  icon="search"
                  @click="onInquire">
        </a-button> -->
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: () => "",
      required: true,
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    value: {
      type: [String, Number],
      default: '',
    },
    // showIcon: {
    //   type: Boolean,
    //   default: () => false,
    // }
  },
  data() {
    return {
      content: "",
    };
  },
  watch: {
    value: {
      handler(val) {
        this.content = val
      },
      deep: true,
      immediate: true,  // 回显值使用
    },
  },
  methods: {
    search() {
      this.$emit("getContent", this.content);
    },
    handleEnter() {
      this.$emit('onEnter', true)
    },
    // onInquire() {
    //   this.$emit('onInquire', true)
    // }
    handleInput() {
      this.$emit('update:value', this.content)
    },
  },
};
</script>

<style lang="scss" scoped>
// ::v-deep .ant-form-item {
//   display: flex;
//   align-items: center;
// }
// ::v-deep .ant-form-item-children {
//   display: flex;
// }
// ::v-deep .ant-btn {
//   width: 25%;
// }
</style>
