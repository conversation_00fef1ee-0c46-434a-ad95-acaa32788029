import { saveAs } from "file-saver";
import axios from "axios";
// import { getAccessToken } from '@/utils/auth'
import { Message } from "element-ui";
import { getAccessToken } from "@/utils/accessToken";
import { log } from "@antv/g2plot/lib/utils";
import Vue from "vue";

// const Vue.prototype.GLOBAL.basePath_1 = process.env.VUE_APP_BASE_API;

export default {
  name(name, isDelete = true) {
    var url =
      Vue.prototype.GLOBAL.basePath_1 +
      "/common/download?fileName=" +
      encodeURI(name) +
      "&delete=" +
      isDelete;
    axios({
      method: "get",
      url: url,
      responseType: "blob",
      headers: { token: getAccessToken() },
    }).then(async (res) => {
      const isLogin = await this.blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data]);
        this.saveAs(blob, decodeURI(res.headers["download-filename"]));
      } else {
        Message.error("无效的会话，或者会话已过期，请重新登录。");
      }
    });
  },
  resource(resource) {
    var url =
      Vue.prototype.GLOBAL.basePath_1 +
      "/common/download/resource?resource=" +
      encodeURI(resource);
    axios({
      method: "get",
      url: url,
      responseType: "blob",
      headers: { token: getAccessToken() },
    }).then(async (res) => {
      const isLogin = await this.blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data]);
        this.saveAs(blob, decodeURI(res.headers["download-filename"]));
      } else {
        Message.error("无效的会话，或者会话已过期，请重新登录。");
      }
    });
  },
  zip(url, name) {
    var url = Vue.prototype.GLOBAL.basePath_1 + url;
    axios({
      method: "get",
      url: url,
      responseType: "blob",
      headers: { token: getAccessToken() },
    }).then(async (res) => {
      const isLogin = await this.blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data], { type: "application/zip" });
        this.saveAs(blob, name);
      } else {
        Message.error("无效的会话，或者会话已过期，请重新登录。");
      }
    });
  },
  saveAs(text, name, opts) {
    saveAs(text, name, opts);
  },
  async blobValidate(data) {
    try {
      const text = await data.text();
      JSON.parse(text);
      return false;
    } catch (error) {
      return true;
    }
  },
};
