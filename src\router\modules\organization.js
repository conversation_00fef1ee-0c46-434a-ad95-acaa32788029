import Layout from "@/layouts";

// {
//   path: "staging",
//   name: "InformStaging",
//   component: () => import("@/views/meeting/testPage/index"),
//   meta: {
//     title: "待办任务",
//     permissions: ["I_XTGLY", "I_XXFBY"],
//   },
// },
// 履职活动组织
export default [
  {
    path: "/dutyActivityIndex",
    component: Layout,
    redirect: "noRedirect",
    name: "dutyActivityIndex",
    title: "履职活动组织",
    meta: {
      title: "首页",
    },
    children: [
      {
        path: "index",
        name: "index",
        component: () =>
          import("@/views/dutyActivityIndex/activityOrganizationIndex.vue"),
        meta: {
          title: "代表首页",
        },
      },
      {
        path: "activityworkIndex",
        name: "activityworkIndex",
        component: () =>
          import("@/views/dutyActivityIndex/activityworkIndex.vue"),
        meta: {
          title: "工作人员首页",
        },
      },
      // {
      //   path: "index",
      //   name: "index",
      //   component: () => import("@/views/dutyActivityIndex/index.vue"),
      //   meta: {
      //     title: "代表首页",
      //   },
      // },
      // {
      //   path: "workIndex",
      //   name: "workIndex",
      //   component: () => import("@/views/dutyActivityIndex/workIndex.vue"),
      //   meta: {
      //     title: "工作人员首页",
      //   },
      // },
    ],
  },

  //活动通知管理
  {
    path: "/toHuoDong",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "履职活动组织",
    meta: {
      title: "活动通知管理",
      icon: "database",
      permissions: [
        "I_XTGLY",
        "I_DBHDGLY",
        "I_QRDGZRY",
        "I_SHDZZGLY",
        "I_QHDZZGLY",
      ],
    },
    alwaysShow: false,
    children: [
      {
        // 原来的代表活动列表
        path: "huoDongList",
        name: "huoDongList",
        component: () => import("@/views/meeting/huoDong/index"),
        meta: {
          title: "新建活动",
          permissions: ["I_XTGLY", "I_DBHDGLY"],
        },
      },
      {
        path: "organizationData1",
        name: "organizationData1",
        component: () => import("@/views/meeting/organizationData/index1"),
        meta: {
          title: "通知提醒管理",
        },
      },
      {
        path: "sendMsgPreConfirm",
        name: "sendMsgPreConfirm",
        component: () => import("@/views/meeting/huoDong/sendMsg/index"),
        meta: {
          title: "短信发送管理",
        },
      },
      {
        path: "sendMsgPreConfirmOper",
        name: "sendMsgPreConfirmOper",
        component: () => import("@/views/meeting/huoDong/sendMsg/operation"),
        meta: {
          title: "短信发送管理-操作详情",
        },
        hidden: true
      },
      {
        path: "sendMsgPreConfirmDetails",
        name: "sendMsgPreConfirmDetails",
        component: () => import("@/views/meeting/huoDong/sendMsg/details"),
        meta: {
          title: "短信发送管理-查看详情",
        },
        hidden: true
      },
      {
        path: "organizationData11",
        name: "organizationData11",
        component: () => import("@/views/meeting/organizationData/index11"),
        meta: {
          title: "通知二维码管理",
        },
      },
      {
        // 原来的区人大代表活动列表
        path: "QUList",
        name: "QUhuoDongList",
        hidden: true,
        component: () => import("@/views/meeting/huoDong/QUindex"),
        meta: {
          title: "区人大所有活动列表",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
        },
      },

      {
        path: "/huoDong/baoming",
        name: "huoDongbaoming",
        component: () => import("@/views/meeting/huoDong/baoming"),
        meta: {
          title: "活动报名统计",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "huoDongAdd",
        name: "huoDongAdd",
        component: () => import("@/views/meeting/huoDong/add"),
        meta: {
          title: "新增活动",
        },
        hidden: true,
      },

      {
        path: "/huoDong/details",
        name: "huoDongdetails",
        component: () => import("@/views/meeting/huoDong/details"),
        meta: {
          title: "活动详情",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      // {
      //   path: "/huoDong/Newedit",
      //   name: "huoDongdetails",
      //   component: () => import("@/views/meeting/huoDong/Newedit"),
      //   meta: {
      //     title: "活动编辑",
      //     permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
      //   },
      //   hidden: true,
      // },
      {
        path: "/huoDong/Newedit",
        name: "huoDongdetails",
        component: () => import("@/views/meeting/huoDong/Newedit"),
        meta: {
          title: "活动编辑",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "newHuodongData",
        name: "huoDongdetails",
        hidden: true,
        component: () => import("@/views/meeting/huoDong/newHuodongData"),
        meta: {
          title: "新的活动新增",
        },
      },
      {
        path: "/huoDong/edit",
        name: "huoDongdetails",
        component: () => import("@/views/meeting/huoDong/edit"),
        meta: {
          title: "活动详情",
          // permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/showStyle",
        name: "huoDongdeShowStyle",
        component: () => import("@/views/meeting/huoDong/showStyle"),
        meta: {
          title: "查看样式",
          // permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      // {
      //   path: "/huoDong/newDetail",
      //   name: "huoDongdetails2",
      //   component: () => import("@/views/meeting/huoDong/newDetail"),
      //   meta: {
      //     title: "活动详情",
      //     // permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
      //   },
      //   hidden: true,
      // },
      {
        path: "/huoDong/newDetail",
        name: "details",
        component: () => import("@/views/meeting/huoDong/newDetail"),
        meta: {
          title: "详情",
          // permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/attendance",
        name: "/huoDong/attendance",
        component: () => import("@/views/meeting/huoDong/attendance"),
        meta: {
          title: "出勤情况",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
        },
        hidden: true,
      },

      // {
      //   path: "organizationData10",
      //   name: "organizationData10",
      //   hidden: true,
      //   component: () => import("@/views/meeting/organizationData/index10"),
      //   meta: {
      //     title: "签到设备管理",
      //   },
      // },


      // {
      //   path: "organizationData2",
      //   name: "organizationData2",
      //   component: () => import("@/views/meeting/organizationData/index2"),
      //   meta: {
      //     title: "通知二维码管理",
      //   },
      // },
    ],
  },


  //活动意向征集
  {
    path: "/collect",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "履职活动组织",
    meta: {
      title: "活动意向征集",
    },
    children: [
      {
        path: "IntentionSolicitation",
        name: "IntentionSolicitation",
        component: () => import("@/views/meeting/IntentionSolicitation/IntentionSolicitationList"),
        meta: {
          title: "意向征集",
        },
      },
      {
        path: "AddIntentionSolicitation",
        name: "AddIntentionSolicitation",
        component: () => import("@/views/meeting/IntentionSolicitation/AddIntentionSolicitation"),
        meta: {
          title: "新意向征集新增",
        },
        hidden: true,
      },
      // {
      //   path: "AddIntentionSolicitation2",
      //   name: "AddIntentionSolicitation2",
      //   component: () => import("@/views/meeting/IntentionSolicitation/AddIntentionSolicitation2"),
      //   meta: {
      //     title: "新意向征集新增2",
      //   },
      //   hidden: true,
      // },
      {
        path: "ShowIntentionSolicitation",
        name: "ShowIntentionSolicitation",
        component: () => import("@/views/meeting/IntentionSolicitation/ShowIntentionSolicitation"),
        meta: {
          title: "查看新意向征集新增2",
        },
        hidden: true,
      },
      {
        path: "IntentionSolicitationStatistics",
        name: "IntentionSolicitationStatistics",
        component: () => import("@/views/meeting/IntentionSolicitation/IntentionSolicitationStatistics"),
        meta: {
          title: "新意向征集统计",
        },
        hidden: true,
      }
    ],
  },
  //活动通知
  {
    path: "/huoDong",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "履职活动组织",
    meta: {
      title: "活动通知",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "add",
        name: "huoDongAdd",
        component: () => import("@/views/meeting/huoDong/add"),
        meta: {
          title: "新增活动",
        },
        hidden: true,
      },

      {
        path: "edit",
        name: "huoDongdetails",
        component: () => import("@/views/meeting/huoDong/edit"),
        meta: {
          title: "修改活动",
          // permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },

      {
        // 原来的代表活动列表
        path: "huoDongList",
        name: "huoDongListTwoå",
        component: () => import("@/views/meeting/huoDong/index"),
        meta: {
          title: "全市活动列表",
          // permissions: ["I_XTGLY", "I_DBHDGLY"],
        },
        // hidden: true,
      },

      {
        path: "attendance",
        name: "/huoDong/attendance",
        component: () => import("@/views/meeting/huoDong/attendance"),
        meta: {
          title: "出勤情况",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "NotStarted",
        name: "NotStarted",
        component: () => import("@/views/meeting/organizationData/NotStarted"),
        meta: {
          title: "尚未开始报名活动",
        },
      },
      {
        path: "Started",
        name: "Started",
        component: () => import("@/views/meeting/organizationData/Started"),
        meta: {
          title: "已结束报名活动",
        },
      },
    ],
  },

  // 活动签到
  {
    path: "/Registration",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "履职活动组织",
    meta: {
      title: "活动签到",
    },
    children: [
      {
        path: "signedIng",
        name: "signedIng",
        component: () => import("@/views/meeting/organizationData/signedIng"),
        meta: {
          title: "正在签到活动",
        },
      },
      {
        path: "dnSigned",
        name: "dnSigned",
        component: () => import("@/views/meeting/organizationData/dnSigned"),
        meta: {
          title: "尚未开始签到活动",
        },
      },
      {
        path: "endSigned",
        name: "endSigned",
        component: () => import("@/views/meeting/organizationData/endSigned"),
        meta: {
          title: "已结束签到活动",
        },
      },
      {
        path: "organizationData9",
        name: "organizationData9",
        hidden: true,
        component: () => import("@/views/meeting/organizationData/index9"),
        meta: {
          title: "签到码展示",
        },
      },
    ],
  },

  // 活动查阅
  {
    path: "/Activities",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "履职活动组织",
    meta: {
      title: "活动查阅",
    },
    children: [
      {
        path: "organizationData12",
        name: "organizationData12",
        component: () => import("@/views/meeting/organizationData/index12"),
        meta: {
          title: "查阅数据",
        },
      },
      {//驾驶舱人事任免
        path: "JSCPersonnelRemovals",
        name: "JSCPersonnelRemovals",
        component: () => import("@/views/jiashicang/addPersonnel"),
        meta: {
          title: "驾驶舱人事任免录入",
        },
      },
      {//驾驶舱人事任免
        path: "listPage",
        name: "listPage",
        component: () => import("@/views/jiashicang/listPage"),
        meta: {
          title: "驾驶舱人事任免列表",
        },
      },
      {
        path: "addSuggestv",
        name: "addSuggestv",
        component: () => import("@/views/meeting/suggesting/addSuggestvQz"),
        meta: {
          title: "录入建议",
        },
      },
      {
        path: "myLeadSuggestion",
        name: "myLeadSuggestion",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/myLeadSuggestionQz"),
        meta: {
          title: "我录入的建议",
        },
      },
    ],
  },

  //统计分析
  {
    path: "/Analyse",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "履职活动组织",
    meta: {
      title: "统计分析",
    },
    children: [
      {
        path: "organizationData13",
        name: "organizationData13",
        component: () => import("@/views/meeting/organizationData/index13"),
        meta: {
          title: "分析数据",
        },
      },
    ],
  },

  //系统管理
  {
    path: "/systemIndexlvzhi",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "履职活动组织",
    meta: {
      title: "系统管理",
      permissions: ["ADMIN", "I_XTGLY"],
    },
    children: [
      {
        path: "activityTypeManager",
        name: "activityTypeManager",
        component: () =>
          import("@/views/meeting/organizationData/activityTypeManager"),
        meta: {
          title: "活动类型管理",
        },
      },
      {
        path: "organizationData15",
        name: "organizationData15",
        component: () => import("@/views/meeting/organizationData/index15"),
        meta: {
          title: "活动积分管理",
        },
      },
      {
        path: "organizationData16",
        name: "organizationData16",
        component: () => import("@/views/meeting/organizationData/index16"),
        meta: {
          title: "活动模板管理",
        },
      },
      {
        path: "activityMsgTemplate",
        name: "activityMsgTemplate",
        component: () =>
          import("@/views/meeting/organizationData/activityMsgTemplate"),
        meta: {
          title: "短信模板管理",
        },
      },
      {
        path: "dataPrivilegeManagement",
        name: "dataPrivilegeManagement",
        component: () =>
          import("@/views/meeting/dataPrivilegeManagement/index"),
        meta: {
          title: "数据权限管理",
        },
      },
      {
        path: "session",
        name: "session",
        component: () => import("@/views/election/systemIndex/electionMage"),
        // component: () => import("@/views/meeting/session/index"),
        meta: {
          title: "届次管理",
        },
      },
      {
        path: "gradingContr",
        name: "gradingContr",
        component: () => import("@/views/meeting/gradingContr/index"),
        meta: {
          title: "分级管理",
        },
      },

      {
        path: "menuManage",
        name: "MenuManage",
        component: () => import("@/views/menu/index"),
        meta: {
          title: "菜单管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "roleManage",
        name: "RoleManage",
        component: () => import("@/views/role/index"),
        meta: {
          title: "角色管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "userManage",
        name: "UserManage",
        component: () => import("@/views/user/index"),
        meta: {
          title: "用户管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },

      {
        path: "editRole",
        name: "EditRole",
        component: () => import("@/views/role/edit"),
        meta: {
          title: "编辑角色",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
      {
        path: "parameterManagement",
        name: "parameterManagement",
        component: () => import("@/views/meeting/parameterManagement/index"),
        meta: {
          title: "参数管理",
        },
      },
    ],
  },
];
