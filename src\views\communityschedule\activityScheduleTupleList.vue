<template>
  <div style="height: 100%">
    <!-- <a-collapse-panel
      header="新增社区民意"
      style="
        margin: 0px 20px 40px 20px;
        padding: 10px 0px;
        background: rgba(190, 64, 61, 0.1);
        padding-bottom: 12px;
        border-bottom: 1px solid #cfcece;
      "
    /> -->
    <div class="content-item">
      <h3>新增社区民意</h3>
    </div>
    <div style="padding: 10px">
      <a-card style="weight: 80%; height: 40%; overflow: auto">
        <collectCommunitySchedule
          @changeSelected="handleSelect"
        ></collectCommunitySchedule>
      </a-card>
    </div>
    <div style="padding: 10px">
      <a-card>
        <collectPublicOpinion
          :communityScheduleId="communityScheduleId"
        ></collectPublicOpinion>
      </a-card>
    </div>
  </div>
</template>

<script>
import collectCommunitySchedule from "./collectCommunitySchedule";
import collectPublicOpinion from "./collectPublicOpinion";
export default {
  components: { collectCommunitySchedule, collectPublicOpinion },
  data() {
    return {
      communityScheduleId: "",
    };
  },
  methods: {
    handleSelect(val) {
      this.communityScheduleId = val;
    },
  },
};
</script>

<style lang="scss" scoped>
.content-item {
  > h3 {
    height: 30px;
    line-height: 30px;
    border-left: 4px solid #c71c33;
    padding-left: 18px;
    font-weight: bold;
    font-size: 18px;
    box-sizing: border-box;
  }

  > div {
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    padding: 20px;
  }
}</style>
