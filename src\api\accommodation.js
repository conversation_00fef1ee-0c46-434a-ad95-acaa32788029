import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/accommodation/getAccommodationList",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/accommodation/addAccommodation",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/accommodation/updateAccommodation",
    method: "post",
    data,
  });
}

export function updateAllocation(data) {
  return request({
    url: "/api/v1/accommodation/updateAllocation",
    method: "post",
    data,
  });
}

export function doDelete(param) {
  return request({
    url: "/api/v1/accommodation/delAccommodation/" + param,
    method: "post",
  });
}

export function downloadExcel(data) {
  return request({
    url: "/api/v1/accommodation/downloadExcel",
    method: "post",
    data,
    responseType: "blob",
  });
}

export function getAccommodationIdUser(data) {
  return request({
    url: "/api/v1/accommodation/getAccommodationIdUser",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function getById(data) {
  return request({
    url: "/api/v1/accommodation/getById",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function doExamine(examineStatus, data) {
  return request({
    url: "/api/v1/accommodation/examine/" + examineStatus,
    method: "post",
    data,
  });
}

export function setPublic(arrangePublic, data) {
  return request({
    url: "/api/v1/accommodation/setPublic/" + arrangePublic,
    method: "post",
    data,
  });
}
//impVehicleInformation
// uploadExcel
