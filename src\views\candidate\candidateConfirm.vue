<template>
  <div class="table-container">
    <!-- <a-row style="margin-left:1%">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18 }">
        <a-col span="6">
          <a-form-model-item label="届次"
                             style="width: 100%;">
            <a-select placeholder="请选择届次"
                      v-model="queryForm.jcDm"
                      allow-clear
                      style="width: 100%;">
              <a-select-option v-for="item in periods"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="表单状态"
                             style="width: 100%;">
            <a-select v-model="queryForm.state"
                      placeholder="请选择表单状态"
                      allow-clear
                      style="width: 100%;">
              <a-select-option v-for="item in STATE"
                               :key="item.dqztDm"
                               :value="item.dqztDm">{{ item.state }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="姓名"
                             prop="name"
                             style="width: 100%;">
            <a-input v-model="queryForm.userName"
                     autocomplete="off"
                     allow-clear
                     placeholder="请输入姓名"
                     v-on:keyup.enter="search"></a-input>
          </a-form-model-item>
        </a-col>
      </a-form-model>
      <div style="float:right">
        <a-button type="primary"
                  @click="search">搜索</a-button>
        <a-button style="margin-left: 12px;"
                  @click="reset"
                  class="pinkBoutton">重置</a-button>
      </div>
    </a-row> -->

    <SearchForm @onReset="reset" @onSearch="search" :noMore="true">
      <template v-slot:topSearch>
        <SingleSelect :title="'届次'" :selectList="periods"  :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm" />
        <SingleSelect :title="'表单状态'" :selectList="STATE"  :showName="'state'" :showValue="'dqztDm'" :value.sync="queryForm.state" />
        <SingleSearch @onEnter="search" :title="'姓名'" :value.sync="queryForm.userName" />
      </template>
    </SearchForm>

    <a-row style="margin: 10px 0px 10px 8px;">
      <a-col :span="6">
        <a-button type="primary"
                  style="margin-left: 12px;"
                  @click="opencandidateConfirmEdit">确认</a-button>
        <a-button type="primary"
                  style="margin-left: 12px;"
                  @click="download">导出</a-button>
      </a-col>
    </a-row>
    <a-row>
      <standard-table :columns="columns"
                      rowKey="ID"
                      :dataSource="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"
                      @change="onChange">

        <div class="operationStyle"
             slot="operation"
             slot-scope="{text, record}">
          <span @click="chakan(record)">查看</span>
          <span @click="xiugai(record)"
                v-if="record.STATE == '草稿'||record.STATE=='退回'">编辑</span>
          <!-- <span @click="shanchu(record)"
                v-if="record.STATE == '草稿'||record.STATE=='退回'">删除</span> -->
          <span @click="shenhe(record)"
                v-if="record.STATE == '待终审'||record.STATE == '待初审'">审核</span>
          <a-dropdown v-if="record.STATE == '草稿'||record.STATE=='退回'">
            <a class="dropdown-link"
               @click="e => e.preventDefault()">
              更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item v-if="record.STATE == '待终审'||record.STATE == '待初审'">
                <a @click="shenhe(record)">审核</a>
              </a-menu-item>
              <a-menu-item @click="shanchu(record)"
                           v-if="record.STATE == '草稿'||record.STATE=='退回'">
                删除</a-menu-item>

            </a-menu>
          </a-dropdown>
        </div>
      </standard-table>
    </a-row>
    <candidateConfirmEdit ref="candidateConfirmEdit"></candidateConfirmEdit>
    <candidateConfirmEcho ref="candidateConfirmEcho"
                          :neWid="neWid"
                          :neWList="neWList"
                          @handleClearId="fetchData">
    </candidateConfirmEcho>
    <candidateConfirmsubmit ref="candidateConfirmsubmit"
                            :neWid="neWid"
                            @handleClearId="fetchData">
    </candidateConfirmsubmit>
    <candidateConfirmmodify ref="candidateConfirmmodify"
                            :neWid="neWid"
                            @handleClearId="fetchData">
    </candidateConfirmmodify>

  </div>
</template>
<script>
import StandardTable from "@/components/table/StandardTable";
import candidateConfirmsubmit from "@/views/candidate/candidateConfirmsubmit";
import candidateConfirmmodify from "@/views/candidate/candidateConfirmmodify"
import candidateConfirmEdit from "./candidateConfirmEdit.vue";
import candidateConfirmEcho from "./candidateConfirmEcho.vue";
import ZTable from "@/components/table/ZTable";
import { myPagination } from "@/mixins/pagination.js";
import { instance_1 } from "@/api/axiosRq";
import { gethxrqrbsdeleteApi } from "@/api/representativeElection/candidateApi.js";
// import StandardTable from "@/components/table/StandardTable";
import {
  getselectApi,
  getLevelListApi,
  getFormStateListApi,
  getCandiDateverifyApi,
} from "@/api/representativeElection/candidateApi.js";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
export default {
  components: { 
    candidateConfirmEdit, 
    ZTable, 
    candidateConfirmEcho, 
    StandardTable, 
    candidateConfirmsubmit, 
    candidateConfirmmodify,
    SingleSelect,
    SearchForm,
    SingleSearch, 
  },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      neWList: [],
      neWid: "",
      STATE: [],
      periods: [
        // 议案届别
        // { id: 16, name: "十五届六次" },
        // { id: 15, name: "十五届一次" },
        // { id: 1, name: "十五届二次" },
        // { id: 2, name: "十五届三次" },
        // { id: 3, name: "十五届四次" },
        // { id: 4, name: "十五届五次" },
      ],
      queryForm: {
        jcDm: 3,
        userName: "",
        state: undefined,
        sort: 'createTime',
        order: 'descend',
        pageSize: 10,
        pageNum: 1,
      },
      columns: [
        {
          title: "届次",
          align: "center",
          width: 160,
          ellipsis: true,
          dataIndex: "LEVEL_NAME",
        },
        {
          title: "表单状态",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "STATE",
          sorter: {
            compare: (a, b) => a.STATE - b.STATE,
            multiple: 1,
          },
          // customRender: (text, record, index) => {
          //   if (text == '00') {
          //     return "已删除"
          //   } else if (text == '11') {
          //     return '草稿'
          //   } else if (text == '12') {
          //     return '退回'
          //   } else if (text == '21') {
          //     return '待初审'
          //   } else if (text == '31') {
          //     return '待终审'
          //   } else if (text == '41') {
          //     return '已终审'
          //   } else if (text == '51') {
          //     return '已归档'
          //   }
          // },
        },
        {
          title: "名单",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "XM",
        },
        {
          title: "录入人",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "CREATOR",
        },
        {
          title: "录入日期",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "CREATE_TIME",
          // customRender: (text, record, index) => {
          //   return text.replace("T", " ").split("Z").join("").substr(0, 19) || "/";
          // },
        },
        {
          fixed: 'right',
          title: "操作",
          width: 230,
          align: "center",
          scopedSlots: { customRender: 'operation' }
        },
        // {
        //   title: "操作",
        //   width: 250,
        //   align: "center",
        //   customRender: (text, record, index) => {
        //     let h = this.$createElement;
        //     return h("div", [
        //       h(
        //         "span",
        //         {
        //           attrs: {
        //             type: "text",
        //           },
        //           style: {
        //             display: record.STATE == '已终审' ? 'none' : 'inline',
        //             cursor: "pointer",
        //             marginLeft: "14px",
        //             color: "#1890ff",
        //           },
        //           on: {
        //             click: () => {
        //               this.shenhe(record);
        //             },
        //           },
        //         },
        //         "审核"
        //       ),
        //       h(
        //         "span",
        //         {
        //           attrs: {
        //             type: "text",
        //           },
        //           style: {
        //             display: record.STATE == '已终审' ? 'inline' : 'none',
        //             cursor: "pointer",
        //             marginLeft: "14px",
        //             color: "#1890ff",
        //           },
        //           on: {
        //             click: () => {
        //               this.chakan(record);
        //             },
        //           },
        //         },
        //         "查看"
        //       ),
        //     ]);
        //   },
        // },
      ],
      dataSource: [],
      selectedRows: [],
      visible: false,
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "候选人管理");
    this.$store.dispatch("navigation/breadcrumb2", "候选人确认");
    this.fetchData();
    this.getLevelListFn();
    this.getFormStateListFn();
  },
  methods: {
    // 导出
    download () {
      instance_1({
        url: "/hxrqrbs/export",
        method: "post",
        responseType: "blob",
        params: this.queryForm
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `候选人确认表.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      })
    },
    //筛选
    onChange (pagination, filters, sorter, extra) {
      console.log(sorter.order, 'sorter');
      if (sorter.order != undefined) {
        this.queryForm.sort = 'state'
        this.queryForm.order = sorter.order
      } else {
        this.queryForm.sort = 'createTime'
        this.queryForm.order = 'descend'
      }
      getCandiDateverifyApi(this.queryForm).then((res) => {
        if (res.data.code == 200) {
          this.dataSource = res.data.rows;
          this.pagination.total = res.data.total;
        }
      });
    },
    // 删除
    shanchu (record) {
      var that = this
      //候选确认表
      let id = record.ID
      this.$confirm({
        title: '警告',
        content: '确定要删除吗？',
        onOk () {
          gethxrqrbsdeleteApi(id).then(res => {
            if (res.data.code == '0000') {
              that.fetchData()
              that.$message.success(res.data.msg)
            } else {
              that.$message.error(res.data.msg)
            }
          })
        },
        cancelText: '取消',
      });

    },
    //审核
    shenhe (record) {
      console.log(record);
      //确认表
      this.neWid = record.ID
      this.$refs.candidateConfirmsubmit.visible = true;
    },
    //修改
    xiugai (record) {
      //确认表
      console.log(record, 'xgai');
      this.neWid = record.ID
      this.$refs.candidateConfirmmodify.visible = true;
    },
    //查看
    chakan (record) {
      this.neWid = record.ID
      this.$refs.candidateConfirmEcho.visible = true;
    },
    //重置
    reset () {
      this.queryForm = {
        jcDm: "3",
        userName: "",
        state: undefined,
        pageSize: 10,
        pageNum: 1,
      };
      this.selectedRows = [];
      this.fetchData();
    },
    //搜索
    search () {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    // 打开
    opencandidateConfirmEdit () {
      this.$refs.candidateConfirmEdit.visible = true;
    },
    // 获取数据
    fetchData () {
      this.TBloading = true;

      getCandiDateverifyApi(this.queryForm).then((res) => {
        if (res.data.code == 200) {
          this.dataSource = res.data.rows;
          this.pagination.total = res.data.total;
          this.TBloading = false;

        }
      });
    },
    //获取表单状态下拉数据列表
    async getFormStateListFn () {
      const res = await getFormStateListApi();
      if (res.data.code === "0000") {
        this.STATE = res.data.data;
      }
    },
    //获取当前届次下拉数据列表
    async getLevelListFn () {
      const res = await getLevelListApi();
      if (res.data.code === "0000") {
        this.periods = res.data.data;
        this.queryForm.jcDm = this.periods[0].jcDm; //改过
        this.fetchData();
      }
    },
  },
};
</script>
<style scoped>
.formBox {
  padding: 10px;
}
</style>
