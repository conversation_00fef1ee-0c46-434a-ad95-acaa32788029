import request from "@/utils/requestTemp";

export function levelRoleItemIdentifyList(params, data) {
  return request({
    url: '/levelRoleItemIdentify/list',
    method: 'post',
    params: params,
    data: data
  })
}

export function addLevelRoleItemIdentify(data) {
  return request({
    url: '/levelRoleItemIdentify/add',
    method: 'post',
    data: data
  })
}
  
export function updateLevelRoleItemIdentify(data) {
  return request({
    url: '/levelRoleItemIdentify/updateById',
    method: 'post',
    data: data
  })
}

export function deleteLevelRoleItemIdentify(params) {
  return request({
    url: '/levelRoleItemIdentify/deleteById',
    method: 'get',
    params: params
  })
}

export function saveItemIdentifyUserList(data) {
  return request({
    url: '/levelRoleItemIdentify/saveUserList',
    method: 'post',
    data: data
  })
}

export function getItemIdentifyUserList(params, data) {
  return request({
    url: '/levelRoleItemIdentify/getUserList',
    method: 'post',
    params: params,
    data: data
  })
}

export function deleteItemIdentifyUserId(params) {
  return request({
    url: '/levelRoleItemIdentify/deleteItemIdentifyUserId',
    method: 'get',
    params: params,
  })
}

export function saveItemIdentifyLayer(data) {
  return request({
    url: '/levelRoleItemIdentify/saveLayer',
    method: 'post',
    data: data
  })
}

export function getById(params) {
  return request({
    url: '/levelRoleItemIdentify/getById',
    method: 'get',
    params: params
  })
}

// 获取角色组列表
export function getLevelRoleGroupList(params) {
  return request({
    url: '/levelRoleGroup/list',
    method: 'post',
    params: params
  })
}
// 新增角色组
export function addLevelRoleGroup(data) {
  return request({
    url: '/levelRoleGroup/add',
    method: 'post',
    data: data
  })
}

export function getLevelRoleGroupItemList(params, data) {
  return request({
    url: '/levelRoleGroupItem/list',
    method: 'post',
    params: params,
    data: data
  })
}
export function saveLevelRoleGroupItemList(data) {
  return request({
    url: '/levelRoleGroupItem/addItems',
    method: 'post',
    data: data
  })
}
export function deleteGroupItem(params) {
  return request({
    url: '/levelRoleGroupItem/delete',
    method: 'post',
    params: params
  })
}

export function getGroupUsers(params, data) {
  return request({
    url: '/levelRoleGroupUser/list',
    method: 'post',
    params: params,
    data: data
  })
}

export function saveGroupUsers(data) {
  return request({
    url: '/levelRoleGroupUser/saveUserList',
    method: 'post',
    data: data
  })
}

export function deleteGroupUser(params) {
  return request({
    url: '/levelRoleGroupUser/deleteById',
    method: 'get',
    params: params
  })
}

export function getUserRecordList(params, data) {
  return request({
    url: '/levelRoleItemIdentifyUserRecord/list',
    method: 'post',
    params: params,
    data: data
  })
}

export function getOrgRecordList(params, data) {
  return request({
    url: '/levelRoleLayerOrgRecord/list',
    method: 'post',
    params: params,
    data: data
  })
}

export function getChangeOrgIds(params) {
  return request({
    url: '/levelRoleLayerOrgRecord/getChangeOrgIds',
    method: 'get',
    params: params,
  })
}
