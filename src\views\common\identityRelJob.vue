<template>
  <a-modal
    :title="title"
    :visible.sync="dialogFormVisible"
    width="1200px"
    @cancel="close"
  >
    <div id="app">
      <a-row class="mt20">
        <a-col :span="24">
          <a-row style="margin-bottom: 15px;">
            <a-col :span="24">
              <a-input
                placeholder="输入关键字进行过滤"
                v-model="filterText"
                @change="onChange"
              ></a-input>
            </a-col>
          </a-row>
          <a-tree
            ref="userTree"
            checkable
            @expand="onExpand"
            :expandedKeys="expandedKeys"
            :replace-fields="replaceFields"
            :tree-data="userTreeData"
            v-model="userTreeDefaultKey"
            @check="onCheck"
          >
            <template slot="title" slot-scope="{ name }">
              <span
                v-if="searchValue && name.indexOf(searchValue) > -1"
                style="color: #f50;"
              >
                {{ name }}
              </span>
              <span v-else>{{ name }}</span>
            </template>
          </a-tree>
        </a-col>
      </a-row>
    </div>
    <div
      slot="footer"
      class="dialog-footer"
      style="position: relative; padding-right: 15px; text-align: right;"
    >
      <el-button type="primary" @click="close">关闭</el-button>
      <el-button
        :loading="loading"
        size="small"
        style="margin-left: 10px;"
        type="success"
        @click="confirm"
      >
        保存
      </el-button>
    </div>
  </a-modal>
</template>
<script>
import {getTreeOrderByXsbh} from "@/api/user";
import checkPermission from "@/utils/permission";

export default {
  data() {
    return {
      tabName: "参加人员",
      loading: false,
      title: "",
      filterText: "",
      dialogFormVisible: false,
      //身份选择参数
      //用户树参数
      userTreeData: [],
      treeCheckedKey: [],
      replaceFields: {
        title: "name",
        key: "uniqueId",
        children: "children",
      },
      checked: [],
      userTreeDefaultKey: [],
      allChildData: [],
      selectedKeys: [],
      // 搜索值
      searchValue: "",
      // 展开的节点
      expandedKeys: [],
      backupsExpandedKeys: [],
      // 是否自动展开
      autoExpandParent: true,
    };
  },
  props: {
    rootOrgId: {
      type: String,
      default: "root",
    },
  },
  created() {
    this.initUserTree();
  },
  methods: {
    // 树状搜索
    onChange(e) {
      const value = e.target.value;
      this.searchValue = value;
      if (value == "") return this.$message.info("请输入关键字");
      this.searchValue = value;
      this.expandedKeys = [];
      this.backupsExpandedKeys = [];
      const candidateKeysList = this.getkeyList(value, this.userTreeData, []);
      candidateKeysList.forEach((item) => {
        const key = this.getParentKey(item, this.userTreeData);
        if (key && !this.backupsExpandedKeys.some((item) => item === key))
          this.backupsExpandedKeys.push(key);
      });
      if (candidateKeysList.length == 0) {
        // 销毁提示
        this.$message.destroy();
        return this.$message.info("没有相关数据");
      }
      const { length } = this.backupsExpandedKeys;
      for (let i = 0; i < length; i++) {
        this.getAllParentKey(this.backupsExpandedKeys[i], this.userTreeData);
      }
      this.expandedKeys = this.backupsExpandedKeys.slice();
    },
    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.name.indexOf(value) > -1) {
          keyList.push(node.uniqueId);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey;
      let temp;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          temp = this.getParentKey(key, node.children);
          if (node.children.some((item) => item.uniqueId === key)) {
            parentKey = node.uniqueId;
          } else if (temp) {
            parentKey = temp;
          }
        }
      }
      return parentKey;
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey;
      if (key) {
        parentKey = this.getParentKey(key, tree);
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey);
          }
          this.getAllParentKey(parentKey, tree);
        }
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },

    // 树状选择
    onCheck(item, data) {
      this.checkedShow = true;
      this.checked = [];
      data.checkedNodesPositions.filter((item1) => {
        this.checked.push(item1.node.componentOptions.propsData.dataRef);
      });
    },
    submitUpload() {},
    handleFilterText() {
      this.$refs.userTree.filter(this.filterText);
    },
    initUserTree() {
      let data = {
        tenantId: this.$tenantId,
        rootOrgId: this.rootOrgId,
      };
      getTreeOrderByXsbh(data).then((res) => {
        this.userTreeData = res.data.children;
      });
    },

    handleHide() {
      this.dialogFormVisible = false;
    },

    handleShow(checkedData) {
      let that = this;
      that.title = "选择发送范围";
      this.tabName = "参加人员";
      that.$forceUpdate();
      that.treeCheckedKey = [];
      that.userTreeDefaultKey = [];
      that.dialogFormVisible = true;

      if (checkedData.length > 0) {
        checkedData.forEach((item) => {
          that.userTreeDefaultKey.push(item);
          that.treeCheckedKey.push(item);
        });
        if (that.$refs.userTree != null) {
          // that.$refs.userTree.setCheckedKeys(this.treeCheckedKey);
          // let checkedNodes = that.$refs.userTree.getCheckedNodes(false, true);
          // checkedNodes.forEach((item) => (item.expanded = true));
        }
      }
    },

    confirm() {
      this.treeCheckedKey = [];
      //回调父页面数据
      this.$parent.saveRelJob(this.checked);
      this.dialogFormVisible = false;
    },


    close() {
      this.dialogFormVisible = false;
    },

    //数组去重
    unique(arr) {
      return Array.from(new Set(arr));
    },
    checkPermission,
  },
};
</script>
<style scoped>
::v-deep .ant-tree {
  max-height: 500px;
  width: 100%;
  overflow-y: auto;
}
</style>
