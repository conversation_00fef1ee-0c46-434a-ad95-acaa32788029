import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/meetingDining/user/getList",
    method: "post",
    data,
    headers: {
        "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
        function(data) {
            return qs.stringify(data);
        },
    ],
  });
}

export function doSave(applyId, data) {
  return request({
    url: "/api/v1/meetingDining/user/save/" + applyId,
    method: "post",
    data,
  });
}
