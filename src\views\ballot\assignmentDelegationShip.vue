<template>
  <!-- <div v-if="this.currentActivity.id != ''"> -->
  <div>
    <a-modal
      title="分配代表团表单审核"
      :model="addForm"
      :visible.sync="visible"
      width="50%"
      @cancel="close"
    >
      <a-row class="liucheng lcmcs" style="padding-left: 5px;">
        <a-col span="4" class="lcmcsss">流程名称</a-col>
        <a-col span="20">{{ processDefinition.name }}</a-col>
      </a-row>
      <a-row class="dqjc">
        <a-col span="4" class="lcmcsss">当前进度</a-col>
        <a-col span="8" class="lcmcsss">{{ currentActivity.name }}</a-col>
        <a-col span="4" class="lcmcsss">上一节点</a-col>
        <a-col span="8">
          <span>{{ prevActivity.name }}</span>
        </a-col>
      </a-row>
      <a-row class="xbcl" style="padding-left: 5px;">
        <a-col span="4" class="lcmcsss">下步处理</a-col>
        <a-col span="20">
          <a-radio-group
            v-for="(item, index) in nextActivities"
            @change="onChange"
            :key="item.id + index"
            v-model="nextActivityId"
          >
            <a-radio :value="item">{{ item.name }}</a-radio>
          </a-radio-group>
        </a-col>
      </a-row>
      <a-row class="liucheng cyren">
        <a-col span="12" class="cyrss" style="height: 300px;">
          <a-tabs
            type="card"
            default-active-key="activeKey"
            @change="callback"
            style="user-select: none;"
          >
            <a-tab-pane key="1" tab="常用人" :style="keys == '1' ? cy : ''">
              <a-radio-group
                v-model="value"
                v-for="item in lists"
                :key="item.posId"
                @change="isChange"
              >
                <a-radio :style="radioStyle" :value="item.posId">
                  {{ item.assignee.fullName }}，{{ item.assignee.posName }}
                </a-radio>
              </a-radio-group>
            </a-tab-pane>
            <a-tab-pane
              key="2"
              tab="机构人员"
              force-render
              :style="keys == '2' ? jg : ''"
            >
              <a-tree
                :disable-branch-nodes="true"
                v-model="reviewOrgId"
                show-checkbox
                checkable
                ref="tree"
                :check-strictly="true"
                :replace-fields="radioStyle"
                :tree-data="treeData"
                @check="onSelect"
              >
                <template slot="title" slot-scope="{ fullName }">
                  <span>{{ fullName }}</span>
                </template>
              </a-tree>
            </a-tab-pane>
            <a-tab-pane
              key="3"
              tab="候选人"
              force-render
              :style="keys == '3' ? hx : ''"
            >
              <a-radio-group
                v-model="hxrvalue"
                v-for="item in hxrList"
                :key="item.userId"
                @change="hxrChange"
              >
                <!-- @change="isChange" -->
                <a-radio :style="radioStyle" :value="item.userId"
                  >{{ item.realName }}，{{ item.deptName }}</a-radio
                >
              </a-radio-group>
            </a-tab-pane>
          </a-tabs>
        </a-col>
        <a-col span="12" style="margin-top: 75px; padding-left: 15px;">
          <a-radio-group
            v-model="value"
            v-for="item in lists"
            :key="item.posId"
          >
            <span
              v-show="value == item.posId"
              :style="radioStyle"
              :value="item.posId"
              @change="qwe"
              >{{ item.assignee.fullName }}，{{ item.assignee.posName }}</span
            >
          </a-radio-group>

          <a-radio-group v-model="isReviewOrgId">
            <span v-show="isReviewOrgId.fullName != ''">
              {{ isReviewOrgId.fullName }},{{ isReviewOrgId.posName }}
            </span>
          </a-radio-group>

          <a-radio-group
            v-model="hxrvalue"
            v-for="item in hxrList"
            :key="item.userId"
          >
            <span
              @change="qwe"
              v-show="hxrvalue == item.userId"
              :value="item.userId"
              >{{ item.realName }},{{ item.deptName }}</span
            >
          </a-radio-group>
        </a-col>
      </a-row>
      <a-row class="liucheng ssyj">
        <a-col span="4" style="padding: 5px 0 0 5px;"><span class="">送审意见</span></a-col>
        <a-col span="20" style="padding: 8px 15px 0 0;">
          <a-form-model-item>   
            <a-input style="height: 100px;" v-model="comment" type="textarea" />
          </a-form-model-item>  
        </a-col>
      </a-row>
      <a-row class="xxts" style="padding: 8px 0;">
        <a-col span="4" style="padding-left: 5px;">提醒信息</a-col>
        <a-col span="12">
          <a-form-model-item>
            <a-input
              v-model="message"
              style="width: 400px; height: 100px;"
              type="textarea"
            />
          </a-form-model-item>
        </a-col>

        <a-col span="8" style="margin-top: 20px;">
          <a-checkbox v-model="isSendSMS" value="sendSMS" @change="asd"
            >发送短信提醒</a-checkbox
          >
        </a-col>
      </a-row>
      <a-row slot="footer">
        <a-col :span="8" push="8">
          <a-space>
            <a-button @click="save"> <a-icon type="check" />发送 </a-button>
            <a-button @click="isclose"> <a-icon type="close" />取消 </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>

<script>
//树形控件
import {
  getprocessApi,
  gethistoryAssigneesApi,
  getfindApprovableODUApi,
  behalfVacantcompleteApi,
  getgetNextUsersApi,
  fpdbtsComplete,
} from "@/api/representativeElection/candidateApi.js";
import { log } from "@antv/g2plot/lib/utils";

export default {
  props: ["procInstId", "ids"],
  data() {
    return {
      activeKey: "3",
      isSendSMS: false,
      visible: false,
      showIcon: false,
      showLine: true,
      threalName: "",
      childrenName: "",
      // 上一节点
      prevActivity: {},
      //流程名称
      processDefinition: {},
      //当前进度
      currentActivity: {},
      // 下步处理
      nextActivities: [],
      lists: [],
      treelist: [],
      treeData: [],
      value: 0,
      hxrvalue: "",
      duanxing: 1,
      hx: {
        height: "240px",
        overflow: "auto",
        paddingLeft: "5px",
      },
      jg: {
        height: "240px",
        overflow: "auto",
        paddingLeft: "5px",
      },
      cy: {
        height: "240px",
        overflow: "auto",
        paddingLeft: "5px",
      },
      radioStyle: {
        display: "block",
        height: "30px",
        lineHeight: "30px",
        title: "fullName",
        key: "id",
      },
      keys: "",
      hxrList: [],
      taskDefinitionKey: "",
      taskId: "",
      addForm: {},
      sendSMS: "",
      message: "",
      comment: "",
      flowNo: "",
      nextActivityId: "",
      procDefKey: "",
      assignee: "",
      currentActivityId: "",
      reviewOrgId: [],
      isReviewOrgId: {},
    };
  },

  created() {
    // console.log(this.ids);
    console.log(this.$forceUpdate);
  },
  computed: {},

  methods: {
    qwe(e) {},
    //关闭窗口
    close() {
      this.visible = false;
    },
    //关闭
    isclose() {
      this.visible = false;
    },

    asd(val) {},
    //预备人选基本情况申报送审
    async getprocessFn() {
      if (this.procInstId != "") {
        let res = await getprocessApi(this.procInstId);
        console.log(res);
        if (res.data.code == "0000") {
          if (res.data.data != null) {
            // this.prevActivity = res.data.data.prevActivity
            this.processDefinition = res.data.data.processDefinition;
            this.currentActivity = res.data.data.currentActivity;
            this.nextActivities = res.data.data.nextActivities;
            this.taskId = res.data.data.taskId;
            console.log(this.processDefinition.id);

            console.log(this.nextActivities, 3434);
            // for (let i = 0; i < this.nextActivities.length; i++) {
            //     this.flowNo = this.nextActivities[i].flowNo
            // }
          }
        }
      }

      // this.nextActivities.forEach((item, index) => {
      //     console.log(item, index);
      //     this.nextActivities = item
      // })
      // // 获取流程组织树与用户
      // if (this.nextActivities.candidateGroups != null) {
      //     getfindApprovableODUApi().then(res => {
      //         console.log(res);
      //     })
      // }
    },
    //常用人/机构人员
    callback(e) {
      this.keys = e;
      this.isReviewOrgId.fullName = [];
      this.isReviewOrgId.posName = [];
      // this.assignee.fullName = []
      // this.assignee.posName = []
      if (this.activeKey == "2") {
        for (var i = 0; i < this.lists.length; i++) {
          console.log(this.lists[i].assignee);
          this.lists[i].assignee.fullName = [];
          this.lists[i].assignee.posName = [];
        }
      }
      if (this.activeKey != "3") {
        //     for (let index = 0; index < this.hxrList.length; index++) {
        //         this.hxrList[index] = []
        //         this.hxrList[index] = []
        //     }
        this.hxrvalue = "";
      }
    },

    //树形控件
    onCheck(checkedKeys, info) {
      // console.log('onCheck', checkedKeys, info);
    },
    //单选框 //常用人和人员机构
    onChange(e) {
      console.log(e);
      // console.log('radio checked', e.target.value);
      this.flowNo = e.target.value.flowNo;
      this.taskDefinitionKey = e.target.value.taskDefinitionKey;
      console.log(this.flowNo);
      const params = {
        currentActivityId: this.currentActivity.id,
        nextActivityId: e.target.value.id,
        procDefKey: this.processDefinition.key,
      };

      //预备人选基本情况申报查询流程审核常用联系人
      gethistoryAssigneesApi(params).then((res) => {
        if (res.data.code == "0000") {
          this.lists = res.data.data;

          console.log(this.lists, 23231);
          for (let i = 0; i < this.lists.length; i++) {
            // console.log(this.lists[i]);
            this.assignee = this.lists[i].assignee.userId;
          }
          this.$message.success(res.data.msg);
        } else {
          this.$message.error(res.data.msg);
        }
      });
      this.nextActivities.forEach((el) => {
        // console.log(el);
        // this.flowNo = el.flowNo
        // console.log(this.flowNo);
        if (el.id == e.target.value) {
          // console.log(el.candidateGroups.join(","));
          // let rootIds = el.candidateGroups.join(",")
          if (el.candidateGroups != null) {
            let rootIds = el.candidateGroups.join(",");
            // console.log(el.candidateGroups);
            // let rootIds = el.candidateGroups
            //人员机构获取树
            getfindApprovableODUApi({ rootIds: rootIds }).then((res) => {
              if (res.data.code == "0000") {
                this.treeData = res.data.data;
                console.log(this.treeData);

                for (let i = 0; i < this.treeData.length; i++) {
                  console.log(this.treeData[i].children);
                  for (
                    let index = 0;
                    index < this.treeData[i].children.length;
                    index++
                  ) {
                    // console.log(this.treeData[i].children[i].children);
                    this.treelist = this.treeData[i].children[i].children;
                    console.log(this.treelist);
                  }
                }
                //父节点不可选
                this.treeData.forEach((el) => {
                  if (el.children != null) {
                    el.disabled = true;
                    el.children.forEach((e) => {
                      if (e.children != null) {
                        e.disabled = true;
                      }
                    });
                  }
                });
              }
            });
          }
        }

        //获取候选人
        let params = {
          taskDefinitionKey: this.taskDefinitionKey,
          taskId: this.taskId,
        };
        getgetNextUsersApi(params).then((res) => {
          if (res.data.code == "0000") {
            this.hxrList = res.data.data.users;
          }
        });
      });
    },
    //候选人
    hxrChange(e) {
      this.message =
        "";
    },
    //复选改单选
    onSelect(selectedKeys, e) {
      this.message =
        "";
      if (selectedKeys.checked.length == 2) {
        this.reviewOrgId = [].concat(
          selectedKeys.checked[selectedKeys.checked.length - 1]
        );
      }
      console.log(this.reviewOrgId);
      e.checkedNodes.forEach((item) => {
        this.isReviewOrgId = item.data.props;
      });
    },
    isChange(e) {
      this.message =
        "";
      console.log(e);
    },
    //发送
    save() {
      const form = {
        currentActivityId: this.currentActivity.id,
        procDefKey: this.processDefinition.key,
        flowNo: this.flowNo,
        nextActivityId: this.nextActivityId.id,
        sendSMS: this.isSendSMS == true ? 1 : 0,
        message: this.message,
        comment: this.comment,
        // assignee: this.assignee,
        assignee: this.hxrvalue,
        // reviewOrgId: this.isReviewOrgId.toString(),
        ids: this.ids,
      };
      fpdbtsComplete(form).then((res) => {
        if (res.data.code == "0000") {
          this.$message.success(res.data.msg);
          this.visible = false;
          this.hxrvalue = "";
          this.hxrList = [];
          this.comment = "";
          // 调用父级的关闭
          // this.$emit("parentClose");
           this.$emit("complete");
        } else {
          this.$message.error(res.data.msg);
        }
      });
      // this.$emit("complete", params);
    },
    successComplete() {
      // this.$message.success(res.data.msg)
      this.visible = false;
      this.hxrvalue = "";
      this.hxrList = [];
      this.comment = "";
      this.getprocessFn();
    },
  },
  //监听父组件传过来的值
  watch: {
    procInstId: {
      immediate: true, // 这句重要  立即执行handler里面的方法
      handler(val) {
        // console.log(val);
        this.procInstId = val;
        this.getprocessFn();
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.liucheng {
  border-bottom: 1px solid #ccc;
}
.xxts {
  border: 1px solid #ccc;
  border-top: none;
}
.ssyj {
  border: 1px solid #ccc;
  border-top: none;
}
.cyren {
  border: 1px solid #ccc;
  border-top: none;
}
.cyrss {
  border-right: 1px solid #ccc;
}
.lcmcs {
  border: 1px solid #ccc;
  height: 30px;
  line-height: 30px;
}
.lcmcsss {
  border-right: 1px solid #ccc;
  height: 30px;
  line-height: 30px;
}
.dqjc {
  border: 1px solid #ccc;
  border-top: none;
  border-bottom: none;
  height: 30px;
  line-height: 30px;
  padding-left: 5px;
}
.xbcl {
  border: 1px solid #ccc;
  height: 30px;
  line-height: 30px;
  padding-left: 5px;
}
.ant-tabs-bar {
  margin: 0 !important;
}
.requireds::before{
   display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    // font-size: 14px;
     @include add-size($font_size_16);
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
}
</style>
