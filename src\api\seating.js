import request from "@/utils/request";
import qs from "qs";

export const isHide = true; //true上传座位图片  false上传座位excel

export function getMeetingList(data) {
    return request({
        url: "/api/v1/meetingSeating/getSimpleMeetinglist",
        method: "post",
    });
}

export function getList(data) {
    return request({
        url: "/api/v1/meetingSeating/getSeatingList",
        method: "post",
        data,
    });
}

export function getImg(data) {
    return request({
        url: '/api/v1/meetingSeating/PCgetImg',
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        transformRequest: [
            function(data) {
                return qs.stringify(data);
            },
        ],
    });
}
//getDisplayTime
export function getDisplayTime(data) {
    return request({
        url: '/api/v1/meetingSeating/getDisplayTime',
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        transformRequest: [
            function(data) {
                return qs.stringify(data);
            },
        ],
    });
}

export function doSave(data) {
    return request({
        url: "/api/v1/meetingSeating/saveMeetingInfo",
        method: "post",
        data
    });
}

export function saveSeating(data) {
    return request({
        url: "/api/v1/meetingSeating/saveSeating",
        method: "post",
        data
    });
}

export function delMeetingFileById(param) {
    return request({
        url: "/api/v1/meetingFile/delMeetingFileById/" + param,
        method: "post",
    });
}

export function getRestMeetingCalendar(param) {
    return request({
        url: "/api/v1/meetingSeating/getRestMeetingCalendar/" + param,
        method: "post",
    });
}

export function uploadFile(data) {
    return request({
        url: "/api/v1/meetingFile/fileUpload",
        method: "post",
        data
    });
}

export function uploadexcel(data) {
    return request({
        url: "/api/v1/meetingSeating/uploadexcel",
        method: "post",
        contentType: false,
        processData: false,
        data
    });
}

export function doDelete(data) {
    return request({
      url: "/api/v1/meetingSeating/delSeating",
      method: "post",
      data
    });
}

export function getById(data) {
    return request({
      url: "/api/v1/meetingSeating/getById",
      method: "post",
      data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        transformRequest: [
            function(data) {
                return qs.stringify(data);
            },
        ],
    });
}