import {
  loadingText,
  messageDuration,
  title,
  tenantId,
} from "@/config/settings";
import * as lodash from "lodash";
import { Loading, Message, MessageBox, Notification } from "element-ui";
// 引入
import { Modal, message, notification } from "ant-design-vue";
import store from "@/store";
const { accessToken, layout } = store.getters;
import { getAccessToken } from "@/utils/accessToken";

const install = (Vue, opts = {}) => {
  /* 全局accessToken */
  Vue.prototype.$baseAccessToken = () => {
    return accessToken || getAccessToken();
  };
  /* 全局标题 */
  Vue.prototype.$baseTitle = (() => {
    return title;
  })();
  /* 全局加载层 */
  Vue.prototype.$baseLoading = (index, text, callback) => {
    let loading;
    if (!index) {
      loading = Loading.service({
        lock: true,
        text: text || loadingText,
        background: "hsla(0,0%,100%,.8)",
      });
    } else {
      loading = Loading.service({
        lock: true,
        text: text || loadingText,
        spinner: "byui-loading-type" + index,
        background: "hsla(0,0%,100%,.8)",
      });
    }
    if (callback) {
      callback(loading);
    } else {
      setTimeout(() => {
        loading.close();
      }, messageDuration);
    }
  };
  /* 全局多彩加载层 */
  Vue.prototype.$baseColorfullLoading = (index, text, callback) => {
    let loading;
    if (!index) {
      loading = Loading.service({
        lock: true,
        text: text || loadingText,
        spinner: "dots-loader",
        background: "hsla(0,0%,100%,.8)",
      });
    } else {
      switch (index) {
        case 1:
          index = "dots";
          break;
        case 2:
          index = "gauge";
          break;
        case 3:
          index = "inner-circles";
          break;
        case 4:
          index = "plus";
          break;
      }
      loading = Loading.service({
        lock: true,
        text: text || loadingText,
        spinner: index + "-loader",
        background: "hsla(0,0%,100%,.8)",
      });
    }
    if (callback) {
      callback(loading);
    } else {
      setTimeout(() => {
        loading.close();
      }, messageDuration);
    }
  };

  /* 全局Message  Ant */
  Vue.prototype.$baseMessage = (messageWord, type) => {
    message[type](messageWord);
  };

  /* 全局Alert element */
  Vue.prototype.$baseAlert = (content, title, callback) => {
    MessageBox.alert(content, title || "温馨提示", {
      confirmButtonText: "确定",
      dangerouslyUseHTMLString: true,
      callback: (action) => {
        if (callback) {
          callback();
        }
      },
    });
  };

  /* 全局Confirm ant */
  Vue.prototype.$baseConfirm = (content, title, callback1, callback2) => {
    Modal.confirm({
      content: content,
      title: title || "温馨提示",
      okText: "确定",
      cancelText: "取消",
      maskClosable: false,
      type: "warning",
      onOk() {
        if (callback1) {
          callback1();
        }
      },
      onCancel() {
        if (callback2) {
          callback2();
        }
      },
    });
  };

  // /* 全局Notification element */
  // Vue.prototype.$baseNotify = (message, title, type, position) => {
  //   Notification({
  //     title: title,
  //     message: message,
  //     position: position || "top-right",
  //     type: type || "success",
  //     duration: messageDuration,
  //   });
  // };

  /* 全局 notification ant */
  Vue.prototype.$baseNotify = (message, title, type = "success", position) => {
    notification[type]({
      message: title,
      description: message,
      placement: position || "topRight",
      duration: 3,
    });
  };

  /* 全局TableHeight */
  Vue.prototype.$baseTableHeight = (formType) => {
    let height = window.innerHeight;
    let paddingHeight = 400;
    const formHeight = 50;

    if (layout === "vertical") {
      paddingHeight = 340;
    }

    if ("number" == typeof formType) {
      height = height - paddingHeight - formHeight * formType;
    } else {
      height = height - paddingHeight;
    }
    return height;
  };

  /* 全局map图层 */
  Vue.prototype.$baseLayer = () => {
    return {
      urlTemplate:
        "http://online{s}.map.bdimg.com/onlinelabel/?qt=tile&x={x}&y={y}&z={z}&styles=pl&scaler=1&p=1",
      subdomains: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      attribution:
        '',
    };
  };
  Vue.prototype.$baseSpatialReference = () => {
    return {
      projection: "baidu",
    };
  };

  /* 全局lodash */
  Vue.prototype.$baseLodash = lodash;
  /* 全局事件总线 */
  Vue.prototype.$baseEventBus = new Vue();

  /**
   * =========== 自定义全局变量 ===========
   */
  Vue.prototype.$tenantId = tenantId;
};

if (typeof window !== "undefined" && window.Vue) {
  install(window.Vue);
}

export default install;
