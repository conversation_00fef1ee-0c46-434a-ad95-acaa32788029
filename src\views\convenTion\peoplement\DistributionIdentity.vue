<template>
  <div class="table-container">
    <a-row :gutter="15">
      <a-col
        :xs="24"
        :sm="24"
        :md="24"
        :lg="5"
        :xl="5"
        style="overflow-y: auto;"
      >
        <a-divider>用户管理</a-divider>
        <a-input-search
          v-model="searchValue"
          placeholder="输入关键字点击搜索"
          class="tree-input"
          @search="onChange"
        />
        <a-tree
          ref="identityTree"
          :tree-data="treeData"
          :checkedKeys="selectedKeys"
          :expanded-keys="expandedKeys"
          :auto-expand-parent="autoExpandParent"
          :replace-fields="defaultProps"
          @select="handleCheck"
          @expand="onExpand"
        >
          <template slot="title" slot-scope="{ orgName }">
            <span
              v-if="searchValue && orgName.indexOf(searchValue) > -1"
              style="color: #f50;"
            >
              {{ orgName }}
            </span>
            <span v-else>{{ orgName }}</span>
          </template>
        </a-tree>
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="19" :xl="19">
        <a-col span="12">
          <a-space class="operator"
                   @click="operator">
            <a-button type="primary"
                      icon="user">设置身份</a-button>
          </a-space>
          <a-space style="margin-left: 8px;"
                   class="operator"
                   @click="identityClick">
            <a-button type="primary">追加身份</a-button>

          </a-space>
        </a-col>
        <!-- <a-col span="12" push="3">
          <a-form ref="form" :form="queryForm" layout="inline">
            <a-form-item label="菜单名称">
              <a-input
                v-model="queryForm.menuName"
                placeholder="菜单名称"
                allow-clear
                class="table-input"
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" native-type="submit" @click="handleQuery"
                >搜索</a-button
              >
            </a-form-item>
          </a-form>
        </a-col> -->
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="19" :xl="19">
        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <!-- 有下拉的table需要  v-if="list.length > 0"  -->
          <a-table
            :bordered="false"
            class="directorySet-table"
            ref="table"
            size="small"
            :columns="columns"
            :pagination="pagination"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            :data-source="list"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>


    <a-modal title="身份分配"
             :visible.sync="dialogFormVisible"
             width="600px"
             @cancel="close"
             @ok="modalOk"
             destroyOnClose>
      <div style="display: flex; height: 80px">
        <a-checkbox-group :value="checkAuthority">
          <a-checkbox style="margin-bottom: 5px"
                      @change="onCheckboxChange"
                      v-for="item in authorityList"
                      :key="item.id"
                      :value="item.id">
            {{ item.name }}
          </a-checkbox>
        </a-checkbox-group>
      </div>
    </a-modal>
    <!-- 追加身份弹出框 -->
    <a-modal title="追加身份选择"
             :visible.sync="identityVisible"
             width="600px"
             @cancel="identityclose"
             @ok="identityOk"
             destroyOnClose>
      <div style="display: flex; height: 80px">
        <a-checkbox-group :value="checkAuthority">
          <a-checkbox style="margin-bottom: 5px"
                      @change="onCheckboxChange"
                      v-for="item in authorityList"
                      :key="item.id"
                      :value="item.id">
            {{ item.name }}
          </a-checkbox>
        </a-checkbox-group>
      </div>
    </a-modal>

  </div>
</template>

<script>
import {
  yajyQueryTree,
  findRelList,
} from "@/api/statisticalAnalysis/inUserMageIndexApi.js";
import {getDistributionIdentityList1,insertUserIdentityApi1,updateTableIdentityList1} from "@/api/convenTion/z_Management";
import moment from "moment";
export default {
  name: "InUserMageIndex",
  data() {
    return {
      options: [],
      list: [],
      listLoading: true,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectedRows: "",
      elementLoadingText: "正在加载...",
      queryForm: {
        page: 1,
        rows: 10,
        orgCode: "",
      },
      //树配置
      treeData: [],
      loading: true,
      filterText: "",
      dialogFormVisible: false,
      checkNodeKeys: [],
      authorityList: [], //权限arr
      checkAuthority: [], //已选权限
      identityVisible: false,
      defaultExpendedKeys: [],
      i: 0,
      treeFlag: 0,
      treeDialogVisible: false,
      treeForm: { rootOrgId: "root" },

      searchValue: "", //搜索
      // 展开的节点
      expandedKeys: [],
      // 是否自动展开
      autoExpandParent: true,
      // 选择的节点
      selectedKeys: [],
      // 替换 treeNode 中 title,key,children 字段
      defaultProps: {
        children: "children",
        title: "orgName",
        key: "orgCode",
      },
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "真实姓名",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "operName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        // {
        //   title: "登录账号",
        //   align: "center",
        //   width: 300,
        //   ellipsis: true,
        //   customRender: (text, record, index) => {
        //     return moment(record.createTime).format("YYYY-MM-DD");
        //   },
        // },
        // {
        //   title: "是否在岗",
        //   align: "center",
        //   width: 300,
        //   ellipsis: true,
        //   customRender: (text, record, index) => {
        //     return moment(record.createTime).format("YYYY-MM-DD");
        //   },
        // },
        // {
        //   title: "是否在职",
        //   align: "center",
        //   width: 300,
        //   ellipsis: true,
        //   customRender: (text, record, index) => {
        //     return moment(record.createTime).format("YYYY-MM-DD");
        //   },
        // },
        // {
        //   title: "是否锁定",
        //   align: "center",
        //   width: 300,
        //   ellipsis: true,
        //   customRender: (text, record, index) => {
        //     return moment(record.createTime).format("YYYY-MM-DD");
        //   },
        // },
        {
          title: "所属机构",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "手机号码",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "mobile",
        },
        {
          title: "身份名称",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "identityName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        // {
        //   fixed: "right",
        //   title: "操作",
        //   align: "center",
        //   width: 200,
        //   customRender: (text, record, index) => {
        //     let h = this.$createElement;
        //     return h("div", [
        //       h(
        //         "span",
        //         {
        //           attrs: {
        //             type: "text",
        //           },
        //           style: {
        //             display: "inline-block",
        //             cursor: "pointer",
        //             marginLeft: "14px",
        //             color: "#DB3046",
        //           },
        //           on: {
        //             click: () => {
        //               this.handleEdit(record);
        //             },
        //           },
        //         },
        //         "编辑"
        //       ),
        //       h(
        //         "span",
        //         {
        //           attrs: {
        //             type: "text",
        //           },
        //           style: {
        //             display: "inline-block",
        //             cursor: "pointer",
        //             marginLeft: "14px",
        //             color: "#DB3046",
        //           },
        //           on: {
        //             click: () => {
        //               this.handleDelete(record);
        //             },
        //           },
        //         },
        //         "删除"
        //       ),
        //     ]);
        //   },
        // },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created() {
    this.getTreeListFuc();
    // 请求身份分配数据
    this.getAuthorityList()
    this.fetchData();
  },
  methods: {
    // 切换页数
    changePageSize(current, pageSize) {
      this.queryForm.page = current; //queryForm.page是页码
      this.queryForm.rows = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = current;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(current, pageSize) {
      this.queryForm.page = current;
      this.pagination.current = current;
      this.fetchData();
    },

    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
      console.log(selectedRowKeys)
      console.log(selectedRows)
    },

    // 点击复选框
    handleCheck(checkedKeys, info) {
      if (info.selected) {
        if (info.node.eventKey == "root") {
          this.queryForm.orgCode = "";
          this.selectedKeys = ["root"];
          this.fetchData();
        } else {
          this.selectedKeys = [info.selectedNodes[0].data.props.orgCode];
          // info.selectedNodes[0].data.props 等于 data
          this.queryForm.orgCode = info.selectedNodes[0].data.props.orgCode;
          this.fetchData();
        }
      }
    },

    //展开点击节点下的子节点
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },

    // 树状搜索
    onChange(e) {
      const value = e;
      const expandedKeys = this.getKeyList(value, this.treeData, []);
      console.log("🤗🤗🤗, expandedKeys =>", expandedKeys);
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      });
    },
    // 获取包含字段的id
    getKeyList(value, tree, keyList) {
      // 获取包含搜索关键字的所有节点
      let getId = (value_id, tree_id, list_id) => {
        for (let i = 0; i < tree_id.length; i++) {
          const node = tree_id[i];
          if (node.orgName.indexOf(value_id) > -1) {
            list_id.push(node.orgCode);
          }
          if (node.children) {
            getId(value_id, node.children, list_id);
          }
        }
        return list_id;
      };
      // 获取包含搜索关键字的所有节点的父节点
      let getParentKey = (value_par, tree_par) => {
        let parentKey;
        for (let index = 0; index < tree_par.length; index++) {
          const node = tree_par[index];
          if (node.children) {
            if (node.children.some((item) => item.orgCode === value_par)) {
              parentKey = node.orgCode;
            } else if (getParentKey(value_par, node.children)) {
              parentKey = getParentKey(value_par, node.children);
            }
          }
        }
        return parentKey;
      };
      // 执行
      getId(value, tree, []).forEach((v) => {
        keyList.push(getParentKey(v, tree));
      });
      return keyList;
    },

    // handleAdd() {
    //   this.$refs["edit"].showEdit(null, this.queryForm.parentId);
    // },

    // 弹出框打开
    operator () {
      this.checkAuthority = [];
      console.log(this.selectedRows, "1122");

      var num = 0;
      if (this.selectedRows == '') return this.$message.warning("请选择用户");
      this.selectedRows.forEach((item) => {
        ++num;
      });
      if (num == 0) return this.$message.warning("请选择列表数据");
      console.log(this.selectedRows);
      if (num == 1) {;
        if (this.selectedRows[0].operId != null) {
          this.selectedRows[0].operId.split(",").forEach((item) => {
            this.checkAuthority.push(item);
          });
        }
      }

      // 删除 checkAuthority 数组中的第一条数据
      if (this.checkAuthority.length > 0) {
        this.checkAuthority.shift(); // 删除数组的第一项
      }

      console.log('checkAuthority:');
      console.log(this.checkAuthority);
      this.dialogFormVisible = true;

    },

    // 保存权限
    modalOk () {
      // 构造数据
      let data = [];
      console.log(this.selectedRows, "00");
      this.selectedRows.map((item) => {
        data.push({
          userId: item.operId,
          userName: item.operName,
          orgId: item.orgCode,
          orgName: item.orgName,
          mobile: item.mobile,
          identityId: this.checkAuthority.toString(),
          // fullId: item.fullId,
        });
      });
      let that = this;
      updateTableIdentityList1(data).then((res) => {
        if (res.data.code == 200) {
          that.$message.success(res.data.msg);
          that.dialogFormVisible = false;
          this.$set(this, "dataSource", []);
          this.$set(this, "selectedRows", []);
          this.$set(this, "selectedRowKeys", []);
          // this.initTreeData();
          //this.getTreeListFuc();
          this.fetchData();
        } else {
          that.$message.error(res.data.msg);
        }
      });
    },

    identityOk () {
      // 构造数据
      let data = [];
      console.log(this.selectedRows, "00");
      this.selectedRows.map((item) => {
        data.push({
          userId: item.operId,
          userName: item.operName,
          orgId: item.orgCode,
          orgName: item.orgName,
          mobile: item.mobile,
          identityId: this.checkAuthority.toString(),
          // fullId: item.fullId,
        });
      });
      let that = this;
      insertUserIdentityApi1(data).then((res) => {
        if (res.data.code == 200) {
          that.$message.success(res.data.msg);
          that.identityVisible = false;
          this.$set(this, "dataSource", []);
          this.$set(this, "selectedRows", []);
          this.$set(this, "selectedRowKeys", []);
          console.log(this.selectedRows, 'this.selectedRows');
          // this.initTreeData();
          this.fetchData();
          //this.getTreeListFuc();
        } else {
          that.$message.error(res.data.msg);
        }
      });
    },

    // 选项变化
    onCheckboxChange (e) {
      if (this.selectedRows.length == 0)
        return this.$message.error("请选择人员数据");
      if (e.target.checked) {
        this.checkAuthority.push(e.target.value);
      } else {
        this.checkAuthority = this.checkAuthority.filter(
          (item) => item != e.target.value
        );
      }
      // console.log(this.checkAuthority)
    },

    //打开追加身份弹出框
    identityClick () {
      if (this.selectedRows.length == 0) return this.$message.warning("请选择人员数据");
      this.identityVisible = true
    },
    //关闭追加身份弹出框
    identityclose () {
      this.identityVisible = false
    },
    close() {
      this.dialogFormVisible = false
    },
    // 获取身份类别列表
    getAuthorityList () {
      getDistributionIdentityList1().then((res) => {
        if (res.data.code == 200) {
          this.authorityList = res.data.data;
        }
      });
    },

    handleEdit(row) {
      this.$refs["edit"].showEdit(row, this.queryForm.parentId);
    },
    handleDelete(row) {
      if (row.menuId) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          doDelete([row.menuId]).then((res) => {
            this.$baseMessage(res.message, "success");
            this.getTreeListFuc();
            this.fetchData();
          });
        });
      } else {
        if (this.selectedRows.length > 0) {
          const ids = this.selectedRows.map((item) => item.menuId);
          this.$baseConfirm("你确定要删除选中项吗", null, () => {
            doDelete(ids).then((res) => {
              this.$baseMessage(res.message, "success");
              this.getTreeListFuc();
              this.fetchData();
            });
          });
        } else {
          this.$baseMessage("未选中任何行", "error");
          return false;
        }
      }
    },

    handleQuery() {
      this.queryForm.page = 1;
      this.fetchData();
    },
    fetchData() {
      this.listLoading = true;
      findRelList(this.queryForm).then((res) => {
        if (res.data.code == 200) this.list = res.data.data;
        // this.listLoading = false;
        // 清空
        this.selectedRowKeys = [];
        // 设置总数
        // this.pagination.total = res.data.total;
      }).catch((error) => {
        throw(error)
      }).finally(() => {
        this.listLoading = false;
        this.loading = false;
      });
    },
    // 获取tree数据
    getTreeListFuc() {
      yajyQueryTree(this.treeForm).then((res) => {
        res.data.data.orgName = "菜单根节点";
        this.treeData = [res.data.data];
        this.expandedKeys = ["root"];
        this.queryForm.orgCode = "root";
        this.fetchData();
      });
    },
    // 设置tree数据
    setTreeData(array) {
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        element.deleted = "";
        if (element.children) {
          this.setTreeData(element.children);
        }
      }
      this.treeData = array;
    },
  },
};
</script>
