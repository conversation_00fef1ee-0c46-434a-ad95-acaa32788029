/**
 * @description 路由守卫
 */
import router from "../router";
import store from "../store";
//NProgress是页面跳转是出现在浏览器顶部的进度条
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import getPageTitle from "@/utils/pageTitle";
import {
  authentication,
  loginInterception, //是否开启登录拦截
  routesWhiteList,//不经过token校验的路由"/login", "/casLogin", "/404", "/401"
} from "@/config/settings";
// 禁用进度环，设置 showSpinner 为 fals
NProgress.configure({ showSpinner: false });
router.beforeEach(async (to, from, next) => {
  NProgress.start();
  // 设置title
  document.title = getPageTitle(to.meta.title);
  let hasToken =2;//获取token

  // let hasToken = store.getters.accessToken;//获取token
  // if (!loginInterception) hasToken = true;
  if (hasToken) {
    if (to.path === "/login") {
      // next({ path: "/" });
      // NProgress.done();
      next()
    } else {
      const hasPermissions =
        store.getters.permissions && store.getters.permissions.length > 0;
      if (hasPermissions) {
        next();
      } else {
        try {
          const { permissions } = await store.dispatch("user/getInfo");
          let accessRoutes = [];
          if (authentication === "intelligence") {
            accessRoutes = await store.dispatch(
              "permission/setRoutes",
              permissions
            );
          } else if (authentication === "all") {
            accessRoutes = await store.dispatch("permission/setAllRoutes");
          }
          router.addRoutes(accessRoutes);
          /*console.log(to);
          let obj1 = { ...to };
          let obj2 = { replace: true };
          console.log(Object.assign(obj1, obj2));
          console.log({ ...to, replace: true });*/
          next({ ...to, replace: true });
        } catch (error) {
          await store.dispatch("user/resetAccessToken");
          next(`/login?redirect=${to.path}`);
          NProgress.done();
        }
      }
    }
  } else {
    if (routesWhiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }

  }
});
router.afterEach(() => {
  NProgress.done();
 
});
