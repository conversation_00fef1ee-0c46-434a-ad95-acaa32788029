<template>
    <div class="table-container">
      <a-row :gutter="15">
        <manageTree ref="tree" :default-expanded-keys="defaultExpandedKeys"
                    @handleClick="handleClick" @handleClickTwo="handleClickTwo" :levelRoleMainIdentify="levelRoleMainIdentify"></manageTree>
        <a-col :xs="24"
               :sm="24"
               :md="24"
               :lg="18"
               :xl="18">
          <a-row>
            <a-form>
              <a-col :span="4">
                <a-form-model-item label="届别"
                                   :label-col="{ span: 6 }"
                                   :wrapper-col="{ span: 14 }">
                  <a-select v-model="jcDm"
                            placeholder="请选择届次"
                            style="width:120px;"
                            @change="onJcDmChange">
                    <a-select-option v-for="item in thNumOptions"
                                     :key="item.termId"
                                     :label="item.termName"
                                     :value="item.termId">{{ item.termName }}</a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="5">
                <a-form-model-item label="代表姓名"
                                   :label-col="{ span: 6 }"
                                   :wrapper-col="{ span: 14 }">
                  <a-input v-model="queryForm.dbxm"
                           style="width: 120px;"
                           allow-clear
                           placeholder="请输入代表姓名"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="5">
              <a-form-model-item label="排名类型" :label-col="{ span: 6 }"
                                   :wrapper-col="{ span: 14 }">
                <a-select default-value="0" style="width: 120px" @change="handleRankTypeChange">
                  <a-select-option value="0">
                    全部代表
                  </a-select-option>
                  <a-select-option value="1">
                    非职务代表
                  </a-select-option>
                  <a-select-option value="2">
                    职务代表
                  </a-select-option>
                </a-select>
              </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="活动时间" :label-col="{ span: 6 }"
                                   :wrapper-col="{ span: 14 }">
                  <a-range-picker
                    v-model="dateRange"
                    style="width: 100%;"
                    :ranges="{
                      最近三个月: [
                        moment(new Date()).subtract(2, 'months'),
                        moment(),
                      ],
                      '今年1-6月': [
                        moment(moment().startOf('year')).startOf('month'),
                        moment(moment().startOf('year'))
                          .add(5, 'months')
                          .endOf('month'),
                      ],
                      '今年7-12月': [
                        moment(moment().startOf('year'))
                          .add(6, 'months')
                          .startOf('month'),
                        moment(moment().startOf('year'))
                          .add(11, 'months')
                          .endOf('month'),
                      ],
                      今年内: [
                        moment(moment().startOf('year')).startOf('month'),
                        moment(moment().startOf('year'))
                          .add(11, 'months')
                          .endOf('month'),
                      ],
                    }"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    @change="onTimeChange"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="4">
                <a-button type="primary"
                          style="margin-left: 8px;"
                          @click="fetchData()">搜索</a-button>
                
              </a-col>
            </a-form>
          </a-row>

          <a-row style="margin: 5px 0px 10px 8px">
            <a-col :span="6">
              <a-button type="primary"
                          style="margin-left: 8px;"
                          :loading="downloadExcelLoading"
                          @click="batchDownload()">批量下载</a-button>
            </a-col>
          </a-row>
  
          <div v-for="(department, index) in list"
               :key="index"
               class="z_new_cleass"
               v-if="department.length>0">
            <h3 v-if="titleBox"
                class="mb10 mt10">{{ titleBox }}</h3>
  
            <h3 v-else
                class="mb10 mt10">{{ department[0].organizationName }}</h3>
            <a-checkbox-group v-model="checkedMembers"
                              style="width: 100%;">
              <a-col v-for="db in department"
                     :key="db.id"
                     :span="4"
                     style="margin-bottom: 2px;">
                <a-col :span="13" :style="{padding: 0}">
                  <a-checkbox :value="db"
                              style="
                      background-color: #f3f3f3;
                      padding: 10px 0 10px 10px;
                      width: 131px;
                    ">
                    <span>{{ db.positionName }}</span>
                  </a-checkbox>
                </a-col>
                <a-col :span="11" :style="{padding: 0}">
                  <div style="
                      background-color: white;
                      padding: 8px;
                      /* margin-left: 1px; */
                      height: auto;
                      border: 2px solid rgb(243 243 243);
                      width: 80px;
                    ">
                  <a-popover placement="bottomLeft">
                  <template slot="content">
                    <a-col>
                      <a-button type="link"
                                @click="downloadExcel(db)">下载履职文档</a-button>
                    </a-col>
                  </template>
                    <a @click="toDetails(db)">
                      <div>{{ db.employeeName }}</div>
                    </a>
                    </a-popover>
                  </div>
                </a-col>
              </a-col>
            </a-checkbox-group>
          </div>
        </a-col>
      </a-row>
    </div>
  </template>
  
  <script>
  import { findDeputyOrganizations } from "@/api/dmcs";
  import { downloadInfoExcel, batchDownloadInfo } from "@/api/electronicArchives.js";
  import {
    findLevelList,
    findAllByOrganizationIdAndTermIdTwo,
  } from "@/api/organization";
  import checkPermission from "@/utils/permission";
  // import manageTree from "./manageTree";
  import manageTree from "@/views/common/manageTree";
  import moment from "moment";
  import constat from "@/utils/constat";
  import SingleSelect from '@/components/SingleSelect/index';
  import SearchForm from '@/components/SearchForm/index';
  import SingleSearch from '@/components/SingleSearch/index';
  import TimeRangePicker from '@/components/TimeRangePicker/index';
  import { getDbdhByOrgId } from "@/api/system/dbdh";
  import { DBLZ_DBLZDJ } from '@/utils/enum/levelRoleMainIdentifyEnum'; // 导入配置文件
  
  import { mapGetters } from "vuex";
  export default {
    name: "Rel",
    components: {
      manageTree,
      SingleSelect,
      SearchForm,
      SingleSearch,
      TimeRangePicker,
    },
    computed: {
      ...mapGetters(["layout", "visitedViews", "routes"]),
      isDelegation () {
        if (this.orgIds.indexOf(this.currOrgId) > -1) {
          return true;
        }
        return false;
      },
    },
    filters: {},
    data () {
      return {
        levelRoleMainIdentify: DBLZ_DBLZDJ,
        downloadExcelLoading: false,
        meetingCode: "",
        orgIds: [
          "1028",
          "1140",
          "435",
          "329",
          "1128",
          "968",
          "139",
          "862",
          "306",
          "283",
          "260",
          "816",
        ],
        titleBox: null,
        list: [],
        listLoading: true,
        total: 0,
        selectRows: "",
        queryForm: {
          organizationId: "",
          termId: "",
          dbxm: "",
        },
        organizationId: "",
        jcDm: "",
        sfDm: "",
        //机构树配置
        treeData: [],
        loading: true,
        filterText: "",
        dialogSort: false,
        showEide: false,
        sortForm: {},
        thNumOptions: [],
        replaceFields: {
          title: "fullName",
          key: "id",
        },
        checkedMembers: [],
        adjustInInfo: {
          isShow: false,
        },
        currOrgId: "1140",
        //TODO 默认展开不生效
        defaultExpandedKeys: [""],
        dateRange: [],
        dutyList: [
          {name: '全部代表', id: '0'},
          {name: '非职务代表', id: '1'},
          {name: '职务代表', id: '2'},
        ]
      };
    },
  
    created () {
      // let queryForm = JSON.parse(localStorage.getItem("tongjifenzi"));
      // if (queryForm) {
      //   this.queryForm = queryForm
      //   this.dateRange[0] = queryForm.startTime
      //   this.dateRange[1] = queryForm.endTime
      // }
      // this.getOrgTreeRawData();
      this.fetchData();
      // this.initJcOptions();
    },
    // beforeDestroy () {
    //   window.localStorage.setItem("tongjifenzi", JSON.stringify(this.queryForm));
    // },
    methods: {
      resetForm() {
        this.queryForm = {
          startTime: '',
          endTime: '',
          dutyTag: '',
          dbxm: '',
        }
        this.jcDm = '',
        this.fetchData()
      },
      getTime(val) {
        this.queryForm.startTime = val[0];
        this.queryForm.endTime = val[1];
      },
      setSelectRows (val) {
        this.selectRows = val;
      },
      handleAdd () {

      },
      handleSort (row) {
        this.sortForm = {
          id: row.id,
          sort: row.sort,
        };
        this.dialogSort = true;
      },
      initJcOptions (dhDm) {
        findLevelList({ meetingCode: this.meetingCode }).then((response) => {
          this.thNumOptions = response.data;
          var leng = response.data.length - 1;
          this.jcDm = response.data[leng].termId;
          this.sfDm = response.data[leng].regionLevel;
          this.queryForm.termId = response.data[leng].termId;
          response.data.forEach((item) => {
            if (item.isCurrentTerm == "1") {
              this.jcDm = item.termId;
              this.sfDm = item.regionLevel;
              this.queryForm.termId = item.termId;
            }
          });
          this.fetchData();
        });
      },
      fetchData () {
        this.listLoading = true;
        this.queryForm.showState = 1
        findAllByOrganizationIdAndTermIdTwo(this.queryForm).then((response) => {
          this.list = response.data;
          this.listLoading = false;
          console.log(this.list);
        });
      },
  
      // 获取tree数据
      getOrgTreeRawData () {
        findDeputyOrganizations().then((res) => {
          this.treeData = res.data;
        });
      },
  
      // 节点过滤操作
      filterNode (value, data) {
        if (!value) return true;
        return data.name.indexOf(value) !== -1;
      },
      //点击节点
      async handleClick (entryId, e) {
        // if (e.node._props.dataRef.ids == "1110") {
        //   this.this = [];
        //   let XM = e.node._props.dataRef.XM;
        //   this.list = {
        //     WKWFOKOWAF: XM,
        //   };
        //   this.titleBox = e.node._props.dataRef.id;
        // } else if (e.node._props.dataRef.meetingCode) {
        //   this.titleBox = null;
        //   let id = entryId;
        //   this.currOrgId = entryId;
        //   this.meetingCode = e.node._props.dataRef.meetingCode;
        //   this.initJcOptions(this.meetingCode);
        //   this.organizationId = e.node._props.dataRef.entryId;
        //   this.queryForm.organizationId = id;
        // }
        this.currOrgId = entryId;
        this.organizationId = e.node._props.dataRef.id;
        this.queryForm.organizationId = e.node._props.dataRef.id;
        const res = await getDbdhByOrgId({ orgId: this.organizationId})
        this.meetingCode = res.data.dhDm
        this.initJcOptions();
      },
      onJcDmChange (value) {
        this.queryForm.termId = value;
        this.fetchData();
      },
      toDetails(info) {
        this.$router.push({
          path: '/electronicArchives/details',
          query: {
            jcDm: info.jcDm,
            userId: info.employeeId,
            sfDm: info.sfDm,
            startTime: this.queryForm.startTime,
            endTime: this.queryForm.endTime
          },
        })
      },
      // 下载
      downloadExcel(info) {
        let query = {
          jcDm: info.jcDm,
          userId: info.employeeId,
          sfDm: info.sfDm,
          startTime: this.queryForm.startTime,
          endTime: this.queryForm.endTime,
          dutyTag: this.queryForm.dutyTag
        }
        // this.downloadExcelLoading = true;
        downloadInfoExcel(query).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = info.employeeName + `代表电子档案` + new Date().getTime() + `.xlsx`;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          setTimeout(() => {
            // this.downloadExcelLoading = false;
          }, 1000);
        });
      },
      batchDownload() {

        // let treeIds = this.$refs.tree.getCheckedKeys()
        // if (treeIds.length == 0) {
        //   this.$baseMessage("请勾选左侧树节点！", "error");
        //   return false;
        // }
        // this.currOrgId

        this.downloadExcelLoading = true;
        this.queryForm.jcDm = this.queryForm.termId
        let treeIds = []
        treeIds.push(this.currOrgId)
        this.queryForm.orgIds = treeIds
        let userIds = []
        this.queryForm.userIds = []
        this.checkedMembers.forEach(item => {
          userIds.push(item.employeeId)
        })
        this.queryForm.userIds = userIds
        batchDownloadInfo(this.queryForm).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `代表电子档案.zip`;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          setTimeout(() => {
            this.downloadExcelLoading = false;
          }, 1000);
        });
      },
      onTimeChange(val) {
        this.queryForm.startTime = val[0];
        this.queryForm.endTime = val[1];
      },
      handleRankTypeChange(value) {
        this.queryForm.dutyTag = value
      },
      //点击节点
      async handleClickTwo (entryId) {
        this.organizationId = entryId;
        this.queryForm.organizationId = entryId;
        const res = await getDbdhByOrgId({ orgId: entryId})
        if(! res.data || ! res.data.dhDm) {
          return
        }
        this.meetingCode = res.data.dhDm
        this.initJcOptions();
      },
      checkPermission,
    },
  };
  </script>
  
  <style>
  .right-panel {
    flex-wrap: nowrap !important;
  }
  .name_color {
    color: red;
  }
  </style>
  <style scoped lang="scss">
  .el-checkbox--small {
    margin-bottom: 5px;
  }
  .z_new_cleass {
    .ant-col-4 {
      width: 20.666667% !important;
    }
  }
  </style>
  