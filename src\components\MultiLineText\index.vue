<template>
  <div style="width: 100%;" class="multi-line-text-container">
    <MultiLineTextItem v-show="countryText" label="全国代表" :values="countryText" />
    <MultiLineTextItem v-show="provinceText" label="省代表" :values="provinceText" />
    <MultiLineTextItem v-show="cityText" label="市代表" :values="cityText" />
    <MultiLineTextItem v-show="administrativeAreaText" label="区代表" :values="administrativeAreaText" />
    <MultiLineTextItem v-show="streetTownText" label="镇街代表" :values="streetTownText" />
  </div>
</template>

<script>
import MultiLineTextItem from './MultiLineTextItem.vue'
export default {
  props: {
    values: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    MultiLineTextItem
  },
  data() {
    return {
      countryText: '',
      provinceText: '',
      cityText: '',
      administrativeAreaText: '',
      streetTownText: ''
    };
  },
  created() {

  },
  mounted() {

  },
  computed: {},
  watch: {
    values: {
      handler(newVal, oldVal) {
        this.handlerValuesChange()
      },
      deep: true
    }
  },
  methods: {
    handlerValuesChange() {
      this.countryText = this.values.filter(item => item.sfDm == 1).map(item => item.name || item.userName).join('、')
      this.provinceText = this.values.filter(item => item.sfDm == 2).map(item => item.name || item.userName).join('、')
      this.cityText = this.values.filter(item => item.sfDm == 3).map(item => item.name || item.userName).join('、')
      this.administrativeAreaText = this.values.filter(item => item.sfDm == 4).map(item => item.name || item.userName).join('、')
      this.streetTownText = this.values.filter(item => item.sfDm == 5).map(item => item.name || item.userName).join('、')
    }
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
}
</script>

<style lang='scss' scoped>
.multi-line-text-container {
  display: flex;
  flex-direction: column;
}


</style>