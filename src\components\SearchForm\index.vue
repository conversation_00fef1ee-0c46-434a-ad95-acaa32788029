<template>
  <div>
    <a-row style="margin-left: 1%">
      <a-form-model
        ref="formInfo"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        style="display: flex"
      >
        <div :style="{flex: leftArea}">
          <!-- <div style="flex: 82%;display: inline-block;"> -->
          <slot style="display: inline-block" name="topSearch"></slot>
          <div v-if="advanced">
            <!-- 更多查询条件抽屉 -->
            <slot style="display: inline-block" name="moreSearch"></slot>
          </div>
        </div>
        <div :style="{flex: rightArea}">
          <!-- <div style="flex: 18%;"> -->
          <span style="float: right; margin-top: 3px">
            <a
              v-if="!noMore"
              style="margin-right: 8px"
              @click="advanced = !advanced"
            >
              {{ advanced ? "收起" : "高级搜索" }}
              <a-icon :type="advanced ? 'up' : 'down'" />
            </a>
            <a-button type="primary" @click="search">搜索</a-button>
            <a-button
              style="margin-left: 12px"
              class="pinkBoutton"
              @click="reset"
              >重置</a-button
            >
          </span>
        </div>
      </a-form-model>
    </a-row>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";

/**
 * 搜索表单
 * 1. 基础使用 <SearchForm :value="queryForm" @onReset="" @onSearch="" />
 * 2. 重置值说明
 *    2.1. 必须指定 value
 *    2.2. onReset(value) 回传，需要赋值 this.queryForm = value
 *    2.3. value 重置值必须先于 created() 初始化，否则无效
 */
export default {
  name: "SearchForm",
  props: {
    value: {
      type: Object,
      required: false,
      default: () => ({}),
    },
    noMore: {
      type: Boolean,
      default: () => false,
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    leftArea: {
      type: String,
      default: () => "82%",
    },
    rightArea: {
      type: String,
      default: () => "18%",
    },
  },
  data() {
    return {
      advanced: false,
      initValue: {},
    };
  },
  created() {
    this.initValue = cloneDeep(this.value);
  },
  methods: {
    search() {
      this.$emit("onSearch", true);
    },
    reset() {
      this.$emit("onReset", cloneDeep(this.initValue));
    },
  },
};
</script>

<style lang="scss" scoped></style>
