<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="24"
             :xl="24">
        <a-form ref="form"
                :form="queryForm"
                layout="horizontal"
                :labelCol="{ span: 5 }"
                :wrapperCol="{ span: 18, offset: 0 }"
                @submit.native.prevent>
          <a-row style="margin-left:1%">
            <a-col :span="6">
              <a-form-item label="届次">
                <a-select v-model="queryForm.session"
                          placeholder="请选择届次"
                          allow-clear
                          show-search>
                  <a-select-option v-for="item in administrativeAreas"
                                   :key="item.id"
                                   :label="item.name"
                                   :value="item.name">
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="年度">
                <a-date-picker v-model="queryForm.year"
                               mode="year"
                               format="YYYY"
                               :open="openYear"
                               @openChange="(status) => {openYear = status;}"
                               @panelChange="(val) => {queryForm.year = moment(val).format('YYYY');openYear = false;}  "
                               @change="  (val) => {queryForm.year = val == null ? null : val;    $forceUpdate();  }"
                               placeholder="请选择年度"
                               style="width:100%"
                               allow-clear></a-date-picker>
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item label="代表姓名">
                <a-input allow-clear
                         v-model="queryForm.userName"
                         placeholder="请输入代表姓名"
                         v-on:keyup.enter="search"
                         autocomplete="off"></a-input>
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <span style="float: right; margin-top: 3px">
                <a-button type="primary"
                          native-type="submit"
                          @click="search">搜索</a-button>
                <a-button @click="reset"
                          style="margin-left: 12px;"
                          class="pinkBoutton">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
        <!-- table -->
        <a-spin :spinning="listLoading"
                :indicator="indicator">
          <a-table :bordered="false"
                   ref="noticeTable"
                   class="tableLimit"
                   :columns="columns"
                   :pagination="pagination"
                   :row-selection="{onChange: setSelectRows,}"
                   :data-source="list"
                   :rowKey="  (record, index) => {    return index;  }"
                   :scroll="{ x: 300, y: 0 }"></a-table>
        </a-spin>
      </a-col>
    </a-row>
    <!-- 删除提示 -->
    <el-dialog title="提示"
               :visible.sync="centerDialogVisible"
               width="30%"
               center>
      <span>你确定要删除当前项吗</span>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
        <el-button type="primary"
                   @click="centerDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { communityScheduleList } from "@/api/intoCommunityOrg.js";
import moment from "moment";
export default {
  name: "IntoCommunityOrg",
  components: {},
  filters: {},
  data () {
    return {
      indicator: <a-icon type="loading" style="font-size: 24px" spin />, //转动的圈圈
      list: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      openYear: false,
      listLoading: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 10,
      background: true,
      //list-----
      selectRows: "",
      centerDialogVisible: false,
      formLabelWidth: "120px",
      //行政区域列表
      administrativeAreas: [
        { name: "第十五届", key: 1 },
        { name: "第十六届", key: 2 },
      ],
      streetTowns: [],
      indexNum: 1,
      // 列表
      columns: [
        {
          title: "标题",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "title",
        },
        {
          title: "年度",
          align: "center",
          width: 80,
          ellipsis: true,
          dataIndex: "year",
        },
        {
          title: "届次",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "session",
        },
        {
          title: "计划时间",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "serverTime",
        },
        {
          title: "联络站名称",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "liaisonStationName",
        },
        {
          title: "行政区名",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "administrativeAreaName",
        },
        {
          title: "街道名",
          align: "center",
          width: 110,
          ellipsis: true,
          dataIndex: "streetTownName",
        },
        {
          title: "填报方式",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "type",
          customRender: (text, record, index) => {
            if (text == 1) {
              return "计划";
            }
            if (text == 2) {
              return "自愿填报";
            }
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "userName",
        },
        // {
        //   title: "签到",
        //   align: "center",
        //   width: 80,
        //   ellipsis: true,
        //   dataIndex: "isSigned",
        //   customRender: (text, record, index) => {
        //     if (text == 1) {
        //       return "已签到";
        //     }
        //     if (text == 0) {
        //       return "未签到";
        //     }
        //   },
        // },
        // {
        //   title: "签到时间",
        //   align: "center",
        //   width: 180,
        //   ellipsis: true,
        //   dataIndex: "signedTime",
        // },
        {
          title: "是否审核",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            if (text == 1) {
              return "是";
            }
            if (text == 0) {
              return "否";
            }
          },
        },
        {
          title: "审核意见",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "auditOpinion",
        },
      ],
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created () {
    let queryForm = JSON.parse(localStorage.getItem("tongjifenzi"));
    if (queryForm) {
      this.queryForm = queryForm
      this.pagination.current = this.queryForm.pageNum
    }
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "网上联络站");
    this.$store.dispatch("navigation/breadcrumb2", "联络站列表");
  },
  beforeDestroy () {
    window.localStorage.setItem("tongjifenzi", JSON.stringify(this.queryForm));
  },
  methods: {
    search () {
      this.queryForm.pageNum = 1
      this.pagination.current = 1
      this.fetchData()
    },
    // 重置
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
    moment,
    // 选择时间
    changeYear (val) {
      this.queryForm.year = val == null ? null : val;
      this.$forceUpdate();
    },
    // 设置administrativeAreaId
    selectadministrativeAreas (state) {
      console.log("🤗🤗🤗, state =>", state);
      this.administrativeAreas.map((item) => {
        if (item.value === state) {
          this.listQuery.administrativeAreaId = item.value;
        }
      });
    },

    // 获取乡镇街道
    liststreetTowns (administrativeAreaId) {
      getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
        }
      );
    },
    // 选择行政区域
    handleAdministrativeAreaChange (val) {
      if (!val) return;
      this.listQuery.streetTownId = "";
      this.selectadministrativeAreas(val);
      this.liststreetTowns(val);
    },
    clearListQuery () {
      for (const key in this.queryForm) {
        this.queryForm[key] = "";
      }
      this.fetchData();
    },
    setSelectRows (val) {
      this.selectRows = val;
    },

    fetchData () {
      this.listLoading = true;
      communityScheduleList(this.queryForm).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        this.list = res.rows;
        this.listLoading = false;
        this.total = res.total;
        this.pagination.total = res.total;
      });
    },
    handleSizeChange (val) {
      this.listQuery.size = val;
      this.fetchData();
    },

    // 切换页数
    changePageSize (current, pageSize) {
      this.queryForm.pageNum = current;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = current;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange (current, pageSize) {
      this.queryForm.pageNum = current;
      this.pagination.current = current;
      this.fetchData();
    },

    // 删除
    deleteItem (id) {
      this.$baseConfirm("你确定要删除当前项吗", null, () => {
        doDelete({ id: id }).then((response) => {
          this.$message.success("删除成功");
          this.fetchData();
        });
      });
    },

    amend (id) {
      console.log(id);
      this.$router
        .push({
          path: "/BBS/add",
          query: {
            id: id,
            oper: "update",
          },
        })
        .catch(() => { });
    },

    handleCurrent (row) {
      console.log("row", row);
    },
  },
};
</script>
<style scoped>
.code_btn {
  margin-bottom: 10px;
}

.p {
  display: flex;
  width: 100%;
}

.p > span {
  flex: 1;
}
</style>
