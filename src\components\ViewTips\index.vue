<template>
  <div class="table-tips">
      <a-icon style="margin-top: 3px;margin-right: 5px;color: #faad14;" type="exclamation-circle" theme="filled" />
      <span >{{tipValue}}</span>
    </div>
</template>

<script>
export default {
  name: "timeLine",
  props: ['tipValue'],
  components: {},
  data() {
    return {
    };
  },
  methods: {
    
  }
}
</script>

<style lang="scss" scoped>
.table-tips {
    display: flex;
    border: 1px solid #f1f3e1;
    background-color: #fefce7;
    padding: 10px;
    font-size: 14px;
    margin-top: 10px;
    margin-bottom: 5px;
  }
</style>