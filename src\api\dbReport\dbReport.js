
import { instance_1 } from "@/api/axiosRq";
import request from "@/utils/request";

export function getDbReport(params) {
  return instance_1({
    url: "/dbReport/getDbReport",
    method: "get",
    params: params
  });
}

export function getUserIdZtc(params) {
  return instance_1({
    url: "/dbReport/getUserIdZtc",
    method: "get",
    params: params,
  });
}
export function getWeekByUserId(params) {
  return instance_1({
    url: "/dbReport/getWeekByUserId",
    method: "get",
    params: params,
  });
}
