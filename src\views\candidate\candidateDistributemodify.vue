<template>
    <a-modal destroyOnClose :visible.sync="jointTableVisible" width="90%" title="候选人分配" @cancel="close"
        @ok="unitTableOk" okText="送审" cancelText="取消">
        <a-row class="steps">
            <a-steps :current="Steps">
                <a-step title="第一步：分配"></a-step>
                <a-step title="第二步：初审" />
                <a-step title="第三步：终审" />
                <a-step title="第四步：分配表审核完成" />
            </a-steps>
        </a-row>
        <a-row style="margin-top: 30px;">
            <!-- 左 -->
            <a-col :span="11">
                <a-form-model ref="leftQueryForm" layout="inline" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }"
                    :model="leftQueryForm">
                    <a-row>
                        <a-col :span="12">
                            <a-form-model-item label="届次" style="width: 100%;">
                                <a-select v-model="leftQueryForm.jcDm" allow-clear disabled style="width: 100%;"
                                    placeholder="请选择届次">
                                    <a-select-option v-for="item in periods" :key="item.jcDm" :value="item.jcDm">{{
                                            item.levelName
                                    }}</a-select-option>
                                </a-select>
                            </a-form-model-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-model-item label="姓名" prop="xm" style="width: 100%;">
                                <a-input v-model="leftQueryForm.xm" autocomplete="off" allow-clear placeholder="请输入姓名">
                                </a-input>
                            </a-form-model-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-model-item label="推荐单位" prop="tjdw" style="width: 100%;">
                                <a-input v-model="leftQueryForm.tjdw" autocomplete="off" placeholder="请输入推荐单位">
                                </a-input>
                            </a-form-model-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-model-item>
                                <a-button type="primary" @click="getdfphxrApiData">搜索</a-button>
                            </a-form-model-item>
                        </a-col>
                    </a-row>
                </a-form-model>

                <a-table bordered :columns="columns" :dataSource="leftNowData" rowKey="DBID" :row-selection="{
                    selectedRowKeys: selectedLeftRowKeys,
                    onSelect: onLeftSelect,
                }" :pagination="leftPagination" :scroll="{ x: '50%', y: 450 }"></a-table>
            </a-col>
            <a-col :span="1">
                <div class="changeBtn">
                    <a-button type size="small" @click="toRight">
                        <a-icon type="right" />
                    </a-button>
                    <br />
                    <a-button type size="small" @click="toLeft">
                        <a-icon type="left" />
                    </a-button>
                </div>
            </a-col>
            <!-- 右 -->
            <a-col :span="11">
                <a-form-model ref="rightQueryForm" layout="inline" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }"
                    :model="rightQueryForm">
                    <a-row>
                        <a-col :span="12">
                            <a-form-model-item label="届次">
                                <a-select v-model="rightQueryForm.jcDm" allow-clear disabled style="width:100%"
                                    placeholder=" 请选择届次">
                                    <a-select-option v-for="item in periods" :key="item.jcDm" :value="item.jcDm">{{
                                            item.levelName
                                    }}</a-select-option>
                                </a-select>
                            </a-form-model-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-model-item label="姓名" prop="xm" style="width:100%">
                                <a-input v-model="rightQueryForm.xm" autocomplete="off" allow-clear placeholder="请输入姓名">
                                </a-input>
                            </a-form-model-item>
                        </a-col>

                        <a-col :span="12">
                            <a-form-model-item label="推荐单位" prop="tjdw" style="width:100%">
                                <a-input v-model="rightQueryForm.tjdw" autocomplete="off" placeholder="请输入推荐单位">
                                </a-input>
                            </a-form-model-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-model-item>
                                <a-button type="primary" @click="getyfphxrApiData">搜索</a-button>
                            </a-form-model-item>
                        </a-col>
                    </a-row>
                </a-form-model>
                <a-table bordered :dataSource="rightNowData" rowKey="DBID" :row-selection="{
                    selectedRowKeys: selectedRightRowKeys,
                    onSelect: onRightSelect,
                }" :columns="rightColumns" :pagination="false" :scroll="{ x: '50%', y: 450 }"></a-table>
            </a-col>
        </a-row>
        <a-modal :visible="selectElectionVisible" title="请选择选举单位" @ok="selectElectionFormOk"
            @cancel="selectElectionFormCancel">
            <a-form-model>
                <a-form-model-item label="选举单位">
                    <a-select v-model="selectElectionForm.xzqhDm" placeholder="请选择选举单位">
                        <a-select-option v-for="item in unitList" :key="item.xzqhDm" :value="item.xzqhDm"
                            :label="item.xzqhmc">{{ item.xzqhmc }}</a-select-option>
                    </a-select>
                </a-form-model-item>
            </a-form-model>
        </a-modal>
        <submitForCensorship ref="submitForCensorship" :procInstId="procInstId" @complete="handleComplete"
            @parentClose="close" :ids="ids"></submitForCensorship>
    </a-modal>
</template>
<script>
import { gethxrfpbscompleteApi } from "@/api/xjgw";
import { getDeputyList } from "@/api/myJob/myProposal.js";
import { getAdministrativeStateListApi, gethxrfpbsfindbyidgetByIdApi } from "@/api/representativeElection/candidateApi.js";
import submitForCensorship from "./candidateDistributeShip";
import {
    getdfphxrApi,
    getyfphxrApi,
    hxrfpbsUpdate,
    hxrfpbsAssign,
    getcancelAssignApi,
} from "@/api/representativeElection/candidateApi.js";
import { getLevelListApi } from "@/api/representativeElection/candidateApi.js";
export default {
    components: { submitForCensorship },
    //   props: { defaultSelectKey: Array },
    props: {
        neWid: {
            type: String,
        },
    },
    // 构造数据
    data() {
        return {
            Steps: 0,
            procInstId: "",
            ids: [],
            newIDs: '',
            jointTableVisible: false,
            selectElectionVisible: false,
            selectElectionForm: { xzqhDm: "" },
            searchValue: "",
            columns: [
                {
                    title: "待分配候选人",
                    dataIndex: "USER_NAME",
                    ellipsis: true,
                    width: 120,
                },
                {
                    title: "性别",
                    dataIndex: "XBMC",
                    ellipsis: true,
                    width: 70,
                },
                {
                    title: "出生日期",
                    dataIndex: "BIRTHDAY",
                    ellipsis: true,
                    width: 120,
                    customRender: (text, record, index) => {
                        return text.slice(0, text.indexOf("T")) || '/';
                    }
                },
                {
                    title: "工作单位及职务",
                    dataIndex: "GZDWJZW",
                    ellipsis: true,
                    width: 180,
                },
                {
                    title: "职业构成",
                    dataIndex: "ZYGCMC",
                    ellipsis: true,
                    width: 100,
                },
                {
                    title: "推荐单位",
                    dataIndex: "MELYMC",
                    ellipsis: true,
                    width: 100,
                },
            ],
            rightColumns: [
                {
                    title: "已分配候选人",
                    dataIndex: "USER_NAME",
                    ellipsis: true,
                    width: 120,
                },
                {
                    title: "性别",
                    dataIndex: "XBMC",
                    ellipsis: true,
                    width: 70,
                },
                {
                    title: "出生日期",
                    dataIndex: "BIRTHDAY",
                    ellipsis: true,
                    width: 120,
                    customRender: (text, record, index) => {
                        return text.slice(0, text.indexOf("T")) || '/';
                    }
                },
                {
                    title: "行政规划",
                    dataIndex: "xzqhDm",
                    ellipsis: true,
                    width: 100,
                },
                {
                    title: "工作单位及职务",
                    dataIndex: "GZDWJZW",
                    ellipsis: true,
                    width: 180,
                },
                {
                    title: "职业构成",
                    dataIndex: "ZYGCMC",
                    ellipsis: true,
                    width: 100,
                },
                {
                    title: "推荐单位",
                    dataIndex: "MELYMC",
                    ellipsis: true,
                    width: 100,
                },
            ],
            queryForm: {
                jcDm: "2",
                xm: "",
                tjdw: "",
                pageNum: "",
                pageSize: "",
            },
            multiple: true, //多选
            rightData: [],
            leftData: [],
            rightNowData: [],
            leftNowData: [],
            selectedLeftRowKeys: [],
            selectedRightRowKeys: [],
            unitList: [],
            ids: [],
            periods: [],
            waterId: "",
            procInstId: "",
            upDataForm: {},
            resourceForm: {},
            leftQueryForm: { jcDm: "", xm: "", tjdw: "", pageNum: 1, pageSize: 10 },
            rightQueryForm: { jcDm: "", xm: "", tjdw: "", pageNum: 1, pageSize: 10 },
            // 左分页器设置
            leftPagination: {
                pageNo: 1,
                pageSize: 10, // 默认每页显示数量
                showSizeChanger: true, // 显示可改变每页数量
                showQuickJumper: true, // 显示跳转功能
                pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
                showTotal: (total) => `总共 ${total} 条`, // 显示总数
                onShowSizeChange: (current, pageSize) =>
                    this.changePageSizeLeft(current, pageSize), // 改变每页数量时更新显示
                onChange: this.onChangeLeft.bind(this), // 点击页码事件
                total: 0, // 总条数
                current: 0, // 当前页数
                buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
                size: "middle",
            },
        };
    },
    watch: {
        jointTableVisible(newVal) {
            if (newVal) {
                this.getDeputyListData();
                this.getAdministrativeStateList();
                this.newIDs = this.neWid
                this.shenhe()
            }
        },
    },
    methods: {
        //回显审核
        shenhe() {
            if (this.jointTableVisible == true) {
                console.log(this.newIDs);
                if (this.newIDs != '') {
                    console.log(this.newIDs);
                    let fpbId = this.newIDs
                    gethxrfpbsfindbyidgetByIdApi(fpbId).then(res => {
                        if (res.data.code == '0000') {
                            this.ids.push(res.data.data.id)
                            this.procInstId = res.data.data.procInstId

                        }
                    })
                }
            }
        },
        //审核保存发送
        handleComplete(data) {
            // , { "tzlx": 0 }
            gethxrfpbscompleteApi(data).then(res => {
                if (res.data.code == '0000') {
                    this.$emit('handleClearId')
                    this.jointTableVisible = false
                    this.ids = []
                    this.$refs.submitForCensorship.successComplete()
                    this.$message.success(res.data.msg)
                } else {
                    this.$message.error(res.data.msg)
                }
            })
        },
        // 切换页数 左
        changePageSizeLeft(pageNum, pageSize) {
            this.leftQueryForm.pageNum = pageNum;
            this.leftQueryForm.pageSize = pageSize;
            this.leftPagination.pageSize = pageSize;
            this.leftPagination.current = pageNum;
            this.getdfphxrApiData();
        },

        // 切换页码 左
        onChangeLeft(pageNum, pageSize) {
            this.leftQueryForm.pageNum = pageNum;
            this.leftPagination.current = pageNum;
            this.getdfphxrApiData();
        },
        // 接受完成
        handleComplete() { },
        // 单位选择保存
        selectElectionFormOk() {
            let form = {};
            form.formData = {};
            //开始右侧没有数据
            form.formData.hxrfpbFbs = [];
            if (!this.selectElectionForm.xzqhDm) {
                return this.$message.error("请选择单位!");
            }
            if (this.rightNowData.length == 0) {
                this.rightData.forEach((item) => {
                    form.formData.hxrfpbFbs.push({ dbId: item.DBID });
                });
                form.formData.jcDm = { jcDm: this.queryForm.jcDm };
                form.xzqhDm = this.selectElectionForm.xzqhDm;
                hxrfpbsAssign(form).then((res) => {
                    console.log("🤗🤗🤗, res =>", res);
                    if (res.data.code == "0000") {
                        this.upDataForm = res.data.data;
                        // 设置值
                        // this.procInstId = res.data.data.procInstId;
                        // this.ids.push(res.data.data.id);
                        this.rightData.map((item) => {
                            item.xzqhDm = "";
                            item.xzqhDm = this.selectElectionForm.xzqhDm;
                        });
                        if (this.rightNowData.length > 0) {
                            this.rightNowData = this.rightNowData.concat(this.rightData);
                        } else {
                            this.rightNowData = this.rightData;
                        }
                        let rightKey = this.rightData.map((item) => item.DBID);
                        this.leftNowData = this.leftNowData.filter((item) => {
                            if (!rightKey.includes(item.DBID)) {
                                return item;
                            }
                        });
                        // this.getdfphxrApiData();
                        this.selectedLeftRowKeys = [];
                        this.selectElectionFormCancel();
                    }
                });
            } else {
                form.xzqhDm = this.selectElectionForm.xzqhDm;
                // 有数据调这个
                form.formData.dqzt = this.upDataForm.dqzt;
                console.log(this.rightData, 'this.rightData');
                this.rightData.forEach((item) => {
                    form.formData.hxrfpbFbs.push({ dbId: item.DBID });
                });
                form.formData.id = this.upDataForm.id;
                // form.formData.jcDm = this.upDataForm.jcDm;
                form.formData.jcDm = { jcDm: this.queryForm.jcDm };
                form.formData.procInstId = this.upDataForm.procInstId;
                console.log(form.formData, 'form.formData');
                form.formData.yxbz = this.upDataForm.yxbz;
                form.formData.zsbmId = "";
                form.formData.zsjgId = "";
                console.log("🤗🤗🤗, form =>", form);
                form.formData.id = this.newIDs
                hxrfpbsUpdate(form).then((res) => {
                    if (res.data.code == "0000") {
                        // 设置值
                        // this.procInstId = res.data.data.procInstId;
                        // this.ids.push(res.data.data.id);
                        this.rightData.map((item) => {
                            item.xzqhDm = "";
                            item.xzqhDm = this.selectElectionForm.xzqhDm;
                        });
                        if (this.rightNowData.length > 0) {
                            this.rightNowData = this.rightNowData.concat(this.rightData);
                        } else {
                            this.rightNowData = this.rightData;
                        }
                        let rightKey = this.rightData.map((item) => item.DBID);
                        this.leftNowData = this.leftNowData.filter((item) => {
                            if (!rightKey.includes(item.DBID)) {
                                return item;
                            }
                        });
                        this.getdfphxrApiData();
                        this.selectedLeftRowKeys = [];
                        this.selectElectionFormCancel();
                    }
                });
            }

            // this.selectedLeftRowKeys = [];
            // this.selectedRightRowKeys = [];
            // this.getyfphxrApiData();
        },
        // 单位选择关闭
        selectElectionFormCancel() {
            this.selectElectionVisible = false;
            this.selectElectionForm = { name: "" };
        },
        // 右移
        toRight() {
            this.selectElectionVisible = true;
        },
        // 左移
        toLeft(row) {
            let form = {};
            form.formData = {};
            form.formData.hxrfpbFbs = [];
            this.leftData.forEach((item) => {
                form.formData.hxrfpbFbs.push({ dbId: item.DBID });
            });
            form.formData.jcDm = { jcDm: this.queryForm.jcDm };
            form.xzqhDm = this.selectElectionForm.xzqhDm;
            form.formData.id = this.newIDs
            getcancelAssignApi(form).then((res) => {
                console.log("🤗🤗🤗, res =>", res);
                if (res.data.code == "0000") {
                    this.selectedLeftRowKeys = [];
                    this.selectedRightRowKeys = [];
                    this.getdfphxrApiData();
                    this.getyfphxrApiData();
                }
            });
        },
        // 保存
        unitTableOk() {
            this.$refs.submitForCensorship.visible = true;
            // this.jointTableVisible = false;
            // Object.assign(this.$data, this.$options.data());
        },
        // 选择
        onLeftSelect(record, selected, selectedRows, nativeEvent) {
            if (selected) {
                this.rightData = selectedRows;
            } else {
                // 取消选中
                this.rightData = this.rightData.filter(
                    (item) => item.DBID != record.DBID
                );
            }
            this.selectedLeftRowKeys = selectedRows.map((item) => item.DBID);
        },
        // 选择
        onRightSelect(record, selected, selectedRows, nativeEvent) {
            if (selected) {
                this.leftData = selectedRows;
            } else {
                // 取消选中
                this.leftData = this.leftData.filter(
                    (item) => item.DBID != record.DBID
                );
            }
            this.selectedRightRowKeys = selectedRows.map((item) => item.DBID);
        },
        // 关闭
        close() {
            this.ids = []
            this.$emit('handleClearId')
            this.jointTableVisible = false;
            this.Steps = 0;
            Object.assign(this.$data, this.$options.data());
        },
        //单位
        getAdministrativeStateList() {
            getAdministrativeStateListApi().then((res) => {
                if (res.data.code == "0000") {
                    this.unitList = res.data.data;
                }
            });
        },
        // 获取数据
        getDeputyListData() {
            //获取届次下拉框
            getLevelListApi().then((res) => {
                if (res.data.code === "0000") {
                    this.periods = res.data.data;
                    this.leftQueryForm.jcDm = this.periods[0].jcDm; //改过
                    this.rightQueryForm.jcDm = this.periods[0].jcDm; //改过
                    this.getdfphxrApiData();
                    this.getyfphxrApiData();
                }
            });
        },
        // 获取左边数据
        getdfphxrApiData() {
            getdfphxrApi(this.leftQueryForm).then((res) => {
                if (res.data.code == 200) {
                    this.leftNowData = res.data.rows;
                    this.leftPagination.total = res.data.total;
                }
            });
        },
        // 获取右边数据
        getyfphxrApiData() {
            let form = Object.assign({}, this.rightQueryForm);
            form.xzqhDm = "";
            form.fpbId = this.newIDs;
            getyfphxrApi(form).then((res) => {
                if (res.data.code == 200) {
                    this.rightNowData = res.data.rows;
                }
            });
        },
    },
};
</script>
<style lang="css" scoped>
.ant-modal-content .changeBtn {
    text-align: center;
    margin-top: 300%;
}
</style>
