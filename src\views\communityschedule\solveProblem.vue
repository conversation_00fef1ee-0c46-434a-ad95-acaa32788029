<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <SearchForm :value="queryForm" @onReset="reset" @onSearch="search">
          <template v-slot:topSearch>
            <AdminStreetStationCascade v-model="queryForm" allow-clear />
          </template>
          <template v-slot:moreSearch>
            <DhJcCascade v-model="queryForm" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>

            <FormInput
              v-model="queryForm.userName"
              :label="'代表姓名'"
              allow-clear
              @search="search"
            />

            <FormPicker
              v-model="queryForm.year"
              label="年度"
              type="year"
              allow-clear
            />
          </template>
        </SearchForm>

        <a-row style="margin: 5px 0px 10px 8px">
          <a-col span="6">
            <!-- <a-button @click="$router.go(-1)"> 返回</a-button> -->
            <a-button
              type="primary"
              style="margin-left: 12px"
              @click="exportExcel"
              >导出</a-button
            >
          </a-col>
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <a-table
            ref="table"
            :bordered="false"
            class="directorySet-table"
            size="small"
            :columns="columns"
            :pagination="pagination"
            :data-source="list"
            :row-key="
              (record, index) => {
                return record.id;
              }
            "
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { solveProblemList, solveProblemExport } from "@/api/communityschedule";
import SearchForm from "@/components/SearchForm/index";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormPicker from "@/components/FormPicker/index.vue";
import { DBLZ_DBJSQ } from "@/utils/enum/levelRoleMainIdentifyEnum";

export default {
  components: {
    FormPicker,
    AdminStreetStationCascade,
    FormInput,
    DhJcCascade,
    SearchForm,
  },
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBJSQ,
      selectedRows: [],
      selectedRowKeys: [],
      list: [],
      listLoading: false,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        dhDm: undefined,
        jcDm: undefined,
        dbtId: undefined,
        userName: undefined,
        year: undefined,
      },
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      // 列表
      columns: [
        {
          title: "序号",
          align: "center",
          width: 60,
          ellipsis: true,
          customRender: (text, record, index) => {
            return index + 1 || "/";
          },
        },
        {
          title: "日期",
          align: "center",
          ellipsis: true,
          width: 130,
          dataIndex: "serverTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站名称",
          align: "center",
          ellipsis: true,
          width: 340,
          dataIndex: "liaisonStationName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "群众姓名及人数",
          align: "center",
          ellipsis: true,
          width: 200,
          dataIndex: "voternameNum",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "接待的人大代表姓名及电话",
          align: "center",
          ellipsis: true,
          width: 200,
          customRender: (text, record, index) => {
            return record.dbnameMoblie;
          },
        },
        {
          align: "center",
          ellipsis: true,
          title: "群众反映的问题、意见或建议内容",
          width: 200,
          dataIndex: "content",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          ellipsis: true,
          title: "交办情况",
          children: [
            {
              title: "不需转交，直接答复",
              align: "center",
              ellipsis: true,
              width: 200,
              customRender: (text, record, index) => {
                return record.bizType || "/";
              },
            },
            {
              title: "转交时间、转交部门及联系方式",
              align: "center",
              ellipsis: true,
              width: 200,
              customRender: (text, record, index) => {
                return record.zjbmjlxfx || "/";
              },
            },
          ],
        },
        {
          align: "center",
          ellipsis: true,
          title: "办理情况",
          children: [
            {
              title: "推进日期、内容等",
              align: "center",
              ellipsis: true,
              width: 200,
              customRender: (text, record, index) => {
                return record.handlingContent || "/";
              },
            },
            {
              title: "推进过程中存在的困难等",
              align: "center",
              ellipsis: true,
              width: 200,
              customRender: (text, record, index) => {
                return record.situation || "/";
              },
            },
          ],
        },
        {
          align: "center",
          ellipsis: true,
          title: "反馈代表、群众日期",
          width: 150,
          customRender: (text, record, index) => {
            return record.replyPeopleDate || "/";
          },
        },
        {
          align: "center",
          ellipsis: true,
          title: "评价情况",
          width: 400,
          children: [
            {
              align: "center",
              ellipsis: true,
              title: "代表反馈",
              width: 200,
              children: [
                {
                  align: "center",
                  ellipsis: true,
                  title: "对办理部门是否满意",
                  width: 200,
                  customRender: (text, record, index) => {
                    return record.committeeGradeForDept || "/";
                  },
                },
                {
                  align: "center",
                  ellipsis: true,
                  title: "对办理结果是否满意",
                  width: 200,
                  customRender: (text, record, index) => {
                    return record.committeeGradeForResult || "/";
                  },
                },
              ],
            },
            {
              align: "center",
              ellipsis: true,
              title: "群众反馈",
              width: 200,
              children: [
                {
                  align: "center",
                  ellipsis: true,
                  title: "对办理部门是否满意",
                  width: 200,
                  customRender: (text, record, index) => {
                    return record.peopleGradeForDept || "/";
                  },
                },
                {
                  align: "center",
                  ellipsis: true,
                  title: "对办理结果是否满意",
                  width: 200,
                  customRender: (text, record, index) => {
                    return record.peopleGradeForResult || "/";
                  },
                },
              ],
            },
          ],
        },
        {
          align: "center",
          ellipsis: true,
          title: "再次办理或解释情况（代表或群众不满意）",
          width: 200,
          customRender: (text, record, index) => {
            return record.secondHandlingContent || "/";
          },
        },
        {
          align: "center",
          ellipsis: true,
          title: "办结时间",
          width: 150,
          customRender: (text, record, index) => {
            return record.completedTime || "/";
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created() {
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "代表进社区活动次数统计");
  },
  methods: {
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    //列表
    fetchData() {
      this.listLoading = true;
      solveProblemList(this.queryForm).then((response) => {
        this.list = response.rows;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    handleSizeChange(val) {
      this.queryForm.size = val;
      this.fetchData();
    },
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    reset(value) {
      this.queryForm = value;
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
    exportExcel() {
      var arr = [];
      if (this.selectedRowKeys.length != 0) {
        // return this.$message.warning("请选择数据！");
        // arr = this.selectRows.map((item) => item.id)
        arr = this.selectedRowKeys.map((item) => item);
      }
      solveProblemExport({
        ...this.queryForm,
        ids: arr.toString(),
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `代表进社区活动解决问题情况一览表.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
  },
};
</script>
