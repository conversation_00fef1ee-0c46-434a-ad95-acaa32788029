import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/message/getMessageList",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/message/addMessage",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/message/updateMessage",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/message/deleteMessage",
    method: "post",
    data
  });
}

export function addMessageInfo() {
  return request({
    url: "/api/v1/message/initMessageId",
    method: "post",
  });
}

export function getNoticeIdentityList(param) {
  return request({
    url: "/api/v1/message/getIdentityList/" + param,
    method: "post",
  });
}
