import request from "@/utils/requestTemp";

export function levelRoleMainIdentifyList(params, data) {
  return request({
    url: '/levelRoleMainIdentify/list',
    method: 'post',
    params: params,
    data: data
  })
}

export function levelRoleMainIdentifyNoPageList(params, data) {
  return request({
    url: '/levelRoleMainIdentify/listNoPage',
    method: 'post',
    params: params,
    data: data
  })
}

export function addLevelRoleMainIdentify(data) {
  return request({
    url: '/levelRoleMainIdentify/add',
    method: 'post',
    data: data
  })
}
  
export function updateLevelRoleMainIdentify(data) {
  return request({
    url: '/levelRoleMainIdentify/updateById',
    method: 'post',
    data: data
  })
}
export function deleteLevelRoleMainIdentify(params) {
  return request({
    url: '/levelRoleMainIdentify/deleteById',
    method: 'get',
    params: params
  })
}