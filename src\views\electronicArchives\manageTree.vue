<template>
  <a-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
    <a-input
      v-model="filterText"
      style="width: 300px;"
      placeholder="输入关键字过滤"
      allow-clear
      @change="onChange"
    />
    <a-tree
      ref="orgTree"
      :replace-fields="replaceFields"
      v-model="checkedKeys"
      :tree-data="treeData"
      @select="handleClick"
      :default-expanded-keys="[treeData[2].children[0].children[2].id]"
      :default-selected-keys="[
        treeData[2].children[0].children[2].id,
      ]"
      checkable
      :checkedKeys="checkedKeys"
    >
      <template slot="title" slot-scope="{ fullName }">
        <span
          v-if="searchValue && fullName.indexOf(searchValue) > -1"
          style="color: #f50;"
          >{{ fullName }}</span
        >
        <span v-else>{{ fullName }}</span>
      </template>
    </a-tree>
  </a-col>
</template>

<script>
import { findDeputyOrganizations } from "@/api/dmcs";
export default {
  data() {
    return {
      replaceFields: {
        title: "fullName",
        key: "id",
        // children: "",
      },
      searchValue: "",
      filterText: "",
      treeData: [],
      allChildData: [],
      keys: "",
      expandedKeys: ["R000000"],
      checkedKeys: []
    };
  },
  created() {
    this.getOrgTreeRawData();
  },
  methods: {
    // 获取tree数据
    getOrgTreeRawData() {
      findDeputyOrganizations().then((res) => {
        this.treeData = res.data;
        this.keys = this.treeData[2].children[0].children[2].id;
      });
    },
    handleClick(data, e) {
      let entryId = data[0].substr(1);
      this.selectedKeys = data;
      this.$emit("handleClick", entryId, e);
    },
    // 树状搜索
    onChange(e) {
      const value = e.target.value;
      const expandedKeys = this.allChildData.map((item) => {
        if (item.name.indexOf(value) > -1) {
          return this.getParentKey(item.id, this.userTreeData);
        }
        return null;
      });
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      });
    },
    // 获取所在的父节点
    getParentKey(id, tree) {
      let parentKey;
      for (let index = 0; index < tree.length; index++) {
        const node = tree[index];
        if (node.children) {
          if (node.children.some((item) => item.id === id)) {
            parentKey = node.id;
          } else if (this.getParentKey(id, node.children)) {
            parentKey = this.getParentKey(id, node.children);
          }
        }
      }
      return parentKey;
    },
    getCheckedKeys() {
      return this.checkedKeys
    }
  },
};
</script>

<style></style>
