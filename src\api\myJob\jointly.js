import { instance_yajy } from "@/api/axiosRq";
// 查询联名管理列表
// type 类型
// queryForm 请求搜索数据
export function jointlyList(type, queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/jointly/queryList/${type}`,
    method: "post",
    data: queryForm || {},
  });
}

export function delegation(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/findPage`,
    method: "post",
    data: queryForm,
  });
}
