import Layout from "@/layouts";

export default [
  {
    path: "/region", 
    component: Layout,
    redirect: "noRedirect",
    name: "region",
    title: "数据驾驶舱",
    meta: {
      title: "数据驾驶舱首页",
    },
    children: [
      {
        path: "region",
        name: "region",
        component: () => import("@/views/sjjsc/index"),
        meta: {
          title: "数据驾驶舱",
        },
      },
    ],
  },
    {
    path: "/personeelDataEntry", 
    component: Layout,
    redirect: "noRedirect",
    name: "dataEntry",
    title: "数据驾驶舱",
    meta: {
      title: "区人事任免数据录入",
    },
    children: [
    {//驾驶舱人事任免
        path: "JSCPersonnelRemovals",
        name: "JSCPersonnelRemovals",
        component: () => import("@/views/jiashicang/addPersonnel"),
        meta: {
          title: "驾驶舱人事任免录入",
        },
      },
      {//驾驶舱人事任免
        path: "listPage",
        name: "listPage",
        component: () => import("@/views/jiashicang/listPage"),
        meta: {
          title: "驾驶舱人事任免列表",
        },
      },
    ],
  },
  {
    path: "/suggestDataEntry", 
    component: Layout,
    redirect: "noRedirect",
    name: "dataEntry",
    title: "数据驾驶舱",
    meta: {
      title: "区建议数据录入",
    },
    children: [
      {
        path: "addSuggestv",
        name: "addSuggestv",
        component: () => import("@/views/meeting/suggesting/addSuggestvQz"),
        meta: {
          title: "录入建议",
        },
      },
      {
        path: "myLeadSuggestion",
        name: "Comprehensive3",
        component: () =>
          import("@/views/meeting/comprehensiveAccess/myLeadSuggestionQz"),
        meta: {
          title: "我录入的建议",
        },
      },
    ],
  }

];
