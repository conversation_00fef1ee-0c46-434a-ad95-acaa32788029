<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="24"
             :xl="24">
        <!-- <byui-query-form>
          <byui-query-form-left-panel :span="12">
            <a-button :class="{ active: queryForm.sourceType === 'zrhy' }"
                      @click="handleChange('zrhy')"
                      shape="round">主任会议成员联系全国代表</a-button>
            <a-button :class="{ active: queryForm.sourceType === 'zcry' }"
                      @click="handleChange('zcry')"
                      shape="round"
                      :autofocus="true">常委会组成人员联系省代表市代表</a-button>
          </byui-query-form-left-panel>
          <byui-query-form-right-panel :span="24">
          </byui-query-form-right-panel>
        </byui-query-form> -->
        <a-tabs default-active-key="1"
                @change="callback">
          <a-tab-pane key="1"
                      tab="主任会议成员联系全国代表">
            <a-table ref="table"
                     class="tableLimit"
                     :bordered="false"
                     :data-source="list"
                     :pagination="pagination"
                     :rowKey="
            (record, index) => {
              return index;
            }
          ">
              <a-table-column title="序号"
                              width="80"
                              align="center">
                <template slot-scope="text, record, index">{{
              index + 1
            }}</template>
              </a-table-column>

              <a-table-column v-for="column in columnList"
                              :key="column.key"
                              :data-index="column.key"
                              align="center"
                              :title="column.label"
                              ellipsis="true"></a-table-column>

              <a-table-column title="操作"
                              align="center"
                              width="350">
                <template slot-scope="text, record">
                  <a v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])"
                     type="warning"
                     style="color:#DB3046"
                     @click="handleEdit(record)">编辑</a>
                  <a v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])"
                     type="danger"
                     style="margin-left:14px;color:#DB3046"
                     @click="handleDelete(record)">删除</a>
                </template>
              </a-table-column>
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="2"
                      tab="常委会组成人员联系省代表市代表"
                      force-render>
            <a-table ref="table"
                     class="tableLimit"
                     :data-source="list"
                     bordered
                     :pagination="pagination"
                     :rowKey="
            (record, index) => {
              return index;
            }
          ">
              <a-table-column title="序号"
                              width="80"
                              align="center">
                <template slot-scope="text, record, index">{{
              index + 1
            }}</template>
              </a-table-column>
              <a-table-column v-for="column in columnList"
                              :key="column.key"
                              :data-index="column.key"
                              align="center"
                              :title="column.label"
                              ellipsis="true"></a-table-column>

              <a-table-column title="操作"
                              align="center"
                              width="350">
                <template slot-scope="text, record">
                  <a v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])"
                     style="color:#DB3046"
                     @click="handleEdit(record)">编辑</a>
                  <a v-if="checkPermission(['I_XTGLY', 'MEETING_ADMIN'])"
                     style="margin-left:14px;color:#DB3046"
                     @click="handleDelete(record)">删除</a>
                </template>
              </a-table-column>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { instance_1 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
export default {
  name: "Table",
  components: {},
  filters: {},
  data () {
    return {
      TBloading: false,
      columnList: [],
      list: [],
      listLoading: true,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        sourceType: "zrhy",
      },
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  components: {},
  created () {
    const sourceType = "zrhy";
    if (sourceType == "zrhy") {
      this.queryForm.sourceType = "zrhy";
      this.columnList = [
        {
          key: "sourceUserNames",
          label: "市人大常委会主任会议",
        },
        {
          key: "targetUserNames1",
          label: "全国代表",
        },
      ];
    } else {
      this.queryForm.sourceType = "zcry";

      this.columnList = [
        {
          key: "sourceUserNames",
          label: "市人大常委会组成人员",
        },
        {
          key: "targetUserNames1",
          label: "广东省代表",
        },
        {
          key: "targetUserNames2",
          label: "广州市代表",
        },
      ];
    }
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "常用服务");
    this.$store.dispatch("navigation/breadcrumb2", "代表双联列表");
  },
  beforeDestroy () { },
  mounted () {
    this.fetchData();
  },
  computed: {},
  methods: {
    callback (key) {
      console.log(key);
      if (key == 1) {
        this.handleChange('zrhy')
      } else {
        this.handleChange('zcry')
      }
    },
    // 切换页数
    changePageSize (pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange (pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    setSelectRows (val) {
      this.selectRows = val;
    },

    // handleSizeChange(val) {
    //   this.queryForm.size = val;
    //   this.fetchData();
    // },
    // handleCurrentChange(val) {
    //   this.queryForm.current = val;
    //   this.fetchData();
    // },
    handleQuery () {
      this.drawer = false;
      this.queryForm.current = 1;
      this.fetchData();
    },

    handleEdit (row) {
      this.$router
        .push({
          name: "DbslEdit",
          query: {
            id: row.id,
          },
        })
        .catch(() => { });
    },

    handleDelete (row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          instance_1({
            url: "/memberContact/del",
            method: "get",
            params: { id: row.id },
          }).then((res) => {
            this.$baseMessage(res.message, "success");
            this.fetchData();
          });
        });
      } else {
        if (this.selectRows.length > 0) {
          const ids = this.selectRows.map((item) => item.authId);
          this.$baseConfirm("你确定要删除选中项吗", null, () => {
            instance_1({
              url: "/memberContact/del",
              method: "get",
              params: ids,
            }).then((res) => {
              this.$baseMessage(res.message, "success");
              this.fetchData();
            });
          });
        } else {
          this.$baseMessage("未选中任何行", "error");
          return false;
        }
      }
    },

    handleChange (sourceType) {
      if (sourceType == "zrhy") {
        this.queryForm.sourceType = "zrhy";
        this.columnList = [
          {
            key: "sourceUserNames",
            label: "市人大常委会主任会议",
          },
          {
            key: "targetUserNames1",
            label: "全国代表",
          },
        ];
      } else {
        this.queryForm.sourceType = "zcry";

        this.columnList = [
          {
            key: "sourceUserNames",
            label: "市人大常委会组成人员",
          },
          {
            key: "targetUserNames1",
            label: "广东省代表",
          },
          {
            key: "targetUserNames2",
            label: "广州市代表",
          },
        ];
      }
      this.queryForm.pageNum = 1;
      this.queryForm.pageSize = 10;
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },

    fetchData () {
      this.listLoading = true;
      instance_1({
        url: "/memberContact/list",
        method: "get",
        params: this.queryForm,
      }).then((res) => {
        console.log(res);
        this.list = res.data.rows;
        this.total = res.data.total;
        this.pagination.total = res.data.total;
        setTimeout(() => {
          this.listLoading = false;
        }, 500);
      });
    },

    checkPermission,
  },
};
</script>
<style>
.tableLimit tr td .cell {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important; /*可以显示的行数，超出部分用...表示 */
  -webkit-box-orient: vertical !important;
}
.tableLimit .a-button--small {
  padding: 0 !important;
}
.active {
  color: #1890ff;
  border-color: #badeff;
  background-color: #e8f4ff;
}
</style>
