<template>
  <div>
    <a-col :span="spanNum" :class="{ 'overLength': title.length > 5 && spanNum <= 8 }">
      <a-form-model-item  :label="title" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-date-picker 
          v-model="chooseDate"
          allow-clear
          value-format="YYYY-MM-DD"
          :placeholder="'请选择'+ title"
          style="width: 100%;"
          @change="onTimeChange"></a-date-picker>
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '选择时间',
      required: true
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    value: {
      type: [String, Number],
      default: () => '',
    },
  },
  data() {
    return {
      chooseDate: '',
    };
  },
  watch: {   
    value: {  
      handler(val) {
        this.chooseDate = val;
      },
      deep: true,
      immediate: true,
    },
  }, 
  methods: {
    onTimeChange(val) {
      // this.$emit('getTime', val)
      this.$emit('update:value', val)
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
