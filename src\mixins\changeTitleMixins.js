// 代表议案管理 修改标题按钮
// <!-- 修改标题 提出框 -->
// <a-modal
//   :visible="changeTitleVisible"
//   title="修改标题"
//   @cancel="closeTitle"
// >
//   <a-form-model>
//     <a-form-model-item label="标题">
//       <a-input v-model="recordForm.proposalTitle"></a-input>
//     </a-form-model-item>
//   </a-form-model>
//   <div slot="footer" class="dialog-footer">
//     <a-button @click="closeTitle">取 消</a-button>
//     <a-button type="primary" @click="okTitle" :loading="saveBtnVisible"
//       >确 定</a-button
//     >
//   </div>
// </a-modal>
import {
  proposalById,
  getJointlyList,
  getProposalUndertakes,
  suggestionUpData,
} from "@/api/myJob/myProposal.js";
import { getAttachmentList, getUserData_yajy } from "@/api/commonApi/file.js";
export const changeTitleMixins = {
  data() {
    return {
      saveBtnVisible: false,
      recordForm: {},
      changeTitleVisible: false,
      userData: {},
    };
  },
  created() {
    this.getOneUser();
    // userData.authorities[0].authority == 'YAJY_QRD'
  },
  methods: {
    // 获取登录人数据
    getOneUser() {
      getUserData_yajy().then((res) => {
        if (res.data.code == 200) {
          // 获取登录人的权限
          this.userData = res.data.data;
          console.log("🤗🤗🤗, this.userData =>", this.userData);
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 修改标题 提交修改
    okTitle() {
      // 设置文件入参
      if (this.recordForm.fuJianIds && this.recordForm.fuJianIds.length > 0) {
        this.recordForm.fuJianIds =
          this.recordForm.fuJianIds.toString() +
          "," +
          this.recordForm.contentId;
      } else {
        this.recordForm.fuJianIds = this.recordForm.contentId;
      }
      suggestionUpData(this.recordForm).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.closeTitle();
          this.fetchData();
          this.selectedRows = [];
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 修改标题 关闭窗口
    closeTitle() {
      this.changeTitleVisible = false;
      this.recordForm = {};
    },
    // 修改标题 触发
    changeTitle() {
      if (this.selectedRows.length == 0) {
        return this.$message.error("请选择数据!");
      }
      // 打开界面
      this.changeTitleVisible = true;
      // 获取数据
      proposalById({ proposalId: this.selectedRows[0].proposalId }).then(
        (res) => {
          if (res.data.code == 200) {
            this.recordForm = Object.assign({}, res.data.data);
            // 回显数据
            this.echoData();
          } else {
            this.$message.error(res.data.message);
          }
        }
      );
    },
    // 修改标题 单位/代表 回显
    echoData() {
      //代表回显
      var p1 = new Promise((resolve, reject) => {
        getJointlyList(this.selectedRows[0].proposalId).then((res) => {
          if (res.data.code == 200) {
            let operIds = [];
            let operIdsName = [];
            for (const item of res.data.data) {
              if (item.ifpublic == 0) {
                operIds.push(item.operId);
              }
            }
            for (const item of res.data.data) {
              if (item.ifpublic == 0) {
                operIdsName.push(item.name);
              }
            }
            this.recordForm.operIds = operIds.toString();
            this.recordForm.operIdsName = operIdsName.toString();
            resolve(true);
          }
        });
      });
      // 单位
      var p2 = new Promise((resolve, reject) => {
        getProposalUndertakes({
          orgName: "",
        }).then((res) => {
          res.data.data.forEach((item) => {
            if (this.recordForm.intentOrg == item.orgCode) {
              this.recordForm.intentOrg = item.orgName;
            }
          });
          resolve(true);
        });
      });
      // 获取附件列表
      var p3 = new Promise((resolve, reject) => {
        getAttachmentList({
          proposalId: this.selectedRows[0].proposalId,
          type: "",
        }).then((res) => {
          if (res.data.code == 200) {
            // 文件回显
            let file_zw = [];
            // 合并正文附件以及正文html的attId
            res.data.data.forEach((item) => {
              if (item.attType == 9 || item.attType == 11) {
                file_zw.push(item.attId);
              }
            });
            if (file_zw.length > 0) {
              console.log("🤗🤗🤗, file_zw =>", file_zw);
              // 获取正文附件
              let file_9 = res.data.data.filter((item) => item.attType == 9);
              file_9.map((item) => {
                item.attId = file_zw.toString();
                item.uid = item.attId;
                item.name = item.attName + item.attSuffix;
              });
              // 设置正文
              this.recordForm.contentId = file_9[0].attId;
              this.fileTextList = file_9;
              console.log("🤗🤗🤗, this.fileTextList =>", this.fileTextList);
            }
            let file_1 = res.data.data.filter((item) => item.attType == 1);
            if (file_1.length > 0) {
              file_1.map((item) => {
                item.uid = item.attId;
                item.name = item.attName + item.attSuffix;
              });
              this.fileAppendixList = file_1;
              // 设置附件
              this.recordForm.fuJianIds = file_1.map((item) => item.attId);
              console.log(
                "🤗🤗🤗, this.fileAppendixList =>",
                this.fileAppendixList
              );
            }

            resolve(true);
          }
        });
        Promise.all([p1, p2, p3])
          .then((result) => {
            console.log("3个接口返回", result);
            this.saveBtnVisible = false;
          })
          .catch((error) => {
            console.log(error);
          });
      });
    },
  },
};
