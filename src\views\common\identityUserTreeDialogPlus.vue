<template>
  <el-dialog
    :title="identityTreeDialogTitle"
    :visible.sync="identityTreeDialogVisible"
    width="30%"
  >
    <el-input
      placeholder="输入关键字进行过滤"
      v-model="filterText"
      @input="$forceUpdate()"
    ></el-input>
    <el-tree
      ref="identityTree"
      :data="identityTreeData"
      :props="identityTreeProps"
      node-key="id"
      @check-change="identityCheckChange"
      show-checkbox
      :check-on-click-node="true"
      :default-checked-keys="identityTreeDefaultKey"
      :default-expanded-keys="identityTreeExpandedKey"
      :filter-node-method="filterNode"
      :check-strictly="true"
    ></el-tree>
    <span slot="footer" class="dialog-footer">
      <el-button @click="identityTreeDialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getAllResourceIdentityUser } from "@/api/identity";
export default {
  data() {
    return {
      filterText: "",
      identityTreeDialogVisible: false,
      treeCheckedKey: [],
      identityTreeData: [],
      identityTreeDefaultKey: [],
      identityTreeExpandedKey: ['root'],
      identityTreeProps: {
        children: "children",
        label: "name",
      },
      treeDataMap: [],
      checkLimit: 0,
      checkLimitTips: '',
      rootNodeList: null,
    };
  },
  props: {
    identityTreeDialogTitle: {
      type: String,
      default: "选择用户",
    },
  },
  created() {
    this.initTree();
  },
  watch: {
    filterText(val) {
      this.$refs.identityTree.filter(val);
    },
  },
  methods: {
    initTree() {
      //获取所有身份
      getAllResourceIdentityUser().then((res) => {
        var list = res.data;
        this.generateParentIdMap(list);
        this.formatTreeData('root', '所有类型');
      });
    },
    generateParentIdMap(list){
      list.forEach(item => {
        if(item.type != 'user'){
          item.disabled = true;
          if(!item.parentId){
            item.parentId = 'root'
          }
        }
        if(50043 == item.id){
          console.info(item);
        }
        // 父id-item映射
        var subList = this.treeDataMap[item.parentId];
        if(!subList){
          subList = [];
        }
        subList.push(item);
        this.treeDataMap[item.parentId] = subList;
      });
    },
    formatTreeData(parentId, parentName){
      var list = this.treeDataMap[parentId];
      if(!list || list.length == 0){
        return null;
      }
      list.forEach(item => {
        item.parentName = parentName;
        var subList = this.formatTreeData(item.id, item.name);
        if(subList){
          item.children = subList;
        }
      });
      return list;
    },
    open(checkedData, rootNodeList, checkLimit, checkLimitTips) {
      this.identityTreeDialogVisible = true;
      this.rootNodeList = rootNodeList;
      this.checkLimit = checkLimit;
      this.checkLimitTips = checkLimitTips;

      this.identityTreeData = [];
      if(!this.rootNodeList || this.rootNodeList.length == 0){
        var rootChildren = this.treeDataMap['root'];

        this.identityTreeData.push({
          id: "root",
          name: "所有类型",
          children: rootChildren,
          disabled: true,
        });
      }else{
        this.rootNodeList.forEach(rootNode => {
          console.info(rootNode);
          this.identityTreeData.push({
            id: rootNode.id,
            name: rootNode.name,
            children: this.treeDataMap[rootNode.id],
            disabled: true,
          });
        });
      }

      //回选树节点
      if (checkedData) {
        //清空旧数据
        this.treeCheckedKey = [];
        this.identityTreeDefaultKey = [];
        if (checkedData.length > 0) {
          checkedData.forEach((item) => {
            this.identityTreeDefaultKey.push(item);
            this.treeCheckedKey.push(item);
          });

          if (this.$refs.identityTree != null) {
            console.info(checkedData);
            this.$refs.identityTree.setCheckedKeys(this.treeCheckedKey);
            let checkedNodes = this.$refs.identityTree.getCheckedNodes(false, true);
            checkedNodes.forEach((item) => (item.expanded = true));
          }
        }
      }
      this.$forceUpdate();
    },
    confirm() {
      if(this.checkLimit && this.$refs.identityTree.getCheckedNodes().length >  this.checkLimit){
        this.$message({
          message: this.checkLimitTips,
          type: 'warning'
        }); 
        return;
      }

      this.$emit('confirm', this.$refs.identityTree.getCheckedNodes(), this.relType);

      this.$refs.identityTree.setCheckedKeys([]);

      this.treeCheckedKey = [];
      this.identityTreeDefaultKey = [];

      this.identityTreeDialogVisible = false;
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    
    identityCheckChange(data, checked, indeterminate) {
      if (checked) {
        if(this.checkLimit && this.$refs.identityTree.getCheckedNodes().length >  this.checkLimit){
          this.$message({
            message: this.checkLimitTips,
            type: 'warning'
          }); 
          return false;
        }
      }
    },
  },
};
</script>