<template>
    <a-modal
      :title="title"
      :visible.sync="dialogFormVisible"
      width="800px"
      :dialog-style="{ top: '50px' }"
      @cancel="close"
    >     
      <div class="table_container">
        <div class="table_left">
              <a-col span="23">
                <a-input-search
                  v-model="filterText"
                  placeholder="输入关键字进行过滤"
                  @search="onChange"
                ></a-input-search>
              </a-col>
              <a-spin :indicator="indicator" :spinning="listLoading">
                <div id="app" style="min-height: 50px;">
                  <a-tree
                    ref="userTree"
                    v-model="userTreeDefaultKey"
                    checkable
                    :expanded-keys="expandedKeys"
                    multiple
                    :selectable="false"
                    :replace-fields="replaceFields"
                    :tree-data="userTreeData"
                    @expand="onExpand"
                    @check="onCheck"
                  >
                    <template slot="title" slot-scope="{ name }">
                      <span
                        v-if="searchValue && name.indexOf(searchValue) > -1"
                        style="color: #f50;"
                        >{{ name }}</span
                      >
                      <span v-else>{{ name }}</span>
                    </template>
                  </a-tree>
                </div>
              </a-spin>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
        style="position: relative; padding-right: 15px; text-align: right;"
      >
        <a-button @click="close">关闭</a-button>
        <a-button
          type="primary"
          :loading="loading"
          style="margin-left: 10px;"
          @click="confirm"
          >保存</a-button
        >
      </div>
    </a-modal>
  </template>
  <script>
  import { getOrgTreeByAdministrativeAreaId, settingAdministrativeAreaOrgTree } from "@/api/administrativeArea/dbDistrictTree";
  import { findTreeByCode } from "@/api/user";
  export default {
    props: {
      rootOrgId: {
        type: String,
        default: "root",
      },
    //   components: {
    //   },
    },
    data() {
      return {
        administrativeAreaId: '',
        loading: false,
        title: "设置范围",
        filterText: "",
        dialogFormVisible: false,
        //身份选择参数
        identityTypeUserList: [],
        checked: [],
        checkIdentyData: [],
        allChildData: [],
        //用户树参数
        userTreeData: [],
        // 已选择的用户树
        chooseTreeData: [],
        userTreeDefaultKey: [],
        replaceFields: {
          title: "name",
          key: "id",
          children: "children",
        },
        // 搜索值
        searchValue: "",
        // 展开的节点
        expandedKeys: [],
        backupsExpandedKeys: [],
        // 是否自动展开
        autoExpandParent: true,
        plainOptions: [], //人员数据
        listLoading: false,
        indicator: <a-icon type="loading" style="font-size: 24px" spin />,
        checkList: [],
        chooseTreeDefaultKey: [],
        expandedChooseKeys: [],
      };
    },
    created() {
      this.initUserTree();
    },
    methods: {
      // 树状搜索
      onChange(e) {
        console.log('点击了搜索框');
        this.listLoading = true;
        const value = e;
        this.searchValue = value;
        if (value == "") {
          this.listLoading = false;
          this.$message.info("请输入关键字");
        } else {
          this.searchValue = value;
          this.expandedKeys = [];
          this.backupsExpandedKeys = [];
          const candidateKeysList = this.getkeyList(value, this.userTreeData, []);
          if (candidateKeysList.length == 0) {
            this.$message.destroy();
            this.listLoading = false;
            this.$message.info("没有相关数据");
            return;
          }
          candidateKeysList.forEach((item) => {
            const key = this.getParentKey(item, this.userTreeData);
            if (key && !this.backupsExpandedKeys.some((item) => item === key))
              this.backupsExpandedKeys.push(key);
          });
          const { length } = this.backupsExpandedKeys;
          for (let i = 0; i < length; i++) {
            this.getAllParentKey(this.backupsExpandedKeys[i], this.userTreeData);
          }
          this.expandedKeys = this.backupsExpandedKeys.slice();
          this.listLoading = false;
        }
      },
      getkeyList(value, tree, keyList) {
        for (let i = 0; i < tree.length; i++) {
          const node = tree[i];
          if (node.name.indexOf(value) > -1) {
            keyList.push(node.id);
          }
          if (node.children) {
            this.getkeyList(value, node.children, keyList);
          }
        }
        return keyList;
      },
      // 该递归主要用于获取key的父亲节点的key值
      getParentKey(key, tree) {
        let parentKey;
        let temp;
        for (let i = 0; i < tree.length; i++) {
          const node = tree[i];
          if (node.children) {
            temp = this.getParentKey(key, node.children);
            if (node.children.some((item) => item.id === key)) {
              parentKey = node.id;
            } else if (temp) {
              parentKey = temp;
            }
          }
        }
        return parentKey;
      },
      // 获取该节点的所有祖先节点
      getAllParentKey(key, tree) {
        let parentKey;
        if (key) {
          parentKey = this.getParentKey(key, tree);
          if (parentKey) {
            if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
              this.backupsExpandedKeys.push(parentKey);
            }
            this.getAllParentKey(parentKey, tree);
          }
        }
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys;
        this.autoExpandParent = false;
      },
      onChooseExpand(expandedKeys) {
        this.expandedChooseKeys = expandedKeys;
      },
      // 获取机构树内容
      initUserTree() {
        this.chooseTreeData = [];
        this.userTreeData = [];
        this.expandedChooseKeys = [];
        this.userTreeDefaultKey = [];
        this.listLoading = true;
        findTreeByCode({code : "dbxx-tree"}).then((res) => {
          this.userTreeData = res.data;
          this.expandedChooseKeys = this.userTreeData
          this.listLoading = false;
        });

        this.getCheckedOrgTree() 
      },
      // 回显
      handleShow(administrativeAreaId) {
        let that = this;
        that.administrativeAreaId = administrativeAreaId
        that.$forceUpdate();
        that.dialogFormVisible = true;
        that.initUserTree()
      },
      onTreeShowList(data) {
        let list = data.filter(item => !item.children || (item.children && item.children.length === 0));
        return list
      },
      onSearch(data) {
        console.log('搜索',data);
      },
      // 树状选择
      onCheck(item, data) {
        this.checkList = [];
        this.checked = [];
        this.checked = item.concat(data.halfCheckedKeys);
        this.checkList = this.onTreeShowList(this.checked)
        this.checkList = this.checkList.map(item => { return item.name })
      },
      confirm() {
        let checkedData = [];
        let orgIds = [];
        orgIds = this.checked
        const data = {
          administrativeAreaId: this.administrativeAreaId,
          orgIds,
        }
        settingAdministrativeAreaOrgTree(data).then((res) => {
          this.$message.success(res.data)
          this.dialogFormVisible = false;
        })
      },
      close() {
        this.checkIdentyData = [];
        this.dialogFormVisible = false;
        this.filterText = "";
      },
      getCheckedOrgTree() {
        getOrgTreeByAdministrativeAreaId({administrativeAreaId: this.administrativeAreaId}).then((res) => {
          //回选旧数据
          this.getTreeChildren(res.data)
          this.userTreeDefaultKey = this.chooseTreeData
        })
      },
      // 1.循环遍历出最深层子节点，存放在一个数组中
      getTreeChildren(data){
        data&&data.map(item=>{
          if(item.children && item.children.length > 0){
            this.getTreeChildren(item.children);
          }else{
            this.chooseTreeData.push(item.id);
          };
          return null;
        });
        return this.chooseTreeData;
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  ::v-deep .ant-modal-body {
    padding: 0 24px;
  }
  .group_item {
    flex: 48%;
    // flex: 4.5;
    border: 1px solid #e8e8e8;
    padding: 10px;
    overflow-y: auto;
  }
  ::v-deep .ant-modal {
    // top: 30px;
    height: 650px;
    overflow: hidden;
  }
  .table_container {
    display: flex;
    width: 100%;
    height: 460px;
    padding-bottom: 10px;
    margin-top: 10px;
    .table_left {
      flex: 48%;
      border: 1px solid #e8e8e8;
      padding: 10px;
      padding-right: 0;
    }
    .table_icon {
      flex: 10%;
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 30px;
      justify-content: space-evenly;
      padding: 0 10px;
    }
    ::v-deep .ant-spin-nested-loading,
    .choose_list {
      padding: 10px;
      flex: 48%;
      .choose_tree {
        height: 100%;
      }
      // flex: 5;
    }
    ::v-deep .ant-spin-nested-loading {
      width: 100%;
      height: 400px;
      overflow-y: auto;
    }
  }
  .choose_list {
      border: 1px solid #e8e8e8;
    // padding: 0 20px;
    .list_table {
      width: 100%;
      height: 398px;
      overflow: auto;
      // border: 1px solid #e8e8e8;
      .list_all {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e8e8e8;
        padding: 5px 10px;
        .list_left {
          width: 30px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          border-radius: 50%;
          font-size: 12px;
          background-color: #d8293a;
          color: #fff;
          margin-right: 15px;
        }
        .list_right {
          color: #000;
        }
      }
    }
  }
  // ::v-deep .ant-spin-nested-loading {
  //   margin-top: 35px;
  // }
  .all_Num {
    font-size: 16px;
    font-weight: 700;
    /* color: #d92b3e; */
    text-align: left;
  }
  .loading-indicator {
    text-align: center;
  }
  ::v-deep span.ant-radio + * {
    padding-right: 8px;
    padding-left: 1px !important;
  }
  
  .el-checkbox.is-bordered.el-checkbox--mini {
    margin-bottom: 5px;
    margin-left: 10px;
  }
  
  ::v-deep .ant-tree {
    max-height: 500px;
    width: 100%;
    overflow-y: auto;
  }
  
  ::v-deep .cjry .radioStyle span.ant-radio + * {
    padding-left: 0px;
    padding-right: 30px;
  }
  
  ::v-deep .sortOrder span.ant-radio + * {
    padding: 0 8px !important;
  }
  .import_style {
    ::v-deep .ant-modal-body {
      height: 455px;
      overflow: scroll;
    }
  }
  /* 表格斑马样式 */  
  ::v-deep .ant-table-tbody tr:nth-child(2n) {  
    background-color: #fafafa;  
  }
  </style>
  