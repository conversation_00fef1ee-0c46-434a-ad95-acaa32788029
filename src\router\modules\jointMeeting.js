import Layout from "@/layouts";

// {
//   path: "staging",
//   name: "InformStaging",
//   component: () => import("@/views/meeting/testPage/index"),
//   meta: {
//     title: "待办任务",
//     permissions: ["I_XTGLY", "I_XXFBY"],
//   },
// },
// 联系人大常委会
export default [
  {
    // path: "/inform",
    path: "/inform1",
    component: Layout,
    redirect: "noRedirect",
    name: "inform1",
    title: "联系人大常委会",
    meta: {
      title: "首页",
      icon: "database",
      permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "InformList",
        name: "InformList",
        component: () => import("@/views/meeting/inform/lxIndex"),
        meta: {
          title: "代表首页",
          permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },
      {
        path: "wordhomeIndex",
        name: "wordhomeIndex",
        component: () => import("@/views/meeting/inform/wordhomeIndex"),
        meta: {
          title: "工作人员首页",
        },
      },
    ],
  },

  //通知公告
  {
    path: "/inform",
    component: Layout,
    redirect: "noRedirect",
    name: "Inform",
    title: "联系人大常委会",
    meta: {
      title: "通知公告",
      icon: "database",
      permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "list",
        name: "list",
        component: () => import("@/views/meeting/inform/list"),
        meta: {
          title: "通知公告列表",
          permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },
      {
        path: "Newlist",
        name: "newInformList",
        hidden: true,
        component: () => import("@/views/meeting/inform/Newlist"),
        meta: {
          title: "区人大通知公告列表",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
      },
      {
        path: "edit",
        name: "InformEdit",
        hidden: true,
        component: () => import("@/views/meeting/inform/edit2"),
        meta: {
          title: "新增通知公告",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
      },
      {
        path: "referRemind",
        name: "referRemind",
        hidden: true,
        component: () => import("@/views/meeting/inform/referRemind"),
        meta: {
          title: "查阅提醒",
          //  permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },

      {
        path: "/inform/Data",
        name: "InformEditStatistics",
        component: () => import("@/views/meeting/inform/data"),
        meta: {
          title: "查看详情",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/inform/statistics",
        name: "InformStatistics",
        component: () => import("@/views/meeting/inform/statisticsList"),
        meta: {
          title: "通知统计",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },
    ],
  },


  //府院通报
  {
    path: "/bulletin",
    component: Layout,
    redirect: "noRedirect",
    name: "Bulletin",
    title: "联系人大常委会",
    meta: {
      title: "府院通报",
      icon: "database",
    },
    alwaysShow: false,
    children: [
      {
        path: "bulletinList",
        name: "bulletinList",
        component: () => import("@/views/meeting/bulletin/list"),
        meta: {
          title: "府院通报列表",
        },
      },

      {
        path: "bulletinEdit",
        component: () => import("@/views/meeting/bulletin/edit"),
        meta: {
          title: "府院通报",
        },
        hidden: true,
      },

    ],
  },

  //履职周报
  {
    path: "/zhou",
    component: Layout,
    redirect: "noRedirect",
    name: "Inform",
    title: "联系人大常委会",
    meta: {
      title: "履职周报",
      icon: "database",
    },
    alwaysShow: false,
    children: [
      {
        path: "weekDatalist",
        name: "weekDatalist",
        component: () => import("@/views/meeting/weekData/list"),
        meta: {
          title: "周报信息",
        },
      },
      {
        path: "weekDataInformedit",
        component: () => import("@/views/meeting/weekData/edit"),
        meta: {
          title: "新增周报",
        },
        hidden: true,
      },
      // {
      //   path: "weekDataInformedit2",
      //   component: () => import("@/views/meeting/weekData/edit2"),
      //   meta: {
      //     title: "新增周报2",
      //   },
      //   hidden: true,
      // },
      {
        path: "mySubmission",
        name: "mySubmission",
        component: () => import("@/views/meeting/mySubmission/list"),
        meta: {
          title: "我的投稿",
        },
      },
      {
        path: "mySubmissionAdmin",
        name: "mySubmissionAdmin",
        component: () => import("@/views/meeting/mySubmission/listAdmin"),
        meta: {
          title: "代表投稿管理",
        },
      },
      {
        path: "mySubmissionAdd",
        name: "mySubmissionAdd",
        component: () => import("@/views/meeting/mySubmission/edit"),
        meta: {
          title: "我的投稿新增",
        },
        hidden: true,
      },
      {
        path: "/inform/statisticsweekData",
        name: "InformStatistics",
        component: () => import("@/views/meeting/weekData/statisticsList"),
        meta: {
          title: "通知统计",
        },
        hidden: true,
      },
    ],
  },

  //急难愁盼
  // {
  //   path: "/hardsolve",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "Inform",
  //   title: "联系人大常委会",
  //   meta: {
  //     title: "急难愁盼",
  //     icon: "database",
  //   },
  //   alwaysShow: false,
  //   children: [
  //     {
  //       path: "hardSolveList",
  //       name: "hardSolveList",
  //       component: () => import("@/views/hardsolve/list"),
  //       meta: {
  //         title: "急难愁盼管理",
  //       },
  //     },
  //     {
  //       path: "myHardSolveList",
  //       name: "myHardSolveList",
  //       component: () => import("@/views/hardsolve/myList"),
  //       meta: {
  //         title: "我的急难愁盼投稿",
  //       },
  //     },
  //     {
  //       path: "info",
  //       name: "info",
  //       component: () => import("@/views/hardsolve/info"),
  //       meta: {
  //         title: "我的投稿新增",
  //       },
  //       hidden: true,
  //     },
  //   ]
  // },

  //主任会议组成人员联系代表
  {
    path: "/ComponentData",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "联系人大常委会",
    meta: {
      title: "主任会议组成成员联系代表",
    },
    children: [
      {
        path: "relationship",
        name: "relationship",
        component: () =>
          import("@/views/meeting/meetingDirectors/relationship"),
        meta: {
          title: "建立联系",
        },
      },
      {
        path: "management",
        name: "management",
        component: () => import("@/views/meeting/meetingDirectors/management"),
        meta: {
          title: "联系管理",
        },
      },
      {
        path: "informationSearch",
        name: "informationSearch",
        hidden: true,
        component: () =>
          import("@/views/meeting/meetingDirectors/informationSearch"),
        meta: {
          title: "联系情况查阅",
        },
      },
    ],
  },

  //常委会组成人员联系代表
  {
    path: "/ComponentValue",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "联系人大常委会",
    meta: {
      title: "常委会组成人员联系代表",
    },
    children: [
      {
        path: "relationship1",
        name: "relationship1",
        component: () =>
          import("@/views/meeting/standingCommittee/relationship"),
        meta: {
          title: "建立联系",
        },
      },
      {
        path: "management1",
        name: "management1",
        component: () => import("@/views/meeting/standingCommittee/management"),
        meta: {
          title: "联系管理",
        },
      },
      {
        path: "informationSearch1",
        name: "informationSearch1",
        hidden: true,
        component: () =>
          import("@/views/meeting/standingCommittee/informationSearch"),
        meta: {
          title: "联系情况查阅",
        },
      },
    ],
  },
  {
    path: "/ComponentDataDw",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "联系人大常委会",
    meta: {
      title: "代表联系民营企业",
    },
    children: [
      {
        path: "relationshipDw",
        name: "relationshipDw",
        component: () =>
          import("@/views/meeting/meetingDirectors/relationshipDw"),
        meta: {
          title: "建立联系",
        },
      },
      {
        path: "managementDw",
        name: "managementDw",
        component: () =>
          import("@/views/meeting/meetingDirectors/managementDw"),
        meta: {
          title: "联系管理",
        },
      },
        {
        path: "systemManagement",
        name: "systemManagement",
        component: () =>
          import("@/views/meeting/meetingDirectors/systemManagement"),
        meta: {
          title: "制度管理",
        },
      },
      {
        path: "informationSearch",
        name: "informationSearch",
        hidden: true,
        component: () =>
          import("@/views/meeting/meetingDirectors/informationSearch"),
        meta: {
          title: "联系情况查阅",
        },
      },
    ],
  },
  //社情民意专报
  {
    path: "/through",
    component: Layout,
    name: "through",
    title: "联系人大常委会",
    meta: {
      title: "社情民意专报",
    },
    children: [
      {
        path: "throughIndex",
        name: "throughIndex",
        component: () => import("@/views/through/throughIndex"),
        meta: {
          title: "社情民意专报列表",
        },
      },
      {
        path: "throughAdd",
        name: "throughAdd",
        component: () => import("@/views/through/add"),
        meta: {
          title: "新增社情民意专报列表",
        },
        hidden: true,
      },
      {
        path: "throughAdmin",
        name: "throughAdmin",
        component: () => import("@/views/through/throughAdmin"),
        meta: {
          title: "社情民意专报管理列表",
        },
      },
      {
        path: "/throughAdmin/details",
        name: "throughAdmindetails",
        component: () => import("@/views/through/details"),
        meta: {
          title: "社情民意专报详情",
        },
        hidden: true,
      },
      {
        path: "handledetails",
        name: "handledetails",
        component: () => import("@/views/through/handledetails"),
        meta: {
          title: "社情民意专报处理",
        },
        hidden: true,
      },
    ],
  },

  //系统管理
  {
    path: "/systemData",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "联系人大常委会",
    meta: {
      title: "系统管理",
      permissions: ["ADMIN", "I_XTGLY"],
    },
    children: [
      {
        path: "logStatistics",
        name: "logStatistics",
        component: () => import("@/views/logStatistics/index"),
        meta: {
          title: "日志统计",
        },
      },
      // +
      {
        path: "newlogStatistics",
        name: "newlogStatistics",
        component: () => import("@/views/logStatistics/PClogStatistics"),
        meta: {
          title: "日志统计", //pc
        },
      },
      {
        path: "XCXlogStatistics",
        name: "XCXlogStatistics",
        component: () => import("@/views/logStatistics/XCXlogStatistics"),
        meta: {
          title: "小程序日志统计",
        },
        hidden: true,
      },
      {
        path: "Statistics",
        name: "Statistics",
        component: () => import("@/views/logStatistics/Statistics"),
        meta: {
          title: "日志统计详情", //中间圆形
        },
        hidden: true,
      },
      {
        path: "pcDLnumData",
        name: "pcDLnumData",
        component: () => import("@/views/logStatistics/pcDLnumData"),
        meta: {
          title: "日志统计详情", //PC  登录
        },
        hidden: true,
      },
      // {
      //   path: "pcDJnumData",
      //   name: "pcDJnumData",
      //   component: () => import("@/views/logStatistics/pcDJnumData"),
      //   meta: {
      //     title: "日志统计详情",  //PC操作点击
      //   },
      //   hidden: true,
      // },
      {
        path: "pcXTDJnumData",
        name: "pcXTDJnumData",
        component: () => import("@/views/logStatistics/pcXTDJnumData"),
        meta: {
          title: "日志统计详情", //PC操作点击
        },
        hidden: true,
      },
      {
        path: "iChartData",
        name: "iChartData",
        component: () => import("@/views/logStatistics/iChartData"),
        meta: {
          title: "日志统计详情", //小程序
        },
        hidden: true,
      },

      {
        path: "synchronousManagement",
        name: "synchronousManagement",
        component: () => import("@/views/meeting/synchronousManagement/index"),
        meta: {
          title: "数据同步管理",
        },
      },
      {
        path: "session",
        name: "session",
        component: () => import("@/views/election/systemIndex/electionMage"),
        // component: () => import("@/views/meeting/session/index"),
        meta: {
          title: "届次管理",
        },
      },
      {
        path: "gradingManagement",
        name: "gradingManagement",
        component: () =>
          import("@/views/election/systemIndex/gradingManagement"),
        // component: () => import("@/views/meeting/gradingManagement/index"),
        meta: {
          title: "分级管理",
        },
      },
      {
        path: "meetingEdit",
        name: "MeetingEdit",
        component: () => import("@/views/meeting/edit"),
        meta: {
          title: "数据权限管理",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "menuManage",
        name: "MenuManage",
        component: () => import("@/views/menu/index"),
        meta: {
          title: "菜单管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "userManage",
        name: "UserManage",
        component: () => import("@/views/user/index"),
        meta: {
          title: "用户管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },

      {
        path: "roleManage",
        name: "RoleManage",
        component: () => import("@/views/role/index"),
        meta: {
          title: "角色管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "editRole",
        name: "EditRole",
        component: () => import("@/views/role/edit"),
        meta: {
          title: "编辑角色",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },

      {
        path: "parameterManagement",
        name: "parameterManagement",
        component: () => import("@/views/meeting/parameterManagement/index"),
        meta: {
          title: "参数管理",
        },
      },
      {
        path: "relationshipConfig",
        name: "SystemRelationshipConfig",
        component: () =>
          import("@/views/meeting/meetingDirectors/relationshipConfig"),
        meta: {
          title: "代表双边联系管理",
        },
      },
    ],
  },

  // //意见征集
  // {
  //   path: "/opinion",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "Opinion",
  //   title: "联系人大常委会",
  //   meta: {
  //     title: "意见征集",
  //     icon: "database",
  //     permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
  //   },
  //   alwaysShow: false,
  //
  //   children: [
  //     // 意见征集列表
  //     {
  //       path: "YKOpinionUserList",
  //       name: "YKOpinionUserList",
  //       component: () => import("@/views/meeting/opinion/YKOpinionUserList"),
  //       meta: {
  //         title: "意见征集游客列表",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //     },
  //     {
  //       path: "OpinionTouristList",
  //       name: "OpinionTouristList",
  //       component: () => import("@/views/meeting/opinion/OpinionTouristList"),
  //       meta: {
  //         title: "意见征集游客查看本人列表",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //     },
  //     //
  //     {
  //       path: "GZOpinionTouristList",
  //       name: "GZOpinionTouristList",
  //       component: () => import("@/views/meeting/opinion/YKoption/index"),
  //       meta: {
  //         title: "公众查看意见征集",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //     },
  //     //
  //     {
  //       path: "OpinionUserList",
  //       name: "OpinionUserList",
  //       component: () => import("@/views/meeting/opinion/OpinionUserList"),
  //       meta: {
  //         title: "意见征集列表",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //     },
  //     {
  //       path: "OpinionList1",
  //       name: "OpinionList1",
  //       component: () => import("@/views/meeting/opinion/listnew"),
  //       meta: {
  //         title: "意见征集管理列表",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //     },
  //     {
  //       path: "OpinionAddnew",
  //       name: "OpinionAddnew",
  //       component: () => import("@/views/meeting/opinion/OpinionAddnew"),
  //       meta: {
  //         title: "意见征集添加",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //       hidden: true,
  //     },
  //     {
  //       path: "OpinionEditnew",
  //       name: "OpinionEditnew",
  //       component: () => import("@/views/meeting/opinion/OpinionEditnew"),
  //       meta: {
  //         title: "意见征集填写",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //       hidden: true,
  //     },
  //
  //     // ++
  //     {
  //       path: "YKOpinionEditnew",
  //       name: "YKOpinionEditnew",
  //       component: () => import("@/views/meeting/opinion/YKOpinionEditnew"),
  //       meta: {
  //         title: "意见征集填写(游客)",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //       hidden: true,
  //
  //     },
  //     // ?
  //     {
  //       path: "OpinionSeeResultsnew",
  //       name: "OpinionSeeResultsnew",
  //       component: () => import("@/views/meeting/opinion/OpinionSeeResultsnew"),
  //       meta: {
  //         title: "意见征集查看意见",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //       hidden: true,
  //     },
  //     {
  //       path: "opinionStatisticsDetailnew",
  //       name: "opinionStatisticsDetailnew",
  //       component: () =>
  //         import("@/views/meeting/opinion/opinionStatisticsDetailnew"),
  //       meta: {
  //         title: "意见征集查看统计意见",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //       hidden: true,
  //     },
  //     {
  //       path: "opinionStatisticsDetailIndividualsnew",
  //       name: "opinionStatisticsDetailIndividualsnew",
  //       component: () =>
  //         import(
  //           "@/views/meeting/opinion/opinionStatisticsDetailIndividualsnew"
  //           ),
  //       meta: {
  //         title: "意见征集查看详情意见",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //       hidden: true,
  //     },
  //     // ////////
  //     {
  //       path: "OpinionList",
  //       name: "OpinionList",
  //       component: () => import("@/views/meeting/opinion/list"),
  //       meta: {
  //         title: "意见征集列表旧",
  //         permissions: ["I_XTGLY", "I_YJZJGLY"],
  //       },
  //     },
  //     {
  //       path: "NewlistS",
  //       name: "NewOpinionList",
  //       component: () => import("@/views/meeting/opinion/Newlist"),
  //       meta: {
  //         title: "区人大意见征集列表",
  //         permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
  //       },
  //     },
  //     {
  //       path: "OpinionEdit",
  //       name: "OpinionEdit",
  //       component: () => import("@/views/meeting/opinion/edit"),
  //       meta: {
  //         title: "新增意见征集",
  //         permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
  //       },
  //     },
  //     {
  //       path: "/opinion/tongji",
  //       name: "OpinionTongji",
  //       component: () => import("@/views/meeting/opinion/tongji"),
  //       meta: {
  //         title: "征集统计",
  //         permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
  //       },
  //       hidden: true,
  //     },
  //     {
  //       path: "/opinion/data",
  //       name: "OpinionData",
  //       component: () => import("@/views/meeting/opinion/data"),
  //       meta: {
  //         title: "意见详情",
  //         permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
  //       },
  //       hidden: true,
  //     },
  //     {
  //       path: "callComments",
  //       name: "callComments",
  //       component: () => import("@/views/meeting/opinion/callComments"),
  //       meta: {
  //         title: "查询意见征集",
  //         // permissions: ["I_XTGLY", "I_XXFBY"],
  //       },
  //     },
  //   ],
  // },
  //
  //调查问卷
  {
    path: "/investigation",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "联系人大常委会",
    meta: {
      title: "调查问卷",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "GZsurveyList",
        name: "GZsurveyList",
        component: () => import("@/views/survey/YKsurveys/index"),
        meta: {
          title: "公众查看调查问卷",
          permissions: ["I_XTGLY", "I_YJZJGLY"],
        },
      },
  //     // 1026

      {
        path: "survey",
        name: "survey",
        component: () => import("@/views/survey/survey"),
        meta: {
          title: "新问卷调查管理",
        },
      },
      {
        path: "mySurvey",
        name: "mySurvey",
        component: () => import("@/views/survey/mySurvey"),
        meta: {
          title: "新问卷调查个人",
        },
      },
      {
        path: "YKmySurvey",
        name: "YKmySurvey",
        component: () => import("@/views/survey/YKmySurvey"),
        meta: {
          title: "新问卷调查游客",
        },
        hidden: true,
      },
    // ]
  // },
      {
        path: "YKpreview",
        name: "YKpreview",
        component: () => import("@/views/survey/YKpreview"),
        meta: {
          title: "调查问卷预览游客",
        },
        hidden: true,
      },
      {
        path: "design",
        name: "design",
        component: () => import("@/views/survey/design"),
        meta: {
          title: "问卷设计",
        },
        hidden: true,
      },
      {
        path: "preview",
        name: "preview",
        component: () => import("@/views/survey/preview"),
        meta: {
          title: "问卷预览",
        },
        hidden: true,
      },
      {
        path: "sjpreview",
        name: "sjpreview",
        component: () => import("@/views/survey/sjpreview"),
        meta: {
          title: "设计问卷预览",
        },
        hidden: true,
      },
      {
        path: "eachQuestionnaireStatistics",
        name: "eachQuestionnaireStatistics",
        component: () => import("@/views/survey/eachQuestionnaireStatistics"),
        meta: {
          title: "每个问卷统计",
        },
        hidden: true,
      },
      {
        path: "eachQuestionStatisticsCorrection",
        name: "eachQuestionStatisticsCorrection",
        component: () => import("@/views/survey/eachQuestionStatisticsCorrection"),
        meta: {
          title: "每个考试问卷用户统计",
        },
        hidden: true,
      },
      {
        path: "QuestionnaireStatistics",
        name: "QuestionnaireStatistics",
        component: () => import("@/views/survey/QuestionnaireStatistics"),
        meta: {
          title: "问卷统计",
        },
        hidden: true,
      },
      {
        path: "questionnaire",
        name: "questionnaire",
        component: () =>
          import("@/views/onlineLearningTraining/questionnaireList"),
        meta: {
          title: "调查问卷",
        },
      },
      {
        path: "InvestigationManagement",
        name: "InvestigationManagement",
        component: () =>
          import("@/views/meeting/diaochawenjuan/InvestigationManagement"),
        // component: () => import("@/views/onlineLearningTraining/InvestigationManagement"),
        meta: {
          title: "调查问卷管理",
        },
      },
      {
        path: "ProblemModel",
        name: "ProblemModel",
        component: () => import("@/views/meeting/diaochawenjuan/ProblemModel"),
        meta: {
          title: "问题模型",
        },
        hidden: true,
      },
    ]
  },


  // {
  //   path: "/InformationBase",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "representative",
  //   title: "联系人大常委会",
  //   meta: {
  //     title: "代表资讯库",
  //   },
  //   children: [
  //     {
  //       path: "asDynamic",
  //       name: "asDynamic",
  //       component: () => import("@/views/meeting/Information/asDynamic"),
  //       meta: {
  //         title: "人大工作动态",
  //       },
  //     },
  //     {
  //       path: "addAsDynamic",
  //       name: "addAsDynamic",
  //       component: () => import("@/views/meeting/Information/addAsDynamic"),
  //       meta: {
  //         title: "增加人大工作动态",
  //       },
  //       hidden: true,
  //     },
  //     {
  //       path: "zhengYuanWork",
  //       name: "zhengYuanWork",
  //       component: () => import("@/views/meeting/Information/zhengYuanWork"),
  //       meta: {
  //         title: "政院工作动态",
  //       },
  //     },
  //   ],
  // },
];
