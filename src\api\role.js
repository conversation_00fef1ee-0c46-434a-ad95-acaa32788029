import request from "@/utils/request";
import qs from "qs";

export function doSave(data) {
  return request({
    url: "/api/v1/role/manage/add",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/role/manage/update",
    method: "post",
    data,
  });
}

export function getPage(data) {
  return request({
    url: "/api/v1/role/manage/findPage",
    method: "post",
    data,
  });
}

export function getList(data) {
  return request({
    url: "/api/v1/role/manage/findList",
    method: "post",
    data,
  });
}

export function getTree(data) {
  return request({
    url: "/api/v1/role/manage/findTree",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/role/manage/remove",
    method: "post",
    data,
  });
}

export function doChangeSort(data) {
  return request({
    url: "/api/v1/role/manage/changeSort",
    method: "post",
    data,
  });
}

export function getById(data) {
  return request({
    url: "/api/v1/role/manage/findById",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function authorizeByRole(data, roleId) {
  return request({
    url: "/api/v1/role/manage/authorizeByRole/reset/" + roleId,
    method: "post",
    data,
  });
}

export function authorizeByUser(data, userId) {
  return request({
    url: "/api/v1/role/manage/authorizeByObject/reset/0/" + userId,
    method: "post",
    data,
  });
}

export function authorizeByOrg(data, orgId) {
  return request({
    url: "/api/v1/role/manage/authorizeByObject/reset/1/" + orgId,
    method: "post",
    data,
  });
}

export function authorizeByUserGroup(data, groupId) {
  return request({
    url: "/api/v1/role/manage/authorizeByObject/reset/2/" + groupId,
    method: "post",
    data,
  });
}

export function findRolesByObject(data) {
  return request({
    url: "/api/v1/role/manage/findRolesByObject",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function findScopeByRoleId(data) {
  return request({
    url: "/api/v1/role/manage/findScopeByRoleId",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function findScopeByRole(data) {
  return request({
    url: "/api/v1/role/manage/findScopeByRole",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function saveScope(data, orgId) {
  return request({
    url: "/api/v1/role/manage/saveScope",
    method: "post",
    data,
  });
}
