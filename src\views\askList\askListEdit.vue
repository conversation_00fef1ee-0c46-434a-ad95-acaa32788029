<template>
  <a-modal :visible.sync="visible" width="60%" :title="title" @cancel="close">
    <a-form-model
      ref="resourceForm"
      :model="resourceForm"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-row>
        <a-col span="24">
          <a-form-model-item label="询问标题" prop="inquiryTitle">
            <a-input
              v-model="resourceForm.inquiryTitle"
              autocomplete="off"
              :disabled="title == '查看'"
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col span="24">
          <a-form-model-item label="询问内容" prop="inquiryContent">
            <a-input
              v-model="resourceForm.inquiryContent"
              autocomplete="off"
              :disabled="title == '查看'"
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col span="24">
          <a-form-model-item label="询问附件">
            <a-upload
              v-if="title != '查看'"
              v-model="resourceForm.attid"
              action=""
              :remove="handleTextRemove"
              @change="uploadChange($event, '32')"
              :beforeUpload="
                () => {
                  return false;
                }
              "
              :fileList="fileTextList"
            >
              <a-button type="primary" :disabled="fileTextList.length === 1"
                >点击上传</a-button
              >
              <div v-show="fileTextList.length < 1">
                只能上传doc,docx文件，且不超过20mb
              </div>
            </a-upload>
            <a-button type="link" v-else @click="downFileData(fileTextList)">{{
              fileTextList[0] ? fileTextList[0].name : ""
            }}</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <div slot="footer" class="dialog-footer" v-if="title != '查看'">
      <a-button @click="close">取 消</a-button>
      <a-button type="primary" @click="add">确 定</a-button>
    </div>
  </a-modal>
</template>

<script>
import StandardTable from "@/components/table/StandardTable";
import { myPagination } from "@/mixins/pagination.js";
import { askSaveOrUpdate, askGetById } from "@/api/myJob/myProposal.js";
import { fileUpload } from "@/api/commonApi/file.js";
import { downFile } from "@/api/commonApi/file.js";

export default {
  name: "AskList",
  components: {
    StandardTable,
  },
  // 引入分页器配置
  mixins: [myPagination],
  data() {
    return {
      id: "",
      title: "编辑",
      visible: false, // 建议详情弹出框显示标记
      fileTextList: [],
      resourceForm: {},
      rules: {
        inquiryTitle: [
          { required: true, message: "请输入询问标题", trigger: "blur" },
        ],
        inquiryContent: [
          { required: true, message: "请输入询问内容", trigger: "blur" },
        ],
      },
    };
  },
  created() {},
  methods: {
    // 下载
    downFileData(fileList) {
      // 下载附件
      let form = {};
      form.type = "32";
      form.relId = fileList[0].id;
      downFile(form).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = fileList[0].name;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 关闭
    close() {
      this.visible = false;
      this.resourceForm = {};
      this.fileTextList = [];
      this.$emit("reset");
    },
    // 新增/编辑
    add() {
      this.$refs["resourceForm"].validate((valid) => {
        if (valid) {
          askSaveOrUpdate(this.resourceForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.close();
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 获取数据
    getDataById() {
      askGetById({ id: this.id }).then((res) => {
        this.resourceForm = Object.assign({}, this.resourceForm, res.data.data);
        this.fileTextList.push({
          uid: "-1",
          id: this.resourceForm.id,
          name: this.resourceForm.attName,
          status: "done",
          attId: this.resourceForm.attId,
        });
        this.$forceUpdate();
      });
    },
    // 删除文件
    handleTextRemove() {
      this.fileTextList = [];
      this.resourceForm.attName = "";
      this.resourceForm.attId = "";
    },
    // 上传操作
    uploadChange(val, typeNum) {
      if (val.file.status == "removed") return;
      let formData = new FormData();
      formData.append("file", val.file);
      formData.append("type", typeNum);
      fileUpload(formData).then((res) => {
        this.resourceForm.attId = res.data.data[0].attId;
        this.resourceForm.attName =
          res.data.data[0].attName + "." + res.data.data[0].attSuffix;
        this.fileTextList = val.fileList;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
  margin-bottom: 10px;
}
.fold {
  width: calc(100% - 216px);
  display: inline-block;
}
.operator {
  margin-left:10px ;
  margin-bottom: 18px;
}
.mt2 {
  margin-top: 36px;
}
@media screen and (max-width: 900px) {
  .fold {
    width: 100%;
  }
}
</style>
