<template>
  <div>
    <!-- 代表选择dialog -->
    <el-dialog
      title="选择代表"
      :visible.sync="selectMemberDialogVisible"
      width="50%"
      min-height
      :close-on-click-modal="false"
      top="20vh"
      center
    >
      <a-col :xs="24">
        <a-row :gutter="10">
          <a-col :span="12">
            <el-card :style="{ height: '400px', overflow: 'auto' }">
              <a-row style="margin-bottom: 15px">
                <a-col :span="15">
                  <el-input
                    v-model="filterText"
                    placeholder="输入关键字进行过滤"
                    clearable
                    @input="$forceUpdate()"
                  ></el-input>
                </a-col>
                <a-col :span="8" style="margin-left: 10px">
                  <el-button icon="el-icon-search" @click="handleFilterText"
                    >搜索</el-button
                  >
                </a-col>
              </a-row>
              <el-tree
                ref="userTree"
                v-loading="loading"
                :data="userTreeData"
                :props="userTreeProps"
                node-key="uniqueId"
                show-checkbox
                :check-on-click-node="true"
                :default-checked-keys="userTreeDefaultKey"
                :filter-node-method="filterNode"
                @check-change="userCheckChange"
                @node-expand="treeClickHandle"
              ></el-tree>
              <!-- :default-expanded-keys="userTreeExpandedKey" -->
            </el-card>
          </a-col>
          <a-col :span="12">
            <el-card style="height: 400px; overflow: auto">
              <div v-for="item in checkUsers" :key="item.uniqueId">
                <el-card
                  shadow="hover"
                  body-style="padding:10px;background: #e8f3fe;color:#7dbcfc"
                >
                  <div style="">
                    {{ "代表 " + item.name }}
                  </div>
                </el-card>
              </div>
            </el-card>
          </a-col>
        </a-row>
      </a-col>
      <span slot="footer" class="dialog-footer">
        <el-button @click="selectMemberDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  findDbOrgUserTreeByRoleForLiaisonOrderByXsbh
} from "@/api/registrationMage/tableIng.js";

export default {
  props: {
    userTreeDialogTitle: {
      type: String,
      default: "选择关联人员",
    },
    rootOrgId: {
      type: String,
      default: "root",
    },
    showCheckbox: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      filterText: "",
      selectMemberDialogVisible: false,
      treeCheckedKey: [],
      userTreeData: [],
      userTreeDefaultKey: [],
      userTreeExpandedKey: [],
      userTreeProps: {
        children: "children",
        label: "name",
      },
      isRadio: false,
      checkMembers: [],
      loading: false,
    };
  },
  computed: {
    checkUsers() {
      return this.checkMembers.filter((it) => it.itemType == 2);
    },
  },
  created() {
    // this.initUserTree();
  },
  methods: {
    initUserTree() {
      // let data = {
      //   tenantId: this.$tenantId,
      //   rootOrgId: this.rootOrgId,
      // };
      this.loading = true;
      return new Promise((resolve, reject) => {
        findDbOrgUserTreeByRoleForLiaisonOrderByXsbh({}).then((res) => {
          this.userTreeData = res.data.data;
          this.loading = false;
          resolve("");
        });
      });
    },
    handleFilterText() {
      this.$refs.userTree.filter(this.filterText);
    },
    openTree(checkedData) {
      return new Promise((resolve, reject) => {
        //回选树节点
        if (checkedData) {
          //清空旧数据
          this.treeCheckedKey = [];
          this.userTreeDefaultKey = [];
          if (checkedData.length > 0) {
            checkedData.forEach((item) => {
              this.userTreeDefaultKey.push(item);
              this.treeCheckedKey.push(item);
            });

            if (this.$refs.userTree != null) {
              this.$refs.userTree.setCheckedKeys(this.treeCheckedKey);
            }
          }
        }
        resolve();
      });
    },
    async open(checkedIds) {
      this.selectMemberDialogVisible = true;

      await this.initUserTree()
        .then(() => {
          this.openTree(checkedIds).then(() => {
            this.$refs.userTree.setCheckedKeys(checkedIds);
            this.checkMembers = this.$refs.userTree.getCheckedNodes();
          });
        })
        .catch((res) => {
          console.log(res);
        });
    },
    userCheckChange(data, checked, indeterminate) {
      this.checkMembers = this.$refs.userTree.getCheckedNodes();
    },
    confirm() {
      let checkedNodes = this.$refs.userTree
        .getCheckedNodes()
        .filter((it) => it.itemType == 2);
      this.$emit("confirm", checkedNodes);

      this.$refs.userTree.setCheckedKeys([]);

      this.treeCheckedKey = [];
      this.userTreeDefaultKey = [];

      this.selectMemberDialogVisible = false;
    },

    close() {
      this.$refs.userTree.setCheckedKeys([]);
      this.treeCheckedKey = [];
      this.userTreeDefaultKey = [];
      this.HandleExpand();
      this.selectMemberDialogVisible = false;
    },

    //事件触发全部收缩
    HandleExpand() {
      let that = this;
      let treeList = this.userTreeData;
      for (let i = 0; i < treeList.length; i++) {
        that.$refs.userTree.store.nodesMap[
          treeList[i].uniqueId
        ].expanded = false;
        this.closeChildren(
          that.$refs.userTree.store.nodesMap[treeList[i].uniqueId]
        );
      }
    },

    closeChildren(node) {
      if (this.hasChildren) {
        node.childNodes.forEach((ele) => {
          ele.expanded = false;
          if (this.hasChildren(ele)) {
            this.closeChildren(ele);
          }
        });
      }
    },

    hasChildren(node) {
      return node.childNodes != null;
    },

    treeClickHandle(data, node, tag) {
      tag.expanded = !tag.expanded;
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
  },
};
</script>

<style></style>
