<template>
  <div class="table-container">
    <div>
      <div style="width: 100%; display: flex; height: 20%;">
        <div style="width: 75%; height: 100%;">
          <div class="jbxx-contt">
            <div class="jbqkss"><span class="jbqkbz">新增功能</span></div>
            <!-- <div class="fengexian"></div> -->
            <div style="padding: 18px;">
              <a-row>
                <a-col v-for="(item, index) in AddList"
                       :key="index"
                       :span="8"
                       @click="clickIndex(item)">
                  <div class="jbxxson_Big">
                    <img :src="item.img"
                         :alt="item.name" />
                    <div class="jbxxson_Big_box">
                      <div class="tits">
                        {{ item.name }}
                        <span v-show="item.num != null"
                              class="Numbers">({{ item.num }})</span>
                      </div>
                      <!-- <span style="font-size: 16px;font-weight: 400;letter-spacing: 0px;color: rgba(56, 56, 56, 1);">个</span> -->
                      <div style=""
                           class="remarks"
                           v-html="item.remarks"></div>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
        <div style="width: 26.5%; height: 100%;">
          <div class="jbxx-contt">
            <div class="jbqkss"><span class="jbqkbz">快捷按钮</span></div>
            <!-- <div class="fengexian"></div> -->
            <div style="padding: 18px; 0">
              <a-row>
                <a-col v-for="(item, index) in ToDoList"
                       :key="index"
                       @click="clickIndex(item)">
                  <div class="jbxxson_Big">
                    <img :src="item.img"
                         :alt="item.name" />
                    <div class="jbxxson_Big_box">
                      <div class="tits">
                        {{ item.name }}
                        <span v-show="item.num != null"
                              class="Numbers">({{ item.num }})</span>
                      </div>
                      <!-- <span style="font-size: 16px;font-weight: 400;letter-spacing: 0px;color: rgba(56, 56, 56, 1);">个</span> -->
                      <div style=""
                           class="remarks"
                           v-html="item.remarks"></div>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
      </div>

      <div class="jbxx-contt"
           style="padding-bottom: 20px;">
        <div class="jbqkss"><span class="jbqkbz">数据查询</span></div>
        <div>
          <a-row>
            <a-col v-for="(item, index) in DataqueryList"
                   :key="index"
                   :span="4"
                   @click="clickIndex(item)">
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.img"
                     :alt="item.name" />
                <div class="jbxxson_box">
                  <div class="tits"
                       style="">{{ item.name }}</div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
                <div v-if="item.size"
                     class="sizeData">{{ item.size }}</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
      <div class="jbxx-contt"
           style="padding-bottom: 20px;">
        <div class="jbqkss"><span class="jbqkbz">统计报表</span></div>
        <div>
          <a-row>
            <a-col v-for="(item, index) in OtheServicesList"
                   :key="index"
                   :span="4"
                   @click="clickIndex(item)">
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.img"
                     :alt="item.name" />
                <div class="jbxxson_box">
                  <div class="tits"
                       style="">
                    {{ item.name }}
                  </div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getLevelList } from "@/api/registrationMage/tableIng.js";
import { instance_1, instance_yajy, instance_3 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
import { backlogValue } from "@/api/area.js";
import { backlogHandle } from "@/api/area.js";
export default {
  components: {},
  data () {
    return {
      TBloading: false,
      // ===)履职活动组织代表工作人员~
      // 待办事项
      AddList: [
        {
          name: "代报名",
          remarks: "",
          num: null,
          img: require("@/assets/images/indexImage/履职活动组织-图标/履职活动组织-工作人员首页/代报名.png"),
          system: false,
          path: "/toHuoDong/huoDongList",
          type: "查看,报名信息",
        },
        {
          name: "代签到",
          remarks: "",
          num: null,
          img: require("@/assets/images/indexImage/履职活动组织-图标/履职活动组织-工作人员首页/代签到.png"),
          system: false,
          path: "/toHuoDong/huoDongList",
          type: "查看,出缺情况",
        },
        {
          name: "活动同步",
          remarks: "",
          num: null,
          img: require("@/assets/images/indexImage/履职活动组织-图标/履职活动组织-工作人员首页/活动同步.png"),
          system: false,
          path: "/toHuoDong/huoDongList",
          type: "查看,同步信息",
        },
      ],
      ToDoList: [
        {
          name: "活动发布",
          remarks: "",
          num: null,
          img: require("@/assets/images/indexImage/履职活动组织-图标/履职活动组织-工作人员首页/活动发布.png"),
          system: false,
          path: "/huoDong/add",
        },
      ],
      // 数据查询
      DataqueryList: [
        // {
        //   name: "活动通知查询",
        //   remarks: "查询代表<br/>的各类活动通知",
        //   img: require("@/assets/images/indexImage/履职活动组织-图标/履职活动组织-工作人员首页/活动通知查询.png"),
        //   system: false,
        //   path: "/toHuoDong/organizationData0",
        //   type: '查看',
        // },
        {
          name: "活动查询",
          remarks: "查询代表<br/>的各类活动信息",
          img: require("@/assets/images/indexImage/履职活动组织-图标/履职活动组织-工作人员首页/活动查询.png"),
          system: false,
          path: "/toHuoDong/huoDongList",
          size: null,
          type: "查看",
        },
        {
          name: "活动报名查询",
          remarks: "查询代表<br/>活动报名情况",
          img: require("@/assets/images/indexImage/履职活动组织-图标/履职活动组织-工作人员首页/活动报名查询.png"),
          system: false,
          path: "/toHuoDong/huoDongList",
          type: "查看,报名信息",
        },
        {
          name: "活动签到查询",
          remarks: "查询代表<br/>活动签到的情况",
          img: require("@/assets/images/indexImage/履职活动组织-图标/履职活动组织-工作人员首页/活动签到查询.png"),
          system: false,
          path: "/toHuoDong/huoDongList",
          type: "查看,出缺情况",
        },
      ],
      OtheServicesList: [
        {
          name: "活动统计情况",
          remarks: "查询代表<br/>参加活动的统计情况",
          img: require("@/assets/images/indexImage/履职活动组织-图标/履职活动组织-工作人员首页/活动统计表.png"),
          system: false,
          path: "/toHuoDong/huoDongList",
          size: null,
          type: "查看",
        },
      ],
      isDb: null,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      form: {
        jcDm: null,
        activityName: null,
        startTime: null,
        endTime: null,
      },
    };
  },

  created () {
    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));
  },
  methods: {
    checkPermission,
    // 获取节次
    getLevelListData () {
      getLevelList({}).then((res) => {
        if (res.data.code == "0000") {
          this.form.jcDm = res.data.data[0].jcDm;
          this.getNotRead();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    async getNotRead () {
      //  未办统计数
      let res = await backlogValue(this.queryForm, this.form);
      if (res.code == 200) {
        this.AddList[0].num = res.total; //待办总数
        this.$forceUpdate();
      }
      this.getRead();
    },
    async getRead () {
      //  已办统计数
      let res = await backlogHandle(this.queryForm, this.form);
      if (res.code == 200) {
        this.AddList[1].num = res.total; //已办总数
        this.$forceUpdate();
      }
    },
    clickIndex (event) {
      // if (
      //   this.isDb == "0" &&
      //   (event.name == "代表随手拍" || event.name == "社情民意专报")
      // ) {
      //   this.$message.info("对不起此模块仅向人大代表开放！");
      // }

      // 跨子系统跳转
      if (event.system) {
        let locationSystem = {
          path: this.$router.history.current.path,
          systemCode: this.$router.history.current.meta.systemId,
        };
        sessionStorage.setItem(
          "locationSystem",
          JSON.stringify(locationSystem)
        );
        this.$nextTick(() => {
          window.top.postMessage(
            {
              event: "locationSystem", // 约定的消息事件
              args: {
                path: event.path, //路径
                systemCode: event.systemCode, //子系统编码
              }, // 参数
            },
            "*"
          );
        });
      }
      //其他情况
      else if (event.name == "工作动态") {
        window.open("https://www.rd.gz.cn/");
      }
      // 本子系统路由跳转
      else {
        // 工作人员首页
        var Type = event.type ? event.type : null;
        this.$router.push({ path: event.path, query: { isDb: false, STARE: 'cgzryj', Type } });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 0px !important;
  background-color: rgb(246, 246, 246) !important;
}

.tits {
  // font-size: 16px;
  @include add-size($font_size_16);
  color: rgba(56, 56, 56, 1);
  font-weight: 700;
  margin-top: 15px;
  cursor: pointer;
}

.wddb1 {
  float: left;
  // font-size: 20px;
  @include add-size($font_size_16);
  color: black;
  font-weight: 700;
}

.jbxxson_Big {
  cursor: pointer;
  display: flex;
  align-content: center;
  flex-direction: row;
  padding: 10px;
  margin: 5% 10px;
  height: 20%;
  border-radius: 10px;
  align-items: center;
  justify-content: center;

  img {
    width: 18%;
    margin-right: 12%;
  }

  .jbxxson_Big_box {
    .Numbers {
      text-align: center;
      color: #cc3131;
      // font-size: 24px;
      // font-weight: 600;
    }

    .tits {
      font-size: 18px !important;
      font-weight: 400;
      letter-spacing: 0px;
      color: rgba(56, 56, 56, 1);
      text-align: left;
      vertical-align: top;
      margin: 0 auto;
      width: 100%;
      text-align: center;
      font-weight: 500; //小标题加粗
    }

    .remarks {
      width: 100%;
      text-align: center;
      margin: 1% auto;
    }

    color: rgba(153, 153, 153, 1);
  }
}

.jbxxson {
  cursor: pointer;
  box-shadow: 1.03px 1.85px 4px 0px rgb(166 166 166 / 51%);
  display: flex;
  align-content: center;
  flex-direction: column;
  padding: 10px;
  margin: 8% 6%;
  height: 20%;
  // border-radius: 10px;
  justify-content: flex-start;

  img {
    width: 38px;
    height: 38px;
    margin: 0% auto;
    margin-top: 10px;
  }

  .jbxxson_box {
    .tits {
      margin: 0 auto;
      width: 100%;
      text-align: center;
      color: rgba(56, 56, 56, 1);
      margin: 10px auto;
      font-weight: 600; //盒子标题
    }

    .remarks {
      width: 100%;
      text-align: center;
      margin: 1% auto;
    }

    color: rgba(153, 153, 153, 1);
  }

  .sizeData {
    position: absolute;
    right: 0%;
    color: #f4efef;
    font-family: pingFang-M;
    top: -5px;
    height: 37px;
    width: 37px;
    line-height: 37px;
    border-radius: 30px;
    background-color: #d52838;
    text-align: center;
  }
}

.a1 {
  color: #999999;
}

.jbxx-contt {
  background-color: #fff;
  width: 100%;
  border: 5px solid #f6f6f6;
  border-radius: 10px;
}

.rightCyygn {
  width: 20%;
  margin-right: 50px;
}

.cygn1 {
  width: 100%;
  display: flex;
  // justify-content: space-between;
  flex-wrap: wrap;
}

.imgss1 {
  width: 27%;
  margin-left: 20px;
  text-align: center;
  margin-bottom: 25px;

  div {
    height: 85px;
  }

  img {
    width: 80%;
    height: 80%;
  }
}

::v-deep {
  .ant-badge-status-dot {
    width: 10px !important;
    height: 10px !important;
  }
}

.xxssdtcont {
  display: flex;
  width: 100%;
}

.xxssdtLeft {
  width: 30%;
  color: #999999;
}

.dcl12 {
  margin-left: 15px;
  // font-size: 16px;
  @include add-size($font_size_16);
  color: #f50;
}

.xxssdtRight {
  // display: flex;
  width: 65%;
}

.slr12 {
  // float: right;
  margin-top: 5px;
  margin-left: 20px;
  color: #999999;
}

.slr15 {
  margin-top: 5px;
  margin-left: 20px;
}

.jbqkbz {
  padding-left: 18px;
  border-left: 4px solid rgba(199, 28, 51, 1);
  // font-size: 16px;
  @include add-size($font_size_16);
  font-weight: 700;
}

.sjcx_son {
  display: flex;
  flex-wrap: wrap;
  margin-left: 35px;
}

.fengexian {
  width: 100%;
  height: 1px;
  background: rgba(237, 237, 237, 1);
  margin: 25px 10px 20px 10px;
}

.jbqkss {
  padding: 10px 1%;
}

.cardHvover:hover {
  // background-color: #fbeeef;
  outline: 1px solid #d43030;
}
.Heightjbxxson {
  min-height: 160px;
}
@media screen and (max-width: 1920px) {
  .Heightjbxxson {
    min-height: 160px;
  }
}

@media screen and (max-width: 1600px) {
  .Heightjbxxson {
    min-height: 160px;
  }
}

@media screen and (max-width: 1280px) {
  .Heightjbxxson {
    min-height: 180px;
  }
}
</style>
