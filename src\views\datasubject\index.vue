<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <byui-query-form>
          <byui-query-form-left-panel :span="8">
            <el-button
              v-if="checkPermission(['OPER_DATA_SUBJECT_BASE'])"
              icon="el-icon-plus"
              type="primary"
              @click="handleAdd"
              >添加</el-button
            >
            <el-button
              v-if="checkPermission(['OPER_DATA_SUBJECT_BASE'])"
              icon="el-icon-delete"
              type="danger"
              @click="handleDelete"
              >删除</el-button
            >
          </byui-query-form-left-panel>
          <byui-query-form-right-panel :span="16">
            <el-form
              ref="form"
              :model="queryForm"
              :inline="true"
              @submit.native.prevent
            >
              <el-form-item>
                <el-input
                  v-model="queryForm.subjectName"
                  placeholder="主题名称"
                  class="table-input"
                />
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryForm.subjectStatus"
                  clearable
                  placeholder="请选择主题状态"
                  class="table-input"
                >
                  <el-option
                    v-for="item in subjectStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  type="primary"
                  native-type="submit"
                  @click="handleQuery"
                  >查询</el-button
                >
              </el-form-item>
              <el-form-item>
                <el-badge
                  :value="conditionsCount"
                  class="item"
                  style="margin-top: -2px;"
                  :hidden="badgeHide"
                >
                  <el-button type="primary" plain @click="drawer = true"
                    >更多查询条件</el-button
                  >
                </el-badge>
              </el-form-item>
            </el-form>
          </byui-query-form-right-panel>
        </byui-query-form>
        <!-- 更多查询条件抽屉 -->
        <el-drawer
          title="更多查询条件"
          :visible.sync="drawer"
          direction="rtl"
          :before-close="handleClose"
        >
          <byui-query-form
            style="padding: 0 10px; overflow: auto;"
            :style="{ height: drawerHeight }"
          >
            <el-form
              ref="drawerForm"
              :model="queryForm"
              @submit.native.prevent
              label-width="100px"
            >
              <!-- <el-form-item label="主题编号" prop="subjectCode">
                <el-input v-model="queryForm.subjectCode" @change="$forceUpdate()" />
              </el-form-item>-->
              <el-form-item label="主题名称" prop="subjectName">
                <el-input
                  v-model="queryForm.subjectName"
                  @change="$forceUpdate()"
                />
              </el-form-item>
              <el-form-item label="主题状态" prop="subjectStatus">
                <el-select
                  v-model="queryForm.subjectStatus"
                  clearable
                  placeholder="请选择主题状态"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="item in subjectStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="数据来源" prop="dataSourceName">
                <el-select
                  v-model="queryForm.dataSourceName"
                  filterable
                  placeholder="请选择数据来源"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="item in dataSourceOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="上传单位" prop="sourceOrgName">
                <el-input
                  v-model="queryForm.sourceOrgName"
                  placeholder="上传单位"
                  @change="$forceUpdate()"
                />
              </el-form-item>
              <el-form-item label="更新频率" prop="sourceUploadFrequency">
                <el-select
                  v-model="queryForm.sourceUploadFrequency"
                  filterable
                  placeholder="请选择更新频率"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="item in sourceUploadFrequencyOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="数据类型" prop="sourceDataType">
                <el-select
                  v-model="queryForm.sourceDataType"
                  filterable
                  placeholder="请选择数据类型"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <el-option
                    v-for="item in sourceDataTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="需求单位" prop="targetOrgName">
                <el-input
                  v-model="queryForm.targetOrgName"
                  placeholder="需求单位"
                  @change="$forceUpdate()"
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  type="primary"
                  native-type="submit"
                  @click="handleQuery"
                  >查询</el-button
                >
                <el-button icon="el-icon-delete" @click="handleClear"
                  >清空</el-button
                >
              </el-form-item>
            </el-form>
          </byui-query-form>
        </el-drawer>

        <el-table
          ref="datasourceTable"
          v-loading="listLoading"
          :data="list"
          border
          :element-loading-text="elementLoadingText"
          @selection-change="setSelectRows"
          @sort-change="tableSortChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="序号" width="80" fixed>
            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column
            label="主题名称"
            width="220"
            prop="subjectName"
            :show-overflow-tooltip="true"
            fixed
          ></el-table-column>
          <el-table-column
            label="主题状态"
            width="150"
            prop="subjectStatus.desc"
          ></el-table-column>
          <el-table-column
            label="数据来源"
            width="140"
            prop="dataSourceName"
          ></el-table-column>
          <el-table-column
            label="上传单位"
            width="200"
            prop="sourceOrgName"
          ></el-table-column>
          <el-table-column
            label="更新频率"
            width="140"
            prop="sourceUploadFrequency.desc"
          ></el-table-column>
          <el-table-column
            label="数据类型"
            width="140"
            prop="sourceDataType.desc"
          ></el-table-column>
          <el-table-column
            label="需求单位"
            width="200"
            prop="targetOrgName"
          ></el-table-column>
          <el-table-column
            label="最新数据"
            width="140"
            prop="lastDataDesc"
          ></el-table-column>
          <el-table-column
            label="操作"
            width="180px"
            fixed="right"
            v-if="checkPermission(['OPER_DATA_SUBJECT_BASE'])"
          >
            <template slot-scope="scope">
              <el-button
                v-if="checkPermission(['OPER_DATA_SUBJECT_BASE'])"
                type="text"
                @click="handleEdit(scope.row)"
                >编辑</el-button
              >
              <el-button
                v-if="checkPermission(['OPER_DATA_SUBJECT_BASE'])"
                type="text"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :background="background"
          :current-page="queryForm.page"
          :layout="layout1"
          :page-size="queryForm.rows"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        ></el-pagination>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { getPage, doDelete } from "@/api/datasubject";
import { getList as getDataSourceList } from "@/api/datasource";
import moment from "moment";
import { mapGetters } from "vuex";
import checkPermission from "@/utils/permission";
export default {
  name: "Table",
  components: {},
  filters: {},
  data() {
    return {
      list: [],
      listLoading: true,
      layout1: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      queryForm: {
        page: 1,
        rows: 10,
      },
      drawer: false,
      drawerHeight: window.innerHeight - 75 + "px",
      badgeHide: true,
      subjectStatusOptions: [
        {
          value: 0,
          label: "启用",
        },
        {
          value: 1,
          label: "禁用",
        },
      ],
      sourceUploadFrequencyOptions: [
        {
          value: 1,
          label: "日",
        },
        {
          value: 2,
          label: "周",
        },
        {
          value: 3,
          label: "月",
        },
        {
          value: 4,
          label: "季",
        },
        {
          value: 5,
          label: "半年",
        },
        {
          value: 6,
          label: "年",
        },
      ],
      sourceDataTypeOptions: [
        {
          value: 0,
          label: "数据表",
        },
        {
          value: 1,
          label: "文件",
        },
        {
          value: 2,
          label: "目录",
        },
        {
          value: 3,
          label: "API",
        },
      ],
      dataSourceOptions: [],
    };
  },
  created() {
    this.fetchData();
    this.initDataSource();
  },
  beforeDestroy() {},
  mounted() {
    // 抽屉滚动区高度
    window.onresize = () => {
      return (() => {
        this.drawerHeight = window.innerHeight - 75 + "px";
      })();
    };
  },
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
    conditionsCount: function () {
      let size = 0;
      for (let key in this.queryForm) {
        if (
          this.queryForm[key] !== undefined &&
          this.queryForm[key] !== null &&
          this.queryForm[key] !== ""
        ) {
          size++;
        }
      }
      const defaultSize = 2;
      this.badgeHide = size - defaultSize <= 0;
      return size - defaultSize;
    },
  },
  methods: {
    tableSortChange(column) {},
    setSelectRows(val) {
      this.selectRows = val;
    },
    handleAdd() {
      // this.$refs["edit"].showEdit(null, this.dataSourceOptions);
      this.$router
        .push({
          path: "/dataManage/editDataSubject",
          query: {
            subjectId: null,
          },
        })
        .catch(() => {});
    },
    handleEdit(row) {
      // this.$refs["edit"].showEdit(row, this.dataSourceOptions);
      // 不使用params方式传递参数，使用该方式会导致页面无法刷新（刷新后传递的参数会丢失）
      this.$router
        .push({
          path: "/dataManage/editDataSubject",
          query: {
            subjectId: row.subjectId,
          },
        })
        .catch(() => {});
    },
    handleDelete(row) {
      if (row.subjectId) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          doDelete([row.subjectId]).then((res) => {
            this.$baseMessage("删除成功", "success");
            this.fetchData();
          });
        });
      } else {
        if (this.selectRows.length > 0) {
          const ids = this.selectRows.map((item) => item.subjectId);
          this.$baseConfirm("你确定要删除选中项吗", null, () => {
            doDelete(ids).then((res) => {
              this.$baseMessage("删除成功", "success");
              this.fetchData();
            });
          });
        } else {
          this.$baseMessage("未选中任何数据主题", "error");
          return false;
        }
      }
    },
    handleSizeChange(val) {
      this.queryForm.rows = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryForm.page = val;
      this.fetchData();
    },
    handleQuery() {
      this.drawer = false;
      this.queryForm.page = 1;
      this.fetchData();
    },
    handleClear() {
      if (this.$refs["drawerForm"] !== undefined) {
        this.$refs["drawerForm"].resetFields();
      }
    },
    fetchData() {
      this.listLoading = true;
      getPage(this.queryForm).then((res) => {
        this.list = res.data.records;
        this.total = res.data.total;
        this.queryForm.page = res.data.current;
        this.queryForm.rows = res.data.size;
        setTimeout(() => {
          this.listLoading = false;
        }, 500);
      });
    },
    handleClose(done) {
      done();
    },
    initDataSource() {
      getDataSourceList().then((res) => {
        this.dataSourceOptions = [];
        res.data.forEach((element) => {
          this.dataSourceOptions.push({
            value: element.dsId,
            label: element.dsName,
          });
        });
      });
    },
    dateFormat(row, column, cellValue, index) {
      var date = row[column.property];
      if (date == undefined) {
        return "";
      }
      return moment(date).format("YYYY-MM-DD");
    },
    checkPermission,
  },
};
</script>
