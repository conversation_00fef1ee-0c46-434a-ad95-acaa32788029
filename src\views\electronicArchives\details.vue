<template>
      <!-- <a-button style="float: left;z-index: 99; margin: 30px;" @click="$router.go(-1)"> 返回</a-button> -->
      <div class="table-container">
        <a-spin tip="加载中..." :spinning="spinning">
          <h3>代表履职电子档案详情</h3>
          <a-col>
            <a-row>
              <a-col :span="3">
                <a-form-model-item>
                <a-button type="link"
                @click="downloadExcel()"><div style="font-size: 1.17em;">下载履职文档</div></a-button>
                </a-form-model-item>
              </a-col>
              <a-col :span="2">
                <a-form-model-item>
                  <a-button type="link" v-print="'#printCode'">
                    <div style="font-size: 1.17em;">打印</div>
                  </a-button>
                </a-form-model-item>
              </a-col>
              <a-col :span="5">
                <a-form-model-item label="排名类型" :label-col="{ span: 6 }"
                                    :wrapper-col="{ span: 14 }">
                  <a-select default-value="0" style="width: 150px" @change="handleRankTypeChange">
                    <a-select-option value="0">
                      全部代表
                    </a-select-option>
                    <a-select-option value="1">
                      非职务代表
                    </a-select-option>
                    <a-select-option value="2">
                      职务代表
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                  <a-form-model-item label="活动时间" :label-col="{ span: 6 }"
                                    :wrapper-col="{ span: 14 }">
                    <a-range-picker
                      v-model="dateRange"
                      style="width: 100%;"
                      :ranges="{
                        最近三个月: [
                          moment(new Date()).subtract(2, 'months'),
                          moment(),
                        ],
                        '今年1-6月': [
                          moment(moment().startOf('year')).startOf('month'),
                          moment(moment().startOf('year'))
                            .add(5, 'months')
                            .endOf('month'),
                        ],
                        '今年7-12月': [
                          moment(moment().startOf('year'))
                            .add(6, 'months')
                            .startOf('month'),
                          moment(moment().startOf('year'))
                            .add(11, 'months')
                            .endOf('month'),
                        ],
                        今年内: [
                          moment(moment().startOf('year')).startOf('month'),
                          moment(moment().startOf('year'))
                            .add(11, 'months')
                            .endOf('month'),
                        ],
                      }"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      @change="onTimeChange"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item>
                    <a-button type="primary"
                              style="margin-left: 8px;"
                              @click="initData()">搜索</a-button>
                  </a-form-model-item>
                </a-col>
            </a-row>
          </a-col>
          <div id="printCode">
            <a-col :xs="24" :sm="24" :md="24">
              <a-col :span="20">
                <a-row class="base-ant-col">
                  <a-col :span="6" >
                    <div class="col col-lable">姓名</div>
                  </a-col>
                  <a-col :span="6" >
                    <div class="col col-value">{{ info.dbBaseInfo && info.dbBaseInfo.userName }}</div>
                  </a-col>
                  <a-col :span="4" >
                    <div class="col col-lable">性别</div>
                  </a-col>
                  <a-col :span="2" >
                    <div class="col col-value">{{ info.dbBaseInfo && info.dbBaseInfo.sex }}</div>
                  </a-col>
                  <a-col :span="3" >
                    <div class="col col-lable">民族</div>
                  </a-col>
                  <a-col :span="3" >
                    <div class="col col-value">{{ info.dbBaseInfo && info.dbBaseInfo.mz }}</div>
                  </a-col>
                </a-row>
                <a-row class="base-ant-col">
                  <a-col :span="6" >
                    <div class="col col-lable">出生年月</div>
                  </a-col>
                  <a-col :span="12" >
                    <div class="col col-value">{{ info.dbBaseInfo && info.dbBaseInfo.birthDay }}</div>
                  </a-col>
                  <a-col :span="3" >
                    <div class="col col-lable">政治面貌</div>
                  </a-col>
                  <a-col :span="3" >
                    <div class="col col-value">{{ info.dbBaseInfo && info.dbBaseInfo.politicsStatusName }}</div>
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="4" class="base-ant-col">
                  <div class="col" style="height: 100px;">
                    <img
                      height="95"
                      :src="getImageUrl(info.dbBaseInfo && info.dbBaseInfo.photograph)"
                      alt="avatar"
                    />
                  </div>
              </a-col>
              <a-row class="base-ant-col">
                  <a-col :span="10">
                    <div class="col col-lable" style="border-top: none;">单位及职务</div>
                  </a-col>
                  <a-col :span="14" >
                    <div class="col col-value" style="border-top: none;">{{ info.dbBaseInfo && info.dbBaseInfo.workUnit }}</div>
                  </a-col>
              </a-row>
              <a-row class="base-ant-col">
                  <a-col :span="10" >
                    <div class="col col-lable">所在代表团</div>
                  </a-col>
                  <a-col :span="14" >
                    <div class="col col-value">{{ info.dbBaseInfo && info.dbBaseInfo.orgName }}</div>
                  </a-col>
              </a-row>
              <a-row class="base-ant-col">
                  <a-col :span="10" >
                    <div class="col col-lable">推荐单位</div>
                  </a-col>
                  <a-col :span="14" >
                    <div class="col col-value">{{ info.dbBaseInfo && info.dbBaseInfo.quotaSourceName }}</div>
                  </a-col>
              </a-row>
              <a-row class="base-ant-col">
                  <a-col :span="10" >
                    <div class="col col-lable">选举单位</div>
                  </a-col>
                  <a-col :span="14" >
                    <div class="col col-value">{{  info.dbBaseInfo && info.dbBaseInfo.electoralUnit }}</div>
                  </a-col>
              </a-row>
              <a-row class="base-ant-col">
                  <a-col :span="24" >
                    <div class="col col-lable">主要履职情况</div>
                  </a-col>
              </a-row>
              <details-item itemLabel="出席代表大会及发言情况" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.meetingNumRank" :itemList="info && info.meetingBaseInfos"></details-item>
              <details-item itemLabel="领衔提出议案建议情况" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.proposalSuggestionNumRank"  :itemList="info && info.proposals"></details-item>
              <details-item itemLabel="参加进社区活动情况" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.communityScheduleNumRank" :itemList="info && info.communityScheduleBaseInfos"></details-item>
              <details-item itemLabel="参加代表培训活动情况" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.studyNumRank" :itemList="info && info.trains"></details-item>
              <details-item itemLabel="参加代表集中视察情况" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.investigationNumRank" :itemList="info && info.inspects"></details-item>
              <details-item itemLabel="参加“更好发挥人大代表作用”主题活动情况" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.themeMonthNumRank" :itemList="info && info.themes"></details-item>
              <details-item itemLabel="意见建议被《人大代表情况反映》情况" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.achievementNumRank" :itemList="info && info.achievements"></details-item>
              <details-item itemLabel="履职事迹被《人大代表履职周报》刊登次数" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.resumptionWeekUserRecordNumRank" :itemList="info && info.achievementNum"></details-item>
              <details-item itemLabel="使用代表“随手拍”反映问题次数" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.memberCommentNumRank" :itemList="info && info.memberCommentNum"></details-item>
              <details-item itemLabel="参加其他履职活动情况" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.otherNumRank" :itemList="info && info.otherDutyActivities"></details-item>
              <details-item itemLabel="获得奖励情况" :itemRank="info.electronicArchivesRank && info.electronicArchivesRank.dutyActivityAwardNumRank" :itemList="info && info.awards" style="margin-bottom: 200px;"></details-item>
            </a-col>
          </div>
          </a-spin>
      </div>
  </template>
  <script>
  import { myPagination } from "@/mixins/pagination.js";
  import { getInfo } from "@/api/electronicArchives.js"
  import detailsItem from "./details-item"
  import { downloadInfoExcel } from "@/api/electronicArchives.js";
  import Print from "vue-print-nb";
  import Vue from "vue";
  Vue.use(Print);
  export default {
    mixins: [myPagination],
    components: {detailsItem},
    props: {

    },

    data() {
      return {
        userId: null,
        sfDm: null,
        jcDm: null,
        info: {},
        dbBaseInfo: {},
        action: Vue.prototype.GLOBAL.basePath_1 + "/dbxx/",
        dateRange: [],
        dutyTag: '0',
        spinning: false
      };
    },

    mounted() {
      this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
      this.$store.dispatch("navigation/breadcrumb2", "社情民意列表详情");
      let { userId, sfDm, jcDm, startTime, endTime } = this.$route.query;
      this.userId = userId;
      this.sfDm = sfDm;
      this.jcDm = jcDm;
      this.startTime = startTime;
      this.endTime = endTime;
      this.dateRange[0] = startTime
      this.dateRange[1] = endTime
      this.initData()
    },

    methods: {
      initData() {
        this.spinning = true
        let data = {
          userId: this.userId,
          sfDm: this.sfDm,
          jcDm: this.jcDm,
          startTime: this.startTime,
          endTime: this.endTime,
          dutyTag: this.dutyTag
        };
        getInfo(data).then(res => {
          this.info = res.data
          console.log(this.info,"240---------------")
          this.dbBaseInfo = res.data.dbBaseInfo
          this.spinning = false
        })
      },
      getImageUrl(url) {
        return this.action + url;
      },
      // 下载
      downloadExcel(info) {
        let query = {
          jcDm: this.jcDm,
          userId: this.userId,
          sfDm: this.sfDm,
          startTime: this.startTime,
          endTime: this.endTime
        }
        this.downloadExcelLoading = true;
        downloadInfoExcel(query).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = this.info.dbBaseInfo.userName + `代表电子档案` + new Date().getTime() + `.xlsx`;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          setTimeout(() => {
            this.downloadExcelLoading = false;
          }, 1000);
        });
      },
      onTimeChange(val) {
        this.startTime = val[0];
        this.endTime = val[1];
        this.initData()
      },
      handleRankTypeChange(value) {
        this.dutyTag = value
        this.initData()
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  .table-container {
    width: 80%;
    padding: 20px;
    // height: 2000px;
  }

  .base-ant-col {
  .col {
    display: flex;
    height: 50px;
    border: 1px solid #000;
    margin-right:-1px;
    margin-bottom:-1px;
    align-items: center;
    justify-content: center;
  }

  .col-lable {
    font-weight: bold;
  }
  .col-value {
    font-weight: bold;
    color: #00000070;
  }
}
  </style>
