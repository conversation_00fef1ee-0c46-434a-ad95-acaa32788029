<template>
  <div class="table-container">
    <a-row class="formBox">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :labelCol="{ span: 6}"
                    :wrapperCol="{ span: 18, offset: 0 }">
        <a-col span="6">
          <a-form-model-item label="届次">
            <a-select placeholder="请选择届次"
                      v-model="queryForm.period"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in periods"
                               :key="item.id"
                               :value="item.id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="性别"
                             prop="sex">
            <a-select v-model="queryForm.sex"
                      allow-clear
                      style="width: 100%">
              <a-select-option key="0"
                               value="0">女</a-select-option>
              <a-select-option key="1"
                               value="1">男</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="姓名"
                             prop="name">
            <a-input v-model="queryForm.name"
                     autocomplete="off"
                     allow-clear></a-input>
          </a-form-model-item>
        </a-col>
      </a-form-model>
      <a-button type="primary">搜索</a-button>
      <a-button style="margin-left: 12px;"
                class="pinkBoutton">重置</a-button>
    </a-row>
    <a-row>
      <standard-table :columns="columns"
                      rowKey="id"
                      :dataSource="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"></standard-table>
    </a-row>
  </div>
</template>
<script>
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
export default {
  // 候选人登记表
  components: { StandardTable },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      periods: [
        // 议案届别
        { id: 16, name: "十五届六次" },
        { id: 15, name: "十五届一次" },
        { id: 1, name: "十五届二次" },
        { id: 2, name: "十五届三次" },
        { id: 3, name: "十五届四次" },
        { id: 4, name: "十五届五次" },
      ],
      queryForm: {},
      columns: [
        {
          title: "届次",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "meeting",
        },
        {
          title: "姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "name",
        },
        {
          title: "性别",
          align: "center",
          width: 90,
          ellipsis: true,
          dataIndex: "sex",
        },
        {
          title: "出生日期",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "birthDay",
        },
        {
          title: "民族",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "phone",
        },
        {
          title: "政治面貌",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "address",
        },
        {
          title: "籍贯",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "unit",
        },
        {
          title: "工作单位",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "job",
        },
        {
          title: "职务",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "isOutC",
        },
        {
          title: "职称",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "isOutAddress",
        },
        {
          title: "全日制教育学历",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "isOutLongAddress",
        },
        {
          title: "在职教育学历",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "isHk",
        },
        {
          title: "是否公务员",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "isPlan",
        },
        {
          title: "是否连任代表",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "isRepresent",
        },
        {
          title: "是否同任两级以上代表",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "inPer",
        },
        {
          title: "是否村委会村党支部组成人员",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createdTime",
        },
        {
          title: "是否归侨眷属",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createdTime",
        },
        {
          title: "是否农民工",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createdTime",
        },
        {
          title: "是否事业单位负责人",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createdTime",
        },
        {
          title: "综合构成",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createdTime",
        },
        {
          title: "职业构成",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createdTime",
        },
        {
          title: "推荐单位",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createdTime",
        },
        {
          title: "录入人",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createdTime",
        },
        {
          title: "录入日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createdTime",
        },
      ],
      dataSource: [],
      selectedRows: [],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "候选人选管理");
    this.$store.dispatch("navigation/breadcrumb2", "候选人登记表");
    this.fetchData();
    // 创建假数据
    let data = {};
    this.columns.map((item) => {
      data[item.dataIndex] = "假数据";
    });
    this.dataSource.push(data);
  },
  methods: {
    // 获取数据
    fetchData () { },
  },
};
</script>
<style scoped>
.formBox {
  padding: 20px;
}
</style>
