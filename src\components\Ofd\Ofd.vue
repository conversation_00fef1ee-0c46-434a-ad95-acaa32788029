<template>
<div>
  <a-button type="primary" style="float:right;margin :10px 20px 0 0" @click="()=>$emit('click')">返回</a-button>
   <div id="content">
    <div class="seal_img_div"></div>
  </div>
</div>
 
  <!-- <div id="divId" style="text-align: center"></div> -->
</template>

<script>
import { parseOfdDocument, renderOfd } from "ofd.js";
import { downFile } from "@/api/api.js";

export default {
  props: {
    id: {
      default: "",
    },
  },
  data() {
    return {};
  },
  mounted() {
    downFile(this.id).then((file) => {
      parseOfdDocument({
        // ofd写入文件地址
        ofd: window.URL.createObjectURL(file.data),
        success(res) {
          let screenWidth = 800;
          const divs = renderOfd(screenWidth, res[0]);
          let contentDiv = document.getElementById("content");
          contentDiv.innerHTML = "";
          for (const div of divs) {
            contentDiv.appendChild(div);
          }
          for (let ele of document.getElementsByName("seal_img_div")) {
            this.addEventOnSealDiv(
              ele,
              JSON.parse(ele.dataset.sesSignature),
              JSON.parse(ele.dataset.signedInfo)
            );
          }
        },
        fail(error) {
          console.log(error);
        },
      });
    });
  },
  methods: {},
};
</script>
<style lang="less" scoped>
// .login-wrap {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 100vh;
//   .login {
//     width: 368px;
//     position: relative;
//     left: -33%;
//     .header {
//       margin-top: 9vh;
//       margin-bottom: 6vh;
//       text-align: center;
//       .description {
//         font-size: 30px;
//         font-weight: 600;
//         margin-top: 1vh;
//       }
//     }
//   }
//   .login-form {
//     background-color: #fff;
//     padding: 0 20px 20px;
//     box-shadow: 0 0 20px #ddd;
//     .form {
//       padding-top: 1vh;
//     }
//     .login-btn {
//       display: inline-block;
//       width: 100%;
//       margin-top: 2vh;
//     }
//   }
// }
</style>