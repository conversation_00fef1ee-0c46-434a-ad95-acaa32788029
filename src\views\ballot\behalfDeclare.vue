<template>
  <div class="table-container">
    <!-- <a-row style="margin-left:1%">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :labelCol="{ span: 6 }"
                    :wrapperCol="{ span: 18, offset: 0 }">
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="届次">
            <a-select placeholder="请选择届次"
                      v-model="queryForm.jcDm"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in getPeriodList"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="行政区划">
            <a-select placeholder="请选择行政区划"
                      v-model="queryForm.xzqhDm"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in getAdministrativeStateList"
                               :key="item.xzqhDm"
                               :value="item.xzqhDm">{{
                  item.xzqhmc
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="姓名"
                             prop="name">
            <a-input v-model="queryForm.dbxm"
                     autocomplete="off"
                     allow-clear
                     placeholder="请输入姓名"
                     v-on:keyup.enter="fetchData"></a-input>
          </a-form-model-item>
        </a-col>
      </a-form-model>
      <a-col :md="6"
             :sm="24">
        <span style="float: right; margin-top: 3px;">
          <a-button type="primary"
                    @click="fetchData()">搜索</a-button>
          <a-button style="margin-left: 12px;"
                    @click="reset"
                    class="pinkBoutton">重置</a-button>

        </span>
      </a-col>
    </a-row> -->

    <SearchForm @onReset="reset" @onSearch="fetchData" :noMore="true">
      <template v-slot:topSearch>
        <SingleSelect :title="'届次'" :selectList="getPeriodList"  :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm"/>
        <SingleSelect :title="'行政区划'" :selectList="getAdministrativeStateList"  :showName="'xzqhmc'" :showValue="'xzqhDm'" :value.sync="queryForm.xbDm"/>
        <SingleSearch @onEnter="fetchData" :title="'姓名'" :value.sync="queryForm.dbxm" />
      </template>
    </SearchForm>

    <a-row style="margin: 5px 0px 10px 8px;">
      <a-col :span="6">
        <a-button type="primary"
                  style="margin-left: 12px;"
                  @click="download">导出</a-button>
      </a-col>
    </a-row>
    <a-row style="margin-top:20px">
      <standard-table :columns="columns"
                      rowKey="ID"
                      :dataSource="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"
                      @selectedRowChange="onSelectChange"></standard-table>
    </a-row>
  </div>
</template>
<script>
import { myPagination } from "@/mixins/pagination.js";
import {
  declareList,
  getPeriod,
  getSex,
  getAdministrativeState
} from "@/api/election.js";
import StandardTable from "@/components/table/StandardTable";
import { instance_1 } from "@/api/axiosRq";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
export default {
  // 预备人选名册
  components: { 
    StandardTable,
    SingleSelect,
    SearchForm,
    SingleSearch,
  },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      jcDm: '',
      tableKey: [],
      tableData: [],
      advanced: false,
      periods: [
        // 议案届别
        { id: 16, name: "十五届六次" },
        { id: 15, name: "十五届一次" },
        { id: 1, name: "十五届二次" },
        { id: 2, name: "十五届三次" },
        { id: 3, name: "十五届四次" },
        { id: 4, name: "十五届五次" },
      ],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        jcDm: '',/* 届次id */
        dbxm: '',/* 代表姓名 */
        xzqhDm: undefined,/* 行政区划id */
      },
      columns: [
        {
          title: "届次",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "LEVEL_NAME",
        },
        {
          title: "姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "USER_NAME",
        },
        {
          title: "性别",
          align: "center",
          width: 90,
          ellipsis: true,
          dataIndex: "SEX",
        },
        {
          title: "出生日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "BIRTHDAY",
          customRender: (text, record, index) => {
            if (text) {
              // return text.replace("T", " ").split("Z").join("").substr(0, 19);
              return text.slice(0, text.indexOf("T"));
            } else {
              return '/'
            }
          },
        },
        {
          title: "户籍所在地",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "NATIVE_PLACE",
        },
        {
          title: "工作单位",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "WORK_UNIT",
        },
        {
          title: "职务",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "DUTY",
        },
        {
          title: "录入人",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "CREATOR",
        },
        {
          title: "录入日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "CREATE_TIME",
          customRender: (text, record, index) => {
            if (text) {
              return text.replace("T", " ").split("Z").join("").substr(0, 19)
            } else {
              return '/'
            }
          },
        },
      ],
      dataSource: [],
      selectedRows: [],
      getPeriodList: [], /* 获取届次 */
      getSexList: [],/* 获取性别 */
      getNationList: [],/* 获取民族 */
      getAdministrativeStateList: [],/* 行政区划下拉数据 */
      getPoliticalList: [],/* 获取出党派下拉数据列表 */
      getRecommendList: [],/* 获取出推荐单位下拉数据列表 */
      getSynthesizeList: [],/* 获取出综合构成下拉数据列表 */
      getOccupationList: [],/* 获取出职业构成下拉数据列表 */
      getEducationStateList: [],/* 获取在职教育学历、全日制教育学历下拉数据列表 */
      selectData: [ /*  是：1     否：0 */
        { id: '', name: '全部' },
        { id: '1', name: '是' },
        { id: '0', name: '否' }
      ]
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "投票选举管理");
    this.$store.dispatch("navigation/breadcrumb2", "代表申报表");
    this.selectList()
  },
  methods: {
    async selectList () {
      let res = await getPeriod()
      if (res.code == "0000") {
        this.getPeriodList = res.data
        this.queryForm.jcDm = res.data[0].jcDm //改过
        this.jcDm = res.data[0].jcDm
      }
      let res1 = await getSex()
      if (res1.code == "0000") {
        this.getSexList = res1.data
      }
      // let res2=await getNation()
      //   if(res2.code=="0000"){
      //     this.getNationList=res2.data
      //   }
      //  let res3=await getPolitical()
      //   if(res3.code=="0000"){
      //     this.getPoliticalList=res3.data
      //   }  
      // let res4=await getRecommend()
      //   if(res4.code=="0000"){
      //     this.getRecommendList=res4.data
      //   } 
      //  let res5=await getSynthesize()
      //   if(res5.code=="0000"){
      //     this.getSynthesizeList=res5.data
      //   }  
      //  let res6=await getOccupation()
      //   if(res6.code=="0000"){
      //     this.getOccupationList=res6.data
      //   }  
      //  let res7=await getEducationState()
      //   if(res7.code=="0000"){
      //     this.getEducationStateList=res7.data
      //   }     
      let res8 = await getAdministrativeState()
      if (res8.code == "0000") {
        this.getAdministrativeStateList = res8.data
        this.getAdministrativeStateList.unshift({ xzqhmc: '全部', xzqhDm: '' })
      }
      this.fetchData();
    },
    // 获取数据
    fetchData () {
      this.TBloading = true;

      instance_1({
        url: "/representative/behalfDeclaration/findAll",
        method: "post",
        params: this.queryForm
      }).then((res) => {
        if (res.data.code == '200') {
          this.dataSource = res.data.rows
          this.pagination.total = res.data.total;
          this.TBloading = false;
        }
      })
    },
    // 导出
    download () {
      if (this.tableKey.length == "0") {
        this.$message.warning('您还暂未勾选数据');
      } else {
        // let {jc_dm,level_name}= this.tableData[0]
        instance_1({
          url: "/representative/behalfDeclaration/export",
          method: "post",
          responseType: "blob",
          data: { ids: this.tableKey, ...this.queryForm }
        }).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          this.tableKey.length == "1" ? a.download = `代表申报表.xls` : a.download = `代表申报表.zip`;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
        })
      }
    },
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
      }
      this.queryForm.jcDm = this.jcDm
      this.fetchData();
    },
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    onSelectChange (key, data) {
      this.tableKey = key
      this.tableData = data
    }
  },
};
</script>
<style scoped>
.formBox {
  padding: 0 20px;
}
</style>
