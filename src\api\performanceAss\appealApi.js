import { instance_yajy } from "@/api/axiosRq";
// 申诉处理列表
// queryForm 请求搜索数据
export function appealList(queryForm) {
  return instance_yajy({
    url: `/api/v1/appraise/form/queryPleadList`,
    method: "post",
    data: queryForm,
  });
}
// 申述处理-审核列表
export function pleadList(queryForm) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/pleadList`,
    method: "post",
    data: queryForm,
  });
}
// 申述处理-审核列表评分明细查看
export function auditDetailList(queryForm) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/auditDetailList`,
    method: "post",
    data: queryForm,
  });
}

// 申述处理-审核列表评分明细删除
export function auditDetailListDelete(id) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/delete`,
    method: "post",
    params: { id },
  });
}

// 申述处理-审核列表评分明细修改 用于获取数据1
export function auditDetailGetById(queryForm) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/getById`,
    method: "post",
    params: queryForm,
  });
}
// 申述处理-审核列表评分明细修改 用于获取数据2
export function auditDetailGetBySmallItem(queryForm) {
  return instance_yajy({
    url: `/api/v1/appraise/smallItem/getById`,
    method: "post",
    params: queryForm,
  });
}
// 申述处理-审核列表评分明细修改 用于修改
export function auditDetailSave(queryForm) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/save`,
    method: "post",
    data: queryForm,
  });
}
// 工作评价 工作评价表 明细列表
export function pleadAudit(form) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/pleadAudit`,
    method: "post",
    data: form.data,
  });
}
