import request from "@/utils/request";
import {
  data
} from "jquery";
import qs from "qs";

export function getListByRelId(param) {
  return request({
    url: "/api/v1/meetingFile/getMeetingFileList/" + param,
    method: "post",
  });
}

export function getAllListByRelId(param) {
  return request({
    url: "/api/v1/meetingFile/getAllFileList/" + param,
    method: "post",
  });
}

export function getRelFileList(data) {
  return request({
    url: "/api/v1/meetingFile/getRelFileList",
    method: "post",
    data
  });
}

export function doDelete(param) {
  return request({
    url: "/api/v1/meetingFile/delMeetingFileById/" + param,
    method: "post",
  });
}

export function doUpdate(data, param) {
  return request({
    url: "/api/v1/meetingFile/updatePatchFile",
    method: "post",
    data,
  });
}

export function doUpdate2(data, param) {
  return request({
    url: "/api/v1/meetingFile/updatePatchFile/" + param,
    method: "post",
    data,
  });
}

export function doUpdateTimeLimit(data) {
  return request({
    url: "/api/v1/meetingFile/updateFileTimeLimit",
    method: "post",
    data,
  });
}
