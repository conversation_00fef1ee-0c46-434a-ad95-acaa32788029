<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <SearchForm :value="queryForm" @onReset="reset" @onSearch="search">
          <template v-slot:topSearch>
            <DhJcCascade v-model="queryForm" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>
          </template>
          <template v-slot:moreSearch>
            <AdminStreetStationCascade v-model="queryForm" allow-clear />

            <FormInput
              v-model="queryForm.userName"
              :label="'代表姓名'"
              allow-clear
              @enter="handleEnter"
            />

            <FormRangePicker
              v-model="queryForm"
              start-prop="startTime"
              end-prop="endTime"
              label="时间范围"
              allow-clear
            />

            <FormInput
              v-model="queryForm.contactName"
              :label="'联系人姓名'"
              @enter="handleEnter"
            />
          </template>
        </SearchForm>

        <a-row style="margin: 5px 0px 10px 8px">
          <a-col :span="12" style="margin-bottom: 10px">
            <a-button
              v-show="isDb == 0"
              type="primary"
              style="margin-left: 12px"
              @click="onDetail()"
              >新增</a-button
            >
            <a-button
              type="primary"
              style="margin-left: 12px"
              :loading="downloadExcelLoading"
              @click="downloadExcel"
            >
              下载模版</a-button
            >
            <div style="display: inline-block">
              <a-upload
                name="file"
                accept=".xls"
                :multiple="true"
                :file-list="fileList"
                :before-upload="beforeUpload"
                @change="downImport"
              >
                <a-button type="primary" style="margin-left: 8px">
                  导入</a-button
                >
              </a-upload>
            </div>
            <a-button
              type="primary"
              :loading="downloaDataLoading"
              style="margin-left: 8px"
              @click="downloaData"
              >导出</a-button
            >
            <a-button
              type="primary"
              :loading="downloaDataZipLoading"
              style="margin-left: 8px"
              @click="downloaDataZip"
              >导出各联络站办理群众意见建议情况汇总表</a-button
            >
          </a-col>
          <a-col :span="12">
            <a-button
              type="primary"
              :loading="downloadLoading"
              style="float: right"
              @click="handeInstructions"
            >
              进社区活动操作指引</a-button
            >
          </a-col>
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <a-table
            ref="table"
            :bordered="false"
            class="directorySet-table"
            size="small"
            :columns="columns"
            :pagination="pagination"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            :data-source="list"
            :row-key="
              (record, index) => {
                return record.id;
              }
            "
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import {
  downloadCommunityActivitiesExcel,
  ExportExcelValue,
  downloadCommunityActivitiesGuide,
  addCommunityActivitiesExcel,
  ExportZipValue,
  checkSummaryExport,
} from "@/api/communityschedule";
import { mineList } from "@/api/communityscheduleactivity";
import SearchForm from "@/components/SearchForm/index";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import FormRangePicker from "@/components/FormRangePicker/index.vue";
import { DBLZ_DBJSQ } from "@/utils/enum/levelRoleMainIdentifyEnum";
// import {checkSummaryExport} from "../../api/communityschedule";

export default {
  components: {
    FormRangePicker,
    FormInput,
    AdminStreetStationCascade,
    DhJcCascade,
    SearchForm,
  },
  filters: {},
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBJSQ,
      downloaDataLoading: false,
      downloaDataZipLoading: false,
      downloadExcelLoading: false,
      isDb: 0,
      downloadLoading: false,
      list: [],
      listLoading: false,
      //list-----
      selectRows: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        dhDm: undefined,
        jcDm: undefined,
        dbtId: undefined,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        userName: undefined,
        startTime: "",
        endTime: "",
        contactName: "",
      },
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 60,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "当前状态",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "currStateName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        // {
        //   align: "center",
        //   title: "活动编号",
        //   width: 180,
        //   ellipsis: true,
        //   dataIndex: "activityNo",
        //   customRender: (text, record, index) => {
        //     if (text) {
        //       return text;
        //     } else {
        //       return "/";
        //     }
        //   },
        // },
        {
          title: "活动时间",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "serverTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "行政区划",
          align: "center",
          width: 90,
          ellipsis: true,
          dataIndex: "administrativeAreaName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "乡镇街道",
          align: "center",
          width: 140,
          ellipsis: true,
          dataIndex: "streetTownName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站名称",
          align: "center",
          width: 340,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.liaisonStationName;
          },
        },
        {
          title: "联系人",
          align: "center",
          width: 100,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.contactName || "/";
          },
        },
        {
          title: "联系人电话",
          align: "center",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.contactPhoneNumber || "/";
          },
        },
        {
          align: "center",
          title: "代表",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.inviteRangeDesc || "/";
          },
        },
        {
          align: "center",
          title: "备注",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.mark || "/";
          },
        },
        {
          align: "center",
          title: "录入日期",
          width: 190,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.createTime || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 150,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.$router.push({
                      //   path: "/community/intoTheCommunityActivityWrapper",
                      //   query: { id: record.id, oper: "view" },
                      // });
                      this.onDetail("view", record.id);
                    },
                  },
                },
                "查看"
              ),
              // h(
              //   "span",
              //   {
              //     attrs: {
              //       type: "text",
              //     },
              //     style: {
              //       cursor: "pointer",
              //       marginLeft: "14px",
              //       color: "#DB3046",
              //     },
              //     on: {
              //       click: () => {
              //         this.addSocialconditions(record.id)
              //       },
              //     },
              //   },
              //   "社情民意"
              // ),
            ]);
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      fileList: [],
      searchDebounced: _.debounce(this.search, 500),
      isKeepAlive: false,
    };
  },
  created() {
    // 判断是否是路由跳转过来
    // this.isDb = this.$route.query.isDb
    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));
    // 判断是否是路由跳转过来
    if (this.$route.query.type == "add") {
      this.onDetail();
    }

    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "进社区活动");
  },
  activated() {
    if (!this.isKeepAlive) {
      this.isKeepAlive = true;
      return;
    }
    this.fetchData();
  },
  methods: {
    handleEnter() {
      this.searchDebounced();
    },
    // 操作指引
    handeInstructions() {
      this.downloadLoading = true;
      downloadCommunityActivitiesGuide().then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区活动操作指引.pdf`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000);
      });
      // // 预览指引pdf文件
      //     let pdfUrl = Vue.prototype.GLOBAL.basePath_1 +
      //     "newPublicOpinionController/downloadHDZN"+ "&token=" + Vue.prototype.GLOBAL.token;
      // window.open(
      //   process.env.BASE_URL +
      //   "pdfjs/web/viewer.html?file=" +
      //   encodeURIComponent(pdfUrl)
      // );
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    // 导出
    downloaData() {
      const params = {
        ...this.queryForm,
        ids: this.selectedRowKeys.map((son) => son).join(),
      };
      this.downloaDataLoading = true;
      ExportExcelValue(params).then((res) => {
        let a = window.document.createElement("a");
        // console.log("ddddddddddddddd",JSON.stringify(res.code))
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区活动数据.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloaDataLoading = false;
        }, 1000);
      });
    },
    downloaDataZip() {
      const params = {
        ...this.queryForm,
        ids: this.selectedRowKeys.map((son) => son).join(),
      };
      this.downloaDataZipLoading = true;

      checkSummaryExport(params).then((res) => {
        if (res.data.code == '0000') {
          ExportZipValue(params).then((res) => {
            let a = window.document.createElement("a");
            res = URL.createObjectURL(res.data);
            a.href = res;
            a.download = `进社区活动数据.zip`;
            window.document.body.appendChild(a);
            a.click();
            window.document.body.removeChild(a);
            setTimeout(() => {
              this.downloaDataZipLoading = false;
            }, 1000);
            this.$message.success("导出成功")
          });
        } else {
          this.$message.error(res.data.msg);
        }
      });
      this.downloaDataZipLoading = false;
    },
    // 下载
    downloadExcel() {
      this.downloadExcelLoading = true;
      downloadCommunityActivitiesExcel(this.queryForm).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区活动导入模板.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadExcelLoading = false;
        }, 1000);
      });
    },
    // 上传文件限制 直接return
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },

    //导入活动表
    downImport({ file, fileList }) {
      // console.log(file);
      var data = new FormData();
      data.append("file", file);
      addCommunityActivitiesExcel(data).then((res) => {
        console.log(res, "res");
        if (res.data.code != "0000") {
          return this.$message.error(res.data.msg);
        }
        if (res.data.code == "0000") {
          this.$baseMessage("上传成功", "success");
          this.fetchData();
        }
      });
    },
    //列表
    fetchData() {
      this.listLoading = true;
      mineList({ ...this.queryForm, isDb: this.isDb }).then((response) => {
        this.list = response.rows;
        this.total = response.total;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    handleSizeChange(val) {
      this.queryForm.size = val;
      this.fetchData();
    },
    onDetail(oper, id) {
      this.$router.push({
        path: "/community/intoTheCommunityActivityDetail",
        query: {
          oper,
          id,
        },
      });
    },
    // 搜索
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    // 重置
    reset(value) {
      this.queryForm = value;
      this.pagination.current = 1;
      this.queryForm.pageNum = 1;
      this.queryForm.pageSize = 10;
      this.fetchData();
    },
  },
};
</script>
