<template>
  <div class="EnterContent">
    <div class="content">
      <a-alert v-if="alert" :message="alert" banner />

      <div class="content-item">
        <h3>{{ operTitle }}</h3>

        <div>
          <slot name="default" />
        </div>
      </div>

      <!-- <div class="content-item">
        <h3>录入人信息（系统自动录入）</h3>

        <div>
          <a-form-model ref="form">
            <a-row :gutter="50">
              <a-col :span="24" :lg="12" :xl="8">
                <a-form-model-item label="录入人层级">
                  <a-input disabled />
                </a-form-model-item>
              </a-col>
              <a-col :span="24" :lg="12" :xl="8">
                <a-form-model-item label="录入人机构">
                  <a-input disabled />
                </a-form-model-item>
              </a-col>
              <a-col :span="24" :lg="12" :xl="8">
                <a-form-model-item label="录入人姓名">
                  <a-input disabled />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </div>
      </div> -->

      <div class="actions">
        <slot name="actions" />
      </div>
    </div>

    <div v-if="!!$slots.right" class="right">
      <slot name="right" />
    </div>
  </div>
</template>

<script>
export default {
  name: "ActivitySchedule",
  model: {
    prop: "form",
    event: "change",
  },
  props: {
    title: {
      type: String,
      required: true,
    },
    alert: {
      type: String,
      default: "",
    },
    // create | view | update | audit
    oper: {
      type: String,
      required: true,
    },
  },
  computed: {
    operTitle() {
      if (this.oper == "create") return "添加" + this.title;

      if (this.oper == "update") return "编辑" + this.title;

      if (this.oper == "audit") return "送审、审核" + this.title;

      return "查看" + this.title;
    },
  },
  mounted() {
    console.log(this.$slots);
  },
};
</script>

<style lang="scss" scoped>
.EnterContent {
  display: flex;
  padding: 0 20px;

  .content {
    flex: 1;

    .content-item {
      margin-top: 20px;

      > h3 {
        height: 30px;
        line-height: 30px;
        border-left: 4px solid #c71c33;
        padding-left: 18px;
        font-weight: bold;
        font-size: 18px;
        box-sizing: border-box;
      }

      > div {
        border-radius: 8px;
        border: 1px solid #dcdfe6;
        padding: 20px;
      }
    }

    ::v-deep .actions {
      padding: 20px 0;
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }

  .right {
    flex-shrink: 0;
    padding: 0 20px;

    > h3 {
      height: 30px;
      line-height: 30px;
      font-weight: bold;
      font-size: 18px;
      box-sizing: border-box;
    }
  }
}
</style>
