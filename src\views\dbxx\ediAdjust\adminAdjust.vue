<template>
  <div class="adminAdjust table-container">
    <!-- <a-form layout="horizontal">
      <a-row style="margin-left: 1%;">
        <a-col :md="6" :sm="24">
          <a-form-item
            label="代表姓名"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18, offset: 0 }"
          >
            <a-input
              v-model="queryForm.userName"
              placeholder="请输入代表姓名"
              allow-clear
              @keyup.enter="search"
            ></a-input>
          </a-form-item>
        </a-col>
        <a-col :md="5" :sm="24" style="float: right;">
          <span style="float: right; margin-top: 3px;">
            // <a-button icon="plus"  @click="handleAdd" type="primary"
              >新增</a-button
            >//
            <a-button style="margin-left: 12px;" type="primary" @click="search"
              >搜索</a-button
            >
            <a-button
              style="margin-left: 12px;"
              class="pinkBoutton"
              @click="reset"
              >重置</a-button
            >
          </span>
        </a-col>
      </a-row>
    </a-form> -->

    <SearchForm @onReset="reset" @onSearch="search" :noMore="true">
      <template v-slot:topSearch>
        <SingleSearch @onEnter="search" :title="'代表姓名'" :value.sync="queryForm.userName" />
      </template>
    </SearchForm>

    <!-- <a-modal
      :title="title"
      destroyOnClose
      :visible.sync="visibleShow"
      width="600px"
      @cancel="close"
      @ok="confirm"
    >
      <div
        slot="footer"
        class="dialog-footer"
        style="position: relative; padding-right: 15px; text-align: right"
      >
        <a-button @click="close">关闭</a-button>
        <a-button
          :disabled="iShow"
          type="primary"
          style="margin-left: 10px"
          @click="confirm"
        >
          确定
        </a-button>
      </div>

      <a-form-model
        ref="noticeForm"
        :label-col="{ span: 5 }"
        :rules="rules"
        :wrapper-col="{ span: 16 }"
        :model="forValue"
      >
        <a-form-model-item label="届次" prop="level_name">
          <a-input
            :disabled="iShow"
            placeholder=""
            v-model="forValue.level_name"
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="代表姓名" prop="USER_NAME">
          <a-input
            :disabled="iShow"
            placeholder=""
            v-model="forValue.USER_NAME"
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="性别" prop="sex">
          <a-input
            :disabled="iShow"
            placeholder=""
            v-model="forValue.sex"
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="出生日期" prop="BIRTHDAY">
          <a-input
            :disabled="iShow"
            placeholder=""
            v-model="forValue.BIRTHDAY"
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="出生日期" prop="BIRTHDAY">
          <a-input
            :disabled="iShow"
            placeholder=""
            v-model="forValue.BIRTHDAY"
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="党派" prop="POLITICS_STATUS_NAME">
          <a-input
            :disabled="iShow"
            placeholder=""
            v-model="forValue.POLITICS_STATUS_NAME"
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="学历" prop="xlmc">
          <a-input
            :disabled="iShow"
            placeholder=""
            v-model="forValue.xlmc"
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="手机号码" prop="PHONE">
          <a-input
            :disabled="iShow"
            placeholder=""
            v-model="forValue.PHONE"
          ></a-input>
        </a-form-model-item>
      </a-form-model>
    </a-modal>-->
    <standard-table
      :columns="columns"
      :data-source="list"
      row-key="userId"
      :loading="TBloading"
      :pagination="pagination"
      @tableClick="clickRow"
    >
    </standard-table>
  </div>
</template>

<script>
import { myPagination } from "@/mixins/pagination.js";
import { signList, queryData, queryInfo } from "@/api/infoQuery.js";
import constat from "@/utils/constat";
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';

export default {
  mixins: [myPagination],
  components: {
    SearchForm,
    SingleSearch, 
  },
  data() {
    return {
      TBloading: false,
      visibleShow: false,
      iShow: false,
      list: [],
      title: "",
      indexNum: 1,
      // 列表
      columns: [
        // {
        //   fixed: 'left',

        //   key: "index",
        //   align: "center",
        //   width: 80,
        //   ellipsis: true,
        //   customRender: (text, record, index) =>
        //     `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        // },
        {
          fixed: "left",
          title: "届次",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "level",
          customRender: (text, record, index) => {
            return record.level.levelName || "/";
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "性别",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "genderName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "出生日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "birthday",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "党派",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "politicsStatusName",
          customRender: (text, record, index) => {
            return text || "无";
          },
        },
        {
          title: "学历",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "educationName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "手机号码",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "phone",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleData(record.userId);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(record.userId);
                    },
                  },
                },
                "编辑"
              ),
            ]);
          },
        },
      ],
      forValue: {
        level_name: "",
        USER_NAME: "",
        sex: "",
        BIRTHDAY: "",
        POLITICS_STATUS_NAME: "",
        xlmc: "",
        PHONE: "",
      },
      rules: {
        type: [
          { required: true, message: "活动类型不能为空", trigger: "blur" },
        ],
        // modelNumber: [
        //   { required: true, message: "设备型号不能为空", trigger: "blur" },
        // ],
        // name: [
        //   { required: true, message: "设备名称不能为空", trigger: "blur" },
        // ],
        // remark: [{ required: true, message: "备注不能为空", trigger: "blur" }],
      },
      list: [],
      indexNum: 1,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        facilityNum: "",
        modelNumber: "",
        name: "",
      },
    };
  },
  created() {
    let queryForm = JSON.parse(localStorage.getItem("tongjifenzi"));
    if (queryForm) {
      this.queryForm = queryForm;
      this.pagination.current = this.queryForm.pageNum;
    }
    this.$store.dispatch("navigation/breadcrumb1", "代表信息修改");
    this.$store.dispatch("navigation/breadcrumb2", "管理员修改");
    this.fetchData();
  },
  beforeDestroy() {
    window.localStorage.setItem("tongjifenzi", JSON.stringify(this.queryForm));
  },
  methods: {
    // 点击行
    clickRow(event, record) {
      try {
        let state;
        let tableHeader = event.target.__vue__.column.key;
        tableHeader ? (state = true) : (state = false);
        if (tableHeader) {
          this.handleData(record.userId);
        }
      } catch (error) {}
    },
    close() {
      this.forValue = {
        level_name: "",
        USER_NAME: "",
        sex: "",
        BIRTHDAY: "",
        POLITICS_STATUS_NAME: "",
        xlmc: "",
        PHONE: "",
      };
      this.title = "";
      this.visibleShow = false;
      this.iShow = false;
    },

    async handleEdit(id) {
      let res = await queryData(id);
      let { JC_DM, SF_DM, DB_ID } = res.data;
      this.$router.push({
        path: "/ediAdjust/editInfo",
        query: {
          oper: constat.EDIT,
          jcDm: JC_DM,
          dbId: DB_ID,
          sfDm: SF_DM,
        },
      });
    },
    async handleData(id, edit) {
      let res = await queryData(id);
      if (res.code == "0000") {
        let { JC_DM, SF_DM, DB_ID } = res.data;
        this.$router.push({
          path: "/ediAdjust/editInfo",
          query: {
            oper: constat.VIEW,
            jcDm: JC_DM,
            dbId: DB_ID,
            sfDm: SF_DM,
          },
        });
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    search() {
      this.query();
    },

    handleAdd() {
      this.$router.push({
        path: "/addDlegate/addCommittee-CRUD",
        query: {
          oper: "constat.ADD",
        },
      });
    },

    reset() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        facilityNum: "",
        modelNumber: "",
        name: "",
      };
      this.query();
    },
    async query() {
      let res = await queryInfo(this.queryForm);
      if (res.code == 200) {
        this.list = res.rows;
        this.pagination.total = res.total;
      } else {
        this.$message({
          message: res.message,
          type: "error",
        });
      }
    },

    async fetchData() {
      this.TBloading = true;
      let res = await signList(this.queryForm);
      if (res.code == 200) {
        this.list = res.rows;
        this.pagination.total = res.total;
      } else {
        this.$message({
          message: res.message,
          type: "error",
        });
      }
      this.TBloading = false;
    },
  },
};
</script>
<style scoped>
.adminAdjust {
  padding: 15px !important;
}
</style>
