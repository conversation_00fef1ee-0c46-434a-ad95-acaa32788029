import Layout from "@/layouts";

// {
//   path: "staging",
//   name: "InformStaging",
//   component: () => import("@/views/meeting/testPage/index"),
//   meta: {
//     title: "待办任务",
//     permissions: ["I_XTGLY", "I_XXFBY"],
//   },
// },
// 在线学习培训
export default [
  {
    path: "/organizationIndex",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "在线学习培训",
    meta: {
      title: "首页",
    },
    children: [
      {
        path: "organizationTraining",
        name: "organizationTraining",
        component: () => import("@/views/onlineLearningTraining/index"),
        meta: {
          title: "代表首页",
        },
      },
      // {
      //   path: "organizationTraining2",
      //   name: "organizationTraining2",
      //   component: () => import("@/views/onlineLearningTraining/index2"),
      //   meta: {
      //     title: "代表首页",
      //   },
      // },
      {
        path: "organizationWork",
        name: "organizationWork",
        component: () =>
          import("@/views/onlineLearningTraining/organizationWork"),
        meta: {
          title: "工作人员首页",
        },
      },
      // {
      //   path: "organizationWork2",
      //   name: "organizationWork2",
      //   component: () =>
      //     import("@/views/onlineLearningTraining/organizationWork2"),
      //   meta: {
      //     title: "工作人员首页",
      //   },
      // },
      {
        path: "newVideo",
        name: "newVideo",
        component: () =>
          import("@/views/onlineLearningTraining/xuankezhongxinList"),
        meta: {
          title: "最新资源",
        },
        hidden: true,
      },
      // ++++++=
      {
        path: "fileList",
        name: "fileList",
        component: () => import("@/views/onlineLearningTraining/fileList"),
        meta: {
          title: "文件列表",
        },
        hidden: true,
      },
      {
        path: "bookList",
        name: "bookList",
        component: () => import("@/views/onlineLearningTraining/bookList"),
        meta: {
          title: "履职应知应会",
        },
        hidden: true,
      },

      // +++++工作人员首页
      {
        path: "RegistrationManagementActive",
        name: "RegistrationManagementActive",
        component: () =>
          import("@/views/onlineLearningTraining/RegistrationManagementActive"),
        meta: {
          title: "报名管理",
        },
        hidden: true,
      },

      {
        path: "CourseManagement",
        name: "CourseManagement",
        component: () =>
          import("@/views/onlineLearningTraining/CourseManagement"),
        meta: {
          title: "课程管理",
        },
        hidden: true,
      },
      {
        path: "ExpertDatabaseManagement",
        name: "ExpertDatabaseManagement",
        component: () =>
          import("@/views/onlineLearningTraining/ExpertDatabaseManagement"),
        meta: {
          title: "专家库管理",
        },
        hidden: true,
      },
      {
        path: "KnowledgeBaseManagement",
        name: "KnowledgeBaseManagement",
        component: () =>
          import("@/views/onlineLearningTraining/KnowledgeBaseManagement"),
        meta: {
          title: "知识库管理",
        },
        hidden: true,
      },
      {
        path: "LeaderboardManagement",
        name: "LeaderboardManagement",
        component: () =>
          import("@/views/onlineLearningTraining/LeaderboardManagement"),
        meta: {
          title: "排行榜管理",
        },
        hidden: true,
      },
      {
        path: "InvestigationManagement",
        name: "InvestigationManagement",
        component: () =>
          import("@/views/onlineLearningTraining/InvestigationManagement"),
        meta: {
          title: "调查问卷管理",
        },
        hidden: true,
      },
      {
        path: "ContentPublishing",
        name: "ContentPublishing",
        component: () => import("@/views/meeting/ContentPublishing/index"),
        meta: {
          title: "内容管理",
        },
        hidden: true,
      },
      // {
      //   path: "announcement",
      //   name: "announcement",
      //   component: () =>
      //     import("@/views/onlineLearningTraining/announcementList"),
      //   meta: {
      //     title: "通知公告",
      //   },
      //   hidden: true,
      // },
      // {
      //   path: "questionnaire",
      //   name: "questionnaire",
      //   component: () =>
      //     import("@/views/onlineLearningTraining/questionnaireList"),
      //   meta: {
      //     title: "调查问卷",
      //   },

      //   hidden: true,
      // },
      // {
      //   path: "websitelinks",
      //   name: "websitelinks",
      //   component: () =>
      //     import("@/views/onlineLearningTraining/websitelinks"),
      //   meta: {
      //     title: "网站链接",
      //   },

      //   hidden: true,
      // },
      {
        path: "organizationIndexData",
        name: "organizationIndexData",
        component: () =>
          import("@/views/onlineLearningTraining/organizationIndexData"),
        meta: {
          title: "详情",
        },

        hidden: true,
      },
      // {
      //   path: "announcementData",
      //   name: "announcementData",
      //   component: () =>
      //     import("@/views/onlineLearningTraining/announcementData"),
      //   meta: {
      //     title: "通知详情",
      //   },

      //   hidden: true,
      // },
    ],
  },
  {
    path: "/onlineRegistration",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "在线报名",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "onlineRegistration",
        name: "onlineRegistration",
        component: () => import("@/views/meeting/baoming/onlineRegistration"),
        // component: () => import("@/views/onlineLearningTraining/CourseManagement"),
        meta: {
          title: "在线报名",
        },
      },

      //+
      {
        path: "onlineRegistrationData",
        name: "onlineRegistrationData",
        component: () =>
          import("@/views/meeting/baoming/onlineRegistrationData"),
        meta: {
          title: "在线报名详情",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/zaixianxuexi", //===
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "在线学习",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "xuanke",
        name: "xuanke",
        component: () => import("@/views/meeting/zaixianxuexi/xuanke"),
        meta: {
          title: "选课中心",
        },
      },
      {
        path: "test11",
        name: "test11",
        component: () => import("@/views/meeting/zaixianxuexi/xuexi"),
        meta: {
          title: "在线学习",
        },
      },
      {
        path: "test12",
        name: "test12",
        component: () => import("@/views/meeting/zaixianxuexi/xuexijindu"),
        meta: {
          title: "在线学习进度",
        },
      },

      //+
      // {
      //   path: "onlineRegistrationData",
      //   name: "onlineRegistrationData",
      //   component: () => import("@/views/meeting/baoming/onlineRegistrationData"),
      //   meta: {
      //     title: "在线报名详情",
      //   },
      //   hidden: true,
      // },
    ],
  },
  // {
  //   path: "/test1",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "huoDong",
  //   title: "在线学习培训",
  //   meta: {
  //     title: "在线学习",
  //     icon: "database",
  //     permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
  //   },
  //   alwaysShow: false,
  //   children: [
  //     {
  //       path: "test11",
  //       name: "test11",
  //       component: () => import("@/views/meeting/zaixianxuexi/xuexi"),
  //       meta: {
  //         title: "在线学习",
  //       },
  //     },
  //   ],
  // },
  {
    path: "/expert",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "专家库",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "expert",
        name: "expert",
        component: () => import("@/views/meeting/zhuanjia/zhuanjiaku"),
        meta: {
          title: "专家库",
        },
      },
      // +
      {
        path: "zhuanjiakulist",
        name: "zhuanjiakulist",
        component: () => import("@/views/meeting/zhuanjia/zhuanjiakulist"),
        meta: {
          title: "专家库列表",
        },
        hidden: true,
      },

      {
        path: "expertData",
        name: "expertData",
        component: () => import("@/views/meeting/zhuanjia/expertData"),
        meta: {
          title: "文章详情",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/training",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "知识库",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "knowledge",
        name: "knowledge",
        component: () => import("@/views/meeting/repository/knowledge"),
        meta: {
          title: "知识库",
        },
      },
      {
        path: "knowledgeData",
        name: "knowledgeData",
        component: () => import("@/views/meeting/repository/knowledgeData"),
        meta: {
          title: "知识库详情",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/jianzuolianjie", //==
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "讲座链接",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "test14",
        name: "test14",
        component: () => import("@/views/meeting/chair/chair"),
        meta: {
          title: "讲座链接",
        },
      },
    ],
  },
  {
    path: "/otherVideos",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "其他视频",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "otherVideos",
        name: "otherVideos",
        component: () => import("@/views/meeting/video/video"),
        meta: {
          title: "其他视频",
        },
      },
      {
        path: "videoData",
        name: "videoData",
        component: () => import("@/views/meeting/video/videoData"),
        meta: {
          title: "其他视频详情",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/trainingSilhouette",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "培训剪影",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "trainingSilhouette",
        name: "trainingSilhouette",
        component: () =>
          import("@/views/meeting/peixunjianying/peixunjianying"),
        meta: {
          title: "培训剪影",
        },
      },
      //+
      {
        path: "peixunjianyingData",
        name: "peixunjianyingData",
        component: () =>
          import("@/views/meeting/peixunjianying/peixunjianyingData"),
        meta: {
          title: "培训剪影详情",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/paihangbang", //===
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "排行榜",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "paihangbangIndex", //===
        name: "paihangbangIndex",
        component: () => import("@/views/meeting/paihangbang/paihangbang"),
        meta: {
          title: "排行榜",
        },
      },
    ],
  },
  {
    path: "/notice",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "通知公告",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "announcement",
        name: "announcement",
        component: () => import("@/views/meeting/tongzhi/announcementList"),
        meta: {
          title: "通知公告",
        },
      },
      {
        path: "announcementData",
        name: "announcementData",
        component: () => import("@/views/meeting/tongzhi/announcementData"),
        meta: {
          title: "通知详情",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/test1",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "省代表调研视察培训报名",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "test19",
        name: "test19",
        // component: () =>
        //   import("@/views/meeting/shengdaibiaopeixunbaoming/list"),

        component: () => import("@/views/meeting/baoming/onlineRegistration"),
        meta: {
          title: "省代表调研视察培训报名",
        },
      },
    ],
  },
 
  {
    path: "/website",
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "网站链接",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      {
        path: "websiteLink",
        name: "websiteLink",
        // component: () => import("@/views/meeting/websiteLink/index"),
        component: () => import("@/views/onlineLearningTraining/websitelinks"),
        meta: {
          title: "网站链接",
        },
      },
    ],
  },
  {
    path: "/ContentManagement", //===
    component: Layout,
    redirect: "noRedirect",
    name: "huoDong",
    title: "在线学习培训",
    meta: {
      title: "内容管理",
      icon: "database",
      permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
    },
    alwaysShow: false,
    children: [
      // {
      //   path: "ContentManagement",
      //   name: "ContentManagement",
      //   component: () => import("@/views/meeting/ContentManagement/index"),
      //   meta: {
      //     title: "内容管理",
      //   },
      // },
      // {
      //   path: "statisticAnalysis",
      //   name: "statisticAnalysis",
      //   component: () => import("@/views/meeting/statisticAnalysis/index"),
      //   meta: {
      //     title: "统计分析",
      //   },
      // },
      // {
      //   path: "catalogManagement",
      //   name: "catalogManagement",
      //   component: () => import("@/views/meeting/catalogManagement/index"),
      //   meta: {
      //     title: "栏目管理",
      //   },
      // },
      {
        path: "ResourceManagement",
        name: "ResourceManagement",
        component: () => import("@/views/meeting/ResourceManagement/index"),
        meta: {
          title: "资源库管理",
        },
      },
      {
        path: "ContentPublishing",
        name: "ContentPublishing",
        component: () => import("@/views/meeting/ContentPublishing/index"),
        meta: {
          title: "内容发布",
        },
      },
      {
        path: "CommentManagement",
        name: "CommentManagement",
        component: () => import("@/views/meeting/CommentManagement/index"),
        meta: {
          title: "评论管理",
        },
      },
      // {
      //   path: "ContentManagement",
      //   name: "ContentManagement",
      //   component: () => import("@/views/meeting/ContentManagement/index"),
      //   meta: {
      //     title: "内容管理",
      //   },
      // },
      {
        path: "statisticAnalysis",
        name: "statisticAnalysis",
        component: () => import("@/views/meeting/statisticAnalysis/index"),
        meta: {
          title: "统计分析",
        },
      },
      {
        path: "catalogManagement",
        name: "catalogManagement",
        component: () => import("@/views/meeting/catalogManagement/index"),
        meta: {
          title: "栏目管理",
        },
      },
    ],
  },
  //  // +
  //  {
  //   path: "/informationStatistics",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "huoDong",
  //   title: "在线学习培训",
  //   meta: {
  //     title: "统计分析",
  //     icon: "database",
  //     permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
  //   },
  //   alwaysShow: false,
  //   children: [
  //     {
  //       path: "informationStatistics",
  //       name: "informationStatistics",
  //       component: () => import("@/views/meeting/informationStatistics/informationStatistics"),
  //       meta: {
  //         title: "信息量统计",
  //       },
  //     },
  //   ],
  // },

  {
    path: "/systemIndexxue",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "在线学习培训",
    meta: {
      title: "系统管理",
      permissions: ["ADMIN", "I_XTGLY"],
    },
    children: [
      {
        path: "meetingEdit",
        name: "MeetingEdit",
        component: () => import("@/views/meeting/edit"),
        meta: {
          title: "数据权限管理",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "session",
        name: "session",
        component: () => import("@/views/election/systemIndex/electionMage"),
        // component: () => import("@/views/meeting/session/index"),
        meta: {
          title: "届次管理",
        },
      },
      {
        path: "gradingContr",
        name: "gradingContr",
        component: () => import("@/views/meeting/gradingContr/index"),
        meta: {
          title: "分级管理",
        },
      },

      {
        path: "menuManage",
        name: "MenuManage",
        component: () => import("@/views/menu/index"),
        meta: {
          title: "菜单管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "roleManage",
        name: "RoleManage",
        component: () => import("@/views/role/index"),
        meta: {
          title: "角色管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "orgManage",
        name: "OrgManage",
        component: () => import("@/views/org/index"),
        meta: {
          title: "机构管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "dictionaryContr",
        name: "dictionaryContr",
        component: () => import("@/views/meeting/dictionaryContr/index"),
        meta: {
          title: "字典管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      // ++++++++
      // SMSManagement
      {
        path: "SMSManagement",
        name: "SMSManagement",
        component: () => import("@/views/meeting/SMSManagement/index"),
        meta: {
          title: "短信管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      // DynamicManagement
      {
        path: "DynamicManagement",
        name: "DynamicManagement",
        component: () => import("@/views/meeting/DynamicManagement/index"),
        meta: {
          title: "动态发布管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "myInfo",
        name: "myInfo",
        component: () => import("@/views/onlineLearningTraining/myInfo"),
        meta: {
          title: "个人信息",
        },
      },
      {
        path: "RegistrationManagement",
        name: "RegistrationManagement",
        component: () =>
          import("@/views/onlineLearningTraining/RegistrationManagement"),
        meta: {
          title: "在线报名情况表",
        },
        // hidden: true,
      },
      {
        path: "RegistrationDetails",
        name: "RegistrationDetails",
        component: () =>
          import("@/views/onlineLearningTraining/RegistrationDetails"),
        meta: {
          title: "报名管理详情",
        },
        hidden: true,
      },
      {
        path: "settingSTUDY",
        name: "settingSTUDY",
        component: () => import("@/views/meeting/settingSTUDY/index"),
        meta: {
          title: "系统设置",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "userLog",
        name: "userLog",
        component: () => import("@/views/meeting/userLog/index"),
        meta: {
          title: "日志查询",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "kalendar",
        name: "kalendar",
        component: () => import("@/views/meeting/kalendar/index"),
        meta: {
          title: "日历",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "helpManual",
        name: "helpManual",
        component: () => import("@/views/meeting/helpManual/index"),
        meta: {
          title: "帮助手册",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
    ],
  },
];
