// 履职登记管理-登记管理-活动查询
// 获取届次
import { instance_1 } from "@/api/axiosRq";
import request from "@/utils/request";
export function getLevelList(params) {
  return instance_1({
    url: "/dictCode/common/getLevelList",
    method: "get",
    params,
  });
}
//根据获取
export function getLevelListByDhDm(form) {
  return instance_1({
    url: "/dictCode/common/getLevelListByDhDm",
    method: "get",
    params: form,
  });
}
// 活动类型
export function getActivityTypeList(form) {
  return instance_1({
    url: "/dictCode/common/getActivityTypeList",
    method: "get",
    params: form,
  });
}
// 获取数据来源下拉
export function getDataSources(form) {
  return instance_1({
    url: "/dictCode/common/getDataSourceList",
    method: "get",
    params: form,
  });
}
// 获取数据归属下拉
export function getDataHome(form) {
  return instance_1({
    url: "/dictCode/common/getDataBelongList",
    method: "get",
    params: form,
  });
}
// 获取状态下拉
export function getFormState(form) {
  return instance_1({
    url: "/dictCode/common/getFormStateList",
    method: "get",
    params: form,
  });
}
// 获取列表
export function selectAll(data) {
  let form = {
    pageNum: data.pageNum,
    pageSize: data.pageSize,
    jcDm: data.jcDm,
  };
  return instance_1({
    url: "/dutyActive/selectAll",
    method: "post",
    data,
    params: form,
  });
}
// 组织单位树
export function findOO() {
  return instance_1({
    url: "/dmcs/common/ours/findOO",
    method: "post",
    params: {
      rootIds:
        "O2925,O3251,O3259,O2929,OID-REV-GLDW,O3525,O998,O3527,O166,O3529,O447,O3531,O1001,O3533,O554,O3535,O510,O3539,O1142,O3537,O439,O3545,O523,O3541,O1041,O3543,O644",
    },
  });
}

// 组织机构部门树
export function findOD() {
  return instance_1({
    url: "/dmcs/common/ours/findOD",
    method: "post",
    params: {
      rootIds:
        "O2925,O3251,O3259,O2929,OID-REV-GLDW,O3525,OID-REV-GLDW-440104,O3527,OID-REV-GLDW-440105,O3529,OID-REV-GLDW-440103,O3531,OID-REV-GLDW-440106,O3533,OID-REV-GLDW-440111,O3535,OID-REV-GLDW-440112,O3539,OID-REV-GLDW-440114,O3537,OID-REV-GLDW-440113,O3545,OID-REV-GLDW-440115,O3541,OID-REV-GLDW-440184,O3543,OID-REV-GLDW-440183",
    },
  });
}

// 组织单位树
export function findOOData() {
  return instance_1({
    url: "/dmcs/common/ours/findOO",
    method: "post",
    params: {
      rootIds: "O3251,O3259,O2927,O3231,O2933",
    },
  });
}
// 受邀范围树
export function findDbOU(params) {
  console.log(params);
  return instance_1({
    url: "/dmcs/common/ours/findDbOU",
    method: "get",
    params: {
      rootIds: "O3521,O3523,O3251,O3259,O2927,O3231,O2933",
      allFlag: "0",
      jcDm: params.jcDm || params || "3",
    },
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}
// 受邀范围树
export function findDbOUDW(params) {
  console.log(params);
  return instance_1({
    url: "/dmcs/common/ours/findDbDW",
    method: "get",
    params: {
      rootIds: "O3521,O3523,O3251,O3259,O2927,O3231,O2933",
      allFlag: "0",
      jcDm: params.jcDm || params || "3",
    },
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}
export function getBygroupId(params) {
  console.log(params);
  return instance_1({
    url: "/sysManager/userGroup/getBygroupId",
    method: "get",
    params: {
      groupId: params,
    },
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}
// 受邀范围树(镇街)
export function findDbOUUnion(params) {
  console.log(params);
  return instance_1({
    url: "/dmcs/common/ours/findAllDbOUUnion",
    method: "get",
    params: {
      rootIds: "O3521,O3523,O3251,O3259,O2927,O3231,O2933,O9999",
      allFlag: "0",
      jcDm: params.jcDm || params || "3",
    },
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

// // 受邀范围树
// export function findDbOUForCommunitySchedule(params) {
//   return instance_1({
//     url: "/dmcs/common/ours/findDbOU",
//     method: "get",
//     params: {
//       rootIds: "O1028,O1140,O435,O329,O1128,O968,O139,O862,O306,O283,O260,O816,O3521,O3523",
//       allFlag: "0",
//       jcDm: params.jcDm || '3',

//     },
//     headers: {
//       "Content-Type": "application/x-www-form-urlencoded",
//     },
//   });
// }

//常委会组成成员树
export function getfindDbOUApi(jcDm) {
  return instance_1({
    url: "/dmcs/common/ours/findDbOU",
    method: "get",
    params: {
      rootIds: "O3251",
      allFlag: "0",
      jcDm: jcDm || "3",
    },
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}
// 工作人员树
export function personnelOU() {
  return instance_1({
    url: "/dmcs/common/ours/findOU",
    method: "post",
    params: {
      rootIds:
        "O2929,OID-REV-GLDW,O3525,O998,O3527,O166,O3529,O447,O3531,O1001,O3533,O554,O3535,O510,O3539,O1142,O3537,O439,O3545,O523,O3541,O1041,O3543,O644",
    },
  });
}

// 获取会议性质下拉数据列表
export function getActivityNatureList() {
  return instance_1({
    url: "/dictCode/common/getActivityNatureList",
    method: "get",
  });
}

// 获取会议类型下拉数据列表
export function getActivityTypeData() {
  return instance_1({
    url: "/dictCode/common/getActivityTypeList",
    method: "get",
  });
}

// 获取会议类型下拉数据列表
export function taskReject(params) {
  return instance_1({
    url: "/dutyActive/taskReject",
    method: "get",
    params: params,
  });
}

// 履职活动导出数据
export function downloaExcelData(proposalId) {
  return instance_1({
    url: `/dutyActive/exportByIds`,
    method: "get",
    responseType: "blob",
    params: { ids: proposalId },
  });
}

// 检查录入冲突
export function checkConflict(data) {
  return instance_1({
    url: "/dutyActive/checkConflict",
    method: "post",
    data: data,
  });
}

// 获取会议类型下拉数据列表
export function rejectToCreator(params) {
  return instance_1({
    url: "/dutyActive/rejectToCreator",
    method: "get",
    params: params,
  });
}

// 修改基础信息
export function updateBaseInfo(data) {
  return instance_1({
    url: "/dutyActive/updateBaseInfo",
    method: "post",
    params: data,
  });
}

// 删除已终审的数据
export function deleteForComplete(params) {
  return instance_1({
    url: "/dutyActive/deleteForComplete",
    method: "post",
    params: params,
  });
}

// 获取活动登记首页配置项列表
export function getActivityTypeItemConfigList(params) {
  return instance_1({
    url: "/activityTypeItemConfig/list",
    method: "get",
    params: params,
  });
}

// 登录角色权限受邀范围树
export function findDbOrgUserTreeByRole(params) {
  console.log(params);
  return instance_1({
    url: "/dmcs/common/ours/findDbOrgUserTreeByRole",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

export function findDbOrgUserTreeByRoleForLiaison(params) {
  console.log(params);
  return instance_1({
    url: "/dmcs/common/ours/findDbOrgUserTreeByRoleForLiaison",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

export function findDbOrgUserTreeByRoleForLiaisonOrderByXsbh(params) {
  console.log(params);
  return instance_1({
    url: "/dmcs/common/ours/findDbOrgUserTreeByRoleForLiaisonOrderByXsbh",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}
