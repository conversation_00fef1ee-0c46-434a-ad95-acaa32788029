<template>
  <div v-show="isShow">
    <FormItem
      :label="label"
      :prop="prop"
      :span="span"
      :xs="xs"
      :sm="sm"
      :md="md"
      :lg="lg"
      :xl="xl"
      :xxl="xxl"
    >
      <a-select
        :value="input"
        :mode="multiple ? 'multiple' : undefined"
        :placeholder="
          cascadeLabel
            ? `请先选择${cascadeLabel}`
            : placeholder || `请选择${placeholderLabel}`
        "
        :disabled="disabled || !!cascadeLabel"
        :allow-clear="allowClear"
        @change="onChange"
        :filter-option="filterOption"
        :show-search="showSearch"
        :not-found-content="options.length === 0 ? '暂无数据' : null"
        style="width: 100%"
      >
        <a-select-option
          v-for="item in options"
          :key="item[optionsValue]"
          :label="item[optionsLabel]"
          :value="item[optionsValue]"
          >{{ item[optionsLabel] }}
        </a-select-option>
      </a-select>
    </FormItem>
  </div>
</template>

<script>
import FormItem from "@/components/FormItem/index.vue";

/**
 * 表单选择品组件。部分参数参考 <FormInput />
 *
 * 1.基础使用 <FormInput v-model="queryQuery.name" label="名 称" :options="[]" />
 * 2.修改选项的字段 <FormInput v-model="queryQuery.name" label="名 称" :options="[]" options-value="id" options-label="name" />
 * 2.多选 <FormInput v-model="queryQuery.name" label="名 称" :options="[]" multiple />
 */
export default {
  name: "FormSelect",
  components: { FormItem },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: [Number, Array, String],
      required: true,
      default: "",
    },
    prop: {
      type: String,
      required: false,
      default: undefined,
    },
    label: {
      type: String,
      required: true,
      default: "",
    },
    span: {
      type: [Number, String],
      default: undefined,
    },
    xs: {
      type: [Number, String],
      default: undefined,
    },
    sm: {
      type: [Number, String],
      default: undefined,
    },
    md: {
      type: [Number, String],
      default: undefined,
    },
    lg: {
      type: [Number, String],
      default: undefined,
    },
    xl: {
      type: [Number, String],
      default: undefined,
    },
    xxl: {
      type: [Number, String],
      default: undefined,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    options: {
      type: Array,
      default: () => [],
      required: true,
    },
    optionsLabel: {
      type: String,
      default: "name",
    },
    optionsValue: {
      type: String,
      default: "id",
    },
    // 级联标题
    cascadeLabel: {
      type: [String, Boolean],
      required: false,
      default: false,
    },
    allowClear: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: undefined,
    },
    isShow : {
      type: Boolean,
      default: true,
    },
    showSearch: {
      type: Boolean,
      default: true,
    },
    filterOption: {
      type: [Function, Boolean],
      default: (input, option) => {
        if (input) {
          return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                 (option.data.attrs.label && option.data.attrs.label.toLowerCase().indexOf(input.toLowerCase()) >= 0);
        }
        return true;
      },
    },
  },
  computed: {
    input() {
      return typeof this.value === "string" && !this.value
        ? undefined
        : this.value;
    },
    placeholderLabel() {
      return this.label.replace(/\s/g, "");
    },
  },
  methods: {
    onChange(value) {
      this.$emit("update:value", value);
      this.$emit("change", value);

      const it =
        value == null || value === undefined
          ? undefined
          : this.options.find((it) => it[this.optionsValue] === value);
      this.$emit("select", it);
    },
  },
};
</script>

<style lang="scss" scoped></style>
