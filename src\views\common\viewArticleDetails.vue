<template>
  <!-- 详情页内容 -->
  <a-modal
    title="查看文章"
    :visible.sync="visible"
    width="80%"
    @cancel="close"
    destroyOnClose
  >
    <div>
      <a-form-model
        :model="dataForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-row>
          <a-col
            ><a-form-model-item label="归属栏目">
              <div class="searchStyle">
                <a-input
                  disabled
                  style="width: 300px;"
                  v-model="inviteRangeDesc"
                  enter-button
                >
                </a-input>
                <a-button
                  type="primary"
                  icon="search"
                  @click="openAttributionColumn"
                >
                </a-button>
              </div>
              <a-checkbox
                v-model="dataForm.isexternalLinks"
                style="margin-left: 10px;"
                @change="dataForm.isexternalLinks == !dataForm.isexternalLinks"
                >外部链接</a-checkbox
              >
            </a-form-model-item></a-col
          >
          <a-col>
            <a-form-model-item label="标题" prop="title">
              <a-input style="width: 360px;" v-model="dataForm.title" />
              <span style="margin-left: 10px;">颜色：</span
              ><a-select style="width: 80px;" v-model="dataForm.color">
                <a-select-option value="0">默认</a-select-option>
                <a-select-option value="1">红色</a-select-option>
                <a-select-option value="2">绿色</a-select-option>
                <a-select-option value="3">蓝色</a-select-option>
                <a-select-option value="4">黄色</a-select-option>
                <a-select-option value="5">橙色</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col v-if="dataForm.isexternalLinks">
            <a-form-model-item label="外部链接">
              <a-input style="width: 360px;" v-model="dataForm.externalLinks" />
              <span style="margin-left: 10px;">绝对或相对地址。</span>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="关键字">
              <a-input style="width: 360px;" v-model="dataForm.keywords" />
              <span style="margin-left: 10px;"> 多个关键字，用空格分隔。</span>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="排序">
              <a-input style="width: 80px;" v-model="dataForm.weight" />
              <a-checkbox
                style="margin-left: 10px;"
                v-model="dataForm.isTopping"
              >
                置顶
              </a-checkbox>
            </a-form-model-item>
            <a-form-model-item label="过期时间">
              <a-date-picker
                style="width: 240px;"
                v-model="dataForm.weightDate"
                allow-clear
                value-format="YYYY-MM-DD"
                placeholder="选择开始时间"
              ></a-date-picker>
              <span style="margin-left: 10px;"
                >数值越小排序越靠前，过期时间可为空，过期后取消置顶。</span
              >
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="摘要">
              <a-input
                style="width: 360px;"
                v-model="dataForm.description"
                type="textarea"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="图片文件">
              <div>
                <span v-if="dataForm.picturelist.length == 0">无</span>
                <!-- <span v-else v-for="(item,index)in dataForm.picturelist" :key="index">{{item}}</span> -->
              </div>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="文档文件">
              <div>
                <span v-if="dataForm.filelist.length == 0">无</span>
                <!-- <span v-else v-for="(item,index)in dataForm.filelist" :key="index">{{item}}</span> -->
              </div>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="音频文件">
              <div>
                <span v-if="dataForm.audiolist.length == 0">无</span>
                <!-- <span v-else v-for="(item,index)in dataForm.audiolist" :key="index">{{item}}</span> -->
              </div>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="视频文件">
              <div>
                <span v-if="dataForm.videolist.length == 0">无</span>
                <!-- <span v-else v-for="(item,index)in dataForm.videolist" :key="index">{{item}}</span> -->
              </div>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="正文">
              <editor
                v-model="dataForm.content"
                :min-height="192"
                :height="230"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="获取渠道">
              <a-input style="width: 360px;" v-model="dataForm.copyfrom" />
            </a-form-model-item>
          </a-col>
          <!-- <a-col>
                  <a-form-model-item label="相关文章">
                    <a-button @click="openRelatedArticles">添加相关</a-button>
                  </a-form-model-item>
                </a-col> -->
          <a-col>
            <a-form-model-item label="是否允许评论">
              <a-radio-group v-model="dataForm.allowComment">
                <a-radio :value="1"> 是 </a-radio>
                <a-radio :value="2"> 否 </a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="推荐单">
              <a-checkbox
                v-model="dataForm.Recommended"
                style="margin-left: 10px;"
              >
                栏目页文章推荐
              </a-checkbox>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="发布时间">
              <a-date-picker
                style="width: 240px;"
                v-model="dataForm.createDate"
                allow-clear
                value-format="YYYY-MM-DD"
                placeholder="选择开始时间"
              ></a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="发布状态">
              <a-radio-group v-model="dataForm.status">
                <a-radio :value="1"> 发布 </a-radio>
                <a-radio :value="2"> 审核 </a-radio>
                <a-radio :value="3"> 删除 </a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="自定义内容视图">
              <a-select style="width: 360px;" v-model="dataForm.pl">
                <a-select-option value="0">是</a-select-option>
                <a-select-option value="1">否</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item label="自定义视频参数">
              <a-input style="width: 360px;" v-model="dataForm.CVparameters" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <!-- 定义了插槽 -->
    <slot slot="footer" name="userFooter">
      <a-button type="primary" @click="close">关闭</a-button>
    </slot>
    <!-- 归属栏目 -->
    <attributionTree
      ref="attributionTree"
      @confirm="confirmattributionTree"
    ></attributionTree>
  </a-modal>
</template>
<script>
import attributionTree from "@/views/common/attributionTree.vue";
import { instance_1 } from "@/api/axiosRq";
import { log } from "@antv/g2plot/lib/utils";
export default {
  components: { attributionTree },
  data() {
    return {
      contentStatus: "内容添加",
      //内容添加表单
      dataForm: {
        title: "", //标题
        picturelist: [], // 图片数组
        filelist: [], // 文档数组
        audiolist: [], // 音频数组音频
        videolist: [], // 视频数组
        color: "默认", // 标题颜色
        isTopping: false, //是否置顶
        isexternalLinks: false, //外部链接
        allowComment: 1, //是否评论
        status: 1, //发布状态
        weight: 0, //排序
        Recommended: false, // 推荐位
      },
      visible: false,
      inviteRangeDesc: "",
    };
  },
  created() {},
  methods: {
    //打开归属栏目
    openAttributionColumn() {
      this.$refs.attributionTree.initOrgTree();
    },
    //归属栏目返回的结果
    confirmattributionTree(data) {
      this.inviteRangeDesc = data.node.dataRef.name;
      this.fetchData();
    },
    // 数据渲染
    fetchData() {
      //   instance_1({
      //     url: "home/notice/findArticleContent",
      //     method: "get",
      //     params: this.dialogFormdata,
      //   }).then((res) => {
      //     if (res.data.code == "0000") {
      //     //   this.dataForm = res.data.data;
      //     }
      //   });
    },

    // 关闭窗口
    close() {
      this.visible = false;
      Object.assign(this.$data, this.$options.data());
    },
  },
};
</script>
<style lang="scss" scoped>
// 自定义搜索框样式
.searchStyle {
  display: flex !important;
  .ant-input {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  .ant-btn-primary {
    width: 66px;
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
.formBox {
  padding: 20px;
}
</style>
