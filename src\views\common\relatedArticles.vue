<template>

  <!-- 详情页内容 -->
  <a-modal title="添加相关"
           :visible.sync="visible"
           width="80%"
           @cancel="close"
           destroyOnClose>
    <div class="table-container">
      <a-row class="formBox">
        <a-form-model ref="queryForm"
                      :model="queryForm"
                      layout="inline">
          <a-col span="8">
            <a-form-model-item label="栏目">
              <div class="searchStyle">
                <a-input disabled
                         v-model="inviteRangeDesc"
                         enter-button>
                </a-input>
                <a-button type="primary"
                          icon="search"
                          @click="openAttributionColumn">
                </a-button>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col span="10">
            <a-form-model-item label="文档标题">
              <a-input v-model="queryForm.title"
                       autocomplete="off"
                       allow-clear></a-input>
            </a-form-model-item>
            <a-button type="primary"
                      style="margin-left: 12px; margin-top: 2px"
                      @click="fetchData">搜索</a-button>
            <a-button style="margin-left: 12px;"
                      @click="reset"
                      class="pinkBoutton">重置</a-button>
          </a-col>
        </a-form-model>
      </a-row>
      <a-row>
        <standard-table :columns="columns"
                        :rowKey=" (record, index) => {return record.id;}"
                        :dataSource="dataSource"
                        :loading="TBloading"
                        :pagination="pagination"
                        :selectedRows.sync="selectedRows"
                        @selectedRowChange="onSelectChange"></standard-table>
      </a-row>
    </div>
    <!-- 定义了插槽 -->
    <slot slot="footer"
          name="userFooter">
      <a-button style="padding-left: 10px"
                type="primary"
                @click="confirm">确 定</a-button>
      <a-button type="primary"
                @click="close">关闭</a-button>
    </slot>
    <!-- 归属栏目 -->
    <attributionTree ref="attributionTree"
                     @confirm="confirmattributionTree"></attributionTree>
    <!-- 查看详情 -->
    <viewArticleDetails ref="viewArticleDetails"></viewArticleDetails>
  </a-modal>
</template>
<script>
import viewArticleDetails from "@/views/common/viewArticleDetails.vue";
import attributionTree from "@/views/common/attributionTree.vue";
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
import { instance_1 } from "@/api/axiosRq";
import { log } from '@antv/g2plot/lib/utils';
export default {
  components: { StandardTable, attributionTree, viewArticleDetails },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        flag: 0,
      },
      inviteRangeDesc: '',
      //选中tableKey
      tableKey: [],
      //tableData
      tableData: [],
      visible: false,
      dataSource: [{
        content: "12",
        score: "12",
        title: "baiot",
      }],
      selectedRows: [],
      //列头 
      columns: [
        {
          title: "栏目",
          align: "center",
          ellipsis: true,
          dataIndex: "category.name",
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  on: {
                    click: () => {
                      this.toDetailed(record);
                    },
                  },
                },
                text
              ),
            ]);
          },
        },
        {
          title: "标题",
          align: "center",
          ellipsis: true,
          dataIndex: "title",
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  on: {
                    click: () => {
                      // this.opentDetailed(record);
                    },
                  },
                },
                text
              ),
            ]);
          },
        },
        {
          title: "排序",
          align: "center",
          ellipsis: true,
          dataIndex: "sorte",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "点击数",
          align: "center",
          ellipsis: true,
          dataIndex: "hits",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "发布者",
          align: "center",
          ellipsis: true,
          dataIndex: "createBy",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "更新时间",
          align: "center",
          ellipsis: true,
          dataIndex: "createDate",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
      ],
    };
  },
  created () {

  },
  methods: {
    confirm () {
      this.visible = false;
      // console.log(this.selectedRows)
      this.$emit("confirm", this.selectedRows);
    },
    //重置
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        flag: 0,
      };
      this.inviteRangeDesc = "";
      this.fetchData();
    },
    //打开归属栏目树
    openAttributionColumn () {
      this.$refs.attributionTree.initOrgTree();
      // 弹窗
    },
    //归属栏目返回的结果
    confirmattributionTree (data) {
      this.queryForm.categoryId = data.node.dataRef.id;
      this.inviteRangeDesc = data.node.dataRef.name;
      this.fetchData();
    },
    // 弹窗详情
    opentDetailed () {
      this.$refs.viewArticleDetails.visible = true;
    },
    // 查看
    toDetailed () {

    },
    //复选框选改变
    onSelectChange (key, data) {
      this.selectedRows = data;
      this.tableKey = key;
      this.tableData = data;
    },
    // 数据渲染
    fetchData () {

      this.TBloading = true;
      instance_1({
        url: "contentmanager/issue/findContentList",
        method: "get",
        params: this.queryForm,
      }).then((res) => {

        if (res.data.code == "200") {
          this.dataSource = res.data.rows;

          this.TBloading = false;
        }
      });
    },


    // 关闭窗口
    close () {
      this.visible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
// 自定义搜索框样式
.searchStyle {
  display: flex !important;
  .ant-input {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  .ant-btn-primary {
    width: 66px;
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
</style>
