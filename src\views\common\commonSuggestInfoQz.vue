<template>
  <div>
    <a-modal :visible="visible"  width="80%" :centered="true" :footer="null"
      :body-style="{ height: '600px', overflowY: 'auto' }" @cancel="close">
        <div  class="title-container" slot="title">
          <div>
            <span class="title-text" style="font-size:18px; display:block; text-align:center; margin-bottom: 5px;">{{ record.proposalTitle }}</span>
          </div>
        </div>
      <EnterContent :alert="tipMessage"
      />
      <a-tabs v-model="tabsKey" type="card" @change="changeTab">
        <a-tab-pane key="1" tab="议案建议信息">
          <div style="display: flex; justify-content: center; align-items: center; width: 100%; margin-bottom: 10px;">
            <span style="font-weight: bold; margin-right: 990px;">建议号：{{ record.proposalNum }}</span>
            <span style="font-weight: bold;">状态：{{ record.status | filterStatus }}</span>
          </div>
          <div :style="tabStyle">
            <a-descriptions bordered size="small">
              <a-descriptions-item label="届次" :labelStyle="{ width:'150px' }">{{
                  record.periodDesc
                }}</a-descriptions-item>
              <a-descriptions-item label="议案建议类型">{{
                  record.proposalType | filterProposalType
                }}</a-descriptions-item>
              <a-descriptions-item label="内容所属类别">{{
                  record.proposalContentType | filterProposalContentType
                }}</a-descriptions-item>
              <a-descriptions-item label="领衔代表">{{
                  record.headPer
                }}</a-descriptions-item>
              <a-descriptions-item label="所在代表团">{{
                  record.deputyOrg
                }}</a-descriptions-item>
              <a-descriptions-item label="代表本人是否同意公开">{{
                  record.ifPublice == "0"
                    ? "不"
                    : record.ifPublice == "1"
                    ? "是"
                    : record.ifPublice == "2"
                      ? "已公开"
                      : ""
                }}</a-descriptions-item>
              <a-descriptions-item label="代表建议答复方式">{{
                  record.codeHaveOrNo == "1"
                    ? "书面"
                    : record.codeHaveOrNo == "2"
                    ? "网上"
                    : record.codeHaveOrNo == "0"
                    ? "只做工作参考用，无需正式答复"
                    : ""
                }}</a-descriptions-item>
              <a-descriptions-item label="代表与承办单位沟通方式">
                {{
                  record.ifContect == "1"
                    ? "需要见面座谈和调研"
                    : record.ifContect == "0"
                    ? "只需电话微信沟通"
                    : record.ifContect == "2"
                      ? "不需要沟通，直接答复"
                      : record.ifContect == "3"
                        ? "工作参考用，不需要正式书面答复"
                        : ""
                }}
              </a-descriptions-item>
              <a-descriptions-item label="多年多次提出、尚未解决">{{
                  record.overTheYearsNotResolved == "1"
                    ? "是"
                    : record.overTheYearsNotResolved == "0"
                    ? "否"
                    : record.overTheYearsNotResolved == "2"
                      ? "未详"
                      : ""
                }}</a-descriptions-item>
<!--              <a-descriptions-item label="标题" :span="2">{{-->
<!--                  record.proposalTitle-->
<!--                }}</a-descriptions-item>-->
              <a-descriptions-item label="意向承办单位" :span="3">{{
                  record.intentOrgName || ""
                }}</a-descriptions-item>
              <a-descriptions-item label="当前经办人" :span="3">
                <div v-if="record.yajyOrgList && record.yajyOrgList.length > 0">
                    <span v-for="(item, index) in record.yajyOrgList" :key="index">
                      {{
                        item.orgName +
                        "(" +
                        item.orgDispname +
                        item.orgDispphone +
                        ");"
                      }}
                    </span>
                </div>
                <div v-if="record.yajyOrgOperDTOList &&
                    record.yajyOrgOperDTOList.length > 0
                    ">
                    <span v-if="record.yajyOrgOperDTOList[0].orgCode == '387'">
                      <span>
                        {{
                          record.yajyOrgOperDTOList[0].orgName +
                          "(" +
                          record.yajyOrgOperDTOList[0].telNo +
                          ")"
                        }}
                      </span>
                      <br />
                    </span>
                  <span v-else>
                      <span v-for="(item, index) in record.yajyOrgOperDTOList" :key="index">
                        {{ item.operName + "(" + item.telNo + ")" }}
                      </span>
                    </span>
                </div>
                <div v-if="record.identityAllocationList && record.identityAllocationList.length > 0">
                    <span v-for="(item, index) in record.identityAllocationList" :key="index">
                      {{
                        item.orgName +
                        "(" +
                        item.userName +
                        item.mobile +
                        ");"
                      }}
                    </span>
                </div>

              </a-descriptions-item>
              <a-descriptions-item label="交办日期">{{
                  record.handleDate
                }}</a-descriptions-item>
              <a-descriptions-item label="办理期限至">主办：{{
                  majorLastTime ? majorLastTime.substring(0, 10) : ""
                }}
                会办：{{
                  minorLastTime ? minorLastTime.substring(0, 10) : ""
                }}</a-descriptions-item>
              <a-descriptions-item label="办理情况">{{
                  record.proposalHandle
                }}</a-descriptions-item>
              <a-descriptions-item label="代表工委是否公开意见" :span="1">{{
                  record.xlwPublic == "0" || record.xlwPublic == null
                    ? "未提出意见"
                    : record.xlwPublic == "1"
                    ? "建议公开"
                    : record.xlwPublic == "2"
                      ? "建议不公开"
                      : ""
                }}
                <a-button type="primary" @click="handleComment('xlw')" style="margin-left: 70px;">查看出具意见</a-button>
              </a-descriptions-item>
              <a-descriptions-item label="研究室建议是否公开" :span="1">{{
                  record.yjsPublic == "0" || record.yjsPublic == null
                    ? "未提出意见"
                    : record.yjsPublic == "1"
                    ? "建议公开"
                    : record.yjsPublic == "2"
                      ? "建议不公开"
                      : ""
                }}
                  <a-button type="primary" @click="handleComment('yjs')" style="margin-left: 70px;">查看出具意见</a-button>
              </a-descriptions-item>
              <a-descriptions-item label="建议审核是否公开发布" :span="1">{{
                  record.examineXlgw == "0"
                    ? "未审核"
                    : record.examineXlgw == "1"
                    ? "审核公开"
                    : record.examineXlgw == "2"
                      ? "审核不公开"
                      : ""
                }}
              </a-descriptions-item>
              <a-descriptions-item label="建议纸" :span="1" v-if="this.mediaType != '其他建议'"><a
                @click="downMotion">下载建议纸</a>
              </a-descriptions-item>
              <a-descriptions-item label="附件列表" :span="2" v-if="mediaType != '其他建议'"><span
                v-if="record.existFile == true">附件：<a @click="downFileData('1', '')">附件下载</a></span>
              </a-descriptions-item>
              <a-descriptions-item label="正文" :span="2">
                <a-space>
                  <a @click="lookWord">查看正文</a>
                  <a @click="downFileData('9', '')" v-if="this.mediaType != '其他建议'">下载正文附件</a>
                  <a @click="resetFile" v-if="this.mediaType != '其他建议'">重新上传正文附件</a>
                  <!-- <a @click="regenerateText">重新生成正文</a> -->
                </a-space>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="答复、反馈">
          <div style="display: flex; justify-content: space-between; width: 100%;">
            <span  style="font-weight: bold;">建议号：{{ record.proposalNum }}</span>
          </div>
          <div :style="tabStyle">
            <a-descriptions bordered size="small" style="margin-bottom: 10px;">
              <a-descriptions-item label="议案建议类型">{{
                  record.proposalType | filterProposalType
                }}</a-descriptions-item>
              <a-descriptions-item label="领衔代表">{{
                  record.headPer
                }}</a-descriptions-item>
              <!-- 电话 短信 邮件 微信：0或1或4      2  座谈会  调研    3   代表表示无需见面沟通 -->
              <a-descriptions-item label="答复1沟通方式">{{
                  record.dffs == "1" || record.dffs == "0" || record.dffs == "4"
                    ? "电话 短信 邮件 微信"
                    : record.dffs == "2"
                    ? "座谈会 调研"
                    : record.dffs == "3"
                      ? "代表表示无需见面沟通"
                      : ""
                }}</a-descriptions-item>
            </a-descriptions>
                <a-descriptions v-if="majorUndertakeDfList.length > 0" class="red-line" title="主办单位"></a-descriptions>
            <template v-for="item in majorUndertakeDfList" v-if="majorUndertakeDfList.length > 0">
              <a-descriptions  :key="item.undertakeId" size="small" bordered >
                <a-descriptions-item labale label="单位名称" :span="3">{{
                    item.orgName
                  }}</a-descriptions-item>
                <a-descriptions-item label="签收人" :span="1">{{
                    item.signName
                  }}</a-descriptions-item>
                <a-descriptions-item label="签收人手机" :span="1"><a>{{ item.signPhone }}</a></a-descriptions-item>
                <a-descriptions-item label="是否签收" :span="1">{{
                    item.signTime ? item.signTime : "未签收"
                  }}</a-descriptions-item>
                <a-descriptions-item label="经办人" :span="2">部门：{{ item.jbrDept }} 职务：{{ item.jbrJob }} 姓名：{{
                    item.jbrName
                  }}</a-descriptions-item>
                <a-descriptions-item label="经办人手机" :span="1"><a>{{ item.jbrPhone }}</a></a-descriptions-item>
              </a-descriptions>
              <template v-for="(itemdf, index) in item.dfList">
                <a-descriptions :key="itemdf.dfId" size="small" bordered>
                  <a-descriptions-item :label="`第${index + 1}次答复时间`" :span="1">
                    {{ itemdf.dfrq }}
                  </a-descriptions-item>
                  <a-descriptions-item label="答复类型" :span="1">
                    <span v-if="itemdf.dflb == 'A'">已解决或基本解决的问题</span>
                    <span v-if="itemdf.dflb == 'B'">列入年度计划解决的问题</span>
                    <span v-if="itemdf.dflb == 'C'">列入规划逐步解决的问题</span>
                    <span v-if="itemdf.dflb == 'D'">受法规、政策、财力等客观条件限制，难以实现</span>
<!--                    <span>{{ itemdf.dflb }}</span>-->
                  </a-descriptions-item>
                  <a-descriptions-item label="答复公开" :span="1"><span>{{ itemdf.isPublic == "1" ? "是" : "否" }}</span>
                  </a-descriptions-item>
                  <a-descriptions-item v-if="itemdf.isPublic == '0'" label="答复不公开理由" :span="3"><span>{{
                      itemdf.remarkText }}</span>
                  </a-descriptions-item>
                  <a-descriptions-item label="答复附件" :span="3"><a v-if="itemdf.fujianList.length > 0"
                                                                 @click="downFileDataDf(itemdf, 2)">【下载答复附件】</a>
                  </a-descriptions-item>
                  <a-descriptions-item v-if="itemdf.hisEvaluation" :label="`第${index + 1}次反馈时间`" :span="1">
                    {{ itemdf.hisEvaluation.submittime }}
                  </a-descriptions-item>
                  <a-descriptions-item v-if="itemdf.hisEvaluation" label="反馈意见" :span="1"><span
                    v-if="itemdf.hisEvaluation.score >= 80">满意</span>
                    <span v-if="itemdf.hisEvaluation.score >= 60 &&
                        itemdf.hisEvaluation.score < 80
                        ">基本满意</span>
                    <span v-if="itemdf.hisEvaluation.score < 60">不满意</span>
                  </a-descriptions-item>
                  <a-descriptions-item v-if="itemdf.hisEvaluation" label="反馈内容" :span="1"><a
                    @click="viewFkEvaluation(itemdf.hisEvaluation)">【查看反馈内容】</a>
                  </a-descriptions-item>
                </a-descriptions>
              </template>
            </template>
            <a-descriptions v-if="minorUndertakeDfList.length > 0" class="red-line" title="会办单位"></a-descriptions>
            <template v-for="item in minorUndertakeDfList" v-if="minorUndertakeDfList.length > 0">
              <a-descriptions :key="item.undertakeId" size="small" bordered >
                <a-descriptions-item  label="单位名称" :span="3">{{
                    item.orgName
                  }}</a-descriptions-item>
                <a-descriptions-item label="签收人" :span="1">{{
                    item.signName
                  }}</a-descriptions-item>
                <a-descriptions-item  label="签收人手机" :span="1"><a>{{ item.signPhone }}</a></a-descriptions-item>
                <a-descriptions-item  label="是否签收" :span="1">{{
                    item.signTime ? item.signTime : "未签收"
                  }}</a-descriptions-item>
                <a-descriptions-item  label="经办人" :span="2">部门：{{ item.jbrDept }} 职务：{{ item.jbrJob }} 姓名：{{
                    item.jbrName
                  }}</a-descriptions-item>
                <a-descriptions-item  label="经办人手机" :span="1"><a>{{ item.jbrPhone }}</a></a-descriptions-item>
              </a-descriptions>
              <template v-for="itemdf in item.dfList">
                <a-descriptions class="hbdwDfInfo" :key="itemdf.dfId" size="small" bordered>
                  <a-descriptions-item label="答复时间" :span="1">
                    {{ itemdf.dfrq }}
                  </a-descriptions-item>
                  <a-descriptions-item label="附件名称" :span="2"><a v-if="itemdf.fujianList.length > 0"
                                                                 @click="downFileDataDf(itemdf, 2)">【下载答复附件】</a>
                  </a-descriptions-item>
                </a-descriptions>
              </template>
            </template>
          </div>
        </a-tab-pane>
        <a-tab-pane key="3" tab="提出代表">
          <div :style="tabStyle">
            <a-space v-if="userData.authorities
                ? record.status < 90 &&
                (userData.authorities[0].authority == 'YAJY_QRD' ||
                  userData.authorities[0].authority == 'YAJY_XLW' ||
                  userData.authorities[0].authority == 'YAJY_DBC' ||
                  userData.authorities[0].authority == 'I-XTGLY' ||
                  userData.authorities[0].authority == 'YAJY_DB')
                : false
                " class="operator">
              <a-button type="primary" icon="plus-circle" @click="showUserTable('提出')"
                        v-if="this.mediaType != '其他建议'">新建</a-button>
              <a-button icon="delete" @click="delJointlyList" v-if="this.mediaType != '其他建议'">删除</a-button>
            </a-space>
            <standard-table row-key="jointlyId" :columns="deputationColumns"
                            :selected-rows.sync="deputationSelectedRows" :data-source="deputationDataSource" :show-alert="userData.authorities
                  ? record.status < 90 &&
                  (userData.authorities[0].authority == 'YAJY_QRD' ||
                    userData.authorities[0].authority == 'YAJY_XLW' ||
                    userData.authorities[0].authority == 'YAJY_DBC' ||
                    userData.authorities[0].authority == 'I-XTGLY' ||
                    userData.authorities[0].authority == 'YAJY_DB')
                  : false
                  ">
            </standard-table>
          </div>
        </a-tab-pane>
        <a-tab-pane key="4" tab="承办单位">
          <div :style="tabStyle">
            <standard-table row-key="undertakeId" :columns="unitColumns" :data-source="unitDataSource">
            </standard-table>
          </div>
        </a-tab-pane>
        <a-tab-pane key="5" tab="流程跟踪">
          <div :style="tabStyle">
            <!-- 步骤条 -->
            <div class="step-container">
              <div v-for="(step, index) in processDataSource" :key="step.historyId" class="step"
                   :style="{ position: 'relative', marginBottom: index < processDataSource.length - 1 ? '20px' : '0' }">
                <div class="step-circle step-active step-completed">
                </div>
                <div class="step-content">
                  <!-- replyType在最上面，居中显示 -->
                  <div class="step-reply-type">{{ step.replyType }}</div>
                  <div style="margin-left:20px">
                    <div class="step-reply-time">{{ step.replyTime }}</div>

                    <!-- 时间、提交人、办理意见在一行内，用灰色背景包裹，使用竖线分隔 -->
                    <div class="step-info">
                      <!-- 提交人、处理人 -->
                      <div class="info-item" style="width:20%">
                          <span v-if="index == 0">
                            <strong style="margin-right:10px">提交人:</strong> {{ step.reply_by_name }}
                          </span>
                        <span v-else>
                            <strong style="margin-right:10px">处理人:</strong> {{ step.reply_by_name }}
                          </span>
                      </div>
                      <div class="info-item" style="width:60%">
                             <span v-if="step.replyComment" class="comment-text" :title="step.replyComment" @click="clickRowData(step)">
                              <div class="text-container"><strong style="margin-right:10px">办理结果:</strong>
                               {{ step.replyComment.length >= 90 ? step.replyComment.slice(0, 87)+"..."  : step.replyComment  }} </div>
                          </span>
                          <span v-else>
                            <strong style="margin-right:10px">办理结果:</strong> ------
                          </span>
                        <a v-if="step.fujianList" @click="downFileDataUnder(step, 50)">【下载不予立案理由】</a>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-if="index < processDataSource.length - 1" class="step-line" style="height: 100%"></div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-modal>

  </div>
</template>
<script>
// 建议详情 显示详情
import EnterContent from "@/components/EnterContent/indexList.vue";
import submitForCensorship from '@/views/common/submitReturn';
import jbrTable from "@/views/common/jbrTable.vue";
import { fileUpload } from "@/api/commonApi/file.js";
import {
  getDicts,
} from "@/api/parameterManagement/data";
import {
  proposalById,
  findCommentByType,
  getJointlyList,
  delJointly,
  getLocalHistory,
  getDfhData,
  getProposalUnder,
  seeText,
  getUndertakeDfAndFeedbackList,
  againUploadTextFile,
  downloadProposal,
  getUndertakeDfInfo,
  findOpinionOption,
} from "@/api/myJob/myProposal.js";
import {proposalQzById} from "@/api/myJob/myProposalQz.js";
import moment from "moment";
import { downFile, getUserData_yajy, downDfhFile } from "@/api/commonApi/file.js";
export default {
  components: {
    EnterContent,
    adjustListEdit,
    jbrTable,
    submitForCensorship,
  },

  watch: {
    // visible(val) {
    //   if (val) {
    //     this.getOneUser();
    //   }
    // },
    "resourceForm.text": {
      handler: function (val) {
        let TEXT = null;
        this.resourceForm.text.map((i, n) => {
          TEXT ? (TEXT += n + 1 + "." + i + "  ") : (TEXT = "1." + i + "  ");
        });
        // this.resourceForm.comment = TEXT
        if (this.resourceForm.text.toString().indexOf("其他") != -1) {
          this.isOtheryj = true;
          // this.resourceForm.comment += '(其他原因：)'
        } else {
          this.isOtheryj = false;
        }
      },
    },
  },
  filters: {
    filterStatus(text) {
      if (text === 10) {
        return "草稿";
      }
      if (text === 11) {
        return "联名中";
      }
      if (text === 20) {
        return "校核中";
      }
      if (text === 21) {
        return "分类中";
      }
      if (text === 22) {
        return "初审中";
      }
      if (text === 30 || text === 25) {
        return "复审中";
      }
      if (text === 33) {
        return "研究室出具意见中";
      }
      if (text === 40) {
        return "交办中";
      }
      if (text === 41) {
        return "不予立案审核中";
      }
      if (text === 42) {
        return "分办确认中";
      }
      if (text === 43) {
        return "分办二次确认中";
      }
      if (text === 45) {
        return "不予立案确认中";
      }
      if (text === 50) {
        return "签收中";
      }
      if (text === 60) {
        return "答复中";
      }
      if (text === 70) {
        return "待反馈";
      }
      if (text === 80) {
        return "已反馈";
      }
      if (text === 90) {
        return "办毕";
      }
    },
    filterProposalContentType(text) {
      if (text == "JYFL01") return "法制";
      if (text == "JYFL02") return "监察和司法";
      if (text == "JYFL03") return "经济";
      if (text == "JYFL04") return "城建环资";
      if (text == "JYFL05") return "农村农业";
      if (text == "JYFL06") return "教科文卫";
      if (text == "JYFL07") return "华侨外事民族宗教";
      if (text == "JYFL08") return "其他";
      if (text == "JYFL09") return "预算";
      if (text == "JYFL10") return "社会建设";
    },
    filterProposalType(text) {
      if (text == "1") return "大会议案";
      if (text == "2") return "大会建议";
      if (text == "3") return "闭会建议";
      if (text == "4") return "供参考建议";
    },
  },
  data() {
    var hongzixinxibiao = (rule, value, callback) => {
      if (
        this.resourceForm.orgEvaluation == "" ||
        this.resourceForm.orgEvaluation == undefined
      ) {
        callback(new Error("请输入建议内容质量评分"));
      } else {
        callback();
      }
    };
    return {
      //字典表单
      detailsForm: {status:''},
      selectedRowKeys: [], // 选中的行key
      originalUndertakeList: [], // 存储API返回的原始数据
      isRendered: false,
      stepsData: [
        {
          "historyId": "6bbe7b47a74989414c967dec9f729469",
          "proposalId": "e926ec2239cbf9972a0b76ee835daa76",
          "replyBy": "45019",
          "replyByCode": "1028",
          "replyStatus": 10,
          "replyTime": "2024-07-09 09:59:14",
          "replyType": "正式提交",
          "reply_by_name": "雷*威",
          "revived": "0"
        },
        {
          "historyId": "2cb41dceb0a858337bdfed1a288892aa",
          "jbrPhone": "100******00",
          "proposalId": "e926ec2239cbf9972a0b76ee835daa76",
          "replyBy": "48305",
          "replyByCode": "1028",
          "replyComment": "",
          "replyStatus": 20,
          "replyTime": "2024-12-06 10:32:47",
          "replyType": "校核通过",
          "reply_by_name": "越*员",
          "revived": "0"
        }
      ],
      tipMessage:"",
      isMeeting:"",
      mediaType: "",
      userName: null,
      OpinionOptionList: [
        "建议内容不属于广州市行政区域内事务或者行政区域职权范围。",
        "建议内容不够明确具体，请进一步加强调研，反映实际情况，分析问题原因，提出改进工作、解决问题、完善政策的具体措施。",
        "存在《广东省各级人民代表大会代表建议、批评和意见办理规定》第十条所规定的不应当作为代表建议提出的若干情形。",
        "代表本人要求退回。",
        "其他",
      ],
      current: 0,
      isOtheryj: false,
      isPublicDisable: false,
      listLoading: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      tabStyle: { maxHeight: "600px", overflowY: "auto" }, // 对话框中的 tab 内容高度及垂直滚动条设置
      visible: false,
      proposalId: "", //父组件直接赋值 议题id
      fkVisible: false,
      fkEvaluation: "",
      fkContent: "",
      procInstId: "",
      defaultNode:"",
      actTypeReturn:"",
      majorUndertakeDfList: [],
      minorUndertakeDfList: [],
      ProposalUndertakeDfhList: [],
      record: { unIfmainList: [], ifmainList: [] },
      iShow: false,
      replyComment: "",
      userData: {},
      publicComment:"",
      resourceForm: {
        meetName: "",
        meet: "",
        text: [],
        host: "",
        hostName: "",
        adjust: "",
        adjustName: "",
        publicComment:"",
        isPublic: "",
        ifPublice:"",
        xlwIfPublice:"1",
        historyId:undefined,
        yjsIfPublice:"1",
        xlwCheck: "1",
        subLeaderCheck:"1",
        leaderCheck:"1",
        zwSug:"",
        dbSug:"",
        dffs: [],
        jbrName: "",
        jbrDept: "",
        jbrJob: "",
        jbrPhone: "",
        jbrOph: "",
        dflb: null,
        orgCode: "",
        mainHandleLastTimeStr: "",
        minorHandleLastTimeStr: "",
        finalAssignTimeStr: "",
        contentType: "",
        remark: "",
        remarkText: null
      },
      delayFormRules: {
        comment: [
          { required: true, message: "请输入延期描述", trigger: "blur" },
        ],
        delayDay: [
          { required: true, message: "请输入延期天数", trigger: "blur" },
        ],
      },
      rules: {
        jbrName: [
          { required: true, message: "请输入经办人姓名", trigger: "blur" },
        ],
        jbrDept: [
          { required: true, message: "请输入经办人部门", trigger: "blur" },
        ],
        jbrJob: [
          { required: true, message: "请输入经办人职务", trigger: "blur" },
        ],
        jbrPhone: [
          { required: true, message: "请输入经办人手机", trigger: "blur" },
        ],
        jbrOph: [
          { required: true, message: "请输入经办人办公电话", trigger: "blur" },
        ],
        dflb: [
          { required: true, message: "请输入答复类型", trigger: "change" },
        ],
        dffs: [
          { required: true, message: "请输入沟通方式", trigger: "change" },
        ],
        isPublic: [
          { required: true, message: "请输入答复是否公开", trigger: "change" },
        ],
        remark: [
          { required: true, message: "请选择答复不公开理由", trigger: "change" },
        ],
        remarkText: [
          { required: true, message: "请填写答复不公开其他理由", trigger: "blur" },
        ],
        zwSug: [
          {
            required: true,
            message: "请选择专委转供参考意见",
            trigger: "change",
          },
        ],
        public: [
          {
            required: true,
            message: "请选择是否公开",
            trigger: "blur",
          },
        ],
        contentType: [
          {
            required: true,
            message: "请选择内容分类",
            trigger: "change",
          },
        ],
        dbSug: [
          {
            required: true,
            message: "请选择代表转供参考意见",
            trigger: "change",
          },
        ],
        orgEvaluation: [
          {
            required: true,
            message: "请输入建议内容质量评分",
            trigger: "blur",
            validator: hongzixinxibiao,
          },
        ],
      },
      unitColumns: [
        // 承办单位表格头定义
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "ifmain",
          customRender: (text, record, index) =>
            text == 1 ? "主办单位" : "会办单位",
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "办理期限至",
          ellipsis: true,
          dataIndex: "handleLastTime",
          customRender: (text) => {
            return text ? text.substring(0, 10) : "";
          }
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
      ],
      unitDataSource: [
        // 承办单位表格数据
      ],
      processColumns: [
        // 流程跟踪表格头定义
        {
          title: "状态",
          ellipsis: true,
          dataIndex: "replyType",
        },
        {
          title: "处理人",
          ellipsis: true,
          dataIndex: "reply_by_name",
        },
        {
          title: "处理时间",
          ellipsis: true,
          dataIndex: "replyTime",
        },
        {
          title: "办理意见",
          ellipsis: true,
          dataIndex: "replyComment",
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#1890ff",
                  },
                  on: {
                    click: () => {
                      this.clickRowData(record);
                    },
                  },
                },
                record.replyComment
              ),
            ]);
          },
        },
      ],
      processDataSource: [
        // 流程跟踪数据
      ],
      flowNodeDataSource:[
        //流程节点数据
      ],
      dfhColumns: [
        // 公文答复函表格头定义
        {
          title: "案号",
          ellipsis: true,
          dataIndex: "proposalNum",
          width: 300,
        },
        {
          title: "标题",
          ellipsis: true,
          dataIndex: "dfhTitle",
          width: 600,
        },
        {
          title: "发文单位",
          ellipsis: true,
          dataIndex: "orgName",
          width: 200,
        },
        {
          title: "答复函",
          ellipsis: true,
          dataIndex: "dfhId",
          width: 400,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#1890ff",
                  },
                  on: {
                    click: () => {
                      //答复函附件类型 40
                      this.downFileDataDfh(record);
                    },
                  },
                },
                "下载"
              ),
            ]);
          },
        },
      ],
      dfhDataSource: [
        // 答复函数据
      ],
      deputationColumns: [
        // 提出代表表格头定义
        {
          title: "类别",
          ellipsis: true,
          dataIndex: "ifpublic",
          customRender: (text, record, index) => {
            if (text == 1) return "领衔";
            if (text == 0) return "联名";
          },
        },
        {
          title: "代表姓名",
          ellipsis: true,
          dataIndex: "name",
        },
        {
          title: "通讯地址",
          ellipsis: true,
          dataIndex: "unitAddress",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "unitPhone",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "mobilePhone",
        },
        {
          title: "Email",
          ellipsis: true,
          dataIndex: "",
        },
        {
          title: "邮政编码",
          ellipsis: true,
          dataIndex: "",
        },
        {
          title: "申请时间",
          ellipsis: true,
          dataIndex: "createTime",
        },
        {
          title: "反馈时间",
          ellipsis: true,
          dataIndex: "agreen",
        },
        {
          title: "联名状态",
          ellipsis: true,
          dataIndex: "ifagreen",
          customRender: (text, record, index) => {
            if (text == 1) return "同意";
            if (text == 0) return "不同意";
            if (text == null) return "未处理";
          },
        },
      ],
      // 提出列表table
      deputationDataSource: [],
      // 提出列表选中数据
      deputationSelectedRows: [],
      unitType: "", //单位类型
      returnValue: false,
      tabsKey: "1",
      activeTabKey: "basic",
      fileTextList: [], //答复附件
      fileUnRegisterList: [], //不予立案
      perType: "", //打开代表列表的场景
      scenesStatus: "", // 场景默认是 处理延期
      dataSourcePostpone: [], //处理延期数据
      selectedRowsPostpone: [], //处理延期数据-已选
      dialogVisiblePublicComment:false ,//公开意见弹窗
      dialogVisiblePublicCommentFs:false ,//公开意见弹窗-修改
      columnsPostpone: [
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "",
          customRender: (text, record, index) => {
            return record.ifmain == 1 ? "主办单位" : "会办单位";
          },
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "申请时间",
          ellipsis: true,
          dataIndex: "applyDelayTime",
        },
        {
          title: "延期时长",
          ellipsis: true,
          dataIndex: "delayTimeTo",
        },
        {
          title: "调整原因",
          ellipsis: true,
          dataIndex: "applyCommnent",
        },
        {
          title: "附件名称",
          scopedSlots: { customRender: "action" },
        },
      ], //处理延期数据/单位调整 - 表头
      unitModalVisibleColumns: [
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "ifmain",
          customRender: (text, record, index) => {
            if (text == "1") return "主办单位";
            if (text == "0") return "会办单位";
          },
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
        {
          title: "签收状态",
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            if (text == 0) return "调整中";
            if (text == 1) return "签收中";
            if (text == 2) return "答复中";
            if (text == 3) return "待反馈";
            if (text == 5) return "拟删除";
            if (text == 6) return "拟改变单位性质";
            if (text == 7) return "拟添加";
          },
        },
      ], //弹出的单位调整列表
      undertakeId: "", //子流程id
      visiblePostpone: false,
      keepType: "",
      showOperate: true, //是否显示操作 默认显示
      modalTitle: "处理延期", //延期/调整弹出框
      unitModalVisible: false, //单位调整弹出框
      unitModalVisibleSelectedRows: [], //单位调整已选数据
      unitModalVisibleDataSource: [], //单位调整列表
      oldunitModalVisibleDataSource: [], //原单位调整列表

      unitModalInfoVisible: false, //会办单位调整信息

      unitMinorModalVisible: false, //调整会办单位弹出框
      unitMinorModalVisibleSelectedRows: [], //调整会办单位已选数据
      unitMinorModalVisibleDataSource: [], //调整会办单位列表
      changeIfMainMinorVisible: false, //调整会办单位弹出框 修改主办/会办

      changeIfMainVisible: false, //单位调整弹出框 修改主办/会办
      fileVisible: false, //重新上传文件窗口开关
      fileTextListReset: [], //重新上传文件列表
      isFileReset: undefined,
      contentTypeList: [
        // 内容分类
        { id: "JYFL01", name: "法制" },
        { id: "JYFL02", name: "监察和司法" },
        { id: "JYFL09", name: "预算" },
        { id: "JYFL03", name: "经济" },
        { id: "JYFL04", name: "城建环资" },
        { id: "JYFL05", name: "农村农业" },
        { id: "JYFL06", name: "教科文卫" },
        { id: "JYFL07", name: "华侨外事民族宗教" },
        { id: "JYFL10", name: "社会建设" },
        { id: "JYFL08", name: "其他" },
      ],
      jointlyId: "", //联名id
      undertakeStatus: false, //显示反馈的依据，获取子流程状态
      delayVisible: false,
      hbDataSource: [], //会办数据
      minorLastTime: "",
      majorLastTime: "",
      onlyMajorLastTime:"",
      onlyMinorLastTime:"",
      fileTextList_7: [],
      fileTextList_8: [],
      fileUnRegisterList: [],
      delayForm: { delayDay: "", comment: "" },
      defaultUnit: [],
      disableSelectedRowKeys: [],
      flowId: "",
      flowName: "",
      defaultUnitHB: [],
      disableSelectedRowKeysHB: [],
      // 申请调整
      changeIfMainTitle: "修改承办类别",
      changeIfMainForm: {},
      // 联名按钮loading
      lmLoading: false,
      // 交办按钮loading
      jbLoading: false,
      // 校核按钮loading
      jhLoading: false,
      // 审核按钮loading
      shLoading: false,
      // 答复按钮loading
      sfLoading: false,
      // 经办人orgCode
      jbrTableOrgCode: "",
      zwCKyj: false, //转为参考意见的转圈


      disableSelectedRowKeylist: [], //禁止添加的用户（已添加）
      isshowLian: false,
      // 附件上传
      action: process.env.VUE_APP_BASE_API + "/api/v1/bulletin/upload",
      fileData: {
        relId: "",
        type: 0,
        encrypted: true,
      },
      visible: false,
      ids: [],
      isBanMeet: false,
      // tab 导航栏的 key 值
      tabIndex: 1,
    };
  },
// 监听record的变化
  watch: {
    record: {
      handler() {
        this.isRendered = false;
        this.$nextTick(() => {
          this.isRendered = true;
        });
      },
      deep: true,
    },
  },
  created() {
    // this.getOpinionOption();
    if (sessionStorage.getItem("USERID")) {
      this.userName = JSON.parse(sessionStorage.getItem("USERID")).username;
      console.log("userName: " + this.userName);
    }
    console.log("userName: " + this.userName);
 // 调用 getDictData 方法获取字典数据
    this.getDictData().then(data => {
      //强复制
      this.detailsForm= JSON.parse(JSON.stringify(data[0]));
      console.log("字典数据加载完成:", this.detailsForm);
    }).catch(error => {
      console.error("获取字典数据失败:", error);
    });
  },
  methods: {
  // 获取字典数据
  getDictData() {
    return getDicts('sys_DF_unit_adjustment').then((response) => {
      this.detailsForm = response.data.data;
      return this.detailsForm;
    });
  },
    // 清除
    handleRemove(file, fileList) {
      this.fujian.forEach((item, index) => {
        if (item.path == file.url || item.id == file.id) {
          this.fujian.splice(index, 1);
        }
      });
      this.fileUrl.forEach((item, index) => {
        if (item.url == file.url) {
          this.fileUrl.splice(index, 1);
        }
      });
      if (this.fujian.length == 0) {
        this.noticeForm.fileUrl = null;
      }
      this.$baseMessage(`删除成功`, "success");
    },
    // 上传
    uploadChange({ file, fileList }) {
      this.fileUrl = fileList;
      this.$set(this.noticeForm, "fileUrl", URL.createObjectURL(file));
      // 上传
      var wj = new FormData();
      wj.append("file", file);
      instance_2({
        method: "post",
        url: "/bulletin/upload",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        data: wj,
      })
        .then((res) => {
          var regId = res.data.data[0].newName.split(".")[0];
          this.fujian.push({
            newName: res.data.data[0].newName,
            fileName: res.data.data[0].originName,
            id: regId,
            path: res.data.data[0].path,
            type: 2,
          });
          this.fileUrl = [];
          this.fujian.forEach((item) => {
            this.fileUrl.push({
              id: item.id,
              name: item.fileName,
              path: item.path,
              url: item.path,
              uid: "-1",
            });
          });

        })
        .catch((error) => { });
    },
    // 上传大小限制
    beforeAvatarUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      // var reg = /^.+(\/pdf)$/;
      var reg = /(pdf|ofd)$/i;
      const isPDF = reg.test(file.type);
      if (!isPDF) {
        this.$message.error("上传的文件格式只能是PDF和OFD");
      }
      if (isLt20M && isPDF) return false;
    },

    // 计算竖线的高度
    getLineHeight(index) {
      const stepContentHeight = this.$refs[`step-content-${index}`]?.offsetHeight || 0;
      console.log(stepContentHeight)
      return stepContentHeight > 0 ? stepContentHeight + 30 : 90; // 默认最小长度
    },
    // 获取多选的意见类型
    getOpinionOption() {
      findOpinionOption().then((res) => {
        if (res.data.code == "200") {
          let aa = res.data.data.split('"');
          for (let i = 0; i < aa.length; i++) {
            if (i % 2 != 0) {
              this.OpinionOptionList.push(aa[i]);
            }
          }
        }
      });
    },
    // 点击行
    clickRowData(record) {
      console.log("是否点击----");
      console.log(record,"内容为-------------")
      if (record.replyComment) {
        this.replyComment = record.replyComment;
        this.iShow = true;
      }
    },
    // 校核弹窗 关闭
    cancelReturn() {
      this.returnValue = false;
      this.resourceForm.comment = "";
    },
    // 正式提交
    formallySubmit() {
      this.$router.push({
        path: "/meSuggestv/addSuggestv",
        query: {
          showUser: false,
          proposalId: this.proposalId,
        },
      });
    },
    // 取消调整
    cancelUnitModal() {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "如果关闭当前页面，将会丢失已编辑的承办单位，确认要关闭?",
        onOk: () => {
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 关闭窗口
    cancelModal() {
      this.delayVisible = false;
      this.delayForm = { delayDay: "", comment: "" };
      this.delayFileTextList = [];
    },

    // 下载建议纸
    downMotion() {
      let downFile = (res) => {
        let a = window.document.createElement("a");
        let resData = URL.createObjectURL(res.data);
        a.href = resData;
        let proposalNum = this.record.proposalNum
          ? this.record.proposalNum
          : "";
        let fileName = proposalNum + this.record.proposalTitle + "建议纸.docx";
        a.download = fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(resData);
      };
      downloadProposal({ id: this.proposalId, type: 10 }).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        let errmsg = "";
        if (res.headers.errmsg) {
          errmsg = this.$str2utf8(res.headers.errmsg);
          return this.$message.error(errmsg);
        } else {
          downFile(res);
        }
      });
    },

    //重新上传正文附件 关闭
    closeFileModal() {
      this.fileVisible = false;
      this.fileTextListReset = [];
      this.delayFileTextList = [];
      this.resourceForm = {
        meetName: "",
        meet: "",
        host: "",
        hostName: "",
        adjust: "",
        adjustName: "",
        isPublic: "",
        dffs: [],
        jbrName: "",
        jbrDept: "",
        jbrJob: "",
        jbrPhone: "",
        jbrOph: "",
      };
      this.fileTextList = [];
      this.fileTextList_7 = [];
      this.fileTextList_8 = [];
      this.fileUnRegisterList = [];
    },
    //重新上传正文附件 保存
    resetSubmit() {
      let form = {};
      form.proposalId = this.proposalId;
      form.type = "9";
      form.encrypted = false;
      form.file = this.resourceForm.file;
      againUploadTextFile(form).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.$message.success("操作完成");
          this.closeFileModal();
        } else {
          this.$message.error(res.data.data);
        }
      });
    },
    //重新上传正文附件 删除
    handleTextRemoveReset() {
      this.resourceForm.file = "";
      this.fileTextListReset = [];
      this.$baseMessage(`删除成功`, "success");
    },

    //重新上传正文附件 上传前
    beforeUploadReset(file) {
      console.log(file, "file");
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /^.+(docx|doc)$/;
      const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      if (!isDoc) {
        this.$message.error("上传的文件格式只能是doc或docx");
      }
      if (isLt20M && isDoc) {
        this.isFileReset = true;
        return false;
      } else {
        this.isFileReset = false;
        return true;
      }
    },
    //重新上传正文附件 上传操作
    uploadChangeReset(val, typeNum) {
      if (this.isFileReset) {
        // let formData = new FormData();
        // formData.append("file", val.file);
        // formData.append("type", typeNum);
        this.resourceForm.file = "";
        this.resourceForm.file = val.file;
        this.fileTextListReset = val.fileList;
      }
    },
    // 重新上传正文附件
    resetFile() {
      // 打开上传文件窗口
      this.fileVisible = true;
    },

    // 下载
    downFileDataDf(itemdf, type) {
      if (this.mediaType === "其他建议") {
        this.$message.error("抱歉，你没有该权限！");
      } else {
        let form = {};
        let attName = "";
        let attSuffix = "";
        if (type === 6) {
          form.relId = itemdf.undertakeId || itemdf.attId; //_______attId
          form.type = type; //应该是6待测试
          attName = itemdf.attName;
          attSuffix = itemdf.attSuffix;
        } else {
          itemdf.fujianList.forEach((item) => {
            if (item.attType === type) {
              form.relId = item.relId;
              form.type = item.attType;
              attName = item.attName;
              attSuffix = item.attSuffix;
            }
          });
        }
        console.log("🤗🤗🤗, form =>", form);
        downFile(form).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `${attName}.${attSuffix}`;

          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          window.URL.revokeObjectURL(res);
        });
      }
    },
    //下载不予立案附件
    downFileDataUnder(step, type) {
      if (this.mediaType === "其他建议") {
        this.$message.error("抱歉，你没有该权限！");
      } else {
        let form = {};
        let attName = "";
        let attSuffix = "";
        step.fujianList.forEach((item) => {
            if (item.attType === type) {
              form.relId = item.relId;
              form.type = item.attType;
              attName = item.attName;
              attSuffix = item.attSuffix;
            }
          });
        console.log("🤗🤗🤗, form =>", form);
        downFile(form).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `${attName}.${attSuffix}`;

          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          window.URL.revokeObjectURL(res);
        });
      }
    },
    //答复函下载
    downFileDataDfh(record) {
      console.log("🤗🤗🤗2458-------------- record =>", record)
      let form = { relIdList: [] };
      let name = "";
      form.relId = record.oaId;
      form.type = 40;
      if (record.fujianList.length > 1) {
        name = "附件" + ".zip"
      } else {
        name = record.fujianList[0].attName + "." + record.fujianList[0].attSuffix;
      }
      console.log("name--------------", name)
      console.log("🤗🤗🤗, form =>", form);
      downDfhFile(form).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = name;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 下载附件
    downFileData(type, relId) {
      let form = { relIdList: [] };
      if (type == 2 || type == 32) {
        form.relId = relId;
      } else {
        form.relId = this.proposalId;
      }
      let name = "";
      if (type == "1") {
        name = "附件.zip";
      }
      if (type == "2") {
        name = "答复附件.pdf";
      }
      if (type == "9") {
        name = "正文附件.docx";
      }
      if (type == "32") {
        name = "示例文件.pdf"
      }
      form.type = type;
      console.log("🤗🤗🤗, form =>", form);
      downFile(form).then((res) => {
        console.log(Object.getOwnPropertyNames(res.headers));
        console.log(
          "🤗🤗🤗, res =>",
          res,
          "headers",
          res.headers,
          res.headers?.["content-disposition"]
        );
        // let filename = res.headers?.['content-disposition']?.split(';')[1].split("filename=")[1];
        // if (type == "1" && filename) {
        //   let hz = filename?.substr(filename.lastIndexOf('.') + 1);
        //   name = "附件." + hz;
        // }
        if (type == "1") {
          let fjlis = this.record.attachmentList.filter((i) => i.attType == 1);
          console.log(fjlis, "fjlis");
          if (fjlis.length == 1) {
            name = "附件." + fjlis[0].attSuffix;
          }
        }

        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `${this.record.proposalTitle}${name}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 查看正文
    lookWord() {
      console.log("🤗🤗🤗, mediaType =>", this.mediaType);
      if (this.mediaType === "其他建议") {
      } else {
        seeText(this.proposalId).then((res) => {
          if (res.data.code == 200 && res.data.data) {
            let url = this.GLOBAL.basePath_3 + "/" + res.data.data.url;
            window.open(url);
          } else {
            this.$message.error("请求出错!");
          }
        });
      }
    },


    // 上传之前
    beforeUpload(file, fileList) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10M!");
      }
      // var reg = /^.+(docx|doc)$/;
      // const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      // if (!isDoc) {
      //   this.$message.error("上传的文件格式只能是doc或docx");
      //   return;
      // }
      return false;
    },

    // 上传操作
    uploadChange(val, type) {
      // 1、议案附件2、回复的附件3、代表大会议附件
      // 4、常委会议附件5、市政府(两院)宙议附件6
      // 承单位退回附件7、代表无需沟通证据8、座
      // 谈会证据9、建议正文10、建议纸
      //不予立案附件  50
      if (val.file.status == "removed") return;
      let formData = new FormData();
      formData.append("file", val.file);
      formData.append("type", type);
      fileUpload(formData).then((res) => {
        if (type == "2") {
          // 附件
          this.resourceForm.fuJianIds = [];
          this.resourceForm.fuJianIds.push(res.data.data[0].attId);
          this.fileTextList = val.fileList;
        } else if (type == "7") {
          this.resourceForm.forumFile = [];
          this.resourceForm.forumFile.push(res.data.data[0].attId);
          this.fileTextList_7 = val.fileList;
        } else if (type == "8") {
          this.resourceForm.communFile = [];
          this.resourceForm.communFile.push(res.data.data[0].attId);
          this.fileTextList_8 = val.fileList;
        }else if (type == "50") {
          this.resourceForm.fuJianIds = [];
          this.resourceForm.fuJianIds.push(res.data.data[0].attId);
          this.fileUnRegisterList = val.fileList;
        }
      });
    },
    // 删除提出代表
    delJointlyList() {
      if (this.deputationSelectedRows.length == 0) {
        return this.$message.error("请选择数据！");
      }
      // 使用 this.$confirm 弹出确认对话框
      this.$confirm({
        title: '确认删除',
        content: '您确定要删除选中的数据吗？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          let jointlyIdsList = [];
          var allifpublic = false; // 是否为领衔代表
          this.deputationSelectedRows.map((item) => {
            if (item.ifpublic != "1") {
              jointlyIdsList.push({ jointlyIds: item.jointlyId });
            } else {
              allifpublic = true;
            }
          });

          delJointly(jointlyIdsList).then((res) => {
            if (res.data.code == 200) {
              if (allifpublic) {
                this.$message.success("领衔代表不能被删除");
              } else {
                this.$message.success(res.data.message);
              }

              this.deputationSelectedRows = [];
              this.getJointlyListData();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info('已取消删除');
        }
      });
    },

    // 打开提出代表进行新增/答复选择经办人
    showUserTable(type) {
      this.perType = type;
      // this.disableSelectedRowKeylist = this.deputationDataSource.map((i) => i.userId);
      this.$refs.commonUserTable.jointTableVisible = true;
    },


    // tab点击
    changeTab(key) {
      console.log("tab点击", key);
      this.tabIndex = key;
    },
    // 获取反馈答复列表、
    feedbackReply() {
      getUndertakeDfAndFeedbackList(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitDataSource = [];
          this.majorLastTime = "";
          this.minorLastTime = "";
          this.majorUndertakeDfList = [];
          this.minorUndertakeDfList = [];

          if (res.data.data != undefined && res.data.data.length > 0) {
            res.data.data.forEach((item) => {
              this.unitDataSource.push(item);
              // 主办单位
              if (item.ifmain == 1) {
                // 答复反馈tab 显示答复方式
                if (item.dfList) {
                  this.record.dffs = item.dfList[0].dffs;
                }
                this.majorLastTime = item.handleLastTime;
                this.majorUndertakeDfList.push(item);
              } else {
                if (
                  this.minorLastTime == "" ||
                  (this.minorLastTime != "" &&
                    moment(this.minorLastTime).valueOf() <
                    moment(item.handleLastTime).valueOf())
                ) {
                  this.minorLastTime = item.handleLastTime;
                }
                this.minorUndertakeDfList.push(item);
              }
            });
          }
        } else {
        }
        this.$forceUpdate();
      });
    },

    // 获取办理跟踪列表
    getLocalHistoryData() {
      getLocalHistory(this.proposalId).then((res) => {
        // console.log("获取办理跟踪列表🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.processDataSource = res.data.data;
          console.log(this.processDataSource)
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 获取流程节点数据
    getFlowNodeData(){
      if(this.record.status==41 || this.record.status==45){
        getAllFlowNodeOther(this.proposalId).then((res) => {
          if (res.data.code == 200) {
            this.flowNodeDataSource = res.data.data;
            console.log("this.record.status---3488----", this.record.status);
            const index = this.flowNodeDataSource.findIndex(item => item.suggStatus === this.record.status)
            console.log(index);

            if(this.record.status==25 || this.record.status==35){
              this.current = 4;
            } else {
              this.current = index;
            }
          }
        });
      }else {
        getAllFlowNode(this.proposalId).then((res) => {
          if (res.data.code == 200) {
            this.flowNodeDataSource = res.data.data;
            console.log("this.record.status---3488----", this.record.status);
            const index = this.flowNodeDataSource.findIndex(item => item.suggStatus === this.record.status)
            console.log(index);
            console.log(this.flowNodeDataSource[index],"---------index----");

            //领导审定卡环节
            // if(this.flowNodeDataSource[index] && this.flowNodeDataSource[index].showDec ==='领导审定'){
            //   this.current = 6;
            // }else
            if(this.record.status==42 || this.record.status==43){
              console.log("进来了吗");
              this.current = 5;
            }else
            if (this.record.status == 25 || this.record.status==35) {
              let node = this.flowNodeDataSource.find(item => item.showDec === '复审')
              console.log(node,"---------node----");
              this.current = node.sort;
            } else {
              this.current = index;
            }
            console.log(this.flowNodeDataSource)
          } else {
            this.$message.error(res.data.message);
          }
        });
      }
    },

    // 获取答复函数据
    getDfhDataList() {
      getDfhData(this.proposalId).then((res) => {
        // console.log("获取办理跟踪列表🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.dfhDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 获取提出代表
    getJointlyListData() {
      getJointlyList(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.deputationDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    //查看公开意见
    handleComment(type){
      console.log("status--------------5576-----",this.status);
      this.resourceForm.proposalIds = this.proposalId;
      let dataForm = {
        type:type,
        proposalId:this.resourceForm.proposalIds,
      };
      findCommentByType(dataForm).then((res) => {
        if (res.data.code == 200) {
          console.log("data.data.status--------",res.data.data.status)
          console.log("type--------",type)
          if(res.data.data.status ==='30' && type==='xlw') {
            this.resourceForm.publicComment =res.data.data.replyComment;
            this.resourceForm.xlwIfPublice =res.data.data.xlwPublic;
            this.resourceForm.historyId = res.data.data.historyId;
            this.dialogVisiblePublicCommentFs = true;
          }else{
            this.publicComment =res.data.data.replyComment;
            this.resourceForm.xlwIfPublice =res.data.data.xlwPublic;
            this.resourceForm.historyId = res.data.data.historyId;
            this.dialogVisiblePublicComment = true;
          }
          this.shLoading = false;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },



    // 关闭窗口
    close() {
      this.isBanMeet = false
      this.userName = this.record.headPer
      console.log(this.record.undertakeStatus)
      console.log(this.record.headPer)
      this.$emit("parentFetchData");
      this.visible = false;
      this.showOperate = true;
      // for (const key in this.$data) {
      //   if (!Array.isArray(this.$data[key])) {
      //     this[key] = this.$options.data.call(this)[key];
      //   }
      // }
      this.resourceForm.host = [];
      this.resourceForm.meet = [];
      this.resourceForm.mainHandleLastTimeStr = "";
      (this.resourceForm.minorHandleLastTimeStr = ""),
        (this.resourceForm = {
          meetName: "",
          meet: "",
          text: [],
          host: "",
          hostName: "",
          adjust: "",
          adjustName: "",
          isPublic: "",
          dffs: [],
          xlwCheck: "1",
          subLeaderCheck:"1",
          leaderCheck:"1",
          xlwIfPublice:"1",
          yjsIfPublice:"1",
          jbrName: "",
          jbrDept: "",
          jbrJob: "",
          jbrPhone: "",
          jbrOph: "",
          dflb: null,
          orgCode: "",
          mainHandleLastTimeStr: "",
          minorHandleLastTimeStr: "",
          finalAssignTimeStr:"",
          contentType: "",
        });
      this.$refs.adjustListEdit.modalVisible = false;
      this.$refs.evaluationModal.modalVisible = false;
      this.fileTextList_8 = [];
      this.fileTextList = [];
      this.fileTextList_7 = [];
      this.minorLastTime = "";
    },


    showData() {
      console.log("🤗🤗🤗, 下拉内容", this.resourceForm.remarkText);
      console.log("🤗🤗🤗,  下拉值 =>", this.resourceForm.remark);

      this.$forceUpdate()
    },

    //选择是否公开
    changePublic(e) {
      this.$forceUpdate();
    },
    changeZwSug(e) {
      this.$forceUpdate();
    },
    changeDbSug(e) {
      this.$forceUpdate();
    },
    xlwCheck(e) {
      this.$forceUpdate();
    },
    subLeaderCheck(e) {
      this.$forceUpdate();
    },
    leaderCheck(e) {
      this.$forceUpdate();
    },

    // 获取信息
    async showDesc() {
      // console.log("🤗🤗🤗, this.record.status =>", this.record.status);
      console.log("🤗🤗🤗syp,  this.resourceForm =>", this.resourceForm);
      this.listLoading = true;

      let userDataBool = await this.getOneUser();
      if (userDataBool) {
        let res = await proposalQzById({ proposalId: this.proposalId });
        if (res.data.code == 200) {
          this.record = res.data.data;
          // 回显是否公开
          this.resourceForm.ifPublice = this.record.ifPublice;
          if (this.resourceForm.ifPublice == "0") {
            this.isPublicDisable = true;
          }
          this.resourceForm.contentType = this.record.proposalContentType;
          // 答复时获取经办信息 userData 请求数据较慢拿不到
          if (
            this.userData.authorities[0].authority == "YAJY_XLW" ||
            (this.record.undertakeStatus == 2 &&
              this.userData.authorities[0].authority == "YAJY_CBDW") ||
            (this.scenesStatus == "答复修改" &&
              this.userData.authorities[0].authority == "YAJY_CBDW")
          ) {
            let form = {};
            form.proposalId = this.proposalId;
            if (this.scenesStatus == "答复修改") {
              form.isUpdate = "Y";
            }
            this.resourceForm.meetorgCode = null;
            this.resourceForm.hostorgCode = null;
            this.resourceForm.host = [];
            this.resourceForm.meet = [];
            this.disableSelectedRowKeys = [];
            this.defaultUnit = [];
            getUndertakeDfInfo(form).then((res) => {
              if (res.data.code == 200) {
                if (res.data.data.undertakeDf) {
                  this.resourceForm.dfId = res.data.data.undertakeDf.dfId;
                }
                //初始化经办人数据
                this.resourceForm.orgCode = res.data.data.undertake.orgCode;
                this.resourceForm.jbrName = res.data.data.undertake.jbrName;
                this.resourceForm.jbrDept = res.data.data.undertake.jbrDept;
                this.resourceForm.jbrJob = res.data.data.undertake.jbrJob;
                this.resourceForm.jbrPhone = res.data.data.undertake.jbrPhone;
                this.resourceForm.jbrOph = res.data.data.undertake.jbrOph;

                if (
                  res.data.data.undertakeDf != undefined &&
                  res.data.data.undertakeDf != null
                ) {
                  if (
                    res.data.data.undertakeDf.dflb != undefined ||
                    res.data.data.undertakeDf.dflb != null
                  ) {
                    this.resourceForm.dflb = res.data.data.undertakeDf.dflb;
                  }
                  if (
                    res.data.data.undertakeDf.dffs &&
                    res.data.data.undertakeDf.dffs.indexOf(",") >= 0
                  ) {
                    this.resourceForm.dffs =
                      res.data.data.undertakeDf.dffs.split(",");
                  }
                  if (
                    res.data.data.undertakeDf.dffs &&
                    res.data.data.undertakeDf.dffs.indexOf(",") < 0
                  ) {
                    this.resourceForm.dffs = res.data.data.undertakeDf.dffs;
                  }
                  this.resourceForm.isPublic =
                    res.data.data.undertakeDf.isPublic;
                  this.resourceForm.orgEvaluation =
                    res.data.data.undertakeDf.orgEvaluation;
                  this.resourceForm.remark = res.data.data.undertakeDf.remark;
                  this.resourceForm.remarkText = res.data.data.undertakeDf.remarkText;
                  this.$forceUpdate();
                }

                //政务协同
                if (
                  res.data.data.undertake.ifmain == 1 &&
                  res.data.data.xtpfList.length > 0
                ) {
                  this.hbDataSource = res.data.data.xtpfList;
                }

                //附件
                if (
                  res.data.data.fujianList != undefined &&
                  res.data.data.fujianList != null &&
                  res.data.data.fujianList.length > 0
                ) {
                  res.data.data.fujianList.forEach((item) => {
                    if (item.attType == 2) {
                      // 附件
                      this.resourceForm.fuJianIds = [];
                      this.resourceForm.fuJianIds.push(item.attId);
                      let file = {
                        uid: item.attId,
                        name: item.attName + "." + item.attSuffix,
                        status: "done",
                        url: "",
                      };
                      this.fileTextList = [];
                      this.fileTextList.push(file);
                    } else if (item.attType == 7) {
                      this.resourceForm.forumFile = [];
                      this.resourceForm.forumFile.push(item.attId);
                      let file = {
                        uid: item.attId,
                        name: item.attName + "." + item.attSuffix,
                        status: "done",
                        url: "",
                      };
                      this.fileTextList_7 = [];
                      this.fileTextList_7.push(file);
                    } else if (item.attType == 8) {
                      this.resourceForm.communFile = [];
                      this.resourceForm.communFile.push(item.attId);
                      let file = {
                        uid: item.attId,
                        name: item.attName + "." + item.attSuffix,
                        status: "done",
                        url: "",
                      };
                      this.fileTextList_8 = [];
                      this.fileTextList_8.push(file);
                    }
                  });
                }
                this.$forceUpdate();
              }
            });
          }
          //交办时获取已保存的承办单位数据
          if (this.record.status == 40) {
            this.listLoading = true;
            getProposalUnder(this.proposalId).then((res) => {
              console.log("获取存在的承办单位数据, res =>", res);
              if (res.data.code == 200) {
                let hostNames = [];
                let meetNames = [];
                let hostData = [];
                let meetData = [];
                let hostorgCode = [];
                let meetorgCode = [];
                let minorHandleLastTimeStr = "";
                let mainHandleLastTimeStr = "";
                this.resourceForm.hostName = "";
                this.resourceForm.meetName = "";
                this.resourceForm.host = "";
                this.resourceForm.meet = "";
                this.resourceForm.hostorgCode = [];
                this.resourceForm.meetorgCode = [];
                console.log("🤗🤗🤗, 我清空了吗 =>", this.resourceForm);
                if (res.data.data.length !== 0) {
                  res.data.data.forEach((item) => {
                    if (item.ifmain == 1) {
                      hostNames.push(item.orgName);
                      hostData.push({
                        orgCode: item.orgCode,
                        orgName: item.orgName,
                      });
                      hostorgCode.push(item.orgCode);
                      mainHandleLastTimeStr = item.handleLastTime
                        ? item.handleLastTime.slice(0, 10)
                        : "";
                    }
                    if (item.ifmain == 0) {
                      meetNames.push(item.orgName);
                      meetData.push({
                        orgCode: item.orgCode,
                        orgName: item.orgName,
                      });
                      meetorgCode.push(item.orgCode);
                      minorHandleLastTimeStr = item.handleLastTime
                        ? item.handleLastTime.slice(0, 10)
                        : "";
                    }
                  });
                  this.resourceForm["hostName"] = hostNames.join(",");
                  this.resourceForm["meetName"] = meetNames.join(",");
                  this.resourceForm.hostorgCode = hostorgCode;
                  this.resourceForm.meetorgCode = meetorgCode;
                  this.resourceForm.host = hostData;
                  this.resourceForm.meet = meetData;
                  this.resourceForm.mainHandleLastTimeStr = "";
                  this.resourceForm.mainHandleLastTimeStr =
                    mainHandleLastTimeStr;
                  this.resourceForm.minorHandleLastTimeStr = "";
                  this.resourceForm.minorHandleLastTimeStr =
                    minorHandleLastTimeStr;
                  this.$forceUpdate();
                  console.log(this.resourceForm, "this.resourceForm");
                }
                setTimeout(() => {
                  this.listLoading = false;
                }, 50);
              } else {
                this.$message.error(res.data.message);
              }
            });
          }
          //获取答复、反馈数据
          this.feedbackReply();

          //获取代表数据
          this.getJointlyListData();

          //获取流程跟踪数据
          this.getLocalHistoryData();
          //获取流程跟踪数据
          this.getDfhDataList();
          //获取流程节点数据
          this.getFlowNodeData();

          this.listLoading = false;
        } else {
          this.listLoading = false;

          this.$message.error(res.data.message);
        }
      }
      // 获取子流程状态值
      // this.getUndertakeStatusData();
    },
    // 获取登录人数据
    async getOneUser() {
      const res = await getUserData_yajy();
      if (res.data.code == 200) {
        // 获取登录人的权限
        this.userData = res.data.data;
        return true;
      } else {
        this.$message.error(res.data.message);
        return false;
      }
    },

    viewFkEvaluation(data) {
      if (data) {
        this.fkContent = data.gdjy;
        if (data.evaluationValue == 1) {
          this.fkEvaluation = "不满意";
        }
        if (data.evaluationValue == 2) {
          this.fkEvaluation = "基本满意";
        }
        if (data.evaluationValue == 3) {
          this.fkEvaluation = "满意";
        }
      }
      if (this.mediaType === '其他建议') {
        this.$message.error("抱歉，你没有该权限！");
      } else {
        this.fkVisible = true;
      }
    },
    // 关闭反馈窗口
    closeFkVisible() {
      this.fkContent = "";
      this.fkEvaluation = "";
      this.fkVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
/* 设置div的高度和边框 */

/* 设置div为弹性容器，水平和垂直方向都居中对齐内容   */
.tip {
  display: flex;
  justify-content: center;
  align-items: center;
}


.ant-form-item {
  display: flex;
  align-items: center;
}

/* 步骤条样式 */
.step-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 20px;
}

.step {
  display: flex;
  position: relative;
  width: 100%;
}

.step-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #ddd;
  /* 默认灰色 */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
  font-size: 16px;
  font-weight: bold;
  border: 2px solid #ddd;
  z-index: 2;
}

.step-active {
  background-color: #bd3124;
  /* 激活状态 */
  color: white;
  border-color: #bd3124;
}

.step-completed {
  background-color: #bd3124;
  /* 完成状态 */
  color: white;
  border-color: #bd3124;
}

.step-content {
  flex-grow: 1;
  width: 100%;
}

.step-reply-type {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  background-color: #f0f0f0;
}

.step-reply-time {
  margin-bottom: 10px;
}

.step-info {
  /*background-color: #f0f0f0;*/
  width: 95%;
  padding: 8px 8px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
}

.info-item {
  margin: 0 10px;
}

.info-item:not(:first-child):before {
  content: "|";
  margin-right: 20px;
  color: #d3d3d3;
}

.comment-text {
  display: inline-block;   /* 保证文本在一行内 */
  max-width: 94.5%;         /* 防止文本过长，设置最大宽度 */
  white-space: pre-wrap;     /* 换行 */
  overflow: hidden;        /* 隐藏超出区域的内容 */
  text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
  vertical-align: top;     /* 如果有其他元素，可以对齐 */
  width: 100%;             /* 确保文本区域占满容器宽度 */
  line-height: 1.5;
  height: 4.5em;
}
.text-container{
  width: 450px;
  height: 60px;
}

.step-line {
  width: 2px;
  background-color: #d52838;
  /* 竖线颜色 */
  position: absolute;
  top: 20px;
  left: 15px;
  transform: translateX(-50%);
  z-index: 1;
}

.form-item {
  display: flex;
  align-items: flex-start;  /* 保证标签和输入框左上角对齐 */
  margin-bottom: 16px;
}
.tip-x {
  /*font-weight: bold; !* 提示文字加粗 *!*/
  margin-bottom: 4px; /* 提示文字底部间距 */
  display: inline; /* 确保提示文字和标题文字在同一行 */
}
/*.title-container {*/
/*  display: flex;*/
/*  justify-content: space-between; !* 使“处理建议”靠左，tipMessage靠右 *!*/
/*  align-items: center; !* 垂直居中对齐 *!*/
/*  width: 100%;*/
/*}*/

.title-text {
  margin-right: 60px; /* 设置“处理建议”与提示文字之间的间距 */
}

.title-text {
  font-size: 20px;
}

.subtitle-text {
  font-size: 16px;
  display: block;
  margin-top: 5px;
}

.custom-steps {
  width: 90%;
  //::v-deep .ant-steps-item-icon {
  //   width: 22px;
  //   height: 22px;
  //   line-height: 22px;
  //   font-size: 12px;
  //   margin-right: 6px;
  //   }
   ::v-deep .ant-steps-item-title {
     font-size: 12px;
     //line-height: 22px;
     padding-right: 0px;
     }
   //::v-deep .ant-steps-item-tail {
   //  top: 11px;
   //  padding: 0 6px;
   //  }
   ::v-deep .ant-steps-item {
     padding-left: 0px;
     }
   ::v-deep .ant-steps-item-content {
     margin-top: 0;
     }
   ::v-deep .ant-steps {
     line-height: normal;
     }
}
.red-line {
  position: relative;
  padding-left: 15px; /* 根据需要调整 */
}

::v-deep .hbdwDfInfo {
  .ant-descriptions-view{
    .ant-descriptions-row{
      .ant-descriptions-item-label{
        width: 183px;
      }
    }
  }
}

::v-deep .hbdwDfInfo {
  .ant-descriptions-view{
    .ant-descriptions-row{
      .ant-descriptions-item-content{
        width: 305px;
      }
    }
  }
}

::v-deep .ant-descriptions-item-label  {
  width: 150px;
}

::v-deep .ant-descriptions-item-content  {
  width: 250px;
}
.red-line::before {
  content: '';
  position: absolute;
  left: 0;
  top: 31%;
  transform: translateY(-50%);
  width: 4px; /* 竖线的宽度 */
  height: 1em; /* 竖线的高度 */
  background-color: red; /* 竖线的颜色 */
}
.tip-message {
  flex: 1; /* 使tipMessage占据剩余空间并居中 */
  /*text-align: center; !* 居中文本 *!*/
  white-space: normal; /* 允许文本换行 */
  /*word-wrap: break-word; !* 允许长单词或 URL 地址换行到下一行 *!*/
}
</style>


