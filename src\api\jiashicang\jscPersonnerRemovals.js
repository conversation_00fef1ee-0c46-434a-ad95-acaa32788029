import request from '@/utils/requestTemp'
import { instance_1 } from "@/api/axiosRq";


// 保存人事任免信息
export function savejscPersonnerRemovals(data) {
  return request({
    url: '/jscPersonnerRemovals/savePersonnel',
    method: 'post',
    data
  })
}

// 分页查询
export function getList(params,data) {
  return request({
    url: '/jscPersonnerRemovals/getList',
    method: 'post',
    params:params,
    data:data
  })
}

//详情
export function getDetail(data) {
  return request({
    url: '/jscPersonnerRemovals/getDetail',
    method: 'post',
    data: data
  })
}

//编辑
export function updatePersonnel(data) {
  return request({
    url: '/jscPersonnerRemovals/updatePersonnel',
    method: 'post',
    data: data
  })
}

//删除
export function deletePersonnel(data) {
  return request({
    url: '/jscPersonnerRemovals/deletePersonnel',
    method: 'post',
    data: data
  })
}

//获取导入模板
export function getImportTemplate() {
  return instance_1({
    url: '/jscPersonnerRemovals/getImportTemplate',
    method: 'get',
    responseType: 'blob'
    })
}

//批量导入人事任免
export function importPersonnel(data) {
  return request({
    url: '/jscPersonnerRemovals/importPersonnel',
    method: 'post',
    data: data,
     headers: {
      "Content-Type": "multipart/form-data",
    },
    
  })
}