<template>
    <div>
        <div class="top">
            <div class="wddb">
                <div style="height: 50px;margin-bottom: 20px;">
                    <div class="wddb1">我的待办</div>
                    <div class="gdd1">更多</div>
                </div>
                <div class="wdbj">
                    <div class="wdbj1">11</div>
                    <div class="wdxx">20</div>
                    <div class="qtdb">12</div>
                    <a-tabs v-model="activeKey" :value="activeKey">
                        <a-tab-pane key="1" tab="我的办件" class="wdbjf">
                            <!-- 选举产生 -->
                            <div class="xjcs">
                                <div class="xjcs1">选举产生</div>
                                <div class="xjcs2">选举产生选举产生选举产生选举产生选举产生选举产生选举产生选举产生</div>
                                <div class="sj1"><span style="float: right;width: 100px;">07/08 15:15</span></div>
                            </div>
                            <div style="margin-top: 45px;">
                                <div class="xjcs1" style="color: #fe8727;">学习培训</div>
                                <div class="xjcs2">学习培训学习培训学习培训学习培训学习培训学习培训学习培训学习培训学习培训学习培训学习培训</div>
                                <div class="sj1"><span style="float: right;width: 100px;">07/08 15:15</span></div>
                            </div>
                            <div style="margin-top: 90px;">
                                <div class="xjcs1" style="color: #26cc75;">联系人民群众</div>
                                <div class="xjcs2">联系人民群众联系人民群众联系人民群众联系人民群众联系人民群众</div>
                                <div class="sj1"><span style="float: right;width: 100px;">07/08 15:15</span></div>
                            </div>
                            <div style="margin-top: 135px;">
                                <div class="xjcs1" style="color: #3296fa;">参加会议</div>
                                <div class="xjcs2" style="color: #999999;">参加会议参加会议参加会议</div>
                                <div class="sj1"><span style="float: right;width: 100px;">07/08 15:15</span></div>
                            </div>
                            <div style="margin-top: 180px;">
                                <div class="xjcs1" style="color: #3296fa;">参加活动</div>
                                <div class="xjcs2" style="color: #999999;">参加活动参加活动</div>
                                <div class="sj1"><span style="float: right;width: 100px;">07/08 15:15</span></div>
                            </div>
                        </a-tab-pane>
                        <a-tab-pane key="2" tab="我的消息">我的消息</a-tab-pane>
                        <a-tab-pane key="3" tab="其他待办">其他待办</a-tab-pane>
                    </a-tabs>
                </div>
            </div>
            <div class="top-right">
                <div style="height: 50px;">
                    <div class="wddb1">基本情况</div>
                </div>
                <div class="xjcs5">
                    <div class="imgs1">
                        <div class="ings"><img src="../../assets/activity.png" alt=""></div>
                        <span>选举产生</span>
                    </div>
                    <div>
                        <div class="ings"><img src="../../assets/activity.png" alt=""></div>
                        <span>基本信息</span>
                    </div>
                    <div class="imgs2">
                        <div class="ings"><img src="../../assets/activity.png" alt=""></div>
                        <span>履职档案</span>
                    </div>
                </div>
                <div style="height: 50px;">
                    <div class="wddb1">专题内容</div>
                    <div class="gdd1">更多</div>
                </div>
                <div>
                    <a-tabs v-model="activeKey" :value="activeKey" :tab-position="mode" :style="{ height: '200px' }">
                        <a-tab-pane key="1" tab="代表活动">
                            <div>
                                <div>
                                    <div class="a1">111111111</div>
                                    <div style="color: #999999;float: right;margin-top: 15px;">2021/09/05</div>
                                </div>
                                <div>
                                    <div class="a2">222222222</div>
                                    <div style="color: #999999;float: right;margin-top: 15px;">2021/09/05</div>
                                </div>
                                <div>
                                    <div class="a3">333333333333333333333333333333333333333333333333333333333333333
                                    </div>
                                    <div style="color: #999999;float: right;margin-top: 15px;">2021/09/05</div>
                                </div>
                            </div>
                        </a-tab-pane>
                        <a-tab-pane key="2" tab="我的建议">其他待办</a-tab-pane>
                        <a-tab-pane key="3" tab="履职数据">其他待办</a-tab-pane>
                    </a-tabs>
                </div>
            </div>
        </div>
        <div class="contson">
            <div>
                <a-tabs v-model="activeKey" :value="activeKey">
                    <a-tab-pane key="1" tab="代表大会期间">
                        <div class="suoyou">
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""><span>参加会议</span></div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""><span>专题调研</span></div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""> <span>视察</span></div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""><span>执法检测</span></div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""><span>应邀参加</span></div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""><span>一府一委两院</span>
                                </div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""><span>活动</span></div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""><span>提出建议</span></div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""> <span>听取工作报告</span>
                                </div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""> <span>联系人大常委会</span>
                                </div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""><span>联系人民群众</span>
                                </div>
                            </div>
                            <div class="suoyouSon">
                                <div class="tupian"><img src="../../assets/activity.png" alt=""><span>专题询问会</span></div>
                            </div>
                        </div>
                    </a-tab-pane>
                    <a-tab-pane key="2" tab="履职活动" force-render>Content of Tab Pane 2</a-tab-pane>
                    <a-tab-pane key="3" tab="闭会期间">Content of Tab Pane 3</a-tab-pane>
                </a-tabs>
            </div>
        </div>
        <div class="fool">
            <div class="fuwuzq">
                <div style="height: 50px;margin-top: 20px;">
                    <div class="wddb1">服务专区</div>
                </div>
                <!-- <div class="imgsss">
                    <!-- <div class="imgsss1"><img src="../../assets/shouye.png" alt=""></div> -->
                    <!-- <div class="shouye"> -->
                        <!-- <div style="width: 350px;"><img src="../../assets/shouye.png" alt=""></div>
                        <div style="width: 350px;"><img src="../../assets/shouye.png" alt=""></div>
                        <div style="width: 350px;"><img src="../../assets/shouye.png" alt=""></div> -->
                    <!-- </div> -->
                </div> -->
            </div>
        </div>  </div>
</template>

<script>
export default {
    data() {
        return {
            activeKey: "1",
            activeKeys: '1',
            activeIndex: '1',
            mode: 'left',
        }
    }
}
</script>

<style lang="scss" scoped>
.wddb {
    float: left;
    width: 50%;
}

.wddb1 {
    float: left;
    // font-size: 20px;
      @include add-size($font_size_16);
    color: black;
    font-weight: 700;
}

.gdd1 {
    float: right;
    color: deepskyblue;
    cursor: pointer;
    line-height: 50px;
}

.wdbjf {
    position: relative;
    margin-left: 30px;
}

.wdbj1 {
    position: absolute;
    left: 35px;
    top: 45px;
    color: red;
    // font-size: 24px;
      @include add-size($font_size_16);
}

.wdxx {
    position: absolute;
    left: 165px;
    top: 45px;
    color: red;
    // font-size: 24px;
      @include add-size($font_size_16);
}

.qtdb {
    position: absolute;
    left: 288px;
    top: 45px;
    color: red;
    // font-size: 24px;
      @include add-size($font_size_16);
}


.xjcs1 {
    float: left;
    width: 15%;
    // font-size: 16px;
      @include add-size($font_size_16);
    color: #db3046;
}

.xjcs2 {
    float: left;
    width: 60%;
    // font-size: 16px;
      @include add-size($font_size_16);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.sj1 {
    float: right;
    width: 20%;
    color: #999999;
    // font-size: 12px;
      @include add-size($font_size_16);
    margin-top: 5px;
}

.top-right {
    float: left;
    margin-left: 30px;
    width: 45%;
}

.xjcs5 {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
}

.ings {
    width: 50px;
    height: 50px;

    img {
        width: 100%;
        height: 100%;
    }
}

.imgs1 {
    margin-left: 150px;
}

.imgs2 {
    margin-right: 150px;
}

.a1 {
    float: left;
    height: 50px;
    line-height: 50px;
    // font-size: 16px;
      @include add-size($font_size_16);
    width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.a2 {
    float: left;
    height: 50px;
    line-height: 50px;
    margin-top: 5px;
    // font-size: 16px;
       @include add-size($font_size_16);
    width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.a3 {
    float: left;
    height: 50px;
    line-height: 50px;
    margin-top: 4px;
    // font-size: 16px;
       @include add-size($font_size_16);
    width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.contson {
    float: left;
    width: 100%;
}

.suoyou {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    width: 100%;
    margin: 0 60px;
}

.suoyouSon {
    width: 15%;
    margin-bottom: 20px;

    .tupian {
        width: 100px;
        text-align: center;
        cursor: pointer;

        img {
            width: 100%;
            height: 100%;
        }
    }

    span {
        // margin-left: 10px;
        cursor: pointer;
    }
}

.fuwuzq {
    width: 80%;
    float: left;
}

.imgsss {
    width: 90%;
    margin-left: 250px;
}

.imgsss1 {
    width: 100%;
    height: 200px;
    margin-bottom: 20px;

    img {
        width: 100%;
        height: 100%;
    }
}

.shouye {
    width: 100%;
    display: flex;
    justify-content: space-between;

    img {
        width: 100%;
    }
}
</style>
