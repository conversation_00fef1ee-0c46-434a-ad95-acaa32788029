import titleBreacrumb from "./titleBreacrumb.vue";
import ZTable from "@/views/common/ZTable";
import StandardTable from "@/components/table/StandardTable.vue";
import globalModal from "@/CommonComponents/globalModal/globalModa";
const commonComponent = {
  install: (Vue) => {
    // 注册并获取组件，然后在 main.js 中引入，并 Vue.use()挂载
    Vue.component("titleBreacrumb", titleBreacrumb);
    Vue.component("ZTable", ZTable);
    Vue.component("StandardTable", StandardTable);
    Vue.component("globalModal", globalModal);
  },
};
export default commonComponent;
