<template>
  <a-modal :title="fileManagementTitle" :visible.sync="fileManagementVisible" width="80%" height="60%" destroyOnClose
    @cancel="closes">
    <div>
      <!-- 资源库 -->
      <a-row>
        <a-col :md="5" style="">
          <div id="title">文件管理</div>
          <!-- 文件树 -->
          <a-tree style="height: 800px; overflow-y: auto" @select="onSelect" @expand="onExpand"
            :replace-fields="replaceFields" :tree-data="treeDatalist" v-model="selectedKeys" ref="FileTree"
            :selectedKeys="selectedKeys" :checkable="false" selectable :expanded-keys="expandedKeys"
            :auto-expand-parent="autoExpandParent">
            <template #title="{
              id: treeKey,
              fileName: fileName,
              folderName: folderName,
              file: Path,
              flag: flag,
              absolutePath: absPath,
              childFolderName: childFolderName,
              relativePath: relativePath,
              parentId: parentId,
            }">
              <a-dropdown :trigger="['contextmenu']">
                <span>
                  <a-icon :type="false ? 'anticon-folder-open' : 'anticon-folder'" />
                  <a-icon :type="false ? 'apartment' : 'file'" />
                  {{ folderName ? folderName : fileName }}
                </span>
                <template #overlay>
                  <a-menu @click="
                    ({ key: menuKey }) =>
                      onContextMenuClick(
                        parentId,
                        treeKey,
                        Path,
                        absPath,
                        fileName,
                        folderName,
                        menuKey,
                        childFolderName,
                        relativePath
                      )
                  ">
                    <!-- <a-menu-item key="1" v-show="folderName != undefined"
                      >添加</a-menu-item
                    >
                    <a-menu-item key="2"  >重命名</a-menu-item>
                    <a-menu-item key="3"  >删除</a-menu-item> -->
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </a-tree>
        </a-col>

        <!-- <a-alert v-show="isUpload"
      message="正在上传文件，请勿退出当前页面" 
    ></a-alert> -->
        <a-col :md="18" style="padding: 10px">
          <a-spin :spinning="isUpload" tip="正在上传文件，请勿退出当前页面">
            <a-row>
              <a-space>
                <!-- 上传 -->
                <a-upload :action="action" :disabled="!SCdisabled" :remove="handleTextRemove"
                  @change="uploadChange($event)" :beforeUpload="beforeUpload" :fileList="fileTextList">
                  <a-button :disabled="!SCdisabled" type="primary">点击上传</a-button>
                </a-upload>
                <a-button type="primary" @click="AddWFile"> 添加文件夹</a-button>
                <a-button @click="Refresh"> 刷新</a-button>
                <a-button @click="isSetUp = true">设置</a-button>
                <span>上传文件不超过1024MB容量文件</span>
                <!-- <a-button>全屏</a-button> -->
              </a-space>
            </a-row>
            <!-- 进度条 -->
            <!-- <a-row>
          <a-col :span="24">
          <a-progress :percent="Progress" v-show="isUpload" size="small" />
          </a-col>
        </a-row> -->
            <a-row>
              <a-row :gutter="[16, 16]">
                <a-col>
                  <!--显示-->
                  <div class="con_box" v-show="!isSetUp">
                    <!-- 图标 -->
                    <div v-show="queryForm.seeType == '1'">
                      <a-row :gutter="[16, 16]" type="flex" justify="left">
                        <a-col :md="6" v-for="col in formList" :key="col.id">
                          <a-dropdown>
                            <a-card hoverable style="text-align: center" @click="(e) => e.preventDefault()"
                              @contextmenu.prevent="rightClick">
                              <a style="display: block; height: 165px; color: black
                               max-height: 225px;min-height: 225px;" @click="nextLowerLevel(col)" target="_blank">
                                <img v-if="col.folderName != null" slot="cover"
                                  :src="require('@/assets/images/indexPage/t3.png')" style="width: 65%;" />
                                <img v-else-if="col.type == 'image'" slot="cover"
                                  :src="GLOBAL.basePath_1 + '/image' + col.relativePath" style="width: 90%" />
                                <video v-else-if="col.type == 'video'" slot="cover"
                                  :src="GLOBAL.basePath_1 + '/image' + col.relativePath" style="width:100%" />
                                <img v-else-if="col.type == 'word'" slot="cover"
                                  :src="require('@/assets/images/indexPage/word.png')" style="width: 70%" />
                                <img v-else-if="col.type == 'xls'" slot="cover" :src="
                                  require('@/assets/images/indexPage/excel.jpeg')
                                " style="width: 70%" />
                                <img v-else-if="col.type == 'pdf'" slot="cover"
                                  :src="require('@/assets/images/indexPage/pdf.png')" style="height: 70%" />
                                <!-- <img v-else 
                              slot="cover"
                              :src="require('@/assets/images/indexPage/t3.png')" 
                              style="height: 90%" /> -->
                                <a v-if="col.folderName" style="
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                display: block;
                                 color: black;
                              " :title="col.folderName" v-html="col.folderName"></a>
                                <a v-else style="
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                display: block;
                                 color: black;
                              " :title="col.fileName" v-html="col.fileName"></a>
                                <p v-show="show.indexOf(2) != -1" style="  overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;"> {{ col.createTime }}</p>
                                <p v-show="show.indexOf(3) != -1" style="  overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;"> {{ col.fileSize }}</p>
                              </a>
                            </a-card>
                            <a-menu slot="overlay" mode="inline" :style="{
                              left: 0.1 * box_Left + 'px',
                              top: 0.1 * box_Top + 'px',
                            }" style="width: 100px; text-align: center">
                              <a-menu-item v-show="col.folderName">
                                <a @click="addFile(col)">添加</a>
                              </a-menu-item>
                              <a-menu-item @click="updateName(col)">重命名</a-menu-item>
                              <a-menu-item @click="handRemoveFile(col)">删除</a-menu-item>
                            </a-menu>
                          </a-dropdown>
                        </a-col>
                      </a-row>
                    </div>
                    <div v-show="queryForm.seeType == '0'">
                      <a-row :gutter="[16, 16]" style="text-algin: center">
                        <a-col>
                          <a-card>
                            <a-row>
                              <a-col :span="8">
                                <p>文件名称</p>
                              </a-col>
                              <a-col :span="8">
                                <p v-show="show.indexOf(2) != -1">
                                  修改日期
                                </p>
                              </a-col>
                              <a-col :span="8">
                                <p v-show="show.indexOf(3) != -1">大小</p>
                              </a-col>
                            </a-row>
                          </a-card>
                        </a-col>
                        <a-col v-for="col in formList" :key="col.id">
                          <a-dropdown>
                            <a-card hoverable style="text-align: left" @click="nextLowerLevel(col)"
                              @contextmenu.prevent="rightClick">
                              <a-row>
                                <a-col :span="8">
                                  {{
                                      col.folderName ? col.folderName : col.fileName
                                  }}
                                </a-col>
                                <a-col :span="8" v-show="show.indexOf(2) != -1">
                                  {{ col.createTime }}</a-col>

                                <a-col :span="8" v-show="show.indexOf(3) != -1">
                                  {{ col.fileSize }}</a-col>
                              </a-row>
                            </a-card>
                            <a-menu slot="overlay" style="width: 100px; text-align: center" :style="{
                              left: box_Left + 'px',
                              top: box_Top + 'px',
                            }">
                              <a-menu-item v-show="col.folderName">
                                <a @click="addFile(col)">添加</a>
                              </a-menu-item>
                              <a-menu-item @click="updateName(col)">重命名</a-menu-item>
                              <a-menu-item @click="handRemoveFile(col)">删除</a-menu-item>
                            </a-menu>
                          </a-dropdown>
                        </a-col>
                      </a-row>
                    </div>
                  </div>

                  <!-- 设置 -->
                  <div class="con_box" v-show="isSetUp">
                    <h2>设置</h2>
                    <a-form ref="queryForm" :model="queryForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                      <a-row>
                        <a-col>
                          <a-form-model-item label="查看">
                            <a-radio-group v-model="queryForm.seeType">
                              <a-radio :value="1"> 缩略图 </a-radio>
                              <a-radio :value="0"> 列表 </a-radio>
                            </a-radio-group>
                          </a-form-model-item>
                        </a-col>
                        <a-col>
                          <a-form-model-item label="显示">
                            <a-checkbox-group v-model="queryForm.displayData">
                              <a-checkbox value="1" name="displayData" disabled>
                                文件名
                              </a-checkbox>
                              <a-checkbox value="2" name="displayData">
                                日期
                              </a-checkbox>
                              <a-checkbox value="3" name="displayData">
                                大小
                              </a-checkbox>
                            </a-checkbox-group>
                          </a-form-model-item>
                        </a-col>
                        <a-col>
                          <a-form-model-item label="排列顺序">
                            <a-radio-group v-model="queryForm.order" @change="dataTree()">
                              <a-radio :value="1"> 按文件名 </a-radio>
                              <a-radio :value="2"> 按日期 </a-radio>
                              <a-radio :value="3"> 按大小 </a-radio>
                              <a-radio :value="4"> 按扩展名 </a-radio>
                            </a-radio-group>
                          </a-form-model-item>
                        </a-col>
                        <a-col>
                          <a-form-model-item :wrapper-col="{ span: 14, offset: 6 }">
                            <a-button @click="close">关闭</a-button>
                          </a-form-model-item>
                        </a-col>
                      </a-row>
                    </a-form>
                  </div>
                </a-col>
              </a-row>
            </a-row>
          </a-spin>
        </a-col>

      </a-row>
      <!-- 新增 -->
      <a-modal :title="fileType" :visible.sync="visible" width="50%" @cancel="close">
        <a-form-model ref="detailsForm" :model="detailsForm" :label-col="{ span: 8 }" :wrapper-col="{ span: 10 }">
          <a-row>
            <a-col>
              <a-form-model-item label="文件名称" prop="dictLabel">
                <a-input v-model="detailsForm.name" autocomplete="off" placeholder="请输入文件名称" />
              </a-form-model-item>
            </a-col>
            <!-- <a-col v-show="fileType == '新建文件夹'" style="padding: 0 40%">
            <a-upload
           
              action=""
              accept=".docx,.doc"
              :remove="handleTextRemove"
              @change="uploadChange($event)"
              :beforeUpload="beforeUpload"
              :fileList="fileTextList"  
            >
              <a-button :disabled="fileTextList.length == 10" type="primary" 
                >点击上传</a-button
              >
            </a-upload>
          </a-col> -->
          </a-row>
        </a-form-model>
        <!-- 定义了插槽 -->
        <slot slot="footer" name="userFooter">
          <a-button type="primary" @click="save">保存</a-button>
          <a-button type="" style="marigin-left: 10px" @click="close">取消</a-button>
        </slot>
      </a-modal>
    </div>
    <slot slot="footer" name="userFooter">
      <a-button type="primary" @click="closes">关闭</a-button>
    </slot>
  </a-modal>
</template>

<script>
import { instance_1 } from "@/api/axiosRq";
import { fileUpload } from "@/api/commonApi/file.js";
import { CodeSandboxCircleFill } from "@ant-design/icons";
export default {
  data() {
    return {
      selectedKeys: [],
      selectedKeyslist: [],
      autoExpandParent: false,
      isUpload: false,
      Progress: 30,
      box_Left: 10,
      box_Top: 10,
      visible: false,
      isSetUp: false,
      show: "123",
      SCdisabled: false,
      // 表单
      queryForm: {
        seeType: 1,
        displayData: ["1", "2", "3"],
        order: 1,
      },
      treeDatalist: [],
      action: process.env.VUE_APP_BASE_API + "/api/v1/resource/uploadFile",
      // 展开
      expandedKeys: [],
      fileTextList: [], //文件正文文件列表
      treeData: [],
      replaceFields: {
        title: "folderName",
        key: "id",
        children: "childFolderName",
      },
      formList: [],
      selectList: null,
      // 重命名
      updateFile: {},
      // 弹窗表单
      detailsForm: {},
      fileType: "新建文件夹",
      fileManagementVisible: false,
      parentData: null,
    };
  },
  props: {
    fileManagementTitle: {
      type: String,
      default: "文件管理",
    },
  },
  created() {
    this.featData();
    this.dataTree();
  },
  methods: {
    rightClick(event) {
      this.box_Left = event.layerX;
      this.box_Top = event.layerY;
    },
    // 添加文件
    AddWFile() {
      if (this.parentData == null) {
        instance_1({
          url: "resource/getPath",
          method: "post",
        }).then((res) => {
          this.flieAddress = res.data.data.path;
          this.updateFile = {
            path: this.flieAddress,
            flag: 0,
            relativePath: '外层',
            parentId: "root",
          };
        });
      } else {

        this.updateFile = {
          path: this.parentData.absolutePath,
          flag: 1,
          relativePath: this.parentData.relativePath,
          parentId: this.parentData.id,
        };
      }
      this.visible = true;
      this.fileType = "新建文件夹";
    },
    // 保存
    save() {
      if (this.detailsForm.name == undefined || this.detailsForm.name == "") {
        this.$message.success("请输入文件名称");
        return;
      }
      if (this.fileType == "文件重命名") {
        this.updateFile.newName = this.detailsForm.name;
        this.updateFile.newPath += this.detailsForm.name + "/";
        // 文件重命名接口
        instance_1({
          url: "resource/fileRename",
          method: "post",
          data: this.updateFile,
        }).then((res) => {
          if (res.data.code == "0000") {
            this.$message.success("修改成功");
            this.Refresh();
          } else if (res.data.code == "1111") {
            this.$message.warning(res.data.msg);
          }
        });
        this.detailsForm = {};
      } else {
        //  this.updateFile.addPath = this.updateFile.path +this.detailsForm.name;
        this.updateFile.folderName = this.detailsForm.name;
        if (this.updateFile.relativePath == "外层") {
          this.updateFile.path = '/' + this.detailsForm.name;
          this.updateFile.relativePath = '/' + this.detailsForm.name;
        } else {
          this.updateFile.relativePath += this.detailsForm.name;
        }

        // 添加接口
        instance_1({
          url: "resource/creatingFolder",
          method: "post",
          data: this.updateFile,
        }).then((res) => {
          if (res.data.code == "0000") {
            this.$message.success("添加成功");
            this.Refresh();
          } else if (res.data.code == "1111") {
            this.$message.warning(res.data.msg);
          }
        });
        this.detailsForm = {};
      }
      this.visible = false;
    },
    // 文件添加
    addFile(rev) {
      //  console.log(rev);
      var relativePath = '';
      if (rev.relativePath != null) {
        relativePath = rev.relativePath
      }
      this.updateFile = {
        relativePath: relativePath,
        path: rev.absolutePath,
        parentId: rev.id,
        flag: 1
      };
      this.visible = true;
      this.fileType = "新建文件夹";
    },
    // 文件重命名
    updateName(rev) {
      let name = rev.folderName ? rev.folderName : rev.fileName;
      this.detailsForm = {
        name: name,
      };
      var newPath = '';
      if (rev.parentId == 'root') {
        newPath = '/';
      } else {
        newPath = this.parentData.relativePath;
        // 
      }
      this.updateFile = {
        // path: rev.relativePath,
        oldName: name,
        id: rev.id,
        oldPath: rev.relativePath,
        newPath
      };
      this.visible = true;
      this.fileType = "文件重命名";
    },
    handRemoveFile(col) {
      var ids = [];
      ids.push(col.id);
      col.childFolderName.forEach((i, n) => {
        ids.push(i.id)
      });
      var name = "";
      if (col.folderName) {
        name = col.folderName;
      } else {
        name = col.fileName;
      }
      this.removeFile(col.id, ids, name);
    },
    // 文件删除
    async removeFile(id, ids, name) {
      await this.$baseConfirm("确认要删除吗?", null, () => {
        // 删除接口
        instance_1({
          url: "resource/delFolder",
          method: "post",
          data: { parentId: id, ids, name },
        }).then((res) => {
          if (res.data.code == "0000") {
            this.$message.success("删除成功");
            this.Refresh();
          }
        });
      });
    },
    // 鼠标右键
    onContextMenuClick(parentId, treeKey, Path, absPath, fileName, folderName, menuKey, childFolderName, relativePath) {
      console.log(treeKey, Path, absPath, fileName, folderName, menuKey);
      // 添加子节点
      relativePath != null ? relativePath = relativePath : relativePath = ''
      if (menuKey == 1) {
        this.updateFile = {
          path: absPath,
          parentId: treeKey,
          flag: 1,
          relativePath: relativePath,
        };
        this.visible = true;
        this.fileType = "新建文件夹";
      } // 重命名
      else if (menuKey == 2) {
        let name = folderName ? folderName : fileName;
        this.detailsForm = {
          name: name,
        };
        var newPath = '';
        if (parentId == 'root') {
          newPath = '/';
        } else {
          // newPath=relativePath.substring(0,relativePath.lastIndexOf('/'));
          // 
          var news = '';
          news = relativePath.slice(0, relativePath.lastIndexOf('/'));
          newPath = news.slice(0, news.lastIndexOf('/')) + '/';
        }
        this.updateFile = {

          oldName: name,
          id: treeKey,
          oldPath: relativePath,
          newPath
        };
        this.visible = true;
        this.fileType = "文件重命名";
      } //删除
      else {
        var ids = [];
        ids.push(treeKey);
        childFolderName.forEach((i, n) => {
          ids.push(i.id);
        })
        let name = folderName ? folderName : fileName;
        this.removeFile(treeKey, ids, name);
        //  // 删除接口
        //   instance_1({
        //   url: "resource/delFolder",
        //   method: "post",
        //   data: {path:Path+"/"+fileName,},
        // }).then((res) => {
        //   if(res.data.code=="0000"){
        //      this.$message.success("删除成功");
        //     this.Refresh();
        //   }
        // });
      }
    },
    //双击
    onSelect(keys, event) {
      if (event.node.dataRef.folderName == null) {
        this.SCdisabled = false;
        this.$emit("confirm", event.node.dataRef);

        this.fileManagementVisible = false;
      } else if (event.node.dataRef.folderName) {
        if (event.selected) {
          this.selectedKeyslist = [event.node.dataRef];
          this.selectedKeys = [event.node.dataRef.id];

          if (event.node.dataRef.childFolderName.length > 0) {
            this.expandedKeys = [...this.getFamilyree(event.node.dataRef.id, this.treeDatalist)]//获取该节点的父节点数据 

          }
          // this.expandedKeys=this.noRepeat(this.expandedKeys,event.node.dataRef.id) //添加到展开数组中  (没关闭其他节点)
        }
        if (this.selectedKeyslist[0].folderName) {
          this.parentData = this.selectedKeyslist[0];
          this.formList = this.selectedKeyslist[0].childFolderName;
          this.arrAddType(this.formList);

          this.selectList = this.selectedKeyslist[0];
          if (this.selectList != null) {
            this.SCdisabled = true;
          } else {
            this.SCdisabled = false;
            this.$emit("confirm", this.selectedKeyslist[0]);
          }
        }

      }
    },
    // 展开方法
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      console.log(this.expandedKeys, "展开的数据--数组")
      this.autoExpandParent = false;
    },
    // 数组添加id 后去重
    noRepeat(arr, id) {
      arr.push(id);
      var newArr = [...new Set(arr)];
      return newArr
    },
    // 找父节点
    getkeyList(id, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.id == id) {
          // 缓存父级节点
          keyList.push(node.parentId);
        }
        if (node.childFolderName) {
          this.getkeyList(id, node.childFolderName, keyList);
        }
      }
      return keyList;
    },
    // 找该节点的所有父节点
    getFamilyree(id, tree) {
      var temp = [];
      var fonFn = function (arr, id) {
        for (var i = 0; i < arr.length; i++) {
          var item = arr[i];
          if (item.id == id) {
            temp.push(item.id);
            fonFn(tree, item.parentId);
            break
          } else {
            if (item.childFolderName) {
              fonFn(item.childFolderName, id)
            }
          }
        }
      }
      fonFn(tree, id);
      return temp
    },
    // 双击
    nextLowerLevel(res) {
      if (res.folderName == null) {
        this.SCdisabled = false;
        this.$emit("confirm", res);

        this.fileManagementVisible = false;
      } else if (res.folderName) {
        // this.formList = res.childFolderName;
        this.parentData = res;
        this.formList = res.childFolderName;
        this.selectedKeyslist = [res];      //激活左边树节点
        this.selectedKeys = [res.id];    //左边树节点id
        if (res.childFolderName.length > 0) {
          this.expandedKeys = [...this.getFamilyree(res.id, this.treeDatalist), res.id]//获取该节点的所有父节点数据
        }

        //  this.expandedKeys=this.noRepeat(this.expandedKeys,res.id)  //添加到展开数组中 (没关闭其他节点)
        console.log(this.selectedKeyslist, 'aaaa', this.selectedKeys)
        this.arrAddType(this.formList);

        this.selectList = res;
        if (this.selectList != null) {
          this.SCdisabled = true;
        } else {
          this.SCdisabled = false;
          this.$emit("confirm", res);

          this.fileManagementVisible = false;
        }
      }
    },
    confirm() {
      this.fileManagementVisible = false;
      // this.$emit("confirm", this.checked);
    },
    //获取左边树
    dataTree() {
      instance_1({
        url: "resource/getFiles",
        method: "get",
        params: { order: this.queryForm.order }
      }).then((res) => {
        if (res.data.code == "0000") {
          this.treeDatalist = res.data.data;



          // // 获取已选的对应的节点数据
          let getNodeData = (idValue, tree) => {
            let Data = "";
            for (let index = 0; index < tree.length; index++) {
              const node = tree[index];

              if (node.id == idValue) {
                Data = node;
              } else {
                if (node.childFolderName) {
                  if (getNodeData(idValue, node.childFolderName)) {
                    Data = getNodeData(idValue, node.childFolderName);
                  }
                }
              }
            }
            return Data;
          };
          if (this.selectedKeys.length > 0) {
            this.selectedKeyslist.push(getNodeData(this.selectedKeys[0], this.treeDatalist));

            console.log(this.selectedKeys, "kkk#", this.selectedKeyslist);
            this.parentData = this.selectedKeyslist[0];
            this.formList = this.selectedKeyslist[0].childFolderName;
            console.log(this.parentData, "kkk#", this.formList);
          } else {
            this.formList = res.data.data;
          }

          this.arrAddType(this.formList);

        }
      });
    },
    handleClick(e, data) {
      // 发起请求去赋值formList[]
    },

    // 上传操作
    uploadChange(val) {
      if (val.file.status == "removed") return;
      let formData = new FormData();
      formData.append("file", val.file);
      var relativePath = "";
      this.selectList.relativePath != null ? relativePath = this.selectList.relativePath : relativePath = ""
      // formData.append("path",relativePath+this.selectList.folderName );
      // formData.append("parentId", this.selectList.id);
      // let data = {
      //   path: this.formList[0].file + "/" + val.file.name,
      //   name: val.file,
      // };
      this.isUpload = true;
      instance_1({
        url: `/resource/uploadFile`,
        header: {
          "Content-type": "multipart/form-data", // 默认值
        },
        method: "post",
        data: formData,
        params: {
          path: relativePath,
          parentId: this.selectList.id
        },

      }).then((res) => {
        if (res.data.code == "0000") {
          this.isUpload = false;
          this.$baseMessage(`上传成功`, "success");
          this.Refresh()
        }
      });
    },
    // 正文文件删除
    handleTextRemove(file) {
      this.formData.contentId = "";
      this.fileTextList = [];
      this.$baseMessage(`删除成功`, "success");
      this.Refresh()
    },
    // 上传之前
    beforeUpload(file, fileList) {
      // const isLt20M = file.size / 1024 / 1024 < 20;
      // if (!isLt20M) {
      //   this.$message.error("上传文件大小不能超过 20M!");
      // }
      // var reg = /^.+(docx|doc|pdf|png|jpeg|jpg|\/mp4)$/;
      // const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      // if (!isDoc) {
      //   this.$message.error(
      //     "上传的文件格式只能是doc|docx|pdf|png|jpeg|jpg|mp4"
      //   );
      //   return;
      // }
      return false;
    },
    // 关闭
    close() {
      this.visible = false;
      this.isSetUp = false;
      this.show = this.queryForm.displayData.toString();
      this.detailsForm = {};
      this.featData();
    },
    featData() { },
    // 刷新
    Refresh() {
      this.parentData = null;
      // this.SCdisabled = false;
      this.formList = [],
        this.selectedKeyslist = [];
      this.featData();
      this.dataTree();
    },
    //添加类型
    arrAddType(arr) {
      arr.forEach((item, index) => {
        if (item.folderName == null) {
          var hz = item.fileName.substr(item.fileName.lastIndexOf('.') + 1);
          if (hz == 'docx' || hz == 'doc') {
            item['type'] = 'word'
          } else if (hz == 'jpg' || hz == 'png' || hz == 'git' || hz == 'jpeg') {

            item['type'] = 'image'
          } else if (hz == 'mp4' || hz == 'avi' || hz == 'mov' || hz == 'mpeg' || hz == 'wmv' || hz == 'flv' || hz == 'mkv') {

            item['type'] = 'video'
          } else if (hz == 'pdf') {
            item['type'] = 'pdf'
          } else if (hz == "xls" || hz == 'xlsx') {
            item["type"] = "xls";
          }
        } else {

          item['type'] = 'file'
        }
      })
    },
    closes() {
      this.fileManagementVisible = false;
      this.checked = [];
    },
  },
};
</script>
<style scoped>
#title {
  height: 40px;
  line-height: 40px;
  background-color: #fbeeef;
  border-radius: 6px;
  text-align: center;
  font-family: PingFang-M;
}

.con_box {
  padding: 10px;
}

.ant-card-body {
  padding: 8px;
}
</style>