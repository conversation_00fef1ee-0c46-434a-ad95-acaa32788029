<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <a-row>
          <a-form
            ref="queryForm"
            :form="queryForm"
            :model="queryForm"
            layout="horizontal"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18, offset: 0 }"
          >
            <div>
              <SearchForm no-more @onReset="reset" @onSearch="handleQuery">
                <template v-slot:topSearch>
                  <SingleSearch
                    :title="'授权用户'"
                    @onEnter="handleEnter"
                    @getContent="
                    (val) => {
                      queryForm.userName = val;
                    }
                  "
                  />
                  <SingleSearch
                    :title="'操作用户'"
                    @onEnter="handleEnter"
                    @getContent="
                    (val) => {
                      queryForm.createName = val;
                    }
                  "
                  />
                  <DateSelect
                    v-if="queryForm.condition === '0'"
                    :title="'操作时间'"
                    :timeValue.sync="queryForm.time"
                  />
                </template>
              </SearchForm>
            </div>

          </a-form>
        </a-row>
        <a-row>
          <standard-table
            :row-key="
              (record, index) => {
                return record.id;
              }
            "
            :columns="columns"
            :pagination="pagination"
            :data-source="dataSource"
            :loading="TBloading"

          >
            <!--            <div-->
            <!--              slot="operation"-->
            <!--              slot-scope="{ text, record }"-->
            <!--              class="operationStyle"-->
            <!--            >-->
            <!--              <span @click="handleData(record)"> 查看 </span>-->
            <!--            </div>-->
          </standard-table>
        </a-row>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { myPagination } from "@/mixins/pagination.js";
import SearchForm from "@/components/SearchForm/index";
import SingleSearch from "@/components/SingleSearch/index";
import SelectSearch from "@/components/SelectSearch/index";
import {list} from "@/api/rootLog";
import { parseTime } from "@/utils";
import DateSelect from "@/components/DateSelect/index.vue";
export default {
  name: "dataProcess",
  components: {
    DateSelect,
    SearchForm,
    SingleSearch,
    SelectSearch
  },

  mixins: [myPagination],
  data() {
    return {
      TBloading: false,
      queryForm: {
        userName: "",
        createName: "",
        page: 1,
        rows: 10,
        order: "false",
        sortField: "create_time",
        condition: '0',
        time : undefined
      },
      indexNum: 1,
      dataSource: [],
      columns: [
        {
          title: "序号",
          key: "index",
          fixed: "left",

          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "授权身份",
          dataIndex: "identity",
          width: 200,
          ellipsis: true,
          align: "center",
          customRender: (text, record, index) => {
            return text || '0';
          },
        },
        {
          title: "授权用户",
          dataIndex: "userName",
          width: 200,
          ellipsis: true,
          align: "center",
          customRender: (text, record, index) => {
            return text || '/';
          }
        },
        {
          title: "操作人",
          dataIndex: "createName",
          width: 200,
          ellipsis: true,
          align: "center",
          customRender: (text, record, index) => {
            return text || '0';
          },
        },
        {
          title: "操作时间",
          dataIndex: "createTime",
          width: 200,
          ellipsis: true,
          align: "center",
          customRender: (text, record, index) => {
            return parseTime(text);
          },
        }/*,
        {
          title: "总次数",
          dataIndex: "ZS",
          width: 200,
          ellipsis: true,
          align: "center",
          customRender: (text, record, index) => {
            return text || '0';
          },
        }*/
      ],
      searchDebounced: _.debounce(this.handleQuery, 500),
      visible: false,
      templatetypeList: []
    };
  },

  created() {
    this.handleQuery();
    this.loadTemplatetypeList();
  },
  beforeDestroy() {},
  methods: {
    handleEnter() {
      this.searchDebounced();
    },
    handleData(row) {},
    // 点击行
    clickRow(event, record) {
      try {
        let state;
        let tableHeader = event.target.__vue__.column.key;
        tableHeader ? (state = true) : (state = false);
        if (tableHeader) {
          // this.handleData(record, '查看详情');
        }
      } catch (error) {}
    },
    handleQuery() {
      this.queryForm.page = 1;
      this.fetchData();
    },
    async fetchData() {
      this.TBloading = true;
      const res = await list(this.queryForm);
      if (res.code === 200) {
        this.dataSource = res.data.records;
        // 设置总数
        this.pagination.total = res.data.total;
      }

      this.TBloading = false;
    },
    //重置
    reset() {
      this.queryForm = this.$options.data.call(this).queryForm;
      this.dateRange = [];
      this.handleQuery();
    },
    loadTemplatetypeList() {
      // todo 接口待定
    },
  },
};
</script>

<style lang="scss" scoped></style>
