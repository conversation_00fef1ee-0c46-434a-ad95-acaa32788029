import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/meetingRole/getRoleList",
    method: "post",
    data,
  });
}

export function getAllList() {
  return request({
    url: "/api/v1/meetingRole/getAllList",
    method: "post",
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/meetingRole/delRole",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/meetingRole/addRole",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/meetingRole/updateRole",
    method: "post",
    data,
  });
}
