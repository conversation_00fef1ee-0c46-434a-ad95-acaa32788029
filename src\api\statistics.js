import request from "@/utils/request";

export function getDutyList() {
    return request({
        url: "/api/v1/statistics/getDutyList",
        method: "post",
    });
}


export function getViewList(data) {
    return request({
        url: "/api/v1/statistics/getViewList",
        method: "post",
        data
    });
}
export function getDutyMore(data) {
    return request({
        url: "/api/v1/statistics/getDutyInfo",
        method: "post",
        data
    });
}
export function getDutyNoInfo(data) {
    return request({
        url: "/api/v1/statistics/getDutyNoInfo  ",
        method: "post",
        data
    });
}

export function getLoginList(data) {
    return request({
        url: "/api/v1/statistics/getLoginList",
        method: "post",
        data
    });
}

export function getColumnList() {
    return request({
        url: "/api/v1/statistics/getColumnList",
        method: "post",
    });
}