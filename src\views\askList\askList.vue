<template>
  <a-card>
    <!-- <a-form layout="horizontal"
            :form="queryForm">
      <div>
        <a-row>
          <a-col :md="6"
                 :sm="24">
            <a-form-item label="标题"
                         :labelCol="{ span: 6 }"
                         :wrapperCol="{ span: 18, offset: 0 }">
              <a-input allow-clear
                       v-model="queryForm.inquiryTitle"
                       placeholder="请输入标题"
                       v-on:keyup.enter="() => {
                queryForm.page = 1;
                fetchData();
              }" />
            </a-form-item>
          </a-col>
          <a-col :md="6"
                 :sm="24"
                 style="float: right ">
            <a-form-item style="float: right;  ">
              <a-button type="primary"
                        @click="
                () => {
                  queryForm.page = 1;
                  fetchData();
                }
              ">搜索</a-button>
              <a-button style="margin-left: 12px;"
                        @click="resetForm"
                        class="pinkBoutton">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form> -->

    <SearchForm @onReset="resetForm" @onSearch="search" :noMore="true">
      <template v-slot:topSearch>
        <SingleSearch @onEnter="search" :title="'标题'" :value.sync="queryForm.inquiryTitle" />
      </template>
    </SearchForm>

    <div>
      <a-space class="operator">
        <a-button type="primary"
                  @click="addData">新增</a-button>
        <a-button type="primary"
                  @click="delData">删除</a-button>
        <a-button type="primary"
                  @click="editData">修改</a-button>
        <a-button type="primary"
                  @click="showDesc">查看</a-button>
      </a-space>
      <standard-table :columns="columns"
                      :dataSource="dataSource"
                      :selectedRows.sync="selectedRows"
                      :loading="TBloading"
                      :pagination="pagination"
                      rowKey="id">
      </standard-table>
    </div>
    <askListEdit ref="askListEdit"
                 @reset="fetchData"></askListEdit>
  </a-card>
</template>

<script>
import moment from "moment";
import StandardTable from "@/components/table/StandardTable";
import askListEdit from "@/views/askList/askListEdit.vue";
import { askQueryList, askDelete } from "@/api/myJob/myProposal.js";
import { myPagination } from "@/mixins/pagination.js";
import SingleSearch from '@/components/SingleSearch/index';
import SearchForm from '@/components/SearchForm/index';

const columns = [
  {
    title: "标题",
    width: 200,
    dataIndex: "inquiryTitle",
  },
  {
    title: "创建人",
    ellipsis: true,
    width: 120,
    dataIndex: "createName",
  },
  {
    title: "创建时间",
    ellipsis: true,
    width: 120,
    dataIndex: "createTime",
  },
];

export default {
  name: "AskList",
  components: {
    StandardTable,
    askListEdit,
    SearchForm,
    SingleSearch,
  },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,

      columns: columns,
      dataSource: [],
      selectedRows: [],
      queryForm: { page: 1, rows: 10, inquiryTitle: "" },
    };
  },

  created () {
    this.fetchData();
    window.addEventListener("keyup", this.enterSearch);
  },
  methods: {
    search (){
      this.queryForm.page = 1;
      this.fetchData();
    },
    moment,
    enterSearch: function (e) {
      if (e.keyCode === 13) {
        this.fetchData();
      }
    },
    // 重置
    resetForm () {
      this.queryForm = Object.assign(
        {},
        this.$options.data.call(this).queryForm
      ); this.fetchData();
    },
    // 新增
    addData () {
      this.$refs.askListEdit.title = "新增";
      this.$refs.askListEdit.visible = true;
    },
    // 删除
    delData () {
      if (this.selectedRows.length == 0) {
        return this.$message.error("请选择数据！");
      }
      if (this.selectedRows.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "确定删除",
        content: "是否确定删除？",
        onOk: () => {
          askDelete({ id: this.selectedRows[0].id }).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.fetchData();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 编辑
    editData () {
      if (this.selectedRows.length == 0) {
        return this.$message.error("请选择数据！");
      }
      if (this.selectedRows.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.$refs.askListEdit.title = "编辑";
      this.$refs.askListEdit.visible = true;
      this.$refs.askListEdit.id = this.selectedRows[0].id;
      this.$refs.askListEdit.getDataById();
      this.selectedRows = [];
    },
    // 查看
    showDesc () {
      if (this.selectedRows.length == 0) {
        return this.$message.error("请选择数据！");
      }
      if (this.selectedRows.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.$refs.askListEdit.title = "查看";
      this.$refs.askListEdit.visible = true;
      this.$refs.askListEdit.id = this.selectedRows[0].id;
      this.$refs.askListEdit.getDataById();
      this.selectedRows = [];
    },
    // 获取数据
    fetchData () {
      this.TBloading = true;
      askQueryList(this.queryForm).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.dataSource = res.data.data.records;
          this.pagination.total = res.data.data.total;
          console.log(
            "🤗🤗🤗, this.pagination.total =>",
            this.pagination.total
          );
          // this.TBloading = false;
        } else {
          this.$message.error(res.data.message);
        }
      }).catch((error) => {
        throw(error)
      }).finally(() => {
        this.TBloading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
  margin-bottom: 10px;
}

.fold {
  width: calc(100% - 216px);
  display: inline-block;
}

.operator {
  margin-left: 10px;
  margin-bottom: 18px;
}

.mt2 {
  margin-top: 36px;
}

@media screen and (max-width: 900px) {
  .fold {
    width: 100%;
  }
}
</style>
