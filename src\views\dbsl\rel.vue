/* eslint-disable */
<template>
  <div class="table-container">
    <a-row :gutter="15">
      <a-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
        <div class="box-card">
          <a-input-search placeholder="输入关键字过滤" allow-clear anter-button @search="onChangeLeft" />
          <!-- <el-tree
            ref="duTree"
            show-checkbox
            :data="duTreeData"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :props="defaultProps"
            :default-expand-all="false"
            :expand-on-click-node="true"
            :default-expanded-keys="leftDefaultExpandKeys"
            :default-checked-keys="[0]"
            class="byui-filter-tree fk-tree"
            node-key="id"
            @check="leftHandleCheck"
          ></el-tree> -->

          <a-tree ref="identityTree" checkable :tree-data="duTreeData" :checkedKeys="selectedKeys_left"
            :expanded-keys="expandedKeys_left" :auto-expand-parent="autoExpandParent_left" multiple
            :replace-fields="defaultProps" @check="leftHandleCheck" @expand="onExpandLeft" :selectable="false">
            <template slot="title" slot-scope="{ fullName }">
              <span v-if="searchValue_left && fullName.indexOf(searchValue_left) > -1" style="color: #f50;">
                {{ fullName }}
              </span>
              <span v-else>{{ fullName }}</span>
            </template>
          </a-tree>
        </div>
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
        <div class="box-card" style="height: 320px; margin-bottom: 20px; overflow: auto;">
          <a-tag color="blue" v-for="tag in leftTags" :key="tag.id" closable @close="handleLeftClose(tag.id)">
            {{ tag.name }}
          </a-tag>
        </div>
        <div class="box-card" style="height: 320px; margin-bottom: 20px; overflow: auto;">
          <a-tag color="green" style="float: right;" v-for="tag in rightTags" type="success" :key="tag.id" closable
            @close="handleRightClose(tag.id)">
            {{ tag.name }}
          </a-tag>
        </div>
        <a-button style="
            background-color: #13ce66;
            position: relative;
            left: 45%;
            border: none;
            color: #fff;
          " type="success" @click="handleAuth()">
          更改
        </a-button>
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
        <div class="box-card">
          <!-- <el-input
            v-model="filterText2"
            placeholder="输入关键字过滤"
            allow-clear
            class="tree-input"
          />
          <el-tree
            v-if="memberContact.sourceType"
            ref="uTree"
            show-checkbox
            node-key="id"
            :data="uTreeData"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :props="defaultProps"
            :default-expand-all="false"
            :default-expanded-keys="rightDefaultExpandKeys"
            :expand-on-click-node="false"
            :auto-expand-parent="true"
            :default-checked-keys="[0]"
            @check="rightHandleCheck"
            class="byui-filter-tree fk-tree"
          ></el-tree> -->

          <a-input-search placeholder="输入关键字过滤" allow-clear anter-button @search="onChangeRight" />
          <a-tree v-if="memberContact.sourceType" ref="identityTree" checkable :tree-data="uTreeData"
            :checkedKeys="selectedKeys_right" :expanded-keys="expandedKeys_right"
            :auto-expand-parent="autoExpandParent_right" multiple :replace-fields="defaultProps"
            @check="rightHandleCheck" @expand="onExpandRight" :selectable="false">
            <template slot="title" slot-scope="{ fullName }">
              <span v-if="searchValue_right && fullName.indexOf(searchValue_right) > -1" style="color: #f50;">
                {{ fullName }}
              </span>
              <span v-else>{{ fullName }}</span>
            </template>
          </a-tree>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { instance_1 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
import { mapGetters } from "vuex";
import { findDbOU } from "@/api/registrationMage/tableIng.js";
import { now } from "moment";
export default {
  name: "auth",
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
  },
  data() {
    return {
      memberContact: {
        id: null,
        sourceType: "",
        sourceUserNames: "",
        targetUserNames1: "",
        targetUserNames2: "",
        memberContactRelList: [],
      },
      //机构树配置
      duTreeData: [],
      oneduTreeData: [], //市人大常委会主任会议
      twoduTreeData: [], //市人大常委会组成人员
      duTreeData1: [], //用来备份转换的
      uTreeData: [],
      zrhyTreeData: [],
      zcryTreeData: [],
      // defaultProps: {
      //   children: "childrenList",
      //   label: "name",
      // },
      loading: true,
      filterText1: "",
      filterText2: "",
      leftTags: [],
      rightTags: [],
      leftDefaultExpandKeys: ["O3251", "O3259"],
      rightDefaultExpandKeys: ["R000000", "R440000", "R440100"],

      searchValue_left: "", //搜索
      // 展开的节点
      expandedKeys_left: ["O3251", "O3259"],
      // 是否自动展开
      autoExpandParent_left: true,
      // 选择的节点
      selectedKeys_left: [],

      searchValue_right: "", //搜索
      // 展开的节点
      expandedKeys_right: ["R000000", "R440000", "R440100"],
      // 是否自动展开
      autoExpandParent_right: true,
      // 选择的节点
      selectedKeys_right: [],
      // 替换 treeNode 中 title,key,children 字段
      defaultProps: {
        children: "children",
        title: "fullName",
        key: "id",
      },
    };
  },
  created() {
    this.getTreeListFuc();
    this.$store.dispatch("navigation/breadcrumb1", "常用服务");
    this.$store.dispatch("navigation/breadcrumb2", "代表双联");
    const text3 = this.$route.query.id ? "代表双联编辑" : "代表双联";
    this.$store.dispatch("navigation/breadcrumb2", text3);
    console.log("🤗🤗🤗, this.memberContact.sourceType =>",
      this.memberContact.sourceType
    );
    // 赋值右边树 
    if (this.$route.query.type == '0') {
      this.memberContact.sourceType = "zrhy"; //主任
    } else {
      this.memberContact.sourceType = "zcry";
    }
  },
  watch: {
    filterText1(val) {
      this.$refs.duTree.filter(val);
    },
    filterText2(val) {
      this.$refs.uTree.filter(val);
    },
  },
  mounted() { },
  methods: {
    handleLeftClose(id) {
      // this.$refs.duTree.setChecked(id, false, false);
      let arr = this.selectedKeys_left.filter((item) => item != id);
      this.$set(this, "selectedKeys_left", arr);
    },
    handleRightClose(id) {
      // this.$refs.uTree.setChecked(id, false, false);
      let arr = this.selectedKeys_right.filter((item) => item != id);
      this.$set(this, "selectedKeys_right", arr);
      let rightTagslist = this.rightTags.filter((item) => item.id != id);
      this.$set(this, "rightTags", rightTagslist);
    },

    // 获取tree数据
    getTreeListFuc() {


      // 左and右 树的赋值 传了当前届次3
      findDbOU('3').then((res) => {
        // 市人大常委会主任会议
        if (this.$route.query.type == '0') {
          this.duTreeData.push(res.data.data[3]); //市人大常委会主任会议树
          // this.uTreeData=res.data.data;
          // this.zrhyTreeData.push(res.data.data[0]); //全国
           this.zrhyTreeData=res.data.data //改成全部
          this.uTreeData = this.zrhyTreeData;
          console.log("市人大常委会主任会议")
        } else {
          this.duTreeData.push(res.data.data[2]); //市人大常委会组成人员  树  
           this.zrhyTreeData=res.data.data //改成全部
           //移除全国 
          // this.zrhyTreeData.push(res.data.data[1]); 
          // this.zrhyTreeData.push(res.data.data[2]);
          // this.zrhyTreeData.push(res.data.data[3]);
          // this.zrhyTreeData.push(res.data.data[4])
          // this.zrhyTreeData.push(res.data.data[5])
          // this.zrhyTreeData.push(res.data.data[6])
          this.uTreeData = this.zrhyTreeData;
          console.log("市人大常委会组成人员")
        }

        //   //回选数据
        const contactId = this.$route.query.id;
        if (contactId != undefined && contactId != null) {
          this.memberContact.id = contactId;
        }
        if (this.memberContact.id) {
          let params = {
            id: this.memberContact.id,  //用户id
            type: this.$route.query.type //类型
          };
          // 回选
          this.checkRel(params);
        }
      })
    },

    // 节点过滤操作
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    getID(arr, returnArr) {
      for (let index = 0; index < arr.length; index++) {
        const element = arr[index];
        returnArr.push(element.id);
        if (element.children) {
          this.getID(element.children, returnArr);
        }
      }
      return returnArr;
    },
    leftHandleCheck(checkedKeys, info) {
      let notAvailable = [];
      if (info.checked == true) {
        if (
          (info.halfCheckedKeys.includes("O3259") &&
            info.halfCheckedKeys.includes("O3251")) ||
          (info.halfCheckedKeys.includes("O3259") &&
            info.node.eventKey == "O3251") ||
          (info.halfCheckedKeys.includes("O3251") &&
            info.node.eventKey == "O3259")
        ) {
          this.$baseMessage(
            "不可同时选择市人大常委会组成人员与主任会议",
            "error"
          );
          // 获取不可选取节点的id，包括子id
          notAvailable = this.getID([info.node.dataRef], []);
          // 不对不可选取节点赋值,还赋值原来的，解决未选中依旧显示的bug
          let newList = JSON.parse(JSON.stringify(this.selectedKeys_left));
          this.selectedKeys_left = newList;
        } else {
          this.selectedKeys_left.push(info.node.eventKey);
        }

        this.uTreeData = this.zrhyTreeData;
        // 赋值右边树 
        if (this.$route.query.type == 0) {
          this.memberContact.sourceType = "zrhy"; //主任
        } else {
          this.memberContact.sourceType = "zcry";
        }

      } else {
        this.selectedKeys_left = checkedKeys.filter(
          (item) => item != info.node.eventKey
        );
      }
      this.memberContact.sourceUserNames = "";
      this.leftTags = [];
      if (info.checkedNodes.length > 0) {
        info.checkedNodes.forEach((node) => {
          let nodeData = node.data.props.dataRef;
          if (!notAvailable.includes(nodeData.id)) {
            // 判断是否是用户（最外层）
            if (nodeData.userId) {
              this.memberContact.sourceUserNames += nodeData.fullName + ";";
              this.leftTags.push({
                id: nodeData.id,
                name: nodeData.deptName + " " + nodeData.fullName,
                userName: nodeData.fullName,
                userDuty: nodeData.deptName,
                userId: nodeData.userId,
              });
            }
          }
        });
      } else {
        // 清空
        this.memberContact.sourceType = "";
        this.rightTags = [];
        this.selectedKeys_right = [];
        this.expandedKeys_right = [];
        this.memberContact.targetUserNames1 = "";
        this.memberContact.targetUserNames2 = "";
      }
    },

    rightHandleCheck(checkedKeys, info) {
      let notAvailable = []; //不可选取数组
      // 选择
      if (info.checked == true) {
        if (this.memberContact.sourceType == "") {
          this.$baseMessage("请先选择左侧关联的代表", "error");
          // 不进行选择
          let newList = JSON.parse(JSON.stringify(this.selectedKeys_right));
          this.selectedKeys_right = newList;
          return;
        } else if (this.memberContact.sourceType == "zrhy") {
          // 清空
          this.rightTags = [];
          this.memberContact.targetUserNames1 = "";
          this.memberContact.targetUserNames2 = "";
          info.checkedNodes.forEach((node) => {
            let nodeData = node.data.props.dataRef;
            if (!notAvailable.includes(nodeData.id)) {
              if (nodeData.userId) {
                this.memberContact.targetUserNames1 += nodeData.fullName + ";";
                 let targetType = "";
                 if (nodeData.orgName == "全国人大代表") {
                    targetType = 1  //属于全国
                 }
                else if (nodeData.orgName == "省人大代表") {
                  targetType = 2  //属于省
                } else {
                  targetType = 3  //属于市
                }
                this.rightTags.push({
                  id: nodeData.id,
                  name: nodeData.deptName + " " + nodeData.fullName,
                  userName: nodeData.fullName,
                  userDuty: nodeData.deptName,
                  userId: nodeData.userId,
                  targetType: targetType, //全国   //主任只有全国

                });
              }
            }
          });
        } else if (this.memberContact.sourceType == "zcry") {

          this.rightTags = [];
          this.memberContact.targetUserNames1 = "";
          this.memberContact.targetUserNames2 = "";
          info.checkedNodes.forEach((node) => {
            let nodeData = node.data.props.dataRef;
            if (!notAvailable.includes(nodeData.id)) {
              if (nodeData.userId) {
                let targetType = "";
                 if (nodeData.orgName == "全国人大代表") {
                    targetType = 1  //属于全国
                 }
                else  if (nodeData.orgName == "省人大代表") {
                  targetType = 2  //属于省
                } else {
                  targetType = 3  //属于市
                }
                this.rightTags.push({
                  id: nodeData.id,
                  name: nodeData.deptName + " " + nodeData.fullName,
                  userName: nodeData.fullName,
                  userDuty: nodeData.deptName,
                  targetType: targetType,
                  userId: nodeData.userId,
                });
              }
            }
          });
        }
        // 上面进行return
        this.selectedKeys_right.push(info.node.eventKey);
      } else {
        // 对已选的进行取消选择
        // console.log("🤗🤗🤗, notAvailable =>", notAvailable);
        // 获取不可选取节点的id，包括子id
        notAvailable = this.getID([info.node.dataRef], []);
        this.selectedKeys_right = checkedKeys.filter(
          (item) => item != info.node.eventKey
        );
        // 不进行选择
        let newList = JSON.parse(JSON.stringify(this.selectedKeys_right));
        this.selectedKeys_right = newList;
        let newArrRightTags = this.rightTags.filter(
          (item) => !notAvailable.includes(item.id)
        );
        this.$set(this, "rightTags", newArrRightTags);
      }
      if (info.checkedNodes.length == 0) {
        this.rightTags = [];
        this.memberContact.targetUserNames1 = "";
        this.memberContact.targetUserNames2 = "";
        this.selectedKeys_right = [];
      }
    },
    // 保存
    handleAuth() {
      if (this.leftTags.length == 0 || this.rightTags.length == 0) {
        this.$message.error("请选择双联代表");
        return false;
      }
      this.memberContact.memberContactRelList = [];
      this.leftTags.forEach((item1) => {
        this.rightTags.forEach((item2) => {
          this.memberContact.memberContactRelList.push({
            leftId: item1.id, //唯一性
            userId: item1.userId,//用户id
            userName: item1.userName,//用户名
            userDuty: item1.userDuty,//用户职务
            toSfDm: item2.targetType,     //身份代码，1全国，2省，3市  
            rightId: item2.id,
            toUserId: item2.userId,//联系的代表id
            toUserName: item2.userName,//联系的代表名称
            toUserDuty: item2.userDuty,//联系的代表职务
            type: this.memberContact.sourceType == "zrhy" ? 0 : 1,  // type： 类型，0：市人大常委会主任会议，1：市人大常委会组成人员
          });
        });
      });
      instance_1({
        url: "/contact/updateInsertDoubleContact",   //更改
        method: "post",
        data: this.memberContact.memberContactRelList,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.$message.success("更新成功");
          //清空
          Object.assign(this.$data, this.$options.data());
          if (this.$route.query.type == "0") {
            this.$router.push({ path: "/ComponentData/management" });
            // this.$router.push({ path: "/ComponentData/informationSearch" });
          } else {
            this.$router.push({ path: "/ComponentValue/management1" });
            // this.$router.push({ path: "/ComponentValue/informationSearch1" });
          }
        } else {
          this.$message.error(res.data.msg);
        }
      });
      // if (!this.$route.query.id) {
      //   instance_1({
      //     url: "/contact/batchInsertDoubleContact",
      //     method: "post",
      //     data: this.memberContact.memberContactRelList,
      //   }).then((res) => {
      //     if (res.data.code == "0000") {
      //       this.$message.success("保存成功");
      //       //清空
      //       Object.assign(this.$data, this.$options.data());
      //       this.$router.push({ path: "/dbslManage/dbslList" });
      //     } else {
      //       this.$message.error(res.msg);
      //     }
      //   });
      // } else {

      // }
    },

    //回选
    checkRel(params) {
      let sourceUserIds = [];
      let targetUserIds = [];

      instance_1({
        url: "/contact/doubleContactDetail",
        method: "get",
        params: params,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.memberContact = res.data.data;
          if (this.$route.query.type == "0") {
            this.memberContact.sourceType = "zrhy"; //主任
          } else {
            this.memberContact.sourceType = "zcry";
          }
          this.memberContact.sourceUserNames = "";
          this.memberContact.targetUserNames1 = "";
          this.memberContact.targetUserNames2 = "";

          var letfdata = { ...res.data.data[0] };
          sourceUserIds.push(res.data.data[0])
          res.data.data.forEach((item) => {
            targetUserIds.push(item);
          });
          // let leftD=sourceUserIds.map((item)=>{item.leftId})  //左边选中 
          let leftD = []
          sourceUserIds.forEach((item, index) => {
            if (item.leftId.indexOf(',') != -1) {
              let list = item.leftId
              let arr = list.split(',');
              arr.map((item) => {
                leftD.push(item)
              })
            } else {
              leftD.push(item.leftId)
            }
          })

          // let rightD=targetUserIds.map((item)=>item.rightId)  //右边选中
          let rightD = [];
          targetUserIds.forEach((item, index) => {
            if (item.rightId.indexOf(',') != -1) {
              let list = item.rightId
              let arr = list.split(',');
              arr.map((item) => {
                rightD.push(item)
              })
            } else {
              rightD.push(item.rightId)
            }
          })
          this.$nextTick(() => {
            this.selectedKeys_left = leftD;
            this.selectedKeys_right = rightD;
            this.expandedKeys_left = leftD;
            this.expandedKeys_right = rightD;
            this.autoExpandParent_left = true;
            this.autoExpandParent_right = true;
          });
          // // 获取已选的对应的节点数据
          let getNodeData = (idValue, tree) => {
            let Data = "";
            for (let index = 0; index < tree.length; index++) {
              const node = tree[index];

              if (node.id == idValue) {
                Data = node;
              } else {
                if (node.children) {
                  if (getNodeData(idValue, node.children)) {
                    Data = getNodeData(idValue, node.children);
                  }
                }
              }
            }
            return Data;
          };

          setTimeout(() => {
            // let leftCheckNodes = this.$refs.duTree.getCheckedNodes(true);
            let leftCheckNodes = [];
            for (let index = 0; index < sourceUserIds.length; index++) {
              const element = sourceUserIds[index];

              // leftCheckNodes.push(getNodeData(element.leftId, this.duTreeData));      由于数据类型兼容

              if (element.leftId.indexOf(',') != -1) {
                let list = element.leftId
                let arr = list.split(',');
                arr.map((item) => {
                  leftCheckNodes.push(getNodeData(item, this.duTreeData));
                })
              } else {
                leftCheckNodes.push(getNodeData(element.leftId, this.duTreeData));
              }
            }
            if (leftCheckNodes.length > 0) {
              leftCheckNodes.forEach((nodeData) => {
                if (nodeData.userId) {
                  this.memberContact.sourceUserNames += nodeData.fullName + ";";
                  this.leftTags.push({
                    id: nodeData.id,
                    name: nodeData.deptName + " " + nodeData.fullName,
                    userName: nodeData.fullName,
                    userDuty: nodeData.deptName,
                    userId: nodeData.userId,
                  });
                }
              });
            }

            // let rightCheckNodes = this.$refs.uTree.getCheckedNodes(true);
            let rightCheckNodes = [];
            for (let index = 0; index < targetUserIds.length; index++) {
              const element = targetUserIds[index];
              // rightCheckNodes.push(getNodeData(element.rightId, this.uTreeData));    由于数据类型兼容
               if (element.rightId.indexOf(',') != -1) {
                let list = element.rightId
                let arr = list.split(',');
                arr.map((item) => {
                  rightCheckNodes.push(getNodeData(item, this.uTreeData));
                })
              } else {
                rightCheckNodes.push(getNodeData(element.rightId, this.uTreeData));
              }
            }
            if (rightCheckNodes.length > 0) {
              rightCheckNodes.forEach((nodeData) => {
                if (nodeData.userId) {
                  let targetType = "";
                  if (this.$route.query.type == 0) {
                    targetType = 1  //全国
                  } else {
                    if (nodeData.orgName == "省人大代表") {
                      targetType = 2  //属于省
                    } else {
                      targetType = 3  //属于市
                    }
                  }
                  this.rightTags.push({
                    id: nodeData.id,
                    name: nodeData.deptName + " " + nodeData.fullName,
                    userName: nodeData.fullName,
                    userDuty: nodeData.deptName,
                    targetType: targetType,
                    userId: nodeData.userId,
                  });
                }
              });
            }
          }, 1000);
        }
      });

    },

    //展开点击节点下的子节点
    onExpandLeft(expandedKeys) {
      this.expandedKeys_left = expandedKeys;
      this.autoExpandParent_left = false;
    },
    //展开点击节点下的子节点
    onExpandRight(expandedKeys) {
      this.expandedKeys_right = expandedKeys;
      this.autoExpandParent_right = false;
    },

    // 树状搜索
    onChangeLeft(e) {
      const value = e;
      const expandedKeys = this.getKeyList(
        value,
        this.duTreeData,
        [],
        "fullName",
        "id"
      );
      Object.assign(this, {
        expandedKeys_left: expandedKeys,
        searchValue_left: value,
        autoExpandParent_left: true,
      });
    },
    // 树状搜索
    onChangeRight(e) {
      const value = e;
      const expandedKeys = this.getKeyList(
        value,
        this.uTreeData,
        [],
        "fullName",
        "id"
      );
      Object.assign(this, {
        expandedKeys_right: expandedKeys,
        searchValue_right: value,
        autoExpandParent_right: true,
      });
    },

    // 获取包含字段id的父节点
    getKeyList(value, tree, keyList, titleName, idName) {
      // 获取包含搜索关键字的所有节点
      let getId = (value_id, tree_id, list_id) => {
        console.log("🤗🤗🤗, idName =>", idName);
        for (let i = 0; i < tree_id.length; i++) {
          const node = tree_id[i];
          if (node[titleName].indexOf(value_id) > -1) {
            list_id.push(node[idName]);
          }
          if (node.children) {
            getId(value_id, node.children, list_id);
          }
        }
        return list_id;
      };
      // 获取包含搜索关键字的所有节点的父节点
      let getParentKey = (value_par, tree_par) => {
        let parentKey;
        for (let index = 0; index < tree_par.length; index++) {
          const node = tree_par[index];
          if (node.children) {
            if (node.children.some((item) => item[idName] === value_par)) {
              parentKey = node[idName];
            } else if (getParentKey(value_par, node.children)) {
              parentKey = getParentKey(value_par, node.children);
            }
          }
        }
        return parentKey;
      };
      // 执行
      getId(value, tree, []).forEach((v) => {
        keyList.push(getParentKey(v, tree));
      });
      return keyList;
    },

    checkPermission,
  },
};
</script>
<style scoped>
.fk-tree {
  height: 93%;
}

.box-card {
  height: 720px;
  overflow-y: auto;
  border: 1px #ebeef5 solid;
  padding: 10px;
  box-shadow: 2px 2px 6px 5px #f5f5f5;
  border-radius: 2px;
}
</style>
