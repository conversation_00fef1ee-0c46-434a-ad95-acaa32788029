import { instance_yajy } from "@/api/axiosRq";

// 未签收建议统计
// queryForm 请求搜索数据
export function getListData(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/cbdqqd`,
    method: "post",
    params: queryForm,
  });
}
// 导出excel
export function exportExcelData(meeting) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportNoSign`,
    method: "post",
    responseType: "blob",
    params: { meeting },
  });
}
// 分数
export function getdbEvaluation(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/dbEvaluation`,
    method: "post",
    params: queryForm,
  });
}
// 导出1
export function exportDbEvaluation(meeting) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportDbEvaluation`,
    method: "post",
    responseType: "blob",
    params: { meeting },
  });
}
export function exportMainOrgEvaluation(meeting) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportMainOrgEvaluation`,
    method: "post",
    responseType: "blob",
    params: { meeting },
  });
}

export function exportDbSEvaluation(meeting) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportDbSEvaluation`,
    method: "post",
    responseType: "blob",
    params: { meeting },
  });
}
