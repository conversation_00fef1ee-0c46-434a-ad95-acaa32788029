import request from "@/utils/request";
import requestTemp from "@/utils/requestTemp";

import qs from "qs";

export function getTree(data) {
  return request({
    url: "/api/v1/display/org/queryTree",
    method: "post",
    data,
  });
}

export function getTreeByRootOrgIds(data) {
  return request({
    url: "/api/v1/display/org/queryTreeByRootOrgIds",
    method: "post",
    data,
  });
}

export function getList(data) {
  return request({
    url: "/api/v1/manage/org/findPage",
    method: "post",
    data,
  });
}

export function getSourceOrgList(data) {
  return request({
    url: "/api/v1/display/org/queryListByTargetOrg",
    method: "post",
    data,
  });
}

export function getTargetOrgList(data) {
  return request({
    url: "/api/v1/display/org/queryTargetList",
    method: "post",
    data,
  });
}

export function getById(data) {
  return request({
    url: "/api/v1/manage/org/findById",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/manage/org/save",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/manage/org/removeBatch",
    method: "post",
    data,
  });
}

export function changeSort(data) {
  return request({
    url: "/api/v1/manage/org/changeSort",
    method: "post",
    data,
  });
}

export function getOptions(params) {
  return request({
    url: "/api/v1/display/org/queryOptions",
    method: "post",
    params,
  });
}

export function groupByDepartment(params) {
  return requestTemp({
    url: "/organization/group_by_department",
    method: "get",
    params: params,
  });
}

export function findLevelList(params) {
  return requestTemp({
    url: "/organization/findLevelList",
    method: "get",
    params: params,
  });
}

export function findLevelByJcDm(params) {
  return requestTemp({
    url: "/organization/findLevelByJcDm",
    method: "get",
    params: params,
  });
}

export function findAllByOrganizationIdAndTermId(params) {
  return requestTemp({
    url: "/organization/findAllByOrganizationIdAndTermId",
    method: "get",
    params: params,
  });
}

export function findAllByOrganizationIdAndTermIdTwo(params) {
  return requestTemp({
    url: "/organization/findAllByOrganizationIdAndTermIdTwo",
    method: "get",
    params: params,
  });
}

export function findAllByOrganizationIdAndTermIdThree(params) {
  return requestTemp({
    url: "/organization/findAllByOrganizationIdAndTermIdThree",
    method: "get",
    params: params,
  });
}

export function getDepartmentDelegation(params) {
  return requestTemp({
    url: "/organization/departmentDelegation",
    method: "get",
    params: params,
  });
}

export function canManageQZJWorkerInfo(params) {
  return requestTemp({
    url: "/organization/canManageQZJWorkerInfo",
    method: "get",
    params: params,
  });
}
export function canManageSJWorkerInfo(params) {
  return requestTemp({
    url: "/organization/canManageSJWorkerInfo",
    method: "get",
    params: params,
  });
}

export function queryOrgUnitTreeByMainIdentify(params) {
  return requestTemp({
    url: "/dmcs/common/ours/queryOrgUnitTreeByMainIdentify",
    method: "post",
    params: params,
  });
}