<template>
  <div class="table-container">
    <SearchForm :no-more="true" @onReset="reset" @onSearch="search">
      <template v-slot:topSearch>
        <SingleSearch
          :title="'制度名称'"
          :value.sync="queryForm.systemName"
          @onEnter="search"
        />
      </template>
    </SearchForm>

    <div style="margin-bottom: 16px">
      <a-button type="primary" @click="handleAdd">
        <a-icon type="plus" />
        新增制度
      </a-button>
    </div>

    <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
      <a-spin :indicator="indicator" :spinning="listLoading">
        <a-table
          :columns="columns"
          :data-source="list"
          :pagination="pagination"
          row-key="id"
          :scroll="{ x: 800 }"
        >
          <template slot="action" slot-scope="text, record">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleDownload(record)" v-if="record.attachmentId">
                下载附件
              </a-button>
              <a-popconfirm
                title="确定要删除这条记录吗？"
                @confirm="handleDelete(record)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a-button type="link" size="small" style="color: #ff4d4f">
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table>
      </a-spin>
    </a-col>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :width="800"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirmLoading="submitLoading"
    >
      <a-form-model
        ref="systemForm"
        :model="systemForm"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-model-item label="制度名称" prop="systemName">
          <a-input
            v-model="systemForm.systemName"
            placeholder="请输入制度名称"
            :disabled="isView"
          />
        </a-form-model-item>
        
        <a-form-model-item label="制度描述" prop="description">
          <a-textarea
            v-model="systemForm.description"
            placeholder="请输入制度描述"
            :rows="4"
            :disabled="isView"
          />
        </a-form-model-item>

        <a-form-model-item label="制度附件" prop="attachment">
          <div v-if="!isView">
            <a-upload
              :action="uploadAction"
              :headers="uploadHeaders"
              :before-upload="beforeUpload"
              @change="handleUploadChange"
              :file-list="fileList"
              :remove="handleRemove"
              accept=".pdf,.doc,.docx,.xls,.xlsx"
            >
              <a-button>
                <a-icon type="upload" />
                选择文件
              </a-button>
              <div style="margin-top: 8px; color: #666; font-size: 12px;">
                支持 PDF、Word、Excel 格式，文件大小不超过 20MB
              </div>
            </a-upload>
          </div>
          <div v-else-if="systemForm.attachmentName">
            <a-button type="link" @click="handleDownload(systemForm)">
              <a-icon type="download" />
              {{ systemForm.attachmentName }}
            </a-button>
          </div>
          <div v-else>
            <span style="color: #999;">暂无附件</span>
          </div>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
// import {
//   getSystemList,
//   createSystem,
//   updateSystem,
//   deleteSystem,
//   downloadSystemAttachment
// } from "@/api/systemManagement";
import { fileUpload } from "@/api/commonApi/file";
import checkPermission from "@/utils/permission";
import SearchForm from "@/components/SearchForm/index";
import SingleSearch from "@/components/SingleSearch/index";

export default {
  name: "SystemManagement",
  components: {
    SearchForm,
    SingleSearch,
  },
  data() {
    return {
      list: [],
      listLoading: false,
      modalVisible: false,
      modalTitle: "新增制度",
      isView: false,
      submitLoading: false,
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        systemName: "",
      },
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ["10", "20", "30", "40", "50"],
        showTotal: (total) => `总共 ${total} 条`,
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize),
        onChange: this.handleCurrentChange.bind(this),
        total: 0,
        current: 0,
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      // 表格列配置
      columns: [
        {
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          customRender: (_, __, index) =>
            `${(this.pagination.current - 1) * this.pagination.pageSize + index + 1}`,
        },
        {
          title: "制度名称",
          dataIndex: "systemName",
          align: "center",
          ellipsis: true,
        },
        {
          title: "制度描述",
          dataIndex: "description",
          align: "center",
          ellipsis: true,
          width: 300,
        },
        {
          title: "附件名称",
          dataIndex: "attachmentName",
          align: "center",
          ellipsis: true,
          customRender: (text) => text || "暂无附件",
        },
        {
          title: "创建时间",
          dataIndex: "createTime",
          align: "center",
          width: 180,
        },
        {
          title: "操作",
          key: "action",
          align: "center",
          width: 250,
          scopedSlots: { customRender: "action" },
        },
      ],
      // 表单数据
      systemForm: {
        id: null,
        systemName: "",
        description: "",
        attachmentId: null,
        attachmentName: "",
        attachmentPath: "",
      },
      // 表单验证规则
      rules: {
        systemName: [
          { required: true, message: "请输入制度名称", trigger: "blur" },
          { max: 100, message: "制度名称不能超过100个字符", trigger: "blur" },
        ],
        description: [
          { max: 500, message: "制度描述不能超过500个字符", trigger: "blur" },
        ],
      },
      // 文件上传相关
      fileList: [],
      uploadAction: "",
      uploadHeaders: {},
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    // 获取数据列表
    fetchData() {
      this.listLoading = true;

      // 模拟数据，实际使用时替换为真实API调用
      setTimeout(() => {
        const mockData = [
          {
            id: 1,
            systemName: "会议管理制度",
            description: "规范会议组织、召开、记录等各个环节的管理制度",
            attachmentId: "att001",
            attachmentName: "会议管理制度.pdf",
            attachmentPath: "/files/meeting_system.pdf",
            createTime: "2024-01-15 10:30:00",
          },
          {
            id: 2,
            systemName: "代表联系制度",
            description: "建立健全人大代表联系群众的工作制度",
            attachmentId: null,
            attachmentName: null,
            attachmentPath: null,
            createTime: "2024-01-10 14:20:00",
          },
          {
            id: 3,
            systemName: "信息公开制度",
            description: "保障公民知情权，规范信息公开流程和标准",
            attachmentId: "att003",
            attachmentName: "信息公开制度.docx",
            attachmentPath: "/files/info_disclosure.docx",
            createTime: "2024-01-05 09:15:00",
          },
        ];

        // 根据搜索条件过滤数据
        let filteredData = mockData;
        if (this.queryForm.systemName) {
          filteredData = mockData.filter(item =>
            item.systemName.includes(this.queryForm.systemName)
          );
        }

        this.list = filteredData;
        this.pagination.total = filteredData.length;
        this.pagination.current = this.queryForm.pageNum;
        this.listLoading = false;
      }, 500);

      // 真实API调用代码（注释掉，需要时启用）
      /*
      getSystemList(this.queryForm)
        .then((res) => {
          if (res.data.code === "0000" || res.data.code === 200) {
            this.list = res.data.rows || res.data.data || [];
            this.pagination.total = res.data.total || 0;
            this.pagination.current = this.queryForm.pageNum;
          } else {
            this.$message.error(res.data.msg || "获取数据失败");
          }
        })
        .catch((error) => {
          console.error("获取数据失败:", error);
          this.$message.error("获取数据失败");
        })
        .finally(() => {
          this.listLoading = false;
        });
      */
    },

    // 搜索
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },

    // 重置搜索
    reset() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        systemName: "",
      };
      this.pagination.current = 1;
      this.fetchData();
    },

    // 分页改变
    handleCurrentChange(page) {
      this.queryForm.pageNum = page;
      this.pagination.current = page;
      this.fetchData();
    },

    // 每页条数改变
    changePageSize(current, pageSize) {
      this.queryForm.pageNum = current;
      this.queryForm.pageSize = pageSize;
      this.pagination.current = current;
      this.pagination.pageSize = pageSize;
      this.fetchData();
    },

    // 新增
    handleAdd() {
      this.modalTitle = "新增制度";
      this.isView = false;
      this.resetForm();
      this.modalVisible = true;
    },

    // 编辑
    handleEdit(record) {
      this.modalTitle = "编辑制度";
      this.isView = false;
      this.systemForm = { ...record };
      this.fileList = record.attachmentName
        ? [
            {
              uid: record.attachmentId,
              name: record.attachmentName,
              status: "done",
              url: record.attachmentPath,
            },
          ]
        : [];
      this.modalVisible = true;
    },

    // 查看
    handleView(record) {
      this.modalTitle = "查看制度";
      this.isView = true;
      this.systemForm = { ...record };
      this.fileList = record.attachmentName
        ? [
            {
              uid: record.attachmentId,
              name: record.attachmentName,
              status: "done",
              url: record.attachmentPath,
            },
          ]
        : [];
      this.modalVisible = true;
    },

    // 删除
    handleDelete(record) {
      // 模拟删除操作
      this.$message.success("删除成功");
      this.fetchData();

      // 真实API调用代码（注释掉，需要时启用）
      /*
      deleteSystem(record.id)
        .then((res) => {
          if (res.data.code === "0000" || res.data.code === 200) {
            this.$message.success("删除成功");
            this.fetchData();
          } else {
            this.$message.error(res.data.msg || "删除失败");
          }
        })
        .catch((error) => {
          console.error("删除失败:", error);
          this.$message.error("删除失败");
        });
      */
    },

    // 下载附件
    handleDownload(record) {
      if (!record.attachmentId) {
        this.$message.warning("暂无附件可下载");
        return;
      }

      // 模拟下载操作
      this.$message.success(`正在下载 ${record.attachmentName}`);

      // 真实API调用代码（注释掉，需要时启用）
      /*
      downloadSystemAttachment(record.attachmentId)
        .then((res) => {
          // 创建下载链接
          const blob = new Blob([res.data]);
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = record.attachmentName;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(link.href);
        })
        .catch((error) => {
          console.error("下载失败:", error);
          this.$message.error("下载失败");
        });
      */
    },

    // 提交表单
    handleSubmit() {
      this.$refs.systemForm.validate((valid) => {
        if (valid) {
          this.submitLoading = true;

          // 模拟提交操作
          setTimeout(() => {
            this.$message.success(
              this.systemForm.id ? "更新成功" : "创建成功"
            );
            this.modalVisible = false;
            this.fetchData();
            this.submitLoading = false;
          }, 1000);

          // 真实API调用代码（注释掉，需要时启用）
          /*
          const apiMethod = this.systemForm.id ? updateSystem : createSystem;

          apiMethod(this.systemForm)
            .then((res) => {
              if (res.data.code === "0000" || res.data.code === 200) {
                this.$message.success(
                  this.systemForm.id ? "更新成功" : "创建成功"
                );
                this.modalVisible = false;
                this.fetchData();
              } else {
                this.$message.error(res.data.msg || "操作失败");
              }
            })
            .catch((error) => {
              console.error("操作失败:", error);
              this.$message.error("操作失败");
            })
            .finally(() => {
              this.submitLoading = false;
            });
          */
        }
      });
    },

    // 取消弹窗
    handleCancel() {
      this.modalVisible = false;
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.systemForm = {
        id: null,
        systemName: "",
        description: "",
        attachmentId: null,
        attachmentName: "",
        attachmentPath: "",
      };
      this.fileList = [];
      if (this.$refs.systemForm) {
        this.$refs.systemForm.resetFields();
      }
    },

    // 文件上传前验证
    beforeUpload(file) {
      const isValidType = [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ].includes(file.type);

      if (!isValidType) {
        this.$message.error("只能上传 PDF、Word、Excel 格式的文件！");
        return false;
      }

      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("文件大小不能超过 20MB！");
        return false;
      }

      return false; // 阻止自动上传，手动处理
    },

    // 文件上传变化
    handleUploadChange(info) {
      if (info.file.status === "removed") {
        this.systemForm.attachmentId = null;
        this.systemForm.attachmentName = "";
        this.systemForm.attachmentPath = "";
        this.fileList = [];
        return;
      }

      if (info.file.originFileObj) {
        this.uploadFile(info.file.originFileObj);
      }
    },

    // 上传文件
    uploadFile(file) {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("type", "10"); // 制度管理附件类型

      fileUpload(formData)
        .then((res) => {
          if (res.data.code === "0000" || res.data.code === 200) {
            const fileInfo = res.data.data[0];
            this.systemForm.attachmentId = fileInfo.attId;
            this.systemForm.attachmentName = fileInfo.originalName;
            this.systemForm.attachmentPath = fileInfo.filePath;

            this.fileList = [
              {
                uid: fileInfo.attId,
                name: fileInfo.originalName,
                status: "done",
                url: fileInfo.filePath,
              },
            ];

            this.$message.success("文件上传成功");
          } else {
            this.$message.error(res.data.msg || "文件上传失败");
          }
        })
        .catch((error) => {
          console.error("文件上传失败:", error);
          this.$message.error("文件上传失败");
        });
    },

    // 移除文件
    handleRemove() {
      this.systemForm.attachmentId = null;
      this.systemForm.attachmentName = "";
      this.systemForm.attachmentPath = "";
      this.fileList = [];
      return true;
    },

    checkPermission,
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}

.upload-tip {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}

::v-deep .ant-upload-list {
  margin-top: 8px;
}

::v-deep .ant-table-tbody > tr > td {
  padding: 12px 8px;
}

::v-deep .ant-btn-link {
  padding: 0;
  height: auto;
}
</style>
