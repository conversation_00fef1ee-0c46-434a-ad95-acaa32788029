<template>
  <div class=''>
    <a-modal
      :title="title"
      :visible.sync="dialogFormVisible"
      width="1000px"
      @cancel="close"
      @ok="confirm"
    >
    <div style="display: flex;">
    <a-tabs default-active-key="1" @change="changtab" style="width: 45%">
        <a-tab-pane key="1" tab="机构树">
          <div class="table_container">
            <div class="table_left">
              <a-input-search
                v-model="filterText"
                placeholder="输入关键字进行过滤"
                @search="onSearch"
              ></a-input-search>
              <a-tabs @change="changTreeTab" :style="{ margin: '0px' }" v-model="treeTabIndex">
                <a-tab-pane key="1" tab="全国代表">
                  <div style="overflow-y: scroll;">
                    <level-tree ref="countryTree" :userTreeData="countryTree" @checked="handleCountryChange"
                     :treeDefaultKeyProp="countryList"
                     :checkable="checkable"
                     :selectable="selectable"
                     :multiple="multiple"></level-tree>
                  </div>
                </a-tab-pane>
                <a-tab-pane key="2" tab="省代表">
                  <div style="overflow-y: scroll;">
                    <level-tree ref="provinceTree" :userTreeData="provinceTree" @checked="handleProvinceChange"
                     :treeDefaultKeyProp="provinceList"
                     :checkable="checkable"
                     :selectable="selectable"
                     :multiple="multiple"></level-tree>
                  </div>
                </a-tab-pane>
                <a-tab-pane key="3" tab="市代表">
                  <div style="overflow-y: scroll;">
                    <level-tree ref="cityTree" :userTreeData="cityTree" @checked="handleCityChange" 
                    :treeDefaultKeyProp="cityList"
                    :checkable="checkable"
                    :selectable="selectable"
                    :multiple="multiple"></level-tree>
                  </div>
                </a-tab-pane>
                <a-tab-pane key="4" tab="区代表">
                  <div style="overflow-y: scroll;">
                    <level-tree ref="administrativeAreaTree" :userTreeData="administrativeAreaTree" @checked="handleAdministrativeAreaChange"
                     :treeDefaultKeyProp="administrativeAreaList"
                     :checkable="checkable"
                     :selectable="selectable"
                     :multiple="multiple"></level-tree>
                  </div>
                </a-tab-pane>
                <a-tab-pane key="5" tab="镇代表">
                  <div style="overflow-y: scroll;">
                    <level-tree ref="streetTownTree" :userTreeData="streetTownTree" @checked="handleStreetTownChange"
                     :treeDefaultKeyProp="streetTownList"
                     :checkable="checkable"
                     :selectable="selectable"
                     :multiple="multiple"></level-tree>
                  </div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </div>
        </a-tab-pane>
        <!-- <a-tab-pane key="2" tab="用户组">
          <div class="">
            <div class="">
             
            </div>
          </div>
        </a-tab-pane> -->
        <a-tab-pane key="3" tab="常用人" :force-render="true">
          <usual-db @changeItem="handleChangeItem" :allTreeData="allTreeData"></usual-db>
        </a-tab-pane>
      </a-tabs>
      <div class="choose_list" style="width: 55%;">
        <db-table-list :dbList="checkList"></db-table-list>
      </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import levelTree from './levelTree.vue';
import dbTableList from './dbTableList.vue';
import usualDb from './usualDb.vue';
import {
  findDbOrgUserTreeByMainIdentify,
} from "@/api/levelRole/orgDbTree";
export default {
  props: {
    levelRoleMainIdentify: {
      type: String,
      default: null,
    },
    checkable: {
      type: Boolean,
      default: true,
    },
    selectable: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    levelTree,
    dbTableList,
    usualDb
  },
  data() {
    return {
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      title: "",
      dialogFormVisible: false,
      countryList: [],
      provinceList: [],
      cityList: [],
      administrativeAreaList: [],
      streetTownList: [],
      listLoading: false,
      filterText: "",
      countryTree: [],
      provinceTree: [],
      cityTree: [],
      administrativeAreaTree: [],
      streetTownTree: [],
      // 五棵树的postId集合, 用来刷新常用人的选中状态
      allTreeData: [],
      treeTabIndex: "1"
    };
  },
  created() {
    findDbOrgUserTreeByMainIdentify({mainIdentify: this.levelRoleMainIdentify}).then(res => {
      let settingFlag = false;
      // 市排的优先级最高
      this.cityTree = res.data.city
      if(!settingFlag && this.cityTree.length > 0) {
        settingFlag = true;
        this.treeTabIndex = '3'
      }
      this.countryTree = res.data.country
      if(!settingFlag && this.countryTree.length > 0) {
        settingFlag = true;
        this.treeTabIndex = '1'
      }
      this.provinceTree = res.data.province
      if(!settingFlag && this.provinceTree.length > 0) {
        settingFlag = true;
        this.treeTabIndex = '2'
      }
      this.administrativeAreaTree = res.data.administrativeArea
      if(!settingFlag && this.administrativeAreaTree.length > 0) {
        settingFlag = true;
        this.treeTabIndex = '4'
      }
      this.streetTownTree = res.data.streetTown
      if(!settingFlag && this.streetTownTree.length > 0) {
        settingFlag = true;
        this.treeTabIndex = '5'
      }
    });
  },
  mounted() {

  },
  computed: {
    checkList: function() {
      return [
        ...this.countryList,
        ...this.provinceList,
        ...this.cityList,
        ...this.administrativeAreaList,
        ...this.streetTownList
      ];
    }, 
  },
  watch: {
    checkList: {
      handler(newVal, oldVal) {
        this.allTreeData = newVal
      },
      deep: true
    }
  },
  methods: {
    async handleShow(type, checkedData) {
      this.dialogFormVisible = true;
      
      let that = this;
      that.$forceUpdate();
      if(checkedData && checkedData.length > 0) {
        let countryList = await this.dataListToIds(checkedData, "1")
        let provinceList = await this.dataListToIds(checkedData, "2")
        let cityList = await this.dataListToIds(checkedData, "3")
        let administrativeAreaList = await this.dataListToIds(checkedData, "4")
        let streetTownList = await this.dataListToIds(checkedData, "5")
        this.countryList = this.onTreeShowList(countryList);
        this.provinceList = this.onTreeShowList(provinceList);
        this.cityList = this.onTreeShowList(cityList);
        this.administrativeAreaList = this.onTreeShowList(administrativeAreaList);
        this.streetTownList = this.onTreeShowList(streetTownList);
      }
    },

    async dataListToIds(checkedData, sfDm) {
      return checkedData.filter(it => it.sfDm == sfDm)
    },
    close() {
      this.dialogFormVisible = false;
    },
    onSearch(val) {
      if(this.treeTabIndex == "1") {
        this.$refs.countryTree.onSearch(val)
      }
      if(this.treeTabIndex == "2") {
        this.$refs.provinceTree.onSearch(val)
      }
      if(this.treeTabIndex == "3") {
        this.$refs.cityTree.onSearch(val)
      }
      if(this.treeTabIndex == "4") {
        this.$refs.administrativeAreaTree.onSearch(val)
      }
      if(this.treeTabIndex == "5") {
        this.$refs.streetTownTree.onSearch(val)
      }
    },
    changtab(tab) {
      this.checkedShow = tab;
    },
    changTreeTab(tab) {
      this.treeTabIndex = tab
    },
    onChange() {

    },
    // handleChange(checked) {
    //   this.checkList = this.onTreeShowList(checked);
    // },
    handleCountryChange(checked, e) {
      this.countryList = this.onTreeShowList(checked);
    },
    handleProvinceChange(checked) {
      this.provinceList = this.onTreeShowList(checked);
    },

    handleCityChange(checked) {
      this.cityList = this.onTreeShowList(checked);
    },
    handleAdministrativeAreaChange(checked) {
      this.administrativeAreaList = this.onTreeShowList(checked);
    },
    handleStreetTownChange(checked) {
      this.streetTownList = this.onTreeShowList(checked);
    },
    onTreeShowList(data) {
      // let list = data.filter(
      //   (item) =>
      //     !item.children || (item.children && item.children.length === 0)
      // );
      let list = data
      return list.map((item) => {
        return {
          ...item,
          orgName: item.orgName,
          deptName: item.deptName,
          postName: item.postName,
          userName: item.name || item.userName,
        };
      });
    },
    confirm() {
      let checkedData = [];
      // 判断是否为常用人
      checkedData = this.checkList;
      //清空树勾选节点
      //收起所有树节点
      //回调父页面数据
      this.$emit("saveRel", "1", checkedData);
      this.close()
    },
    async handleChangeItem(checked, item) {
      if(item.sfDm == "1") {
        this.changeItem(checked, "countryList", item);
      }
      if(item.sfDm == "2") {
        this.changeItem(checked, "provinceList", item);
      }
      if(item.sfDm == "3") {
        this.changeItem(checked, "cityList", item);
      }
      if(item.sfDm == "4") {
        this.changeItem(checked, "administrativeAreaList", item);
      }
      if(item.sfDm == "5") {
        this.changeItem(checked, "streetTownList", item);
      }
    }, 
    changeItem(checked, list, item) {
      if(checked) {
        this[list].push(item)
        this[list] = this.onTreeShowList(this[list]);
      } else {
        // 取消选择
        this[list] = this[list].filter(it => it.postId !== item.postId);
        this[list] = this.onTreeShowList(this[list]);
      }
    },
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
}
</script>

<style lang="scss" scoped>
  .table_container {
    // display: flex;
    width: 100%;
    height: 100%;
    // padding-bottom: 10px;
    .table_left {
      // width: 45%;
      border: 1px solid #e8e8e8;
      padding: 10px;
      // padding-right: 0;
    }
    // .table_icon {
    //   flex: 1%;
    //   display: flex;
    //   flex-direction: column;
    //   align-items: center;
    //   font-size: 30px;
    //   justify-content: space-evenly;
    // }
    // ::v-deep .ant-spin-nested-loading,
    // .choose_list {
    //   padding: 10px;
    //   flex-shrink: 0;
    //   flex: 60%;
    // }
    // ::v-deep .ant-spin-nested-loading {
    //   width: 100%;
    //   height: 350px;
    //   overflow-y: auto;
    // }
  }
  ::v-deep .table_left .ant-tabs-nav .ant-tabs-tab {
    margin: 0px
  }
  ::v-deep .table_left .ant-tabs-bar {
    margin: 0px
  }
  .choose_list {
    border: 1px solid #e8e8e8;
    padding: 5px;
    .list_table {
    //   width: 100%;
      // height: 398px;
      // overflow: auto;
    //   .list_all {
    //     display: flex;
    //     align-items: center;
    //     border-bottom: 1px solid #e8e8e8;
    //     padding: 5px 10px;
    //     .list_left {
    //       width: 30px;
    //       height: 30px;
    //       line-height: 30px;
    //       text-align: center;
    //       border-radius: 50%;
    //       font-size: 12px;
    //       background-color: #d8293a;
    //       color: #fff;
    //       margin-right: 15px;
    //     }
    //     .list_right {
    //       color: #000;
    //     }
    //   }
    }
  }
</style>