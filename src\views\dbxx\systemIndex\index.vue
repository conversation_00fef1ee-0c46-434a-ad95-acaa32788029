<template>
  <div class="table-container">
    <ZTable :list="list"
            :columns="columns" />
  </div>
</template>

<script>
export default {
  data () {
    return {
      TBloading: false,
      list: [
        {
          index: "1",
          name: "代表门户系统",
          url: "https://192/168.3.220/committeePortal/sync",
          corn: "0 0 12 * * ? ",
          status: "有效",
          createTime: "2021.11.08",
        },
        {
          index: "1",
          name: "代表选举管理系统",
          url: "https://192/168.3.22/committeeLeader/syncAll",
          corn: "0 0 12 * * ? ",
          status: "有效",
          createTime: "2021.11.02",
        },
        {
          index: "1",
          name: "代表后台管理系统",
          url: "https://192/168.3.25/committeebase/syncBase",
          corn: "0 0 12 * * ? ",
          status: "有效",
          createTime: "2021.10.26",
        },
        {
          index: "1",
          name: "代表会议管理系统",
          url: "https://192/168.3.45/committeeMetting/syncInfo",
          corn: "0 0 12 * * ? ",
          status: "有效",
          createTime: "2021.10.24",
        },
      ],
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "状态",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "系统名称",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "name",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "订阅URL",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "url",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "同步周期",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "corn",
          customRender: (text, record, index) => {
            return record.corn;
          },
        },

        {
          title: "创建日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 180,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.switMeeting(record);
                    },
                  },
                },
                "暂停"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleEdit(record);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "系统管理");
    this.$store.dispatch("navigation/breadcrumb2", "代表信息推送");
  },
};
</script>
<style scoped></style>
