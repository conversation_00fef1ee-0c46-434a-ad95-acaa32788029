import { instance_yajy } from "@/api/axiosRq";

// 系统管理-会议管理
// 列表
export function findPageMeeting(form) {
  return instance_yajy({
    url: `/api/v1/system/meetingMgr/findPageMeeting`,
    method: "post",
    data: form,
  });
}
// 新增
export function addMeeting(form) {
  return instance_yajy({
    url: `/api/v1/system/meetingMgr/addMeeting`,
    method: "post",
    data: form,
  });
}
// 删除
export function meetingMgrRemove(meetingId) {
  return instance_yajy({
    url: `/api/v1/system/meetingMgr/remove`,
    method: "post",
    params: { meetingId },
  });
}
// 删除
export function meetingMgrUpdate(form) {
  return instance_yajy({
    url: `/api/v1/system/meetingMgr/update`,
    method: "post",
    data: form,
  });
}
