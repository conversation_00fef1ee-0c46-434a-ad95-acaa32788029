<template>
  <FormItem
    :label="label"
    :prop="prop"
    :span="span"
    :xs="xs"
    :sm="sm"
    :md="md"
    :lg="lg"
    :xl="xl"
    :xxl="xxl"
  >
    <template v-if="type == 'datetime'">
      <a-date-picker
        :value="input"
        show-time
        style="width: 100%"
        :allow-clear="allowClear"
        :disabled="disabled"
        @change="onChange"
      />
    </template>
    <template v-else-if="['date', 'year', 'month']">
      <a-date-picker
        :value="input"
        :mode="type"
        style="width: 100%"
        :open="visible"
        :format="formatPrimary"
        :allow-clear="allowClear"
        :disabled="disabled"
        @openChange="onOpenChange"
        @panelChange="onChange"
        @change="onChange"
      />
    </template>
    <template v-else>
      <a-time-picker
        :value="input"
        :format="formatPrimary"
        :value-format="valueFormatPrimary"
        :allow-clear="allowClear"
        :disabled="disabled"
        @change="onChange"
      />
    </template>
  </FormItem>
</template>

<script>
import FormItem from "@/components/FormItem/index.vue";
import moment from "moment";

/**
 * 表单选择器
 * 1.时间日期 <FormPicker v-model="" label="" allow-clear/>
 * 1.日期 <FormPicker v-model="" type="date" label="" allow-clear/>
 * 1.时间 <FormPicker v-model="" type="time" label="" allow-clear/>
 * 1.年 <FormPicker v-model="" type="year" label="" allow-clear/>
 * 1.月 <FormPicker v-model="" type="month" label="" allow-clear/>
 */
export default {
  name: "FormPicker",
  components: { FormItem },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    // <a-input v-model="" />
    value: {
      type: [Number, String],
      required: true,
      default: "",
    },
    // <a-form-model-item prop="" />
    prop: {
      type: String,
      required: false,
      default: undefined,
    },
    // <a-form-model-item label="" />
    label: {
      type: String,
      required: true,
    },
    // <a-col span="" />
    span: {
      type: [Number, String],
      default: undefined,
    },
    xs: {
      type: [Number, String],
      default: undefined,
    },
    sm: {
      type: [Number, String],
      default: undefined,
    },
    md: {
      type: [Number, String],
      default: undefined,
    },
    lg: {
      type: [Number, String],
      default: undefined,
    },
    xl: {
      type: [Number, String],
      default: undefined,
    },
    xxl: {
      type: [Number, String],
      default: undefined,
    },
    // 级联标题
    cascadeLabel: {
      type: [String, Boolean],
      required: false,
      default: false,
    },
    // <a-input :allow-clear="false" />
    allowClear: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: undefined,
    },
    // 输入框类型 datetime | date | time | year | month
    type: {
      type: String,
      default: "datetime",
    },
    format: {
      type: String,
      default: undefined,
    },
    // 值转换
    valueFormat: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      visible: false,
    };
  },
  computed: {
    input() {
      if (typeof this.value === "string" && !this.value) return undefined;
      const val = moment(this.value, this.valueFormatPrimary);
      return val.isValid() ? val : undefined;
    },
    valueFormatPrimary() {
      if (this.valueFormat) return this.valueFormat;
      switch (this.type) {
        case "date":
          return "YYYY-MM-DD";
        case "time":
          return "HH:mm:ss";
        case "year":
          return "YYYY";
        case "month":
          return "M";
        case "datetime":
        default:
          return "YYYY-MM-DD HH:mm:ss";
      }
    },
    formatPrimary() {
      if (this.format) return this.format;
      return this.valueFormatPrimary;
    },
  },
  methods: {
    onOpenChange(val) {
      this.visible = val;
    },
    onChange(val) {
      this.visible = false;
      const value =
        val == null || val == undefined
          ? undefined
          : moment(val).format(this.valueFormatPrimary);
      this.$emit("update:value", value);
      this.$emit("change", value);
    },
  },
};
</script>

<style lang="scss" scoped></style>
