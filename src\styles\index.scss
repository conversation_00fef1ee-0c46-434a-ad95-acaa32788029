
::v-deep
 .ant-popover-placement-top {
  margin-top: 40px;
  color: red !important;
  // div {
  //   span {
  //     line-height: 0 !important;
  //     height: 20px !important;
  //   }
  // }

}
.table-container{
  border-radius: 6px;
  padding: 30px 20px 20px 20px;
  background-color: #ffffff;
}
// 表格操作的样式
.operationStyle{
  span{
    cursor:pointer;
    margin: 0 8px;
    color: #db3046;
  }
  .dropdown-link{
    color: #db3046;
  }
}
.ant-dropdown-menu-item:hover{
  background-color: #fbeeef  !important;
}
/*  设置ant-collapse颜色 */
// .ant-collapse-icon-position-right > .ant-collapse-item > .ant-collapse-header {
//   padding: 4px 16px;
//   padding-right: 40px;
// }
// .ant-collapse > .ant-collapse-item > .ant-collapse-header {
//   line-height: 45px
// }
// .ant-collapse {
//   border: 0;
//   .ant-collapse-header {
//     pointer-events: none;
//   }
//   i[aria-label="图标: right"] {
//     display: none !important;
//   }
// }
// .ant-collapse-item {
//   background: #fff !important;
// }
// .ant-collapse > .ant-collapse-item {
//   border-bottom: 0;
// }

// 设置粉红色警告bottom按钮颜色变成白色
.ant-btn-danger {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  border-color: #d9d9d9;
}
// 弹出框title文字
.el-dialog__header {
  text-align: center;
  padding: 15px  20px;
}
.el-dialog__title {
  font-size: 16px;
  font-weight: 900;
}
// 设置弹出框内容距离title距离
.el-dialog__body {
  padding: 15px 20px;
}

// 设置所有首页的样式
.a-card-flex {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
}
.a-card-flex img {
  margin-top: 20px;
}

// 代表双联列表 table 行高
.ant-table-thead > tr > th, .ant-table-tbody > tr > td {
  padding: 13px 16px !important;
}
// 表头颜色
.ant-table-thead > tr > th  {
  background: #F4F4F4;
  background-color: #F4F4F4 !important;
}
// table 行高50px
// .ant-table {
//   line-height: 12px;
// }

// 全局修改了table不带竖线 批量恢复的话
// 全局搜索<a-table
            // :bordered="false"改为<a-table
            // bordered
// 但是要排除 两个文件
// src\views\dbxx\elected\statisticsReport.vue
// src\views\dbxx\statementDta\statementReport.vue
// D:\newilliz\src\styles\element-variables.scss
// D:\newilliz\src\styles\index.scss


// 弹出框表单颜色以及文字
.ant-modal-content {
  .ant-form-item label {
    font-family: pingFang-R;
    color: #666666;
  }
}
// 按钮位置居中
.ant-modal-footer {
  text-align: center;
}
.ant-menu-inline .ant-menu-item::after {
  border: 0px;
}

// 设置table显示全部文字  ellipsis: false, 变成 ellipsis: true,


// 输入框的上下间距改为12px
.ant-form-item {
  margin-bottom: 12px
}
.ant-input[disabled]{
  color: rgba(0, 0, 0, 0.65)  !important;
 }
