<template>
    <div style="padding: 20px">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="时间"
                               prop="serverTime">
              <a-date-picker style="width: 100%;"
                             v-model="form.achieveDate"
                             placeholder="选择日期时间">
              </a-date-picker>
            </a-form-model-item>
        <a-form-model-item label="内容" prop="content">
          <a-textarea
            :autoSize="{ minRows: 6, maxRows: 6 }"
            v-model="form.content"
          />
        </a-form-model-item>
        <a-form-model-item :wrapper-col="{ span: 18, offset: 3}">
          <a-button type="primary" @click="onSubmit"> 保  存 </a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
  </template>
  
  <script>
  import { createAchievement } from "@/api/electronicArchives.js";
  import invitedTree from "@/views/common/representative/invitedTree.vue";
  export default {
    components: {invitedTree},
    data() {
      return {
        recordId: "",
        //行政区域列表
        loading: false,
        labelCol: { span: 3 },
        wrapperCol: { span: 14 },
        form: {
        },
        inviteRangeDesc: "",
        defaultSelect: [], //同行代表
        showDisabled: false,
        rules: {
          achieveDate: [
            {
              required: true,
              message: "请选择日期",
              trigger: "blur",
            },
          ],
          content: [
            {
              required: true,
              message: "请输入内容",
              trigger: "blur",
            },
          ],
        },
      };
    },
    mounted() {
    },
    methods: {
      onSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            createAchievement(this.form).then(res => {
              if(res.code == '0000') {
                this.$message.success("新增成功");
                this.$router.push({
                  path: "/electronicArchives/achievement",
                });
              } else {
                this.$message.error("新增失败");
              }
            })
          } else {
            this.$message.error("请完善资料");
            return false;
          }
        });
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .avatar-uploader > .ant-upload {
    width: 128px;
    height: 128px;
  }
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }
  
  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .searchStyle {
  display: flex !important;
  .ant-input {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  .ant-btn-primary {
    width: 66px;
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
  </style>
  