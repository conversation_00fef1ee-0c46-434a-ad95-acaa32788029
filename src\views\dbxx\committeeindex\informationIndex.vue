<template>
  <div class="table-container">
    <div>
      <div class="jbxx-contt">
        <div class="jbqkss"><span class="jbqkbz">快捷按钮</span></div>
        <div style="padding: 18px; 0">
          <a-row>
            <a-col v-for="(item, index) in ToDoList"
                   :key="index"
                   :span="6"
                   :offset="1"
                   @click="clickIndex(item)">
              <div class="jbxxson_Big">
                <img :src="item.img"
                     :alt="item.name" />
                <div class="jbxxson_Big_box">
                  <div class="tits">
                    {{ item.name }}
                  </div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
      <div class="jbxx-contt"
           style="padding-bottom: 20px;">
        <div class="jbqkss"><span class="jbqkbz">快捷按钮</span></div>
        <div>
          <a-row>
            <a-col v-for="(item, index) in DataqueryList"
                   :key="index"
                   :span="4"
                   @click="clickIndex(item)">
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.img"
                     :alt="item.name" />
                <div class="jbxxson_box">
                  <div class="tits"
                       style="">{{ item.name }}</div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
                <div v-if="item.size"
                     class="sizeData">{{ item.size }}</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
      <div class="jbxx-contt"
           style="padding-bottom: 20px;">
        <div class="jbqkss"><span class="jbqkbz">统计分析</span></div>
        <div>
          <a-row>
            <a-col v-for="(item, index) in OtheServicesList"
                   :key="index"
                   :span="4"
                   @click="clickIndex(item)">
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.img"
                     :alt="item.name" />
                <div class="jbxxson_box">
                  <div class="tits"
                       style="">{{ item.name }}</div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getLevelList } from "@/api/registrationMage/tableIng.js";
import checkPermission from "@/utils/permission";
export default {
  components: {},
  data () {
    return {
      TBloading: false,
      // ===)代表信息管理工作人员首页~
      // 待办事项
      ToDoList: [
        {
          name: "我的待办",
          remarks: "",
          num: 0,
          img: require("@/assets/images/indexImage/代表信息管理/我的待办.png"),
          system: false,
          path: "/delegate/backlog",
        },
        {
          name: "我的已办",
          remarks: "",
          num: 0,
          img: require("@/assets/images/indexImage/代表信息管理/我的已办.png"),
          system: false,
          path: "/delegate/Handling",
        },
        {
          name: "代表信息修改",
          // remarks: "工作人员<br/>修改代表信息",
          img: require("@/assets/images/indexImage/代表信息管理/代表信息修改.png"),
          system: false,
          path: "/delegate/manage",
          size: null,
        },
      ],
      // 数据查询
      DataqueryList: [
        {
          name: "手动新增代表",
          remarks: "工作人员手动协助新增代表",
          img: require("@/assets/images/indexImage/代表信息管理/手动新增代表.png"),
          system: false,
          path: "/delegate/manage",
          size: null,
        },
        {
          name: "批量导入代表",
          remarks: "工作人员协助批量导入代表信息",
          img: require("@/assets/images/indexImage/代表信息管理/批量导入代表.png"),
          system: false,
          path: "/addDlegate/batchCommittee", //跳转平台门户
          size: null,
          systemCode: "lzptmh",
        },
        {
          name: "代表调入",
          remarks: "工作人员<br/>选择代表调入",
          img: require("@/assets/images/indexImage/代表信息管理/代表调入.png"),
          system: false,
          path: "/adjust/in",
          size: null,
        },
        {
          name: "代表调出",
          remarks: "工作人员<br/>选择代表调出",
          img: require("@/assets/images/indexImage/代表信息管理/代表调出.png"),
          system: false,
          path: "/adjust/out",
          size: null,
        },
        {
          name: "调整代表团",
          remarks: "工作人员协助调整代表所在代表团",
          img: require("@/assets/images/indexImage/代表信息管理/调整代表团.png"),
          system: false,
          path: "/adjust/adjust",
          size: null,
        },
      ],
      OtheServicesList: [
        {
          name: "代表信息查询",
          remarks: "查询代表信息",
          img: require("@/assets/images/indexImage/代表信息管理/代表信息查询.png"),
          path: "/representDta/infoQuery",
          size: null,
        },
        {
          name: "分析报表",
          remarks: "代表个人信息分析报表",
          img: require("@/assets/images/indexImage/代表信息管理/分析报表.png"),

          size: null,
          system: false,
          path: "/delegate/statisticsData",
        },
      ],
      isDb: null,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      form: {
        jcDm: null,
        activityName: null,
        startTime: null,
        endTime: null,
      },
    };
  },

  created () {
    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));
  },
  methods: {
    checkPermission,
    // 获取节次
    getLevelListData () {
      getLevelList({}).then((res) => {
        if (res.data.code == "0000") {
          this.form.jcDm = res.data.data[0].jcDm;
          this.getNotRead();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    async getNotRead () {
      //  未办统计数
      // let res = await backlogValue(this.queryForm, this.form);
      // if (res.code == 200) {
      //   this.ToDoList[0].num = res.total; //待办总数
      //   this.$forceUpdate();
      // }
      // this.getRead();
    },
    async getRead () {
      //  已办统计数
      // let res = await backlogHandle(this.queryForm, this.form);
      // if (res.code == 200) {
      //   this.ToDoList[1].num = res.total; //已办总数
      //   this.$forceUpdate();
      // }
    },
    clickIndex (event) {
      // 跨子系统跳转
      if (event.system) {
        let locationSystem = {
          path: this.$router.history.current.path,
          systemCode: this.$router.history.current.meta.systemId,
        };

        sessionStorage.setItem(
          "locationSystem",
          JSON.stringify(locationSystem)
        );
        this.$nextTick(() => {
          window.top.postMessage(
            {
              event: "locationSystem", // 约定的消息事件
              args: {
                path: event.path, //路径
                systemCode: event.systemCode, //子系统编码
              }, // 参数
            },
            "*"
          );
        });
      }
      //其他情况
      else if (event.name == "工作动态") {
        window.open("https://www.rd.gz.cn/");
      }
      // 本子系统路由跳转
      else {
        var isDb = event.type ? true : null;

        this.$router.push({ path: event.path, query: { isDb: isDb } });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 0px !important;
  background-color: rgb(246, 246, 246) !important;
}

.tits {
  @include add-size($font_size_16);
  color: rgba(56, 56, 56, 1);
  font-weight: 700;
  margin-top: 15px;
  cursor: pointer;
}

.wddb1 {
  float: left;
  @include add-size($font_size_16);
  color: black;
  font-weight: 700;
}

.jbxxson_Big {
  cursor: pointer;
  display: flex;
  align-content: center;
  flex-direction: row;
  padding: 10px;
  margin: 5% 10px;
  height: 20%;
  border-radius: 10px;
  align-items: center;
  justify-content: center;

  img {
    width: 18%;
    margin-right: 12%;
  }

  .jbxxson_Big_box {
    .Numbers {
      text-align: center;
      color: #cc3131;
      // font-size: 24px;
      // font-weight: 600;
    }

    .tits {
      font-size: 18px !important;
      font-weight: 400;
      letter-spacing: 0px;
      color: rgba(56, 56, 56, 1);
      text-align: left;
      vertical-align: top;
      margin: 0 auto;
      width: 100%;
      text-align: center;
      font-weight: 500; //小标题加粗
    }

    .remarks {
      width: 100%;
      text-align: center;
      margin: 1% auto;
    }

    color: rgba(153, 153, 153, 1);
  }
}

.jbxxson {
  cursor: pointer;
  box-shadow: 1.03px 1.85px 4px 0px rgb(166 166 166 / 51%);
  display: flex;
  align-content: center;
  flex-direction: column;
  padding: 10px;
  margin: 8% 6%;
  height: 20%;
  justify-content: flex-start;

  img {
    width: 38px;
    height: 38px;
    margin: 0% auto;
    margin-top: 10px;
  }

  .jbxxson_box {
    .tits {
      margin: 0 auto;
      width: 100%;
      text-align: center;
      color: rgba(56, 56, 56, 1);
      margin: 10px auto;
      font-weight: 600; //盒子标题
    }

    .remarks {
      width: 100%;
      text-align: center;
      margin: 1% auto;
    }

    color: rgba(153, 153, 153, 1);
  }

  .sizeData {
    position: absolute;
    right: 0%;
    color: #f4efef;
    font-family: pingFang-M;
    top: -5px;
    height: 37px;
    width: 37px;
    line-height: 37px;
    border-radius: 30px;
    background-color: #d52838;
    text-align: center;
  }
}

.a1 {
  color: #999999;
}

.jbxx-contt {
  background-color: #fff;
  width: 100%;
  border: 5px solid #f6f6f6;
  border-radius: 10px;
}

.rightCyygn {
  width: 20%;
  margin-right: 50px;
}

.cygn1 {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.imgss1 {
  width: 27%;
  margin-left: 20px;
  text-align: center;
  margin-bottom: 25px;

  div {
    height: 85px;
  }

  img {
    width: 80%;
    height: 80%;
  }
}

::v-deep {
  .ant-badge-status-dot {
    width: 10px !important;
    height: 10px !important;
  }
}

.xxssdtcont {
  display: flex;
  width: 100%;
}

.xxssdtLeft {
  width: 30%;
  color: #999999;
}

.dcl12 {
  margin-left: 15px;
  @include add-size($font_size_16);
  color: #f50;
}

.xxssdtRight {
  width: 65%;
}

.slr12 {
  margin-top: 5px;
  margin-left: 20px;
  color: #999999;
}

.slr15 {
  margin-top: 5px;
  margin-left: 20px;
}

.jbqkbz {
  padding-left: 18px;
  border-left: 4px solid rgba(199, 28, 51, 1);
  @include add-size($font_size_16);
  font-weight: 700;
}

.sjcx_son {
  display: flex;
  flex-wrap: wrap;
  margin-left: 35px;
}

.fengexian {
  width: 100%;
  height: 1px;
  background: rgba(237, 237, 237, 1);
  margin: 25px 10px 20px 10px;
}

.jbqkss {
  padding: 10px 1%;
}

.cardHvover:hover {
  outline: 1px solid #d43030;
}
.Heightjbxxson {
  min-height: 160px;
}
// 备注有俩行给固定高度
@media screen and (max-width: 1920px) {
  .Heightjbxxson {
    min-height: 160px;
  }
}

@media screen and (max-width: 1600px) {
  .Heightjbxxson {
    min-height: 160px;
  }
}

@media screen and (max-width: 1280px) {
  .Heightjbxxson {
    min-height: 180px;
  }
}
</style>
