import { instance_yajy } from "@/api/axiosRq";
// 获取本届代表信息维护
export function getDeputyListPageY(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/deputy/getDeputyListPage/Y`,
    method: "post",
    data: formData,
  });
}
// 获取历届代表信息维护
export function getDeputyListPageN(formData) {
  return instance_yajy({
    url: `/api/v1/proposal/deputy/getDeputyListPage/N`,
    method: "post",
    data: formData,
  });
}
