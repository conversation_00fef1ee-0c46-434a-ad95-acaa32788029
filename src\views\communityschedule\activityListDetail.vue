<template>
  <div class="table-container">
    <a-spin :spinning="loading">
      <CommunityActivitySchedule
        v-model="form"
        :current="3"
        :title="title"
        :oper="oper"
      >
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row :gutter="50" type="flex">
            <AdminStreetStationCascade
              v-model="form"
              :disabled="isDisabled"
              @liaisonStationSelect="onLiaisonStationSelect"
            />

            <FormInput
              v-model="form.contactName"
              label="联系人名称"
              prop="contactName"
              :disabled="isDisabled"
              :rules="rules"
            />

            <FormInput
              v-model="form.contactPhoneNumber"
              label="联系人电话"
              prop="contactPhoneNumber"
              :disabled="isDisabled"
              :rules="rules"
            />

            <FormPicker
              v-model="form.serverTime"
              label="活动时间"
              prop="serverTime"
              :disabled="isDisabled"
              type="date"
            />
            <FormInput
              v-model="form.peopleNum"
              label="接待群众人数"
              prop="peopleNum"
              :disabled="isDisabled"
              :rules="rules"
            />

            <FormItem label="受邀代表" prop="attendMembers" :span="24">
                  <a-button
                    :disabled="isDisabled"
                    type="primary"
                    icon="plus"
                    @click="onAttendMembers"
                    style="margin-bottom: 5px"
                  ></a-button>
                  <MultiLineText :values="form.attendMembers" />
            </FormItem>

            <FormInput
              v-model="form.mark"
              label="活动内容"
              prop="mark"
              :span="24"
              type="textarea"
              :rows="5"
              :disabled="isDisabled"
              :rules="rules"
            />

            <FormInput
              v-model="form.content"
              label="群众意见"
              prop="content"
              :span="24"
              type="textarea"
              :rows="5"
              :disabled="isDisabled"
              placeholder="请输入群众反映的意见建议或者问题"
              :rules="rules"
            />
          </a-row>
        </a-form-model>

        <template slot="actions">
          <a-button @click="onBack">取 消</a-button>
          <template v-if="oper == 'update'">
            <a-button type="primary" @click="onSubmit(false)"> 更 新 </a-button>
          </template>
          <template v-else-if="oper == 'create'">
            <a-button type="primary" @click="onSubmit(false)"
              >暂存为草稿</a-button
            >
            <a-button type="primary" @click="onSubmit(true)"
              >保存并送审</a-button
            >
          </template>
          <template v-else-if="oper == 'audit'">
            <a-button type="primary" @click="onAudit">审 核</a-button>
          </template>

          <a-button v-if="oper != 'create'" @click="onTurnover"
            >流程流转记录</a-button
          >
        </template>
      </CommunityActivitySchedule>
    </a-spin>

    <org-db-tree
      ref="identityRelAny"
      default-key="1"
      :levelRoleMainIdentify="levelRoleMainIdentify"
      @saveRel="onAttendMembersSubmit"
    ></org-db-tree>
    <!-- 流程弹出页 -->
    <submitForCensorship
      ref="submitForCensorship"
      :proc-inst-id="form.procInstId"
      :ids="[form.activityId]"
      @complete="onAuditSubmit"
    />
    <!-- 流程流转记录 -->
    <lczzjl ref="lczzjl" :proc-inst-id="form.procInstId"></lczzjl>
  </div>
</template>

<script>
import CommunityActivitySchedule from "@/components/CommunityActivitySchedule/index.vue";
import identityRelAnyWithDb from "@/views/common/identityRelAnyWithDb.vue";
import OrgDbTree from "@/components/OrgDbTree/index.vue";
import {
  save,
  update,
  getById,
  checkIsExistLiaisonStationIdAndUserIds,
} from "@/api/communityscheduleactivity";
import { phoneValidator, showNumberValidator } from "@/utils/validator";
import submitForCensorship from "@/views/common/submitForCensorship.vue";
import lczzjl from "@/views/common/lczzjl.vue";
import { communityScheduleComplete } from "@/api/xjgw";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import FormItem from "@/components/FormItem/index.vue";
import FormPicker from "@/components/FormPicker/index.vue";
import MultiLineText from '@/components/MultiLineText'
import { DBLZ_DBJSQ } from '@/utils/enum/levelRoleMainIdentifyEnum'; // 导入配置文件
export default {
  name: "ActivityListDetail",
  components: {
    FormPicker,
    FormItem,
    FormInput,
    AdminStreetStationCascade,
    lczzjl,
    submitForCensorship,
    CommunityActivitySchedule,
    identityRelAnyWithDb,
    OrgDbTree,
    MultiLineText
  },
  data() {
    return {
      title: "进社区活动登记",
      levelRoleMainIdentify : DBLZ_DBJSQ,
      loading: false,
      form: {
        id: undefined,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        contactName: undefined,
        contactPhoneNumber: undefined,
        serverTime: undefined,
        attendMembers: [],
        peopleNum: undefined,
        mark: undefined,
        content: undefined,
      },
      // message 为空则不展示提示语
      rules: {
        administrativeAreaId: { required: true, message: "请选择行政区划" },
        streetTownId: { required: true, message: "请选择街道乡镇" },
        liaisonStationId: { required: true, message: "请选择联络站" },
        contactName: { required: true, message: "请输入联系人名称" },
        contactPhoneNumber: {
          required: true,
          validator: phoneValidator("联系人电话"),
        },
        serverTime: { required: true, message: "请选择活动时间" },
        peopleNum: {
          required: true,
          validator: showNumberValidator("接待群众人数"),
        },
        attendMembers: { required: true, message: "请选择受邀代表" },
        mark: { required: true, message: "请输入活动内容" },
        content: { required: true, message: "请输入群众意见" },
      },
      // 检查行程
      checkJourney: "",
    };
  },
  computed: {
    oper() {
      const oper = this.$route.query.oper;
      const id = this.$route.query.id;
      if (!id) return "create";
      return oper || "update";
    },
    isDisabled() {
      return ["audit", "view"].includes(this.oper);
    },
    // attendMembers() {
    //   if (!this.form.attendMembers) return "";
    //   return this.form.attendMembers.map((it) => it.name || it.userName).join();
    // },
  },
  created() {
    this.title = this.$route.query.title || "进社区活动登记";

    this.load();
  },
  methods: {
    load() {
      const id = this.$route.query.id;
      if (!id) {
        const data = sessionStorage.getItem("ActivitySchedule");
        if (data) {
          Object.assign(this.form, JSON.parse(data));
          sessionStorage.removeItem("ActivitySchedule");
        }
        return;
      }
      getById({ id }).then((res) => {
        this.form = res.data;
        this.checkJourney = JSON.stringify({
          serverTime: this.form.serverTime,
          userIds: this.form.attendMembers.map((it) => it.userId),
        });
      });
    },
    onBack() {
      this.$router.back();
    },
    checkIsExistLiaisonStationIdAndUserIds() {
      return new Promise((resolve, reject) => {
        const params = {
          serverTime: this.form.serverTime,
          userIds: this.form.attendMembers.map((it) => it.userId),
        };

        // 行程未变更
        if (this.checkJourney == JSON.stringify(params)) return resolve();

        checkIsExistLiaisonStationIdAndUserIds(params)
          .then((res) => {
            if (res.data) {
              this.$confirm({
                cancelText: "取消",
                okType: "danger",
                okText: "确定，继续录入",
                title: "温馨提示",
                content: "该代表该日已录入活动，请确认是否重复？",
                onOk: () => {
                  resolve();
                },
                onCancel: () => {
                  this.$message.info("您已取消操作！");
                  reject();
                },
              });
            } else {
              resolve();
            }
          })
          .catch(reject);
      });
    },
    onSubmit(isAudit) {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return this.$message.warning("请完善必填项信息");

        this.loading = true;
        try {
          await this.checkIsExistLiaisonStationIdAndUserIds();
        } catch (e) {
          this.loading = false;
          return;
        }

        const api = this.form.id ? update : save;
        const res = await api({
          ...this.form,
          peopleNum: Number(this.form.peopleNum),
        });
        this.$message.success("保存成功");
        this.loading = false;
        if (isAudit && res.data) {
          // 获取流程id
          getById({ id: res.data }).then((res) => {
            this.form = res.data;
            this.onAudit();
          });
        } else {
          this.onBack();
        }
      });
    },
    onAudit() {
      this.$refs.submitForCensorship.visible = true;
    },
    onAuditSubmit(data) {
      communityScheduleComplete(data).then((res) => {
        if (res.data.code == "0000") {
          this.$refs.submitForCensorship.successComplete();

          this.onBack();

          this.$message.success(res.data.msg);
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    onTurnover() {
      this.$refs.lczzjl.xuanze(this.form.procInstId);
    },
    onAttendMembers() {
      this.$refs["identityRelAny"] &&
        this.$refs["identityRelAny"].handleShow(
          "1",
          this.form.attendMembers
        );
    },
    onAttendMembersSubmit(type, checkedData) {
      //用户树选择赋值
      if (type == "1") {
        const list = checkedData
          // .filter((it) => it.itemType == "2")
          .map((it) => ({
            userId: it.id || it.userId,
            inviteId: it.uniqueId,
            dhDm: it.dhDm,
            jcDm: it.jcDm,
            orgId: it.orgId,
            deptId: it.deptId,
            postId: it.postId,
            orgName: it.orgName,
            deptName: it.deptName,
            postName: it.postName,
            name: it.name,
            userName: it.userName,
            sfDm: it.sfDm,
            committeeMemberTypeId: it.sfDm,
          }));
        this.$set(this.form, "attendMembers", list);
        console.log("this.form.attendMembers:",this.form.attendMembers);
      } else if (type == "2") {
        // todo 旧代码未处理内容
        let relName = checkedData.map((it) => it.name).join();
        let userGroupId = checkedData.map((it) => it.id);
      }
    },
    onLiaisonStationSelect(it) {
      this.form.contactPhoneNumber = it.contactPhoneNumber || "";
      this.form.contactName = it.contactName || "";
    },
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  padding-top: 10px;
}
</style>
