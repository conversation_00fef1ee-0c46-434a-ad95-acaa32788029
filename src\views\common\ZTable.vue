<template>
  <div class="ZTable">
    <div :class="advanced ? 'search' : null">
      <a-form :model="queryForm"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              layout="horizontal">
        <div>
          <a-row>
            <a-col :md="6"
                   :sm="24">
              <a-form-item :label="inputTitle"
                           :labelCol="{ span: 6 }"
                           :wrapperCol="{ span: 18 }">
                <!-- 输入框 -->
                <a-input style="width: 100%;"
                         v-model="queryForm.title" />
              </a-form-item>
            </a-col>
            <a-col :md="6"
                   :sm="24">
              <a-form-item v-if="isShowSelect"
                           :label="selectTitle">
                <!-- 下拉框 -->
                <a-select v-model="queryForm.meetingType"
                          filterable
                          default-first-option>
                  <a-select-option v-for="item in typeOptions"
                                   :key="item.label"
                                   :label="item.label"
                                   :value="item.label">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6"
                   :sm="24">
              <a-form-model ref="drawerForm"
                            :model="queryForm"
                            :labelCol="{ span: 6 }"
                            :wrapperCol="{ span: 18, offset: 0 }">
                <a-form-model-item label="创建时间">
                  <a-range-picker value-format="YYYY-MM-DD HH:mm:ss"
                                  show-time
                                  class="creationTime" />
                </a-form-model-item>
              </a-form-model>
            </a-col>
            <a-col :span="6">
              <span style="float: right; margin-top: 3px;">
                <a-button type="primary"
                          style="margin-left: 12px;">搜索</a-button>
                <a-button style="margin-left: 12px;"
                          class="pinkBoutton">重置</a-button>
              </span>
            </a-col>
            <!-- <a-col :md="6"
                   :sm="24">
              <span style="float: right; margin-top: 3px;">
                <a @click="toggleAdvanced"
                   style="margin-right: 8px;">
                  {{ advanced ? "收起" : "高级搜索" }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
              
              </span>
            </a-col> -->
          </a-row>

          <!-- 更多查询条件抽屉 -->
          <!-- <a-row v-if="advanced">
         
          </a-row> -->
        </div>
      </a-form>
    </div>
    <div>
      <a-space class="operator"
               v-show="addButton">
        <a-button type="primary"
                  style="margin-bottom: 20px;"
                  icon="plus-circle">新建内容</a-button>
      </a-space>
    </div>

    <a-table class="directorySet-table"
             ref="table"
             :columns="columns"
             :pagination="pagination"
             :row-selection="rowSelection"
             :data-source="list"
             :rowKey="
        (record, index) => {
          return index;
        }
      "
             :scroll="{ x: 300, y: 0 }">
    </a-table>
  </div>
</template>

<script>
export default {
  name: "ZTable",
  props: {
    list: Array,
    columns: Array,
    // 是否显示分页器 默认不显示
    pagination: {
      type: [Object, Boolean],
      // default: true,
      default: () => {
        return {
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0,
          showTotal: (total) => `总共 ${total} 条`,
        };
      },
    },
    // 是否显示清空栏 默认显示
    empty: {
      type: Boolean,
      default: true,
    },
    // 是否显示选择框 默认显示
    ischoiceBox: {
      type: Boolean,
      default: true,
    },
    // 设置输入搜索框标题
    inputTitle: {
      type: String,
      default: "选择标题",
    },
    // 设置输入下拉框标题
    selectTitle: {
      type: String,
      default: "选择类型",
    },
    // 下拉框组件是否显示 默认显示
    isShowSelect: {
      type: Boolean,
      default: true,
    },
    // 是否显示新增按钮 默认显示
    addButton: {
      type: Boolean,
      default: true,
    },
  },
  data () {
    return {
      TBloading: false,
      advanced: false,
      stateOptions: [
        { label: "开始", value: "0" },
        { label: "正常会议", value: "1" },
        { label: "归档会议", value: "2" },
      ],
      typeOptions: [
        { label: "常委会会议", value: "1" },
        { label: "人民代表大会", value: "2" },
        { label: "主任会议", value: "3" },
        { label: "其他会议", value: "4" },
      ],
      queryForm: {
        createTime: "",
        endTime: "",
        meetingState: "",
        meetingType: "",
        pageSize: 10,
        startIndex: 1,
        title: "",
      },
      selectedRowKeys: [], // 选择项key
      selectedRows: [],
    };
  },
  created () {
    console.log("🤗🤗🤗, columns =>", this.columns);
    let keys = this.columns.map((item) => item.title);
    console.log("🤗🤗🤗, keys =>", keys);
    if (!keys.includes("无操作")) {
      if (!keys.includes("操作")) {
        this.columns.push({
          fixed: 'right',
          title: "操作",
          align: "center",
          width: "200px",
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.switMeeting(record);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleEdit(record);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        });
      }
    } else {
      //去掉无操作
      this.columns.pop();
    }
    this.needTotalList = this.initTotalList(this.columns);
  },
  computed: {
    // 判断是否显示选择框
    rowSelection () {
      if (this.ischoiceBox) {
        return {
          onSelect: this.selectDutys,
          onSelectAll: this.onSelectAll,
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange,
        };
      }
      return null;
    },
  },
  methods: {
    updateSelect (selectedRowKeys, selectedRows) {
      this.$emit("update:selectedRows", selectedRows);
      this.$emit("selectedRowChange", selectedRowKeys, selectedRows);
    },
    initTotalList (columns) {
      const totalList = columns
        .filter((item) => item.needTotal)
        .map((item) => {
          return {
            ...item,
            total: 0,
          };
        });
      return totalList;
    },
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    handleClear () {
      this.queryForm = {
        createTime: "",
        endTime: "",
        meetingState: "",
        meetingType: "",
        pageSize: 10,
        startIndex: 1,
        title: "",
      };
      this.handleQuery();
    },
    // 选择当个表格时
    selectDutys (item, selected, items) {
      this.selectedRows = items;
      // 批量删除
      this.$emit("deleteBatches", items);
    },
    // 选择全部表单时触发
    onSelectAll (selected, items) {
      console.log(selected);
      this.selectedRows = items;
      // 批量删除
      this.$emit("deleteBatches", items);
    },
    // 选择项变化时触发 获取已选择项Key
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    // 清空所选表单
    onClear () {
      this.selectedRows = [];
      this.selectedRowKeys = [];
    },
  },
  watch: {
    selectedRows (selectedRows) {
      this.needTotalList = this.needTotalList.map((item) => {
        return {
          ...item,
          total: selectedRows.reduce((sum, val) => {
            let v;
            try {
              v = val[item.dataIndex]
                ? val[item.dataIndex]
                : eval(`val.${item.dataIndex}`);
            } catch (_) {
              v = val[item.dataIndex];
            }
            v = !isNaN(parseFloat(v)) ? parseFloat(v) : 0;
            return sum + v;
          }, 0),
        };
      });
    },
  },
};
</script>

<style>
.ZTable {
  padding: 15px;
}
.alert {
  margin-bottom: 16px;
}
.clear {
  float: right;
}
.query {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
}
/* .message {
    a {
      font-weight: 600;
    }
  } */
</style>
