<template>
  <div class="table_content">
    <div style="flex: 8;">
      <div v-if="currentTab === 0"><AddAcitveList /></div>
    </div>
    <!-- 时间线 -->
    <!-- <TimeLine :lineList="lineList" :tab.sync="currentTab" /> -->
  </div>
</template>

<script>
import TimeLine from '@/components/TimeLine/index';
import AddAcitveList from './addAcitveList.vue';
export default {
  name: "newInfo",
  components: {
    TimeLine,
    AddAcitveList
  },
  data() {
    return {
      currentTab: 0,
      lineList: [
        {
          content: '年度活动计划安排',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
          // status: 'finished',
        }, {
          content: '进社区公告发布',
          timestamp: '2018-04-11',
          status: 'unPass',
          type: '审核通过',
          color: '#0bbd87',
          remark: '备注：审核通过'
        }, {
          content: '进社区活动报名',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '进社区活动签到',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '进社区活动登记',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '录入社情民意表',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '社情民意收集归档',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '进社区活动归档',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }
      ],
      // tab: 4,
    }
  },
  created() {
    let { tabIndex } = this.$route.query;
    this.currentTab = Number(tabIndex);
    console.log(this.currentTab,'this.currentTabthis.currentTab');
  },
  methods: {
  }
}
</script>

<style>
.table_content {
  display: flex;
}
</style>
