import request from "@/utils/request";
import qs from "qs";

export function getListByType(param) {
  return request({
    url: "/api/v1/range/getListByType",
    method: "post",
    param,
  });
}

export function getListByTypes(data) {
  return request({
    url: "/api/v1/range/getListByTypes",
    method: "post",
    data,
  });
}

export function getList(data) {
  return request({
    url: "/api/v1/range/getRangeList",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/range/addRange",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/range/updateRange",
    method: "post",
    data,
  });
}

export function doDelete(param) {
  return request({
    url: "/api/v1/range/delRange/" + param,
    method: "post",
  });
}
