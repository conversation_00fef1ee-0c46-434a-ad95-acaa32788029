import request from "@/utils/requestTemp";

// 获取登录分析统计数据
export function getLoginAnalysisStats(params) {
  return request({
    url: '/loginLog/analysis/stats',
    method: 'get',
    params
  })
}

// 获取高频登录子系统统计
export function getAppIdStats(params) {
  return request({
    url: '/loginLog/analysis/appId',
    method: 'get',
    params
  })
}

// 获取高频登录时间段统计
export function getTimeRangeStats(params) {
  return request({
    url: '/loginLog/analysis/timeRange',
    method: 'get',
    params
  })
}

// 获取高频登录用户类型统计
export function getUserTypeStats(params) {
  return request({
    url: '/loginLog/analysis/userType',
    method: 'get',
    params
  })
}

// 获取登录趋势统计（按天）
export function getLoginTrendStats(params) {
  return request({
    url: '/loginLog/analysis/trend',
    method: 'get',
    params
  })
}

// 获取登录日志详情列表（分页）
export function getLoginLogInfoList(params, queryParams) {
  return request({
    url: '/loginLog/analysis/logList',
    method: 'get',
    params: {
      ...params,
      ...queryParams
    }
  })
}
