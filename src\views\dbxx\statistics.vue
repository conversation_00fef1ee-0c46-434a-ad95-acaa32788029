<template>
  <div class="statistics-box">
    <div class="statistics-top">
      <a-row :gutter="15">
        <a-col style="margin-bottom: 15px;">
          <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>代表信息管理</el-breadcrumb-item>
            <el-breadcrumb-item>统计报表</el-breadcrumb-item>
          </el-breadcrumb>
        </a-col>
      </a-row>
    </div>

    <div class="details">
      <div
        class="ds-item"
        @click="viewDetail"
        v-for="(item, index) in dataValue"
        :key="index"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  filters: {},
  data() {
    return {
      dataValue: [
        { name: "基本情况统计" },
        { name: "代表变更代表团情况统计" },
        { name: "名册统计" },
        { name: "代表综合构成统计" },
      ],
    };
  },
  components: {},

  mounted() {},
  methods: {
    viewDetail() {
      this.$router.push({
        path: "dbxxtable",
        query: {
          // communityScheduleId: this.communityScheduleId,
          // oper: 'add'
        },
      });
    },
  },
};
</script>
<style scoped>
.statistics-box {
  width: 100%;
  height: 100%;
}
.statistics-top {
  padding: 15px 0px 15px 15px;
}
.details {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 30px;
  padding-top: 0px;
}
.ds-item {
  width: 32%;
  height: 50px;
  border-radius: 10px;
  margin-bottom: 20px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
  background-color: #f0f7ff;
  color: #255c7a;
}
</style>
