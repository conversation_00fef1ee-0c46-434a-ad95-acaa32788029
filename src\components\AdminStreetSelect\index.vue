<template>
  <div>
    <a-col v-if="showInfo && showInfo.includes('1')" :span="spanNum">
      <a-form-model-item
        label="行政区县"
        prop="administrativeAreaId"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-select
          v-model="administrativeAreaId"
          style="width: 100%"
          placeholder="请选择行政区县"
          allow-clear
          @change="handleAdministrativeAreaChange"
        >
          <a-select-option
            v-for="item in administrativeAreas"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
            {{ item.name }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
    <a-col v-if="showInfo && showInfo.includes('2')" :span="spanNum">
      <a-form-model-item
        label="乡镇街道"
        prop="streetTownId"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-select
          v-model="streetTownId"
          style="width: 100%"
          :placeholder="
            !administrativeAreaId ? '请先选择行政区县' : '请选择乡镇街道'
          "
          allow-clear
          show-search
          :open="streetTownIdState_add"
          :disabled="!administrativeAreaId"
          @select="streetTownIdState_add = false"
          @focus="streetTownIdFocus_add"
          @change="handlestreetTownChange"
        >
          <a-select-option
            v-for="item in streetTowns"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            >{{ item.name }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
    <a-col v-if="showInfo && showInfo.includes('3')" :span="spanNum">
      <a-form-model-item
        label="联 络 站"
        prop="liaisonStationId"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-select
          v-model="liaisonStationId"
          :disabled="!streetTownId"
          :placeholder="!streetTownId ? '请先选择乡镇街道' : '请选择联络站'"
          allow-clear
          show-search
          :open="liaisonStationIdState_add"
          @change="handleListLiaisonStationChange"
          @select="liaisonStationIdState_add = false"
          @focus="liaisonStationIdFocus_add"
        >
          <a-select-option
            v-for="item in liaisonStations"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            >{{ item.name }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
import { getPermList as getAdministrativeAreas } from "@/api/administrativearea";
import { getPermList as getstreetTowns } from "@/api/streettown";
import { listForAdmin as getStations } from "@/api/station";
export default {
  props: {
    spanNum: {
      type: Number,
      default: () => 8,
    },
    showInfo: {
      type: Array,
      default: () => ['1','2','3']
    },
    adminValue: {
      type: [String, Number],
      default: () => '',
    },
    streetValue: {
      type: [String, Number],
      default: () => '',
    },
    liaisonValue: {
      type: [String, Number],
      default: () => '',
    }
  },
  data() {
    return {
      administrativeAreaId: this.adminValue,
      streetTownId: this.streetValue,
      liaisonStationId: this.liaisonValue,
      //行政区域列表
      administrativeAreas: [],
      //街道数据
      streetTowns: [],
      //联络站数据
      liaisonStations: [],
      liaisonStationIdState: false, //联络站数组是否为空
      streetTownIdState: false, //街道数组是否为空
      liaisonStationIdState_add: false, //联络站数组是否为空
      streetTownIdState_add: false, //街道数组是否为空
    };
  },
  created() {
    this.listAdministrativeAreas();
  },
  watch: {   
    adminValue: {  
      handler(val) {
        this.administrativeAreaId = val == '' ? undefined : val;
      },
      deep: true,
      immediate: true,
    }, 
    streetValue: {  
      handler(val) {
        this.streetTownId = val == '' ? undefined : val;
      },
      deep: true,
      immediate: true,
    }, 
    liaisonValue: {  
      handler(val) {
        this.liaisonStationId = val == '' ? undefined : val;
      },
      deep: true,
      immediate: true,
    },  
  }, 
  methods: {
    // 选择行政区域
    handleAdministrativeAreaChange(val) {
      console.log(val, "选择行政区域");
      this.streetTownId = undefined;
      this.liaisonStationId = undefined;
      this.liststreetTowns(val);
      this.$emit("getAdministrativeArea", val);
    },

    // 选择乡镇街道
    handlestreetTownChange(val) {
      console.log(val, "选择乡镇街道");
      this.liaisonStationId = undefined;
      this.listLiaisonStations(val);
      this.$emit("getStreetTown", val);
    },
    handleListLiaisonStationChange(id, state) {
      // console.log('🤗🤗🤗,  =>', id,state)
      for (let index = 0; index < this.liaisonStations.length; index++) {
        const element = this.liaisonStations[index];
        // console.log("🤗🤗🤗, element =>", element);
        if (element.id == id) {
          // console.log("🤗🤗🤗, element =>", element);
          this.contactPhoneNumber = element.contactPhoneNumber;
          this.contactName = element.contactName;
          this.liaisonStation = element;
          this.$forceUpdate();
          break;
        }
        // console.log("🤗🤗🤗,  this =>", this);
      }
      console.log(this.liaisonStation, "this.liaisonStation选择联络站");
      this.$emit("getListLiaisonStation", this.liaisonStation.id);
    },
    listAdministrativeAreas() {
      // 获取行政区县
      getAdministrativeAreas().then((response) => {
        this.administrativeAreas = response.data;
      });
    },
    liststreetTowns(administrativeAreaId) {
      if (!administrativeAreaId) {
        administrativeAreaId = "";
      }
      getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
          this.streetTownId = undefined;
          this.liaisonStation = "";
        }
      );
    },
    // 获取联络站
    listLiaisonStations(streetTownId) {
      getStations({ streetTownId: streetTownId }).then((response) => {
        this.liaisonStations = response.data;
        this.liaisonStation = "";
      });
      console.log(this.liaisonStations, "this.liaisonStations");
    },
    //提示选择
    liaisonStationIdFocus_add() {
      if (this.streetTownId && this.administrativeAreaId) {
        this.liaisonStationIdState_add = true;
      } else {
        this.$message.info("请选择行政区域和乡镇街道");
        this.liaisonStationIdState_add = false;
      }
    },
    //提示选择
    streetTownIdFocus_add() {
      if (this.administrativeAreaId) {
        this.streetTownIdState_add = true;
      } else {
        this.$message.info("请选择行政区域");
        this.streetTownIdState_add = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
