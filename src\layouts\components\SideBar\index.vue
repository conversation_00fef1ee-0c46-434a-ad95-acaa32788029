<template>
  <div class="SideBar">
    <div class="searchData">
      <a-select v-model="currenrtMenu"
                show-search
                :filter-option="filterOption"
                option-filter-prop="children"
                style="width: 160px;"
                class="search-select"
                @change="changeMenu">
        <a-icon slot="suffixIcon"
                type="search" />
        <a-select-option v-for="(item, index) in searchData"
                         :key="item.path + index"
                         :value="item.path">
          {{ item.meta.title }}
        </a-select-option>
      </a-select>
      <div>
        <a-icon :type="collapsed ? 'menu-unfold' : 'menu-fold'"
                @click="toggleCollapsed" />
      </div>
    </div>

    <a-menu v-model="selectedMenuKeys"
            mode="inline"
            style="width: 100%;"
            :open-keys="openKeys"
            :inline-collapsed="collapsed"
            @openChange="onOpenChange">
      <!-- 第一级 -->
      <a-sub-menu v-for="(item, index) in Datatitle"
                  v-show="item.title == navigation.text"
                  :key="item.path">
        <span slot="title"
              @click="skiUrl(item)">
          <span>
            <a-icon :type="item.name == 'SystemManage' ? 'tool' : 'align-center'"
                    @click="skiUrl(item)" />
          </span>{{ item.meta.title }}</span>

        <template v-for="item1 in item.children">
          <!-- 第三级别 -->
          <template v-if="item1.children">
            <a-sub-menu :key="item1.meta.title + item1.path">
              <span slot="title">
                <i class="iconfont"></i>
                <span> {{ item1.meta.title }}</span>
              </span>
              <a-menu-item v-show="!item2.hidden" v-for="item2 in item1.children"
                           :key="item.path + '/' + item1.path + '/' + item2.path"
                           @click="Titlechildren2(item, item1, item2, index)">
                <!-- <i class="iconfont" :class="subitem.icon"></i> -->
                <span :title="item2.meta.title">{{ item2.meta.title }}</span>
              </a-menu-item>
            </a-sub-menu>
          </template>

          <!-- 第二级别 -->
          <template v-else>
            <a-menu-item v-show="!item1.hidden"
                         :key="item.path + '/' + item1.path"
                         style="margin-button: 20px;"
                         @click="Titlechildren(item.path, item1.path, item, item1, index)">
              <!-- <i class="font_family" v-html="icon"></i> -->
              <span :title="item1.meta.title">{{ item1.meta.title }}</span>
            </a-menu-item>
          </template>
        </template>
      </a-sub-menu>
    </a-menu>
  </div>
</template>
<script>
import path from "path";
// import global_ from "@/global";


// import Logo from "@/layouts/components/Logo";
// import SideBarItem from "./SideBarItem";
import variables from "@/styles/variables.scss";
import events from "@/components/events.js";
import { mapGetters } from "vuex";
import { log } from "@antv/g2plot/lib/utils";
import store from "@/store";

export default {
  name: "SideBar",
  // components: { SideBarItem, Logo },
  data () {
    return {
      icon: "&#xe65f",
      msg: "",
      currenrtMenu: "",
      searchData: [] /* 这个是搜索菜单的数据 */,
      collapsed: false,
      selectedMenuKeys: [],
      deKeys: [],
      rootSubmenuKeys: ["Opinion", "huoDong", "resumption"],
      openKeys: [],
      Datatitle: [],
    };
  },
  watch: {
    navigation (newVal, oldVal) {
      console.log("🤗🤗🤗, newVal =>", newVal);
    },
    routeList (newVal, oldVal) {
      //  console.log("路由情况, newVal =>", newVal,oldVal);
      this.selectedMenuKeys = [];
      this.selectedMenuKeys.push(newVal.path);
      this.openKeys.push("/" + newVal.path.split("/")[1]);

      window.sessionStorage.setItem("selectKey", newVal.path);
    },
    '$route'(to, from) {
      // 当路由变化时执行的操作
      let from1 ={}
      from1.menuName= to.meta.title || to.mate.name
      from1.fullUrl = to.fullPath
      store.dispatch("user/getClick",from1)
    }
  },
  created () {
    //
    if (JSON.parse(window.sessionStorage.getItem("openKeys"))) {
      this.openKeys = JSON.parse(window.sessionStorage.getItem("openKeys"));
    }
    this.selectedMenuKeys = [];
    if (window.sessionStorage.getItem("selectKey")) {
      this.selectedMenuKeys.push(window.sessionStorage.getItem("selectKey"));
    }
    events.$on("sendEven", (even) => {
      if (!even) return;
      // 设置打开
      this.openKeys.push(even.openKey);
      // 增加展开菜单项保存到缓存
      let newOpenKeys = JSON.parse(window.sessionStorage.getItem("openKeys"));
      newOpenKeys.push(even.openKey);
      window.sessionStorage.setItem("openKeys", JSON.stringify(newOpenKeys));
      // 设置选中
      this.selectedMenuKeys = [];
      this.selectedMenuKeys.push(even.path);
      this.$forceUpdate();
    });

    let text1 = {
      name: "民呼我应",
      path: "/site/index",
      meta: { title: "民呼我应" },
    };
    let text2 = { name: "首页", path: "/", meta: { title: "首页" } };
    this.Datatitle.unshift(text1);
    this.Datatitle.unshift(text2);
    this.routes.filter((item) => {
      if (item.meta && item.meta.title != "代表连线") {
        this.Datatitle.push(item);
      }
    });

    this.searchList();

    // 党组 -z
    events.$on("sendEvenLeadig", (even) => {
      if (!even) return;
      // 设置选中
      // this.selectedMenuKeys = [];
      // if (even.path == '/portalIndex/index') {
      //   this.selectedMenuKeys.push("home");
      //   sessionStorage.setItem("selectKey", "home");
      // } else {
      //   sessionStorage.setItem("selectKey", even.path);
      //   this.selectedMenuKeys.push(even.path);
      // }
      // // 缓存选中key
      // // console.log(even.path, 'even.path');
      // // 设置打开的菜单栏
      // if (even.hasOwnProperty('item')) {
      //   console.log(even.item, 'item');
      //   if (even.item.openKeys != undefined) {
      //     this.openKeys = even.item.openKeys
      //   } else {
      //     console.log('none');
      //     this.openKeys =
      //       [
      //         "/index常委会会议",
      //       ]
      //   }
      // } else {
      // }
      this.searchList();
      sessionStorage.setItem("openKeys", JSON.stringify(this.openKeys));
      this.$forceUpdate();
    });
  },
  beforeDestroy () {
    events.$off("sendEvenLeadig");
  },
  computed: {
    ...mapGetters(["collapse", "routes", "layout"]),
    navigation () {
      return this.$store.state.navigation.titleText;
    },
    // 路由
    routeList () {
      return this.$route;
    },
    defaultOpen () {
      if (this.collapse) {
      }
      let arr = this.routes.map((item) => {
        return path.resolve(item.path);
      });
      /*只默认展开除了首页,登录,404,重定向以外的第一级*/
      arr = this.$baseLodash.pull(
        arr,
        "/",
        "/!*",
        "/login",
        "/404",
        "/401",
        "/redirect"
      );
      return arr;
    },
    activeMenu () {
      const route = this.$route;
      const { meta, path } = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    variables () {
      return variables;
    },
  },
  methods: {
    searchList () {
      this.currenrtMenu = "";
      this.searchData = [];
      // 这里 做的是搜索菜单的数据
      this.Datatitle.forEach((item) => {
        //  1 等于当前子菜单
        if (item.title == this.navigation.text) {
          //  2 添加一级菜单数据
          this.searchData.push(item);
          if (item.children.length !== 0) {
            item.children.forEach((itemSon) => {
              //  3 添加二级菜单数据
              if (!itemSon.hidden) {
                this.searchData.push(itemSon);
              }
            });
          }
        }
      });
      this.$forceUpdate();
    },
    changeMenu (value) {
      this.Datatitle.forEach((item, index) => {
        // 如果是第一级别
        if (item.path == value) {
          // 展开菜单
          this.openKeys = [];
          this.openKeys.push(index);
        } else {
          if (item.children) {
            //  如果是第二级别的菜单
            item.children.forEach((itemSon) => {
              if (itemSon.path == value) {
                // 展开菜单
                this.openKeys = [];
                this.openKeys.push(index);
                window.sessionStorage.setItem(
                  "openKeys",
                  JSON.stringify(this.openKeys)
                );
                //  选中菜单
                this.selectedMenuKeys = [];
                this.selectedMenuKeys.push(item.path + "/" + itemSon.path);
                window.sessionStorage.setItem(
                  "selectKey",
                  item.path + "/" + itemSon.path
                );
                //  跳转
                this.$router
                  .push({
                    path: item.path + "/" + itemSon.path,
                  })
                  .catch((err) => {
                    console.log(err);
                  });
              }
            });
          }
        }
      });
    },
    toggleCollapsed () {
      this.collapsed = !this.collapsed;
    },
    filterOption (input, option) {
      //  下拉框搜索
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 一级
    homeUrl () {
      this.$router.push({ path: "/portalIndex/index" }).catch((err) => {
        console.log(err);
      });
      // 储存页签
      events.$emit(
        "onTabs",
        "/portalIndex/index",
        "门户首页",
        sessionStorage.getItem("title2"),
        sessionStorage.getItem("index2"),
        []
      );
      //  console.log(this.selectedMenuKeys,"一级");
    },

    Titlechildren (path1, path2, keyPath, item, keys) {
      //   const res = await store.dispatch("user/sendLogInfo",
      //   {
      //     id:'5296d7d08f184f75bcf12182cbcfedac',
      //     systemId:'lzhdzz',
      //     hidden:'0',
      //   }
      // );
      console.log(path1, path2, '0o0o00o')
      if (path2 == "TrainingStudy") {
        this.navigation.text = "在线学习培训";
        events.$emit("sendIndex", "8");
        this.deKeys = ["organizationTraining"];
        this.$router.push({
          path: "/organizationIndex/organizationTraining",
        });
      } else if (path2 == "publilist") {
        this.$router.push({
          path: "/publicopinion/publilist",
        });
        this.$store.dispatch("navigation/breadcrumb1", "社情民意收集");
        this.$store.dispatch("navigation/breadcrumb2", "社情民意列表");
      } else if (path2 == "site/website/MakeSuggestions") {
        this.$router.push({
          path: "/site/website/MakeSuggestions",
        });
      }
      else if (path2 == "OpinionTouristList" || path2 === "GZOpinionTouristList") {
        // 0-0-1`====_
        // var routeUrl = this.$router.resolve({ path: "/opinion/GZOpinionTouristList", });//意见征集公众端查询
        // window.open(routeUrl.href, "_blank");
        // window.open('https://zhrd.rd.gz.cn/lzpc/#/opinion/GZOpinionTouristList', "_blank");
        window.open( window.location.protocol + "//" +
              window.location.host + '/lzpc/?appId=xtggym&systemId=xtggym&UrlData=/opinion/GZOpinionTouristList', "_blank");
      }
      else if (path2 == "GZsurveyList" || path2 === "GZsurveyList") {
        // 0-0-1`====_
        // var routeUrl = this.$router.resolve({ path: "/investigation/GZsurveyList", });//问卷公众端查询
        // window.open(routeUrl.href, "_blank");
        window.open( window.location.protocol + "//" +
              window.location.host + '/lzpc/?appId=xtggym&systemId=xtggym&UrlData=/investigation/GZsurveyList', "_blank");
      }
      else if (path2 == "scan_syhome" || path2 === "sydj_portal") {
        this.$router.push({
          path: "/scan_syhome/sydj_portal",   //工作门户
        });
        window.open('https://gdpc.gdgov.cn/baseyhzx5/szrd/workstation/web/', '_blank'); // 新窗口
      }
      else if (path2 == "scan_syhome" || path2 === "sydj_workPortal") {
        this.$router.push({
          path: "/scan_syhome/sydj_workPortal", //联络站工作平台
        });
        window.open('https://gdpc.gdgov.cn/ydj/npc/site-mng/?app=cpc#/login', '_blank'); // 新窗口
      }
      else if (path2 == "scan_syhome" || path2 === "sydj_station") {
        this.$router.push({
          path: "/scan_syhome/sydj_station",    //代表联络站管理
        });
        window.open('https://gdpc.gdgov.cn/ydj/npc/site-mng/organ.html?from=home&role=llz#/site-info-cpc/list', '_blank'); // 新窗口
      }
      else if (path2 == "scan_syhome" || path2 === "sydj_peopleAdvice") {
        this.$router.push({
          path: "/scan_syhome/sydj_peopleAdvice",   //群众意见管理
        });
        window.open('https://gdpc.gdgov.cn/ydj/npc/site-mng/organ.html?from=home&role=llz#/public-message/list', '_blank'); // 新窗口
      }

      else if (path2 == "scan_syhome" || path2 === "sydj_portalHlw") {
        this.$router.push({
          path: "/scan_syhome/sydj_portalHlw",
        });
        window.open('https://www.gdpc.gov.cn/baseyhzx5/szrd/workstation/web/#/home', '_blank'); // 新窗口
      }
      else if (path2 == "scan_syhome" || path2 === "sydj_workPortalHlw") {
        this.$router.push({
          path: "/scan_syhome/sydj_workPortalHlw",
        });
        window.open('https://www.gdpc.gov.cn/t/LLZDL', '_blank'); // 新窗口
      }
      else if (path2 == "scan_syhome" || path2 === "sydj_stationHlw") {
        this.$router.push({
          path: "/scan_syhome/sydj_stationHlw",
        });
        window.open('https://www.gdpc.gov.cn/ydj/npc/site-mng/organ.html?from=home&role=llz#/site-info-cpc/list', '_blank'); // 新窗口
      }
      else if (path2 == "scan_syhome" || path2 === "sydj_peopleAdviceHlw") {
        this.$router.push({
          path: "/scan_syhome/sydj_peopleAdviceHlw",
        });
        window.open('https://www.gdpc.gov.cn/ydj/npc/site-mng/organ.html?from=home&role=llz#/public-message/list', '_blank'); // 新窗口
      } else {
        this.$router
          .push({
            path: path1 + "/" + path2,
          })
          .catch((err) => {
            console.log(err);
          });
        window.sessionStorage.setItem("selectKey", path1 + "/" + path2);
      }
      // 储存页签
      events.$emit(
        "onTabs",
        path1 + "/" + path2,
        item.meta.title,
        sessionStorage.getItem("title2"),
        sessionStorage.getItem("index2"),
        [keys]
      );

      //  setTimeout(() => {
      //     window.localStorage.setItem("tongjifenzi", JSON.stringify(null));
      //  }, 100);
      this.$nextTick(() => {
        window.localStorage.setItem("tongjifenzi", JSON.stringify(null));
      });
    },

    Titlechildren2 (item, item1, item2, item3) {
      this.$router.push({
        path: item.path + "/" + item1.path + "/" + item2.path,
      });
      window.sessionStorage.setItem(
        "selectKey",
        item.path + "/" + item1.path + "/" + item2.path
      );
      // 储存页签
      events.$emit(
        "onTabs",
        item.path + "/" + item1.path + "/" + item2.path,
        item2.meta.title,
        sessionStorage.getItem("title2"),
        sessionStorage.getItem("index2"),
        [item1.meta.title, item3]
      );
      this.$nextTick(() => {
        window.localStorage.setItem("tongjifenzi", JSON.stringify(null));
      });
    },
    skiUrl (item) {
      if (item.path == "/site/index" || item.path == "/") {
        this.$router
          .push({
            path: item.path,
          })
          .catch((err) => {
            console.log(err);
          });
      } else if (item.path == "/portalIndex") {
        this.$router
          .push({
            path: item.path + "/" + item.children[0].path,
          })
          .catch((err) => {
            console.log(err);
          });
      }

    },

    onOpenChange (openKeys) {
      // 当菜单被展开时触发此处
      this.openKeys = openKeys;
      window.sessionStorage.setItem("openKeys", JSON.stringify(this.openKeys));
    },
  },
};
</script>
<style lang="scss" scoped>
.side-bar-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: $base-z-index;
  width: $base-left-menu-width;
  height: 100vh;
  overflow: hidden;
  background: $base-menu-background;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  transition: all 0.2s ease-in-out;

  ::v-deep {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .el-menu {
      border: 0;
      .el-menu-item {
        height: 56px !important;
        overflow: hidden;
        line-height: 56px !important;
        // border-bottom: 1px solid #ddd;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:hover {
          color: $base-color-white !important;
          background-color: $base-menu-background-active;
        }

        &.is-active {
          color: $base-color-white !important;
          background-color: $base-menu-background-active;
        }
      }
    }

    .svg-inline {
      &--fa {
        width: 1rem;
      }
    }

    .nest-menu {
      [class*="menu"] {
        // background-color: $base-menu-background;

        // &.is-active {
        //   color: $base-color-white !important;
        //   background-color: $base-menu-background-active !important;
        // }
      }
    }
  }

  &.is-collapse {
    width: $base-left-menu-width-min;
    border-right: 0 !important;

    ::v-deep {
      .title {
        display: none;
      }

      .el-menu--collapse {
        border-right: 0 !important;

        .el-submenu__icon-arrow {
          right: 10px;
          margin-top: -3px;
        }

        .el-submenu__title {
          span {
            display: none;
          }
        }
      }
    }
  }
}
.searchData {
  margin-top: 20px;
  padding: 0 10px;
  width: 100%;
  display: flex;
  .search-select {
    ::v-deep .ant-select-selection {
      border-radius: 20px;
      background-color: #f1f1f1;
    }
  }
}
.qxj-side {
  box-shadow: none;
  -webkit-transition: width 0.28s;
  transition: width 0.28s;
  width: 200px !important;
  background-color: #fff;
  height: 83%;
  position: fixed;
  font-size: 0;
  top: 90px;
  bottom: 0;
  left: 0;
  z-index: 1000;
  overflow: hidden;
}
.SideBar {
  overflow-x: hidden;
  overflow-y: scroll;
  text-align: left;
  background-color: #ffffff;
}
::v-deep .ant-menu-submenu-selected {
  .ant-menu-submenu-title {
    color: #c51512;
  }
}
.table-container {
  // padding: 30px 20px 20px 20px;
  background-color: #fff;
}
.ant-menu {
  @include add-size($font_size_16);
}
.ant-menu-submenu-inline {
  .ant-menu-submenu-title {
    span {
      @include add-size($font_size_16);
    }
  }
}
.ant-menu-submenu-inline {
  @include add-size($font_size_16);
  font-family: pingFang-M;
}
.ant-menu-item {
  @include add-size($font_size_16);
  font-family: pingFang-R;
}
::v-deep .ant-menu-submenu-selected {
  color: #db3046;
}
.SideBar::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 0px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.SideBar::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background: #d9dadc;
}
.SideBar::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px #d9dadc;
  border-radius: 10px;
  background: #ededed;
}

::v-deep .ant-menu-item:last-of-type {
  margin-bottom: -15px;
  //
}
::v-deep .ant-menu-submenu {
  // margin-bottom: 20px;
  //
}
.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  color: #c51512;
  background-color: #fbeeef;
}
.ant-menu-submenu-selected span {
  // 父级、
  // color: #c51512 !important;
}
.ant-menu-item:hover {
  color: #c51512;
}
.ant-menu-inline .ant-menu-item::after {
  border: 0px;
}
</style>
<style lang="scss">
.ant-menu-inline,
.ant-menu-vertical,
.ant-menu-vertical-left {
  border: 0px !important;
}
.ant-menu-submenu-selected {
  color: #7c7c7c !important;
}
.ant-menu-submenu-active {
  color: #7c7c7c !important;
}
.ant-menu-submenu-title:hover {
  color: #7c7c7c !important;
}
.ant-menu-submenu-selected:hover {
  color: #7c7c7c !important;
}
.ant-menu-submenu:hover {
  color: #7c7c7c !important;
}
.ant-menu-submenu-inline
  > .ant-menu-submenu-title
  .ant-menu-submenu-arrow::before {
  background-image: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.65),
    rgba(0, 0, 0, 0.65)
  ) !important;
}
</style>
