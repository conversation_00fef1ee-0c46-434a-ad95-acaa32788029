<template>
  <a-modal :visible="visible"
           :title="showOperate ? '处理建议' : '建议详情'"
           width="80%"
           :centered="true"
           :footer="null"
           :body-style="{ height: '600px', overflowY: 'auto' }"
           @cancel="close">
    <a-tabs v-model="tabsKey"
            type="card"
            @change="changeTab">
      <a-tab-pane key="1"
                  tab="议案建议信息">
        <div :style="tabStyle">
          <a-descriptions bordered
                          size="small">
            <a-descriptions-item label="标题">{{
              record.proposalTitle
            }}</a-descriptions-item>
            <a-descriptions-item label="届次">{{
              record.periodDesc
            }}</a-descriptions-item>
            <a-descriptions-item label="意向承办单位">{{
              record.intentOrg
            }}</a-descriptions-item>
            <!-- <a-descriptions-item label="状态">{{
              record.status | filterStatus
            }}</a-descriptions-item>
            <a-descriptions-item label="当前经办人" :span="2">
              <div v-if="record.yajyOrgList && record.yajyOrgList.length > 0">
                <span v-for="(item, index) in record.yajyOrgList" :key="index">
                  {{
                    item.orgName +
                    "(" +
                    item.orgDispname +
                    item.orgDispphone +
                    ");"
                  }}
                </span>
              </div>
              <div
                v-if="
                  record.yajyOrgOperDTOList &&
                  record.yajyOrgOperDTOList.length > 0
                "
              >
                <span v-if="record.yajyOrgOperDTOList[0].orgCode == '387'">
                  <span>
                    {{
                      record.yajyOrgOperDTOList[0].orgName +
                      "(" +
                      record.yajyOrgOperDTOList[0].telNo +
                      ")"
                    }}
                  </span>
                  <br />
                </span>
                <span v-else>
                  <span
                    v-for="(item, index) in record.yajyOrgOperDTOList"
                    :key="index"
                  >
                    {{ item.operName + "(" + item.telNo + ")" }}
                  </span>
                </span>
              </div>
            </a-descriptions-item> -->
            <a-descriptions-item label="建议号">{{
              record.proposalNum
            }}</a-descriptions-item>
            <a-descriptions-item label="议案建议类型">{{
              record.proposalType | filterProposalType
            }}</a-descriptions-item>
            <a-descriptions-item label="领衔代表">{{
              record.headPer
            }}</a-descriptions-item>
            <a-descriptions-item label="内容分类">{{
              record.proposalContentType | filterProposalContentType
            }}</a-descriptions-item>
            <a-descriptions-item label="办理情况">{{
              record.proposalHandle
            }}</a-descriptions-item>
            <a-descriptions-item label="所在代表团">{{
              record.deputyOrg
            }}</a-descriptions-item>
            <a-descriptions-item label="交办日期">{{
              record.handleDate
            }}</a-descriptions-item>
            <a-descriptions-item label="办理期限至">主办：{{ majorLastTime.substring(0, 10) }} 会办：{{
                minorLastTime.substring(0, 10)
              }}</a-descriptions-item>
            <a-descriptions-item label="答复方式">{{
              record.codeHaveOrNo == "1"
                ? "书面"
                : record.codeHaveOrNo == "0"
                ? "网上"
                : ""
            }}</a-descriptions-item>
            <a-descriptions-item label="加强沟通">
              {{
                record.ifContect == "1"
                  ? "需要见面座谈和调研"
                  : record.ifContect == "0"
                  ? "只需电话微信沟通"
                  : record.ifContect == "2"
                  ? "不需要沟通，直接答复"
                  : record.ifContect == "3"
                  ? "只做工作参考用，不需要正式书面答复"
                  : ""
              }}
            </a-descriptions-item>
            <a-descriptions-item label="多年多次提出、尚未解决">{{
              record.overTheYearsNotResolved == "1"
                ? "是"
                : record.overTheYearsNotResolved == "0"
                ? "否"
                : record.overTheYearsNotResolved == "2"
                ? "未详"
                : ""
            }}</a-descriptions-item>
            <a-descriptions-item label="公开情况">{{
              record.ifPublice == "1"
                ? "是"
                : record.ifPublice == "0"
                ? "不"
                : ""
            }}</a-descriptions-item>

            <a-descriptions-item label="附件列表"
                                 :span="3"><span v-if="record.existFile == true">附件：<a
                   @click="downFileData('1', '')">附件下载</a></span>
            </a-descriptions-item>
            <a-descriptions-item label="建议纸"
                                 :span="3"><a @click="downMotion">下载建议纸</a>
            </a-descriptions-item>
            <a-descriptions-item label="正文"
                                 :span="3">
              <a-space>
                <a @click="lookWord">查看正文</a>
                <a @click="downFileData('9', '')">下载正文附件</a>
                <a @click="resetFile">重新上传正文附件</a>
                <!-- <a @click="regenerateText">重新生成正文</a> -->
              </a-space>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2"
                  tab="审议处理信息">
        <h3>
          代表大会审议处理信息：
        </h3>
        <standard-table row-key="attId"
                        :columns="columns"
                        :pagination="false"
                        :data-source="congressDataSource">
        </standard-table>
        <div v-if="isShowUpload"
             class="fileBox">
          <a-upload class="fileBoxUpload"
                    action=""
                    accept=".docx,.doc"
                    :remove="
              (file) => {
                handleTextRemoveReset(file, 'congressFile');
              }
            "
                    :file-list="congressFile"
                    :before-upload="
              () => {
                return false;
              }
            "
                    @change="uploadChange($event, 'congressFile', 26)">
            <a-button type="primary">选择文件</a-button>
          </a-upload>
          <div class="fileBoxFileText">
            <a-button type="primary"
                      @click="upLoadFileList('congressFile', 'congressDataSource', 26)">上传文件</a-button>
            <span>
              （请先选择文件，然后点击上传，文件方可保存）
            </span>
          </div>
        </div>
        <h3>
          常委会审议处理信息：
        </h3>
        <standard-table row-key="attId"
                        :columns="columns"
                        :pagination="false"
                        :data-source="standingCommitteeDataSource">
        </standard-table>
        <div v-if="isShowUpload"
             class="fileBox">
          <a-upload class="fileBoxUpload"
                    action=""
                    accept=".docx,.doc"
                    :remove="
              (file) => {
                handleTextRemoveReset(file, 'standingCommitteeFile');
              }
            "
                    :before-upload="
              () => {
                return false;
              }
            "
                    :file-list="standingCommitteeFile"
                    @change="uploadChange($event, 'standingCommitteeFile', 27)">
            <a-button type="primary">点击上传</a-button>
          </a-upload>
          <div class="fileBoxFileText">
            <a-button type="primary"
                      @click="
                upLoadFileList(
                  'standingCommitteeFile',
                  'standingCommitteeDataSource',
                  27
                )
              ">上传文件</a-button>
            <span>
              （请先选择文件，然后点击上传，文件方可保存）
            </span>
          </div>
        </div>
        <h3>
          市政府（两院）信息：
        </h3>
        <standard-table row-key="attId"
                        :columns="columns"
                        :pagination="false"
                        :data-source="cityHallDataSource">
        </standard-table>
        <div v-if="isShowUpload"
             class="fileBox">
          <a-upload class="fileBoxUpload"
                    action=""
                    accept=".docx,.doc"
                    :remove="
              (file) => {
                handleTextRemoveReset(file, 'cityHallFile');
              }
            "
                    :before-upload="
              () => {
                return false;
              }
            "
                    :file-list="cityHallFile"
                    @change="uploadChange($event, 'cityHallFile', 28)">
            <a-button type="primary">选择文件</a-button>
          </a-upload>
          <div class="fileBoxFileText">
            <a-button type="primary"
                      @click="upLoadFileList('cityHallFile', 'cityHallDataSource', 28)">上传文件</a-button>
            <span>
              （请先选择文件，然后点击上传，文件方可保存）
            </span>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="3"
                  tab="办理进度报告">
        <h3>
          政府办理进度：
        </h3>
        <standard-table row-key="attId"
                        :columns="columns"
                        :pagination="false"
                        :data-source="government">
        </standard-table>
        <div v-if="isShowUpload"
             class="fileBox">
          <a-upload class="fileBoxUpload"
                    action=""
                    accept=".docx,.doc"
                    :remove="
              (file) => {
                handleTextRemoveReset(file, 'governmentFile');
              }
            "
                    :before-upload="
              () => {
                return false;
              }
            "
                    :file-list="governmentFile"
                    @change="uploadChange($event, 'governmentFile', 29)">
            <a-button type="primary">点击上传</a-button>
          </a-upload>
          <div class="fileBoxFileText">
            <a-button type="primary"
                      @click="upLoadFileList('governmentFile', 'government', 29)">上传文件</a-button>
            <span>
              （请先选择文件，然后点击上传，文件方可保存）
            </span>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="4"
                  tab="办理结果报告">
        <h3>
          政府办理结果：
        </h3>
        <standard-table row-key="attId"
                        :columns="columns"
                        :pagination="false"
                        :data-source="reportResults">
        </standard-table>
        <div v-if="isShowUpload"
             class="fileBox">
          <a-upload action=""
                    accept=".docx,.doc"
                    :remove="
              (file) => {
                handleTextRemoveReset(file, 'reportResultsFile');
              }
            "
                    :before-upload="
              () => {
                return false;
              }
            "
                    :file-list="reportResultsFile"
                    @change="uploadChange($event, 'reportResultsFile', 30)">
            <a-button type="primary">点击上传</a-button>
          </a-upload>
          <div class="fileBoxFileText">
            <a-button type="primary"
                      @click="upLoadFileList('reportResultsFile', 'reportResults', 30)">上传文件</a-button>
            <span>
              （请先选择文件，然后点击上传，文件方可保存）
            </span>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="5"
                  tab="提出代表">
        <div :style="tabStyle">
          <!-- v-if="
              ((record.status == 10 || record.status < 20) &&
                authority == 'YAJY_QRD') ||
              authority == 'YAJY_XLW' ||
              authority == 'YAJY_DBC'
            " -->
          <a-space v-if="
              userData.authorities
                ? (record.status < 40 &&
                    userData.authorities[0].authority == 'YAJY_QRD') ||
                  userData.authorities[0].authority == 'YAJY_XLW' ||
                  userData.authorities[0].authority == 'YAJY_DBC'||
                  userData.authorities[0].authority == 'I-XTGLY'
                : false
            "
                   class="operator">
            <a-button type="primary"
                      icon="plus-circle"
                      @click="showUserTable('提出')">新建</a-button>
            <a-button icon="delete"
                      @click="delJointlyList">删除</a-button>
          </a-space>
          <standard-table row-key="jointlyId"
                          :columns="deputationColumns"
                          :selected-rows.sync="deputationSelectedRows"
                          :data-source="deputationDataSource">
          </standard-table>
        </div>
      </a-tab-pane>
      <a-tab-pane key="6"
                  tab="承办单位">
        <div :style="tabStyle">
          <standard-table row-key="undertakeId"
                          :columns="unitColumns"
                          :data-source="unitDataSource">
          </standard-table>
        </div>
      </a-tab-pane>
      <a-tab-pane key="7"
                  tab="备注">
        <a-row>
          <a-form-model :model="remarkForm"
                        layout="inline">
            <a-col span="12">
              <a-form-model-item label="备注">
                <a-input v-model="remarkForm.remakeTitle"> </a-input>
              </a-form-model-item>
            </a-col>
            <a-col span="12">
              <a-form-model-item label="日期">
                <a-date-picker v-model="remarkForm.remakeTime"
                               allow-clear
                               value-format="YYYY-MM-DD"
                               placeholder="选择时间"></a-date-picker>
              </a-form-model-item>
            </a-col>
          </a-form-model>
        </a-row>
        <a-row>
          <a-textarea v-model="remarkForm.remarkContent"
                      :rows="6"></a-textarea>
        </a-row>
        <a-row style="margin: 10px 0;">
          <a-button v-if="isShowUpload"
                    @click="saveRemarkEvent">保存</a-button>
        </a-row>
        <standard-table row-key="attId"
                        :columns="remarkColumns"
                        :pagination="false"
                        :data-source="remarkDataSource">
        </standard-table>
        <div v-if="isShowUpload"
             class="fileBox">
          <a-upload class="fileBoxUpload"
                    action=""
                    accept=".docx,.doc"
                    :remove="
              (file) => {
                handleTextRemoveReset(file, 'remarkFile');
              }
            "
                    :before-upload="
              () => {
                return false;
              }
            "
                    :file-list="remarkFile"
                    @change="uploadChange($event, 'remarkFile', 31)">
            <a-button type="primary">点击上传</a-button>
          </a-upload>
          <div class="fileBoxFileText">
            <a-button type="primary"
                      @click="upLoadFileList('remarkFile', 'remarkDataSource', 31)">上传文件</a-button>
            <span>
              （请先选择文件，然后点击上传，文件方可保存）
            </span>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
    <commonUnitShuttleTable ref="commonUnitShuttleTable"
                            :default-unit="defaultUnit"
                            @emitUnitTable="getUnitTable"></commonUnitShuttleTable>
    <commonUserTable ref="commonUserTable"
                     :disableSelectedRowKeys="disableSelectedRowKeylist"
                     @emitJoinTable="getJoinTable"></commonUserTable>
    <jbrTable ref="jbrTable"
              @emitJbrTable="getJbrTable"></jbrTable>
    <evaluationModal ref="evaluationModal"
                     @emitClose="close"></evaluationModal>
    <adjustListEdit ref="adjustListEdit"
                    @close="close"></adjustListEdit>
    <a-modal v-model="fkVisible"
             title="反馈内容"
             @ok="closeFkVisible">
      <a-descriptions>
        <a-descriptions-item label="反馈评价"
                             :span="3">{{
          fkEvaluation
        }}</a-descriptions-item>
        <a-descriptions-item label="反馈内容"
                             :span="3">{{
          fkContent
        }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </a-modal>
</template>
<script>
// 议案详情公共组件 显示详情
import commonUnitShuttleTable from "@/views/common/commonUnitShuttleTable.vue";
import evaluationModal from "@/views/meeting/suggesting/evaluationModal.vue";
import commonUserTable from "@/views/common/commonUserTable.vue";
import jbrTable from "@/views/common/jbrTable.vue";
import { fileUpload, deleteById } from "@/api/commonApi/file.js";
import adjustListEdit from "@/views/meeting/suggesting/adjustListEdit.vue";
import {
  isYiAnCanUpload,
  proposalById,
  doAssign,
  doAudit,
  doCheck,
  getJointlyList,
  addJointly,
  delJointly,
  againProduce,
  getLocalHistory,
  getProposalUnder,
  doReply,
  getDelayUndertakeListById,
  doDelay,
  getReviseUndertakeListById,
  keepRevise,
  keepDelay,
  seeText,
  getUndertakeInfo,
  doRevise,
  getJbrList,
  updateJbr,
  getUndertakeDfAndFeedbackList,
  againUploadTextFile,
  downloadProposal,
  saveCBDW,
  doSignedSave,
  getUndertakeStatus,
  getUndertakeDfInfo,
  applyDelay,
  saveRemark,
} from "@/api/myJob/myProposal.js";

import {
  downFile,
  downFileItem,
  getUserData_yajy,
} from "@/api/commonApi/file.js";
export default {
  components: {
    commonUnitShuttleTable,
    commonUserTable,
    evaluationModal,
    adjustListEdit,
    jbrTable,
  },
  filters: {
    filterStatus (text) {
      if (text === 10) {
        return "草稿";
      }
      if (text === 11) {
        return "联名中";
      } if (text === 20) {
        return "校核中";
      }
      if (text === 21) {
        return "分类中";
      }
      if (text === 22) {
        return "初审中";
      }
      if (text === 30 || text === 25) {
        return "复审中";
      }
      if (text === 40) {
        return "交办中";
      }
      if (text === 41) {
        return "不予立案审核中";
      }
      if (text === 42) {
        return "分办确认中";
      }
      if (text === 45) {
        return "不予立案审核中";
      }
      if (text === 50) {
        return "签收中";
      }
      if (text === 60) {
        return "答复中";
      }
      if (text === 70) {
        return "待反馈";
      }
      if (text === 80) {
        return "已反馈";
      }
      if (text === 90) {
        return "办毕";
      }
    },
    filterProposalContentType (text) {
      if (text == "JYFL01") return "法制";
      if (text == "JYFL02") return "监察和司法";
      if (text == "JYFL03") return "经济";
      if (text == "JYFL04") return "城建环资";
      if (text == "JYFL05") return "农村农业";
      if (text == "JYFL06") return "教科文卫";
      if (text == "JYFL07") return "华侨外事民族宗教";
      if (text == "JYFL08") return "其他";
      if (text == "JYFL09") return "预算";
      if (text == "JYFL10") return "社会建设";
    },
    filterProposalType (text) {
      if (text == "1") return "大会议案";
      if (text == "2") return "大会建议";
      if (text == "3") return "闭会建议";
      if (text == "4") return "供参考建议";
    },
  },
  data () {
    const columns = [
      {
        ellipsis: true,
        title: "文件名",
        customRender: (text, record, index) => {
          return record.attName + "." + record.attSuffix;
        },
      },
      {
        ellipsis: true,

        title: "文件大小",
        customRender: (text, record, index) => {
          return record.attSize + record.attUnit;
        },
      },
      {
        ellipsis: true,

        title: "上传时间",
        ellipsis: true,
        dataIndex: "createTime",
      },
      {
        title: "上传单位信息",
        ellipsis: true,
        dataIndex: "orgName",
      },
      {
        title: "操作",
        customRender: (text, record, index) => {
          let isShowUpload = this.isShowUpload;
          let h = this.$createElement;
          return h("div", [
            h(
              "span",
              {
                attrs: {
                  type: "text",
                },
                style: {
                  cursor: "pointer",
                  color: "#1890ff",
                },
                on: {
                  click: () => {
                    this.downFileList(record);
                  },
                },
              },
              "下载"
            ),
            h(
              "span",
              {
                attrs: {
                  type: "text",
                },
                style: {
                  cursor: "pointer",
                  marginLeft: "14px",
                  color: "#1890ff",
                  display: isShowUpload ? "inline-block" : "none",
                },
                on: {
                  click: () => {
                    this.handleDelete(record);
                  },
                },
              },
              "删除"
            ),
          ]);
        },
      },
    ];
    const remarkColumns = [
      {
        ellipsis: true,

        title: "文件名",
        customRender: (text, record, index) => {
          return record.attName + "." + record.attSuffix;
        },
      },
      {
        ellipsis: true,

        title: "文件大小",
        customRender: (text, record, index) => {
          return record.attSize + record.attUnit;
        },
      },
      {
        ellipsis: true,

        title: "上传时间",
        ellipsis: true,
        dataIndex: "createTime",
      },
      {
        title: "操作",
        customRender: (text, record, index) => {
          let isShowUpload = this.isShowUpload;
          let h = this.$createElement;
          return h("div", [
            h(
              "span",
              {
                attrs: {
                  type: "text",
                },
                style: {
                  cursor: "pointer",
                  color: "#1890ff",
                },
                on: {
                  click: () => {
                    this.downFileList(record);
                  },
                },
              },
              "下载"
            ),
            h(
              "span",
              {
                attrs: {
                  type: "text",
                },
                style: {
                  display: isShowUpload ? "inline-block" : "none",
                  cursor: "pointer",
                  marginLeft: "14px",
                  color: "#1890ff",
                },
                on: {
                  click: () => {
                    this.handleDelete(record);
                  },
                },
              },
              "删除"
            ),
          ]);
        },
      },
    ];
    return {
      tabStyle: { maxHeight: "600px", overflowY: "auto" }, // 对话框中的 tab 内容高度及垂直滚动条设置
      visible: false,
      proposalId: "", //父组件直接赋值 议题id
      fkVisible: false,
      fkEvaluation: "",
      fkContent: "",
      majorUndertakeDfList: [],
      minorUndertakeDfList: [],
      record: { unIfmainList: [], ifmainList: [] },
      userData: {},
      resourceForm: {
        meetName: "",
        meet: "",
        host: "",
        hostName: "",
        adjust: "",
        adjustName: "",
        isPublic: "",
        dffs: [],
        jbrName: "",
        jbrDept: "",
        jbrJob: "",
        jbrPhone: "",
        jbrOph: "",
        dflb: null,
        orgCode: "",
        mainHandleLastTimeStr: "",
        minorHandleLastTimeStr: "",
        contentType: "",
      },
      columns,
      remarkColumns,
      rules: {
        jbrName: [
          { required: true, message: "请输入经办人姓名", trigger: "blur" },
        ],
        jbrDept: [
          { required: true, message: "请输入经办人部门", trigger: "blur" },
        ],
        jbrJob: [
          { required: true, message: "请输入经办人职务", trigger: "blur" },
        ],
        jbrPhone: [
          { required: true, message: "请输入经办人手机", trigger: "blur" },
        ],
        jbrOph: [
          { required: true, message: "请输入经办人办公电话", trigger: "blur" },
        ],
        dflb: [
          { required: true, message: "请输入答复类型", trigger: "change" },
        ],
        dffs: [
          { required: true, message: "请输入沟通方式", trigger: "change" },
        ],
        isPublic: [
          { required: true, message: "请输入答复是否公开", trigger: "change" },
        ],
      },
      unitColumns: [
        // 承办单位表格头定义
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "ifmain",
          customRender: (text, record, index) =>
            text == 1 ? "主办单位" : "会办单位",
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
      ],
      unitDataSource: [
        // 承办单位表格数据
      ],
      processColumns: [
        // 流程跟踪表格头定义
        {
          title: "状态",
          ellipsis: true,
          dataIndex: "replyType",
        },
        {
          title: "处理人",
          ellipsis: true,
          dataIndex: "reply_by_name",
        },
        {
          title: "处理时间",
          ellipsis: true,
          dataIndex: "replyTime",
        },
        {
          title: "办理意见",
          ellipsis: true,
          dataIndex: "replyComment",
        },
      ],
      processDataSource: [
        // 流程跟踪数据
      ],
      deputationColumns: [
        // 提出代表表格头定义
        {
          title: "类别",
          ellipsis: true,
          dataIndex: "ifpublic",
          customRender: (text, record, index) => {
            if (text == 1) return "领衔";
            if (text == 0) return "联名";
          },
        },
        {
          title: "代表姓名",
          ellipsis: true,
          dataIndex: "name",
        },
        {
          title: "通讯地址",
          ellipsis: true,
          dataIndex: "unitAddress",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "unitPhone",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "mobilePhone",
        },
        {
          title: "Email",
          ellipsis: true,
          dataIndex: "",
        },
        {
          title: "邮政编码",
          ellipsis: true,
          dataIndex: "",
        },
        {
          title: "申请时间",
          ellipsis: true,
          dataIndex: "createTime",
        },
        {
          title: "反馈时间",
          ellipsis: true,
          dataIndex: "agreen",
        },
        {
          title: "联名状态",
          ellipsis: true,
          dataIndex: "ifagreen",
          customRender: (text, record, index) => {
            if (text == 1) return "同意";
            if (text == 0) return "不同意";
            if (text == null) return "未处理";
          },
        },
      ],
      // 提出列表table
      deputationDataSource: [],
      // 提出列表选中数据
      deputationSelectedRows: [],
      unitType: "", //单位类型
      returnValue: false,
      tabsKey: "1",
      fileTextList: [], //答复附件
      perType: "", //打开代表列表的场景
      scenesStatus: "", // 场景默认是 处理延期
      dataSourcePostpone: [], //处理延期数据
      selectedRowsPostpone: [], //处理延期数据-已选
      columnsPostpone: [
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "",
          customRender: (text, record, index) => {
            return record.ifmain == 1 ? "主办单位" : "会办单位";
          },
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "申请时间",
          ellipsis: true,
          dataIndex: "applyDelayTime",
        },
        {
          title: "延期时长",
          ellipsis: true,
          dataIndex: "delayTimeTo",
        },
        {
          title: "调整原因",
          ellipsis: true,
          dataIndex: "applyCommnent",
        },
      ], //处理延期数据/单位调整 - 表头
      unitModalVisibleColumns: [
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "ifmain",
          customRender: (text, record, index) => {
            if (text == "1") return "主办单位";
            if (text == "0") return "会办单位";
          },
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
        {
          title: "签收状态",
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            if (text == 0) return "调整中";
            if (text == 1) return "签收中";
            if (text == 2) return "答复中";
            if (text == 3) return "待反馈";
          },
        },
      ], //弹出的单位调整列表
      undertakeId: "", //子流程id
      visiblePostpone: false,
      keepType: "",
      showOperate: true, //是否显示操作 默认显示
      modalTitle: "处理延期", //延期/调整弹出框
      unitModalVisible: false, //单位调整弹出框
      unitModalVisibleSelectedRows: [], //单位调整已选数据
      unitModalVisibleDataSource: [], //单位调整列表
      changeIfMainVisible: false, //单位调整弹出框 修改主办/会办
      fileVisible: false, //重新上传文件窗口开关
      fileTextListReset: [], //重新上传文件列表
      contentTypeList: [
        // 内容分类
        { id: "JYFL01", name: "法制" },
        { id: "JYFL02", name: "监察和司法" },
        { id: "JYFL09", name: "预算" },
        { id: "JYFL03", name: "经济" },
        { id: "JYFL04", name: "城建环资" },
        { id: "JYFL05", name: "农村农业" },
        { id: "JYFL06", name: "教科文卫" },
        { id: "JYFL07", name: "华侨外事民族宗教" },
        { id: "JYFL10", name: "社会建设" },
        { id: "JYFL08", name: "其他" },
      ],
      jointlyId: "", //联名id
      undertakeStatus: false, //显示反馈的依据，获取子流程状态
      delayVisible: false,
      hbDataSource: [], //会办数据
      minorLastTime: "",
      majorLastTime: "",
      fileTextList_7: [],
      fileTextList_8: [],
      delayForm: { delayDay: "", comment: "" },
      defaultUnit: [],
      // 申请调整
      changeIfMainTitle: "修改承办类别",
      changeIfMainForm: {},
      cityHallDataSource: [], //市政府（两院）信息
      standingCommitteeDataSource: [], //常委会审议处理信息
      congressDataSource: [], //代表大会审议处理信息
      reportResults: [], //政府办理结果
      remarkFile: [],
      remarkDataSource: [],
      government: [], //政府办理进度
      remarkForm: { remakeTime: "", remakeTitle: "" },
      //文件列表
      congressFile: [],
      standingCommitteeFile: [],
      cityHallFile: [],
      governmentFile: [],
      reportResultsFile: [],
      isShowUpload: true, //获取当前登陆人是否有上传文件的权限
      disableSelectedRowKeylist: [],//禁止添加的用户（已添加）
    };
  },
  methods: {
    // 文件列表下载
    downFileList (row) {
      console.log("🤗🤗🤗, row =>", row);
      let form = {};
      form.relId = this.proposalId;
      form.attId = row.attId;
      form.type = row.attType;
      downFileItem(form).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `${row.attName}.${row.attSuffix}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 文件列表删除
    handleDelete (row) {
      console.log("🤗🤗🤗, row =>", row);
      deleteById({ Id: row.attId }).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          if (row.attType == 26) {
            this.congressDataSource = this.congressDataSource.filter(
              (item) => item.attId != row.attId
            );
          }
          if (row.attType == 27) {
            this.standingCommitteeDataSource = this.standingCommitteeDataSource.filter(
              (item) => item.attId != row.attId
            );
          }
          if (row.attType == 28) {
            this.cityHallDataSource = this.cityHallDataSource.filter(
              (item) => item.attId != row.attId
            );
          }
          if (row.attType == 29) {
            this.government = this.government.filter(
              (item) => item.attId != row.attId
            );
          }
          if (row.attType == 30) {
            this.reportResults = this.reportResults.filter(
              (item) => item.attId != row.attId
            );
          }
          if (row.attType == 31) {
            this.remarkDataSource = this.remarkDataSource.filter(
              (item) => item.attId != row.attId
            );
          }
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 上传文件
    upLoadFileList (fileList, dataSourceString, type) {
      if (this[fileList].length == 0) {
        this.$message.destroy();
        return this.$message.error("请选择文件！");
      }
      let returnValue = [];
      console.log("🤗🤗🤗, fileList =>", fileList);
      this[fileList].forEach((item) => {
        // 上传文件
        returnValue.push(this.upLoadEvent(item, dataSourceString, type));
      });
      Promise.all(returnValue).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.every((item) => item === true)) {
          this[fileList] = [];
        }
      });
    },
    // 上传文件操作
    async upLoadEvent (item, dataSourceString, type) {
      let formData = new FormData();
      formData.append("file", item.originFileObj);
      formData.append("type", type);
      formData.append("relId", this.proposalId);
      let res = await fileUpload(formData);
      if (res.data.code == 200) {
        this[dataSourceString].push({
          attId: res.data.data[0].attId,
          attSize: res.data.data[0].attSize,
          createTime: res.data.data[0].createTime,
          attName: res.data.data[0].attName,
          attSuffix: res.data.data[0].attSuffix,
          attUnit: res.data.data[0].attUnit,
          attType: type,
        });
        return true;
      } else {
        return false;
      }
    },
    // 保存备注
    saveRemarkEvent () {
      this.remarkForm.proposalId = this.proposalId;
      saveRemark(this.remarkForm).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.showDesc();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 校核弹窗 关闭
    cancelReturn () {
      this.returnValue = false;
      this.resourceForm.comment = "";
    },
    // 正式提交
    formallySubmit () {
      this.$router.push({
        path: "/meSuggestv/addSuggestv",
        query: {
          showUser: false,
          proposalId: this.proposalId,
        },
      });
    },
    // 取消调整
    cancelUnitModal () {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "如果关闭当前页面，将会丢失已编辑的承办单位，确认要关闭?",
        onOk: () => {
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 申请调整
    adjust () {
      this.$refs.adjustListEdit.modalVisible = true;
      this.$refs.adjustListEdit.proposalId = this.proposalId;
    },
    // 申请延期 保存
    adjustSubmit () {
      let form = {};
      form.comment = this.delayForm.comment;
      form.delayDay = this.delayForm.delayDay;
      form.proposalId = this.proposalId;
      this.$refs["delayForm"].validate((valid) => {
        if (valid) {
          // 申请延期
          applyDelay(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.cancelModal();
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 关闭窗口
    cancelModal () {
      this.delayVisible = false;
      this.delayForm = { delayDay: "", comment: "" };
      this.delayFileTextList = [];
    },
    // 申请延期
    postpone () {
      this.delayVisible = true;
    },
    // 处理联名
    changeJoin (status) {
      let form = {};
      form.jointlyid = this.jointlyId;
      form.state = status;
      doSignedSave(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作完成");
          this.visible = false;
          this.$parent.$parent.fetchData();
        } else {
          this.$message.error(res.data.data);
        }
      });
    },
    // 下载建议纸
    downMotion () {
      let downFile = (res) => {
        let a = window.document.createElement("a");
        let resData = URL.createObjectURL(res.data);
        a.href = resData;
        let proposalNum = this.record.proposalNum
          ? this.record.proposalNum
          : "";
        let fileName = proposalNum + this.record.proposalTitle + "建议纸.doc";
        a.download = fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(resData);
      };
      downloadProposal({ id: this.proposalId, type: 10 }).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        let errmsg = "";
        if (res.headers.errmsg) {
          errmsg = this.$str2utf8(res.headers.errmsg);
          return this.$message.error(errmsg);
        } else {
          downFile(res);
        }
      });
    },

    //重新上传正文附件 关闭
    closeFileModal () {
      this.fileVisible = false;
      this.fileTextListReset = [];
      this.delayFileTextList = [];
      this.resourceForm = {
        meetName: "",
        meet: "",
        host: "",
        hostName: "",
        adjust: "",
        adjustName: "",
        isPublic: "",
        dffs: [],
        jbrName: "",
        jbrDept: "",
        jbrJob: "",
        jbrPhone: "",
        jbrOph: "",
      };
      this.fileTextList = [];
      this.fileTextList_7 = [];
      this.fileTextList_8 = [];
    },
    //重新上传正文附件 保存
    resetSubmit () {
      let form = {};
      form.proposalId = this.proposalId;
      form.type = "9";
      form.encrypted = false;
      form.file = this.resourceForm.file;
      againUploadTextFile(form).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.$message.success("操作完成");
          this.closeFileModal();
        } else {
          this.$message.error(res.data.data);
        }
      });
    },
    //重新上传正文附件 删除
    handleTextRemoveReset (file, dataSting) {
      if (dataSting == "remarkFile") {
        this[dataSting] = [];
      }
      this[dataSting] = this[dataSting].filter((item) => item.uid != file.uid);
      this.$baseMessage(`删除成功`, "success");
    },

    //重新上传正文附件 上传前
    beforeUploadReset (file) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      // var reg = /^.+(docx|doc)$/;
      // const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      // if (!isDoc) {
      //   this.$message.error("上传的文件格式只能是doc或docx");
      //   return;
      // }
      return false;
    },
    // 重新上传正文附件
    resetFile () {
      // 打开上传文件窗口
      this.fileVisible = true;
    },
    // 重新交办
    resetManage () {
      if (this.unitModalVisibleDataSource.length == 0) {
        return this.$message.error("承办单位不能为空");
      }
      let form = {};
      let undertakeList = [];
      this.unitModalVisibleDataSource.map((item) => {
        undertakeList.push({
          ifmain: item.ifmain,
          orgCode: item.orgCode,
          orgName: item.orgName,
          undertakeId: item.undertakeId || "",
          proposalId: this.proposalId,
        });
      });
      form.undertakeList = undertakeList;
      form.proposalId = this.proposalId;
      form.contentType = this.record.proposalContentType;
      form.proposalType = this.record.proposalType;
      doRevise(undertakeList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 单位调整弹出框 新增 打开
    openChange () {
      this.changeIfMainVisible = true;
      this.changeIfMainTitle = "添加承办单位";
    },
    // 单位调整弹出框 删除
    unitModalVisibleDel () {
      if (this.unitModalVisibleSelectedRows.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.unitModalVisibleSelectedRows.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      // 单位调整-删除逻辑
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定删除？",
        onOk: () => {
          this.unitModalVisibleDataSource = this.unitModalVisibleDataSource.filter(
            (item) =>
              item.orgCode != this.unitModalVisibleSelectedRows[0].orgCode
          );
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 单位调整弹出框 修改主办/会办 保存
    saveChangeIfMain () {
      if (this.changeIfMainTitle == "修改承办类别") {
        this.unitModalVisibleSelectedRows.forEach((item) => {
          this.unitModalVisibleDataSource.map((v) => {
            if (v.orgCode == item.orgCode) {
              v.ifmain = this.changeIfMainForm.ifmain;
            }
          });
        });

        this.changeIfMainVisible = false;
        this.$forceUpdate();
        delete this.changeIfMainForm.ifmain;
        this.unitModalVisibleSelectedRows = [];
      } else {
        // 新增
        this.changeIfMainForm.rows.forEach((item) => {
          item.ifmain = this.changeIfMainForm.ifmain;
          item.orgName = item.orgName;
          item.jbrName = item.orgDispname;
          item.jbrPhone = item.orgDispphone;
          item.jbrOph = item.orgDisptelephone;
        });
        let orgCodeList = this.unitModalVisibleDataSource.map(
          (item) => item.orgCode
        );
        let rowsOrgCode = this.changeIfMainForm.rows.map(
          (item) => item.orgCode
        );
        for (let index = 0; index < rowsOrgCode.length; index++) {
          const element = rowsOrgCode[index];
          if (orgCodeList.includes(element)) {
            return this.$message.error("单位已存在！");
          }
        }

        this.unitModalVisibleDataSource = this.unitModalVisibleDataSource.concat(
          this.changeIfMainForm.rows
        );
        this.changeIfMainForm = {};
        this.changeIfMainVisible = false;
      }
    },
    // 单位调整弹出框 修改主办/会办
    changeIfMain () {
      if (this.unitModalVisibleSelectedRows.length == 0) {
        return this.$message.error("请选择数据");
      }
      this.changeIfMainVisible = true;
      this.changeIfMainTitle = "修改承办类别";
    },
    // 单位调整弹出框 确定保存
    saveUnitModal () {
      let form = {};
      let undertakeList = [];
      this.unitModalVisibleDataSource.map((item) => {
        undertakeList.push({
          ifmain: item.ifmain,
          orgCode: item.orgCode,
          orgName: item.orgName,
          undertakeId: item.undertakeId || "",
          proposalId: this.proposalId,
        });
      });
      form.undertakeList = undertakeList;
      form.proposalId = this.proposalId;
      form.contentType = this.record.proposalContentType;
      form.proposalType = this.record.proposalType;
      doRevise(undertakeList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 下载
    downFileDataDf (itemdf) {
      console.log("🤗🤗🤗, row =>", row);
      let form = {};
      form.relId = itemdf.fujianList[0].relId;
      form.type = itemdf.fujianList[0].attType;
      downFile(form).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `${itemdf.fujianList[0].attName}.${itemdf.fujianList[0].attSuffix}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 下载附件
    downFileData (type, relId) {
      let form = { relIdList: [] };
      if (type == 2) {
        form.relId = relId;
      } else {
        form.relId = this.proposalId;
      }
      let name = "";
      if (type == "1") {
        name = "附件.zip";
      }
      if (type == "2") {
        name = "答复附件.pdf";
      }
      if (type == "9") {
        name = "正文附件.docx";
      }
      form.type = type;
      console.log("🤗🤗🤗, form =>", form);
      downFile(form).then((res) => {
        console.log("🤗🤗🤗, res =>", res, 'headers', res.headers, res.headers?.['content-disposition']);
        // let filename = res.headers?.['content-disposition']?.split(';')[1].split("filename=")[1];
        // if (type == "1" && filename) {
        //   let hz = filename?.substr(filename.lastIndexOf('.') + 1);
        //   name = "附件." + hz;
        // }
        if (type == "1") {
          let fjlis = this.record.attachmentList.filter((i) => i.attType == 1);
          if (fjlis.length == 1) {
            name = "附件." + fjlis[0].attSuffix;
          }
        }
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `${this.record.proposalTitle}${name}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 查看正文
    lookWord () {
      seeText(this.proposalId).then((res) => {
        if (res.data.code == 200 && res.data.data) {
          let url = this.GLOBAL.basePath_3 + "/" + res.data.data.url;
          window.open(url);
        } else {
          this.$message.error("请求出错");
        }
      });
    },
    // 打开反馈
    openEvaluationModal () {
      this.$refs.evaluationModal.proposalId = this.proposalId;
      this.$refs.evaluationModal.getFeedUndertakeList();
      this.$refs.evaluationModal.modalVisible = true;
    },

    // 打开单位调整弹出框
    openUnitModalVisible () {
      this.unitModalVisible = true;
      getUndertakeInfo(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitModalVisibleDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 维持原交办
    maintainAPrimaryOffice () {
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.visiblePostpone = true;
      this.modalTitle = "维持原交办";
    },
    // 处理延期/调整/维持原交办 -弹窗保存
    submitDelay () {
      let form = {};
      form.comment = this.resourceForm.comment;
      form.undertakeId = this.selectedRowsPostpone[0].undertakeId;
      if (this.modalTitle == "处理延期") {
        form.delayDay = this.resourceForm.delayDay;
        doDelay(form).then((res) => {
          if (res.data.code == 200) {
            this.$message.success("操作成功");
            this.resourceForm.comment = "";
            this.getASubflow();
            this.visiblePostpone = false;
          } else {
            this.$message.error(res.data.message);
          }
        });
      } else if (this.modalTitle == "维持原交办") {
        if (this.keepType == "revise") {
          keepRevise(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.resourceForm.comment = "";
              this.getAdjust();
              this.visiblePostpone = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        }
        if (this.keepType == "delay") {
          keepDelay(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.getASubflow();
              this.visiblePostpone = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        }
      }
    },
    // 处理延期-事件
    treatmentDelay () {
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.visiblePostpone = true;
      this.modalTitle = "处理延期";
      this.resourceForm.delayDay = this.selectedRowsPostpone[0].delayTimeTo;
    },
    // 处理延期-获取子流程 父组件调用
    getASubflow () {
      getDelayUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "delay";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 处理调整-获取子流程 父组件调用
    getAdjust () {
      getReviseUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "revise";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 上传之前
    beforeUpload (file, fileList) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10M!");
      }
      // var reg = /^.+(docx|doc)$/;
      // const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      // if (!isDoc) {
      //   this.$message.error("上传的文件格式只能是doc或docx");
      //   return;
      // }
      return false;
    },
    // 答复 上传删除
    handleTextRemove (file) {
      this.resourceForm.fuJianIds = [];
      this.fileTextList = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 上传操作
    uploadChange (val, dataString, type) {
      // 26、代表大会审议处理信息   27、常委会审议处理信息   28、市政府（两院）信息   29、政府办理进度  30、政府办理结果   31、议案备注附件
      if (val.file.status == "removed") return;
      this[dataString] = val.fileList;
    },
    // 答复
    replyData (type) {
      let forumFile = "";
      let communFile = "";
      this.$refs["resourceForm"].validate((valid) => {
        console.log("🤗🤗🤗, valid =>", valid);
        if (valid) {
          let jbrData = {};
          jbrData.jbrName = this.resourceForm.jbrName;
          jbrData.jbrDept = this.resourceForm.jbrDept;
          jbrData.jbrJob = this.resourceForm.jbrJob;
          jbrData.jbrOph = this.resourceForm.jbrOph;
          jbrData.jbrPhone = this.resourceForm.jbrPhone;
          jbrData.proposalId = this.proposalId;

          //先更新经办人信息
          updateJbr(jbrData).then((res) => {
            if (res.data.code != 200) {
              this.$message.error(res.data.message);
              return false;
            }
          });

          if (
            this.resourceForm.forumFile &&
            this.resourceForm.forumFile.length > 0
          ) {
            forumFile = this.resourceForm.forumFile.toString();
          }
          if (
            this.resourceForm.communFile &&
            this.resourceForm.communFile.length > 0
          ) {
            communFile = this.resourceForm.communFile.toString();
          }
          let form = {};
          // form.dfr = this.userData.username;
          // form.dfrId = this.userData.userId;
          form.actType = type;
          form.dflb = this.resourceForm.dflb;
          form.dffs = this.resourceForm.dffs.toString();
          form.isPublic = this.resourceForm.isPublic;
          form.remark = this.resourceForm.remark;
          form.xtpfList = this.hbDataSource;

          if (this.resourceForm.fuJianIds != undefined) {
            form.fuJianIds += this.resourceForm.fuJianIds.toString();
          }
          if (communFile != "") {
            form.fuJianIds += "," + communFile;
          }
          if (forumFile != "") {
            form.fuJianIds += "," + forumFile;
          }

          doReply(form, this.proposalId).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.visible = false;
              this.fileTextList = [];
              this.close();
              this.$parent.$parent.fetchData();
            } else {
              this.$message.error(res.data.message);
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 重新生成正文
    // regenerateText() {
    //   againProduce(this.proposalId).then((res) => {
    //     console.log("重新生成正文🤗🤗🤗, res =>", res);
    //     if (res.data.code == 200) {
    //       this.$message.success(res.data.message);
    //     } else {
    //       this.$message.error(res.data.message);
    //     }
    //   });
    // },
    // 删除提出代表
    delJointlyList () {
      if (this.deputationSelectedRows.length == 0) {
        return this.$message.error("请选择数据！");
      }
      let jointlyIdsList = [];
      var allifpublic = false;//是否为领衔代表
      this.deputationSelectedRows.map((item) => {
        if (item.ifpublic != '1') {
          jointlyIdsList.push({ jointlyIds: item.jointlyId });
        }
        else {
          allifpublic = true;
        }
      });

      delJointly(jointlyIdsList).then((res) => {
        if (res.data.code == 200) {
          if (allifpublic) { this.$message.success('领衔代表不能被删除'); }
          else {
            this.$message.success(res.data.message);
          }

          this.deputationSelectedRows = []
          this.getJointlyListData();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 接受提出代表
    getJoinTable (rows) {
      let alreadyDBli = [];//找出已添加用户数组
      let addlist = [];//可添加的代表
      let list = this.deputationDataSource.map((i) => i.operId)
      alreadyDBli = rows.filter((item) => list.includes(item.userId));
      addlist = rows.filter((item) => !list.includes(item.userId));
      // 判断当前选择人的类型
      if (this.perType == "提出") {
        let form = {
          operids: addlist.map((item) => item.userId).toString(),
          proposalId: this.proposalId,
        };
        // 新增
        addJointly(form).then((res) => {
          if (res.data.code == 200) {
            if (alreadyDBli.length != 0) {
              // 提示已经添加不可添加
              let nameli = alreadyDBli.map((i) => i.name);
              this.$message.success(`${nameli} 代表已经添加过了`);
            } else {
              this.$message.success(res.data.message);
            }
            // this.$message.success(res.data.message);
            this.getJointlyListData();
          } else {
            this.$message.error(res.data.message);
          }
        });
        this.disableSelectedRowKeylist = []
      } else {
        console.log("🤗🤗🤗 接受提出代表-答复, rows =>", rows);
        this.resourceForm = Object.assign({}, this.resourceForm, rows[0]);
      }
    },
    // 打开提出代表进行新增/答复选择经办人
    showUserTable (type) {
      this.perType = type;
      // this.disableSelectedRowKeylist = this.deputationDataSource.map((i) => i.userId);
      this.$refs.commonUserTable.jointTableVisible = true;
    },
    // 打开选择经办人
    showJbrTable (orgCode) {
      this.$refs.jbrTable.queryForm.jbrName = "";
      this.$refs.jbrTable.queryForm.jbrPhone = "";
      this.$refs.jbrTable.queryForm.orgCode = orgCode;
      this.$refs.jbrTable.showDesc();
      this.$refs.jbrTable.jbrTableVisible = true;
    },
    // 接受提出代表
    getJbrTable (row) {
      this.resourceForm.jbrName = row.jbrName;
      this.resourceForm.jbrDept = row.jbrDept;
      this.resourceForm.jbrJob = row.jbrJob;
      this.resourceForm.jbrOph = row.jbrOph;
      this.resourceForm.jbrPhone = row.jbrPhone;
    },
    // tab点击
    changeTab (key) {
      // 获取提出代表
      if (key == "3") {
        // this.getJointlyListData();
      }
      // 流程跟踪
      if (key == "5") {
        // this.getLocalHistoryData();
      }
      // 获取议案建议的承办单位
      if (key == "4") {
      }
      // 获取反馈答复列表
      if (key == "2") {
        // this.feedbackReply();
      }
    },
    // 获取反馈答复列表、
    feedbackReply () {
      getUndertakeDfAndFeedbackList(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitDataSource = [];
          this.majorUndertakeDfList = [];
          this.minorUndertakeDfList = [];
          if (res.data.data != undefined && res.data.data.length > 0) {
            res.data.data.forEach((item) => {
              this.unitDataSource.push(item);
              if (item.ifmain == 1) {
                this.majorLastTime = item.handleLastTime;
                this.majorUndertakeDfList.push(item);
              } else {
                this.minorLastTime = item.handleLastTime;
                this.minorUndertakeDfList.push(item);
              }
            });
          }
        } else {
          // this.$message.error(res.data.message);
        }
        this.$forceUpdate();
      });
    },

    // 获取提出代表
    getJointlyListData () {
      getJointlyList(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.deputationDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 提交校核 打开
    openModal (status) {
      this.returnValue = true;
      if (status == "pass") {
        this.returnValueTitle = "校核意见";
      } else {
        this.returnValueTitle = "退回意见";
      }
    },
    // 提交校核
    submitTheSubject (status) {
      let form = {};
      form.proposalIds = this.proposalId;
      form.actType = status;
      form.comment = this.resourceForm.comment;
      //提交校核 通过
      let content = "";
      if (this.returnValueTitle == "校核意见") {
        content = "是否确定校核通过？";
      } else {
        content = "是否确定退回？";
      }
      //提交校核 退回
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: content,
        onOk: () => {
          doCheck(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.$parent.$parent.fetchData();
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment: "",
              };
              this.visible = false;
              this.returnValue = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 提交审核
    submitReview (status) {
      this.resourceForm.proposalIds = this.proposalId;
      this.resourceForm.actType = status;
      let tips = "pass" == status ? "是否确定审核通过？" : "是否确定退回？";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: tips,
        onOk: () => {
          doAudit(this.resourceForm).then((res) => {
            console.log("🤗🤗🤗, res =>", res);
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.$parent.$parent.fetchData();
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
              };
              this.visible = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          // this.$message.info("您已取消操作！");
        },
      });
    },
    // 关闭窗口
    close () {
      this.visible = false;
      this.showOperate = true;
      for (const key in this.$data) {
        if (!Array.isArray(this.$data[key])) {
          this[key] = this.$options.data()[key];
        }
      }
      this.fileTextList_8 = [];
      this.fileTextList_7 = [];
      this.cityHallDataSource = [];
      this.standingCommitteeDataSource = [];
      this.congressDataSource = [];
      this.reportResults = [];
      this.congressFile = [];
      this.standingCommitteeFile = [];
      this.cityHallFile = [];
      this.governmentFile = [];
      this.government = [];
      this.reportResultsFile = [];
      this.remarkFile = [];
      this.$parent.$parent.fetchData();
    },
    // 退回
    back () { },
    // 交办-提交交办
    manage () {
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }

      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定交办？",
        onOk: () => {
          let hostData = [];
          let meetData = [];
          this.resourceForm.host.map((item) => {
            hostData.push({
              ifmain: 1,
              orgCode: item.orgCode,
              orgName: item.orgName,
              proposalId: this.proposalId,
            });
          });
          if (this.resourceForm.meet.length > 0) {
            this.resourceForm.meet.map((item) => {
              meetData.push({
                ifmain: 0,
                orgCode: item.orgCode,
                orgName: item.orgName,
                proposalId: this.proposalId,
              });
            });
          }
          let undertakeList = [];
          undertakeList = hostData.concat(meetData);
          let newForm = {};
          newForm.undertakeList = undertakeList;
          newForm.proposalId = this.proposalId;
          newForm.contentType = this.resourceForm.contentType || "";
          newForm.proposalType = this.record.proposalType;
          newForm.minorHandleLastTimeStr = this.resourceForm.minorHandleLastTimeStr;
          newForm.mainHandleLastTimeStr = this.resourceForm.mainHandleLastTimeStr;
          doAssign(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("交办成功");
              // 交办后重新获取数据，显示答复
              this.$parent.$parent.fetchData();
              Object.assign(this.$data, this.$options.data());
              this.showDesc();
              // this.visible = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
      });
    },
    //转为参考建议
    changeRefer () {
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
      let hostData = [];
      let meetData = [];
      this.resourceForm.host.map((item) => {
        hostData.push({
          ifmain: 1,
          orgCode: item.orgCode,
          orgName: item.orgName,
        });
      });
      if (this.resourceForm.meet.length > 0) {
        this.resourceForm.meet.map((item) => {
          meetData.push({
            ifmain: 0,
            orgCode: item.orgCode,
            orgName: item.orgName,
          });
        });
      }
      let undertakeList = [];
      undertakeList = hostData.concat(meetData);
      let newForm = {};
      newForm.undertakeList = undertakeList;
      newForm.proposalId = this.proposalId;
      newForm.contentType = this.resourceForm.contentType || "";
      newForm.proposalType = "4";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "确认要转为供参考建议？",
        onOk: () => {
          doAssign(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("转为参考建议成功");
              this.$parent.$parent.fetchData();
              this.visible = false;
              Object.assign(this.$data, this.$options.data());
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 交办-保存
    save () {
      let newForm = { params: {}, data: {} };
      newForm.params.contentType = this.resourceForm.contentType || "";
      newForm.params.proposalType = this.record.proposalType;
      newForm.params.proposalId = this.proposalId;
      newForm.params.mainHandleLastTimeStr = this.resourceForm.mainHandleLastTimeStr;
      newForm.params.minorHandleLastTimeStr = this.resourceForm.minorHandleLastTimeStr;
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
      let hostData = [];
      let meetData = [];
      this.resourceForm.host.map((item) => {
        hostData.push({
          ifmain: 1,
          orgCode: item.orgCode,
          orgName: item.orgName,
          proposalId: this.proposalId,
        });
      });
      if (this.resourceForm.meet.length > 0) {
        this.resourceForm.meet.map((item) => {
          meetData.push({
            ifmain: 0,
            orgCode: item.orgCode,
            orgName: item.orgName,
            proposalId: this.proposalId,
          });
        });
      }
      let data = [];
      data = hostData.concat(meetData);
      newForm.data = data;
      saveCBDW(newForm).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("保存成功");
          this.$parent.$parent.fetchData();
          this.visible = false;
          Object.assign(this.$data, this.$options.data());
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 打开单位table
    openUnitTable (type) {
      // 再次点击 设置默认值
      if (this.resourceForm[type]) {
        this.defaultUnit = this.resourceForm[type].map((item) => item.orgCode);
      } else {
        this.defaultUnit = [];
      }
      this.unitType = type;
      this.$refs.commonUnitShuttleTable.unitTableVisible = true;
    },
    // 接受单位
    getUnitTable (rows) {
      // 交办选择主/会办单位
      if (this.unitType == "meet" || this.unitType == "host") {
        let name = rows.map((item) => item.orgName).toString();
        this.resourceForm[this.unitType] = rows;
        this.resourceForm[this.unitType + "Name"] = name;
        this.defaultUnit = rows.map((item) => item.orgCode);
      } else if (this.unitType == "单位调整") {
        console.log("🤗🤗🤗, rows =>", rows);
        this.changeIfMainForm.orgName = "";
        this.changeIfMainForm.orgName = rows
          .map((item) => item.orgName)
          .toString();
        // 储存
        this.changeIfMainForm.rows = [];
        this.changeIfMainForm.rows = rows;
        this.changeIfMainVisible = true;
        this.$forceUpdate();
      }
    },
    // 获取当前登陆人是否有上传文件的权限
    isYiAnCanUploadData () {
      isYiAnCanUpload({ proposalId: this.proposalId }).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        this.isShowUpload = res.data.data == "Y" ? true : false;
      });
    },
    // 获取信息
    async showDesc () {
      // 获取当前登陆人是否有上传文件的权限
      this.isYiAnCanUploadData();
      let res = await proposalById({ proposalId: this.proposalId });
      if (res.data.code == 200) {
        this.record = res.data.data;
        this.resourceForm.contentType = this.record.proposalContentType;
        // 备注回显
        this.remarkForm.remarkContent = this.record.remarkContent || "";
        this.remarkForm.remakeTime = "";
        this.remarkForm.remakeTime = this.record.remakeTime
          ? this.record.remakeTime.slice(0, 10)
          : "";
        this.remarkForm.remakeTitle = this.record.remakeTitle
          ? this.record.remakeTitle
          : "";
        // 文件回显
        let attachmentList = this.record.attachmentList;
        this.congressDataSource = attachmentList.filter(
          (item) => item.attType == 26
        );
        // 承办单位数据
        console.log(
          "🤗🤗🤗, this.record.undertakeDTOList =>",
          this.record.undertakeDTOList
        );
        if (this.record.undertakeDTOList) {
          this.unitDataSource = this.record.undertakeDTOList;
        }
        this.standingCommitteeDataSource = attachmentList.filter(
          (item) => item.attType == 27
        );
        this.cityHallDataSource = attachmentList.filter(
          (item) => item.attType == 28
        );
        this.government = attachmentList.filter((item) => item.attType == 29);
        this.reportResults = attachmentList.filter(
          (item) => item.attType == 30
        );
        this.remarkDataSource = attachmentList.filter(
          (item) => item.attType == 31
        );
        this.$forceUpdate();

        //获取代表数据
        this.getJointlyListData();
      } else {
        this.$message.error(res.data.message);
      }
      // 获取登录人数据
      this.getOneUser();
      // 获取子流程状态值
      // this.getUndertakeStatusData();
    },
    // 获取登录人数据
    getOneUser () {
      getUserData_yajy().then((res) => {
        if (res.data.code == 200) {
          // 获取登录人的权限
          this.userData = res.data.data;
          console.log("🤗🤗🤗, this.userData =>", this.userData);
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 答复时获取经办信息
    getJbrData () {
      getJbr(this.proposalId).then((res) => {
        console.log("🤗🤗🤗, res =>答复时获取经办信息", res);
        if (res.data.code == 200) {
          this.resourceForm.jbrName = res.data.data[0].jbrName;
          this.resourceForm.jbrDept = res.data.data[0].jbrDept;
          this.resourceForm.jbrJob = res.data.data[0].jbrJob;
          this.resourceForm.jbrPhone = res.data.data[0].jbrPhone;
          this.resourceForm.jbrOph = res.data.data[0].jbrOph;
        }
      });
    },

    viewFkEvaluation (data) {
      if (data) {
        this.fkContent = data.gdjy;
        if (data.evaluationValue == 1) {
          this.fkEvaluation = "不满意";
        }
        if (data.evaluationValue == 2) {
          this.fkEvaluation = "基本满意";
        }
        if (data.evaluationValue == 3) {
          this.fkEvaluation = "满意";
        }
        this.fkVisible = true;
      }
    },
    closeFkVisible () {
      this.fkContent = "";
      this.fkEvaluation = "";
      this.fkVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.fileBox {
  position: relative;
  margin-bottom: 10px;
  .fileBoxUpload {
  }
  .fileBoxFileText {
    position: absolute;
    left: 100px;
    top: 1px;
  }
}
</style>
