<template>
  <a-modal title="新增笔记" :visible.sync="visible" width="80%" @cancel="close">
    <a-card title="笔记内容" >
      <a-form-model ref="detailsForm"
       :rules="rules"
       :model="detailsForm"
       >
        <a-row span="24">
          <a-col span="24">
             <a-form-model-item prop="title"> 
               <a-input v-model="detailsForm.title"
               autocomplete="off"
                placeholder="笔记标题" />
             </a-form-model-item>
          </a-col>

          <a-col span="24">
            <a-form-model-item prop="content">
              <a-textarea
                placeholder="笔记内容"
                autocomplete="off"
                v-model="detailsForm.content"
                rows="8"
                allow-clear
              ></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>
    <!-- 定义了插槽 -->
    <slot slot="footer" name="userFooter">
      <a-button type="primary" @click="save">保存</a-button>
      <a-button style="margin-left: 12px;" @click="tomyNotes">我的笔记</a-button>
      <a-button style="margin-left: 12px;" @click="close">关闭</a-button>
    </slot>
   <!-- <noted ref="noted"></noted> -->
  </a-modal>
</template>
<script>
// import noted from "./noted";
import { instance_1 } from "@/api/axiosRq";
export default {
  name: "notes",
    // components: { noted},
  data() {
    return { 
      visible: false,
      contentId:'',
      detailsForm: {},
      rules:{
         title: [{ required: true, message: "请输入标题", trigger: "blur" }],
        content: [{ required: true, message: "请输入内容", trigger: "blur" }],
      }
    };
  },
  created() {},
  methods: { 
    // 保存
    save() { 
      this.$refs.detailsForm.validate(valid => {
        if (valid) {
            this.detailsForm["contentid"]=this.contentId; 
      instance_1({
        url: "home/notice/addNote",
        method: "post", 
        data:this.detailsForm
      }).then((res) => { 
        if(res.data.code=="0000"){
          //  alert("保存成功") 
           this.$baseMessage(`保存成功`, "success");
          this.visible = false;
        }else if(res.data.code=="1111"){
          // alert("笔记已存在！")
          this.$baseMessage(`笔记已存在！`, "success");
          this.visible=false
        }
        this.detailsForm={}
       
      })
        } else {
        
          return false;
        }
      });
   
 
    },
    //我的笔记
    tomyNotes() {  
      // this.visible=false;    //加了会报错 
      this.$emit("notes",this.contentId)
        //   this.$refs.noted.contentId=this.contentId, 
        //    this.$refs.noted.fetchData();
        // this.$refs.noted.visible = true; 
    },
    // 关闭窗口
    close() {
      this.visible = false; 
    },
  },
};
</script>
<style scoped>
.formBox {
  padding: 20px;
}
</style>
