<template>
  <div>
    <a-col :span="spanNum" :class="{ 'overLength': title.length > 5 && spanNum <= 8 }">
      <a-form-model-item :label="title" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-date-picker style="width: 100%;"
          :disabled="disabled"
          v-model="chooseTime"
          allow-clear
          value-format="YYYY-MM-DD"
          :placeholder="'请选择'+ title"
          @change="onDateChange"
        />
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
import moment from 'moment';
import { log } from 'console';

export default {
  props: {
    title: {
      type: String,
      default: () => "",
      required: true,
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    timeValue: {
      type: [String, Number],
      default: () => '',
    }
  },
  data() {
    return {
      chooseTime: null,
    };
  },
  watch: {   
    timeValue: {  
      handler(val) {
        console.log(val,'刚进来的val');
        
        this.chooseTime = val;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    onDateChange(val) {
      console.log(val,'选中val数值')
      // this.chooseTime = moment(val).format('YYYY-MM-DD'); 
      // this.$emit('getDateChange', this.chooseTime)
      this.$emit('update:timeValue', val)
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
