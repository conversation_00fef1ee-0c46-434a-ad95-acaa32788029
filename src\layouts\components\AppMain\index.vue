<template>
  <section class="app-main-containers">
    <transition mode="out-in" name="fade-transform">
      <!-- <keep-alive :include="cachedViews" :max="10">
        <router-view :key="key" style="min-height: 78vh;" />
      </keep-alive> -->
      <full-path-keep-alive />
    </transition>
  </section>
</template>

<script>
// import { ByuiKeel, ByuiKeelHeading, ByuiKeelText } from "@/plugins/byuiKeel";
import { mapGetters } from "vuex";
import { copyright } from "@/config/settings";
import FullPathKeepAlive from "@/layouts/components/FullPathKeepAlive/FullPathKeepAlive";

export default {
  name: "AppMain",
  components: {
    // ByuiKeel,
    // ByuiKeelHeading,
    // ByuiKeelText,
    FullPathKeepAlive,
  },
  data() {
    return {
      show: true,
      nodeEnv: process.env.NODE_ENV,
      fullYear: new Date().getFullYear(),
      copyright,
    };
  },
  computed: {
    ...mapGetters(["cachedViews", "device"]),
    key() {
      return this.$route.path;
    },
  },
  watch: {
    $route(to, from) {
      this.$nextTick(() => {
        if (this.$store.state.tagsView.skeleton) {
          this.show = true;
          setTimeout(() => {
            this.show = false;
          }, 200);
        } else {
          this.show = false;
        }
        if ("mobile" === this.device) {
          this.$store.dispatch("settings/foldSideBar");
        }
      });
    },
  },
  created() {},
  mounted() {
    setTimeout(() => {
      this.show = false;
    }, 200);
  },
  beforeDestroy() {
    window.localStorage.setItem("tongjifenzi", JSON.stringify(this.queryForm));
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.app-main-containers {
  // padding: 30px 20px 20px 20px;
  background-color: #ffffff;
  border-radius: 6px;
  position: relative;
  width: 100%;
  overflow: hidden;
  .footer-copyright {
    min-height: 70px;
    line-height: 35px;
    color: rgba(0, 0, 0, 0.45);
    text-align: center;
  }
}
// ::v-deep.ant-card-meta-title{
//    @include add-size($font_size_20);
// }
// ::v-deep.ant-card{
//    @include add-size($font_size_20);
// }
</style>

<style lang="scss">
.ant-steps-item-process .ant-steps-item-icon {
  background: #db3046;
  border-color: #db3046;
}
// 自定义搜索框样式
.searchStyle {
  margin-top: 3px;
  display: flex !important;
  .ant-input {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  .ant-btn-primary {
    z-index: 99;
    width: 66px;
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}

// switch
.ant-switch-checked {
  background-color: #d92b3e !important;
}
/* 时间选择 */
.ant-calendar-time-picker-btn {
  color: #505659 !important;
}
.ant-calendar-today-btn {
  color: #505659 !important;
}
.ant-calendar-ok-btn {
  background-color: #d92b3e !important;
  border-color: #d92b3e !important;
  color: #fff !important;
}
.ant-table-tbody
  tr:nth-of-type(1n):hover:not(.ant-modal-content-table-expamded-row):not(
    .ant-table-row-selected
  )
  > td {
  background-color: #fbeeef !important;
}
.ant-table-tbody
  tr:nth-of-type(1n):hover:not(.ant-modal-content-table-expamded-row):not(
    .ant-table-row-selected
  )
  > td {
  background-color: #fbeeef !important;
}
.ant-table-tbody
  tr:nth-of-type(2n):hover:not(.ant-modal-content-table-expamded-row):not(
    .ant-table-row-selected
  )
  > td {
  background-color: #fbeeef !important;
}
.ant-table-tbody > tr.ant-table-row-selected td {
  background-color: #fbeeef !important;
}
.ant-table-tbody
  > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(
    .ant-table-row-selected
  )
  > td {
  background-color: #fbeeef !important;
}

.ant-pagination-item-active {
  color: #fff !important;
  border-color: #d8293a !important;
}

.ant-pagination-item-active a {
  background-color: #d8293a;
  color: #fff !important;
  border: 1px solid #d8293a;
}
.ant-pagination-item:hover {
  border-color: #d8293a !important;
}
.ant-pagination-item a:hover {
  color: #d8293a !important;
}
.ant-pagination-item-active a:hover {
  color: #fff !important;
}
.ant-btn-primary {
  background-color: #d92b3e !important;
  border-color: #d92b3e !important;
}

.ant-btn-primary, .ant-btn-primary:hover {
    background-color: #d92b3e !important;
}

.ant-btn-primary:hover {
  background-color: #d92b3e !important;
}
.ant-btn-background-ghost {
  color: #d92b3e !important;
}
.ant-btn-background-ghost:hover {
  color: white !important;
}
// 分页器样式
.ant-pagination-item-link {
  margin-top: 1px;
  width: 28px !important;
  height: 28px !important;
  line-height: 28px !important;
}
.ant-pagination-item {
  // width: 28px !important;
  height: 28px !important;
  line-height: 28px !important;
}
.ant-pagination-item-active {
  width: 28px !important;
  height: 28px !important;
  line-height: 28px !important;
  min-width: 28px !important;
  border: 0px !important;
}
.ant-pagination-options-quick-jumper {
  height: 28px !important;
  line-height: 28px !important;
}
.ant-pagination-options-quick-jumper input {
  height: 28px !important;
  line-height: 28px !important;
}
.ant-pagination-options-quick-jumper input:hover {
  border-color: #d43030 !important;
  box-shadow: 0 !important;
}
.ant-input:hover {
  // color: #d43030!important;
  border-color: #d43030 !important;
  box-shadow: 0 !important;
}
.ant-pagination-item-link:hover {
  color: #d43030 !important;
  border-color: #d43030 !important;
  box-shadow: 0 !important;
}

.ant-pagination-options {
  .ant-select-selection--single {
    height: 28px !important;
    line-height: 28px !important;
  }
}
.ant-select-selection--single:hover {
  border-color: #d43030 !important;
  box-shadow: 0 !important;
}
.ant-select-selection {
  // border-color: #d43030!important;
  box-shadow: 0 !important;
}
.ant-pagination-item-active a {
  width: 30px !important;
  height: 28px !important;
  line-height: 28px !important;
  border-radius: 4px !important;
}
.overLength {
  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    line-height: 26px;
    label {
      white-space: normal;
      text-align: center;
      &:after {
        content: ":" !important; //解决上面的样式label后面会有空格
      }
    }
  }
}
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 12px;
  background-color: #f7f7f7;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px #f7f7f7;
  border-radius: 10px;
  // background-color: #0f5a81;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  box-shadow: inset 0 0 0px #dfdfdf;
  background-color: #dfdfdf;
}
.content {
  margin-bottom: 80px;
}

// 表格全局
.ant-table-column-title {
  font-family: pingFang-M;
  @include add-size($font_size_16);
}
.ant-table-row {
  font-family: pingFang-R;
  @include add-size($font_size_16);
}
.ant-form {
  @include add-size($font_size_16);
}
// 二级标题 （表头，小标题等 ）
.ant-collapse-header,
.ant-form-item-label {
  font-family: pingFang-M;
  @include add-size($font_size_16);
}
.ant-form label {
  @include add-size($font_size_16);
}
.ant-pagination-total-text {
  @include add-size($font_size_16);
}
.ant-select-selection-selected-value {
  @include add-size($font_size_16);
}
.ant-tree-title {
  @include add-size($font_size_16);
}
.ant-tree {
  @include add-size($font_size_16);
}
// 常规内容
.ant-input,
.ant-descriptions-item-content,
.ant-btn {
  font-family: pingFang-R;
  @include add-size($font_size_16);
}
// select下拉框菜单样式
.ant-select-dropdown-menu {
  // background-color: #384057 !important;
  font-family: pingFang-R;
  @include add-size($font_size_16);
  .ant-select-dropdown-menu {
    background: #384057;
    border: solid 1px #384057;
  }
  .ant-select-dropdown-menu-item {
    @include add-size($font_size_16);
    // color: #fff !important;
  }
  .ant-select-dropdown-menu-item:hover {
    background: #fbeeef !important;
  }
  .ant-select-dropdown-menu-item-selected {
    // background: #384057 !important;
  }
  .ant-select-dropdown-menu-item-active {
    background: #fbeeef !important;
  }
}

// 单个日期选择器样式
.ant-calendar-today .ant-calendar-date {
  background: #fbeeef;
  color: #505659;
  border-color: #fbeeef !important;
}
.ant-calendar-cell .ant-calendar-date:hover {
  background: #fbeeef;
}
.ant-calendar-selected-day .ant-calendar-date {
  background-color: #d92b3e !important;
  border-radius: 12px;
  color: #fff;
}

.ant-calendar-time-picker-combobox ::-webkit-scrollbar {
  // background-color: #394158 !important;
}
.ant-calendar-table {
  // color: #b4bac3;
}
.ant-calendar-header {
  border-bottom: 0px !important;
}
.ant-calendar-time-picker-select
  .ant-calendar-time-picker-select-option-selected {
  background-color: #fbeeef;
  color: #505659;
}
.ant-calendar-time-picker-select li:hover {
  background-color: #fbeeef;
  color: #505659;
}
.ant-calendar-time-picker-inner,
.ant-calendar-time-picker-select-option-selected {
  // background-color: #394158;
  color: #020918;
}
.ant-calendar-year-select,
.ant-calendar-month-select,
.ant-calendar-day-select {
  color: #fff;
}
.ant-calendar-input {
  // color: #a1c1d9;
  // background-color: #394158;
  text-align: center;
}
.ant-calendar-range-middle {
  padding: 0px 10px 0 10px !important;
  // color: #151616;
}
.ant-calendar {
  // background-color: #394158;
  // border: 1px solid #0f314d;
}
.ant-calendar-body,
.ant-calendar-footer {
  // border-top: 1px solid #658397;
}
.ant-calendar-input-wrap {
  border-bottom: 0px;
}
.ant-calendar-date {
  // color: #a1c1d9;
}
.ant-calendar-in-range-cell::before {
  background-color: #394158;
}
.ant-calendar-ok-btn-disabled {
  // color: #fff;
  border: 0px;
  // background-color: #1890ff;
}
// 选择
.ant-calendar-time-picker-combobox {
  // background-color: #fbeeef !important;
  border-top: 1px solid #fbeeef !important;
  /* 定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 4px;
    border-radius: 10px;
    background-color: #f7f7f7;
  }
  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 0px #f7f7f7;
    border-radius: 10px;
    // background-color: #0f5a81;
  }
  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 0px #dfdfdf;
    background-color: #dfdfdf;
  }
}
.ant-calendar-picker-clear:hover {
  // color: #a6c5db;
  // background-color: #0f314d !important;
}

.anticon-inbox svg {
  color: #d92b3e;
}
// 多选框
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #d92b3e !important;
  border-color: #d92b3e !important;
}
.ant-checkbox-wrapper-checked .ant-checkbox-checked:hover {
  border-color: #d92b3e !important;
}
.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: #d92b3e !important;
}
.ant-checkbox-indeterminate .ant-checkbox-inner::after {
  background-color: #d92b3e;
}
// 树
.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
  background-color: #d92b3e;
}
.ant-tree-checkbox-checked .ant-tree-checkbox-inner {
  background-color: #d92b3e !important;
  border-color: #d92b3e !important;
}
.ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-inner,
.ant-tree-checkbox:hover .ant-tree-checkbox-inner,
.ant-tree-checkbox-input:focus + .ant-tree-checkbox-inner {
  border-color: #d92b3e !important;
}
.ant-tree-checkbox-checked::after {
  border-color: #d92b3e !important;
}
.ant-checkbox-checked::after {
  border-color: #d92b3e !important;
}
// tabs
.ant-tabs-nav .ant-tabs-tab-active {
  color: #d92b3e !important;
}
.ant-descriptions-view {
  @include add-size($font_size_16);
  .ant-descriptions-row {
    @include add-size($font_size_16);
    .ant-descriptions-item-label {
      @include add-size($font_size_16);

      .ant-descriptions-item-content {
        @include add-size($font_size_16);

        .ant-descriptions-item-label {
          @include add-size($font_size_16);
        }
      }
    }
  }
}
.ant-tabs-nav-container,
.ant-modal-title {
  @include add-size($font_size_16);
  font-family: pingFang-M;
}
.ant-card {
  @include add-size($font_size_16);
  .ant-card-body {
    @include add-size($font_size_16);
    .ant-card-meta {
      @include add-size($font_size_16);
      .ant-card-meta-detail {
        @include add-size($font_size_16);
        .ant-card-meta-title {
          @include add-size($font_size_16);
        }
      }
    }
  }
}
.ant-tabs {
  @include add-size($font_size_16);
}
.ant-tabs-nav .ant-tabs-tab-active:hover {
  color: #d92b3e !important;
}
.ant-tabs-tab:hover {
  color: #d92b3e !important;
}
.ant-radio-inner::after {
  background-color: #d92b3e !important;
}
.ant-tabs-ink-bar {
  background-color: #d92b3e !important;
}
.ant-tabs-nav .ant-tabs-tab-active {
  color: #d92b3e;
  @include add-size($font_size_16);
  font-family: pingFang-M;
}
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #d92b3e !important;
  border-color: #d92b3e !important;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #d92b3e !important;
  border-color: #d92b3e !important;
}
.el-button--primary {
  background-color: #d92b3e !important;
  border-color: #d92b3e !important;
}
.ant-tag-blue {
  font-family: pingFang-R;
  color: rgba(0, 0, 0, 0.65);
  background: #fbeeef;
  border-color: #fbeeef;
}
.el-tabs__item.is-active {
  color: #d92b3e !important;
}
.el-tabs__active-bar {
  background-color: #d92b3e !important;
}
.ant-tree.ant-tree-directory
  .ant-tree-child-tree
  > li.ant-tree-treenode-selected
  > span.ant-tree-node-content-wrapper::before {
  color: #d92b3e !important;
  border-color: #fff !important;
}
.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
  background-color: #d92b3e !important;
}
.ant-tree.ant-tree-directory
  .ant-tree-child-tree
  > li.ant-tree-treenode-selected
  > span.ant-tree-checkbox.ant-tree-checkbox-checked
  .ant-tree-checkbox-inner::after {
  border-color: #fff !important;
}

.ant-menu-inline:last-child {
  margin-bottom: 10px !important;
}
.ant-radio-checked .ant-radio-inner {
  border-color: #d92b3e !important;
}

// +++
.ant-table-small {
  border: 0px !important;
}
</style>
