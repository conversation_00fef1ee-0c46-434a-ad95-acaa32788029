/* 代表画像页面图标样式 */
.icon-duty::before { content: '📋'; }
.icon-standard::before { content: '📊'; }
.icon-supervision::before { content: '👁'; }
.icon-help::before { content: '🤝'; }
.icon-advisor::before { content: '💡'; }
.icon-model::before { content: '⭐'; }
.icon-phone::before { content: '📞'; }
.icon-email::before { content: '✉️'; }
.icon-arrow-up::before { content: '↗️'; }
.icon-arrow-down::before { content: '↘️'; }
.icon-minus::before { content: '➖'; }

/* 基础图标样式 */
[class*="icon-"] {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

/* 趋势图标颜色 */
.icon-arrow-up {
  color: #52c41a;
}

.icon-arrow-down {
  color: #ff4d4f;
}

.icon-minus {
  color: #faad14;
}
