import { instance_yajy } from "@/api/axiosRq";

// 根据条件获取往届建议列表
export function previousFindPage(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestionHis/findPage`,
    method: "post",
    data: queryForm,
  });
}
// 往届建议导出excel
export function previousExportExcel(params) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestionHis/exportExcel`,
    method: "get",
    responseType: "blob",
    params,
  });
}
