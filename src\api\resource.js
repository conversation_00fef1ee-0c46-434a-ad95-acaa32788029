import request from "@/utils/request";
import qs from "qs";

export function getListByType(param) {
  return request({
    url: "/api/v1/meetingResource/getListByType",
    method: "post",
    param,
  });
}

export function getListByTypes(data) {
  return request({
    url: "/api/v1/meetingResource/getListByTypes",
    method: "post",
    data,
  });
}

export function getList(data) {
  return request({
    url: "/api/v1/meetingResource/getResourceList",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/meetingResource/addResource",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/meetingResource/updateResource",
    method: "post",
    data,
  });
}

export function doDelete(param) {
  return request({
    url: "/api/v1/meetingResource/delResource/" + param,
    method: "post",
  });
}