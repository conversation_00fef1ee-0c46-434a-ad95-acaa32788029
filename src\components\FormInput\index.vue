<template>
  <FormItem
    :label="label"
    :prop="prop"
    :span="span"
    :xs="xs"
    :sm="sm"
    :md="md"
    :lg="lg"
    :xl="xl"
    :xxl="xxl"
    :rules="rules"
  >
    <a-input-number
      v-if="type === 'number'"
      :value="input"
      :placeholder="
        cascadeLabel
          ? `请先输入${cascadeLabel}`
          : placeholder || `请输入${placeholderLabel}`
      "
      :disabled="disabled || !!cascadeLabel"
      :allow-clear="allowClear"
      :min="min"
      :max="max"
      style="width: 100%"
      @keyup.enter="onEnter"
      @change="onChange"
      @input="onChange($event.target.value)"
      autocomplete="off"
    />
    <a-input
      v-else
      :value="input"
      :placeholder="
        cascadeLabel
          ? `请先输入${cascadeLabel}`
          : placeholder || `请输入${placeholderLabel}`
      "
      :disabled="disabled || !!cascadeLabel"
      :allow-clear="allowClear"
      :type="type"
      :rows="rows"
      style="width: 100%"
      @keyup.enter="onEnter"
      @input="onChange($event.target.value)"
    />
  </FormItem>
</template>

<script>
import FormItem from "@/components/FormItem/index.vue";

/**
 * 表单输入组件
 *  1.基础使用 <FormInput v-model="queryQuery.name" label="名 称" @enter="handleEnter"/>
 */
export default {
  name: "FormInput",
  components: { FormItem },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    // <a-input v-model="" />
    value: {
      type: [Number, String],
      required: true,
      default: "",
    },
    // <a-form-model-item prop="" />
    prop: {
      type: String,
      required: false,
      default: undefined,
    },
    // <a-form-model-item label="" />
    label: {
      type: String,
      required: true,
    },
    // <a-col span="" />
    span: {
      type: [Number, String],
      default: undefined,
    },
    xs: {
      type: [Number, String],
      default: undefined,
    },
    sm: {
      type: [Number, String],
      default: undefined,
    },
    md: {
      type: [Number, String],
      default: undefined,
    },
    lg: {
      type: [Number, String],
      default: undefined,
    },
    xl: {
      type: [Number, String],
      default: undefined,
    },
    xxl: {
      type: [Number, String],
      default: undefined,
    },
    // 级联标题
    cascadeLabel: {
      type: [String, Boolean],
      required: false,
      default: false,
    },
    // <a-input :allow-clear="false" />
    allowClear: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: undefined,
    },
    // 输入框类型 text | textarea | number
    type: {
      type: String,
      default: "text",
    },
    rows: {
      type: [Number, String],
      default: undefined,
    },
    min: {
      type: Number,
      default: undefined,
    },
    max: {
      type: Number,
      default: undefined,
    },
    rules: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    input() {
      if (
        (typeof this.value == "string" && !this.value) ||
        this.value == undefined ||
        this.value == null
      ) {
        return undefined;
      }

      if (this.type === "number") return Number(this.value);

      return this.value;
    },
    placeholderLabel() {
      return this.label.replace(/\s/g, "");
    },
  },
  methods: {
    onChange(value) {
      if (value == null) value = undefined;
      this.$emit("update:value", value);
      this.$emit("change", value);

      // console.log(value)

      if (value != null || value != '') {
        // console.log(this.rules)
        // console.log(this.rules[this.prop])
        // 隐藏

      } else {
        // 显示
      }
    },
    onEnter() {
      this.$emit("enter");
    },
  },
};
</script>

<style lang="scss" scoped>
    // 控制提示语的显示和隐藏
    ::v-deep .ant-form-explain {
      // color: aqua !important;
      // display: none;
    }
</style>
