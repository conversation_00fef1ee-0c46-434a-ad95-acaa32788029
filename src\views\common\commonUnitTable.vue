<template>
  <!-- 13楼 单位数据列表 -->
  <a-modal
    :visible.sync="unitTableVisible"
    width="60%"
    title="查找"
    @cancel="close"
    @ok="unitTableOk"
  >
    <div style="justify-content: center; display: flex; align-items: center;">
      <a-form layout="inline">
        <a-form-item label="单位名称">
          <a-row>
            <a-input
              @keydown="enterSearch()"
              v-model="queryForm.orgName"
              allow-clear
              style="width: 400px;"
            ></a-input>
          </a-row>
        </a-form-item>
      </a-form>
      <a-space>
        <a-button type="primary" @click="getProposalUndertakesList"
          >查询</a-button
        >
        <a-button
          type="primary"
          @click="
            () => {
              selectedRowKeys = [];
            }
          "
          >清空已选</a-button
        >
      </a-space>
    </div>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      rowKey="orgCode"
      :customRow="clickRow"
      :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onChange: onSelectChange,
        type: 'radio',
      }"
      :pagination="false"
    >
    </a-table>
  </a-modal>
</template>
<script>
import { getProposalUndertakes } from "@/api/myJob/myProposal.js";
export default {
  data() {
    return {
      unitTableVisible: false,
      searchValue: "",
      selectedRows: [],
      dataSource: [],
      columns: [
        {
          title: "单位名称",
          dataIndex: "orgName",
        },
      ],
      selectedRowKeys: [],
      queryForm: {
        orgName: "",
      },
    };
  },
  props: {
    defaultUnitRowKeys: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  methods: {
    // 点击行
    clickRow(record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            this.selectedRowKeys = [];
            this.selectedRowKeys.push(record.orgCode);
            this.selectedRows = [];
            this.selectedRows.push(record);
          },
        },
      };
    },
    // 回车搜索
    enterSearch() {
      var event = window.event || arguments.callee.caller.arguments[0];
      if (event.keyCode == 13) {
        this.getProposalUndertakesList();
      }
    },
    // 保存
    unitTableOk() {
      this.$emit("emitUnitTable", this.selectedRows);
      this.unitTableVisible = false;
      this.selectedRows = [];
      this.selectedRowKeys = [];
    },
    // 选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
    },
    // 关闭
    close() {
      this.unitTableVisible = false;
      Object.assign(this.$data, this.$options.data());
    },
    // 获取议案建议的承办单位列表
    getProposalUndertakesList() {
      getProposalUndertakes(this.queryForm).then((res) => {
        this.dataSource = res.data.data;
        console.log(
          "🤗🤗🤗, this.defaultUnitRowKeys =>",
          this.defaultUnitRowKeys
        );
        if (this.defaultUnitRowKeys.length >= 1) {
          this.selectedRowKeys = this.defaultUnitRowKeys;
        }
        this.unitTableVisible = true;
      });
    },
  },
};
</script>
<style scoped>
div >>> .ant-modal-body {
  height: 500px;
  overflow-y: scroll;
}
</style>
