<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <a-row style="margin-left: 1%">
          <a-form
            ref="queryForm"
            :form="queryForm"
            :model="queryForm"
            layout="horizontal"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 18, offset: 0 }"
          >
            <SearchForm
              :value="queryForm"
              @onReset="reset"
              @onSearch="handleQuery"
            >
              <template v-slot:topSearch>
                <FormInput
                  v-model="queryForm.subject"
                  label="主体"
                  allow-clear
                  @enter="handleEnter"
                />
                <FormInput
                  v-model="queryForm.address"
                  label="地址"
                  allow-clear
                  @enter="handleEnter"
                />
                <FormSelect
                  v-model="queryForm.status"
                  :options="arrOptions"
                  label="处理进度"
                  options-label="label"
                  options-value="value"
                />
              </template>
              <template v-slot:moreSearch>
                <FormInput
                  v-model="queryForm.userName"
                  label="代表姓名"
                  allow-clear
                  @enter="handleEnter"
                />
                <FormSelect
                  v-model="queryForm.commentCategory"
                  :options="cityItems"
                  label="类型"
                  options-label="title"
                  options-value="title"
                />
                <FormRangePicker
                  v-model="queryForm"
                  start-prop="startTime"
                  end-prop="endTime"
                  label="时间范围"
                  allow-clear
                />
              </template>
            </SearchForm>

            <!-- <a-row>
              <a-col :span="6">
                <a-form-item label="主体">
                  <a-input v-model="queryForm.subject"
                           placeholder="请输入主体"
                           clearable
                           allow-clear
                           @keyup.enter="handleQuery">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="地址">
                  <a-input v-model="queryForm.address"
                           placeholder="请输入地址"
                           clearable
                           allow-clear
                           @keyup.enter="handleQuery"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item style="display: flex;"
                             label="处理进度">
                  <a-select v-model="queryForm.status"
                            placeholder="请选择处理进度"
                            clearable
                            allow-clear>
                    <a-select-option v-for="(item, index) in arrOptions"
                                     :key="index"
                                     :label="item.label"
                                     :value="item.value">
                      {{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <span style="float: right; margin-top: 3px;">
                  <a style="margin-right: 8px;"
                     @click="toggleAdvanced">
                    {{ advanced ? "收起" : "高级搜索" }}
                    <a-icon :type="advanced ? 'up' : 'down'" />
                  </a>
                  <a-button type="primary"
                            @click="handleQuery">搜索</a-button>
                  <a-button style="margin-left: 12px;"
                            class="pinkBoutton"
                            @click="reset">重置</a-button>
                </span>
              </a-col>
            </a-row> -->
            <!-- <a-row v-if="advanced">
              <a-col :md="6"
                     :sm="24">
                <a-form-item label="代表姓名"
                             prop="userName">
                  <a-input v-model="queryForm.userName"
                           clearable
                           allow-clear
                           placeholder="请输入代表姓名"
                           @keyup.enter="handleQuery" />
                </a-form-item>
              </a-col>
              <a-col :md="6"
                     :sm="24">
                <a-form-item label="类型"
                             prop="commentCategory">
                  <a-select v-model="queryForm.commentCategory"
                            placeholder="请选择类型"
                            clearable
                            allow-clear>
                    <a-select-option v-for="(item, index) in cityItems"
                                     :key="index"
                                     :label="item.title"
                                     :value="item.title">
                      {{ item.title }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col span="6">
                <a-form-model-item style="width: 100%;"
                                   label="时间范围">
                  <a-range-picker v-model="dateRange"
                                  style="width: 100%;"
                                  :ranges="{
                      最近三个月: [
                        moment(new Date()).subtract(2, 'months'),
                        moment(),
                      ],
                      '今年1-6月': [
                        moment(moment().startOf('year')).startOf('month'),
                        moment(moment().startOf('year'))
                          .add(5, 'months')
                          .endOf('month'),
                      ],
                      '今年7-12月': [
                        moment(moment().startOf('year'))
                          .add(6, 'months')
                          .startOf('month'),
                        moment(moment().startOf('year'))
                          .add(11, 'months')
                          .endOf('month'),
                      ],
                      今年内: [
                        moment(moment().startOf('year')).startOf('month'),
                        moment(moment().startOf('year'))
                          .add(11, 'months')
                          .endOf('month'),
                      ],
                    }"
                                  format="YYYY-MM-DD"
                                  value-format="YYYY-MM-DD"
                                  @change="onTimeChange" />
                </a-form-model-item>
              </a-col>
            </a-row> -->
          </a-form>
        </a-row>
        <a-row style="margin: 5px 0px 10px 8px">
          <!-- 新增  改过-->
          <a-button
            v-if="isDb != '0'"
            style="margin-left: 10px; margin-bottom: 10px"
            type="primary"
            @click="handleAdd"
          >
            新增</a-button
          >
          <!-- 统计情况 -->
          <!-- <a-button
                  style="margin-left: 10px; margin-bottom: 10px;"
                  type="primary"
                  @click="handeStatistics"
                  >统计情况</a-button> -->
        </a-row>
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <!-- 更多查询条件抽屉 结束 -->
        <!-- table -->
        <a-spin :spinning="listLoading" :indicator="indicator">
          <a-table
            ref="table"
            :bordered="false"
            class="tableLimit"
            :columns="columns"
            :pagination="pagination"
            :data-source="list"
            :row-key="
              (record, index) => {
                return index;
              }
            "
            :custom-row="clickRow"
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>
    <!-- 新增弹窗随手拍 -->
    <a-modal
      :title="titleText"
      :visible.sync="dialogFormVisible"
      width="1000px"
      destroy-on-close
      @cancel="modalClose"
    >
      <template slot="footer">
        <a-button @click="modalClose">取消</a-button>
        <a-button style="background-color: '#00a64d'" @click="modalOk(1)"
          >草稿</a-button
        >
        <a-button type="primary" @click="modalOk()">确定</a-button>
      </template>

      <a-tabs :default-active-key="active" @change="callback">
        <a-tab-pane key="1" tab="拍照上传">
          <div class="Data-value">
            <div class="title" style="width: 180px">上传图片:</div>
            <div class="upData">
              <a-upload
                ref="uploadPic"
                list-type="picture-card"
                :multiple="false"
                :file-list="imgList"
                accept="image/*"
                :before-upload="beforeAvatarUpload"
                @change="handlePicChange"
                @preview="handlePreview"
              >
                <!-- 设置文件上传个数限制 -->
                <div v-if="Boolean(imgList) && imgList.length < 5">
                  <a-icon type="plus" />
                  <div>上传</div>
                </div>
              </a-upload>
              <a-modal
                :visible.sync="dialogVisible"
                :footer="null"
                @cancel="
                  () => {
                    dialogVisible = false;
                  }
                "
              >
                <img width="100%" :src="dialogImageUrl" alt />
              </a-modal>
            </div>
          </div>
          <div class="Data-value">
            <div class="title">
              <span style="color: #ff3a29">*</span
              ><span>所反映意见建议涉及的主体:</span>
            </div>
            <div class="textarea">
              <a-textarea
                v-model="content"
                placeholder="店名或相关信息(必填）"
                auto-size
              />
            </div>
          </div>
          <div class="Data-value">
            <div class="title">
              <span style="color: #ff3a29">*</span>所反映意见建议涉及的地址:
            </div>
            <div class="textarea">
              <a-textarea v-model="address" placeholder="反映地址" auto-size />
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 405px">
              <span style="color: #ff3a29">*</span>意见建议内容(必填):
            </div>
            <div class="contentBox">
              <div
                v-for="(item, index) in cityItems"
                :key="index"
                :class="{ bgColor: index == isColor }"
                class="contents"
                @click="fnColor(index, item.title)"
              >
                {{ item.title }}
              </div>
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 170px">
              <span style="color: #ff3a29">*</span>
              请根据需要填写您的意见:
            </div>
            <div class="textarea">
              <a-textarea
                v-model="comment"
                style="height: 160px"
                :rows="8"
                placeholder="请填写意见内容"
                auto-size
              />
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 180px">上传附件:</div>
            <div class="upData">
              <a-upload
                accept=".pdf"
                :action="action"
                :data="fileData"
                :remove="handleRemoveFU"
                :file-list="fileValue"
                :before-upload="beforeAvatarUploadFU"
                @preview="previewData"
                @change="uploadChange"
              >
                <a-button type="primary">点击上传</a-button>
                <div>只能上传pdf文件，且不超过20mb</div>
              </a-upload>
            </div>
          </div>
          <!-- <div class="Data-value">
            <div class="title" style="width: 178px;">选择转办地址:</div>
            <a-radio-group v-model="progressStatus" @change="onprogressStatus">
              <a-radio
                v-for="(items, index) in locationData"
                :key="index"
                :value="items.liaisonStationId"
                >{{ items.liaisonStationName }}</a-radio
              >
              <a-radio :value="2">转办联络站跟进办理</a-radio>
              <a-radio :value="3">转办12345政府热线办理</a-radio>
            </a-radio-group>
          </div> -->
        </a-tab-pane>

        <a-tab-pane key="2" tab="视频上传">
          <div class="Data-value">
            <div class="title" style="width: 180px">上传视频:</div>
            <div class="upData">
              <div class="upData">
                <a-upload
                  accept=".mp4"
                  :action="action"
                  :data="fileData"
                  :remove="handleRemoveFU"
                  :before-upload="beforeAvatarUploadMP4"
                  @change="uploadChange4"
                >
                  <a-button type="primary" :disabled="isDisabled"
                    >点击上传</a-button
                  >
                  <div>上传文件不能超过20mb,且只能上传一个</div>
                </a-upload>

                <div v-if="videoFujianData.length > 0" label="视频文件">
                  <div v-for="(video, index) in videoFujianData" :key="index">
                    <video
                      ref="video"
                      :src="video"
                      :controls="true"
                      class="video-js vjs-big-play-centered vjs-fluid"
                      webkit-playsinline="true"
                      playsinline="true"
                      x-webkit-airplay="allow"
                      x5-playsinline
                      style="height: 200px; width: 100%"
                    ></video>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="Data-value">
            <div class="title">
              <span style="color: #ff3a29">*</span>所反映意见建议涉及的主体:
            </div>
            <div class="textarea">
              <a-textarea
                v-model="content"
                placeholder="店名或相关信息"
                auto-size
              />
            </div>
          </div>
          <div class="Data-value">
            <div class="title">
              <span style="color: #ff3a29">*</span>所反映意见建议涉及的地址:
            </div>
            <div class="textarea">
              <a-textarea v-model="address" placeholder="反映地址" auto-size />
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 405px">
              <span style="color: #ff3a29">*</span>意见建议内容(必填):
            </div>
            <div class="contentBox">
              <div
                v-for="(item, index) in cityItems"
                :key="index"
                :class="{ bgColor: index == isColor }"
                class="contents"
                @click="fnColor(index, item.title)"
              >
                {{ item.title }}
              </div>
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 170px">
              <span style="color: #ff3a29">*</span>
              请根据需要填写您的意见:
            </div>
            <div class="textarea">
              <a-textarea
                v-model="comment"
                style="height: 160px"
                :rows="8"
                placeholder="请填写意见内容"
                auto-size
              />
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 180px">上传附件:</div>
            <div class="upData">
              <a-upload
                accept=".pdf"
                :action="action"
                :data="fileData"
                :remove="handleRemoveFU"
                :before-upload="beforeAvatarUploadFU"
                @preview="previewData"
                @change="uploadChange"
              >
                <a-button type="primary">点击上传</a-button>
                <div>只能上传pdf文件，且不超过20mb</div>
              </a-upload>
            </div>
          </div>
          <!-- <div class="Data-value">
            <div class="title" style="width: 178px;">选择转办地址:</div>
            <a-radio-group v-model="progressStatus" @change="onprogressStatus">
              <a-radio
                v-for="(items, index) in locationData"
                :key="index"
                :value="items.liaisonStationId"
                >{{ items.liaisonStationName }}</a-radio
              >
              <a-radio :value="3">转办12345政府热线办理</a-radio>
            </a-radio-group>
          </div> -->
        </a-tab-pane>
      </a-tabs>
    </a-modal>
    <!-- 统计 page -->
    <Statistics ref="Statistics"></Statistics>
  </div>
</template>

<script>
import Statistics from "./Statistics.vue";
import { instance_1, instance_3 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
import Vue from "vue";
import SingleSelect from "@/components/SingleSelect/index";
import SearchForm from "@/components/SearchForm/index";
import SingleSearch from "@/components/SingleSearch/index";
import TimeRangePicker from "@/components/TimeRangePicker/index";
import FormInput from "@/components/FormInput/index.vue";
import FormSelect from "@/components/FormSelect/index.vue";
import FormRangePicker from "@/components/FormRangePicker/index.vue";
const action =
  Vue.prototype.GLOBAL.basePath_1 + "/api/v1/meetingFile/fileUpload";

export default {
  name: "Table",
  components: {
    FormRangePicker,
    FormSelect,
    FormInput,
    Statistics,
    SingleSelect,
    SearchForm,
    SingleSearch,
    TimeRangePicker,
  },
  filters: {
    getState: function (state) {
      if (state == 1) {
        return "草稿";
      } else if (state == 2) {
        return "联络站处理";
      } else if (state == 3) {
        return "12345热线处理";
      } else if (state == 4) {
        return "已办结";
      }
    },
  },
  data() {
    return {
      TBloading: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />, //转动的圈圈
      dateRange: [],
      isDb: null,
      ids: "",
      editShow: false,
      titleText: "",
      active: "1",
      videoList: [],
      videoFujianData: [],
      fujian: [],
      imgFujian: [],
      videoFujian: [],
      qitaFujian: [],
      isDisabled: false,
      content: "",
      address: "",
      comment: "",
      station: "",
      commentCategory: "城市建设",
      progressStatus: 3,
      fileList: [],
      fileValue: [],
      imgList: [],
      action: action,
      interfaceLocation: "",
      //  action: "http://************:8080/ilz/api/v1/" + "activity/upload",
      fileData: {
        relId: "",
        type: 0,
        encrypted: true,
      },
      urlData: "/memberComment/insert",
      dialogImageUrl: "",
      formDataFU: new FormData(),
      dialogVisible: false,
      formData: new FormData(),
      defaultfile: [],
      isColor: "0",
      dialogFormVisible: false,
      isCheck: false,
      badgeHide: true,
      drawer: false,
      drawerHeight: window.innerHeight - 75 + "px",
      list: [],
      listLoading: false,
      listHeight: window.innerHeight - 360 + "px",
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      filePath: "",
      cityItems: [
        { id: 400, title: "城市建设", status: false },
        { id: 500, title: "房产管理", status: false },
        { id: 600, title: "环境保护", status: false },
        { id: 700, title: "市容城管", status: false },
        { id: 800, title: "交通运输", status: false },
        { id: 900, title: "经济财贸", status: false },
        { id: 1000, title: "治安消防", status: false },
        { id: 1100, title: "交通管理", status: false },
        { id: 1200, title: "市场管理", status: false },
        { id: 1300, title: "民政救济", status: false },
        { id: 1400, title: "文教卫体", status: false },
        { id: 1500, title: "三农问题", status: false },
        { id: 1600, title: "重大突发事件", status: false },

        { id: 1800, title: "涉军涉警", status: false },

        { id: 2000, title: "退役事务", status: false },
        { id: 2100, title: "人力资源", status: false },
        { id: 9999, title: "其他事件", status: false },
      ],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        subject: undefined,
        status: undefined,
        address: undefined,
        commentCategory: undefined,
        startTime: undefined,
        endTime: undefined,
        userName: undefined,
      },
      DataValue: "查看",
      indexNum: 1,
      URL: null,
      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "日期",
          align: "center",
          width: 140,
          ellipsis: true,
          dataIndex: "reflectTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },

        {
          title: "类型",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "commentCategory",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "处理进度",
          align: "center",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            if (record.progressStatus == "1" || record.progressStatus == "0")
              return "草稿";
            if (record.progressStatus == "2") return "联络站处理";
            if (record.progressStatus == "3") return "12345热线处理";
            // if (record.progressStatus == "3" && record.status == "0")
            //   return "12345提交中";
            // if (record.progressStatus == "3" && record.status == "1")
            //   return "12345处理中";
            // if (record.progressStatus == "3" && record.status == "2")
            //   return "12345已回复";
            if (record.progressStatus == "4") return "已办结";
          },
        },
        {
          title: "意见内容",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "generalComments",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "代表手机",
          align: "center",
          width: 160,
          ellipsis: true,
          dataIndex: "userPhone",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        // {
        //   title: "所属区域",
        //   align: "center",
        //   width: 100,
        //   ellipsis: true,
        //   dataIndex: "district",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "所属乡镇街道",
        //   align: "center",
        //   width: 170,
        //   ellipsis: true,
        //   dataIndex: "street",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "联络站",
        //   align: "center",
        //   width: 150,
        //   ellipsis: true,
        //   dataIndex: "interfaceLocation",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "站长姓名",
        //   align: "center",
        //   width: 100,
        //   ellipsis: true,
        //   dataIndex: "stationAgentName",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "站长电话",
        //   align: "center",
        //   width: 160,
        //   ellipsis: true,
        //   dataIndex: "stationAgentPhoneNumber",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        {
          title: "反映涉及主体",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "subject",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "地址",
          align: "center",
          width: 170,
          ellipsis: true,
          dataIndex: "address",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },

        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 100,
          customRender: (text, record, index) => {
            // let sfpjs = true;
            // let isPermissions = [
            //   "I_XTGLY",
            //   "I_QRDGZRY",
            //   "I_SSPGLY",
            //   "I_SSSPGLY",
            //   "I_QSSPGLY",
            //   "I_LLZSSPGLY",
            // ];
            // this.$store.getters.permissions.map((item) => {
            //   isPermissions.map((son) => {
            //     if (item == son) {
            //       operating = true;
            //     }
            //   });
            // });
            let permission =
              record.progressStatus == "1" || record.progressStatus == "0";

            // this.$route.query.isDb == "1" ? sfpjs = false : sfpjs = true;
            // let isdele = false;
            // (this.URL == '/memberComment/miniList' && record.progressStatus == '1')
            //   || this.URL == '/memberComment/list'
            //   ?
            //   isdele = true : isdele = false
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "inline-block" : "none",
                    cursor: "pointer",
                    marginRight: "20px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handlerRedact(record.id);
                    },
                  },
                },
                `编辑`
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(record);
                    },
                  },
                },
                this.DataValue
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "inline-block" : "none",
                    cursor: "pointer",
                    marginLeft: "20px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleDelete(record);
                    },
                  },
                },
                `删除`
              ),
            ]);
          },
        },
      ],
      locationData: [],
      liaisonStationId: "",
      advanced: false,
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      arrOptions: [
        {
          value: "",
          label: "全部",
        },
        // {
        //   value: 1,
        //   label: "草稿",
        // },
        // {
        //   value: 2,
        //   label: "联络站处理",
        // },
        // {
        //   value: 3,
        //   label: "12345处理",
        // },

        {
          value: 4,
          label: "已办结",
        },
      ],
      statusOptions: [
        {
          value: "",
          label: "全部",
        },
        {
          value: 0,
          label: "已转办",
        },
        {
          value: 1,
          label: "已回复",
        },
      ],

      // 限制结束日期不能大于开始日期
      pickerOptions0: {
        disabledDate: (time) => {
          const endDateVal = this.queryForm.endTime;
          if (endDateVal) {
            return time.getTime() >= new Date(endDateVal).getTime();
          }
        },
      },
      pickerOptions1: {
        disabledDate: (time) => {
          const beginDateVal = this.queryForm.startTime;
          if (beginDateVal) {
            return time.getTime() < new Date(beginDateVal).getTime();
          }
        },
      },
      searchDebounced: _.debounce(this.handleQuery, 500),
      isKeepAlive: false,
    };
  },
  computed: {
    conditionsCount: function () {
      let size = 0;
      console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
      for (const key in this.queryForm) {
        if (Object.hasOwnProperty.call(this.queryForm, key)) {
          if (key == "pageNum" || key == "pageSize") {
            continue;
          }
          const element = this.queryForm[key];
          if (
            Array.isArray(element)
              ? element.length > 0
              : element !== "" && element != null
          ) {
            size++;
          }
        }
      }
      return size;
    },
    routeList() {
      return this.$route;
    },
  },
  watch: {
    conditionsCount(newVal, oldVal) {
      this.badgeHide = newVal <= 0;
    },
  },
  created() {
    // // 判断是否是路由跳转过来
    // this.isDb = this.$route.query.isDb

    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));

    // 判断是否是路由跳转过来
    if (this.$route.query.type == "add") {
      this.handleAdd();
    }
    this.$store.dispatch("navigation/breadcrumb1", "代表随手拍");
    this.$store.dispatch("navigation/breadcrumb2", "随手拍意见建议上传");
    this.fetchData();
    this.getMyLiaisonStation();
    this.interfaceData();
    this.$refs.Statistics.handleTime("今年1~6月"); //统计
  },
  mounted() {
    // 抽屉滚动区高度
    window.onresize = () => {
      return (() => {
        this.drawerHeight = window.innerHeight - 75 + "px";
        this.listHeight = window.innerHeight - 360 + "px";
      })();
    };
  },
  activated() {
    if (this.isKeepAlive) {
      if (this.$route.query.type == "add") {
        this.handleAdd();
      }
    } else {
      this.isKeepAlive = true;
    }
  },

  methods: {
    handleEnter() {
      this.searchDebounced();
    },
    // 统计情况
    handeStatistics() {
      // 弹窗
      this.$refs.Statistics.fetchData();
      this.$refs.Statistics.visible = true;
    },
    interfaceData() {
      instance_1({
        method: "get",
        url: "/memberComment/getMyStationBaseInfo",
        data: {},
        header: {
          "Content-type": "application/x-www-form-urlencoded",
        },
      }).then((res) => {
        this.locationData = res.data.data;
        this.liaisonStationId = res.data.data[0].liaisonStationId;
        this.progressStatus = res.data.data[0].liaisonStationId;
      });
    },
    getMyLiaisonStation() {
      instance_1({
        method: "get",
        url: "/liaisonStation/getMyLiaisonStation",
        data: {},
        header: {
          "Content-type": "application/x-www-form-urlencoded",
        },
      }).then((res) => {
        this.interfaceLocation = res.data.data.name;
      });
    },

    // 高级搜索
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    //重置
    reset(value) {
      this.queryForm = value;
      this.pagination.pageNo = 1;
      this.fetchData();
    },
    // 时间选择
    onTimeChange(val) {
      // console.log("🤗🤗🤗, val =>", val);
      this.queryForm.startTime = val[0];
      this.queryForm.endTime = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },
    // 编辑页面时
    handlerRedact(id) {
      this.editShow = true;
      this.titleText = "编辑随手拍";
      instance_1({
        method: "get",
        url: "/memberComment/details",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          id: id,
        },
      }).then((data) => {
        let {
          subject,
          address,
          commentCategory,
          generalComments,
          attachmentList,
          id,
          liaisonStationId,
        } = data.data.data;
        this.liaisonStationId = liaisonStationId;
        this.progressStatus = liaisonStationId;
        // 回显
        this.ids = id;
        this.address = address;
        this.commentCategory = commentCategory;
        this.comment = generalComments;
        this.content = subject;
        this.dialogFormVisible = true;
        this.cityItems.forEach((item, index) => {
          if (item.title == commentCategory) {
            this.isColor = index;
          }
        });
        if (attachmentList.length > 0) {
          attachmentList.forEach((item) => {
            if (item.type == "2") {
              this.qitaFujian.push({
                fileName: item.fileName,
                uid: item.id,
                type: "2",
                path: item.path,
              });
              this.fileValue.push({
                uid: item.id,
                name: item.fileName,
                url: item.path,
              });
            } else {
              this.getFile(item.path, item.type, item);
            }
          });
        }
      });
    },
    getFile(id, type, item) {
      if (type == 1) {
        this.active = "2";
        this.videoFujian.push({
          fileName: item.fileName,
          path: item.path,
          type: type,
          src: item.path,
          uid: item.id,
        });
        instance_3({
          url: "/file/view",
          method: "get",
          responseType: "blob",
          params: { file: item.path },
        }).then((res) => {
          const dataInfo = res.data;
          let reader = new window.FileReader();
          // 使用readAsArrayBuffer读取文件, result属性中将包含一个 ArrayBuffer 对象以表示所读取文件的数据
          reader.readAsArrayBuffer(dataInfo);
          reader.onload = (e) => {
            const result = e.target.result;
            let contentType = "video/mp4";
            // 生成blob图片,需要参数(字节数组, 文件类型)
            const blob = new Blob([result], { type: contentType });
            // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
            const url = window.URL.createObjectURL(blob);
            this.videoFujianData.push(url);
          };
        });
      } else {
        let that = this;
        that.active = "1";
        instance_3({
          url: "/file/view",
          method: "get",
          responseType: "blob",
          params: { file: item.path },
        }).then((res) => {
          const dataInfo = res.data;
          let reader = new window.FileReader();
          // 使用readAsArrayBuffer读取文件, result属性中将包含一个 ArrayBuffer 对象以表示所读取文件的数据
          reader.readAsArrayBuffer(dataInfo);
          reader.onload = function (e) {
            const result = e.target.result;
            let contentType = dataInfo.type;
            //判断文件类型
            if (type == 1) {
              contentType = "video/mp4";
            }
            // 生成blob图片,需要参数(字节数组, 文件类型)
            const blob = new Blob([result], { type: contentType });
            // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
            const url = window.URL.createObjectURL(blob);
            if (url != "" && contentType != "video/mp4") {
              this.imgFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: item.id,
              });
              //构造数据显示图片
              that.imgList.push({
                url: url,
                nameL: item.fileName,
                uid: item.id,
              });
            }
          };
        });
      }
    },

    previewData(item) {
      this.qitaFujian.forEach((item1) => {
        if (item.uid == item1.uid) {
          this.filePath = item1.path;
        }
      });
      let pdfUrl =
        Vue.prototype.GLOBAL.basePath_1 +
        "/file/view?file=" +
        this.filePath +
        "&token=" +
        Vue.prototype.GLOBAL.token;
      window.open(
        process.env.BASE_URL +
          "pdfjs/web/viewer.html?file=" +
          encodeURIComponent(pdfUrl)
      );
    },
    //  视频
    uploadChange4({ file, fileList }) {
      //删除文件
      if (file.status == "removed") {
        this.handleRemoveFU(file.uid);
      } else {
        let type = 1;
        // 图片0
        // 视频1
        let formData = new FormData();
        formData.append("type", type);
        formData.append("file", file);
        instance_1({
          method: "post",
          url: "/memberComment/upload",
          header: {
            "Content-type": "multipart/form-data", // 默认值
          },
          data: formData,
        }).then((res) => {
          res.data.data.forEach((item) => {
            if (type == 0) {
              this.imgFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
            }
            if (type == 1) {
              this.videoFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
              // 限制上传个数
              this.isDisabled = true;
              instance_3({
                url: "/file/view",
                method: "get",
                responseType: "blob",
                params: { file: item.path },
              }).then((res) => {
                const dataInfo = res.data;
                let reader = new window.FileReader();
                // 使用readAsArrayBuffer读取文件, result属性中将包含一个 ArrayBuffer 对象以表示所读取文件的数据
                reader.readAsArrayBuffer(dataInfo);
                reader.onload = (e) => {
                  const result = e.target.result;
                  let contentType = "video/mp4";
                  // 生成blob图片,需要参数(字节数组, 文件类型)
                  const blob = new Blob([result], { type: contentType });
                  // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
                  const url = window.URL.createObjectURL(blob);
                  console.log(url);
                  this.videoFujianData.push(url);
                };
              });
            }
            if (type == 2) {
              this.qitaFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
              });
            }
          });
        });
      }
    },
    //  附件
    uploadChange({ file, fileList }) {
      //删除文件
      if (file.status == "removed") {
        this.handleRemoveFU(file.uid);
      } else {
        let type = 2;
        // 图片0
        // 视频1
        let formData = new FormData();
        formData.append("type", type);
        formData.append("file", file);
        instance_1({
          method: "post",
          url: "/memberComment/upload",
          header: {
            "Content-type": "multipart/form-data", // 默认值
          },
          data: formData,
        }).then((res) => {
          res.data.data.forEach((item) => {
            if (type == 0) {
              this.imgFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
            }
            if (type == 1) {
              this.videoFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
            }
            if (type == 2) {
              this.qitaFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                uid: file.uid,
              });
            }
          });
        });
      }
    },
    //图片上传chage
    handlePicChange({ file, fileList }) {
      console.log(file);
      console.log(fileList);
      //删除文件
      if (file.status == "removed") {
        this.handleRemoveFU(file.uid);
      } else {
        let type = 0;
        this.imgList = fileList;
        // 图片0
        // 视频1
        let formData = new FormData();
        formData.append("type", type);
        formData.append("file", file);
        instance_1({
          method: "post",
          url: "/memberComment/upload",
          header: {
            "Content-type": "multipart/form-data", // 默认值
          },
          data: formData,
        }).then((res) => {
          res.data.data.forEach((item) => {
            if (type == 0) {
              this.imgFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
            }
            if (type == 1) {
              this.videoFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
            }
            if (type == 2) {
              this.qitaFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                uid: file.uid,
              });
            }
          });
        });
        // this.formData.append("image", file);
      }
    },

    beforeAvatarUploadMP4(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /^.+(\/mp4)$/;
      const isMp4 = reg.test(file.type);
      if (!isMp4) {
        this.$message.error("上传的文件格式只能是mp4");
      }
      if (isLt20M && isMp4) return false;
    },
    // 上传大小限制
    beforeAvatarUploadFU(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /^.+(\/pdf)$/;
      const isPDF = reg.test(file.type);
      if (!isPDF) {
        this.$message.error("上传的文件格式只能是PDF");
      }
      if (isLt20M && isPDF) return false;
    },
    //图片格式限制
    beforeAvatarUpload(file, fileList) {
      var reg = /image\/(png|jpg|gif|jpeg|webp)$/;
      const isJPG = reg.test(file.type);
      const isLt8M = file.size / 1024 / 1024 < 8;
      if (!isJPG) {
        this.$message.error("文件格式不正确，请上传图片!");
      }
      if (!isLt8M) {
        this.$message.error("上传头像图片大小不能超过 8MB!");
      }
      return false;
    },
    // 图片预览
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await URL.createObjectURL(file.originFileObj);
      }
      this.dialogImageUrl = file.url || file.preview;
      this.dialogVisible = true;
    },
    //上传文件大小限制
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      // var reg = /^.+(\/pdf)$/;
      // const isPDF = reg.test(file.type);
      // if (!isPDF) {
      //   this.$message.error("上传的文件格式只能是PDF");
      // }
      // if (isLt20M && isPDF) return false;
    },
    handleRemoveFU(uid) {
      this.imgFujian.forEach((item, index) => {
        if (item.uid == uid) {
          this.imgList.splice(index, 1);
          this.imgFujian.splice(index, 1);
        }
      });
      this.videoFujian.forEach((item, index) => {
        if (item.uid == uid) {
          this.videoFujian.splice(index, 1);
          this.videoFujianData = [];
          this.isDisabled = false;
        }
      });

      this.qitaFujian.forEach((item, index) => {
        if (item.uid == uid) {
          this.qitaFujian.splice(index, 1);
        }
      });
    },

    // 新增数据
    handleAdd() {
      this.editShow = false;
      this.titleText = "新增随手拍";
      this.dialogFormVisible = true;
    },

    modalClose() {
      this.Close();
      this.isDisabled = false;
      this.dialogFormVisible = false;
    },
    Close() {
      (this.isColor = "0"), (this.imgList = []);
      this.progressStatus = "2";
      this.videoFujianData = [];
      this.imgFujian = [];
      this.fujian = [];
      this.address = "";
      this.commentCategory = "城市建设";
      this.comment = "";
      this.content = "";
      this.interfaceData();
    },
    modalOk(state) {
      console.log(this.ids, "ids");
      if (
        this.address == "" ||
        this.commentCategory == "" ||
        this.comment == "" ||
        this.subject == ""
      ) {
        this.$baseMessage("内容不完整", "error");
      } else {
        // 判断图片还是视频上传 清空
        this.active == 1 ? (this.videoFujian = []) : (this.imgFujian = []);
        if (this.imgFujian.length != 0) {
          this.imgFujian.forEach((item) => {
            this.fujian.push({
              fileName: item.fileName,
              type: item.type,
              path: item.path,
            });
          });
        }
        if (this.videoFujian.length != 0) {
          this.videoFujian.forEach((item) => {
            this.fujian.push({
              fileName: item.fileName,
              type: item.type,
              path: item.path,
            });
          });
        }
        if (this.qitaFujian.length != 0) {
          this.qitaFujian.forEach((item) => {
            this.fujian.push({
              fileName: item.fileName,
              type: item.type,
              path: item.path,
            });
          });
        }

        //  判断是否为编辑页面
        if (this.editShow) {
          if (state == 1) {
            // 草稿时
            this.progressStatus = "1";
            this.urlData = "/memberComment/update";
          } else {
            // if (this.progressStatus !== 1 || this.progressStatus !== 3) {
            //   this.progressStatus = 2;
            // }
            this.progressStatus = 3;

            this.urlData = "/memberComment/submit";
          }
        } else {
          // 草稿时
          if (state == 1) {
            this.progressStatus = state;
            this.urlData = "/memberComment/save";
          } else {
            // if (this.progressStatus !== 1 || this.progressStatus !== 3) {
            //   this.progressStatus = 2;
            //   this.content = '【代表提出】' + this.content;
            // }
            this.progressStatus = 3;

            this.urlData = "/memberComment/insert";
          }
        }

        instance_1({
          method: "post",
          url: this.urlData,
          data: {
            id: this.ids,
            address: this.address,
            attachmentList: this.fujian,
            commentCategory: this.commentCategory, //意见建议内容
            generalComments: this.comment, //概括性意见
            interfaceLocation: this.interfaceLocation,
            // this.station, 联络站点
            subject: this.content, //涉及的主体相关信息
            progressStatus: this.progressStatus, //转发
            liaisonStationId: this.liaisonStationId /* 联络站 */,
          },
          header: {
            "Content-type": "application/x-www-form-urlencoded",
          },
        }).then((res) => {
          if (res.data.code == "0000") {
            this.fetchData();
            this.$baseMessage("新增成功", "success");
            this.Close();
            this.dialogFormVisible = false;
            this.disabledDate = false;
          } else {
            this.fujian = [];
            this.$baseMessage(res.data.msg, "error");
          }
        });
      }
    },
    callback(active) {
      this.active = active;
      // 切换
    },
    // 点击行
    clickRow(record) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            if ("__vue__" in event.target) {
              this.handleEdit(record);
            }
          },
        },
      };
    },
    //获取勾选的行数据
    setSelectRows(val) {
      this.selectRows = val;
    },
    rowClick(row, column, event) {
      if (column.label !== "操作") {
        this.handleEdit(row);
      }
    },

    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    //查询
    handleQuery() {
      this.drawer = false;
      this.queryForm.pageNum = 1;
      this.fetchData();
    },

    //重置查询参数
    handleClear() {
      if (this.$refs["drawerForm"] !== undefined) {
        this.$refs["drawerForm"].resetFields();
      }
    },

    handleEdit(row) {
      this.$router
        .push({
          path: "/recommended/details",
          query: {
            id: row.id,
          },
        })
        .catch(() => {});
    },

    //删除
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          instance_1({
            method: "get",
            url: "/memberComment/delete",
            params: {
              id: row.id,
            },
          }).then((res) => {
            if (res.data.code == "0000") {
              this.$baseMessage("删除成功", "success");
              this.fetchData();
            } else {
              this.$baseMessage(res.data.msg, "success");
            }
          });
        });
      } else {
        //批量删除，暂无接口
        if (this.selectRows.length > 0) {
          const ids = this.selectRows.map((item) => item.id);
          this.$baseConfirm("你确定要删除选中项吗", null, () => {});
        } else {
          this.$baseMessage("未选中任何行", "error");
          return false;
        }
      }
    },

    //查询列表数据
    fetchData() {
      let URL = "/memberComment/list";
      let isPermissions = [
        "I_XTGLY",
        "I_QRDGZRY",
        "I_SSPGLY",
        "I_SSSPGLY",
        "I_QSSPGLY",
        "I_LLZSSPGLY",
      ];
      this.$store.getters.permissions.map((item) => {
        isPermissions.map((son) => {
          if (item == son) {
            URL = "/memberComment/miniList";
          }
        });
      });
      if (this.$route.query.isDb == "1") {
        URL = "/memberComment/miniList";
        this.queryForm.sfpj = "0";
        this.DataValue = "评价";
      }
      this.listLoading = true;
      this.URL = URL;
      instance_1({
        method: "get",
        url: URL,
        params: { ...this.queryForm, isDb: this.isDb },
      }).then((res) => {
        this.list = res.data.rows;
        this.total = res.data.total;
        this.pagination.total = res.data.total;
        setTimeout(() => {
          this.listLoading = false;
        }, 500);
      });
    },
    onprogressStatus(value) {
      this.progressStatus = value.target.value;
      if (value.target.value == "3") {
        this.liaisonStationId = "";
      } else {
        this.liaisonStationId = value.target.value;
      }
    },
    fnColor(index, title) {
      this.commentCategory = title;
      this.isColor = index;
    },

    checkPermission,
  },
};
</script>
<style lang="scss" scoped>
.tableLimit tr td .cell {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  /*可以显示的行数，超出部分用...表示 */
  -webkit-box-orient: vertical !important;
}

.tableLimit .el-button--small {
  padding: 0 !important;
}

.Data-value {
  display: flex;
  line-height: 34px;
  margin-top: 10px;

  .title {
    // font-size: 14px;
    @include add-size($font_size_16);
    font-family: PingFang-M;
  }

  .contentBox {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    height: 130px;

    .contents {
      width: 100px;
      height: 30px;
      line-height: 30px;
      margin-left: 10px;
      text-align: center;
      border-radius: 6px;
      border: 1px solid rgb(214, 208, 208);
    }
  }
}

.textarea {
  margin-left: 10px;
  width: 400px;
}

.bgColor {
  background-color: #cc3031;
  color: #fff;
}

.upload-list-inline >>> .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-right: 8px;
}

.upload-list-inline >>> .ant-upload-animate-enter {
  animation-name: uploadAnimateInlineIn;
}

.upload-list-inline >>> .ant-upload-animate-leave {
  animation-name: uploadAnimateInlineOut;
}

.ant-upload-picture-card-wrapper {
  display: flex;
}
</style>
