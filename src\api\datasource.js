import request from "@/utils/request";
import qs from "qs";

export function getPage(data) {
  return request({
    url: "/api/v1/manage/datasource/queryPage",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function getList(data) {
  return request({
    url: "/api/v1/manage/datasource/queryList",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/manage/datasource/add",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/manage/datasource/update",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/manage/datasource/deleteByIds",
    method: "post",
    data,
  });
}

export function doConnectTest(data) {
  return request({
    url: "/api/v1/manage/datasource/connectTest",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}
