import request from "@/utils/requestTemp";

export function findDeputyOrganizations() {
  return request({
    url: "/dmcs/common/ours/findDeputyOrganizations",
    method: "get",
  });
}

export function findDbOU(data) {
  return request({
    url: "/dmcs/common/ours/findDbOU",
    method: "get",
    params: data,
  });
}

export function findAllDelegation(params) {
  return request({
    url: "/dmcs/common/ours/findAllDbt",
    method: "get",
    params: params,
  });
}

export function findUserManageDbtList(data) {
  return request({
    url: "/dmcs/common/ours/findUserManageDbtList",
    method: "get",
    params: data,
  });
}

export function baseData() {
  return request({
    url: "/dmcs/common/ours/findDeputyOrganizations",
    method: "get",
    params: {},
  });
}

export function findDbOrgInfoTree() {
  return request({
    url: "/dmcs/common/ours/findDbOrgInfoTree",
    method: "get",
  });
}

export function findManageDbOrgInfoTree() {
  return request({
    url: "/dmcs/common/ours/findManageDbOrgInfoTree",
    method: "get",
  });
}

export function findDbt(params) {
  return request({
    url: "/dmcs/common/ours/findDbt",
    method: "get",
    params
  });
}

export function isProfessionalGroup(params) {
  return request({
    url: "/dmcs/common/ours/isProfessionalGroup",
    method: "get",
    params: params,
  });
}
