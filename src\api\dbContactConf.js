import { instance_1 } from "@/api/axiosRq";

export function getList(data) {
  return instance_1({
    url: "/dbContactConf/list",
    method: "get",
    params: data,
  });
}

export function save(data) {
  return instance_1({
    url: "/dbContactConf/save",
    method: "post",
    data: data,
  });
}

export function update(data) {
  return instance_1({
    url: "/dbContactConf/update",
    method: "post",
    data: data,
  });
}

export function del(ids) {
  return instance_1({
    url: "/dbContactConf/delete",
    method: "post",
    data: ids,
  });
}

export function getAreaList(data) {
  return instance_1({
    url: "/dbContactConf/getAreaList",
    method: "get",
    params: data,
  });
}

export function saveOrgDetail(data) {
  return instance_1({
    url: "/dbContactConf/saveOrgDetail",
    method: "post",
    data,
  });
}

export function saveUserDetail(data) {
  return instance_1({
    url: "/dbContactConf/saveUserDetail",
    method: "post",
    data,
  });
}

export function findOrgAndUserTree(data) {
  let params = { rootOrgId: "root", id: data.id };
  return instance_1({
    url: "/dbContactConf/findOrgAndUserTree",
    method: "post",
    params,
    data,
  });
}

export function getOrgTree() {
  return instance_1({
    url: "/dbContactConf/getOrgTree",
    method: "get",
  });
}
