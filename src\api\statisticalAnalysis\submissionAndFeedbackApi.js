import { instance_yajy } from "@/api/axiosRq";

// 代表提交与反馈建议数目统计
// queryForm 请求搜索数据
export function subAndFeedList(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/dbtjyfkjysmtj`,
    method: "post",
    params: queryForm,
  });
}
// 导出excel
export function subAndFeedExcel(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/export_dbtjyfkjysmtj`,
    method: "post",
    params: queryForm,
    responseType: "blob",
  });
}
