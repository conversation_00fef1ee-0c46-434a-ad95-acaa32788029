import request from "@/utils/request";
import qs from "qs";

export function doSave(data) {
  return request({
    url: "/api/v1/authority/manage/add",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/authority/manage/update",
    method: "post",
    data,
  });
}

export function getPage(data) {
  return request({
    url: "/api/v1/authority/manage/findPage",
    method: "post",
    data,
  });
}

export function getList(data) {
  return request({
    url: "/api/v1/authority/manage/findList",
    method: "post",
    data,
  });
}

export function getTree(data) {
  return request({
    url: "/api/v1/authority/manage/findMenuWithAuthorityTree",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/authority/manage/remove",
    method: "post",
    data,
  });
}

export function doChangeSort(data) {
  return request({
    url: "/api/v1/authority/manage/changeSort",
    method: "post",
    data,
  });
}

export function getById(data) {
  return request({
    url: "/api/v1/authority/manage/findById",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}

export function getAuthCodeByRoleId(data) {
  return request({
    url: "/api/v1/authority/manage/findAuthCodeByRoleId",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}
