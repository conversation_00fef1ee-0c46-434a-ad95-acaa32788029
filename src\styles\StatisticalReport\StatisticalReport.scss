
.tits {
  // font-size: 16px;
  @include add-size($font_size_16);
  color: rgba(56, 56, 56, 1);
  font-weight: 700;
  // margin-top: 15px;
  cursor: pointer;
}

.jbxxson {
  cursor: pointer;
  box-shadow: 1.03px 1.85px 4px 0px rgb(166 166 166 / 51%);
  display: flex;
  align-content: center;
  flex-direction: column;
  min-width: 180px;
  padding: 10px;
  margin: 5% 6%;
  height: 20%;
  // border-radius: 10px;
  // justify-content: flex-start;
  justify-content: space-between;
  border-radius: 30px;
  img {
    width: 38px;
    height: 38px;
    margin: 0% auto;
    // margin-top: 10px;
    margin-top: 10%;
  }

  .jbxxson_box {
    .tits {
      margin: 0 auto;
      width: 100%;
      text-align: center;
      color: rgba(56, 56, 56, 1);
      // margin: 10px auto;
      font-weight: 600; //盒子标题
      margin-bottom: 10px; //没有备注文字
    }

    .remarks {
      width: 100%;
      text-align: center;
      margin: 1% auto;
    }

    color: rgba(153, 153, 153, 1);
  }
}

.jbxx-contt {
  background-color: #fff;
  width: 100%;
  border: 5px solid #f6f6f6;
  border-radius: 10px;
}

::v-deep {
  .ant-badge-status-dot {
    width: 10px !important;
    height: 10px !important;
  }
}

.jbqkbz {
  padding-left: 18px;
  border-left: 4px solid rgba(199, 28, 51, 1);
  // font-size: 16px;
  @include add-size($font_size_16);
  font-weight: 700;
}

.sjcx_son {
  display: flex;
  flex-wrap: wrap;
  margin-left: 35px;
}

.jbqkss {
  padding: 10px 1%;
}

.cardHvover:hover {
  // background-color: #fbeeef;
  outline: 1px solid #d43030;
}
  .Heightjbxxson {
    min-height: 180px;
    max-height: 180px;
  }
@media screen and (max-width: 1920px) {
  .Heightjbxxson {
    min-height: 180px;
    max-height: 180px;
  }
}

@media screen and (max-width: 1600px) {
  .Heightjbxxson {
    min-height: 180px;
    max-height: 180px;
  }
}

@media screen and (max-width: 1280px) {
  .Heightjbxxson {
    min-height: 180px;
    max-height: 180px;
  }
}