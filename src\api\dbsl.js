import request from "@/utils/requestTemp";

export function getList(data) {
  return request({
    url: "/api/v1/memberContact/list",
    method: "get",
    params: data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/memberContact/save",
    method: "post",
    data: data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/memberContact/update",
    method: "post",
    data: data,
  });
}

export function getById(data) {
  return request({
    url: "/api/v1/memberContact/getById",
    method: "get",
    params: data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/memberContact/del",
    method: "get",
    params: data,
  });
}
