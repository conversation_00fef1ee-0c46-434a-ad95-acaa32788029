<template>
  <span v-if="themeBar">
    <byui-icon
      title="主题配置"
      :icon="['fas', 'palette']"
      @click="handleChangeTheme"
      v-if="layout != 'theme3'"
    />
    <!-- <div class="theme-bar-setting">
      <div @click="handleChangeTheme">
        <byui-icon :icon="['fas', 'palette']" />
        <p>主题配置1</p>
      </div> -->
      <!-- <div @click="handleGetCode">
        <byui-icon :icon="['fas', 'laptop-code']"></byui-icon>
        <p>拷贝代码</p>
      </div>-->
      <!--<div @click="handleChangeQq">
        <byui-remix-icon icon-class="qq-fill" />
        <p>学习交流</p>
      </div>-->
    <!-- </div> -->

    <el-drawer
      title="主题配置"
      :visible.sync="drawerVisible"
      direction="rtl"
      append-to-body
      size="300px"
    >
      <el-scrollbar style="height: 94vh; overflow: hidden;">
        <div class="el-drawer__body">
          <el-form ref="form" :model="theme">
            <el-form-item label="风格">
              <!-- <el-radio-group v-model="theme.layout">
                <el-radio-button label="vertical">饿了吗风格</el-radio-button>
                <el-radio-button label="horizontal">请休假风格</el-radio-button>
                <el-radio-button label="horizontal">唔知咩风格</el-radio-button>
              </el-radio-group>-->

              <el-select v-model="theme.layout">
                <el-option label="主题一" value="horizontal"></el-option>
                <el-option label="主题二" value="vertical"></el-option>
                <el-option label="主题三" value="horizontal"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="头部">
              <el-radio-group v-model="theme.header">
                <el-radio-button label="fixed">固定头部</el-radio-button>
                <el-radio-button label="noFixed">不固定头部</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="多标签">
              <el-radio-group v-model="theme.tagsView">
                <el-radio-button label="true">开启</el-radio-button>
                <el-radio-button label="false">不开启</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="请休假风格">
              <el-radio-group v-model="theme.qxjView">
                <el-radio-button label="true">开启</el-radio-button>
                <el-radio-button label="false">不开启</el-radio-button>
              </el-radio-group>
            </el-form-item>-->
            <el-form-item label="菜单背景色">
              <el-color-picker
                v-model="theme.menuBackground"
                :predefine="[
                  '#2a58ad',
                  '#001529',
                  '#f56c6c',
                  '#0fd59d',
                  '#3fb884',
                  '#ff7a47',
                  '#a80505',
                ]"
                show-alpha
              ></el-color-picker>
            </el-form-item>
            <el-form-item label="菜单子级背景色">
              <el-color-picker
                v-model="theme.menuChildrenBackground"
                :predefine="[
                  '#2a58ad',
                  '#001529',
                  '#f56c6c',
                  '#0fd59d',
                  '#3fb884',
                  '#ff7a47',
                  '#a80505',
                ]"
                show-alpha
              ></el-color-picker>
            </el-form-item>
            <el-form-item label="菜单选中色">
              <el-color-picker
                v-model="theme.menuBackgroundActive"
                :predefine="['#22468a', '#1890ff', '#21e6af', '#f57e6c']"
                show-alpha
              ></el-color-picker>
            </el-form-item>
            <el-form-item label="菜单文字色">
              <el-color-picker
                v-model="theme.menuColor"
                :predefine="['#000', '#fff']"
                show-alpha
              ></el-color-picker>
            </el-form-item>
            <el-form-item label="标签主题色">
              <el-color-picker
                v-model="theme.tagViewsBackgroundActive"
                :predefine="['#1890ff', '#0fd59d', '#f56c6c']"
                show-alpha
              ></el-color-picker>
            </el-form-item>
            <el-form-item label="默认按钮主题色">
              <el-color-picker
                v-model="theme.buttonBackground"
                :predefine="['#1890ff', '#0fd59d', '#f56c6c']"
                show-alpha
              ></el-color-picker>
            </el-form-item>
            <el-form-item label="分页选中色">
              <el-color-picker
                v-model="theme.paginationBackgroundActive"
                :predefine="['#1890ff', '#0fd59d', '#f56c6c']"
                show-alpha
              ></el-color-picker>
            </el-form-item>
            <el-form-item>
              <el-button @click="handleSetDfaultTheme">恢复默认</el-button>
              <el-button type="primary" @click="handleSaveTheme"
                >保存</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-scrollbar>
    </el-drawer>
  </span>
</template>

<script>
import { updateTheme } from "@/api/user";
import variables from "@/styles/variables.scss";
import { mapGetters } from "vuex";
import { themeBar } from "@/config/settings";
import GetCode from "./mixin/GetCode";
export default {
  name: "ThemeBar",
  mixins: [GetCode],
  data() {
    return {
      themeBar,
      drawerVisible: false,
      refStore: "",
      theme: {
        layout: "",
        header: "",
        tagsView: "",
        // qxjView: "",
        menuBackground: variables["menu-background"],
        menuChildrenBackground: variables["menu-children-background"],
        menuBackgroundActive: variables["menu-background-active"],
        menuColor: variables["menu-color"],
        tagViewsBackgroundActive: variables["tagviews-background-active"],
        buttonBackground: variables["button-background"],
        paginationBackgroundActive: variables["pagination-background-active"],
      },
    };
  },
  computed: {
    ...mapGetters(["layout", "header", "tagsView"]),
  },
  mounted() {
    this.$baseEventBus.$on("theme", () => {
      this.handleChangeTheme();
    });
  },
  created() {
    const theme = localStorage.getItem("BYUI-VUE-THEME");
    this.theme.layout = this.layout;
    this.theme.header = this.header;
    this.theme.tagsView = this.tagsView;
    // this.theme.qxjView = this.qxjView;
    if (null !== theme) {
      this.$set(this.theme, "menuBackground", JSON.parse(theme).menuBackground);
      this.$set(
        this.theme,
        "menuChildrenBackground",
        JSON.parse(theme).menuChildrenBackground
      );
      this.$set(
        this.theme,
        "menuBackgroundActive",
        JSON.parse(theme).menuBackgroundActive
      );
      this.$set(this.theme, "menuColor", JSON.parse(theme).menuColor);
      this.$set(
        this.theme,
        "tagViewsBackgroundActive",
        JSON.parse(theme).tagViewsBackgroundActive
      );
      this.$set(
        this.theme,
        "buttonBackground",
        JSON.parse(theme).buttonBackground
      );
      this.$set(
        this.theme,
        "paginationBackgroundActive",
        JSON.parse(theme).paginationBackgroundActive
      );
      this.handleSetTheme();
    }
  },
  methods: {
    handleChangeTheme() {
      this.drawerVisible = true;
    },
    handleChangeQq() {
      window.open("tencent://message/?uin=1204505056");
    },
    handleSetTheme() {
      // $("#BYUI-VUE-THEME").remove();
      let {
        layout,
        header,
        tagsView,
        menuBackground,
        menuChildrenBackground,
        menuBackgroundActive,
        menuColor,
        tagViewsBackgroundActive,
        buttonBackground,
        paginationBackgroundActive,
      } = this.theme;
      // if (this.theme.layout == "")
      //请休假风格
      if (layout == "horizontal") {
        this.theme.menuBackground = "#fff";
        this.theme.menuColor = "#596B8C";
        this.theme.menuChildrenBackground = "#f9f9f9";
        this.theme.menuBackgroundActive = "#f9f9f9";
        menuBackground = "#fff";
        menuBackgroundActive = "#f9f9f9";
        menuChildrenBackground = "#f9f9f9";
        menuColor = "#596B8C";
      } else if (layout == "theme3") {
        this.theme.menuBackground = "#ffffff";
        this.theme.menuChildrenBackground = "#ffffff";
        this.theme.menuColor = "#1f2e4c";
        this.theme.menuChildrenBackground = "#ffffff";
        this.theme.menuBackgroundActive = "#f6f8f9";
        menuBackground = "#ffffff";
        menuChildrenBackground = "#ffffff";
        menuBackgroundActive = "#f6f8f9";
        menuChildrenBackground = "#ffffff";
        menuColor = "#1f2e4c";
      }
      //饿了吗风格
      else {
        $("#BYUI-VUE-THEME").remove();
        console.log(this.$refs["form"], ' this.$refs["form"]');
        localStorage.removeItem("BYUI-VUE-THEME");
        this.$store.dispatch("settings/changeLayout", this.theme.layout);
        this.$refs["form"].resetFields();
        Object.assign(this.$data, this.$options.data());
        this.theme.layout = "vertical";
      }
      console.log(this.theme, "this.theme121212");
      console.log(this.$store, "this.$store");

      let style = document.createElement("style");
      style.id = "BYUI-VUE-THEME";
      style.innerHTML = `
         .top-bar-container,
         .top-bar-container .byui-main,
         .side-bar-container,
         .logo-container-vertical,
         .logo-container-horizontal,
         .el-menu,
         .el-menu-item,
         .el-submenu.is-active.is-opened,
         .el-submenu__title,
         .el-menu-item.is-active,
         .el-menu-item .is-active {
           background-color:${menuBackground} !important;
         }

        body .el-menu--horizontal .top-bar-item-container .el-menu-item:hover,
        body .el-menu--horizontal .top-bar-item-container .el-menu-item.is-active,
        body .app-wrapper .side-bar-container .el-submenu .el-menu-item.is-active,
        body .app-wrapper .side-bar-container .el-menu-item:hover,
        body .side-bar-container .el-menu .el-menu-item.is-active {
          background-color:${menuBackgroundActive}!important;
        }
      

        .tags-view-item.router-link-exact-active.router-link-active.active {
          background-color: ${tagViewsBackgroundActive}!important;
          border: 1px solid ${tagViewsBackgroundActive}!important;
        }

      

        .el-pagination.is-background .el-pager li:not(.disabled).active {
          background-color: ${paginationBackgroundActive}!important;
          border-color: ${paginationBackgroundActive}!important;
        }

        body .app-wrapper .side-bar-container .nest-menu .el-menu-item {
          background-color: ${menuChildrenBackground}!important;
        }

        body .app-wrapper .side-bar-container .el-menu .nest-menu [class*=menu] {
          background-color: ${menuChildrenBackground}
        }

        body .app-wrapper .side-bar-container .el-menu .nest-menu [class*=menu].is-active {
          background-color:${menuBackgroundActive}
        }
        body .app-wrapper .side-bar-container .el-menu [class*=menu] span,
        body .app-wrapper .side-bar-container .el-menu [class*=menu] svg,
        body .app-wrapper .side-bar-container .el-menu [class*=menu] i
        {
          color:${menuColor}!important
        }
          .side-bar-container .el-menu .el-menu-item.is-active {
            color: #fff !important;
            background-color: ${menuBackgroundActive} !important;
        }

      `;
      document.getElementsByTagName("head").item(0).appendChild(style);
      localStorage.setItem(
        "BYUI-VUE-THEME",
        `{
            "menuBackground":"${menuBackground}",
            "menuChildrenBackground":"${menuChildrenBackground}",
            "menuBackgroundActive":"${menuBackgroundActive}",
            "menuColor":"${menuColor}",
            "tagViewsBackgroundActive":"${tagViewsBackgroundActive}",
            "layout":"${layout}",
            "header":"${header}",
            "tagsView":"${tagsView}",
            "buttonBackground":"${buttonBackground}",
            "paginationBackgroundActive":"${paginationBackgroundActive}"
          }`
      );

      this.handleSwitchLayout(layout);
      this.handleSwitchHeader(header);
      this.handleSwitchTagsView(tagsView);
      this.drawerVisible = false;
    },
    handleSaveTheme() {
      this.handleSetTheme();
      // this.$confirm("修改成功, 是否重启?", "温馨提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "稍后重启",
      //   type: "warning",
      // })
      //   .then(() => {
      //     location.reload();
      //   })
      //   .catch(() => {});
      // location.reload();
      const obj = {
        theme: this.theme.layout,
      };
      updateTheme(obj).then((res) => {
        // location.reload();
      });
    },
    handleSetDfaultTheme() {
      $("#BYUI-VUE-THEME").remove();
      localStorage.removeItem("BYUI-VUE-THEME");
      this.$store.dispatch("settings/changeLayout", this.theme.layout);
      this.$refs["form"].resetFields();
      console.log(this.$refs["form"], ' this.$refs["form"]');
      Object.assign(this.$data, this.$options.data());
      this.drawerVisible = false;
      location.reload();
    },
    handleSwitchLayout(layout) {
      this.$store.dispatch("settings/changeLayout", layout);
    },
    handleSwitchHeader(header) {
      this.$store.dispatch("settings/changeHeader", header);
    },
    handleSwitchTagsView(tagsView) {
      this.$store.dispatch("settings/changeTagsView", tagsView);
    },
  },
};
</script>

<style lang="scss" scoped>
@mixin right-bar {
  position: fixed;
  right: 0;
  z-index: $base-z-index;
  width: 60px;
  min-height: 60px;
  text-align: center;
  cursor: pointer;
  background: $base-color-blue;
  border-radius: $base-border-radius;

  > div {
    padding-top: 10px;
    border-bottom: 1px solid $base-color-white;

    &:hover {
      opacity: 0.9;
    }

    p {
      font-size: $base-font-size-small;
      line-height: 30px;
      color: $base-color-white;
    }
  }
}

.theme-bar-setting {
  @include right-bar;

  top: 40vh;

  ::v-deep {
    svg:not(:root).svg-inline--fa {
      display: block;
      margin-right: auto;
      margin-left: auto;
      color: $base-color-white;
    }

    .svg-icon {
      display: block;
      margin-right: auto !important;
      margin-left: auto !important;
      // font-size: 20px !important;
       @include add-size($font_size_16);
      color: $base-color-white !important;
      fill: $base-color-white !important;
    }
  }
}

.el-drawer__body {
  padding: 20px;
}
</style>
