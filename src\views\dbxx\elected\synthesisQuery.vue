<template>
  <div class="table-container">
    <ZTable :list="list"
            :columns="columns"
            :addButton="false"
            inputTitle="代表姓名"
            selectTitle="选择代表类型" />
  </div>
</template>

<script>
export default {
  data () {
    return {
      TBloading: false,
      list: [
        {
          thNum: "省委会视察",
          type: "视察活动调查",
          name: "林*强",
          sex: "男",
          birthdayTime: "1974.03.25",
          party: "中国共产党",
          education: "本科",
          unit: "xx技术有限公司 董事长",
          status: "出席",
          currStatus: "正常",
          createTime: "2021.09.22",
        },
      ],
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "标题",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "thNum",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "内容",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "type",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.switMeeting(record);
                    },
                  },
                },
                "查看"
              ),

              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "当选管理");
    this.$store.dispatch("navigation/breadcrumb2", "综合查询");
  },
};
</script>
<style scoped></style>
