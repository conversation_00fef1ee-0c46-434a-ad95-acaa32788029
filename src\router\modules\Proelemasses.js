import Layout from "@/layouts";
// EmptyLayout 适用于三级菜单中二级菜单的 component,否则会跳转空白页面
import EmptyLayout from "@/layouts/EmptyLayout";
import childrenAppMain from "@/layouts/components/childrenAppMain/index";
// import AppMain from "@/layouts/components/AppMain/index";
// 联系人民群众
export default [
  {
    path: "/Proelemasses",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "联系人民群众",
    meta: {
      title: "首页",
    },
    children: [
      // {
      //   path: "site/index",
      //   name: "siteIndex",
      //   component: () => import("@/views/proelemasses/index/index"),
      //   meta: {
      //     title: "人民群众首页",
      //   },
      // },
      {
        path: "site/index",
        name: "siteIndex",
        component: () =>
          import("@/views/proelemasses/representativeHome/index"),
        meta: {
          title: "代表首页",
        },
      },
      {
        path: "staffHomepage",
        name: "staffHomepage",
        component: () => import("@/views/proelemasses/staffHomepage/index"),
        meta: {
          title: "工作人员首页",
        },
      },
    ],
  },

  //代表进社区
  {
    path: "/community",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "联系人民群众",
    meta: {
      title: "代表进社区",
    },
    children: [
      {
        path: "todo",
        name: "todo",
        component: () => import("@/views/communityschedule/todo"),
        meta: {
          title: "待办事项",
        },
      },
      {
        path: "handled",
        name: "handled",
        component: () => import("@/views/communityschedule/handled"),
        meta: {
          title: "已办事项",
        },
      },
      {
        path: "intoTheCommunityPlan",
        name: "intoTheCommunityPlan",
        component: () => import("@/views/communityschedule/list"),
        meta: {
          title: "年度活动计划",
        },
      },
      {
        path: "intoTheCommunityPlanDetail",
        name: "intoTheCommunityPlanDetail",
        component: () => import("@/views/communityschedule/listDetail"),
        meta: {
          title: "年度活动计划",
        },
        hidden: true,
      },
      {
        path: "BBS_ListNotice",
        name: "BBS_ListNotice",
        component: () => import("@/views/statistics/BBS/listNotice"),
        meta: {
          title: "进社区活动公告",
        },
      },
      {
        path: "intoTheCommunityActivity",
        name: "intoTheCommunityActivity",
        component: () => import("@/views/communityschedule/activityList"),
        meta: {
          title: "进社区活动登记",
        },
      },
      {
        path: "intoTheCommunityActivityDetail",
        name: "intoTheCommunityActivityDetail",
        component: () => import("@/views/communityschedule/activityListDetail"),
        meta: {
          title: "进社区活动登记",
        },
        hidden: true,
      },
      {
        path: "newlyAddedList",
        name: "newlyAddedList",
        component: () => import("@/views/communityschedule/newlyAddedList"),
        meta: {
          title: "社情民意登记",
        },
      },
      {
        path: "intoTheCommunityActivityQuery",
        name: "intoTheCommunityActivityQuery",
        component: () => import("@/views/communityschedule/activityListQuery"),
        meta: {
          title: "进社区活动查询",
        },
      },
      {
        path: "intoTheCommunityActivityWrapper",
        name: "intoTheCommunityActivityWrapper",
        component: () => import("@/views/communityschedule/activityWrapper"),
        meta: {
          title: "进社区活动跳转",
        },
        hidden: true,
      },

      {
        path: "newlyAdded",
        name: "newlyAdded",
        component: () => import("@/views/communityschedule/newlyAdded"),
        meta: {
          title: "新增社情民意",
        },
        hidden: true,
      },
      {
        path: "activityScheduleTupleList",
        name: "activityScheduleTupleList",
        component: () =>
          import("@/views/communityschedule/activityScheduleTupleList"),
        meta: {
          title: "新增社情民意",
        },
        hidden: true,
      },
      {
        path: "listNoticeAdmin",
        name: "listNoticeAdmin",
        component: () => import("@/views/statistics/BBS/listNoticeAdmin"),
        meta: {
          title: "接待群众安排查询",
        },
      },
      // {
      //   path: "intoCommunityOrg",
      //   name: "intoCommunityOrg",
      //   component: () => import("@/views/community/intoCommunityOrg"),
      //   meta: {
      //     title: "进社区组织",
      //   },
      // },
      // {
      //   path: "CommunityAnalysis",
      //   name: "CommunityAnalysis",
      //   component: () => import("@/views/community/CommunityAnalysis"),
      //   meta: {
      //     title: "统计分析",
      //   },
      // },

      // {
      //   path: "StatisticalReport",
      //   name: "StatisticalReport",
      //   component: () => import("@/views/community/StatisticalReport"),
      //   meta: {
      //     title: "统计报表",
      //   },
      // },
      // {
      //   path: "StatisticalReportDetails",
      //   name: "StatisticalReportDetails",

      //   component: () => import("@/views/community/StatisticalReportDetails"),
      //   meta: {
      //     title: "统计报表详情",
      //   },
      //   hidden: true,
      // },
      {
        path: "statisticsData",
        name: "statisticsData",
        component: () => import("@/views/communityschedule/statisticsData"),
        meta: {
          title: "代表进社区统计",
        },
      },
      {
        path: "stationMember",
        name: "stationMember",
        component: () => import("@/views/communityschedule/stationMember"),
        meta: {
          title: "代表驻站情况",
        },
        hidden: true,
      },
      {
        path: "yearScheduled",
        name: "yearScheduled",
        component: () => import("@/views/communityschedule/yearScheduled"),
        meta: {
          title: "年度活动计划情况",
        },
        hidden: true,
      },
      {
        path: "communityMember",
        name: "communityMember",
        component: () => import("@/views/communityschedule/communityMember"),
        meta: {
          title: "代表进社区活动次数统计",
        },
        hidden: true,
      },
      {
        path: "liaisonStationActivity",
        name: "liaisonStationActivity",
        component: () =>
          import("@/views/communityschedule/liaisonStationActivity"),
        meta: {
          title: "活动情况",
        },
        hidden: true,
      },
      {
        path: "stationActivityMember",
        name: "stationActivityMember",
        component: () =>
          import("@/views/communityschedule/stationActivityMember"),
        meta: {
          title: "各区的代表进社区活动次数统计",
        },
        hidden: true,
      },
      {
        path: "solveProblem",
        name: "solveProblem",
        component: () => import("@/views/communityschedule/solveProblem"),
        meta: {
          title: "办理群众意见情况汇总",
        },
        hidden: true,
      },
      {
        path: "evaluationWork",
        name: "evaluationWork",
        component: () => import("@/views/communityschedule/evaluationWork"),
        meta: {
          title: "代表对联络站工作的评价情况汇总",
        },
        hidden: true,
      },
      {
        path: "liaisonStationActivityStatistics",
        name: "liaisonStationActivityStatistics",
        component: () =>
          import("@/views/communityschedule/liaisonStationActivityStatistics"),
        meta: {
          title: "联络站活动情况查询统计",
        },
        hidden: true,
      },
    ],
  },
  //省人大工作门户
  {
    path: "/scan_syhome",
    component: Layout,
    redirect: "noRedirect",
    name: "scanSyhome",
    title: "联系人民群众",
    meta: {
      title: "省人大工作门户",
    },
    children: [
      {
        path: "sydj_portal", // 新增二级路径
        name: "sydj_portal", // 新增路由名称
        component: () => import("@/views/sydj/sydj_portal"),
        meta: {
          title: "工作门户", // 新增标题
        },
      },
      {
        path: "sydj_workPortal", // 新增二级路径
        name: "sydj_workPortal", // 新增路由名称
        component: () => import("@/views/sydj/sydj_workPortal"),
        meta: {
          title: "联络站工作平台", // 新增标题
        },
      },
      {
        path: "sydj_station", // 新增二级路径
        name: "sydj_station", // 新增路由名称
        component: () => import("@/views/sydj/sydj_station"),
        meta: {
          title: "代表联络站管理", // 新增标题
        },
      },
      {
        path: "sydj_peopleAdvice", // 新增二级路径
        name: "sydj_peopleAdvice", // 新增路由名称
        component: () => import("@/views/sydj/sydj_peopleAdvice"),
        meta: {
          title: "群众意见管理", // 新增标题
        },
      },
      {
        path: "sydj_portalHlw", // 新增二级路径 互联网地址
        name: "sydj_portalHlw", // 新增路由名称
        component: () => import("@/views/sydj/sydj_portalHlw"),
        meta: {
          title: "工作门户", // 新增标题
        },
      },
      {
        path: "sydj_workPortalHlw", // 新增二级路径 互联网地址
        name: "sydj_workPortalHlw", // 新增路由名称
        component: () => import("@/views/sydj/sydj_workPortalHlw"),
        meta: {
          title: "联络站工作平台", // 新增标题
        },
      },
      {
        path: "sydj_stationHlw", // 新增二级路径 互联网地址
        name: "sydj_stationHlw", // 新增路由名称
        component: () => import("@/views/sydj/sydj_stationHlw"),
        meta: {
          title: "代表联络站管理", // 新增标题
        },
      },
      {
        path: "sydj_peopleAdviceHlw", // 新增二级路径 互联网地址
        name: "sydj_peopleAdviceHlw", // 新增路由名称
        component: () => import("@/views/sydj/sydj_peopleAdviceHlw"),
        meta: {
          title: "群众意见管理", // 新增标题
        },
      },
    ],
  },


  //网上联络站
  {
    path: "/BBS",
    component: Layout,
    redirect: "noRedirect",
    name: "BBS",
    title: "联系人民群众",
    meta: {
      title: "您扫码我行动",
      icon: "database",
      permissions: ["I_XTGLY"],
    },
    children: [
      {
        path: "manage_ListNotice",
        name: "manage_ListNotice",
        component: () => import("@/views/statistics/BBS/manage_ListNotice"),
        meta: {
          title: "群众意见处理",
        },
      },
      {
        path: "BBS_List",
        name: "BBS_List",
        component: () => import("@/views/statistics/BBS/list"),
        meta: {
          title: "联络站信息管理",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "stationMap",
        name: "StationMap",
        component: () => import("@/views/statistics/BBS/stationMap"),
        meta: {
          title: "云上联络站",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "tong_ListNotice",
        name: "tong_ListNotice",
        component: () => import("@/views/statistics/BBS/tong_ListNotice"),
        meta: {
          title: "线上反映问题汇总",
        },
      },
      {
        path: "onlineLiaisonStationStatistics",
        name: "onlineLiaisonStationStatistics",
        component: () =>
          import("@/views/statistics/BBS/onlineLiaisonStationStatistics"),
        meta: {
          title: "网上反映问题统计",
        },
      },
      {
        path: "dbDistrict/list",
        name: "/dbDistrict/list",
        component: () => import("@/views/dbDistrict/list"),
        meta: {
          title: "区镇进站代表列表",
        },
      },
      {
        path: "adviceList",
        name: "adviceList",
        component: () => import("@/views/statistics/BBS/adviceList"),
        meta: {
          title: "建言献策",
        },
      },
      // {
      //   path: "scan_syhome",
      //   name: "scan_syhome",
      //   component: AppMain,
      //   meta: {
      //     title: "省粤当家",
      //   },
      //   children: [
      //     {
      //       path: "sydj_portal", // 新增三级路径
      //       name: "sydj_portal", // 新增路由名称
      //       component: () => import("@/views/communityschedule/sydj_portal"),
      //       meta: {
      //         title: "工作门户", // 新增标题
      //       },
      //     },
      //     {
      //       path: "sydj_station", // 新增三级路径
      //       name: "sydj_station", // 新增路由名称
      //       component: () => import("@/views/communityschedule/sydj_station"),
      //       meta: {
      //         title: "联络站", // 新增标题
      //       },
      //     },
      //     {
      //       path: "peopleAdvice", // 新增三级路径
      //       name: "peopleAdvice", // 新增路由名称
      //       component: () => import("@/views/communityschedule/peopleAdvice"),
      //       meta: {
      //         title: "群众意见管理", // 新增标题
      //       },
      //     },
      //   ],
      // },
      {
        path: "add",
        name: "add",
        hidden: true,
        component: () => import("@/views/statistics/BBS/add"),
        meta: {
          title: "联络站编辑",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "details",
        name: "details",
        hidden: true,
        component: () => import("@/views/statistics/BBS/details"),
        meta: {
          title: "联络站详情",
          permissions: ["I_XTGLY"],
        },
      },

      // {
      //   path: "throughIndex",
      //   name: "throughIndex",
      //   component: () => import("@/views/dbReport/throughIndex"),
      //   meta: {
      //     title: "工委公告列表",
      //   },
      // },

      // {
      //   path: "dai_ListNotice",
      //   name: "dai_ListNotice",
      //   component: () => import("@/views/statistics/BBS/dai_ListNotice"),
      //   meta: {
      //     title: "联络站代表列表",
      //   },
      // },
      //
      {
        path: "appealData",
        name: "appealData",
        hidden: true,
        component: () => import("@/views/statistics/BBS/appealData"),
        meta: {
          title: "联络站详情",
        },
      },
      {
        path: "adviceDetails",
        name: "adviceDetails",
        hidden: true,
        component: () => import("@/views/statistics/BBS/adviceDetails"),
        meta: {
          title: "建言献策详情",
        },
      },


      {
        path: "appealData_ys",
        name: "appealData_ys",
        hidden: true,
        component: () => import("@/views/statistics/BBS/appealData_ys"),
        meta: {
          title: "联络站详情",
        },
      },


      {
        path: "appealData-tong",
        name: "appealData-tong",
        hidden: true,
        component: () => import("@/views/statistics/BBS/appealData-tong"),
        meta: {
          title: "联络站详情",
        },
      },
      //
      {
        path: "ListNotice_add",
        name: "ListNotice_add",
        hidden: true,
        component: () => import("@/views/statistics/BBS/edit"),
        meta: {
          title: "新增公告",
        },
      },
      {
        path: "ListNoticeDetail",
        name: "ListNoticeDetail",
        hidden: true,
        component: () => import("@/views/statistics/BBS/detail"),
        meta: {
          title: "公告详情",
        },
      },
      // //++代表联络站
      // {
      //   path: "site/website/MakeSuggestions",
      //   name: "/site/website/MakeSuggestions",
      //   component: () => import("@/views/statistics/BBS/DBLiaisonStation"),
      //   meta: {
      //     title: "代表联络站",
      //     permissions: ["I_XTGLY"],
      //   },
      // },
      // {
      //   path: "site/website/statisticsData",
      //   name: "/site/website/statisticsData",
      //   component: () => import("@/views/statistics/BBS/statisticsData"),
      //   meta: {
      //     title: "统计报表",
      //   },
      // },
      // {
      //   path: "dbReport/index",
      //   name: "dbReport/index",
      //   component: () => import("@/views/dbReport/index"),
      //   meta: {
      //     title: "代表使用统计报表",
      //   },
      // },
      {
        path: "through",
        name: "through",
        component: () => import("@/views/dbReport/throughIndex"),
        meta: {
          title: "代表个人社情民意专报",
        },
      },
      {
        path: "weekbook",
        name: "weekbook",
        component: () => import("@/views/dbReport/weekBook"),
        meta: {
          title: "代表个人周报",
        },
      },
      {
        path: "dbDistrict/edit",
        name: "/dbDistrict/edit",
        component: () => import("@/views/dbDistrict/edit"),
        meta: {
          title: "修改区镇代表信息",
        },
        hidden: true,
      },
      {
        path: "dbDistrict/add",
        name: "/dbDistrict/add",
        component: () => import("@/views/dbDistrict/add"),
        meta: {
          title: "新增区镇代表",
        },
        hidden: true,
      },
      {
        path: "BBS_ListNoticeDetail",
        name: "BBS_ListNoticeDetail",
        component: () => import("@/views/statistics/BBS/listNoticeDetail"),
        meta: {
          title: "进社区活动公告",
        },
        hidden: true,
      },
      // {
      //   path: "StatisticalReport",
      //   name: "StatisticalReport",
      //   component: () => import("@/views/community/StatisticalReport"),
      //   meta: {
      //     title: "意见建议分级统计图",
      //   },
      //   hidden: true,
      // },
      // {
      //   path: "StatisticalReportDetails",
      //   name: "StatisticalReportDetails",
      //
      //   component: () => import("@/views/community/StatisticalReportDetails"),
      //   meta: {
      //     title: "意见建议分级统计图详情",
      //   },
      //   hidden: true,
      // },
      // {
      //   path: "site/website/notRepliedStatistical",
      //   name: "/site/website/notRepliedStatistical",
      //   component: () => import("@/views/statistics/BBS/notRepliedStatistical"),
      //   meta: {
      //     title: "未及时回复意见统计表",
      //     permissions: ["I_XTGLY"],
      //   },
      //   hidden: true,
      // },
      // {
      //   path: "site/website/nickedDeleteTable",
      //   name: "/site/website/nickedDeleteTable",
      //   component: () => import("@/views/statistics/BBS/nickedDeleteTable"),
      //   meta: {
      //     title: "留痕删除意见统计表",
      //     permissions: ["I_XTGLY"],
      //   },
      //   hidden: true,
      // },
      // {
      //   path: "site/website/deleteTable",
      //   name: "/site/website/deleteTable",
      //   component: () => import("@/views/statistics/BBS/deleteTable"),
      //   meta: {
      //     title: "删除意见统计表",
      //     permissions: ["I_XTGLY"],
      //   },
      //   hidden: true,
      // },
      // {
      //   path: "site/website/opinionSuggestionTable",
      //   name: "/site/website/opinionSuggestionTable",
      //   component: () =>
      //     import("@/views/statistics/BBS/opinionSuggestionTable"),
      //   meta: {
      //     title: "意见建议统计表",
      //     permissions: ["I_XTGLY"],
      //   },
      //   hidden: true,
      // },
      // {
      //   path: "massOpinionCollection",
      //   name: "massOpinionCollection",
      //   component: () => import("@/views/statistics/BBS/massOpinionCollection"),
      //   meta: {
      //     title: "民呼我应(群众意见收集)",
      //     // permissions: ["I_XTGLY", "I_XXFBY"],
      //   },
      // },
      // {
      //   path: "massOpinionReview",
      //   name: "massOpinionReview",
      //   component: () => import("@/views/statistics/BBS/massOpinionReview"),
      //   meta: {
      //     title: "民呼我应(群众意见审查)",
      //     // permissions: ["I_XTGLY", "I_XXFBY"],
      //   },
      // },
      // {
      //   path: "massOpinions",
      //   name: "massOpinions",
      //   component: () => import("@/views/statistics/BBS/massOpinion"),
      //   meta: {
      //     title: "民呼我应(群众意见办理)",
      //     // permissions: ["I_XTGLY", "I_XXFBY"],
      //   },
      // },
    ],
  },
  // 省粤当家
  {
    path: "/scan_syhome",
    name: "scan_syhome",
    redirect: "noRedirect",
    component: Layout,
    title: "省粤当家",
    meta: {
      title: "省粤当家",
    },
    children: [
      {
        path: "sydj_portal", // 新增二级路径
        name: "sydj_portal", // 新增路由名称
        component: () => import("@/views/sydj/sydj_portal"),
        meta: {
          title: "工作门户", // 新增标题
        },
      },
      {
        path: "sydj_workPortal", // 新增二级路径
        name: "sydj_workPortal", // 新增路由名称
        component: () => import("@/views/sydj/sydj_workPortal"),
        meta: {
          title: "联络站工作平台", // 新增标题
        },
      },
      {
        path: "sydj_station", // 新增二级路径
        name: "sydj_station", // 新增路由名称
        component: () => import("@/views/sydj/sydj_station"),
        meta: {
          title: "代表联络站管理", // 新增标题
        },
      },
      {
        path: "sydj_peopleAdvice", // 新增二级路径
        name: "sydj_peopleAdvice", // 新增路由名称
        component: () => import("@/views/sydj/sydj_peopleAdvice"),
        meta: {
          title: "群众意见管理", // 新增标题
        },
      },
      {
        path: "sydj_portalHlw", // 新增二级路径 互联网地址
        name: "sydj_portalHlw", // 新增路由名称
        component: () => import("@/views/sydj/sydj_portalHlw"),
        meta: {
          title: "工作门户", // 新增标题
        },
      },
      {
        path: "sydj_workPortalHlw", // 新增二级路径 互联网地址
        name: "sydj_workPortalHlw", // 新增路由名称
        component: () => import("@/views/sydj/sydj_workPortalHlw"),
        meta: {
          title: "联络站工作平台", // 新增标题
        },
      },
      {
        path: "sydj_stationHlw", // 新增二级路径 互联网地址
        name: "sydj_stationHlw", // 新增路由名称
        component: () => import("@/views/sydj/sydj_stationHlw"),
        meta: {
          title: "代表联络站管理", // 新增标题
        },
      },
      {
        path: "sydj_peopleAdviceHlw", // 新增二级路径 互联网地址
        name: "sydj_peopleAdviceHlw", // 新增路由名称
        component: () => import("@/views/sydj/sydj_peopleAdviceHlw"),
        meta: {
          title: "群众意见管理", // 新增标题
        },
      },
    ],
  },

  //代表随手拍
  {
    path: "/conveniently",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "联系人民群众",
    meta: {
      title: "代表随手拍",
    },
    children: [
      {
        path: "recommended",
        name: "recommended",
        component: () => import("@/views/conveniently/recommended"),
        meta: {
          title: "随手拍意见建议上传",
        },
      },
      {
        path: "/recommended/details",
        name: "recommended",
        component: () => import("@/views/conveniently/details"),
        meta: {
          title: "随手拍意见建议上传详情",
        },
        hidden: true,
      },
      {
        path: "/recommended/details2",
        name: "recommended2",
        component: () => import("@/views/conveniently/details2"),
        meta: {
          title: "随手拍意见建议上传详情",
        },
        hidden: true,
      },
      {
        path: "/recommended/statisticDetail",
        name: "recommended",
        component: () => import("@/views/conveniently/statisticDetail"),
        meta: {
          title: "随手拍详情",
        },
        hidden: true,
      },
      // {
      //   path: "recommendation",
      //   name: "recommendation",
      //   component: () => import("@/views/conveniently/recommendation"),
      //   meta: {
      //     title: "随手拍意见建议处理",
      //   },
      // },
      // +小西
      {
        path: "recommendedStatistics",
        name: "recommendedStatistics",
        component: () => import("@/views/conveniently/recommendedStatistics"),
        meta: {
          title: "随手拍意见类型统计",
        },
      },
      {
        path: "statisticalAnalysis",
        name: "statisticalAnalysis",
        component: () => import("@/views/conveniently/statisticalAnalysis"),
        meta: {
          title: "统计分析",
        },
      },
      {
        path: "statisticalAnalysisByUser",
        name: "statisticalAnalysisByUser",
        component: () => import("@/views/dbReport/statisticalAnalysisByUser"),
        meta: {
          title: "代表个人随手拍",
        },
      },
    ],
  },

  //代表i履职使用情况
  {
    path: "/noRedirect",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "联系人民群众",
    meta: {
      title: "代表i履职使用情况",
    },
    children: [
      {
        path: "dbReport",
        name: "dbReport",
        component: () => import("@/views/dbReport/index"),
        meta: {
          title: "代表使用统计报表",
        },
      },
    ],
  },

  //系统管理
  {
    path: "/systemData",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "联系人民群众",
    meta: {
      title: "系统管理",
      permissions: ["ADMIN", "I_XTGLY"],
    },
    children: [
      {
        path: "infoMsg",
        name: "infoMsg",
        component: childrenAppMain,
        meta: {
          title: "消息管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        children: [
          {
            path: "sensitiveWord",
            name: "sensitiveWord",
            component: () => import("@/views/meeting/infoManagement/list"),
            meta: {
              title: "敏感词管理",
            },
          },
          {
            path: "mobileBlack",
            name: "mobileBlack",
            component: () => import("@/views/meeting/infoManagement/mobileBlacklist"),
            meta: {
              title: "黑名单管理",
            },
          },
        ]
      },
      {
        path:"SystemServerMonitor",
        name:"SystemServerMonitor",
        component:()=>import("@/views/serverMonitor/server/index"),
        meta:{
          title:"系统服务器监控"
        }
      },
      {
        path:"SystemCacheMonitor",
        name:"SystemCacheMonitor",
        component:()=>import("@/views/serverMonitor/cache/index"),
        meta:{
          title:"系统缓存监控"
        }
      },
      {
        path: "SystemAccessLog",
        name: "SystemAccessLog",
        component: () => import("@/views/meeting/journal/SystemAccessLog"),
        meta: {
          title: "系统访问日志",
        },
      },
      {
        path: "SystemOperationLog",
        name: "SystemOperationLog",
        component: () => import("@/views/meeting/journal/SystemOperationLog"),
        meta: {
          title: "系统操作日志",
        },
      },
      {
        path: "UserOperationAnalysis",
        name: "UserOperationAnalysis",
        component: () => import("@/views/meeting/journal/UserOperationAnalysis"),
        meta: {
          title: "用户操作分析",
        },
      },
      {
        path: "SystemPermissionLog",
        name: "SystemPermissionLog",
        component: () => import("@/views/meeting/journal/SystemPermissionLog"),
        meta: {
          title: "系统授权日志",
        },
      },
      {
        path: "LeadSecretary",
        name: "LeadSecretary",
        component: () => import("@/views/meeting/lead/LeadSecretary"),
        meta: {
          title: "领导秘书",
        },
      },
      {
        path: "job",
        name: "job",
        component: () => import("@/views/monitor/job/index"),
        meta: {
          title: "定时任务",
        },
      },
      {
        path: "joblog",
        name: "joblog",
        component: () => import("@/views/monitor/job/log"),
        meta: {
          title: "定时任务日志",
        },
      },
      {
        path: "LeadSecretaryInfo",
        name: "LeadSecretaryInfo",
        component: () => import("@/views/meeting/lead/LeadSecretaryInfo"),
        meta: {
          title: "领导秘书详情",
        },
        hidden: true,
      },
      {
        path: "logoManager",
        name: "logoManager",
        component: () => import("@/views/meeting/logoManager/logoManager"),
        meta: {
          title: "logo管理",
        },
      },
      {
        path: "logoManagerInfo",
        name: "logoManagerInfo",
        component: () => import("@/views/meeting/logoManager/logoManagerInfo"),
        meta: {
          title: "logo管理详情",
        },
        hidden: true,
      },
      {
        path: "loginManager",
        name: "loginManager",
        component: () => import("@/views/meeting/loginManager/loginManager"),
        meta: {
          title: "登录样式管理",
        },
      },
      {
        path: "synchronousManagement",
        name: "synchronousManagement",
        component: () => import("@/views/meeting/synchronousManagement/index"),
        meta: {
          title: "数据同步管理",
        },
      },
      {
        path: "dhManage",
        name: "dhManage",
        component: () => import("@/views/election/systemIndex/dhManage"),
        meta: {
          title: "代表大会管理",
        },
      },
      {
        path: "session",
        name: "session",
        component: () => import("@/views/election/systemIndex/electionMage"),
        // component: () => import("@/views/meeting/session/index"),
        meta: {
          title: "届次管理",
        },
      },
      {
        path: "gradingManagement",
        name: "gradingManagement",
        component: () =>
          import("@/views/election/systemIndex/gradingManagement"),
        // component: () => import("@/views/meeting/gradingManagement/index"),
        meta: {
          title: "分级管理",
        },
      },
      {
        path: "dataPrivilegeManagement",
        name: "dataPrivilegeManagement",
        component: () => import("@/views/meeting/edit"),
        meta: {
          title: "数据权限管理",
        },
      },
      {
        path: "parameterManagement",
        name: "parameterManagement",
        component: () => import("@/views/meeting/parameterManagement/index"),
        meta: {
          title: "参数管理",
        },
      },
      {
        path: "identityList",
        name: "IdentityList",
        component: () => import("@/views/meeting/identity/list"),
        meta: {
          title: "身份管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "newidentityList",
        name: "newIdentityList",
        component: () => import("@/views/meeting/identity/newlist"),
        meta: {
          title: "新的身份管理",
        },
      },
      {
        path: "identityRel",
        name: "IdentityRel",
        component: () => import("@/views/meeting/identity/rel"),
        meta: {
          title: "身份关联",
        },
      },
      {
        path: "newidentityRel",
        name: "newIdentityRel",
        component: () => import("@/views/meeting/identity/relNew"),
        meta: {
          title: "新身份关联",
        },
      },
      {
        path: "identityNewRel",
        name: "identityNewRel",
        component: () => import("@/views/meeting/identity/newRel"),
        meta: {
          title: "街道乡镇身份管理",
        },
      },
      {
        path: "districtDbTree",
        name: "districtDbTree",
        component: () => import("@/views/districtDbTree/index"),
        meta: {
          title: "区级机构树管理",
        },
      },
      {
        path: "meetingEdit",
        name: "MeetingEdit",
        component: () => import("@/views/meeting/edit"),
        meta: {
          title: "权限配置",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "identityAuth",
        name: "IdentityAuth",
        component: () => import("@/views/meeting/identity/auth"),
        meta: {
          title: "身份授权",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "resourceList",
        name: "ResourceList",
        component: () => import("@/views/meeting/resource/list"),
        meta: {
          title: "资源管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "verify",
        name: "verify",
        component: () => import("@/views/meeting/resource/list"),
        meta: {
          title: "二次验证配置",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },

      {
        path: "orgRangeConfig",
        name: "orgRangeConfig",
        component: () => import("@/views/orgRangeConfig/list"),
        meta: {
          title: "机构范围配置",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "dbLevelOrgRangeConfig",
        name: "dbLevelOrgRangeConfig",
        component: () => import("@/views/dbLevelOrgRangeConfig/list"),
        meta: {
          title: "分级代表树设置",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "roleLevel",
        name: "roleLevel",
        component: childrenAppMain,
        meta: {
          title: "分级分权基础设置",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        children: [
          {
            path: "levelRoleLayerList",
            name: "levelRoleLayerList",
            component: () => import("@/views/levelRole/levelRoleLayer/list"),
            meta: {
              title: "层级列表",
            },
          },
          {
            path: "levelRoleMainIdentifyList",
            name: "levelRoleMainIdentifyList",
            component: () => import("@/views/levelRole/levelRoleMainIdentify/list"),
            meta: {
              title: "业务编码列表",
            },
          },
          {
            path: "levelRoleItemIdentifyList",
            name: "levelRoleItemIdentifyList",
            component: () => import("@/views/levelRole/levelRoleItemIdentify/list"),
            meta: {
              title: "子项业务编码列表",
            },
          },
          {
            path: "levelRoleItemIdentifyUserManager",
            name: "levelRoleItemIdentifyUserManager",
            component: () => import("@/views/levelRole/levelRoleItemIdentify/userManager"),
            meta: {
              title: "子项业务绑定管理人员",
            },
            hidden: true,
          },
          {
            path: "levelRoleItemIdentifyUserList",
            name: "levelRoleItemIdentifyUserList",
            component: () => import("@/views/levelRole/levelRoleItemIdentify/userList"),
            meta: {
              title: "人员角色绑定列表",
            },
          },
          {
            path: "levelRoleItemIdentifyGroup",
            name: "levelRoleItemIdentifyGroup",
            component: () => import("@/views/levelRole/group/list"),
            meta: {
              title: "子业务编码角色组",
            },
          },
          {
            path: "groupItemIdentifyManager",
            name: "groupItemIdentifyManager",
            component: () => import("@/views/levelRole/group/groupItemIdentifyList"),
            meta: {
              title: "角色组绑定角色列表",
            },
            hidden: true,
          },
          {
            path: "groupUserManager",
            name: "groupUserManager",
            component: () => import("@/views/levelRole/group/groupUserList"),
            meta: {
              title: "角色组绑定管理人员",
            },
            hidden: true,
          },
        ]
      },
      {
        path: "virtualNode",
        name: "virtualNode",
        component: () => import("@/views/orgRangeConfig/virtualnode1/index"),
        meta: {
          title: "虚拟节点配置",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
      {
        path: "orgManage",
        name: "OrgManage",
        component: () => import("@/views/org/index"),
        meta: {
          title: "机构管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "authorizeOrg",
        name: "AuthorizeOrg",
        component: () => import("@/views/org/authorizeOrg"),
        meta: {
          title: "分配角色",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },

      {
        path: "userManage",
        name: "UserManage",
        component: () => import("@/views/user/index"),
        meta: {
          title: "用户管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "operLog",
        name: "operLog",
        component: () => import("@/views/operlog/index"),
        meta: {
          title: "操作日志",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "/systemManage/editUser",
        name: "/systemManage/EditUser",
        component: () => import("@/views/user/edit"),
        meta: {
          title: "编辑用户",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
      {
        path: "/systemManage/authorizeUser",
        name: "AuthorizeUser",
        component: () => import("@/views/user/authorizeUser"),
        meta: {
          title: "分配角色",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },

      {
        path: "relManage",
        name: "RelManage",
        component: () => import("@/views/rel/index"),
        meta: {
          title: "用户机构关联管理",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "authorityManage",
        name: "AuthorityManage",
        component: () => import("@/views/authority/index"),
        meta: {
          title: "权限管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "editAuthority",
        name: "EditAuthority",
        component: () => import("@/views/authority/edit"),
        meta: {
          title: "编辑权限",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
      {
        path: "roleManage",
        name: "RoleManage",
        component: () => import("@/views/role/index"),
        meta: {
          title: "角色管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "editRole",
        name: "EditRole",
        component: () => import("@/views/role/edit"),
        meta: {
          title: "编辑角色",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
      {
        path: "authorizeRole",
        name: "AuthorizeRole",
        component: () => import("@/views/role/authorizeRole"),
        meta: {
          title: "分配权限",
          permissions: ["ADMIN", "AUTHORIZE", "I_XTGLY"],
        },
        hidden: true,
      },
      {
        path: "editOrgScope",
        name: "EditOrgScope",
        component: () => import("@/views/role/editOrgScope"),
        meta: {
          title: "分配机构范围",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
      {
        //传参 /systemData/AdduserGroupManage
        path: "AdduserGroupManage",
        name: "AdduserGroupManage",
        component: () => import("@/views/usergroup/addUsergroup"),
        meta: {
          title: "新增用户组",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "UsergroupList",
        name: "UsergroupList",
        component: () => import("@/views/usergroup/UsergroupList"),
        meta: {
          title: "用户组管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "userGroupManage",
        name: "UserGroupManage",
        component: () => import("@/views/usergroup/index"),
        meta: {
          title: "用户组管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "authorizeUserGroup",
        name: "AuthorizeUserGroup",
        component: () => import("@/views/usergroup/authorizeUserGroup"),
        meta: {
          title: "分配角色",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
      {
        path: "menuManage",
        name: "MenuManage",
        component: () => import("@/views/menu/index"),
        meta: {
          title: "菜单管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "newmenuManage",
        name: "newMenuManage",
        component: () => import("@/views/menu/newindex"),
        meta: {
          title: "新的菜单管理",
        },
      },
      {
        path: "smsTemplate",
        name: "smsTemplate",
        component: () => import("@/views/smsTemplate/index"),
        meta: {
          title: "短信模板",
        },
      },
      // {
      //   path: "roleProcess",
      //   name: "roleProcess",
      //   component: () => import("@/views/roleProcess/index"),
      //   meta: {
      //     title: "测试授权审计记录",
      //   },
      // },
      // {
      //   path: "dataProcess",
      //   name: "dataProcess",
      //   component: () => import("@/views/dataProcess/index"),
      //   meta: {
      //     title: "角色功能授权审计记录",
      //   },
      // },
      {
        path: "orgRecord",
        name: "orgRecord",
        component: () => import("@/views/levelRole/orgRecord/index"),
        meta: {
          title: "数据权限数据范围授权记录",
        }
      },
      {
        path: "userRecord",
        name: "userRecord",
        component: () => import("@/views/levelRole/userRecord/index"),
        meta: {
          title: "数据权限授权角色授权记录",
        }
      },
      {
        path: "menuProcess",
        name: "menuProcess",
        component: () => import("@/views/menuProcess/index"),
        meta: {
          title: "功能菜单访问审计记录",
        }
      },
      {
        path: "highFrequency",
        name: "highFrequency",
        component: () => import("@/views/highFrequency/index"),
        meta: {
          title: "高频功能菜单统计",
        }
      },
      {
        path: "userProcess",
        name: "userProcess",
        component: () => import("@/views/userProcess/index"),
        meta: {
          title: "用户访问子系统审计记录",
        }
      },
      {
        path: "userFrequency",
        name: "userFrequency",
        component: () => import("@/views/userFrequency/index"),
        meta: {
          title: "用户访问子系统审计记录",
        }
      },
      {
        path: "systemErrMsg",
        name: "systemErrMsg",
        component: () => import("@/views/systemErrMsg/index"),
        meta: {
          title: "系统异常日志",
        }
      },
      {
        path: "SqlSlowOrErr",
        name: "SqlSlowOrErr",
        component: () => import("@/views/systemErrMsg/sqlSlowOrErr"),
        meta: {
          title: "异常查询与慢查询",
        }
      },
      {
        path: "exceptionInfo",
        name: "exceptionInfo",
        component: () => import("@/views/systemErrMsg/exceptionInfo"),
        meta: {
          title: "系统异常审计记录",
        }
      },
      {
        path: "remoteApiLog",
        name: "remoteApiLog",
        component: () => import("@/views/remoteApiLog/index"),
        meta: {
          title: "接口对接审计记录",
        }
      },
      {
        path: "loginLog",
        name: "loginLog",
        component: () => import("@/views/loginLog/index"),
        meta: {
          title: "用户登录审计日志",
        }
      },
      {
        path: "loginLogAnalysis",
        name: "loginLogAnalysis",
        component: () => import("@/views/loginLog/analysis"),
        meta: {
          title: "用户登录分析统计",
        }
      }
    ],
  },

  //
  // {
  //   path: "/publicopinion",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "representative",
  //   title: "联系人民群众",
  //   meta: {
  //     title: "社区民意收集",
  //   },
  //
  //   children: [
  //     {
  //       path: "/publicopinion/add",
  //       name: "PublicOpinionAdd",
  //       hidden: true,
  //       component: () => import("@/views/publicopinion/add"),
  //       meta: {
  //         title: "新增社情民意",
  //         permissions: ["I_XTGLY"],
  //       },
  //     },
  //     {
  //       path: "publilist",
  //       name: "PublicOpinionList",
  //       component: () => import("@/views/publicopinion/list"),
  //       meta: {
  //         title: "社情民意列表",
  //         icon: "database",
  //         permissions: ["I_XTGLY"],
  //       },
  //     },
  //     {
  //       path: "/publicopinion/details",
  //       name: "PublicOpinionDetails",
  //       hidden: true,
  //       component: () => import("@/views/publicopinion/details"),
  //       meta: {
  //         title: "新增社情民意",
  //         permissions: ["I_XTGLY"],
  //       },
  //     },
  //     {
  //       path: "listForMember",
  //       name: "listForMember",
  //       component: () => import("@/views/publicopinion/listForMember"),
  //       meta: {
  //         title: "代表社情民意",
  //         icon: "database",
  //         permissions: ["I_XTGLY"],
  //       },
  //     },
  //     {
  //       path: "listForStation",
  //       name: "listForStation",
  //       component: () => import("@/views/publicopinion/listForStation"),
  //       meta: {
  //         title: "联络站社情民意",
  //         icon: "database",
  //         permissions: ["I_XTGLY"],
  //       },
  //     },
  //     {
  //       path: "listForStreet",
  //       name: "listForStreet",
  //       component: () => import("@/views/publicopinion/listForStreet"),
  //       meta: {
  //         title: "社区社情民意",
  //         icon: "database",
  //         permissions: ["I_XTGLY"],
  //       },
  //     },
  //     {
  //       path: "connection",
  //       name: "connection",
  //       component: () => import("@/views/publicopinion/connection"),
  //       meta: {
  //         title: "民呼我应收集",
  //         //  permissions: ["I_XTGLY", "I_XXFBY"],
  //       },
  //     },
  //     {
  //       path: "representative",
  //       name: "representative",
  //       component: () => import("@/views/publicopinion/representative"),
  //       meta: {
  //         title: "代表随手拍收集",
  //         //  permissions: ["I_XTGLY", "I_XXFBY"],
  //       },
  //     },
  //     {
  //       path: "communityCollection",
  //       name: "communityCollection",
  //       component: () => import("@/views/publicopinion/communityCollection"),
  //       meta: {
  //         title: "代表进社区收集",
  //         //  permissions: ["I_XTGLY", "I_XXFBY"],
  //       },
  //     },
  //     {
  //       path: "otherWaysCollection",
  //       name: "otherWaysCollection",
  //       component: () => import("@/views/publicopinion/otherWaysCollection"),
  //       meta: {
  //         title: "其他途径收集",
  //         //  permissions: ["I_XTGLY", "I_XXFBY"],
  //       },
  //     },
  //   ],
  // },
  // {
  //   path: "/toRepresent",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "representative",
  //   title: "联系人民群众",
  //   meta: {
  //     title: "民呼我应(面向代表)",
  //   },
  //   children: [
  //     {
  //       path: "directly",
  //       name: "directly",
  //       component: () => import("@/views/toRepresent/directly"),
  //       meta: {
  //         title: "群众直接发来意见处理",
  //       },
  //     },
  //     {
  //       path: "transfer",
  //       name: "transfer",
  //       component: () => import("@/views/toRepresent/transfer"),
  //       meta: {
  //         title: "转来群众意见处理",
  //       },
  //     },
  //     {
  //       path: "mySetting",
  //       name: "mySetting",
  //       component: () => import("@/views/toRepresent/mySetting"),
  //       meta: {
  //         title: "我的设置",
  //       },
  //     },
  //     {
  //       path: "statisticalReport",
  //       name: "statisticalReport",
  //       component: () => import("@/views/toRepresent/statisticalReport"),
  //       meta: {
  //         title: "统计报表",
  //       },
  //     },
  //   ],
  // },
];
