<template>
  <div class="table-container">
    <!-- <a-row style="margin-left:1%">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18 }">
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="届次">
            <a-select v-model="queryForm.jcDm"
                      placeholder="请选择届次"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in getPeriodList"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="性别"
                             prop="sex">
            <a-select v-model="queryForm.xbDm"
                      placeholder="请选择性别"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in getSexList"
                               :key="item.xbDm"
                               :value="item.xbDm">{{ item.xbmc }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="姓名"
                             prop="name">
            <a-input v-model="queryForm.dbxm"
                     autocomplete="off"
                     placeholder="请输入姓名"
                     allow-clear
                     v-on:keyup.enter="fetchData"></a-input>
          </a-form-model-item>
        </a-col>
      </a-form-model>
      <a-col :md="6"
             :sm="24">
        <span style="float: right; margin-top: 3px;">
          <a @click="toggleAdvanced">
            {{ advanced ? "收起" : "高级搜索" }}
            <a-icon :type="advanced ? 'up' : 'down'" />
          </a>
          <a-button type="primary"
                    style="margin-left: 12px;"
                    @click="fetchData()">搜索</a-button>
          <a-button style="margin-left: 12px;"
                    @click="reset"
                    class="pinkBoutton">重置</a-button>

        </span>
      </a-col>
    </a-row>
    <a-row v-if="advanced"
           style="margin-left:1%">
      <a-col>
        <a-row>
          <a-form-model ref="queryForm"
                        :label-col="{ span: 6 }"
                        :wrapper-col="{ span: 18 }"
                        :model="queryForm"
                        layout="inline">
            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="民族">
                <a-select v-model="queryForm.mzDm"
                          placeholder="请选择民族"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getNationList"
                                   :key="item.mzDm"
                                   :value="item.mzDm">{{ item.mzmc }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="党派">
                <a-select v-model="queryForm.zzmmDm"
                          placeholder="请选择党派"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getPoliticalList"
                                   :key="item.zzmmDm"
                                   :value="item.zzmmDm">{{
                      item.politicsStatusName
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="行政区划">
                <a-select v-model="queryForm.xzqhDm"
                          placeholder="请选择行政区划"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getAdministrativeStateList"
                                   :key="item.xzqhDm"
                                   :value="item.xzqhDm">{{
                      item.xzqhmc
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="综合构成">
                <a-select v-model="queryForm.getSynthesizeList"
                          placeholder="请选择综合构成"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getSynthesizeList"
                                   :key="item.zhgcDm"
                                   :value="item.zhgcDm">{{
                      item.zhgcmc
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="职业构成">
                <a-select v-model="queryForm.zygcDm"
                          placeholder="请选择职业构成"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getOccupationList"
                                   :key="item.zygcDm"
                                   :value="item.zygcDm">{{
                      item.zygcmc
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="6"
                   class="overLength">
              <a-form-model-item style="width: 100%"
                                 label="是否为公务员">
                <a-select v-model="queryForm.gwybz"
                          placeholder="请选择是否为公务员"
                          allow-clear
                          style="width: 100%">
                  <a-select-option key
                                   value>全部</a-select-option>
                  <a-select-option key="1"
                                   value="1">是</a-select-option>
                  <a-select-option key="0"
                                   value="0">否</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="是否连任">
                <a-select v-model="queryForm.lrbz"
                          placeholder="请选择是否连任"
                          allow-clear
                          style="width: 100%">
                  <a-select-option key
                                   value>全部</a-select-option>
                  <a-select-option key="1"
                                   value="1">是</a-select-option>
                  <a-select-option key="0"
                                   value="0">否</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

            <a-col span="6"
                   class="overLength">
              <a-form-model-item style="width: 100%"
                                 label="同任两级以上">
                <a-select v-model="queryForm.trljdbbz"
                          placeholder="请选择是否同任两级以上"
                          allow-clear
                          style="width: 100%">
                  <a-select-option key
                                   value>全部</a-select-option>
                  <a-select-option key="1"
                                   value="1">是</a-select-option>
                  <a-select-option key="0"
                                   value="0">否</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

            <a-col span="6"
                   class="overLength">
              <a-form-model-item style="width: 100%"
                                 label="村委会人员">
                <a-select v-model="queryForm.cwhcdzbzcrybz"
                          placeholder="请选择是否村委会人员"
                          allow-clear
                          style="width: 100%">
                  <a-select-option key
                                   value>全部</a-select-option>
                  <a-select-option key="1"
                                   value="1">是</a-select-option>
                  <a-select-option key="0"
                                   value="0">否</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

            <a-col span="6"
                   class="overLength">
              <a-form-model-item style="width: 100%"
                                 label="是否归侨眷属">
                <a-select v-model="queryForm.gqbz"
                          placeholder="请选择是否归侨眷属"
                          allow-clear
                          style="width: 100%">
                  <a-select-option key
                                   value>全部</a-select-option>
                  <a-select-option key="1"
                                   value="1">是</a-select-option>
                  <a-select-option key="0"
                                   value="0">否</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="是否农民">
                <a-select v-model="queryForm.nmgbz"
                          placeholder="请选择是否农民"
                          allow-clear
                          style="width: 100%">
                  <a-select-option key
                                   value>全部</a-select-option>
                  <a-select-option key="1"
                                   value="1">是</a-select-option>
                  <a-select-option key="0"
                                   value="0">否</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

            <a-col span="6"
                   class="overLength">
              <a-form-model-item style="width: 100%"
                                 label="是否单位负责人">
                <a-select v-model="queryForm.fzrbz"
                          placeholder="请选择是否单位负责人"
                          allow-clear
                          style="width: 100%">
                  <a-select-option key
                                   value>全部</a-select-option>
                  <a-select-option key="1"
                                   value="1">是</a-select-option>
                  <a-select-option key="0"
                                   value="0">否</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="6">
              <a-form-model-item style="width: 100%"
                                 label="年龄范围"
                                 prop="name">

                <div style="display: flex;">
                  <a-input v-model="queryForm.nlMin"
                           style="width:45%" />
                  <p style="width:10%;text-align: center;">至</p>
                  <a-input v-model="queryForm.nlMax"
                           style="width:45%" />
                </div>
              </a-form-model-item>
            </a-col>
            <a-col span="6"
                   class="overLength">
              <a-form-model-item style="width: 100%"
                                 label="全日制教育学历">
                <a-select v-model="queryForm.qrzxl"
                          placeholder="请选择全日制教育学历"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getEducationStateList"
                                   :key="item.xlDm"
                                   :value="item.xlDm">{{
                      item.xlmc
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="6"
                   class="overLength">
              <a-form-model-item style="width: 100%"
                                 label="在职学历代码">
                <a-select v-model="queryForm.zzxl"
                          placeholder="请选择在职学历代码"
                          allow-clear
                          style="width: 100%">
                  <a-select-option v-for="item in getEducationStateList"
                                   :key="item.xlDm"
                                   :value="item.xlDm">{{
                      item.xlmc
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

          </a-form-model>
        </a-row>
      </a-col>
    </a-row> -->

    <SearchForm @onReset="reset" @onSearch="fetchData">
      <template v-slot:topSearch>
        <SingleSelect :title="'届次'" :selectList="getPeriodList"  :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm" />
        <SingleSelect :title="'性别'" :selectList="getSexList"  :showName="'xbmc'" :showValue="'xbDm'" :value.sync="queryForm.xbDm" />
        <SingleSearch @onEnter="fetchData" :title="'姓名'" :value.sync="queryForm.dbxm" />
      </template>
      <template v-slot:moreSearch>
        <SingleSelect :title="'民族'" :selectList="getNationList" :showName="'mzmc'" :showValue="'mzDm'" :value.sync="queryForm.mzDm" />
        <SingleSelect :title="'党派'" :selectList="getPoliticalList" :showName="'politicsStatusName'" :showValue="'zzmmDm'" :value.sync="queryForm.zzmmDm" />
        <SingleSelect :title="'行政区划'" :selectList="getAdministrativeStateList" :showName="'xzqhmc'" :showValue="'xzqhDm'" :value.sync="queryForm.xzqhDm" />
        <SingleSelect :title="'综合构成'" :selectList="getSynthesizeList" :showName="'zhgcmc'" :showValue="'zhgcDm'" :value.sync="queryForm.getSynthesizeList" />
        <SingleSelect :title="'职业构成'" :selectList="getOccupationList" :showName="'zygcmc'" :showValue="'zygcDm'" :value.sync="queryForm.zygcDm" />
        <SingleSelect :title="'是否为公务员'" :selectList="checkList" :value.sync="queryForm.gwybz" />
        <SingleSelect :title="'是否连任'" :selectList="checkList" :value.sync="queryForm.lrbz" />
        <SingleSelect :title="'同任两级以上'" :selectList="checkList" :value.sync="queryForm.trljdbbz" />
        <SingleSelect :title="'是否归侨眷属'" :selectList="checkList" :value.sync="queryForm.gqbz" />
        <SingleSelect :title="'村委会人员'" :selectList="checkList" :value.sync="queryForm.cwhcdzbzcrybz" />
        <SingleSelect :title="'是否农民'" :selectList="checkList" :value.sync="queryForm.nmgbz" />
        <SingleSelect :title="'是否单位负责人'" :selectList="checkList" :value.sync="queryForm.fzrbz" />
        <SingleSelect :title="'全日制教育学历'" :selectList="getEducationStateList" :showName="'xlmc'" :showValue="'xlDm'" :value.sync="queryForm.qrzxl" />
        <SingleSelect :title="'在职学历代码'" :selectList="getEducationStateList" :showName="'xlmc'" :showValue="'xlDm'" :value.sync="queryForm.zzxl" />
        <NumRangePicker :minValue.sync="queryForm.nlMin" :maxValue.sync="queryForm.nlMax" />
      </template>
    </SearchForm>

    <a-row style="margin: 10px 0px 10px 8px;">
      <a-col :span="6">
        <a-dropdown type="primary"
                    style="margin-left: 8px;">
          <a-menu slot="overlay">
            <a-menu-item key="1">
              <span @click="download">导出</span>
            </a-menu-item>
            <a-menu-item key="2">
              <span @click="downloadImg">导出照片</span>
            </a-menu-item>
          </a-menu>
          <a-button>
            导出
            <a-icon type="down" />
          </a-button>
        </a-dropdown>
      </a-col>
    </a-row>
    <a-row style="margin-top: 20px;">
      <standard-table :columns="columns"
                      row-key="ID"
                      :data-source="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selected-rows.sync="selectedRows"
                      @selectedRowChange="onSelectChange"></standard-table>
    </a-row>
  </div>
</template>
<script>
import { myPagination } from "@/mixins/pagination.js";
import {
  preliminary,
  Secondary,
  getPeriod,
  getSex,
  getNation,
  getPolitical,
  getRecommend,
  getSynthesize,
  getOccupation,
  getEducationState,
  getAdministrativeState,
} from "@/api/election.js";
import StandardTable from "@/components/table/StandardTable";
import { instance_1 } from "@/api/axiosRq";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
import NumRangePicker from '@/components/NumRangePicker/index';
import UnitPicker from '@/components/UnitPicker/index';

export default {
  // 预备人选名册
  components: { 
    StandardTable,
    SingleSelect,
    SearchForm,
    SingleSearch,
    NumRangePicker,
    UnitPicker
  },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,

      jcDm: "",
      tableKey: [],
      tableData: [],
      onSjmelyDmList: [],
      advanced: false,
      periods: [
        // 议案届别
        { id: 16, name: "十五届六次" },
        { id: 15, name: "十五届一次" },
        { id: 1, name: "十五届二次" },
        { id: 2, name: "十五届三次" },
        { id: 3, name: "十五届四次" },
        { id: 4, name: "十五届五次" },
      ],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        jcDm: "" /* 届次id */,
        dbxm: "" /* 代表姓名 */,
        xbDm: "" /* 性别id */,
        mzDm: "" /* 民族id */,
        nlMin: "" /* 年龄起始 */,
        nlMax: "" /* 年龄结束 */,
        zzmmDm: "" /* 政治面貌id */,
        xzqhDm: "" /* 行政区划 */,
        zhgcDm: "" /* 综合构成id */,
        zygcDm: "" /* 职业构成代码 */,
        qrzxl: "" /* 全日制学历代码 */,
        zzxl: "" /* 在职学历代码 */,
        gwybz: "" /* 是否公务员 */,
        lrbz: "" /* 是否连任代表 */,
        trljdbbz: "" /* 是否同任两级代表 */,
        cwhcdzbzcrybz: "" /* 是否村委会村党支部组成人员 */,
        gqbz: "" /* 是否归侨侨眷 */,
        nmgbz: "" /* 是否农民工 */,
        fzrbz: "" /* 是否事业单位负责人 */,
        sort: "createTime",
        order: "descend",
      },
      columns: [
        {
          title: "届次",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "LEVEL_NAME",
        },
        {
          title: "姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "USER_NAME",
        },
        {
          title: "性别",
          align: "center",
          width: 90,
          ellipsis: true,
          dataIndex: "sex",
          customRender: (text, record, index) => {
            return record.XB_DM == "1" ? "男" : "女";
          },
        },
        {
          title: "出生日期",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "BIRTHDAY",
          customRender: (text, record, index) => {
            // return text.replace("T", " ").split("Z").join("").substr(0, 19);
            return text.slice(0, text.indexOf("T")) || '/';
          },
        },

        {
          title: "民族",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "MZMC",
        },
        {
          title: "政治面貌",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "POLITICS_STATUS_NAME",
        },
        {
          title: "籍贯",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "NATIVE_PLACE",
        },
        {
          title: "工作单位",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "WORK_NUIT",
        },
        {
          title: "职务",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "JOB",
        },
        {
          title: "职称",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "JOB_TITLE",
        },
        {
          title: "全日制教育学历",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "QRZJYXLMC",
        },
        {
          title: "在职教育学历",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "ZZJYXLMC",
        },
        {
          title: "是否为公务员",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "SF_GWYBZ",
          customRender: (text, record, index) => {
            return record.SF_GWYBZ == "1" ? "是" : "否";
          },
        },
        {
          title: "是否连任代表",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "SF_LRBZ",
          customRender: (text, record, index) => {
            return record.SF_LRBZ == "1" ? "是" : "否";
          },
        },
        {
          title: "是否同任两级以上代表",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "SF_TRLJDBBZ",
          customRender: (text, record, index) => {
            return record.SF_TRLJDBBZ == "1" ? "是" : "否";
          },
        },
        {
          title: "是否村委会村党支部组成人员",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "SF_CWHCDZBZCRYBZ",
          customRender: (text, record, index) => {
            return record.SF_CWHCDZBZCRYBZ == "1" ? "是" : "否";
          },
        },
        {
          title: "是否归侨眷属",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "SF_GQBZ",
          customRender: (text, record, index) => {
            return record.SF_GQBZ == "1" ? "是" : "否";
          },
        },
        {
          title: "是否农民工",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "SF_NMGBZ",
          customRender: (text, record, index) => {
            return record.SF_NMGBZ == "1" ? "是" : "否";
          },
        },
        {
          title: "是否事业单位负责人",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "SF_FZRBZ",
          customRender: (text, record, index) => {
            return record.SF_FZRBZ == "1" ? "是" : "否";
          },
        },
        {
          title: "综合构成",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "zhgcmc",
        },
        {
          title: "职业构成",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "ZYGCMC",
        },
        {
          title: "推荐单位",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "MELYMC",
        },
        {
          title: "录入人名称",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "CREATOR_NAME",
        },
        {
          title: "录入日期",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "CREATE_TIME",
          customRender: (text, record, index) => {
            return text.replace("T", " ").split("Z").join("").substr(0, 19);
          },
        },
      ],
      dataSource: [],
      selectedRows: [],
      getPeriodList: [] /* 获取届次 */,
      getSexList: [] /* 获取性别 */,
      getNationList: [] /* 获取民族 */,
      getPoliticalList: [] /* 获取出党派下拉数据列表 */,
      getRecommendList: [] /* 获取出推荐单位下拉数据列表 */,
      getSynthesizeList: [] /* 获取出综合构成下拉数据列表 */,
      getOccupationList: [] /* 获取出职业构成下拉数据列表 */,
      getAdministrativeStateList: [] /* 行政区划下拉数据 */,
      getEducationStateList: [] /* 获取在职教育学历、全日制教育学历下拉数据列表 */,
      selectData: [
        /*  是：1     否：0 */ { id: "", name: "全部" },
        { id: "1", name: "是" },
        { id: "0", name: "否" },
      ],
      checkList: [
        {name: '全部', id: ''},
        {name: '是', id: '1'},
        {name: '否', id: '0'},
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "投票选举管理");
    this.$store.dispatch("navigation/breadcrumb2", "代表登记表");
    this.selectList();
  },
  methods: {
    async selectList () {
      let res = await getPeriod();
      if (res.code == "0000") {
        this.getPeriodList = res.data;
        this.queryForm.jcDm = res.data[0].jcDm; //改过
        this.jcDm = res.data[0].jcDm;
      }
      let res1 = await getSex();
      if (res1.code == "0000") {
        this.getSexList = res1.data;
      }
      let res2 = await getNation();
      if (res2.code == "0000") {
        this.getNationList = res2.data;
      }
      let res3 = await getPolitical();
      if (res3.code == "0000") {
        this.getPoliticalList = res3.data;
      }
      let res4 = await getRecommend();
      if (res4.code == "0000") {
        this.getRecommendList = res4.data;
        this.getRecommendList.unshift({ melyDm: "", melymc: "全部" });
      }
      let res5 = await getSynthesize();
      if (res5.code == "0000") {
        this.getSynthesizeList = res5.data;
      }
      let res6 = await getOccupation();
      if (res6.code == "0000") {
        this.getOccupationList = res6.data;
      }
      let res7 = await getEducationState();
      if (res7.code == "0000") {
        this.getEducationStateList = res7.data;
      }
      let res8 = await getAdministrativeState();
      if (res8.code == "0000") {
        this.getAdministrativeStateList = res8.data;
        this.getAdministrativeStateList.unshift({ xzqhmc: "全部", xzqhDm: "" });
      }
      this.fetchData();
    },
    // 获取数据
    fetchData () {
      this.TBloading = true;

      instance_1({
        url: "/representative/behalfRegister/queryBehalfRegis",
        method: "post",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "200") {
          this.dataSource = res.data.rows;
          this.pagination.total = res.data.total;
          this.TBloading = false;
        }
      });
    },
    // 导出
    download () {
      if (this.tableKey.length == "0") {
        this.$message.warning("您还暂未勾选数据");
      } else {
        // let {jc_dm,level_name}= this.tableData[0];

        instance_1({
          url: "/representative/behalfRegister/export",
          method: "post",
          responseType: "blob",
          data: this.tableKey,
        }).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          this.tableKey.length == "1"
            ? (a.download = `代表登记表.xls`)
            : (a.download = `代表登记表.zip`);
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
        });
      }
    },
    // 导出照片
    downloadImg () {
      if (this.tableKey.length == "0") {
        this.$message.warning("您还暂未勾选数据");
      } else {
        // let {jc_dm,level_name}= this.tableData[0]
        instance_1({
          url: "/representative/behalfRegister/exportPhotos",
          method: "post",
          responseType: "blob",
          data: this.tableKey,
        }).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `代表登记表图片.zip`;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
        });
      }
    },
    //  主下拉框
    async onSjmelyDm (id) {
      let res = await Secondary({ id });
      if (res.code == "0000") {
        this.onSjmelyDmList = res.data;
        this.onSjmelyDmList.unshift({ melyDm: "", melymc: "全部" });
        this.queryForm.melymc = this.onSjmelyDmList[0].melymc;
      }
    },
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.queryForm.jcDm = this.jcDm;
      this.fetchData();
    },
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    onSelectChange (key, data) {
      this.tableKey = key;
      this.tableData = data;
    },
  },
};
</script>
<style scoped>
.formBox {
  /* display: flex; */
  padding: 0 20px;
}
</style>
