<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="24"
             :xl="24">
        <a-row style="margin-left:1%">
          <a-form-model ref="listQuery"
                        :model="listQuery"
                        :label-col="{ span: 5 }"
                        :wrapper-col="{ span: 18 }">
            <!-- <a-col span="9">
              <a-form-model-item style="float: left; margin-right: 5px;">
                <a-button type="primary" @click="handleAdd">新增</a-button>
              </a-form-model-item>
              <a-form-model-item style="float: left; margin-right: 5px;">
                <a-button type="primary" @click="addSocialconditions"
                  >社情民意</a-button
                >
              </a-form-model-item>
              <a-form-model-item style="float: left; margin-right: 5px;">
                <a-button type="primary" @click="downloadExcel"
                  >下载模版</a-button
                >
              </a-form-model-item>
              <a-form-model-item style="float: left; margin-right: 5px;">
                <a-upload
                  name="file"
                  accept=".xls"
                  :multiple="true"
                  :file-list="fileList"
                  @change="downImport"
                  :before-upload="beforeUpload"
                >
                  <a-button type="primary"> 导入</a-button>
                </a-upload>
              </a-form-model-item>
            </a-col> -->
            <a-col :span="6">
              <a-form-model-item label="届次">
                <a-select v-model="listQuery.session"
                          placeholder="请选择届次"
                          allow-clear>
                  <a-select-option v-for="item in SessionList"
                                   :key="item.id"
                                   :label="item.id"
                                   :value="item.id">{{item.name}}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="活动状态">
                <a-select v-model="listQuery.status"
                          placeholder="请选择活动状态"
                          allow-clear>
                  <a-select-option :value="0">
                    待审核
                  </a-select-option>
                  <a-select-option :value="1">
                    已审核
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="年度">
                <a-input v-model="listQuery.year"
                         placeholder="请选择年度"
                         allow-clear
                         v-on:keyup.enter="search" />
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <span style="float: right; margin-top: 3px;">
                <a style="margin-right: 8px;"
                   @click="toggleAdvanced">
                  {{  advanced ? "收起" : "高级搜索"  }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
                <a-button type="primary"
                          @click="search">搜索</a-button>
                <a-button style="margin-left: 12px;"
                          @click="reset"
                          class="pinkBoutton">重置</a-button>
              </span>
            </a-col>
          </a-form-model>
        </a-row>
        <a-row style="margin-left:1%"
               v-if="advanced">
          <a-form-model ref="listQuery"
                        :model="listQuery"
                        :label-col="{ span: 5 }"
                        :wrapper-col="{ span: 18 }">
            <!-- 更多查询条件抽屉 -->

            <a-col :span="6">
              <a-form-model-item label="行政区县"
                                 prop="administrativeAreaId">
                <a-select v-model="listQuery.administrativeAreaId"
                          placeholder="请选择行政区县"
                          @change="handleAdministrativeAreaChange"
                          allow-clear>
                  <a-select-option v-for="item in administrativeAreas"
                                   :key="item.id"
                                   :label="item.name"
                                   :value="item.id">{{  item.name  }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="乡镇街道"
                                 prop="streetTownId">
                <a-select v-model="listQuery.streetTownId"
                          @change="handlestreetTownChange"
                          placeholder="请选择乡镇街道"
                          allow-clear
                          show-search
                          :open="streetTownIdState"
                          @select="streetTownIdState = false"
                          @focus="streetTownIdFocus">
                  <a-select-option v-for="item in streetTowns"
                                   :key="item.id"
                                   :label="item.name"
                                   :value="item.id">{{item.name}}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <!-- 9.6 -->
            <a-col :span="6">
              <a-form-model-item label="联络站">
                <a-select v-model="listQuery.liaisonStationId"
                          allow-clear
                          show-search
                          :open="liaisonStationIdState"
                          @select="liaisonStationIdState = false"
                          @focus="liaisonStationIdFocus"
                          @change="handleListLiaisonStationChange"
                          placeholder="请选择联络站">
                  <a-select-option v-for="item in liaisonStations"
                                   :key="item.id"
                                   :label="item.name"
                                   :value="item.id">{{item.name}}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="代表姓名">
                <a-input v-model="listQuery.userName"
                         v-on:keyup.enter="search"
                         placeholder="请输入代表姓名"
                         allow-clear />
              </a-form-model-item>
            </a-col>
            <a-col :span="6"
                   class="overLength">
              <a-form-model-item label="联系人名称">
                <a-input v-model="listQuery.contactName"
                         v-on:keyup.enter="search"
                         placeholder="请输入联系人名称"
                         allow-clear />
              </a-form-model-item>
            </a-col>
            <!-- <a-col span="6">
              <a-form-item label="开始时间"
                           prop="startTime"
                           style="width: 100%">
                <a-date-picker v-model="listQuery.startTime"
                               allow-clear
                               value-format="YYYY-MM-DD "
                               placeholder="选择开始时间"
                               style="width: 100%;"
                               :disabled-date="(current) =>current && listQuery.endTime?
                                    current.valueOf() >=moment(new Date(listQuery.endTime)).valueOf(): false"></a-date-picker>
              </a-form-item>
            </a-col>
            <a-col span="6">
              <a-form-item label="结束时间"
                           prop="endTime"
                           style="width: 100%">
                <a-date-picker v-model="listQuery.endTime"
                               allow-clear
                               value-format="YYYY-MM-DD"
                               placeholder="选择结束时间"
                               style="width: 100%;"
                               :disabled-date="(current) =>current && listQuery.startTime?
                  moment(new Date(listQuery.startTime)).valueOf() >=current.valueOf(): false"></a-date-picker>
              </a-form-item>
            </a-col> -->

            <a-col span="6">
              <a-form-model-item style="width: 100%;"
                                 label="时间范围">
                <a-range-picker style="width: 100%;"
                                :ranges="{
                  最近三个月: [
                    moment(new Date()).subtract(2, 'months'),
                    moment(),
                  ],
                  '今年1-6月': [
                    moment(moment().startOf('year')).startOf('month'),
                    moment(moment().startOf('year'))
                      .add(5, 'months')
                      .endOf('month'),
                  ],
                  '今年7-12月': [
                    moment(moment().startOf('year'))
                      .add(6, 'months')
                      .startOf('month'),
                    moment(moment().startOf('year'))
                      .add(11, 'months')
                      .endOf('month'),
                  ],
                  今年内: [
                    moment(moment().startOf('year')).startOf('month'),
                    moment(moment().startOf('year'))
                      .add(11, 'months')
                      .endOf('month'),
                  ],
                }"
                                v-model="dateRange"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                @change="onTimeChange" />
              </a-form-model-item>
            </a-col>
            <!-- 9.6 -->
          </a-form-model>
        </a-row>
        <a-row style="margin: 5px 0px 10px 8px;">
          <a-col :span="12"
                 style="margin-bottom:10px">
            <a-button type="primary"
                      style="margin-left:12px"
                      @click="handleAdd"
                      v-show="isDb == 0">新增</a-button>
            <a-button type="primary"
                      style="margin-left:12px"
                      @click="handleAdd2"
                      v-show="isDb == 0">新增2</a-button>

            <a-button type="primary"
                      @click="downloadExcel"
                      style="margin-left:12px">下载模版</a-button>
            <div style=" display: inline-block;margin-left:12px">
              <a-upload name="file"
                        accept=".xls"
                        :multiple="true"
                        :file-list="fileList"
                        @change="downImport"
                        :before-upload="beforeUpload">
                <a-button type="primary"> 导入</a-button>
              </a-upload>
            </div>
            <a-button type="primary"
                      @click="downloaData"
                      style="margin-left:12px"
                      :loading="downloadLoading">导出
            </a-button>
          </a-col>
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator"
                :spinning="listLoading">
          <!-- 有下拉的table需要  v-if="list.length > 0"  -->
          <a-table ref="table"
                   :bordered="false"
                   class="directorySet-table"
                   size="small"
                   :columns="columns"
                   :pagination="pagination"
                   :customRow="clickRow"
                   :row-selection="{selectedRowKeys: selectedRowKeys,onChange: onSelectChange,}"
                   :data-source="list"
                   :row-key="  (record, index) => {    return record.id;  }"
                   :scroll="{ x: 300, y: 0 }"></a-table>
        </a-spin>
      </a-col>
    </a-row>

    <!-- 新增表单 -->
    <a-modal :title="title"
             :visible.sync="addDialogVisible"
             width="50%"
             destroyOnClose
             @cancel="close()">
      <a-form-model ref="form"
                    :model="form"
                    :rules="rules"
                    :label-col="{ span: 8 }"
                    :wrapper-col="{ span: 16 }">
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="届次"
                               prop="session">
              <!-- <a-input v-model="form.session" auto-complete="off" /> -->
              <a-select v-model="form.session"
                        placeholder="请选择届次"
                        allow-clear
                        show-search>
                <a-select-option v-for="item in SessionList"
                                 :key="item.id"
                                 :label="item.id"
                                 :value="item.id">
                  {{  item.name  }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="10"
                 :offset="2">
            <a-form-model-item label="时间"
                               prop="serverTime">
              <a-date-picker v-model="form.serverTime"
                             value-format="YYYY-MM-DD HH:mm:ss"
                             show-time
                             placeholder="选择日期时间"
                             @change="handleServerTime">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="年度">
              <a-input v-model="form.year"
                       disabled
                       auto-complete="off" />
            </a-form-model-item>
          </a-col>
          <a-col :span="10"
                 :offset="2">
            <a-form-model-item label="行政区县"
                               prop="administrativeAreaId">
              <a-select v-model="form.administrativeAreaId"
                        style="width: 100%;"
                        placeholder="请选择行政区县"
                        @change="handleAdministrativeAreaChange_add">
                <a-select-option v-for="item in administrativeAreas"
                                 :key="item.id"
                                 :label="item.name"
                                 :value="item.id">
                  {{  item.name  }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="乡镇街道"
                               prop="streetTownId">
              <a-select v-model="form.streetTownId"
                        style="width: 100%;"
                        placeholder="请选择乡镇街道"
                        allow-clear
                        show-search
                        :open="streetTownIdState_add"
                        @select="streetTownIdState_add = false"
                        @focus="streetTownIdFocus_add"
                        @change="handlestreetTownChange_add">
                <a-select-option v-for="item in streetTowns"
                                 :key="item.id"
                                 :label="item.name"
                                 :value="item.id">{{item.name}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="10"
                 :offset="2">
            <a-form-model-item label="联络站"
                               prop="liaisonStationId">
              <a-select v-model="form.liaisonStationId"
                        placeholder="请选择联络站"
                        @change="handleListLiaisonStationChange"
                        allow-clear
                        show-search
                        :open="liaisonStationIdState_add"
                        @select="liaisonStationIdState_add = false"
                        @focus="liaisonStationIdFocus_add">
                <a-select-option v-for="item in liaisonStations"
                                 :key="item.id"
                                 :label="item.name"
                                 :value="item.id">{{
                   item.name
                  }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="联系人姓名"
                               prop="contactName">
              <a-input v-model="form.contactName"
                       allow-clear
                       show-search
                       placeholder="请输入联系人姓名"
                       auto-complete="off" />
            </a-form-model-item>
          </a-col>
          <a-col :span="10"
                 :offset="2">
            <a-form-model-item label="联系人电话"
                               allow-clear
                               show-search
                               placeholder="请输入联系人电话"
                               prop="contactPhoneNumber">
              <a-input v-model="form.contactPhoneNumber"
                       auto-complete="off" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="受邀代表"
                               prop="inviteRangeDesc"
                               :label-col="{ span: 3 }"
                               :wrapper-col="{ span: 20 }">
              <div class="searchStyle">
                <a-input v-model="form.inviteRangeDesc"
                         disabled
                         enter-button></a-input>
                <a-button :disabled="showDisabled"
                          type="primary"
                          icon="search"
                          @click="openattendMembers"></a-button>
              </div>
            </a-form-model-item>
          </a-col>

        </a-row>
        <a-row>

          <a-col :span="24">
            <a-form-model-item label="活动内容"
                               prop="mark"
                               :label-col="{ span: 3 }"
                               :wrapper-col="{ span: 20 }">
              <a-input v-model="form.mark"
                       allow-clear
                       show-search
                       placeholder="请输入活动内容"
                       type="textarea"
                       :rows="5"
                       auto-complete="off" />
            </a-form-model-item>
          </a-col>

          <a-col :span="10">

          </a-col>
        </a-row>
      </a-form-model>
      <span slot="footer"
            class="dialog-footer">
        <a-button @click="addDialogVisible = false">取 消</a-button>
        <a-button type="primary"
                  @click="confirm">保 存</a-button>
      </span>
    </a-modal>
    <!-- 审核dialog -->
    <a-modal title="审核意见"
             :visible.sync="auditDialogVisible"
             width="50%"
             top="20vh"
             center
             destory-on-close
             @cancel="initAudit()">
      <a-col :xs="24">
        <a-form ref="auditForm"
                :model="auditForm"
                :label-width="formLabelWidth">
          <a-row>
            <a-col>
              <a-form-item label="意见">
                <a-textarea v-model="auditOpinion"
                            auto-complete="off"
                            rows="8" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
      <span slot="footer"
            class="dialog-footer">
        <a-button type="danger"
                  @click="audit(2)">不通过</a-button>
        <a-button type="primary"
                  @click="audit(1)">通过</a-button>
      </span>
    </a-modal>

    <!-- 受邀代表的树 -->
    <invitedTree ref="ShowinvitedTree"
                 :jc-dm="form.session"
                 :default-select="defaultSelect"
                 @confirm="confirminvitedTree"></invitedTree>
  </div>
</template>

<script>
import {
  getList,
  save,
  update,
  getCommunityScheduleById,
  audit,
  downloadCommunityPlanExcel,
  addCommunityPlanExcel,
  ExportExcelData
} from "@/api/communityschedule";
import invitedTree from "@/views/common/representative/invitedTree.vue";
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
import { listForAdmin as getStations } from "@/api/station";
import { instance_1, instance_2 } from "@/api/axiosRq";
import SessionList from '@/view/components/sessionSelect.vue';
export default {
  components: { invitedTree, SessionList },
  filters: {},
  data () {
    var validateSession = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请填写届次信息"));
      }
      callback();
    };
    var validateServerTime = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请选择时间"));
      }
      callback();
    };
    var validateAdministrativeArea = (rule, value, callback) => {
      if (value === "" || value === undefined) {
        callback(new Error("请选择行政区县"));
      }
      callback();
    };
    var validateStreetTown = (rule, value, callback) => {
      if (value === "" || value === undefined) {
        callback(new Error("请选择乡镇街道"));
      }
      callback();
    };
    var validateLiaisonStation = (rule, value, callback) => {
      if (value === "" || value === undefined) {
        callback(new Error("请选择联络站"));
      }
      callback();
    };
    var validateContactName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请填写联系人名称"));
      }
      callback();
    };
    var validateContactPhoneNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请填写联系人电话"));
      }
      callback();
    };
    var inviteRangeDesc = (rule, value, callback) => {
      if (value.length == 0) {
        callback(new Error("请选择受邀代表"));
      }
      callback();
    };
    var validateMark = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请填写活动内容"));
      }
      callback();
    };
    return {
      dateRange: [],
      liaisonStationIdState: false,//联络站数组是否为空
      streetTownIdState: false,//街道数组是否为空

      liaisonStationIdState_add: false,//联络站数组是否为空
      streetTownIdState_add: false,//街道数组是否为空
      isDb: 0,
      downloadLoading: false,
      SessionList: [
        { name: "第十四届", id: '1' },
        { name: "第十五届", id: '2' },
        { name: "第十六届", id: '3' },
      ],

      addDialogVisible: false,
      auditDialogVisible: false,
      selectMemberDialogVisible: false,
      statusList: [
        { id: "", name: "全部" },
        { id: 0, name: "待审核" },
        { id: 1, name: "已审核" },
      ],
      defaultSelect: [],
      //list-----
      elementLoadingText: "正在加载...",
      list: [],
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        status: undefined,
        session: undefined,
        year: null,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        userName: null,
        liaisonStationId: undefined
      },
      showDisabled: false,
      advanced: false,
      title: "新增年度安排",
      listLoading: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 10,
      background: true,
      //list-----
      selectRows: [],
      formLabelWidth: "120px",
      form: {
        inviteRangeDesc: '',
        attendMembers: [],
        session: '3',
        serverTime: "",
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        contactPhoneNumber: "",
        contactName: "",
        year: "",
        countryMemberIds: [],
        liaisonStation: "",
      },
      addstreetTowns: [],
      addliaisonStations: [],
      //行政区域列表
      administrativeAreas: [],
      //街道数据
      streetTowns: [],
      //联络站数据
      liaisonStations: [],
      userTreeData: [],
      userTreeProps: {
        children: "children",
        label: "name",
      },
      checkMembers: [],
      desc: "",
      multipleSelection: [],
      treeIds: [],
      auditOpinion: "",
      currId: "",
      auditForm: {},
      rules: {
        session: [{ validator: validateSession, trigger: "blur" }],
        serverTime: [{ required: true, validator: validateServerTime, trigger: "change" },],
        administrativeAreaId: [{ required: true, validator: validateAdministrativeArea, trigger: "blur", },],
        streetTownId: [{ required: true, validator: validateStreetTown, trigger: "change" },],
        liaisonStationId: [{ required: true, validator: validateLiaisonStation, trigger: "change", },],
        contactName: [{ required: true, validator: validateContactName, trigger: "blur" },],
        contactPhoneNumber: [{ required: true, validator: validateContactPhoneNumber, trigger: "blur", },],
        countryMemberIds: [{ required: true, validator: inviteRangeDesc, trigger: "blur" },],
        customMark: [{ required: true, validator: validateMark, trigger: "blur", },
        ],
      },

      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,

      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 50,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        // {
        //   title: "届别",
        //   align: "center",
        //   width: 100,
        //   ellipsis: true,
        //   dataIndex: "sessionName",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        {
          title: "时间",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "serverTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "年度",
          align: "center",
          width: 80,
          ellipsis: true,
          dataIndex: "year",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        // {
        //   title: "状态",
        //   align: "center",
        //   width: 120,
        //   ellipsis: true,
        //   dataIndex: "statusDesc",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        {
          title: "行政区划",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "administrativeAreaName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "乡镇街道",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "streetTownName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站",
          align: "center",
          width: 340,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.liaisonStationName;
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.membersName;
          },
        },
        {
          title: "联系人名称",
          align: "center",
          width: 120,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.contactName || "/";
          },
        },
        {
          title: "联系人电话",
          align: "center",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.contactPhoneNumber || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 210,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.details(record);
                    },
                  },
                },
                "受邀代表"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handelEmodify(record.id);
                    },
                  },
                },
                "修改"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handedelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      fileList: [],
    };
  },
  watch: {
    checkMembers: {
      handler () {
        (this.desc = ""), this.showMemberDesc();
      },
    },
  },
  created () {
    // 判断是否是路由跳转过来
    // this.isDb = this.$route.query.isDb
    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));
    if (this.$route.query.type == 'add') {
      this.handleAdd()
    }
    this.fetchData();
    this.listAdministrativeAreas();


  },
  beforeDestroy () {
    conslog.log("销毁,........")
  },
  methods: {
    //提示选择
    liaisonStationIdFocus () {
      if (this.listQuery.streetTownId && this.listQuery.administrativeAreaId) {
        this.liaisonStationIdState = true;
      } else {
        this.$message.info("请选择行政区域和乡镇街道");
        this.liaisonStationIdState = false;
      }
    },
    //提示选择
    streetTownIdFocus () {
      if (this.listQuery.administrativeAreaId) {
        this.streetTownIdState = true;
      } else {
        this.$message.info("请选择行政区域");
        this.streetTownIdState = false;
      }
    },
    //提示选择
    liaisonStationIdFocus_add () {
      if (this.form.streetTownId && this.form.administrativeAreaId) {
        this.liaisonStationIdState_add = true;
      } else {
        this.$message.info("请选择行政区域和乡镇街道");
        this.liaisonStationIdState_add = false;
      }
    },
    //提示选择
    streetTownIdFocus_add () {
      if (this.form.administrativeAreaId) {
        this.streetTownIdState_add = true;
      } else {
        this.$message.info("请选择行政区域");
        this.streetTownIdState_add = false;
      }
    },
    // 删除
    async handedelete (recode) {
      await this.$baseConfirm("确认要删除吗?", null, () => {
        //
        instance_1({
          url: "/communitySchedule/delete",//接口
          method: "post",
          params: { id: recode.id },
        }).then((res) => {

          if (res.data.code == "0000") {
            this.$message.success("删除成功");
            this.fetchData()
          }
        });
      })
    },
    // 修改
    handeUpdate () {

      this.addDialogVisible = true;
      this.title = "修改年度安排";
      this.desc = ''
      this.initUserTree(this.checkMembers);
    },
    clickRow (record) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            // this.handelEmodify( record.id)
          },
        },
      };
    },
    // 新增社情民意
    addSocialconditions () {
      let list = this.selectRows;
      if (list == null || list.length == 0 || list.length > 1) {
        this.$message.warning("请选择一条数据");
        return;
      } else {
        this.currId = list[0].id;
      }
      this.$router.push({
        path: "/community/newlyAdded",
        query: {
          to_type: true,
          communityScheduleId: this.currId,
        },
      });
    },
    // 切换页数
    changePageSize (pageNum, pageSize) {
      this.listQuery.pageNum = pageNum;
      this.listQuery.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange (pageNum, pageSize) {
      this.listQuery.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    // 多选选择
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    /**
     * 预加载数据
     */

    listAdministrativeAreas () {
      // 获取行政区县
      getAdministrativeAreas().then((response) => {
        this.administrativeAreas = response.data;
      });
    },
    liststreetTowns (administrativeAreaId) {
      // 111
      if (!administrativeAreaId) {
        administrativeAreaId=""
      }
        getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
          this.form.streetTownId = undefined;
          this.form.liaisonStation = "";
        }
      );
    },
    // 获取联络站
    listLiaisonStations (streetTownId) {
      getStations({ streetTownId: streetTownId }).then((response) => {
        this.liaisonStations = response.data;
        this.form.liaisonStation = "";
      });
    },
    //列表
    fetchData () {
      this.listLoading = true;
      getList({ ...this.listQuery, isDb: this.isDb }).then((response) => {
        response.rows.forEach(item => {
          item.membersName = item.members.map((son) => son.name).toString();
        })
        this.list = response.rows;
        this.total = response.total;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    handleSizeChange (val) {
      this.listQuery.size = val;
      this.fetchData();
    },
    // handleCurrentChange(val) {
    //   this.listQuery.current = val;
    //   this.fetchData();
    // },
    handleStatusQuery () {
      this.fetchData();
    },
    handleAdd () {
      this.$refs.ShowinvitedTree.checkIdentyData = [];
      this.$refs.ShowinvitedTree.expandedKeys = [];
      this.$refs.ShowinvitedTree.defaultSelect = [];
      this.defaultSelect = []
      this.$refs.ShowinvitedTree.checked = [];
      this.initForm();
      this.addDialogVisible = true;
      this.title = "新增年度安排";
      this.$forceUpdate();
    },
    handleAdd2() {
      this.$router.push({
        path: "/representative/activeDetail",
      });
    },
    get_details (id) {
      getCommunityScheduleById({ id: id }).then((res) => {
        this.form = res.data;
        // 暂做回显
        let checkIdentyData = []
        let checked = []
        if (this.form.attendMembers.length > 0) {
          this.form.attendMembers.forEach(item => {
            if (item.postId) {
              checkIdentyData.push("U" + item.postId)
              item.id = "U" + item.postId
              item.fullName = item.userName;
              // item.userId = item.userId;
              checked.push(item)
            } else {
              checkIdentyData = []
              checked = []
            }
          })
        }
        this.$refs.ShowinvitedTree.checkIdentyData = checkIdentyData;
        this.$refs.ShowinvitedTree.checked = checked;
        this.$forceUpdate();
        this.handleServerTime();
        getstreetTowns({
          administrativeAreaId: this.form.administrativeAreaId,
        }).then((response) => {
          this.streetTowns = response.data;
        });
        getStations({ streetTownId: this.form.streetTownId }).then(
          (response) => {
            this.liaisonStations = response.data;
          }
        );
      });
    },
    handelEmodify (id) {
      this.get_details(id);
      this.addDialogVisible = true;
      this.title = "修改年度安排";
      this.desc = ''
    },
    confirm () {
      if (this.checkMembers.length != 0) {
        // let ids = [];
        // this.checkMembers.forEach((item) => {
        //   ids.push(item.id);
        // });
        // this.form.countryMemberIds = ids;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.title == "新增年度安排") {
            save(this.form).then((response) => {
              this.$message.success("保存成功");
              this.addDialogVisible = false;
              this.fetchData();
              this.$refs.form.resetFields();
            });
          } else {
            update(this.form).then((response) => {
              this.$message.success("保存成功");
              this.addDialogVisible = false;
              this.fetchData();
              this.$refs.form.resetFields();
            });
          }
        } else {
          this.$message.warning("请完善必填项信息");
          reject("参数检验错误");//报错
        }
      });

      //联络站
      // this.form.liaisonStationId = this.form.liaisonStation.id;
      //代表id
    },
    // 新增关闭
    close () {
      this.$refs.ShowinvitedTree.checkIdentyData = [];
      this.$refs.ShowinvitedTree.expandedKeys = [];
      this.$refs.ShowinvitedTree.defaultSelect = [];
      this.defaultSelect = []
      this.$refs.ShowinvitedTree.checked = [];
      this.$forceUpdate();
      this.addDialogVisible = false;
      this.initForm();
    },
    initForm () {
      this.form.session = "3";
      this.form.serverTime = "";
      this.form.administrativeAreaId = undefined;
      this.form.streetTownId = undefined;
      this.form.liaisonStationId = undefined;
      this.form.contactPhoneNumber = undefined;
      this.form.attendMembers = [];
      this.form.inviteRangeDesc = undefined;

      this.form.contactName = undefined;
      this.form.year = "";
      // this.form.countryMemberIds = [];
      this.form.currMember = [];
      // this.$refs.form.resetFields();
      // this.$refs.form.clearValidate();
    },
    // 选择行政区域
    handleAdministrativeAreaChange (val) {
      this.listQuery.streetTownId = undefined;
      this.liststreetTowns(val);
    },

    // 选择乡镇街道
    handlestreetTownChange (val) {
      this.listQuery.liaisonStationId = undefined;
      this.listLiaisonStations(val);
    },
    // 选择行政区域 添加的
    handleAdministrativeAreaChange_add (val) {
      this.form.streetTownId = undefined;
      this.liststreetTowns(val);
    },
    // 选择乡镇街道 添加的
    handlestreetTownChange_add (val) {
      this.form.liaisonStationId = undefined;
      this.listLiaisonStations(val);
    },
    clearData(){
      console.log('🤗🤗🤗, clearData =>')
    },
    handleListLiaisonStationChange (id,state) {
      console.log('🤗🤗🤗,  =>', id,state)
      for (let index = 0; index < this.liaisonStations.length; index++) {
        const element = this.liaisonStations[index];
        // console.log("🤗🤗🤗, element =>", element);
        if (element.id == id) {
          // console.log("🤗🤗🤗, element =>", element);
          this.form.contactPhoneNumber = element.contactPhoneNumber;
          this.form.contactName = element.contactName;
          this.form.liaisonStation = element;
          this.$forceUpdate();
          break;
        }
        // console.log("🤗🤗🤗,  this.form =>", this.form);
      }
    },
    confirminvitedTree (data) {
      if (data) {
        this.defaultSelect = data;
        this.form.attendMembers = [];
        this.form.inviteRangeDesc = "";
        this.form.attendMembers = this.recursionChild(data);
        this.form.attendMembers.forEach((item, index) => {
          if (index !== this.form.attendMembers.length - 1) {
            this.form.inviteRangeDesc =
              this.form.inviteRangeDesc + item.userName + "、";
          } else {
            this.form.inviteRangeDesc =
              this.form.inviteRangeDesc + item.userName;
          }
        });
        // if (this.isshowIn) {
        //   //  如果新增后再改变自动修改一下
        //   this.$nextTick(() => {
        //     this.SubmitCensorship(true);
        //   });
        // }
      }
    },
    recursionChild (arr) {
      var res = [];
      arr.map((item) => {
        if (item.children && Array.isArray(item.children)) {
          // res = res.concat(this.recursionChild(item.children));
        } else {
          res.push({
            userId: item.userId,
            userName: item.fullName,
            postId: item.posId || item.postId,
          });
        }
      });
      return res;
    },
    openattendMembers () {
      this.$refs.ShowinvitedTree.newOrgTreeDialogVisible = true;
      this.$refs.ShowinvitedTree.orgTreeDialogTitle = "选择受邀代表";
    },
    details (row) {
      this.$router.push({
        path: "/representative/memberList",
        query: {
          communityScheduleId: row.id,
        },
      });
    },
    confirmCheck (users) {
      let checkMembers = users.filter((item) => item.itemType == 2);
      // if (checkMembers.length == 0) {
      // return
      // }
      this.checkMembers = checkMembers;
    },
    handleAudit () {
      let list = this.selectRows;
      // let list=this.selectedRows[this.selectRows.length-1]
      if (list == null || list.length == 0 || list.length > 1) {
        this.$message.warning("请选择一条数据");
        return;
      } else {
        this.currId = list[0].id;
        this.auditDialogVisible = true;
      }
    },
    audit (status) {
      //审核
      // debugger;
      audit({
        id: this.currId,
        status: status,
        auditOpinion: this.auditOpinion,
      }).then((response) => {
        this.$message.success("审核成功");
        this.auditDialogVisible = false;
        this.auditForm = {};
        this.fetchData();
      });
    },
    initAudit () {
      this.auditDialogVisible = false;
      this.currId = "";
      this.auditOpinion = "";
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },
    getSelection () {
      return this.multipleSelection;
    },

    showMemberDesc () {
      this.treeIds = [];
      this.checkMembers.forEach((item) => {
        this.desc += item.name + "; ";
        this.treeIds.push(item.uniqueId);
      });
    },
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    checkboxT (row, rowIndex) {
      if (row.status == 0) {
        return true; //不禁用
      } else {
        return false; //禁用
      }
    },
    handleServerTime () {
      this.form.year = this.form.serverTime.substring(0, 4);
    },
    search () {
      this.listQuery.pageNum = 1
      this.pagination.current = 1
      this.fetchData()
    },
    //重置
    reset () {
      // 111
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        status: undefined,
        session: undefined,
        year: null,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        userName: null,
         liaisonStationId: undefined
      };
      this.dateRange = [];
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
    // 时间选择
    onTimeChange (val) {
      // console.log("🤗🤗🤗, val =>", val);
      this.listQuery.startTime = val[0];
      this.listQuery.endTime = val[1];
      // console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
    },
    // 导出
    downloaData () {

      if (this.selectedRowKeys.length == 0)
        return this.$message.error("请选择数据");
      this.downloadLoading = true;
      let proposalId = this.selectedRowKeys.map((son) => son).toString();
      ExportExcelData(proposalId).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区计划数据.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000)
      });
    },
    // 下载
    downloadExcel () {
      downloadCommunityPlanExcel(this.queryForm).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区计划导入模板.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    // 上传文件限制 直接return
    beforeUpload (file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },
    //导入计划表
    downImport ({ file, fileList }) {
      // console.log(file);
      var data = new FormData();
      data.append("file", file);
      addCommunityPlanExcel(data).then((res) => {
        // console.log(res, "res");
        if (res.data.code != "0000") {
          return this.$message.error(res.data.msg);
        }
        if (res.data.code == "0000") {
          this.$baseMessage("上传成功", "success");
          this.fetchData();
        }
        // if (res.data.data.hxrdjbList.length == "0") {
        //   return this.$message.error(res.data.data.msg);
        // }
        // this.addForm = res.data.data.hxrdjbList
        //
        // res.data.data.hxrdjbList.forEach((el) => {
        //   console.log(el, "el");
        //   // this.composition.forEach((i,index)=>{
        //   // //   if(i.zygcmc==el.zygcmc){
        //   // // this.addForm.zygcDm=i.zygcDm;
        //   // //   }
        //   // })

        //   this.$message.success("上传成功");
        //   this.fetchData();
        // });
      });
    },
  },
};
</script>
