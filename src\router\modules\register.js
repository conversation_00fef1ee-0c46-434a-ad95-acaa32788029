import Layout from "@/layouts";

// {
//   path: "staging",
//   name: "InformStaging",
//   component: () => import("@/views/meeting/testPage/index"),
//   meta: {
//     title: "待办任务",
//     permissions: ["I_XTGLY", "I_XXFBY"],
//   },
// },
// 履职登记管理
export default [
  {
    path: "/registrationMage",
    component: Layout,
    redirect: "noRedirect",
    name: "registrationMage",
    title: "履职登记管理",
    meta: {
      title: "首页",
    },
    children: [
      {
        path: "registrationMage",
        name: "registrationMage",
        component: () => import("@/views/registrationMage/honIndex"),
        meta: {
          title: "代表首页",
        },
      },
      // {
      //   path: "registrationMage",
      //   name: "registrationMage",
      //   component: () => import("@/views/registrationMage/index"),
      //   meta: {
      //     title: "代表首页",
      //   },
      //   hidden: true,
      // },

      {
        path: "registrationIndex2",
        name: "registrationIndex2",
        component: () => import("@/views/registrationIndex/workIndex.vue"),
        meta: {
          title: "工作人员首页",
        },
        hidden: true,
      },
      {
        path: "dutiesworkIndex",
        name: "dutiesworkIndex",
        component: () =>
          import("@/views/registrationIndex/dutiesworkIndex.vue"),
        meta: {
          title: "工作人员首页",
        },
      },
    ],
  },

  //登记管理
  {
    path: "/registerMange",
    component: Layout,
    redirect: "noRedirect",
    name: "registerMange",
    title: "履职登记管理",
    meta: {
      title: "登记管理",
      icon: "database",
    },
    alwaysShow: false,
    children: [
      {
        path: "register",
        name: "register",
        component: () => import("@/views/meeting/activityRegister/register"),
        meta: {
          title: "登记活动",
        },
      },
      {
        path: "backlog",
        name: "backlog",
        component: () => import("@/views/meeting/registerMange/backlog"),
        meta: {
          title: "待办事项",
        },
      },
      {
        path: "Handling",
        name: "Handling",
        component: () => import("@/views/meeting/registerMange/Handling"),
        meta: {
          title: "已办事项",
        },
      },
      {
        path: "HandlingOrg",
        name: "HandlingOrg",
        component: () => import("@/views/meeting/registerMange/HandlingOrg"),
        meta: {
          title: "已办事项（单位）",
        },
      },
      // {
      //   path: "applying",
      //   name: "applying",
      //   // component: () => import("@/views/meeting/registerMange/applying"),
      //   component: () => import("@/views/meeting/organizationData/NotStarted"),
      //   meta: {
      //     title: "活动报名",
      //   },
      // },
      {
        path: "tableIng",
        name: "tableIng",
        component: () => import("@/views/meeting/registerMange/tableIng"),
        meta: {
          title: "活动查询",
        },
      },
      {
        path: "statisticsData",
        name: "statisticsData",
        component: () => import("@/views/meeting/registerMange/statisticsData"),
        meta: {
          title: "统计报表",
        },
      },
      {
        path: "newStatisticsData",
        name: "newStatisticsData",
        hidden: true,
        component: () => import("@/views/meeting/registerMange/newStatisticsData"),
        meta: {
          title: "新的统计报表",
        },
      },
      {
        path: "myStatisticsTable",
        name: "myStatisticsTable",
        component: () =>
          import("@/views/meeting/registerMange/statistics/myStatisticsTable"),
        meta: {
          title: "代表个人履职情况登记表",
        },
        hidden: true,
      },
      {
        path: "organizationTable",
        name: "organizationTable",
        component: () =>
          import("@/views/meeting/registerMange/statistics/organizationTable"),
        meta: {
          title: "各单位组织代表活动综合统计表",
        },
        hidden: true,
      },

      {
        path: "/statement/enteringTable",
        name: "enteringTable",
        component: () =>
          import("@/views/meeting/registerMange/statistics/enteringTable"),
        meta: {
          title: "各单位录入代表活动综合统计表",
        },
        hidden: true,
      },

      {
        path: "/statement/userTable",
        name: "userTable",
        component: () =>
          import("@/views/meeting/registerMange/statistics/userTable"),
        meta: {
          title: "个人履职详情统计表",
        },
        hidden: true,
      },
      {
        path: "/statement/newuserTable",
        name: "newuserTable",
        component: () =>
          import("@/views/meeting/registerMange/statistics/newuserTable"),
        meta: {
          title: "个人履职详情统计表",
        },
        hidden: true,
      },
      {
        path: "myStatsTable",
        name: "myStatsTable",
        component: () =>
          import("@/views/meeting/registerMange/statistics/myStatsTable"),
        meta: {
          title: "代表个人履职情况统计表",
        },
        hidden: true,
      },
      {
        path: "/statistics/handbookTable",
        name: "handbookTable",
        component: () =>
          import("@/views/meeting/registerMange/statistics/handbookTable"),
        meta: {
          title: "常委会组成成员履职登记手册统计表",
        },
        hidden: true,
      },
      {
        path: "/statement/mySdetailTable",
        name: "/statement/mySdetailTable",
        component: () =>
          import("@/views/meeting/registerMange/statistics/mySdetailTable"),
        meta: {
          title: "代表个人履职情况表统计表(明细)",
        },
        hidden: true,
      },
      {
        path: "mySnationwideTable",
        name: "mySnationwideTable",
        component: () =>
          import("@/views/meeting/registerMange/statistics/mySnationwideTable"),
        meta: {
          title: "全国,省代表履职活动查询",
        },
        hidden: true,
      },
    ],
  },

  //活动审核
  // {
  //   path: "/HuoDongcheck",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "huoDong",
  //   title: "履职登记管理",
  //   meta: {
  //     title: "活动审核",
  //     icon: "database",
  //     permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
  //   },
  //   alwaysShow: false,
  //   children: [
  //     {
  //       path: "activityAuditList",
  //       name: "activityAuditList",
  //       component: () =>
  //         import("@/views/meeting/activityAudit/activityAuditList"),
  //       meta: {
  //         title: "同步活动审核",
  //       },
  //     },
  //     {
  //       path: "addActivityAuditList",
  //       name: "addActivityAuditList",
  //       component: () =>
  //         import("@/views/meeting/activityAudit/addActivityAuditList"),
  //       meta: {
  //         title: "录入活动审核",
  //       },
  //     },
  //   ],
  // },

  //履职评分管理
  {
    path: "/grade",
    component: Layout,
    name: "through",
    title: "履职登记管理",
    meta: {
      title: "履职评分管理",
    },
    children: [
      // {
      //   path: "score",
      //   name: "score",
      //   component: () => import("@/views/meeting/score/scoreList"),
      //   meta: {
      //     title: "评分管理",
      //   },
      // },

      {
        path: "leaveList",
        name: "leaveList",
        component: () => import("@/views/meeting/score/leaveList"),
        meta: {
          title: "履职活动请假名单",
        },
      },

      // {
      //   path: "leaveRank",
      //   name: "leaveRank",
      //   component: () => import("@/views/meeting/score/leaveRankList"),
      //   meta: {
      //     title: "履职活动排名",
      //   },
      // },
      {
        path: "userGrade",
        name: "userGrade",
        // hidden: true,
        component: () => import("@/views/grade/userGrade"),
        meta: {
          title: "个人履职分数",
        },
      },
      {
        path: "appraise",
        name: "appraise",
        component: () => import("@/views/meeting/appraise/index"),
        meta: {
          title: "代表履职评分",
        },
      },
      {
        path: "subItem",
        name: "subItem",
        component: () => import("@/views/meeting/appraise/subItem"),
        meta: {
          title: "代表履职评分明细表",
        },
        hidden: true,
      },
      {
        path: "appraiseManage",
        name: "appraiseManage",
        component: () => import("@/views/meeting/appraise/appraiseManage"),
        meta: {
          title: "代表履职积分规则管理",
        },
      },
      {
        path: "appraiseSmallManage",
        name: "appraiseSmallManage",
        component: () => import("@/views/meeting/appraise/appraiseSmallManage"),
        hidden: true,
        meta: {
          title: "代表履职积分细项规则管理",
        },
      },
      {
        path: "updateSmallItem",
        name: "updateSmallItem",
        component: () => import("@/views/meeting/appraise/updateSmallItem"),
        meta: {
          title: "编辑积分项",
        },
        hidden: true,
      },
      {
        path: "addSmallItem",
        name: "addSmallItem",
        component: () => import("@/views/meeting/appraise/addSmallItem"),
        meta: {
          title: "新增积分项",
        },
        hidden: true,
      },
      {
        path: "customAppraise",
        name: "customAppraise",
        component: () => import("@/views/meeting/appraise/customAppraise/index"),
        meta: {
          title: "手动上报积分项",
        },
      },
      {
        path: "addCustomAppraise",
        name: "addCustomAppraise",
        component: () => import("@/views/meeting/appraise/customAppraise/add"),
        meta: {
          title: "新增上报积分项",
        },
        hidden: true,
      },
      {
        path: "editCustomAppraise",
        name: "editCustomAppraise",
        component: () => import("@/views/meeting/appraise/customAppraise/edit"),
        meta: {
          title: "编辑上报积分项",
        },
        hidden: true,
      },
      {
        path: "customAppraiseItem",
        name: "customAppraiseItem",
        component: () => import("@/views/meeting/appraise/customAppraise/itemList"),
        meta: {
          title: "添加上报积分项用户",
        },
        hidden: true,
      },
      {
        path: "addCustomAppraiseItem",
        name: "addCustomAppraiseItem",
        component: () => import("@/views/meeting/appraise/customAppraise/addItem"),
        meta: {
          title: "新增积分代表",
        },
        hidden: true,
      },
      {
        path: "appraiseRangeSetting",
        name: "appraiseRangeSetting",
        component: () => import("@/views/meeting/appraise/rangeSetting/index"),
        meta: {
          title: "积分采集时段管理",
        },
      },
      {
        path: "/userGradetal",
        name: "userGradetal",
        component: () => import("@/views/grade/userGradetal"),
        meta: {
          title: "个人履职详情",
        },
        hidden: true,
      },
      {
        path: "/commonDetail",
        name: "commonDetail",
        component: () => import("@/views/grade/commonDetail.vue"),
        meta: {
          title: "议案建议详情",
        },
        hidden: true,
      },
      {
        path: "/activitiesDetails",
        name: "activitiesDetails",
        component: () => import("@/views/grade/activitiesDetails.vue"),
        meta: {
          title: "活动详情",
        },
        hidden: true,
      },
      {
        path: "/attendanceDetal",
        name: "attendanceDetal",
        component: () => import("@/views/grade/attendanceDetal.vue"),
        meta: {
          title: "出勤详情",
        },
        hidden: true,
      },
    ],
  },



  //履职统计报表
  {
    path: "/statement",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "履职登记管理",
    meta: {
      title: "履职统计报表",
    },
    children: [
      // +10/20
      {
        path: "suggDetails",
        name: "suggDetails",
        component: () =>
          import("@/views/meeting/resumptionCount/suggDetails"),
        meta: {
          title: "议案建议报表详情",
        },
        hidden: true,
      },
      {
        path: "MessageDetails",
        name: "MessageDetails",
        component: () =>
          import("@/views/meeting/resumptionCount/MessageDetails"),
        meta: {
          title: "报表详情",
        },
        hidden: true,
      },
      {
        path: "attendUserList",
        name: "attendUserList",
        component: () =>
          import("@/views/meeting/resumptionCount/attendUserList"),
        meta: {
          title: "已参加人员列表",
        },
        hidden: true,
      },
      {
        path: "unAttendUserList",
        name: "unAttendUserList",
        component: () =>
          import("@/views/meeting/resumptionCount/unAttendUserList"),
        meta: {
          title: "未参加人员列表",
        },
        hidden: true,
      },
      {
        path: "attendDetailList",
        name: "attendDetailList",
        component: () =>
          import("@/views/meeting/resumptionCount/attendDetailList"),
        meta: {
          title: "参加活动人次列表",
        },
        hidden: true,
      },
      {
        path: "resumptionCountList",
        name: "resumptionCountList",
        component: () =>
          import("@/views/meeting/resumptionCount/resumptionCountList"),
        meta: {
          title: "各单位相关报表",
        },
      },
      {
        path: "npcMemberCountList",
        name: "npcMemberCountList",
        component: () =>
          import("@/views/meeting/resumptionCount/npcMemberCountList"),
        meta: {
          title: "市代表个人履职情况统计表",
        },
      },
      {
        path: "dbBIReportList",
        name: "dbBIReportList",
        component: () =>
          import("@/views/meeting/dbBIReport/dbBIReportList"),
        meta: {
          title: "报表管理",
        },
      },
      // {
      //   path: "newnpcMemberCountList",
      //   name: "newnpcMemberCountList",
      //   component: () =>
      //     import("@/views/meeting/resumptionCount/newnpcMemberCountList"),
      //   meta: {
      //     title: "市代表个人履职情况统计表",
      //   },
      // },
      // 10/24+
      {
        path: "NoDBnpcMemberCountListRanking",
        name: "NoDBnpcMemberCountListRanking",
        component: () =>
          import("@/views/meeting/resumptionCount/NoDBnpcMemberCountListRanking"),
        meta: {
          title: "非机关代表履职排名",
        },
        hidden: true,
      },
      {
        path: "DBnpcMemberCountListRanking",
        name: "DBnpcMemberCountListRanking",
        component: () =>
          import("@/views/meeting/resumptionCount/DBnpcMemberCountListRanking"),
        meta: {
          title: "机关代表履职排名",
        },
        hidden: true,
      },
      {
        path: "newNoDBnpcMemberCountListRanking",
        name: "newNoDBnpcMemberCountListRanking",
        component: () =>
          import("@/views/meeting/resumptionCount/newNoDBnpcMemberCountListRanking"),
        meta: {
          title: "非机关代表履职排名",
        },
        hidden: true,
      },
      {
        path: "newDBnpcMemberCountListRanking",
        name: "newDBnpcMemberCountListRanking",
        component: () =>
          import("@/views/meeting/resumptionCount/newDBnpcMemberCountListRanking"),
        meta: {
          title: "机关代表履职排名",
        },
        hidden: true,
      },
      // 10/24+
      {
        path: "provinceList",
        name: "provinceList",
        component: () => import("@/views/meeting/resumptionCount/provinceList"),
        meta: {
          title: "全国、省代表履职活动",
        },
      },
      {
        path: "wyhList",
        name: "wyhList",
        hidden: true,
        component: () => import("@/views/meeting/resumptionCount/wyhList"),
        meta: {
          title: "委员会履职活动统计表",
        },
      },
      {
        path: "dbtList",
        name: "dbtList",
        hidden: true,
        component: () => import("@/views/meeting/resumptionCount/dbtList"),
        meta: {
          title: "代表团履职活动统计表",
        },
      },
      {
        path: "newdbtList",
        name: "newdbtList",
        hidden: true,
        component: () => import("@/views/meeting/resumptionCount/newdbtList"),
        meta: {
          title: "代表团履职活动统计表",
        },
      },
    ],
  },

  //代表履职电子档案
  {
    path: "/electronicArchives",
    component: Layout,
    name: "through",
    title: "履职登记管理",
    meta: {
      title: "代表履职电子档案",
    },
    children: [
      {
        path: "index",
        name: "index",
        component: () => import("@/views/electronicArchives/index"),
        meta: {
          title: "履职电子档案",
        },
      },
      {
        path: "details",
        name: "details",
        component: () => import("@/views/electronicArchives/details"),
        meta: {
          title: "履职电子档案详情",
        },
        hidden: true,
      },
      {
        path: "achievement",
        name: "achievement",
        component: () => import("@/views/electronicArchives/achievement/index"),
        meta: {
          title: "意见建议被《人大代表情况反映》情况",
        },
      },
      {
        path: "achievementItem",
        name: "achievementItem",
        component: () => import("@/views/electronicArchives/achievement/itemList"),
        meta: {
          title: "新增意见建议被《人大代表情况反映》情况代表",
        },
        hidden: true,
      },
      {
        path: "addAchievement",
        name: "addAchievement",
        component: () => import("@/views/electronicArchives/achievement/add"),
        meta: {
          title: "新增意见建议被《人大代表情况反映》情况",
        },
        hidden: true,
      },
      {
        path: "editAchievement",
        name: "editAchievement",
        component: () => import("@/views/electronicArchives/achievement/edit"),
        meta: {
          title: "编辑意见建议被《人大代表情况反映》情况",
        },
        hidden: true,
      },
      {
        path: "dutyActivityAward",
        name: "dutyActivityAward",
        component: () => import("@/views/electronicArchives/dutyActivityAward/index"),
        meta: {
          title: "获奖情况",
        },
      },
      {
        path: "addDutyActivityAward",
        name: "addDutyActivityAward",
        component: () => import("@/views/electronicArchives/dutyActivityAward/add"),
        meta: {
          title: "新增获奖情况",
        },
        hidden: true,
      },
      {
        path: "editDutyActivityAward",
        name: "editDutyActivityAward",
        component: () => import("@/views/electronicArchives/dutyActivityAward/edit"),
        meta: {
          title: "编辑获奖情况",
        },
        hidden: true,
      },
      {
        path: "dutyActivityAwardItem",
        name: "dutyActivityAwardItem",
        component: () => import("@/views/electronicArchives/dutyActivityAward/itemList"),
        meta: {
          title: "新增获奖情况代表",
        },
        hidden: true,
      },
      {
        path: "resumptionWeekUserRecord",
        name: "resumptionWeekUserRecord",
        component: () => import("@/views/electronicArchives/resumptionWeekUserRecord/index"),
        meta: {
          title: "履职事迹被《人大代表履职周报》刊登次数",
        },
      },
      {
        path: "addResumptionWeekUserRecord",
        name: "addResumptionWeekUserRecord",
        component: () => import("@/views/electronicArchives/resumptionWeekUserRecord/add"),
        meta: {
          title: "新增履职事迹被《人大代表履职周报》刊登次数",
        },
        hidden: true,
      },
      {
        path: "editResumptionWeekUserRecord",
        name: "editResumptionWeekUserRecord",
        component: () => import("@/views/electronicArchives/resumptionWeekUserRecord/edit"),
        meta: {
          title: "编辑履职事迹被《人大代表履职周报》刊登次数",
        },
        hidden: true,
      },
      // TODO yu
      {
        path: "rankList",
        name: "rankList",
        component: () => import("@/views/electronicArchives/rankList"),
        meta: {
          title: "代表履职记录排名情况",
        },
      },


      {
        path: "viewResumptionWeekItem",
        name: "viewResumptionWeekItem",
        component: () => import("@/views/electronicArchives/resumptionWeekUserRecord/itemList"),
        meta: {
          title: "查看履职事迹被《人大代表履职周报》刊登次数代表列表",
        },
        hidden: true,
      },
      {
        path: "addResumptionWeekItem",
        name: "addResumptionWeekItem",
        component: () => import("@/views/electronicArchives/resumptionWeekUserRecord/addItem"),
        meta: {
          title: "新增履职事迹被《人大代表履职周报》刊登次数代表",
        },
        hidden: true,
      },
      // {
      //   path: "allRank",
      //   name: "allRank",
      //   component: () => import("@/views/electronicArchives/rankList"),
      //   meta: {
      //     title: "代表履职电子文档记录排名",
      //   },
      // },
    ],
  },

  {
    path: "/userProfile",
    component: Layout,
    name: "UserProfile",
    title: "履职登记管理",
    meta: {
      title: "代表画像",
    },
    children: [
      {
        path: "list",
        name: "UserProfileList",
        component: () => import("@/views/userProfile/list"),
        meta: {
          title: "代表列表",
        }
      },
      {
        path: "index",
        name: "UserProfileIndex",
        component: () => import("@/views/userProfile/index"),
        meta: {
          title: "代表画像详情",
        },
        hidden: true,
      },
    ]
  },

  //代表履职评分
  // {
  //   path: "/score",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "representative",
  //   title: "履职登记管理",
  //   meta: {
  //     title: "代表履职评分",
  //   },
  //   children: [
  //     {
  //       path: "score",
  //       name: "score",
  //       component: () => import("@/views/meeting/score/scoreList"),
  //       meta: {
  //         title: "评分管理",
  //       },
  //     },
  //     {
  //       path: "region",
  //       name: "region",
  //       component: () => import("@/views/meeting/score/region"),
  //       meta: {
  //         title: "各区评价得分",
  //       },
  //     },
  //     {
  //       path: "rankingList",
  //       name: "rankingList",
  //       component: () => import("@/views/meeting/score/rankingList"),
  //       meta: {
  //         title: "评分排行榜",
  //       },
  //     },
  //     {
  //       path: "leaveList",
  //       name: "leaveList",
  //       component: () => import("@/views/meeting/score/leaveList"),
  //       meta: {
  //         title: "履职活动请假名单",
  //       },
  //     },
  //     {
  //       path: "leaveRank",
  //       name: "leaveRank",
  //       component: () => import("@/views/meeting/score/leaveRankList"),
  //       meta: {
  //         title: "履职活动请假排名",
  //       },
  //       hidden: true,
  //     },
  //     {
  //       path: "dutyRankList",
  //       name: "dutyRankList",
  //       component: () => import("@/views/meeting/score/dutyRankList"),
  //       meta: {
  //         title: "履职活动排名",
  //       },
  //     },
  //   ],
  // },

  //系统管理
  {
    path: "/systemIndexdeng",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "履职登记管理",
    meta: {
      title: "系统管理",
      permissions: ["ADMIN", "I_XTGLY"],
    },
    children: [
      {
        path: "meetingEdit",
        name: "MeetingEdit",
        component: () => import("@/views/meeting/edit"),
        meta: {
          title: "数据权限管理",
          permissions: ["I_XTGLY"],
        },
      },
      {
        path: "session",
        name: "session",
        component: () => import("@/views/election/systemIndex/electionMage"),
        // component: () => import("@/views/meeting/session/index"),
        meta: {
          title: "届次管理",
        },
      },
      {
        path: "groupManagement",
        name: "groupManagement",
        component: () => import("@/views/meeting/registerMange/group/index"),
        meta: {
          title: "分组管理",
        },
      },
      {
        path: "gradingManagement",
        name: "gradingManagement",
        component: () =>
          import("@/views/election/systemIndex/gradingManagement"),
        meta: {
          title: "分级管理",
        },
      },

      {
        path: "menuManage",
        name: "MenuManage",
        component: () => import("@/views/menu/index"),
        meta: {
          title: "菜单管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "roleManage",
        name: "RoleManage",
        component: () => import("@/views/role/index"),
        meta: {
          title: "角色管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "userManage",
        name: "UserManage",
        component: () => import("@/views/user/index"),
        meta: {
          title: "用户管理",
          permissions: ["ADMIN", "I_XTGLY"],
        },
      },
      {
        path: "editRole",
        name: "EditRole",
        component: () => import("@/views/role/edit"),
        meta: {
          title: "编辑角色",
          permissions: ["ADMIN", "I_XTGLY"],
        },
        hidden: true,
      },
      {
        path: "parameterManagement",
        name: "parameterManagement",
        component: () => import("@/views/meeting/parameterManagement/index"),
        meta: {
          title: "参数管理",
        },
      },

    ],
  },


  // {
  //   path: "/HuoDongin",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "huoDong",
  //   title: "履职登记管理",
  //   meta: {
  //     title: "活动登记",
  //     icon: "database",
  //   },
  //   alwaysShow: false,
  //   children: [
  //     {
  //       path: "unitAddList",
  //       name: "unitAddList",
  //       component: () => import("@/views/meeting/activityRegister/unitAddList"),
  //       meta: {
  //         title: "组织单位录入",
  //       },
  //     },
  //     {
  //       path: "userAddList",
  //       name: "userAddList",
  //       component: () => import("@/views/meeting/activityRegister/userAddList"),
  //       meta: {
  //         title: "代表补录",
  //       },
  //     },
  //   ],
  // },
];
