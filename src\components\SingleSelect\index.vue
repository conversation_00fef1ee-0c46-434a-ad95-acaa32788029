<template>
  <div>
    <a-col :span="spanNum" :class="{ 'overLength': title.length > 5 && spanNum <= 8 }">
      <a-form-model-item :label="title" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-select v-model="selectInfo"
                  :mode="multiple ? 'multiple' : 'null'"
                  :placeholder="'请选择'+ title"
                  allow-clear
                  @change="search">
          <a-select-option v-for="item in selectList"
                           :key="item[showValue]"
                           :label="item[showValue]"
                           :value="item[showValue]">{{item[showName]}}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
    </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: () => "",
      required: true,
    },
    showName: {
      type: String,
      default: () => 'name',
    },
    showValue: {
      type: String,
      default: () => 'id',
    },
    selectList: {
      type: Array,
      default: () =>[],
      required: true,
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    multiple: {
      type: Boolean,
      default: () => false,
    },
    value: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      selectInfo: this.value,
    };
  },
  created() {
  },
  watch: {   
    value: {  
      handler(val) {
        this.selectInfo = val == '' ? undefined : val;
      },
      deep: true,
      immediate: true,
    },  
    title: {
      handler(val) {
        this.titleInfo = val
      },
      deep: true,
      immediate: true,
    }
  }, 
  methods: {
    search(val) {
      // this.$emit('getSelect', this.selectInfo)
      this.$emit('update:value', val)
      this.$emit('change', val)
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
