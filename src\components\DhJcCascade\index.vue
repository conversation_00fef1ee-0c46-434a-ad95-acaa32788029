<template>
  <div>
    <FormSelect
      :value="dhDm"
      label="层级"
      :prop="dhDmKey"
      :options="dbdhList"
      options-label="dhmc"
      options-value="dhDm"
      :disabled="disabled"
      :allow-clear="allowClear"
      @change="onDbdhChange"
      @select="$emit('dbDmSelect', $event)"
    />
    <FormSelect
      :value="jcDm"
      :prop="jcDmKey"
      label="届 次"
      :options="jcDmList"
      options-label="levelName"
      options-value="jcDm"
      :cascade-label="!dhDm && '层级'"
      :disabled="disabled"
      :allow-clear="allowClear"
      @change="onJcDmChange"
      @select="$emit('jcDmSelect', $event)"
    />

    <FormSelect
      :isShow="isShowDbt"
      :value="dbt"
      :prop="dbtKey"
      label="所属代表团"
      :options="dbtList"
      options-label="orgName"
      options-value="orgId"
      :cascade-label="!jcDm && '届次'"
      :disabled="disabled"
      :allow-clear="allowClear"
      @change="onDbtChange"
      @select="$emit('dbtSelect', $event)"
    />
  </div>
</template>

<script>
import { getList, getMyDbdhList, getMyDbdhListForLayer } from "@/api/system/dbdh";
import { levelList } from "@/api/area";
import FormSelect from "@/components/FormSelect/index.vue";
import { findDbt } from "@/api/dmcs";

/**
 * 代表大会、届次联动选择器
 * <DhJcCascade v-model="queryForm" />
 */
export default {
  name: "DhJcCascade",
  components: { FormSelect },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    dhDmKey: {
      type: String,
      default: "dhDm",
    },
    jcDmKey: {
      type: String,
      default: "jcDm",
    },
    allPerm: {
      type: Boolean,
      default: false,
    },
    allowClear: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isDbt: {
      type: [Boolean, String],
      default: false,
    },
    isShowDbt: {
      type: Boolean,
      default: true,
    },
    /**
     * (String | 'first' | 'last' | undefined)[dhDm, jcDm, orgId]
     */
    defaultValue: {
      type: [Array],
      default: undefined,
    },
    // 新分级分权方案标识
    levelRoleMainIdentify: {
      type: String,
      default: null,
    },
    isDefaultLoad: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dbdhList: [],
      jcDmList: [],
      dbtList: [],
      isDefault: true,
      isDefaultValue: false,
    };
  },
  computed: {
    dhDm() {
      return this.value[this.dhDmKey];
    },
    jcDm() {
      return this.value[this.jcDmKey];
    },
    dbtKey() {
      if (typeof this.isDbt == "boolean") return "dbtId";
      return this.isDbt;
    },
    dbt() {
      return this.value[this.dbtKey];
    },
  },
  watch: {
    dhDm: {
      immediate: true,
      handler(val) {
        this.jcDmList = [];
        this.dbtList = [];
        if (val) {
          levelList({
            pageNum: 1,
            pageSize: 999,
            dhDm: val,
          }).then((res) => {
            this.jcDmList = res.rows;
            if (this.isDefault) {
              this.isDefault = false;
              if (this.jcDmList.length) {
                // this.onJcDmChange(this.jcDmList[0].jcDm);
              }
              this.$emit('load')
            }
            this.loadDefaultValue(1);
          });
        } else {
          this.loadDefaultValue(1);
        }
      },
    },
    jcDm: {
      immediate: true,
      handler() {
        this.dbtList = [];
        if (this.dhDm && this.jcDm) {
          findDbt({
            dhdm: this.dhDm,
            jcDm: this.jcDm,
          }).then((res) => {
            this.dbtList = res.data;
            this.loadDefaultValue(2);
          });
        } else {
          this.loadDefaultValue(2);
        }
      },
    },
  },
  mounted() {
    console.log('isShowDbt:', this.isShowDbt);
    this.isDefault = this.isDefaultLoad;
  },
  updated() {
    console.log('isShowDbt updated:', this.isShowDbt);
  },
  created() {
    this.load()
    // then(() => {
    //   if (!this.dhDm && this.dbdhList.map((it) => it.dhDm).includes('cfd751f6be2311e6a7af12e754eb9e57')) {
    //     this.isDefault = true;
    //     this.onDbdhChange('cfd751f6be2311e6a7af12e754eb9e57');
    //   } else {
    //     if(this.dbdhList && this.dbdhList.length > 0) {
    //       this.isDefault = true;
    //       this.dhDm = this.dbdhList[0].dhDm;
    //       this.onDbdhChange(this.dbdhList[0].dhDm);
    //     }
    //   }
    // });
  },
  methods: {
    async load() {
      if (this.allPerm) {
// <<<<<<< HEAD
//         const res = await getList({pageNum: 1, pageSize: 999})
//         this.dbdhList = res.rows;
//       } else {
//         const res = await getMyDbdhList({})
//         this.dbdhList = res.data;
// =======
        getList({ pageNum: 1, pageSize: 999 }).then((res) => {
          this.dbdhList = res.rows;
          this.loadDefaultValue(0);
          this.defaultDhDmAndJcDm()

        });
      } else {
        if(this.levelRoleMainIdentify) {
          getMyDbdhListForLayer({levelRoleMainIdentify : this.levelRoleMainIdentify}).then((res) => {
            this.dbdhList = res.data;
            this.loadDefaultValue(0);
            this.defaultDhDmAndJcDm()

          });
        } else {
          getMyDbdhList({}).then((res) => {
            this.dbdhList = res.data;
            this.loadDefaultValue(0);
            this.defaultDhDmAndJcDm()

          });
        }
      }
    },
    onChange(value) {
      Object.assign(this.value, value);
      this.$emit("update:value", value);
      this.$emit("change", value);
    },
    onDbdhChange(value) {
      this.jcDmList = [];
      this.dbtList = [];
      const res = {
        ...this.value,
        [this.dhDmKey]: value,
        [this.jcDmKey]: undefined,
      };
      if (this.isDbt) {
        res[this.dbtKey] = undefined;
      }
      this.onChange(res);
    },
    onJcDmChange(value) {
      this.dbtList = [];
      const res = {
        ...this.value,
        [this.jcDmKey]: value,
      };
      if (this.isDbt) {
        res[this.dbtKey] = undefined;
      }
      this.onChange(res);
    },
    onDbtChange(value) {
      this.onChange({
        ...this.value,
        [this.dbtKey]: value,
      });
    },
    loadDefaultValue(index) {
      const ops = {
        0: { options: this.dbdhList, valueKey: 'dhDm', onChange: this.onDbdhChange },
        1: { options: this.jcDmList, valueKey: 'jcDm', onChange: this.onJcDmChange },
        2: { options: this.dbtList, valueKey: 'orgId', onChange: this.onDbtChange },
      }
      const options = ops[index].options
      const valueKey = ops[index].valueKey
      const onChange = ops[index].onChange

      if (!this.defaultValue || !this.defaultValue.length || !this.defaultValue[index] || !options.length || (index != 0 && !this.isDefaultValue)) {
        if (index !=0) {
          this.$emit('default-value')
        }
        this.isDefaultValue = false
        return
      }

      if (this.defaultValue[index] == 'first') {
        onChange(options[index][valueKey]);
        this.isDefaultValue = true
      } else if (this.defaultValue[index] == 'last') {
        onChange(options[this.dbdhList.length - 1][valueKey]);
        this.isDefaultValue = true
      } else if (options.map((it) => it[valueKey]).includes(this.defaultValue[index])) {
        onChange(this.defaultValue[0]);
        this.isDefaultValue = true
      } else {
        this.isDefaultValue = false
        if (index !=0) {
          this.$emit('default-value')
        }
      }
    },
    defaultDhDmAndJcDm() {
      if(! this.isDefault) {
        return
      }
      if (this.dbdhList && this.dbdhList.length > 0) {
        if (!this.dhDm && this.dbdhList.map((it) => it.dhDm).includes('cfd751f6be2311e6a7af12e754eb9e57')) {
          // this.isDefault = true;
          // this.onDbdhChange('cfd751f6be2311e6a7af12e754eb9e57');
        } else {
          // this.isDefault = true;
          this.onDbdhChange(this.dbdhList[0].dhDm);
        }
      }
      if (this.jcDmList && this.jcDmList.length > 0) {
        this.onJcDmChange(this.jcDmList[0].jcDm);
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
