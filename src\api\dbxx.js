import request from "@/utils/requestTemp";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/committeeMember/list",
    method: "get",
    params: data,
  });
}

export function getTree() {
  return request({
    url: "/treeGroup/oTree",
    method: "get",
  });
}

export function getDuTree(data) {
  return request({
    url: "/treeGroup/duTree",
    method: "post",
    data: data,
  });
}

export function getInfo(data) {
  return request({
    url: "/personalDetails/getInfo",
    method: "get",
    params: data,
  });
}
// +获取旧数据
export function getOldInfo(data) {
  return request({
    url: "/personalDetails/getOldInfo",
    method: "get",
    params: data,
  });
}
export function update(data) {
  return request({
    url: "/personalDetails/update",
    method: "post",
    data: data,
  });
}

export function getBaseInfo(data) {
  return request({
    url: "/personalDetails/getBaseInfo",
    method: "get",
    params: data,
  });
}

export function insert(data) {
  return request({
    url: "/personalDetails/insert",
    method: "post",
    data: data,
  });
}

export function insertWithWorkFlow(data) {
  return request({
    url: "/addRepresentative/create",
    method: "post",
    data: data,
  });
}

export function checkUsernameAndMobile(params) {
  return request({
    url: "/addRepresentative/checkUsernameAndMobile",
    method: "post",
    params,
  });
}

// 查询新增代表记录
export function getAddRepresentativeById(params) {
  return request({
    url: "/addRepresentative/find",
    method: "get",
    params,
  });
}

export function addRepresentativeComplete(data) {
  return request({
    url: "/addRepresentative/complete",
    method: "post",
    data,
  });
}
export function sendLogInfo(params) {
  return request({
    url: "/system/accessLog/sendLogInfo",
    method: "post",
    params,
  });
}

export function dbBaseInfoUpdateLogList(params) {
  return request({
    url: "/dbBaseInfoUpdateLog/list",
    method: "get",
    params,
  });
}

// 新的查询代表树
export function findDbOrgUserTree(params) {
  return request({
    url: "/dmcs/common/ours/findDbOrgUserTree",
    method: "get",
    params,
  });
}

// 新的查询代表树（按姓氏笔画排序）
export function findDbOrgUserTreeOrderByXsbh(params) {
  return request({
    url: "/dmcs/common/ours/findDbOrgUserTreeOrderByXsbh",
    method: "get",
    params,
  });
}
