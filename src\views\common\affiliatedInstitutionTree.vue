<template>
<!-- 归属栏目树 -->
  <a-modal
    :title="attributionTree"
    :visible.sync="attributionTreeVisible"
    width="50%"
    destroyOnClose
    @cancel="close"
  >
    <a-input
          style="width: 300px;"
          v-model="keyword"
          placeholder="输入关键字过滤"
          allow-clear
          @change="onChange"
        />
    <a-tree
      ref="orgTree"  
       style="    height: 800px;   overflow-y: auto;"
      :default-expanded-keys="expandedKeys"
      :replace-fields="replaceFields"
      :tree-data="orgTreeData" 
      @select="onCheck" 
    >
    <template slot="title" slot-scope="{ name }">
        <span
          v-if="keyword && name.indexOf(keyword) > -1"
          style="color: #f50;"
        >
          {{ name }}
        </span>
        <span v-else>{{ name }}</span>
      </template>
    </a-tree>

    <span slot="footer" class="dialog-footer">
      <a-button style="padding-left: 10px;" type="primary" @click="confirm"
        >确 定</a-button> 
      <a-button @click="close">取 消</a-button>
    </span>
  </a-modal>
</template>

<script>
 
import { instance_1 } from "@/api/axiosRq";
import { log } from "@antv/g2plot/lib/utils";
export default {
  data() {
    return {

        keyword:"",
      name: "",
      expandedKeys: [], 
      attributionTreeVisible: false,
      orgTreeData: [], 
      replaceFields: {
        title: "name",
        key: "id",
        children: "child",
      }, 
     
    };
  },
  props: {
    attributionTree: {
      type: String,
      default: "选择机构",
    },
  },
  watch: {
    attributionTreeVisible(newVal) { 
      if (newVal) {
        this.initOrgTree();
      } else {
        this.expandedKeys = [];  
      }
    },
  },
  methods: {
    // 关键字搜索
      onChange(){

      },
      // 关闭
    close() {
      this.attributionTreeVisible = false; 
      this.keyword=""
    },
     // 获取所在的父节点
    getParentKey(id, tree) {
      let parentKey;
      for (let index = 0; index < tree.length; index++) {
        const node = tree[index];
        if (node.children) {
          if (node.children.some((item) => item.id === id)) {
            parentKey = node.id;
          } else if (this.getParentKey(id, node.children)) {
            parentKey = this.getParentKey(id, node.children);
          }
        }
      }
      return parentKey;
    },
    //获取数据
    initOrgTree() {
     instance_1({
      //  url:"/content/statistics/ColumnSetup/orgTree",
        url: "content/statistics/schedule/orgTree",
        method: "get", 
      }).then((res) => { 
        if(res.data.code=="0000"){
             this.orgTreeData = res.data.data;
         this.attributionTreeVisible=true;
          this.orgTreeData.forEach(item => {
               delete item.root;
                // item.child.forEach((i,n)=>{
                //  delete i.root;
                // })
              });
        } 
      });
    },

    // 树状选择
    onCheck(item, data) { 
       this.attributionTreeVisible = false; 
       this.$emit("confirm", data); 
    }, 
    confirm() { 
      this.attributionTreeVisible = false; 
      // this.$emit("confirm", this.checked);
    
    },
  },
};
</script>
