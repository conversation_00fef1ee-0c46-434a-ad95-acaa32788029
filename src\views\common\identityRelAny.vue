<template>
  <a-modal
    :title="title"
    :visible.sync="dialogFormVisible"
    width="800px"
    @cancel="close"
  >
    <a-tabs default-active-key="1" @change="changtab">
      <!-- <a-tab-pane key="0" tab="常用人">
        <div class="table_container">
          <div class="group_item">
            <a-input-search placeholder="请输入" @search="onSearch" />
            <a-checkbox-group
              v-model="checkedUsualValue"
              @change="onUsualChange"
            >
              <div class="cjry">
                <a-row>
                  <a-col
                    v-for="item2 in usualTreeData"
                    :key="item2.id"
                    :span="24"
                  >
                    <a-checkbox :value="item2"> {{ item2.name }}</a-checkbox>
                  </a-col>
                </a-row>
              </div>
            </a-checkbox-group>
          </div>
          <div class="table_icon">
          </div>
          <div class="choose_list">
            <a-input-search placeholder="请输入" @search="onSearch" />
            <div class="list_table">
              <div
                v-for="(item, key) in usualCheckList"
                :key="key"
                class="list_all"
              >
                <div class="list_left">{{ item.name.substring(1) }}</div>
                <div class="list_right">
                  <div>{{ item.name }} {{ item.jobName }}</div>
                  <div>{{ item.office }} {{ item.address }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-tab-pane> -->
      <a-tab-pane key="1" tab="机构树">
        <a-modal
          title="导入结果"
          :visible="dialogVisible"
          ok-text="下一步"
          :width="700"
          class="import_style"
          @ok="userConfirm"
          @cancel="userClose"
        >
          <div v-if="isLoading" class="loading-indicator">
            <!-- 这里添加你的 loading 指示器，如一个旋转的图标 -->
            <a-icon type="loading" /> 导入中...
          </div>
          <template v-else>
            <a-tabs
              v-model="importDefaultKey"
              :default-active-key="importDefaultKey"
              @change="activeChangeTab"
            >
              <a-tab-pane key="1" tab="正常用户">
                <a-table
                  :columns="columns"
                  :data-source="normalUserList"
                  :pagination="false"
                  :scroll="{ y: 290 }"
                />
              </a-tab-pane>
              <a-tab-pane key="2" tab="重复用户">
                <a-table
                  :row-selection="{
                    selectedRowKeys: selectedRowKeys,
                    onChange: onSelectChange,
                  }"
                  :columns="columns"
                  :data-source="repeatUserList"
                  :pagination="false"
                  :scroll="{ y: 290 }"
                />
              </a-tab-pane>
              <a-tab-pane key="3" tab="不存在用户">
                <a-table
                  :columns="columns"
                  :data-source="absentUserList"
                  :pagination="false"
                  :scroll="{ y: 290 }"
                />
              </a-tab-pane>
            </a-tabs>
          </template>
        </a-modal>

        <a-modal
          title="实际导入结果"
          :visible="realityVisible"
          :width="700"
          @ok="userRealityConfirm"
          @cancel="userRealityClose"
        >
          <div class="all_Num">
            <span>总条数: {{ realityCheckList.length }}</span>
          </div>
          <a-table
            :columns="columns"
            :data-source="realityCheckList"
            :pagination="false"
            :scroll="{ y: 290 }"
          />
        </a-modal>

        <div class="table_container">
          <div class="table_left">
            <div style="margin-bottom: 10px">
              <div style="display: inline-block">
                <a-upload
                  name="file"
                  accept=".xls,.xlsx"
                  :multiple="true"
                  :file-list="fileList"
                  :before-upload="beforeUpload"
                  @change="importRepresentationList"
                >
                  <a-button type="primary">批量用户导入</a-button>
                </a-upload>
              </div>
              <a-button
                type="primary"
                style="margin-left: 15px"
                :loading="downloadExcelLoading"
                @click="downloadExcel"
                >下载导入模版</a-button
              >
            </div>
            <a-col span="18">
              <a-input-search
                v-model="filterText"
                placeholder="输入关键字进行过滤"
                @search="onChange"
              ></a-input-search>
            </a-col>
            <a-col span="18">
              <div class="sortOrder">
                <a-radio-group
                  v-model="sortOrder"
                  style="padding: 10px"
                  @change="changeSortOrder"
                >
                  <a-radio :value="1">默认排序</a-radio>
                  <a-radio :value="2">按姓氏排序</a-radio>
                </a-radio-group>
              </div>
            </a-col>
            <a-spin :indicator="indicator" :spinning="listLoading">
              <div id="app" style="min-height: 50px">
                <!-- v-model="userTreeDefaultKey" -->
                <a-tree
                  ref="userTree"
                  v-model="userTreeDefaultKey"
                  checkable
                  :expanded-keys="expandedKeys"
                  multiple
                  :selectable="false"
                  :replace-fields="replaceFields"
                  :tree-data="userTreeData"
                  @expand="onExpand"
                  @check="onCheck"
                  @select="onSelect"
                >
                  <template slot="title" slot-scope="{ name }">
                    <span
                      v-if="searchValue && name.indexOf(searchValue) > -1"
                      style="color: #f50"
                      >{{ name }}</span
                    >
                    <span v-else>{{ name }}</span>
                  </template>
                </a-tree>
              </div>
            </a-spin>
          </div>
          <div class="table_icon">
            <!-- <div><a-icon type="right-circle" style="color: #b6b7bb;" /></div>
            <div><a-icon type="left-circle"  style="color: #b6b7bb;" /></div>
            <div><a-icon type="double-right" style="color: #b6b7bb;" /></div>
            <div><a-icon type="double-left" style="color: #d92b3e;" /></div> -->
          </div>
          <div class="choose_list">
            <a-input-search placeholder="请输入" @search="onSearch" />
            <div class="list_table">
              <div v-for="(item, key) in checkList" :key="key" class="list_all">
                <div class="list_left">{{ item.name.substring(1) }}</div>
                <div class="list_right">
                  <div>{{ item.name }} {{ item.jobName }}</div>
                  <div>{{ item.office }} {{ item.address }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="用户组" v-if="showUserGroup">
        <div class="table_container">
          <div class="group_item">
            <a-input-search placeholder="请输入" @search="onSearch" />
            <a-checkbox-group
              v-model="checkedGroupValue"
              @change="onGroupChange"
            >
              <div v-for="(item, index) in plainOptions" :key="index">
                <div class="cjry">
                  <a-row>
                    <a-col
                      v-for="item2 in item.userGroupEntityList"
                      :key="item2.id"
                      :span="24"
                    >
                      <a-checkbox :value="item2">
                        {{ item2.groupName }}</a-checkbox
                      >
                    </a-col>
                  </a-row>
                </div>
              </div>
            </a-checkbox-group>
          </div>
          <div class="table_icon">
          </div>
          <div class="choose_list">
            <a-input-search placeholder="请输入" @search="onSearch" />
            <div class="list_table">
              <div
                v-for="(item, key) in groupCheckList"
                :key="key"
                class="list_all"
                style="color: #000"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>

    <div
      slot="footer"
      class="dialog-footer"
      style="position: relative; padding-right: 15px; text-align: right"
    >
      <a-button @click="close">关闭</a-button>
      <a-button
        type="primary"
        :loading="loading"
        style="margin-left: 10px"
        @click="confirm"
        >保存</a-button
      >
    </div>
  </a-modal>
</template>
<script>
import { getIdentityTypeList, findUserGroup } from "@/api/identity";
import {getTree as getUserTree, getTreeOrderByXsbh} from "@/api/user";
import checkPermission from "@/utils/permission";
import { instance_1 } from "@/api/axiosRq";
import { Dialog, Button, Tabs, TabPane } from "element-ui";
import {
  importRepresentation,
  downloadUserImportExcel,
} from "@/api/communityschedule";
export default {
  props: {
    rootOrgId: {
      type: String,
      default: "root",
    },
    // 是否显示参加人员 默认显示
    showParticipants: {
      type: Boolean,
      default: true,
    },
    showUserGroup: {
      type: Boolean,
      default: true,
    },
    components: {
      "el-button": Button,
      "el-dialog": Dialog,
      "el-tabs": Tabs,
      "el-tab-pane": TabPane,
    },
    selfPerm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      sortOrder: 1,
      max: 99,
      tabActive: "identity",
      tabName: "常用人",
      //选择类型：1 身份选择 0 树选择 2 发布单位选择；不显示树 3 日程关联人员不显示树
      type: "1",
      loading: false,
      // checkedShow: false,
      checkedShow: "1",
      title: "",
      filterText: "",
      dialogFormVisible: false,
      //身份选择参数
      identityTypeUserList: [],
      checked: [],
      checkIdentyData: [],
      allChildData: [],
      selectedKeys: [],
      //用户树参数
      userTreeData: [],
      treeCheckedKey: [],
      userTreeDefaultKey: [],
      replaceFields: {
        title: "name",
        key: "uniqueId",
        children: "children",
      },
      // 搜索值
      searchValue: "",
      // 展开的节点
      expandedKeys: [],
      backupsExpandedKeys: [],
      // 是否自动展开
      autoExpandParent: true,
      plainOptions: [], //人员数据
      checkedValue: [], //人员数据
      checkedGroupValue: [],
      defaultKey: "0", //人员数据
      listLoading: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      userIds: [],
      dialogVisible: false,
      activeTabName: "normalUsers",
      normalUserList: [],
      repeatUserList: [],
      absentUserList: [],
      checkedUserList: [],
      confirmUserList: [],
      userConfirmData: [],
      isLoading: false,
      timer: null,
      receiverTreeNodeList: [],
      downloadExcelLoading: false,
      columns: [
        {
          title: "序号",
          dataIndex: "id",
        },
        {
          title: "姓名",
          dataIndex: "userName",
        },
        {
          title: "岗位名称",
          dataIndex: "orgName",
        },
      ],
      selectedRowKeys: [],
      fileList: [],
      realityVisible: false,
      realityCheckList: [],
      importDefaultKey: "1",
      checkList: [],
      groupCheckList: [],
      expandedUsualKeys: [],
      searchUsualValue: "",
      autoExpandUsualParent: true,
      usualTreeData: [
        { name: "刘*强", id: "0", itemType: "2", uniqueId: "0" },
        { name: "吴*亮", id: "1", itemType: "2", uniqueId: "1" },
        { name: "吴*辉", id: "2", itemType: "2", uniqueId: "2" },
        { name: "黄*娟", id: "3", itemType: "2", uniqueId: "3" },
        { name: "张*思", id: "4", itemType: "2", uniqueId: "4" },
      ],
      selectedUsualKeys: [],
      onTreeShowUsualList: [],
      usualChecked: [], // 只有id
      usualCheckList: [], // 包含整个item
      checkedUsualValue: [],
    };
  },
  created() {
    this.initIdentityList();
    this.initUserTree(this.sortOrder);
    this.findUserGroupData();
  },
  methods: {
    // 改变排序
    changeSortOrder(val) {
      this.sortOrder = val.target.value;
      this.initUserTree(this.sortOrder);
    },
    // 用户组选择（单选框）
    // onCheckedChange(e) {
    //   // 清空树状数据
    //   if (this.userTreeDefaultKey || this.expandedKeys) {
    //     this.userTreeDefaultKey = [];
    //     this.expandedKeys = [];
    //   }
    //   var e1 = e.target;
    //   this.checkedValue = e1.value;
    // },
    // 获取用户组数据
    findUserGroupData() {
      instance_1({
        url: "/sysManager/userGroup/findUserGroup",
        method: "get",
      }).then((res) => {
        this.plainOptions = res.data.data;
      });
    },
    // 常用人搜索
    onUsualSerarch() {},
    onGroupChange(e) {
      this.checkedValue = e.map((item) => {
        return item.id;
      });
      this.groupCheckList = e.map((item) => {
        return item.groupName;
      });
    },
    onUsualChange(e) {
      this.usualCheckList = e.map((item) => {
        return {
          ...item,
          jobName: item.postName,
          office: item.orgName,
          address: item.deptName,
        };
      });
    },
    // 树状搜索
    onChange(e) {
      console.log("点击了搜索框");
      this.listLoading = true;
      const value = e;
      this.searchValue = value;
      if (value == "") {
        this.listLoading = false;
        this.$message.info("请输入关键字");
      } else {
        this.searchValue = value;
        this.expandedKeys = [];
        this.backupsExpandedKeys = [];
        const candidateKeysList = this.getkeyList(value, this.userTreeData, []);
        if (candidateKeysList.length == 0) {
          this.$message.destroy();
          this.listLoading = false;
          this.$message.info("没有相关数据");
          return;
        }
        candidateKeysList.forEach((item) => {
          const key = this.getParentKey(item, this.userTreeData);
          if (key && !this.backupsExpandedKeys.some((item) => item === key))
            this.backupsExpandedKeys.push(key);
        });
        const { length } = this.backupsExpandedKeys;
        for (let i = 0; i < length; i++) {
          this.getAllParentKey(this.backupsExpandedKeys[i], this.userTreeData);
        }
        this.expandedKeys = this.backupsExpandedKeys.slice();
        this.listLoading = false;
      }
    },
    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if(node.name === null || node.name == undefined){
          continue;
        }
        if (node.name.indexOf(value) > -1) {
          keyList.push(node.uniqueId);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey;
      let temp;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          temp = this.getParentKey(key, node.children);
          if (node.children.some((item) => item.uniqueId === key)) {
            parentKey = node.uniqueId;
          } else if (temp) {
            parentKey = temp;
          }
        }
      }
      return parentKey;
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey;
      if (key) {
        parentKey = this.getParentKey(key, tree);
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey);
          }
          this.getAllParentKey(parentKey, tree);
        }
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    onExpandUsual(expandedKeys) {
      this.expandedUsualKeys = expandedKeys;
      this.autoExpandUsualParent = false;
    },
    submitUpload() {},
    changtab(tab) {
      console.log(tab, "选中了哪个");
      this.defaultKey = tab;
      this.checkedValue = [];
      this.checkedShow = tab;
    },
    activeChangeTab(tab) {
      this.importDefaultKey = tab;
      console.log(this.importDefaultKey, "--------his.importDefaultKey");
    },
    tabClick(tab, event) {
      this.tabActive = tab.name;

      if (this.type == "0" || this.type == "1") {
        if (tab.name == "tree") {
          this.type = "0";
        } else {
          this.type = "1";
        }
      }
    },
    initUserTree(sortOrder) {
      let data = {
        tenantId: this.$tenantId,
        rootOrgId: this.rootOrgId,
        sortOrder: sortOrder,
        userIds: this.userIds,
      };
      this.listLoading = true;
      if (this.selfPerm) {
        let params = { tenantId: data.tenantId, rootOrgId: data.rootOrgId };
        return instance_1({
          url: `/display/user/findOUTreeByRole`,
          // url: `/dmcs/common/ours/findOUTree`,
          method: "post",
          params,
          data: {
            userIds: data.userIds || [],
            sortOrder: data.sortOrder || "",
          },
        }).then((response) => {
          const res = response.data;
          this.allChildData = [
            ...this.allChildData,
            ...(res.data.children || []),
          ];
          this.userTreeData = res.data.children;
          this.listLoading = false;
        });
      } else {
        if(sortOrder === 1){
          getUserTree(data).then((res) => {
            this.allChildData = [
              ...this.allChildData,
              ...(res.data.children || []),
            ];
            this.userTreeData = res.data.children;
            this.listLoading = false;
          });
        } else if (sortOrder === 2) {
          getTreeOrderByXsbh(data).then((res) => {
            this.allChildData = [
              ...this.allChildData,
              ...(res.data.children || []),
            ];
            this.userTreeData = res.data.children;
            this.listLoading = false;
          });
        }
      }
    },
    initIdentityList() {
      getIdentityTypeList().then((res) => {
        this.identityTypeUserList = res.data;
      });
    },

    handleHide() {
      this.dialogFormVisible = false;
    },
    // 回显
    handleShow(type, checkedData, checkObjData) {
      console.log(checkObjData, "机构树回显收到数据");

      this.checkList = [];
      // let that = this;

      if (type == "0") {
        this.title = "选择受邀范围";
        this.tabActive = "tree";
        this.tabName = "参加人员";
      }
      if (type == "1") {
        this.title = "选择发送范围";
        this.tabActive = "identity";
        this.tabName = "参加人员";
      }
      if (type == "2") {
        this.title = "选择发布单位";
        this.tabActive = "identity";
        this.tabName = "发布单位";
      }
      if (type == "3") {
        this.title = "选择参加人员";
        this.tabActive = "identity";
        this.tabName = "参加人员";
      }
      this.$forceUpdate();

      this.type = type;
      this.dialogFormVisible = true;
      this.checkIdentyData = [];
      this.treeCheckedKey = [];
      this.userTreeDefaultKey = [];

      //回选旧数据
      if (type == "1" || type == "2" || type == "3") {
        if (checkedData.length > 0) {
          checkedData.forEach((item) => {
            this.checkIdentyData.push(item);
          });
        }
      }

      if (type == "0") {
        console.log(checkedData, "checkedData选中数据");

        if (Array.isArray(checkedData) && checkedData.length > 0) {
          checkedData.forEach((item) => {
            this.userTreeDefaultKey.push(item);
            this.treeCheckedKey.push(item);
          });
          this.checkList = checkObjData.map((item) => {
            return {
              ...item,
              name: item.inviteName || item.userName,
              jobName: item.postName,
              office: item.orgName,
              address: item.deptName,
            };
          });
          console.log(this.checkList, "this.checkList");
        } else {
          if (checkedData.length > 0) {
            // 参加人员回显
            this.checkedValue = checkedData.split(",");
          }
        }
      }

      if (type == "2") {
        this.max = 1;
      } else {
        this.max = 99;
      }
    },
    onSelect(data) {
      console.log(data, "onSelect");
    },
    onTreeShowList(data) {
      console.log(data, "data");
      let list = data.filter(
        (item) =>
          !item.children || (item.children && item.children.length === 0)
      );
      return list.map((item) => {
        return {
          ...item,
          jobName: item.postName,
          office: item.orgName,
          address: item.deptName,
        };
      });
      console.log(this.checkList, "this.checkList");
    },
    onSearch(data) {
      console.log("搜索", data);
    },
    // 树状选择
    onCheck(item, data) {
      console.log(data, "data");
      // 清空参加人员
      this.checkedValue = [];
      this.checkList = [];
      this.checkedShow = "1";
      this.checked = [];
      data.checkedNodesPositions.filter((item1) => {
        // console.log(item1,'ITEM1');
        this.checked.push(item1.node.componentOptions.propsData.dataRef);
        // console.log("---++++----",JSON.stringify(item1.node.componentOptions.propsData.dataRef));
      });
      console.log(this.checked, "this.checkedthis.checked");

      this.checkList = this.onTreeShowList(this.checked);
      console.log(this.checked, "this.checked");
    },
    // 常用人树状选择
    onUsualCheck(item, data) {
      this.usualCheckList = [];
      this.usualChecked = [];
      data.checkedNodesPositions.filter((item1) => {
        // console.log(item1,'ITEM1');
        this.usualChecked.push(item1.node.componentOptions.propsData.dataRef);
        // console.log("---++++----",JSON.stringify(item1.node.componentOptions.propsData.dataRef));
      });
      console.log(this.usualChecked, "this.usualChecked");
      this.usualCheckList = this.onTreeShowList(this.usualChecked);
      console.log(this.usualCheckList, "this.usualChecked");
    },
    onSelect(item, data) {},
    confirm() {
      let checkedData = [];
      if (this.type == "1" || this.type == "2" || this.type == "3") {
        checkedData = this.checkIdentyData;
      }
      if (this.type == "0") {
        // checkedData = this.$refs.userTree.getCheckedNodes();
        // this.$refs.userTree.setCheckedKeys([]);
      }
      if (this.type == "0" || this.type == "1") {
        if (this.$refs.userTree != null) {
          // this.$refs.userTree.setCheckedKeys([]);
        }
      }
      console.log(this.checkedShow, "this.checkedShowthis.checkedShow");
      // 判断是否为常用人
      if (this.checkedShow === "0") {
        console.log("进入了常用人", this.usualCheckList);
        // 常用人
        checkedData = this.usualCheckList;
      } else if (this.checkedShow === "1") {
        console.log("选了机构树", this.checked);
        // 机构树
        checkedData = this.checked;
      } else if (this.checkedShow === "2") {
        console.log("选择了用户组");
        console.log(this.checkedValue, "this.checkedValuethis.checkedValue");
        // 受邀范围 选择用户组
        if (this.checkedValue) {
          let plainOptions = [];
          this.plainOptions.map((item) => {
            plainOptions = [...plainOptions, ...item.userGroupEntityList];
          });
          this.checkedValue.forEach((i, n) => {
            var list = plainOptions.filter((item) => item.id == i);
            checkedData = [...checkedData, ...list];
          });
          if (this.defaultKey == "2") {
            checkedData.map((item) => {
              item.itemType = "2";
              item.groupg = "1";
              item.name ? "" : (item.name = item.groupName);
              return item;
            });
          }
        }
      }
      // 判断是否为树状数据
      // if (this.checkedShow) {
      //   checkedData = this.checked;
      // }

      console.log("🤗🤗🤗, checkedData =>", checkedData);
      this.treeCheckedKey = [];
      this.max = 99;
      //清空树勾选节点
      //收起所有树节点
      //this.$refs.userTree.$children.forEach((item) => (item.expanded = false));
      console.log(this.type, "this.typethis.type");
      //回调父页面数据
      if (this.type != "2") {
        console.log("调了this.type！=2");
        this.$parent.saveRel(this.type, checkedData);
      } else {
        console.log("调了另一个");
        this.$parent.savePublish(checkedData);
      }
      this.dialogFormVisible = false;
      // 清空所有选中数据
      // this.checkIdentyData = [];
      // this.checkedValue = [];
      // this.checked = [];
      // 清空右边显示的数据
      // this.usualCheckList = [];
      // this.groupCheckList = [];
      // this.checkList = [];
      // this.checkedUsualValue = [];
      // this.checkedGroupValue = [];
      console.log(
        this.userTreeDefaultKey,
        "userTreeDefaultKeyuserTreeDefaultKey"
      );

      this.userTreeDefaultKey = [];
      this.expandedKeys = [];
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    close() {
      this.checkIdentyData = [];
      this.dialogFormVisible = false;
      this.filterText = "";
    },

    handleCheckedIdentity(value) {
      // this.checkedShow = false;
      this.checkedShow = "0";
      this.checkIdentyData = this.unique(value);
    },

    //数组去重
    unique(arr) {
      return Array.from(new Set(arr));
    },
    checkPermission,
    // 上传文件限制 直接return
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xlsx|xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xlsx或.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },
    //导入代表姓名
    importRepresentationList({ file, fileList }) {
      // console.log("----导入代表用户----")
      var data = new FormData();
      data.append("file", file);
      importRepresentation(data).then((res) => {
        this.normalUserList = [];
        this.repeatUserList = [];
        this.absentUserList = [];
        let normalIndex = 1;
        let repeatIndex = 1;
        let absentIndex = 1;
        res.data.data.forEach((val) => {
          if (val.userType === 0) {
            this.normalUserList.push({
              id: normalIndex++,
              userName: val.userName, //代表姓名
              orgName: val.orgName, //代表岗位名称
              userId: val.userId, //用户Id
              parentId: val.parentId, //机构父Id
              orgId: val.orgId, //机构Id
            });
          } else if (val.userType === 1) {
            this.repeatUserList.push({
              id: repeatIndex++,
              userName: val.userName, //代表姓名
              orgName: val.orgName, //代表岗位名称
              userId: val.userId, //用户Id
              parentId: val.parentId, //机构父Id
              orgId: val.orgId, //机构Id
            });
          } else if (val.userType === 2) {
            this.absentUserList.push({
              id: absentIndex++,
              userName: val.userName, //代表姓名
              orgName: val.orgName, //代表岗位名称
              userId: val.userId, //用户Id
              parentId: val.parentId, //机构父Id
              orgId: val.orgId, //机构Id
            });
          }
        });
        // console.log("----正常用户----",JSON.stringify(this.normalUserList));
        // console.log("----重复用户----",JSON.stringify(this.repeatUserList));
        // console.log("----不存在用户----",JSON.stringify(this.absentUserList));
        if (res.data.code != "0000") {
          return this.$message.error(res.data.msg);
        }
        if (res.data.code == "0000") {
          // console.log("---接口调用返回成功----")
          //判断如果返回列表长度大于0
          if (res.data.data.length > 0) {
            this.$alert("导入成功", "提示", {
              confirmButtonText: "确定",
              callback: (action) => {
                this.dialogVisible = true;
              },
            });
          } else {
            this.$message.error(
              "没有解析到数据，请检查该Excel文件是否存在数据"
            );
          }
        }
      });
    },
    //选择重复代表用户
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.checkedUserList = selectedRows;
    },
    // handleSelectionChange(selection) {
    //   // 当勾选项变化时会触发
    //   this.checkedUserList = selection;
    //   console.log('选中的用户：', JSON.stringify(selection));
    //   return this.checkedUserList;
    // },
    indexMethod(index) {
      return index + 1;
    },
    async userConfirm() {
      this.realityCheckList = [];
      this.receiverTreeNodeList = [];
      this.checkedUserList = [...this.normalUserList, ...this.checkedUserList];
      if (this.checkedUserList.length === 0) {
        this.dialogVisible = false;
      } else {
        this.isLoading = true;
        this.checked = [];
        this.checkedUserList.forEach((userVal, index) => {
          this.findAndPushUserIds(
            this.userTreeData,
            userVal,
            this.receiverTreeNodeList,
            this.realityCheckList
          );
        });
        this.realityCheckList = this.realityCheckList.map((item, index) => {
          return {
            id: index + 1,
            ...item,
          };
        });

        this.isLoading = false;
        this.dialogVisible = false;
        this.importDefaultKey = "1";
        this.realityVisible = true;
      }
    },
    async userRealityConfirm() {
      this.$nextTick(() => {
        this.userTreeDefaultKey = this.receiverTreeNodeList;
        this.treeCheckedKey = this.receiverTreeNodeList;
        this.normalUserList = [];
        this.repeatUserList = [];
        this.checkedUserList = [];
        this.absentUserList = [];
        this.selectedRowKeys = [];
        this.importDefaultKey = "1";
        this.realityVisible = false;
      });
    },
    userClose() {
      // console.log("-------取消功能--------")
      this.normalUserList = [];
      this.repeatUserList = [];
      this.checkedUserList = [];
      this.absentUserList = [];
      this.selectedRowKeys = [];
      this.realityCheckList = [];
      this.importDefaultKey = "1";
      this.dialogVisible = false;
    },
    userRealityClose() {
      // console.log("-------取消功能--------")
      this.normalUserList = [];
      this.repeatUserList = [];
      this.checkedUserList = [];
      this.absentUserList = [];
      this.selectedRowKeys = [];
      this.realityCheckList = [];
      this.importDefaultKey = "1";
      this.realityVisible = false;
    },
    // 下载批量用户导入模板
    downloadExcel() {
      this.downloadExcelLoading = true;
      downloadUserImportExcel(this.userTree).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `批量用户导入模板.xlsx`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadExcelLoading = false;
        }, 2000);
      });
    },
    // 封装成方法
    findAndPushUserIds(data, userVal, receiverTreeNodeList, realityCheckList) {
      data.forEach((val) => {
        this.traverseChildren(
          val.children,
          userVal,
          receiverTreeNodeList,
          realityCheckList
        );
      });
    },
    // 递归遍历 children 的辅助函数
    traverseChildren(
      children,
      userVal,
      receiverTreeNodeList,
      realityCheckList
    ) {
      if (!Array.isArray(children)) {
        return; // 如果 children 不是数组，直接返回
      }
      children.forEach((cval) => {
        // 递归调用，检查下一层，但不进行匹配判断
        if (cval.children) {
          this.traverseChildren(
            cval.children,
            userVal,
            receiverTreeNodeList,
            realityCheckList
          );
        }

        // 只有在没有下一层 children 时，才进行匹配判断
        if (!cval.children) {
          if (userVal.userId === cval.id && userVal.parentId == cval.orgId) {
            if (!receiverTreeNodeList.some((item) => item === cval.uniqueId)) {
              receiverTreeNodeList.push(cval.uniqueId);
              realityCheckList.push({
                userName: userVal.userName,
                orgName: userVal.orgName,
              });
              this.checked.push(cval);
              this.checkList = this.onTreeShowList(this.checked);
            }
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .ant-modal-content {
  top: 50%;
  transform: translate(0, -50%);
}
::v-deep .ant-modal-body {
  padding: 0 24px;
}
.group_item {
  flex: 45%;
  // flex: 4.5;
  border: 1px solid #e8e8e8;
  padding: 10px;
  overflow-y: auto;
}
::v-deep .ant-modal {
  // top: 30px;
  height: 100%;
  overflow: hidden;
  top: 0;
  padding-bottom: 0;
}
.table_container {
  display: flex;
  width: 100%;
  height: 100%;
  // height: 460px;
  padding-bottom: 10px;
  .table_left {
    // flex: 45%;
    flex: 48%;
    border: 1px solid #e8e8e8;
    // height: 500px;
    padding: 10px;
    padding-right: 0;
  }
  .table_icon {
    // flex: 10%;
    flex: 1%;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 30px;
    justify-content: space-evenly;
    // padding: 0 10px;
  }
  ::v-deep .ant-spin-nested-loading,
  .choose_list {
    padding: 10px;
    // flex: 45%;
    flex: 48%;
    // flex: 5;
  }
  ::v-deep .ant-spin-nested-loading {
    width: 100%;
    height: 325px;
    overflow-y: auto;
  }
}
.choose_list {
  border: 1px solid #e8e8e8;
  // padding: 0 20px;
  .list_table {
    width: 100%;
    height: 398px;
    overflow: auto;
    // border: 1px solid #e8e8e8;
    .list_all {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e8e8e8;
      padding: 5px 10px;
      .list_left {
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 50%;
        font-size: 12px;
        background-color: #d8293a;
        color: #fff;
        margin-right: 15px;
      }
      .list_right {
        color: #000;
      }
    }
  }
}
// ::v-deep .ant-spin-nested-loading {
//   margin-top: 35px;
// }
.all_Num {
  font-size: 16px;
  font-weight: 700;
  /* color: #d92b3e; */
  text-align: left;
}
.loading-indicator {
  text-align: center;
}
::v-deep span.ant-radio + * {
  padding-right: 8px;
  padding-left: 1px !important;
}

.el-checkbox.is-bordered.el-checkbox--mini {
  margin-bottom: 5px;
  margin-left: 10px;
}

::v-deep .ant-tree {
  max-height: 500px;
  width: 100%;
  overflow-y: auto;
  scrollbar-width: none; /*对于Firefox 隐藏滚动条*/
}
::v-deep .ant-tree::-webkit-scrollbar {
  display: none;
}
::v-deep .cjry .radioStyle span.ant-radio + * {
  padding-left: 0px;
  padding-right: 30px;
}

::v-deep .sortOrder span.ant-radio + * {
  padding: 0 8px !important;
}
.import_style {
  ::v-deep .ant-modal-body {
    height: 455px;
    overflow: scroll;
  }
}
/* 表格斑马样式 */
::v-deep .ant-table-tbody tr:nth-child(2n) {
  background-color: #fafafa;
}
</style>
