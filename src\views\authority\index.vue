<template>
  <div class="table-container">
    <a-row :gutter="15">
      <a-col style="margin-bottom: 15px;">
        <titleBreacrumb headline="系统管理"
                        title="权限管理" />
      </a-col>
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="5"
             :xl="5">
        <a-input placeholder="输入关键字过滤"
                 class="tree-input"
                 allow-clear
                 v-on:keyup.enter="handleQuery"
                 @change="onChange" />
        <!-- <el-tree
          ref="menuTree"
          :data="menuTreeData"
          :default-expanded-keys="defaultExpendedKeys"
          :default-expand-all="false"
          :filter-node-method="filterNode"
          :highlight-current="true"
          :props="defaultProps"
          class="byui-filter-tree fk-tree"
          node-key="menuId"
          @node-click="handleClick"
        ></el-tree> -->

        <a-tree v-if="menuTreeData.length > 0"
                ref="identityTree"
                :tree-data="menuTreeData"
                :selected-keys="selectedKeys"
                :expanded-keys="expandedKeys"
                :auto-expand-parent="autoExpandParent"
                :replace-fields="defaultProps"
                @select="handleCheck"
                @expand="onExpand">
          <template slot="title"
                    slot-scope="{ menuName }">
            <span v-if="searchValue && menuName.indexOf(searchValue) > -1"
                  style="color: #f50;">
              {{ menuName }}
            </span>
            <span v-else>{{ menuName }}</span>
          </template>
        </a-tree>
      </a-col>
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="19"
             :xl="19">
        <byui-query-form>
          <byui-query-form-left-panel>
            <a-button v-if="checkPermission(['OPER_AUTHORITY_BASE'])"
                      icon="plus"
                      type="primary"
                      @click="handleAdd">添加</a-button>
            <a-button v-if="checkPermission(['OPER_AUTHORITY_BASE'])"
                      icon="delete"
                      type="danger"
                      @click="handleDelete">删除</a-button>
          </byui-query-form-left-panel>
          <byui-query-form-right-panel>
            <a-form ref="form"
                    :form="queryForm"
                    layout="inline">
              <a-form-item label="权限名称">
                <a-input v-model="queryForm.authName"
                         placeholder="权限名称"
                         style="width: 200px;"
                         clearable
                         allow-clear
                         v-on:keyup.enter="handleQuery"
                         class="table-input" />
              </a-form-item>
              <a-form-item>
                <a-button type="primary"
                          native-type="submit"
                          @click="handleQuery">搜索</a-button>
                <a-button style="margin-left: 12px;"
                          @click="reset"
                          class="pinkBoutton">重置</a-button>
              </a-form-item>
            </a-form>
          </byui-query-form-right-panel>
        </byui-query-form>

        <a-spin :indicator="indicator"
                :spinning="listLoading">
          <a-table :bordered="false"
                   class="directorySet-table"
                   ref="table"
                   size="small"
                   :columns="columns"
                   :pagination="pagination"
                   :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
                   :data-source="list"
                   :rowKey="
              (record, index) => {
                return index;
              }
            "
                   :scroll="{ x: 300, y: 0 }"></a-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { getTree as getMenuTree } from "@/api/menu";
import { getPage, doDelete } from "@/api/authority";
import checkPermission from "@/utils/permission";
import { mapGetters } from "vuex";
export default {
  name: "rel",
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
  },
  data () {
    return {
      TBloading: false,
      options: [],
      list: [],
      listLoading: true,
      layoutPage: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      queryForm: {
        page: 1,
        rows: 10,
        authName: "",
        authOwnId: 0,
        tenantId: this.$tenantId,
        authOwnType: "menu",
      },
      //机构树配置
      menuTreeData: [],
      // defaultProps: {
      //   children: "children",
      //   label: "menuName",
      // },
      loading: true,
      filterText: "",
      checkNodeKeys: [],
      defaultExpendedKeys: [],
      i: 0,
      treeFlag: 0,
      treeDialogVisible: false,
      treeForm: {
        rootId: "0",
        tenantId: this.$tenantId,
      },

      //a-tree//
      searchValue: "", //搜索
      // 展开的节点
      expandedKeys: [],
      // 是否自动展开
      autoExpandParent: true,
      // 选择的节点
      selectedKeys: [],
      // 替换 treeNode 中 title,key,children 字段
      defaultProps: {
        children: "children",
        title: "menuName",
        key: "menuId",
      },

      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,

      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "权限名称",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "authName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "权限编码",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "authCode",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "权限类型",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "authType",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "权限描述",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "authDesc",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },

        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 260,
          customRender: (text, record, index) => {
            let permission = checkPermission(["OPER_AUTHORITY_BASE"]);
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(record);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "inline-block" : "none",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "系统管理");
    this.$store.dispatch("navigation/breadcrumb2", "权限管理");
    // if (this.$route.query && this.$route.query.menuId) {
    //   this.expandedKeys = [this.$route.query.menuId];
    //   this.queryForm.authOwnId = this.$route.query.menuId;
    // }
  },
  watch: {
    $route: {
      handler (newRouter) {
        if (this.$route.query && this.$route.query.menuId) {
          this.queryForm.authOwnId = this.$route.query.menuId;
          setTimeout(() => {
            this.expandedKeys.push(this.$route.query.menuId);
            this.selectedKeys = [this.$route.query.menuId];
            this.autoExpandParent = true;
          }, 500);
        }
      },
      immediate: true,
    },
    filterText (val) {
      this.$refs.menuTree.filter(val);
    },
  },
  mounted () {
    const that = this;
    that.getTreeListFuc();
    that.fetchData();
  },
  methods: {
    reset () {
      this.queryForm = {
        page: 1,
        rows: 10,
        authName: "",
        authOwnId: 0,
        tenantId: this.$tenantId,
        authOwnType: "menu",
      };
      this.handleQuery();
    },
    // 切换页数
    changePageSize (current, pageSize) {
      this.queryForm.page = current; //queryForm.page是页码
      this.queryForm.rows = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = current;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange (current, pageSize) {
      this.queryForm.page = current;
      this.pagination.current = current;
      this.fetchData();
    },

    // 多选选择
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },

    setSelectRows (val) {
      this.selectRows = val;
    },
    handleAdd () {
      this.$router
        .push({
          path: "/systemManage/editAuthority",
          query: {
            authOwnId: this.queryForm.authOwnId,
            authOwnType: "menu",
          },
        })
        .catch(() => { });
    },
    handleEdit (row) {
      this.$router
        .push({
          path: "/systemManage/editAuthority",
          query: {
            authOwnId: this.queryForm.authOwnId,
            authOwnType: "menu",
            authId: row.authId,
          },
        })
        .catch(() => { });
    },
    handleDelete (row) {
      if (row.authId) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          doDelete([row.authId]).then((res) => {
            this.$baseMessage(res.message, "success");
            this.fetchData();
          });
        });
      } else {
        if (this.selectRows.length > 0) {
          const ids = this.selectRows.map((item) => item.authId);
          this.$baseConfirm("你确定要删除选中项吗", null, () => {
            doDelete(ids).then((res) => {
              this.$baseMessage(res.message, "success");
              this.fetchData();
            });
          });
        } else {
          this.$baseMessage("未选中任何行", "error");
          return false;
        }
      }
    },
    handleSizeChange (val) {
      this.queryForm.rows = val;
      this.fetchData();
    },

    handleQuery () {
      this.queryForm.page = 1;
      this.fetchData();
    },
    fetchData () {
      this.listLoading = true;
      getPage(this.queryForm).then((res) => {
        this.list = res.data.records;
        this.total = res.data.total;
        // 清空
        this.selectedRowKeys = [];
        // 设置总数
        this.pagination.total = res.data.total;
        setTimeout(() => {
          this.listLoading = false;
        }, 500);
      });
    },

    // 获取tree数据
    getTreeListFuc () {
      const that = this;
      getMenuTree(this.treeForm).then((res) => {
        res.data.menuName = "菜单根节点";
        this.menuTreeData = [res.data];
        this.setTreeData([res.data]);
        this.expandedKeys = [0];
      });
    },
    // 设置tree数据 ant 自带isLeaf参数与原数据的isLeaf冲突
    setTreeData (array) {
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        element.isLeaf = element.isLeaf == "NOT_LEAF" ? false : true;
        if (element.children) {
          this.setTreeData(element.children);
        }
      }
      this.menuTreeData = array;
    },
    tableSortChange () { },

    //a-tree//
    // 点击选择
    handleCheck (checkedKeys, info) {
      if (info.selected == true) {
        this.selectedKeys = [info.node.eventKey];
        this.queryForm.authOwnId = info.node.eventKey;
        this.fetchData();
      }
    },

    //展开点击节点下的子节点
    onExpand (expandedKeys) {
      console.log("🤗🤗🤗, expandedKeys =>", expandedKeys);
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },

    // 树状搜索
    onChange (e) {
      const value = e.target.value;
      const expandedKeys = this.getkeyList(value, this.menuTreeData, []);
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      });
    },

    // 获取包含字段的id
    getkeyList (value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.menuName.indexOf(value) > -1) {
          keyList.push(node.menuId);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },

    checkPermission,
  },
};
</script>
