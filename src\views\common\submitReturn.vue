<template>
  <!-- <div v-if="this.currentActivity.id != ''"> -->
  <div>
    <a-modal :dialog-style="{ top: '0px' }"
             title="处理建议"
             :model="addForm"
             :visible.sync="visible"
             width="65%"
             @cancel="close">
<!--      <div :style="tabStyle">-->
<!--      <a-spin :spinning="iSpinning">-->
      <div :style="tabStyle">
        <a-descriptions bordered size="small">
          <a-descriptions-item label="流程名称" :span="3">{{
              processDefinition.name
            }}</a-descriptions-item>
          <a-descriptions-item label="当前进度" :span="3">{{
              currentActivity && currentActivity.description ? currentActivity.description : '审核'
            }}</a-descriptions-item>
          <a-descriptions-item label="上一节点" :span="3">{{
              prevActivityName && prevActivityName.node ? prevActivityName.node.description : ''
            }}</a-descriptions-item>
          <a-descriptions-item label="退回节点选择" pop: :span="3">
            <a-radio-group
              v-for="item in prevActivity"
                 :key="item.id"
                 v-model="nextActivityId"
                 :style="radioColor"
                 @change="onChange1(item)">
            <a-radio :value="item.node.suggStatus">{{ item.node.description }}</a-radio>
          </a-radio-group>
          </a-descriptions-item>
          <a-descriptions-item  :span="3">
            <div slot="label">
              <span style="color:#ff0000">*</span><span>退回意见</span>
            </div>
              <a-form-model ref="resourceForm" :model="resourceForm"
                            :label-col="{ span: 6 }"
                            :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col span="1" style="text-align: end; height: 105px; line-height: 105px;">
                </a-col>
                <a-col span="23">
                  <a-checkbox-group  v-model="resourceForm.text">
                    <a-row>
                      <a-col v-for="(item, index) in OpinionOptionList" :key="item">
                        <a-checkbox :value="item">{{ item }}<br /></a-checkbox>
                      </a-col>
                    </a-row>
                  </a-checkbox-group>
                  <a-textarea style="width: 80%" v-model="resourceForm.qt" v-show="isOtheryj" :rows="5"></a-textarea>
                </a-col>
              </a-row>
            </a-form-model>
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="3">
            <a-form-model-item prop="comment">
              <a-input v-model="comment"
                       style="height: 100px;"
                       type="textarea" />
            </a-form-model-item>
          </a-descriptions-item>
          <a-descriptions-item label="短信提醒" :span="3">
            <a-form-model-item>
              <a-input v-model="message"
                       style="width: 400px; height: 100px;"
                       type="textarea" />
            </a-form-model-item>
            <a-col span="9"
                   style="margin-top: 0px;">
              <a-checkbox v-model="isSendSMS"
                          value="sendSMS"
                          @change="asd">发送短信消息提醒</a-checkbox>
              <p class="wxts">
              </p>
            </a-col>
          </a-descriptions-item>
        </a-descriptions>
      </div>
<!--      <a-row class="dqjc">-->
<!--        <a-col span="4"-->
<!--               class="lcmcsss">当前进度</a-col>-->
<!--        <a-col span="8"-->
<!--               class="lcmcsss" >{{ currentActivity && currentActivity.description ? currentActivity.description : '审核'}}-->
<!--        </a-col>-->
<!--        <a-col span="4"-->
<!--               class="lcmcsss">上一节点</a-col>-->
<!--        <a-col span="8">-->
<!--          <span>{{prevActivityName && prevActivityName.node ? prevActivityName.node.description : '' }}</span>-->
<!--        </a-col>-->
<!--      </a-row>-->
<!--      <a-row class="xbcl"-->
<!--             style="padding-left: 5px;">-->
<!--        <a-col span="4"-->
<!--               class="lcmcsss">退回节点选择</a-col>-->
<!--        <a-col span="20">-->
<!--          <a-radio-group v-for="item in prevActivity"-->
<!--                         :key="item.id"-->
<!--                         v-model="nextActivityId"-->
<!--                         :style="radioColor"-->
<!--                         @change="onChange1(item)">-->
<!--            <a-radio :value="item.node.suggStatus" class="ellipsis">{{ item.node.description }}</a-radio>-->
<!--          </a-radio-group>-->
<!--        </a-col>-->
<!--      </a-row>-->
<!--        <a-row class="liucheng ssyj">-->
<!--          <div>-->
<!--            <a-form-model ref="resourceForm" :model="resourceForm"  :label-col="{ span: 6 }"-->
<!--                          :wrapper-col="{ span: 16 }">-->
<!--              <a-row>-->
<!--                <a-col span="1" style="text-align: end; height: 105px; line-height: 105px;">-->
<!--                </a-col>-->
<!--                <a-col span="23">-->
<!--                  <a-checkbox-group  v-model="resourceForm.text">-->
<!--                    <a-row>-->
<!--                      <a-col v-for="(item, index) in OpinionOptionList" :key="item">-->
<!--                        <a-checkbox :value="item">{{ item }}<br /></a-checkbox>-->
<!--                      </a-col>-->
<!--                    </a-row>-->
<!--                  </a-checkbox-group>-->
<!--                  <a-textarea style="width: 80%" v-model="resourceForm.qt" v-show="isOtheryj" :rows="5"></a-textarea>-->
<!--                </a-col>-->
<!--              </a-row>-->
<!--            </a-form-model>-->
<!--          </div>-->
<!--        </a-row>-->
<!--      <a-row class="liucheng ssyj">-->
<!--        <a-col span="2"-->
<!--               style="padding: 5px 0 0 5px;">-->
<!--          <span class="">备注</span>-->
<!--        </a-col>-->
<!--        <a-col span="20"-->
<!--               style="padding: 8px 15px 0 0;">-->
<!--                    <a-form-model-item prop="comment">-->
<!--            <a-input v-model="comment"-->
<!--                     style="height: 100px;"-->
<!--                     type="textarea" />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--      </a-row>-->
<!--      <a-row class="xxts"-->
<!--             style="padding: 8px 0;">-->
<!--        <a-col span="2"-->
<!--               style="padding-left: 5px;">提醒信息</a-col>-->
<!--        <a-col span="13">-->
<!--          <a-form-model-item>-->
<!--            <a-input v-model="message"-->
<!--                     style="width: 400px; height: 100px;"-->
<!--                     type="textarea" />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->

<!--        <a-col span="9"-->
<!--               style="margin-top: 0px;">-->
<!--          <a-checkbox v-model="isSendSMS"-->
<!--                      value="sendSMS"-->
<!--                      @change="asd">发送短信消息提醒</a-checkbox>-->
<!--          <p class="wxts">-->
<!--&lt;!&ndash;            【温馨提示】若对方没有关注“广州人大服务号”无法收到提醒时，将自动发送手机短信提醒。&ndash;&gt;-->
<!--          </p>-->
<!--        </a-col>-->
<!--      </a-row>-->
<!--      </a-spin>-->
<!--      </div>-->
      <a-row slot="footer">
        <a-spin :spinning="iSpinning" :indicator="indicator">
        <a-col :span="8"
               push="8">
          <a-space class="operator">
            <a-button @click="save" type="primary">
              <a-icon type="check" />{{ buttonText }}
            </a-button>
            <a-button @click="close">
              <a-icon type="close" />取消
            </a-button>
<!--            <a-button @click="lczzjl">流程流转记录</a-button>-->
          </a-space>
        </a-col>
        </a-spin>
      </a-row>
    </a-modal>
<!--    <lczzjl ref="lczzjl"-->
<!--            :proc-inst-id="procInstId"></lczzjl>-->
    <commonSuggestInfo ref="commonSuggestInfo"></commonSuggestInfo>
  </div>
</template>

<script>
//树形控件
// import lczzjl from "./lczzjl";
import {
  getprocessApi,
  getgetNextUsersApi,
  doReturn,
} from "@/api/yajyFlow/flowApi.js";
import commonSuggestInfo from "@/views/common/commonSuggestInfo.vue";

export default {
  // components: { lczzjl },
   components: { commonSuggestInfo },
  // props: ["procInstId", "flowId"],
  props: {
    procInstId: {
      type: [String, Number],
      required: true
    },
    flowId: {
      type: [String, Number],
      required: true
    },
    defaultNode: {
      type: [String, Number],
      required: true
    },
    actTypeReturn: {
      type: [String, String],
      required: true
    },
    flowName: {
      type: [String, String],
      required: true
    }
  },
  data () {
    return {
      radioColor: {
        borderColor: "black",
      },
      indicator: <a-icon/>,
      iSpinning: false,
      activeKey: "3",
      isSendSMS: false,
      visible: false,
      showIcon: false,
      showLine: true,
      threalName: "",
      childrenName: "",

      // 上一节点
      prevActivity: {},
      prevActivityName: {},
      //流程名称
      //当前进度
      currentActivity: {},
      // 下步处理
      nextActivities: [],
      processDefinition: {
        name: '',
      },
      tabStyle: { maxHeight: "750px", overflowY: "auto" },
      lists: [],
      treelist: [],
      treeData: [],
      value: 0,
      hxrvalue: [],
      duanxing: 1,
      hx: {
        height: "145px",
        overflow: "auto",
        paddingLeft: "5px",
      },
      jg: {
        height: "145px",
        overflow: "auto",
        paddingLeft: "5px",
      },
      cy: {
        height: "145px",
        overflow: "auto",
        paddingLeft: "5px",
      },
      radioStyle: {
        display: "block",
        height: "30px",
        lineHeight: "30px",
        title: "fullName",
        key: "id",
      },
      OpinionOptionList: [
        "建议内容不属于广州市行政区域内事务或者行政区域职权范围。",
        "建议内容不够明确具体，请进一步加强调研，反映实际情况，分析问题原因，提出改进工作、解决问题、完善政策的具体措施。",
        "存在《广东省各级人民代表大会代表建议、批评和意见办理规定》第十条所规定的不应当作为代表建议提出的若干情形。",
        "代表本人要求退回。",
        "其他",
      ],
      isOtheryj: false,
      resourceForm:{
      },
      keys: "",
      hxrList: [],
      taskDefinitionKey: "",
      taskId: "",
      addForm: {},
      sendSMS: "",
      message: "",
      comment: "",
      flowNo: "",
      proposalId:"",
      nextActivityId: "",
      procDefKey: "",
      assignee: "",
      currentActivityId: "",
      reviewOrgId: [],
      isReviewOrgId: {},
      nextId: "",
      isTaskClaim: false,
      isShowTaskClaimUser: true,
      textData: "待审核",
      tenantId: "",
    };
  },
  computed: {
    buttonText() {
      return this.actTypeReturn === "unRegister" ? "确认" : "送审";
    }
  },
  //监听父组件传过来的值
  watch: {
    procInstId: {
      // immediate: true, // 这句重要  立即执行handler里面的方法
      handler (val) {
        this.procInstId = val;
        this.getprocessFn();
      },
    },
    "resourceForm.text": {
      handler: function (val) {
        let TEXT = "";
        this.resourceForm.text.map((i, n) => {
          TEXT ? (TEXT += n + 1 + "." + i + "  ") : (TEXT = "1." + i + "  ");
        });
        // this.resourceForm.comment = TEXT
        if (this.resourceForm.text.toString().indexOf("其他") != -1) {
          this.isOtheryj = true;
          // this.resourceForm.comment += '(其他原因：)'
        } else {
          this.isOtheryj = false;
        }
      },
    },
    // 监听 defaultNode 或 prevActivity 的变化，更新 nextActivityId
    // defaultNode(newValue) {
    //   this.updateSelectedRadio();
    // },
    // prevActivity(newValue) {
    //   this.updateSelectedRadio();
    // }
  },
  mounted() {
    // 初始化时设置默认选中的 radio
    // this.updateSelectedRadio();
  },
  created () {

  },

  methods: {
    // 根据 defaultNode 和 prevActivity 设置默认选中的 radio
    updateSelectedRadio() {
      // 遍历 prevActivity，找到与 defaultNode.suggStatus 匹配的项
      const matchingItem = this.prevActivity.find(item => item.node.suggStatus === this.defaultNode);
      console.log('matchingItem---->',matchingItem)
      if (matchingItem) {
        this.nextActivityId = matchingItem.node.suggStatus;  // 设置选中的值
      } else {
        this.nextActivityId = null;  // 如果没有找到匹配项，可以选择不选中
      }
    },
    qwe (e) { },

    //关闭窗口 默认
    close () {
      this.message = "";
      this.visible = false;
      this.hxrvalue = [];
      this.hxrList = [];
      // this.ids = []
      this.comment = "";
      // 获取数据
      // this.getprocessFn();
      this.iSpinning = false
      this.visible = false;
    },

    //关闭
    isclose () {
      this.visible = false;
    },

    asd (val) { },
    //预备人选基本情况申报送审

    async getprocessFn () {
      console.log("来到这里了吗");
      if (this.procInstId != "") {
        let form = {};
        let node = {};
        this.processDefinition.name = this.flowName;
        form.proposalId = this.procInstId;
        form.status = this.flowId;
        console.log("defaultNode--------",this.defaultNode);
        console.log(form);
         getprocessApi(form).then((res) => {
           console.log("------data",res.data);
           if (res.data.code == 200){
             if (res.data.data != null) {
               this.currentActivity = res.data.data.currentNode[0];
               this.nextActivities = [res.data.data.forwardNode[0]];
               this.prevActivity = res.data.data.backNode;
               console.log("this.prevActivity-----dxxxxx",this.prevActivity);
               this.prevActivityName = res.data.data.backNode[this.prevActivity.length-1];

              if(this.defaultNode) {
                this.nextActivityId = this.defaultNode
              }

              console.log("actTypeReturn417---",this.actTypeReturn);

              if(this.defaultNode==40 && this.actTypeReturn == "assginConfirm"){
                this.prevActivity = [res.data.data.backNode[res.data.data.backNode.length - 1]];
                this.currentActivity = {description: "分办审核"};
              }

               console.log("actTypeReturn424---",this.actTypeReturn);

               if(this.defaultNode==40 && this.actTypeReturn == "assginDbcConfirm"){
                 this.prevActivity = [res.data.data.backNode[res.data.data.backNode.length - 1]];
                 this.currentActivity = {description: "分办复审"};
               }
               if(this.actTypeReturn=="unRegister"){
                 this.prevActivity = res.data.data.backNode.slice(0, 6).filter((_, index) => index !== 4);

               }
               console.log("this.defaultNode-----data",this.defaultNode);
               this.flowNo = this.nextActivities[0].flowNo;
               let nextId = this.nextActivities[0].taskDefinitionKey;
               let flowId = this.nextActivities[0].id;
               let params = {
                 taskDefinitionKey: nextId,
                 taskId: this.taskId,
                 flowId: flowId,
               };
             }
           } else {
             this.$message.error(res.data.msg);
           }
         });

      }
    },
    //常用人/机构人员
    callback (e) {
      this.keys = e;
      this.isReviewOrgId.fullName = [];
      this.isReviewOrgId.posName = [];
      if (this.activeKey == "2") {
        for (var i = 0; i < this.lists.length; i++) {
          console.log(this.lists[i].assignee);
          this.lists[i].assignee.fullName = [];
          this.lists[i].assignee.posName = [];
        }
      }
      if (this.activeKey != "3") {
        this.hxrvalue = [];
      }
    },

    //树形控件
    onCheck (checkedKeys, info) {
      // console.log('onCheck', checkedKeys, info);
    },
    onChange1(item) {
      // console.log(item)
      this.nextActivityId = item.node.suggStatus
      console.log(this.nextActivityId)
      let nodeInfo = item.node;
      if(nodeInfo.suggStatus == 10){
        //退回到草稿 获取当前建议领衔代表
        let form = {};
        form.proposalId = this.procInstId;
        getgetNextUsersApi(form).then((res) => {
          if (res.data.code == 200) {
            this.isTaskClaim = false;
            this.hxrList = [res.data.data];
            console.log("hxrList--->",this.hxrList[0]);
            if (this.hxrList && this.hxrList.length == "1") {
              this.hxrvalue.push(this.hxrList[0])
            }
          }
        })
      }
      console.log("item-->",item);
      // getgetNextUsersApi()
    },

    //候选人
    hxrChange (e) {
      // this.message = "";
    },
    //复选改单选
    onSelect (selectedKeys, e) {
      this.message = "";
      if (selectedKeys.checked.length == 2) {
        this.reviewOrgId = [].concat(
          selectedKeys.checked[selectedKeys.checked.length - 1]
        );
      }
      console.log(this.reviewOrgId);
      e.checkedNodes.forEach((item) => {
        this.isReviewOrgId = item.data.props;
      });
    },
    isChange (e) {
      this.message = "";
      console.log(e);
    },
    //发送
    save () {
      let form = {};


      console.log(this.resourceForm);
      console.log(this.resourceForm.qt);
      console.log(this.resourceForm.text);
      if(this.resourceForm.text.includes('其他') && !this.resourceForm.qt){
        this.$message.error("请填写其他原因");
        return;
      }

      form.proposalIds = this.procInstId;
      // form.comment = this.comment;
      form.actType = this.actTypeReturn;
      form.returnNode = this.nextActivityId;
      // this.close();
      // this.$emit('close');
      console.log(form);
      let TEXT = "";
      let isQT = false;
      if(!this.resourceForm.text && !this.resourceForm.qt){
        return this.$message.error("请勾选或填写退回意见");
      }


      if (this.resourceForm.text != null && this.resourceForm.text.length > 0) {
        this.resourceForm.text.map((i, n) => {
          if (i != "其他") {
            TEXT ? (TEXT +=  i + "  ") : (TEXT = i + "  ");
          }
          if (i == "其他") {
            isQT = true;
          }
        });
      }

      this.resourceForm.comment =
        isQT && this.resourceForm.qt
          ? TEXT  + " 其他 原因：" + this.resourceForm.qt
          : isQT
          ? TEXT  + " 其他"
          : TEXT;
      console.log(this.resourceForm, "this.resourceForm");

      form.comment = this.resourceForm.comment || "";
      // //提交校核 通过
      // let content = "";
      // if (this.returnValueTitle == "校核意见") {
      //   content = "请确认是否已按照文件格式要求调整建议正文附件的文件格式并重新上传？若已调整文件格式点击确定即校核通过。";
      // } else {
      //   content = "是否确定退回？";
      // }
      // 退回处理接口
      doReturn(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success(res.data.message);
          this.resourceForm = {
            meetName: "",
            meet: "",
            host: "",
            hostName: "",
            comment: "",
          };
          this.returnValue = false;
          this.jhLoading = false;
          this.close();
          // 关闭处理建议工作台弹窗
          this.$emit('close');
          this.$refs.commonSuggestInfo.visible = false;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.liucheng {
  border-bottom: 1px solid #ccc;
}

.xxts {
  border: 1px solid #ccc;
  border-top: none;
}

.ssyj {
  border: 1px solid #ccc;
  border-top: none;
}

.cyren {
  border: 1px solid #ccc;
  border-top: none;
}

.cyrss {
  border-right: 1px solid #ccc;
}

.lcmcs {
  border: 1px solid #ccc;
  height: 30px;
  line-height: 30px;
}

.lcmcsss {
  border-right: 1px solid #ccc;
  height: 30px;
  line-height: 30px;
}

.dqjc {
  border: 1px solid #ccc;
  border-top: none;
  border-bottom: none;
  height: 30px;
  line-height: 30px;
  padding-left: 5px;
}

.xbcl {
  border: 1px solid #ccc;
  height: 30px;
  line-height: 30px;
  padding-left: 5px;
}

.ellipsis {
   //white-space: nowrap;
   //overflow: hidden;
   //text-overflow: ellipsis;
   //max-width: 100%;
  word-break: break-all;
 }

.ant-tabs-bar {
  margin: 0 !important;
}

.requireds::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  // font-size: 14px;
  @include add-size($font_size_16);
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}
.operator {
  margin-left: 10px;
  margin-bottom: 18px;
}

.wxts {
  padding-top: 5%;
  margin-left: 0%;
  width: 100%;
  color: red;
}
</style>
