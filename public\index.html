<!DOCTYPE html>
<html lang="zh-cmn-Hans">

<head>
  <meta charset="utf-8" />
  <meta name="renderer" content="webkit" />
  <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
  <meta name="keywords" content="ilz" />
  <meta name="description" content="<%= webpackConfig.name %>" />
  <meta name="author" content="lxp" />
  <link rel="shortcut icon" href="<%= BASE_URL %>favicon.ico" />
  <title>
    <%= webpackConfig.name %>
  </title>
</head>

<body>
  <noscript>
    非常抱歉鉴于安全考量,您无法查看
  </noscript>
  <div id="vue-admin-beautiful"></div>
  <script>
    /^http(s*):\/\//.test(location.href) ||
      alert("基于vue-admin-beautiful开发的项目需要部署到服务器下访问");

  </script>
</body>

</html>
