import { instance_1 } from "@/api/axiosRq";
import requestTemp from "@/utils/requestTemp.js";
import { data } from "jquery";

// -在线学习-学习进度接口
export function getschedulelistApi(data) {
    return instance_1({
        url: `/myPortal/onLineStudy/schedule/list`,
        method: "post",
        params: data,
    });
}

// 调查问卷管理列表
export function getManagerListApi(params) {
    return instance_1({
        url: `/myPortal/questionnaire/manager/list`,
        method: "post",
        params,
    });
}
//查看详情信息
export function getViewDetailsApi(params) {
    return instance_1({
        url: `/myPortal/questionnaire/manager/viewDetails`,
        method: "get",
        params: { id: params },
    });
}
//发布
export function getOperateApi(params) {
    return instance_1({
        url: `/myPortal/questionnaire/manager/operate`,
        method: "get",
        params: { id: params, flag: 1 },
    });
}
//撤销
export function getrevokeApi(params) {
    return instance_1({
        url: `/myPortal/questionnaire/manager/operate`,
        method: "get",
        params: { id: params, flag: 0 },
    });
}
//删除
export function getmanagerdeleteApi(params) {
    return instance_1({
        url: `/myPortal/questionnaire/manager/delete`,
        method: "get",
        params: { id: params },
    });
}
//修改
export function getmanagerupdateApi(data) {
    return instance_1({
        url: `/myPortal/questionnaire/manager/update`,
        method: "post",
        data,
    });
}
//新增
export function getmanagerinsertApi(data) {
    return instance_1({
        url: `/myPortal/questionnaire/manager/insert`,
        method: "post",
        data,
    });
}
//问题模型列表
export function getfindByIdIssueModelListApi(params) {
    return instance_1({
        url: `/myPortal/questionnaire/statistics/findByIdIssueModelList`,
        method: "get",
        params: { modelId: params },
    });
}
//问题模型添加
export function getaddIssueModelApi(data) {
    return instance_1({
        url: `/myPortal/questionnaire/statistics/addIssueModel`,
        method: "post",
        data,
    });
}

//问题模型删除
export function getdeleteIssueModelApi(params) {
    return instance_1({
        url: `/myPortal/questionnaire/statistics/deleteIssueModel`,
        method: "get",
        params: { modelItemId: params },
    });
}
//问题模型修改
export function getupdateIssueModelApi(data) {
    return instance_1({
        url: `/myPortal/questionnaire/statistics/updateIssueModel`,
        method: "post",
        data,
    });
}
//查看问题模型修改
export function getfindDetailApi(params) {
    return instance_1({
        url: `/myPortal/questionnaire/statistics/findDetail`,
        method: "get",
        params: { modelItemId: params },
    });
}