<template>
  <div>
    <a-col
      :span="spanNum"
      :class="{ overLength: title.length > 5 && spanNum <= 8 }"
    >
      <a-form-model-item
        :label="title"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-date-picker
          :value="value"
          :placeholder="'请选择' + title"
          format="YYYY"
          value-format="YYYY"
          mode="year"
          style="width: 100%"
          :open="openYear"
          @openChange="
            (status) => {
              openYear = status;
            }
          "
          @panelChange="onPanelChange"
        />
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
import moment from "moment";
export default {
  model: {
    prop: "value",
    event: "getYearSelect",
  },
  props: {
    value: {
      type: String,
      required: true,
      default: "",
    },
    title: {
      type: String,
      default: () => "",
      required: true,
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    value: {  
      type: [String, Number],  
      default: ''  
    },
  },
  data() {
    return {
      openYear: false,
    };
  },
  computed: {
    year() {
      return this.value ? moment(this.value, "YYYY") : null;
    },
  },
  methods: {
    onPanelChange(val) {
      const value = moment(val).format("YYYY");
      this.openYear = false;
      this.$emit("getYearSelect", value);
    },
  },
};
</script>

<style lang="scss" scoped></style>
