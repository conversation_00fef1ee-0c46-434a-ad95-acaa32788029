<template>
  <div class="table-container">
    <!-- <a-row style="margin-left:1%">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18 }">
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="届次">
            <a-select placeholder="请选择届次"
                      v-model="queryForm.jcDm"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in getPeriodList"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="行政区划">
            <a-select placeholder="请选择行政区划"
                      v-model="queryForm.xzqhDm"
                      allow-clear
                      style="width: 100%">
              <a-select-option v-for="item in getAdministrativeStateList"
                               :key="item.xzqhDm"
                               :value="item.xzqhDm">{{
                  item.xzqhmc
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="姓名"
                             prop="name">
            <a-input v-model="queryForm.dbxm"
                     autocomplete="off"
                     allow-clear
                     placeholder="请输入姓名"
                     v-on:keyup.enter="fetchData"></a-input>
          </a-form-model-item>
        </a-col>
      </a-form-model>
      <a-col :md="6"
             :sm="24">
        <span style="float: right; margin-top: 3px;">
          <a @click="toggleAdvanced"
             style="margin-right: 8px;">
            {{ advanced ? "收起" : "高级搜索" }}
            <a-icon :type="advanced ? 'up' : 'down'" />
          </a>
          <a-button type="primary"
                    @click="fetchData()">搜索</a-button>
          <a-button style="margin-left: 12px;"
                    @click="reset"
                    class="pinkBoutton">重置</a-button>

        </span>
      </a-col>
    </a-row>
    <a-row v-if="advanced"
           style="margin-left:1%">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18 }">
        <a-col span="6">
          <a-form-model-item style="width: 100%"
                             label="时间范围">
            <a-select placeholder="请选择时间范围"
                      allow-clear
                      style="width: 100%"
                      @change="handleTime">
              <a-select-option v-for="item in timeScope"
                               :key="item.name"
                               :value="item.name">{{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-item label="开始时间"
                       prop="startTime"
                       style="width: 100%">
            <a-date-picker :disabled="showDisabled"
                           v-model="queryForm.startTime"
                           allow-clear
                           value-format="YYYY-MM-DD "
                           placeholder="选择开始时间"
                           style="width: 100%;"
                           :disabled-date="
                (current) =>
                  current && queryForm.endTime
                    ? current.valueOf() >=
                    moment(new Date(queryForm.endTime)).valueOf()
                    : false
              "></a-date-picker>
          </a-form-item>
        </a-col>
        <a-col span="6">
          <a-form-item label="结束时间"
                       prop="endTime"
                       style="width: 100%">
            <a-date-picker v-model="queryForm.endTime"
                           allow-clear
                           :disabled="showDisabled"
                           value-format="YYYY-MM-DD"
                           placeholder="选择结束时间"
                           style="width: 100%;"
                           :disabled-date="
                (current) =>
                  current && queryForm.startTime
                    ? moment(new Date(queryForm.startTime)).valueOf() >=
                    current.valueOf()
                    : false
              "></a-date-picker>
          </a-form-item>
        </a-col>
      </a-form-model>
    </a-row> -->

    <SearchForm @onReset="reset" @onSearch="fetchData">
      <template v-slot:topSearch>
        <SingleSelect :title="'届次'" :selectList="getPeriodList" :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm" />
        <SingleSelect :title="'行政区划'" :selectList="getAdministrativeStateList" :showName="'xzqhmc'" :showValue="'xzqhDm'" :value.sync="queryForm.xzqhDm" />
        <SingleSearch @onEnter="fetchData" :title="'姓名'"  :value.sync="queryForm.dbxm" />
      </template>
      <template v-slot:moreSearch>
        <SingleSelect :title="'时间范围'" :selectList="timeScope" :showName="'name'" :showValue="'name'"  :value.sync="queryForm.timeRange" />
        <StartEndTimeSelect :disabled="showDisabled" :startValue.sync="queryForm.startTime" :endValue.sync="queryForm.endTime" />
      </template>
    </SearchForm>

    <a-row style="margin: 5px 0px 10px 8px;">
      <a-col :span="6">
        <a-button type="primary"
                  style="margin-left: 12px;"
                  @click="download">导出</a-button>
      </a-col>
    </a-row>
    <a-row style="margin-top:20px">
      <standard-table :columns="columns"
                      rowKey="ID"
                      :dataSource="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"
                      @selectedRowChange="onSelectChange"></standard-table>
    </a-row>
  </div>
</template>
<script>
import moment from "moment";
import { myPagination } from "@/mixins/pagination.js";
import {
  declareList,
  getPeriod,
  getSex,
  getNation,
  getPolitical,
  getRecommend,
  Secondary,
  getSynthesize,
  getOccupation,
  getEducationState,
  getAdministrativeState
} from "@/api/election.js";
import StandardTable from "@/components/table/StandardTable";
import { instance_1 } from "@/api/axiosRq";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
import StartEndTimeSelect from '@/components/StartEndTimeSelect/index';

export default {
  // 代表补选结果报表
  components: { 
    SingleSelect,
    StandardTable,
    SearchForm,
    SingleSearch,
    StartEndTimeSelect
 },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      jcDm: '',
      tableKey: [],
      tableData: [],
      onSjmelyDmList: [],
      advanced: false,
      showDisabled: true,
      timeScope: [
        // 议案届别
        { id: 1, name: "本届" },
        { id: 2, name: "最近三个月" },
        { id: 3, name: "今年1~6月" },
        { id: 4, name: "今年7~12月" },
        { id: 5, name: "今年内" },
        { id: 6, name: "自定义" },
      ],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        jcDm: '',/* 届次id */
        dbxm: '',/* 代表姓名 */
        xzqhDm: undefined,/* 行政区划id */
        startTime: '',
        endTime: '',
      },
      columns: [
        {
          title: "姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "USER_NAME",
        },
        {
          title: "性别",
          align: "center",
          width: 90,
          ellipsis: true,
          dataIndex: "SEX",
          customRender: (text, record, index) => {
            // return record.sex == "1" ? '男' : '女'
            return record.SEX || "/"
          }
        },
        {
          title: "出生日期",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "BIRTHDAY",
          customRender: (text, record, index) => {
            // return text.replace("T", " ").split("Z").join("").substr(0, 19);
            return text.slice(0, text.indexOf("T")) || '/';
          }
        },

        {
          title: "工作单位",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "WORK_UNIT",
        },
        {
          title: "补选单位",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "BXDWMC",
        },
        {
          title: "补选日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "SUPPLEMENT_DATE",
          customRender: (text, record, index) => {
            return text.replace("T", " ").split("Z").join("").substr(0, 19)
          }
        },
        {
          title: "补选单位信息",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "QHYXX",
        },
        {
          title: "确认单位信息",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "SHYXX",

        },
      ],
      dataSource: [],
      selectedRows: [],
      getPeriodList: [], /* 获取届次 */
      getSexList: [],/* 获取性别 */
      getNationList: [],/* 获取民族 */
      getPoliticalList: [],/* 获取出党派下拉数据列表 */
      getRecommendList: [],/* 获取出推荐单位下拉数据列表 */
      getAdministrativeStateList: [],/* 行政区划下拉数据 */
      getSynthesizeList: [],/* 获取出综合构成下拉数据列表 */
      getOccupationList: [],/* 获取出职业构成下拉数据列表 */
      getEducationStateList: [],/* 获取在职教育学历、全日制教育学历下拉数据列表 */
      selectData: [ /*  是：1     否：0 */
        { id: '', name: '全部' },
        { id: '1', name: '是' },
        { id: '0', name: '否' }
      ]
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "预备人选管理");
    this.$store.dispatch("navigation/breadcrumb2", "代表补选结果报表");
    this.selectList()
  },
  methods: {
    async selectList () {
      let res = await getPeriod()
      if (res.code == "0000") {
        this.getPeriodList = res.data
        this.queryForm.jcDm = res.data[0].jcDm //改过
        this.jcDm = res.data[0].jcDm
      }
      let res1 = await getSex()
      if (res1.code == "0000") {
        this.getSexList = res1.data
      }
      // let res2=await getNation()
      //   if(res2.code=="0000"){
      //     this.getNationList=res2.data
      //   }
      //  let res3=await getPolitical()
      //   if(res3.code=="0000"){
      //     this.getPoliticalList=res3.data
      //   }
      let res4 = await getRecommend()
      if (res4.code == "0000") {
        this.getRecommendList = res4.data
        this.getRecommendList.unshift({ melyDm: '', melymc: '全部' })
      }
      //  let res5=await getSynthesize()
      //   if(res5.code=="0000"){
      //     this.getSynthesizeList=res5.data
      //   }
      //  let res6=await getOccupation()
      //   if(res6.code=="0000"){
      //     this.getOccupationList=res6.data
      //   }
      //  let res7=await getEducationState()
      //   if(res7.code=="0000"){
      //     this.getEducationStateList=res7.data
      //   }
      let res8 = await getAdministrativeState()
      if (res8.code == "0000") {
        this.getAdministrativeStateList = res8.data
        this.getAdministrativeStateList.unshift({ xzqhmc: '全部', xzqhDm: '' })
      }
      this.fetchData();
    },
    //  主下拉框
    async onSjmelyDm (id) {
      let res = await Secondary({ id })
      if (res.code == "0000") {
        this.onSjmelyDmList = res.data
        this.onSjmelyDmList.unshift({ melyDm: '', melymc: '全部' })
        this.queryForm.melyDm = this.onSjmelyDmList[0].melyDm
      }
    },
    // 获取数据
    fetchData () {
      this.TBloading = true;
      instance_1({
        url: "/representative/replenishResult/findAll",
        method: "POST",
        params: this.queryForm
      }).then((res) => {
        if (res.data.code == '200') {
          this.dataSource = res.data.rows
          this.pagination.total = res.data.total;
          this.TBloading = false;
        }
      })
    },
    // 导出
    download () {
      instance_1({
        url: "/representative/replenishResult/export",
        method: "get",
        responseType: "blob",
        params: {
          jcmc: '',
          jcDm: this.queryForm.jcDm,
          xzqhDm: this.queryForm.xzqhDm,
          dbxm: this.queryForm.dbxm,
          startTime: this.queryForm.startTime,
          endTime: this.queryForm.endTime,
        }
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `代表补选结果报表.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      })
    },
    handleTime (name) {
      var time = new Date();
      let Year = time.getFullYear() /* 当前年份 */
      if (name == "最近三个月") {
        let startTime = time.toLocaleDateString()    /* 当前时间  */
        this.queryForm.startTime = startTime
        time.setMonth(time.getMonth() - 3);
        this.queryForm.endTime = time.toLocaleDateString()
        this.showDisabled = true
      } else if (name == "今年1~6月") {
        this.queryForm.startTime = `${Year}/01/01`
        this.queryForm.endTime = `${Year}/06/30`
        this.showDisabled = true
      }
      else if (name == "今年7~12月") {
        this.queryForm.startTime = `${Year}/07/01`
        this.queryForm.endTime = `${Year}/12/31`
        this.showDisabled = true
      } else if (name == "今年内") {
        this.queryForm.startTime = `${Year}/01/01`
        this.queryForm.endTime = `${Year}/12/31`
        this.showDisabled = true
      } else if (name == "自定义") {
        this.queryForm.startTime = ''
        this.queryForm.endTime = ''
        this.showDisabled = false
      } else if (name == "本届") {
        this.queryForm.startTime = ''
        this.queryForm.endTime = ''
        this.showDisabled = true
      }
    },
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
      }
      this.queryForm.jcDm = this.jcDm
      this.fetchData();
    },
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    onSelectChange (key, data) {
      this.tableKey = key
      this.tableData = data
    }
  },
};
</script>
<style scoped>
.formBox {
  padding: 0 20px;
}
</style>
