<template>

</template>

<script>
import constat from "@/utils/constat"
import { signList, queryData, queryInfo } from "@/api/infoQuery.js";
export default {
  created() {
    let userId = sessionStorage.getItem("myUserId");
    this.handleEdit(userId)
  },
  methods: {
    async handleEdit(userId) {
      //  暂存测试个人信息修改
      let res = await queryData(userId);
      let { jc_dm, sf_dm, db_id } = res.data
      this.$router.push({
        path: "/ediAdjust/editInfo",
        query: {
          oper: constat.EDIT,
          jcDm: jc_dm,
          dbId: db_id,
          sfDm: sf_dm,
        }
      })
    }
  },
}
</script>

<style>
</style>