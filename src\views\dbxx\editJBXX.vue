<template>
  <div class="represen-box table-container">
    <!-- <a-button  style="float: left;z-index: 99;" @click="$router.go(-1)"> 返回</a-button> -->
    <div class="Steps">
      <a-steps :current="1">
        <a-step title="登记" />
        <a-step title="初审" />
        <a-step title="终审" />
        <a-step title="完成修改" />
      </a-steps>
    </div>
    <div class="represen">
      <a-form-model
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-collapse v-model="activeKey" expand-icon-position="right">
          <a-collapse-panel key="k1" header="基本信息" :style="customStyle">
            <a-row :gutter="50">
              <a-col :span="18">
                <a-row type="flex">
                  <a-col :span="6">
                    <a-form-model-item label="届次" class="jiec">
                      <span class="jicson">*</span>
                      <a-select v-model="ruleForm.jcDm" disabled>
                        <a-select-option
                          v-for="item in levelOptions"
                          :key="item.jcDm"
                          :label="item.levelName"
                          :value="item.jcDm"
                          >{{ item.levelName }}</a-select-option
                        >
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.dbzHm == this.ruleForm.dbzHm
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.dbzHm }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="代表证号">
                          <a-input
                            v-model="ruleForm.dbzHm"
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                          >
                          </a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="代表证号"
                    >
                      <a-input
                        v-model="ruleForm.dbzHm"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      ></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.userName == this.ruleForm.userName
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.userName }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="姓名" prop="userName">
                          <a-input
                            v-model="ruleForm.userName"
                            :disabled="oper == constat.VIEW"
                            >>
                          </a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="姓名"
                      prop="userName"
                    >
                      <a-input
                        v-model="ruleForm.userName"
                        :disabled="oper == constat.VIEW"
                        >>
                      </a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.xbDm == this.ruleForm.xbDm
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.xbDm }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="性别">
                          <a-select
                            v-model="ruleForm.xbDm"
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                            placeholder="请选择"
                          >
                            <a-select-option
                              v-for="(item, index) in sex"
                              :key="index"
                              :value="item.xbDm"
                              >{{ item.sex }}
                            </a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="性别"
                    >
                      <a-select
                        v-model="ruleForm.xbDm"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="(item, index) in sex"
                          :key="index"
                          :value="item.xbDm"
                          >{{ item.sex }}
                        </a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.birthday1 == this.ruleForm.birthday
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.birthday }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="出生日期">
                          <a-date-picker
                            v-model="ruleForm.birthday"
                            value-format="YYYY-MM-DD"
                            allow-clear
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                            :disabled-date="disabledDate"
                          />
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="出生日期"
                    >
                      <a-date-picker
                        v-model="ruleForm.birthday"
                        value-format="YYYY-MM-DD"
                        allow-clear
                        :disabled-date="disabledDate"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.mzmc == this.ruleForm.mzDm
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.mzDm }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="民族">
                          <a-select
                            v-model="ruleForm.mzDm"
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                            placeholder="请选择"
                          >
                            <a-select-option
                              v-for="(item, index) in nationality"
                              :key="index"
                              :value="item.mzDm"
                              >{{ item.mzmc }}</a-select-option
                            >
                          </a-select>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="民族"
                    >
                      <a-select
                        v-model="ruleForm.mzDm"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="(item, index) in nationality"
                          :key="index"
                          :value="item.mzDm"
                          >{{ item.mzmc }}</a-select-option
                        >
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.nativePlace == this.ruleForm.nativePlace
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.nativePlace }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="籍贯">
                          <a-input
                            v-model="ruleForm.nativePlace"
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                          >
                          </a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="籍贯"
                    >
                      <a-input
                        v-model="ruleForm.nativePlace"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.zjlxmc == this.ruleForm.zjlxDm
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.zjlxDm }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="证件类型">
                          <a-select
                            v-model="ruleForm.zjlxDm"
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                            placeholder="请选择"
                          >
                            <a-select-option
                              v-for="(item, index) in cardType"
                              :key="index"
                              :value="item.zjlxDm"
                              >{{ item.zjlxmc }}</a-select-option
                            >
                          </a-select>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="证件类型"
                    >
                      <a-select
                        v-model="ruleForm.zjlxDm"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="(item, index) in cardType"
                          :key="index"
                          :value="item.zjlxDm"
                          >{{ item.zjlxmc }}</a-select-option
                        >
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.sfzDm == this.ruleForm.sfzDm
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.sfzDm }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="证件号">
                          <a-input
                            v-model="ruleForm.sfzDm"
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                          >
                          </a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="证件号"
                    >
                      <a-input
                        v-model="ruleForm.sfzDm"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      ></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.zzmmmc == this.ruleForm.zzmmDm
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.zzmmDm }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="党派">
                          <a-select
                            v-model="ruleForm.zzmmDm"
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                            placeholder="请选择"
                          >
                            <a-select-option
                              v-for="(item, index) in politicalAffiliation"
                              :key="index"
                              :value="item.zzmmDm"
                              >{{ item.politicsStatusName }}</a-select-option
                            >
                          </a-select>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="党派"
                    >
                      <a-select
                        v-model="ruleForm.zzmmDm"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="(item, index) in politicalAffiliation"
                          :key="index"
                          :value="item.zzmmDm"
                          >{{ item.politicsStatusName }}</a-select-option
                        >
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.joinTime1 == this.ruleForm.joinTime
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.joinTime }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="加入日期">
                          <a-date-picker
                            v-model="ruleForm.joinTime"
                            value-format="YYYY-MM-DD "
                            allow-clear
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                          />
                          <!-- <a-date-picker
                        :disabled="oper == constat.VIEW"
                        allow-clear
                        v-model="ruleForm.joinTime"
                        value-format="YYYY-MM-DD"
                          ></a-date-picker>-->
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="加入日期"
                    >
                      <a-date-picker
                        v-model="ruleForm.joinTime"
                        value-format="YYYY-MM-DD "
                        allow-clear
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      />
                      <!-- <a-date-picker
                        :disabled="oper == constat.VIEW"
                        allow-clear
                        v-model="ruleForm.joinTime"
                        value-format="YYYY-MM-DD"
                      ></a-date-picker>-->
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.workUnit == this.ruleForm.workUnit
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.workUnit }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="单位及职务">
                          <a-input
                            v-model="ruleForm.workUnit"
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                          >
                          </a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="单位及职务"
                    >
                      <a-input
                        v-model="ruleForm.workUnit"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      ></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.joinWorkTime1 ==
                        this.ruleForm.joinWorkTime
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.joinWorkTime }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="参加工作日期">
                          <a-date-picker
                            v-model="ruleForm.joinWorkTime"
                            value-format="YYYY-MM-DD "
                            allow-clear
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                          />
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="参加工作日期"
                    >
                      <a-date-picker
                        v-model="ruleForm.joinWorkTime"
                        value-format="YYYY-MM-DD "
                        allow-clear
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.duty == this.ruleForm.duty
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.duty }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="(弃用)职位">
                          <a-input
                            v-model="ruleForm.duty"
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                          ></a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="(弃用)职位"
                    >
                      <a-input
                        v-model="ruleForm.duty"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      ></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <div
                      v-if="oper == 'constat.SUBMIT'"
                      id="components-popover-demo-placement"
                      :class="
                        this.submitRrom.dutyName == this.ruleForm.dutyName
                          ? ''
                          : 'nameN'
                      "
                    >
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.dutyName }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="职称">
                          <a-input
                            v-model="ruleForm.dutyName"
                            :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                          >
                          </a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item
                      v-if="oper != 'constat.SUBMIT'"
                      label="职称"
                    >
                      <a-input
                        v-model="ruleForm.dutyName"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      ></a-input>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="4">
                <a-form-model-item label>
                  <div class="userImage">
                    <a-upload
                      v-model="ruleForm.imageUrl"
                      :file-list="isfileList"
                      list-type="picture-card"
                      :action="action"
                      accept="image/*"
                      :before-upload="beforeAvatarUpload"
                      @change="handleAvatarSuccess"
                      @preview="handlePreview"
                    >
                      <img v-if="imageUrl" :src="imageUrl" alt="avatar" />
                      <div v-else-if="isfileList.length < 1">
                        <a-icon :type="loading ? 'loading' : 'plus'" />
                        <div class="ant-upload-text">上传头像</div>
                      </div>
                    </a-upload>
                    <a-modal
                      :visible.sync="dialogVisible"
                      :footer="null"
                      @cancel="
                        () => {
                          dialogVisible = false;
                        }
                      "
                    >
                      <img
                        v-if="imageUrl"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                        :src="imageUrl"
                        alt="avatar"
                      />
                    </a-modal>
                  </div>
                  <!-- <div class="userImage">
                    <img src="../../assets/user.gif"
                         alt />
                  </div>-->
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="k2" header="教育情况" :style="customStyle">
            <a-row type="flex">
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.qrzxlmc == this.ruleForm.qrzjyxlDm
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.qrzjyxlDm }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="全日制教育学历">
                      <a-select
                        v-model="ruleForm.qrzjyxlDm"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="(item, index) in education"
                          :key="index"
                          :value="item.xlDm"
                          >{{ item.xlmc }}</a-select-option
                        >
                      </a-select>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="全日制教育学历"
                >
                  <a-select
                    v-model="ruleForm.qrzjyxlDm"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                    placeholder="请选择"
                  >
                    <a-select-option
                      v-for="(item, index) in education"
                      :key="index"
                      :value="item.xlDm"
                      >{{ item.xlmc }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.qrzxwmc == this.ruleForm.qrzjyxwDm
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.qrzjyxwDm }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="全日制教育学位">
                      <a-select
                        v-model="ruleForm.qrzjyxwDm"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="(item, index) in degree"
                          :key="index"
                          :value="item.xwDm"
                          >{{ item.xwmc }}
                        </a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="全日制教育学位"
                >
                  <a-select
                    v-model="ruleForm.qrzjyxwDm"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                    placeholder="请选择"
                  >
                    <a-select-option
                      v-for="(item, index) in degree"
                      :key="index"
                      :value="item.xwDm"
                      >{{ item.xwmc }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.fullSchool == this.ruleForm.fullSchool
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.fullSchool }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="毕业院校">
                      <a-input
                        v-model="ruleForm.fullSchool"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="毕业院校"
                >
                  <a-input
                    v-model="ruleForm.fullSchool"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.fullMajor == this.ruleForm.fullMajor
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.fullMajor }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="系及专业">
                      <a-input
                        v-model="ruleForm.fullMajor"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="系及专业"
                >
                  <a-input
                    v-model="ruleForm.fullMajor"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.zzxlmc == this.ruleForm.zzjyxlDm
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.zzjyxlDm }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="在职教育学历">
                      <a-select
                        v-model="ruleForm.zzjyxlDm"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="(item, index) in education"
                          :key="index"
                          :value="item.xlDm"
                          >{{ item.xlmc }}</a-select-option
                        >
                      </a-select>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="在职教育学历"
                >
                  <a-select
                    v-model="ruleForm.zzjyxlDm"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                    placeholder="请选择"
                  >
                    <a-select-option
                      v-for="(item, index) in education"
                      :key="index"
                      :value="item.xlDm"
                      >{{ item.xlmc }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.zzxwmc == this.ruleForm.zzjyxwDm
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.zzjyxwDm }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="在职教育学位">
                      <a-select
                        v-model="ruleForm.zzjyxwDm"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="(item, index) in degree"
                          :key="index"
                          :value="item.xwDm"
                          >{{ item.xwmc }}
                        </a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="在职教育学位"
                >
                  <a-select
                    v-model="ruleForm.zzjyxwDm"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                    placeholder="请选择"
                  >
                    <a-select-option
                      v-for="(item, index) in degree"
                      :key="index"
                      :value="item.xwDm"
                      >{{ item.xwmc }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.jobSchool == this.ruleForm.jobSchool
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.jobSchool }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="毕业院校">
                      <a-input
                        v-model="ruleForm.jobSchool"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="毕业院校"
                >
                  <a-input
                    v-model="ruleForm.jobSchool"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.serviceEducation ==
                    this.ruleForm.serviceEducation
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.serviceEducation }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="系及专业">
                      <a-input
                        v-model="ruleForm.serviceEducation"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="系及专业"
                >
                  <a-input
                    v-model="ruleForm.serviceEducation"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  >
                  </a-input>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="k3" header="联系方式" :style="customStyle">
            <a-row type="flex">
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.unitAddress == this.ruleForm.unitAddress
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.unitAddress }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="单位地址">
                      <a-input
                        v-model="ruleForm.unitAddress"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="单位地址"
                >
                  <a-input
                    v-model="ruleForm.unitAddress"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.unitPhone == this.ruleForm.unitPhone
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.unitPhone }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="单位电话">
                      <a-input
                        v-model="ruleForm.unitPhone"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="单位电话"
                >
                  <a-input
                    v-model="ruleForm.unitPhone"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.unitPostalCode ==
                    this.ruleForm.unitPostalCode
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.unitPostalCode }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="单位邮编">
                      <a-input
                        v-model="ruleForm.unitPostalCode"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="单位邮编"
                >
                  <a-input
                    v-model="ruleForm.unitPostalCode"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  >
                  </a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.email == this.ruleForm.email ? '' : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.email }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="电子邮件">
                      <a-input
                        v-model="ruleForm.email"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      ></a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="电子邮件"
                >
                  <a-input
                    v-model="ruleForm.email"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.houseAddress == this.ruleForm.houseAddress
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.houseAddress }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="家庭地址">
                      <a-input
                        v-model="ruleForm.houseAddress"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="家庭地址"
                >
                  <a-input
                    v-model="ruleForm.houseAddress"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.housePhone == this.ruleForm.housePhone
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.housePhone }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="家庭电话">
                      <a-input
                        v-model="ruleForm.housePhone"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="家庭电话"
                >
                  <a-input
                    v-model="ruleForm.housePhone"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.housePostalCode ==
                    this.ruleForm.housePostalCode
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.housePostalCode }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="家庭邮编">
                      <a-input
                        v-model="ruleForm.housePostalCode"
                        :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                      >
                      </a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <a-form-model-item
                  v-if="oper != 'constat.SUBMIT'"
                  label="家庭邮编"
                >
                  <a-input
                    v-model="ruleForm.housePostalCode"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  >
                  </a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <!-- 占位 -->
              </a-col>
              <a-col :span="6">
                <div
                  v-if="oper == 'constat.SUBMIT'"
                  id="components-popover-demo-placement"
                  :class="
                    this.submitRrom.phone == this.ruleForm.onephone
                      ? ''
                      : 'nameN'
                  "
                >
                  <a-popover placement="top">
                    <template slot="content">
                      <div style="display: flex;">
                        <span>旧值：</span>
                        <span>{{ this.submitRrom.phone }}</span>
                      </div>
                    </template>
                    <a-form-model-item label="手机">
                      <!-- prop="onephone" -->
                      <a-input
                        v-model="ruleForm.onephone"
                        :disabled="oper == constat.VIEW"
                      ></a-input>
                    </a-form-model-item>
                  </a-popover>
                </div>
                <!-- prop="onephone"  -->
                <a-form-model-item v-if="oper != 'constat.SUBMIT'" label="手机">
                  <a-input
                    v-model="ruleForm.onephone"
                    :disabled="oper == constat.VIEW"
                    @change="changeonephone"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="手机2">
                  <!-- prop="twoPhone" -->
                  <a-input
                    v-model="ruleForm.twoPhone"
                    :disabled="oper == constat.VIEW"
                    @change="changtwoPhone"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="手机3">
                  <!--  prop="threePhone" -->
                  <a-input
                    v-model="ruleForm.threePhone"
                    :disabled="oper == constat.VIEW"
                    @change="changethreePhone"
                  ></a-input>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="k4" header="选举信息" :style="customStyle">
            <a-row type="flex">
              <a-col :span="6">
                <a-form-model-item label="职业构成" prop>
                  <a-input
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item label="选举单位" prop>
                  <a-input
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  ></a-input>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel
            v-if="oper == constat.VIEW"
            key="k5"
            header="其他"
            :style="customStyle"
          >
            <a-form-model-item :label-col="{ span: 2 }">
              <a-row type="flex">
                <a-col :span="6" :push="4">
                  <a-checkbox
                    v-model="ruleForm.isOverseascn"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                    >是否归侨眷属
                  </a-checkbox>
                </a-col>
                <a-col :span="6" :push="4">
                  <a-checkbox
                    v-model="ruleForm.isPeasant"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                    >是否农民工
                  </a-checkbox>
                </a-col>
                <a-col :span="6" :push="4">
                  <a-checkbox
                    v-model="ruleForm.isCivilServant"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                    >是否公务员
                  </a-checkbox>
                </a-col>
                <a-col :span="6" :push="4">
                  <a-checkbox
                    v-model="ruleForm.isReappointment"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  >
                    是否连任代表</a-checkbox
                  >
                </a-col>
              </a-row>
              <a-row type="flex">
                <a-col :span="6" :push="4">
                  <a-checkbox
                    v-model="ruleForm.isRepresentative"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  >
                    是否同任两级以上代表
                  </a-checkbox>
                </a-col>
                <a-col :span="6" :push="4">
                  <a-checkbox
                    v-model="ruleForm.isUnitLeader"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  >
                    是否事业单位负责人</a-checkbox
                  >
                </a-col>
                <a-col :span="6" :push="4">
                  <a-checkbox
                    v-model="ruleForm.isPartyBranch"
                    :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
                  >
                    是否村委会村党支部组成人员
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-form-model-item>
            <a-form-model-item label="简历" :label-col="{ span: 2 }">
              <a-textarea
                v-model="ruleForm.resume"
                placeholder="简历"
                :rows="4"
                :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
              />
            </a-form-model-item>
            <a-form-model-item label="备注" :label-col="{ span: 2 }">
              <a-textarea
                v-model="ruleForm.remark"
                placeholder="备注"
                :rows="4"
                :disabled="oper == constat.VIEW || oper == 'JBXXXG'"
              />
            </a-form-model-item>
          </a-collapse-panel>
        </a-collapse>
        <a-row v-if="oper != constat.VIEW" type="flex" justify="center">
          <a-form-model-item>
            <a-space v-if="recordName != 'LookSee' && oper != 'JBXXXG'">
              <a-button v-show="isShowInTo" type="primary" @click="lczzjl"
                >查询进度</a-button
              >
              <a-button v-show="!isShowInTo" @click="resetForm('ruleForm')"
                >暂存为草稿</a-button
              >
              <a-button
                v-show="!isShowInTo"
                type="primary"
                @click="submitForm('ruleForm')"
                >保存并送审</a-button
              >
              <a-button v-show="isShowInTo" type="primary" @click="handleAudit"
                >送审</a-button
              >
              <a-button @click="cancel">取消</a-button>
            </a-space>
            <!-- 基本信息修改独有 -->
            <a-space v-else-if="recordName != 'LookSee' && oper == 'JBXXXG'">
              <a-button
                v-show="!isShowInTo"
                type="primary"
                @click="submitForm('ruleForm')"
                >保存并送审</a-button
              >
              <a-button @click="previouspage">取消</a-button>
            </a-space>
            <a-space v-if="recordName == 'LookSee'">
              <a-button v-show="isShowInTo" type="primary" @click="lczzjl"
                >查询进度</a-button
              >
              <a-button @click="cancel">取消</a-button>
            </a-space>
          </a-form-model-item>
        </a-row>
      </a-form-model>
    </div>
    <submitForCensorship
      ref="submitForCensorship"
      :proc-inst-id="procInstId"
      :ids="ids"
      @complete="handleComplete"
    >
    </submitForCensorship>
    <lczzjl ref="lczzjl" :proc-inst-id="procInstId"></lczzjl>
  </div>
</template>

<script>
import { getfindCurrentUserInfoApi } from "@/api/infoQuery.js";
import moment from "moment";
import lczzjl from "@/views/common/lczzjl";
import Vue from "vue";
import { dbleaveuploadApi } from "@/api/representativeElection/candidateApi.js";
import submitForCensorship from "@/views/common/submitForCensorship";
import { checkIdNumberValid } from "@/utils/validate";
import { getInfo, update, getBaseInfo, insertWithWorkFlow } from "@/api/dbxx";
import {
  getpersonalDetailscreateApi,
  getpersonCreatePhoneApi,
  getpersonalDetailscompleteApi,
  getpersonalDetailsfindApi,
  getcommitteeMemberupdateApi,
  getcommitteeMgetByIdApi,
} from "@/api/xjgw";
import { findLevelByJcDm } from "@/api/organization";
import constat from "@/utils/constat";

export default {
  components: { submitForCensorship, lczzjl },
  filters: {},
  data() {
    const checkIdNumberValids = (rule, data, callBack) => {
      if (checkIdNumberValid(data)) {
        callBack();
      } else {
        callBack(new Error("请输入有效身份证件"));
      }
    };
    // 根据证件号校验生日
    var validateDateOfBirth = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请选择出生日期"));
      } else {
        if (this.ruleForm.birthday) {
          let year = this.ruleForm.birthday.slice(6, 10);
          let month = this.ruleForm.birthday.slice(10, 12);
          let day = this.ruleForm.birthday.slice(12, 14);
          // console.log(value);
          // let isYear = value.split("-")[0] == year;
          // let isMonth = value.split("-")[1] == month;
          // let isDay = value.split("-")[2] == day;
          let temp_date = new Date(
            year,
            parseFloat(month) - 1,
            parseFloat(day)
          );
          if (temp_date) {
            callback();
          } else {
            callback(new Error("请选择正确的出生日期"));
          }
        }
      }
    };
    // 根据证件类型校验证件号
    var validateIdNum = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入证件号"));
      } else {
        if (this.ruleForm.zjlxDm === "01") {
          let reg = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
          reg.test(value)
            ? callback()
            : callback(new Error("请输入正确的证件号"));
        }
        if (this.ruleForm.zjlxDm === "02") {
          let reg = /^[\u4E00-\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$/;
          reg.test(value)
            ? callback()
            : callback(new Error("请输入正确的证件号"));
        }
      }
    };
    // 校验手机号
    var validatePhone = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入手机号"));
      } else {
        let reg = /^([1][3,4,5,6,7,8,9])\d{9}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的手机号"));
      }
    };
    //校验单位邮政编码
    var validatenuitPostalCode = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入单位邮编"));
      } else {
        let reg = /^[1-9]\d{5}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的单位邮编"));
      }
    };
    //校验家庭邮政编码
    var validatehouseCode = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入家庭邮编"));
      } else {
        let reg = /^[1-9]\d{5}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的家庭邮编"));
      }
    };
    //校验电子邮件
    var validateemail = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入电子邮件"));
      } else {
        let reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的电子邮件"));
      }
    };
    // 校验单位电话
    var validatenuitPhone = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入单位电话"));
      } else {
        let reg = /^([1][3,4,5,6,7,8,9])\d{9}$/;
        let tel_reg = /^0\d{2,3}-?\d{7,8}$/;
        reg.test(value) || tel_reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的单位电话"));
      }
    };
    return {
      // +
      dialogVisible: false,
      isfileList: [],
      imageUrl: "",
      loading: false,
      action: Vue.prototype.GLOBAL.basePath_1 + "/dbxx/",

      submitRrom: {
        dbzHm: "",
        userName: "",
        xbDm: "",
        birthday: "",
        mzDm: "",
        nativePlace: "",
        zjlxDm: "",
        sfzDm: "",
        zzmmDm: "",
        joinTime: "",
        workUnit: "",
        joinWorkTime: "",
        duty: "",
        dutyName: "",
        qrzjyxlDm: "",
        qrzjyxwDm: "",
        fullSchool: "",
        fullMajor: "",
        zzjyxlDm: "",
        zzjyxwDm: "",
        jobSchool: "",
        serviceEducation: "",
        unitAddress: "",
        unitPhone: "",
        unitPostalCode: "",
        email: "",
        houseAddress: "",
        housePhone: "",
        housePostalCode: "",
        phone: "",
        birthday1: "",
        joinWorkTime1: "",
        joinTime1: "",
        zzxlmc: "",
        zzxwmc: "",
        zzmmmc: "",
        zjlxmc: "",
        qrzxwmc: "",
        qrzxlmc: "",
        mzmc: "",
      },
      isShowInTo: false,
      ismemberId: "",
      procInstId: "",
      ids: [],
      activeKey: ["k1", "k2", "k3", "k4", "k5"],
      customStyle: "background:rgba(190,64,61,0.1);",
      active: 0,
      oper: constat.ADD,
      ruleForm: {},
      sex: [],
      nationality: [],
      cardType: [],
      treeData: [],
      politicalAffiliation: [],
      education: [],
      degree: [],
      rules: {
        dbzHm: [{ required: true, message: "请输入代表证号", trigger: "blur" }],
        userName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        xbDm: [{ required: true, message: "请输入性别", trigger: "change" }],
        birthday: [
          { required: true, trigger: "change", validator: validateDateOfBirth },
        ],
        mzDm: [{ required: true, message: "请输入民族", trigger: "change" }],
        nativePlace: [
          { required: true, message: "请输入籍贯", trigger: "blur" },
        ],
        zjlxDm: [
          { required: true, message: "请输入证件类型", trigger: "change" },
        ],
        sfzDm: [{ required: true, trigger: "blur", validator: validateIdNum }],
        zzmmDm: [{ required: true, message: "请输入党派", trigger: "change" }],
        joinTime: [
          { required: true, message: "请输入加入日期", trigger: "change" },
        ],
        workUnit: [
          { required: true, message: "请输入工作单位", trigger: "blur" },
        ],
        joinWorkTime: [
          { required: true, message: "请输入参加工作日期", trigger: "change" },
        ],
        duty: [{ required: true, message: "请输入职位", trigger: "blur" }],
        dutyName: [{ required: true, message: "请输入职称", trigger: "blur" }],
        qrzjyxlDm: [
          {
            required: true,
            message: "请输入全日制教育学历",
            trigger: "change",
          },
        ],
        qrzjyxwDm: [
          {
            required: true,
            message: "请输入全日制教育学位",
            trigger: "change",
          },
        ],
        fullSchool: [
          { required: true, message: "请输入毕业院校历", trigger: "blur" },
        ],
        fullMajor: [
          { required: true, message: "请输入系及专业", trigger: "blur" },
        ],
        unitAddress: [
          { required: true, message: "请输入单位地址", trigger: "blur" },
        ],
        unitPhone: [
          { required: true, trigger: "blur", validator: validatenuitPhone },
        ],
        unitPostalCode: [
          {
            required: true,
            trigger: "blur",
            validator: validatenuitPostalCode,
          },
        ],
        email: [{ required: true, trigger: "blur", validator: validateemail }],
        houseAddress: [
          { required: true, message: "请输入家庭地址", trigger: "blur" },
        ],
        housePhone: [
          { required: true, message: "请输入家庭电话", trigger: "blur" },
        ],
        housePostalCode: [
          { required: true, trigger: "blur", validator: validatehouseCode },
        ],
        onephone: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],
        isOverseascn: [
          { required: true, message: "请输入选举单位", trigger: "blur" },
        ],
        resume: [
          { required: true, message: "请输入选举单位", trigger: "blur" },
        ],
      },
      constat,
      jcDm: "",
      sfDm: "",
      orgId: "",
      inids: [],
      inprocInstId: "",
      levelOptions: [],
      recordName: "",
    };
  },
  mounted() {},
  created() {
    // console.log(this.$route.query, 'this.$route.query.sfDm');
    this.$store.dispatch("navigation/breadcrumb1", "代表信息");
    this.$store.dispatch("navigation/breadcrumb2", "管理代表");
    this.$store.dispatch("navigation/breadcrumb3", "个人信息修改");
    this.oper = this.$route.query.oper;
    this.recordName = this.$route.query.recordName;
    if (
      this.oper == constat.VIEW ||
      this.oper == constat.EDIT ||
      this.oper == "JBXXXG"
    ) {
      const jcDm = this.$route.query.jcDm;
      const dbId = this.$route.query.dbId;
      const sfDm = this.$route.query.sfDm;
      this.jcDm = jcDm;
      this.sfDm = sfDm;
      this.getInfo(jcDm, dbId, sfDm);
      this.findLevelByJcDm();
    } else if (this.oper == constat.ADD) {
      this.jcDm = this.$route.query.jcDm;
      this.sfDm = this.$route.query.sfDm;
      this.orgId = this.$route.query.orgId;
      this.getBaseInfo();
      this.findLevelByJcDm();
    } else {
      getfindCurrentUserInfoApi().then((res) => {
        // console.log(res, 'res');
        if (res.code == "0000") {
          let { JC_DM, SF_DM, DB_ID, GW_ID } = res.data;
          this.oper = constat.EDIT;
          this.JC_DM = JC_DM;
          this.SF_DM = SF_DM;
          this.DB_ID = DB_ID;
          this.GW_ID = GW_ID;
          const jcDm = this.JC_DM;
          const dbId = this.DB_ID;
          const sfDm = this.SF_DM;
          this.jcDm = jcDm;
          this.sfDm = sfDm;
          this.getInfo(jcDm, dbId, sfDm);
          this.findLevelByJcDm();
          // this.$router.push({
          //   path: "/ediAdjust/myselfInfo",
          //   query: {
          //     oper: constat.EDIT,
          //     jcDm: JC_DM,
          //     dbId: DB_ID,
          //     sfDm: SF_DM,
          //   }
          // })
        }
      });
    }
    if (this.oper == "constat.SUBMIT") {
      const jcDm = this.$route.query.jcDm;
      const dbId = this.$route.query.dbId;
      const sfDm = this.$route.query.sfDm;
      this.subgetInfo(jcDm, dbId, sfDm);
      // this.findLevelByJcDm()
    }
    this.huiXianSons();
    this.getBaseInfo();
    this.findLevelByJcDm();
  },
  methods: {
    changeonephone(e) {
      this.ruleForm.onephone = e.target.value;
      this.$forceUpdate();
    },
    changethreePhone(e) {
      this.ruleForm.threePhone = e.target.value;
      this.$forceUpdate();
    },
    changtwoPhone(e) {
      this.ruleForm.twoPhone = e.target.value;
      this.$forceUpdate();
    },
    //返回上一级
    previouspage() {
      this.$router.go(-1);
    },
    // +
    // 图片预览
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await URL.createObjectURL(file.originFileObj);
      }
      this.imageUrl = file.url || file.preview;
    },
    //上传头像成功回调方法 @change
    handleAvatarSuccess({ file, fileList }) {
      this.isfileList = fileList;
      // console.log(file, "1223");
      var data = new FormData();
      data.append("file", file);
      //   instance_3({
      //   method: "post",
      //   url: "/personalDetails/upload",
      //   headers: {
      //     "Content-Type": "multipart/form-data",
      //   },
      //   data: data,
      // }).
      dbleaveuploadApi(data).then((res) => {
        // console.log(res);
        // // let url = Vue.prototype.GLOBAL.basePath_1 + /hxrdjb/upload;
        //
        if (res.data.code == "0000") {
          this.ruleForm.photograph = res.data.data.relativePath;
          this.imageUrl = "";
          this.$message.success("上传成功");
          // console.log(this.ruleForm);
        }
        // console.log(this.ruleForm.photograph, "this.ruleForm.photograph");
        // // this.$message.success("上传成功");
        // const dataInfo = res.data;
        // let reader = new window.FileReader();
        // // 使用readAsArrayBuffer读取文件, result属性中将包含一个 ArrayBuffer 对象以表示所读取文件的数据
        // reader.readAsArrayBuffer(dataInfo);
        // reader.onload = function (e) {
        //   const result = e.target.result;
        //   let contentType = dataInfo.type;
        //   // 生成blob图片,需要参数(字节数组, 文件类型)
        //   const blob = new Blob([result], { type: contentType });
        //   // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
        //   const url = window.URL.createObjectURL(blob);
        //   console.log(url, 'imageUrl');
        //   console.log(url, 'blob');
        //   this.imageUrl = url;
        // };
      });
      this.loading = false;
    },
    //上传头像图片格式限制
    beforeAvatarUpload(file, fileList) {
      var reg = /image\/(png|jpg|gif|jpeg|webp)$/;
      const isJPG = reg.test(file.type);
      const isLt8M = file.size / 1024 / 1024 < 8;
      if (!isJPG) {
        this.$message.error("文件格式不正确，请上传图片!");
      }
      if (!isLt8M) {
        this.$message.error("上传头像图片大小不能超过 8MB!");
      }
      if (isJPG && isLt8M) {
        return false;
      } else {
        return true;
      }
    },
    //不能选择今天以后的日期
    disabledDate(current) {
      return current && current > moment().endOf("day");
    },
    //查询进度
    lczzjl() {
      this.$refs.lczzjl.visible = true;
      this.$refs.lczzjl.id = this.procInstId;
    },
    //送审
    handleAudit() {
      this.$refs.submitForCensorship.visible = true;
    },
    //回显送审
    huiXianSons() {
      if (this.$route.query.record.ID != "") {
        this.ismemberId = this.$route.query.record.ID;
      }
      // this.ismemberId = this.$route.query.params
      // console.log(this.$route.query, 'this.$route.query');
      if (this.ismemberId != undefined) {
        this.isShowInTo = true;
        // console.log(this.ismemberId);
        // getcommitteeMgetByIdApi(this.ismemberId).then(res => {
        getpersonalDetailsfindApi(this.ismemberId).then((res) => {
          // console.log(res.data.data, 'res.data.data');
          if (res.data.code == "0000") {
            this.ruleForm = res.data.data;
            this.procInstId = res.data.data.procInstId;
            this.ids.push(res.data.data.id);
          }
        });
      } else {
        this.isShowInTo = false;
      }
    },

    //取消
    cancel() {
      this.isShowInTo = "";
      this.$router.push("/delegate/manage");
      if (this.ismemberId != undefined) {
        this.$router.push("/delegate/backlog");
      }
    },
    submitForm(formName) {
      if (this.oper == constat.EDIT || this.oper == "JBXXXG") {
        // console.log(this.oper, 'this.oper');
        // console.log(constat.EDIT, 'constat.EDIT');
        this.handleUpdate();
      }
      // this.$refs[formName].validate((valid) => {
      //   if (valid) {
      //     if (this.oper == constat.EDIT || this.oper == 'JBXXXG') {
      //       // console.log(this.oper, 'this.oper');
      //       // console.log(constat.EDIT, 'constat.EDIT');
      //       this.handleUpdate()
      //     }
      //     if (this.oper == constat.ADD) {
      //       this.handleAdd()
      //     }
      //   } else {
      //     console.log('错误!!');
      //     this.$message.error('请输入完整的信息')
      //     return false;
      //   }
      // });
    },
    resetForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // this.$refs[formName].resetFields();
          let dmSex = {
            xbDm: this.ruleForm.xbDm,
          };
          this.ruleForm.dmSex = dmSex;
          let dqztDm = "11";
          this.ruleForm.dqztDm = dqztDm;
          let zjlx = {
            zjlxDm: this.ruleForm.zjlxDm,
          };
          this.ruleForm.zjlx = zjlx;
          getpersonalDetailscreateApi(this.ruleForm).then((response) => {
            if (response.data.code == "0000") {
              console.log(response.data.data, "response.data");

              this.$baseMessage("已存为草稿", "info");
            }
          });
        } else {
          console.log("错误!!");
          return false;
        }
      });
    },
    getInfo(jcDm, dbId, sfDm) {
      getInfo({ jcDm: jcDm, dbId: dbId, sfDm: sfDm, id: dbId }).then(
        (response) => {
          this.ruleForm = response.data.info;
          this.sex = response.data.sex;
          this.nationality = response.data.nationality;
          this.cardType = response.data.cardType;
          this.politicalAffiliation = response.data.politicalAffiliation;
          this.education = response.data.education;
          this.degree = response.data.degree;
          this.imageUrl = this.action + response.data.info.photograph;
          // console.log(this.imageUrl,'this.imageUrl');
          this.ruleForm.photograph = response.data.info.photograph;
          if (response.data.info.phone.indexOf(",") != "-1") {
            var phonelist = response.data.info.phone.split(",");
            this.ruleForm.onephone = phonelist[0];

            if (phonelist.length > 1) {
              this.ruleForm.twoPhone = phonelist[1];
            }
            if (phonelist.length > 2) {
              this.ruleForm.threePhone = phonelist[2];
            }
            this.$forceUpdate();
            phonelist = [];
          } else {
            this.ruleForm.onephone = response.data.info.phone;
            this.$forceUpdate();
          }
        }
      );
    },
    //
    subgetInfo(jcDm, dbId, sfDm) {
      getInfo({ jcDm: jcDm, dbId: dbId, sfDm: sfDm, id: dbId }).then(
        (response) => {
          // console.log(response, 'jcDm: jcDm, dbId: dbId, sfDm: s');
          this.submitRrom.dbzHm = response.data.info.dbzHm;
          this.submitRrom.userName = response.data.info.userName;
          this.submitRrom.xbDm = response.data.info.xbDm;
          if (this.submitRrom.xbDm == "1") {
            this.submitRrom.xbDm = "男";
          } else {
            this.submitRrom.xbDm = "女";
          }
          // +
          this.imageUrl = this.action + response.data.info.photograph;
          // this.dialogVisible = true;
          //去掉日期时分秒
          var date = response.data.info.birthday;
          this.submitRrom.birthday = /\d{4}-\d{1,2}-\d{1,2}/g
            .exec(date)
            .toString();
          this.submitRrom.birthday1 = response.data.info.birthday;
          this.submitRrom.nativePlace = response.data.info.nativePlace;
          this.submitRrom.sfzDm = response.data.info.sfzDm;
          var dates = response.data.info.joinTime;
          this.submitRrom.joinTime = /\d{4}-\d{1,2}-\d{1,2}/g
            .exec(dates)
            .toString();
          this.submitRrom.joinTime1 = response.data.info.joinTime;
          this.submitRrom.workUnit = response.data.info.workUnit;
          var datess = response.data.info.joinWorkTime;
          this.submitRrom.joinWorkTime = /\d{4}-\d{1,2}-\d{1,2}/g
            .exec(datess)
            .toString();
          this.submitRrom.joinWorkTime1 = response.data.info.joinWorkTime;
          this.submitRrom.duty = response.data.info.duty;
          this.submitRrom.dutyName = response.data.info.dutyName;
          this.submitRrom.fullSchool = response.data.info.fullSchool;
          this.submitRrom.fullMajor = response.data.info.fullMajor;
          this.submitRrom.jobSchool = response.data.info.jobSchool;
          this.submitRrom.serviceEducation =
            response.data.info.serviceEducation;
          this.submitRrom.unitAddress = response.data.info.unitAddress;
          this.submitRrom.unitPhone = response.data.info.unitPhone;
          this.submitRrom.unitPostalCode = response.data.info.unitPostalCode;
          this.submitRrom.email = response.data.info.email;
          this.submitRrom.houseAddress = response.data.info.houseAddress;
          this.submitRrom.housePhone = response.data.info.housePhone;
          this.submitRrom.housePostalCode = response.data.info.housePostalCode;
          this.submitRrom.phone = response.data.info.phone;
          this.submitRrom.zzmmDm = response.data.info.zzmmmc;
          this.submitRrom.zjlxDm = response.data.info.zjlxmc;
          this.submitRrom.mzDm = response.data.info.mzmc;
          this.submitRrom.qrzjyxlDm = response.data.info.qrzxlmc;
          this.submitRrom.qrzjyxwDm = response.data.info.qrzxwmc;
          this.submitRrom.zzjyxlDm = response.data.info.zzxlmc;
          this.submitRrom.zzjyxwDm = response.data.info.zzxwmc;
          this.submitRrom.zzmmmc = response.data.info.zzmmDm;
          this.submitRrom.zjlxmc = response.data.info.zjlxDm;
          this.submitRrom.mzmc = response.data.info.mzDm;
          this.submitRrom.qrzxlmc = response.data.info.qrzjyxlDm;
          this.submitRrom.qrzxwmc = response.data.info.qrzjyxwDm;
          this.submitRrom.zzxlmc = response.data.info.zzjyxlDm;
          this.submitRrom.zzxwmc = response.data.info.zzjyxwDm;
          // this.ruleForm = response.data.info
          // this.sex = response.data.sex
          // this.nationality = response.data.nationality
          // this.cardType = response.data.cardType
          // this.politicalAffiliation = response.data.politicalAffiliation
          // this.education = response.data.education
          // this.degree = response.data.degree
        }
      );
    },
    handleUpdate() {
      console.log(123);
      //保存并送审
      // let currentState = { id: '', currStateDm: "11", currStateName: "草稿", seq: '', isUse: '', isDelete: '' }
      // this.ruleForm.currentState = currentState
      let dmSex = {
        xbDm: this.ruleForm.xbDm,
      };
      this.ruleForm.dmSex = dmSex;
      let dqztDm = "11";
      this.ruleForm.dqztDm = dqztDm;
      let zjlx = {
        zjlxDm: this.ruleForm.zjlxDm,
      };
      this.ruleForm.zjlx = zjlx;
      var data = this.ruleForm;
      if (this.ruleForm.onephone) {
        data.phone = data.onephone;
      }
      if (this.ruleForm.twoPhone) {
        data.phone += "," + data.twoPhone;
      }
      if (this.ruleForm.threePhone) {
        data.phone += "," + data.threePhone;
      }
      //修改手机号
      getpersonCreatePhoneApi(data).then((response) => {
        if (response.data.code == "0000") {
          if (response.data.data == null) {
            this.$router.push({
              path: "/delegate/manage",
            });
          }
          this.procInstId = response.data.data.procInstId;
          this.ids.push(response.data.data.id);
          this.$baseMessage("更新成功", "info");
          this.$refs.submitForCensorship.visible = true;
          // this.$router.push('/delegate/manage')
        }
      });
    },
    //审核保存
    handleComplete(data) {
      getpersonalDetailscompleteApi(data, { tzlx: 0 }).then((res) => {
        // this.ids = []
        // this.$refs.submitForCensorship.successComplete()
        if (res.data.code == "0000") {
          this.$emit("handleClearId");
          this.ids = [];
          this.$router.push("/delegate/Handling");
          this.$refs.submitForCensorship.successComplete();
          this.$message.success(res.data.msg);
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    handleAdd() {
      this.ruleForm.sfDm = this.sfDm;
      this.ruleForm.jcDm = this.jcDm;
      this.ruleForm.orgId = this.orgId;
      let dmSex = {
        xbDm: this.ruleForm.xbDm,
      };
      this.ruleForm.dmSex = dmSex;
      // let currentState = { id: '', currStateDm: "11", currStateName: "草稿", seq: '', isUse: '', isDelete: '' }
      // this.ruleForm.currentState = currentState
      insertWithWorkFlow(this.ruleForm).then((response) => {
        if (response.data == null) {
          this.$router.push({
            path: "/delegate/manage",
          });
        }
        this.procInstId = response.data.procInstId;
        this.ids.push(response.data.id);
        this.$baseMessage("更新成功", "info");
        this.$refs.submitForCensorship.visible = true;
        this.$router.push("/delegate/Handling");
      });
    },
    getBaseInfo() {
      getBaseInfo({ jcDm: this.jcDm }).then((response) => {
        this.sex = response.data.sex;
        this.nationality = response.data.nationality;
        this.cardType = response.data.cardType;
        this.politicalAffiliation = response.data.politicalAffiliation;
        this.education = response.data.education;
        this.degree = response.data.degree;
      });
    },
    findLevelByJcDm() {
      if (this.ismemberId != "") {
        this.jcDm = this.$route.query.record.JC_DM;
      }
      findLevelByJcDm({ jcDm: this.jcDm }).then((response) => {
        this.levelOptions = response.data;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.newClas {
  color: blue;
}

.represen-box {
  width: 100%;

  .Steps {
    width: 80%;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .represen {
    padding: 0 60px;
  }

  .userImage {
    height: 200px;
    width: 150px;

    img {
      height: 200px;
      width: 150px;
    }
  }
}

.jic {
  position: relative;
}

.jicson {
  color: red;
  // font-size: 2px;
  @include add-size($font_size_16);
  position: absolute;
  top: -9px;
  left: -51px;
  z-index: 999;
}

// ::v-deep .ant-popover {
//   line-height: 0 !important;
//   height: 20px !important;
// }
.nameN {
  ::v-deep .ant-form-item label {
    color: red;
  }
}
</style>
<style lang="scss">
::v-deep .ant-popover-placement-top {
  margin-top: 40px;
  color: red !important;
  // div {
  //   span {
  //     line-height: 0 !important;
  //     height: 20px !important;
  //   }
  // }
}
</style>
