import request from "@/utils/request";
import qs from "qs";

export function doSave(data) {
  return request({
    url: "/api/v1/sign/addSign",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/sign/updateSign",
    method: "post",
    data,
  });
}

export function getById(param) {
  return request({
    url: "/api/v1/sign/getSign/" + param,
    method: "post",
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/sign/delSign",
    method: "post",
    data,
  });
}

export function getPage(data) {
  return request({
    url: "/api/v1/sign/getSignList",
    method: "post",
    data,
  });
}

export function getRecordPage(data) {
  return request({
    url: "/api/v1/sign/getSignRecordList",
    method: "post",
    data,
  });
}

export function getList(param) {
  return request({
    url: "/api/v1/sign/getSignCodeList/" + param,
    method: "post",
  });
}
