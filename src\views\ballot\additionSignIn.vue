<template>
    <div>
        <a-modal :visible.sync="visible" width="90%" @cancel="close">
            <!-- <a-button type="primary">
                <a-icon type="search" />
            </a-button>-->
            <div class="Steps">
                <a-steps :current="Steps">
                    <a-step title="录入" />
                    <a-step title="初审" />
                    <a-step title="终审" />
                    <a-step title="补选结果录入完成" />
                </a-steps>
            </div>
            <a-row class="formBox">
                <a-card style="margin-top: 20px;">
                    <a-form-model ref="addForm" :model="addForm" :rules="rules" :label-col="{ span: 8 }"
                        :wrapper-col="{ span: 14 }">
                        <a-tabs default-active-key="1" @change="callback" type="card">
                            <a-tab-pane key="1" tab="补选信息">
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="届次">
                                            <a-select v-model="isaddForm.JC_DM" allow-clear disabled>
                                                <a-select-option v-for="item in periods" :key="item.jcDm"
                                                    :value="item.jcDm">{{ item.levelName }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>

                                    <!-- <a-col span="5">
                                        <a-form-model-item label="姓名" prop="USER_NAME">
                                            <a-input v-model="isaddForm.USER_NAME" allow-clear style="width:200px"
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false">
                                            </a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="1">
                                        <a-button
                                            :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                            style="margin-top: 5px;" type="primary" @click="tiaozhuan">
                                            <a-icon type="search" />
                                        </a-button>
                                    </a-col> -->
                                    <a-col :span="6">
                                        <a-form-model-item label="姓名" prop="USER_NAME">
                                            <div class="searchStyle">
                                                <a-input v-model="isaddForm.USER_NAME" allow-clear
                                                    :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false">
                                                </a-input>
                                                <a-button
                                                    :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                    type="primary" icon="search" @click="tiaozhuan">
                                                </a-button>
                                            </div>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="性别" prop="sex">
                                            <a-select disabled v-model="isaddForm.SEX" allow-clear default-first-option>
                                                <a-select-option key="2" value="2">女</a-select-option>
                                                <a-select-option key="1" value="1">男</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="民族" prop="MZMC">
                                            <a-select disabled v-model="isaddForm.MZMC" allow-clear
                                                default-first-option>
                                                <a-select-option v-for="item in nation" :key="item.xh" :value="item.xh">
                                                    {{ item.mzmc }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col :span="6">
                                        <!-- <a-form-model-item label="工作单位" prop="unit">
                                            <a-input allow-clear v-model="addForm.unit"></a-input>
                                        </a-form-model-item>-->
                                        <a-form-model-item label="工作单位" prop="WORK_UNIT">
                                            <a-input allow-clear v-model="isaddForm.WORK_UNIT" disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col :span="6">
                                        <a-form-model-item label="党派" prop="POLITICS_STATUS_NAME">
                                            <a-select disabled v-model="isaddForm.POLITICS_STATUS_NAME" allow-clear
                                                default-first-option>
                                                <a-select-option v-for="item in parties" :key="item.zzmmDm"
                                                    :value="item.zzmmDm">{{ item.politicsStatusName }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>

                                <span>【补选信息录入】</span>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="补选单位" prop="unit">
                                            <a-select v-model="addForm.unit" allow-clear
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false">
                                                <a-select-option v-for="item in xzqhDmList" :key="item.xzqhDm"
                                                    :value="item.xzqhDm">{{ item.xzqhmc }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6" v-if="addForm.unit != '440100'">
                                        <a-form-model-item label="区届别" prop="distinction">
                                            <a-select v-model="addForm.distinction" allow-clear default-first-option
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false">
                                                <a-select-option v-for="item in distinctionList" :key="item.id"
                                                    :value="item.id">{{ item.name }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6" v-if="addForm.unit == '440100'">
                                        <a-form-model-item label="会议信息" prop="areaName">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.areaName" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6" v-if="addForm.unit != '440100'">
                                        <a-form-model-item label="区会议次别" prop="areaDistinction">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.areaDistinction" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <!-- <a-col span="6" v-if="addForm.unit != '440100'">
                                        <a-form-model-item label="区会议名称" prop="areaName">
                                            <a-select
                                                v-model="addForm.areaName"
                                                allow-clear
                                                default-first-option
                                            >
                                                <a-select-option
                                                    v-for="item in areaNameList"
                                                    :key="item.electedStateDm"
                                                    :value="item.electedStateDm"
                                                >{{ item.electedStateName }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>-->
                                    <a-col span="6">
                                        <a-form-model-item label="区会议日期" prop="areaTime">
                                            <a-date-picker value-format="YYYY-MM-DD HH:mm:ss" show-time
                                                v-model="addForm.areaTime" style="width:100%"
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false">
                                            </a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <!-- <a-col span="6">
                                        <a-form-model-item label="确认单位" prop="confirmUnit">
                                            <a-input v-model="addForm.confirmUnit" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>-->
                                    <a-col span="6">
                                        <a-form-model-item label="市会议次别" prop="cityDistinction">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.cityDistinction" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <!-- <a-col span="6">
                                        <a-form-model-item label="市会议名称" prop="cityName">
                                            <a-select
                                                v-model="addForm.cityName"
                                                allow-clear
                                                default-first-option
                                            >
                                                <a-select-option
                                                    v-for="item in areaNameList"
                                                    :key="item.electedStateDm"
                                                    :value="item.electedStateDm"
                                                >{{ item.electedStateName }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>-->
                                    <!-- <a-col span="6">
                                        <a-form-model-item label="确认日期" prop="cityTime">
                                            <a-date-picker
                                                value-format="YYYY-MM-DD HH:mm:ss"
                                                show-time
                                                v-model="addForm.cityTime"
                                            ></a-date-picker>
                                        </a-form-model-item>
                                    </a-col>-->
                                    <a-col span="6">
                                        <a-form-model-item label="市会议日期" prop="cityTime">
                                            <a-date-picker value-format="YYYY-MM-DD HH:mm:ss" show-time
                                                v-model="addForm.cityTime" style="width:100%"
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false">
                                            </a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <span>【补选投票结果】</span>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="实有人员" prop="personnel">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.personnel" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="到会人员" prop="attend">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.attend" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="发出票数" prop="invoiceNumber">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.invoiceNumber" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="收回票数" prop="recycling">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.recycling" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>

                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="废票数" prop="abandoned">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.abandoned" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="赞成票数" prop="agree">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.agree" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="反对票数" prop="against">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.against" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="弃权票数" prop="waiver">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.waiver" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>

                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="无效票数" prop="failure">
                                            <a-input
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                v-model="addForm.failure" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="补选日期" prop="supplementDate">
                                            <a-date-picker value-format="YYYY-MM-DD HH:mm:ss" show-time
                                                v-model="addForm.supplementDate" style="width:100%"
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false">
                                            </a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="24">
                                        <a-form-model-item prop="results" :label-col="{ span: 2 }"
                                            :wrapper-col="{ span: 22 }">
                                            <a-button
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false"
                                                slot="label" @click="scjg">生成结果</a-button>
                                            <a-textarea v-model="addForm.results" allow-clear rows="6"
                                                :disabled="(Istitle == '补选结果详情' || isRecordIn.ID != undefined) ? true : false">
                                            </a-textarea>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                            </a-tab-pane>
                            <a-tab-pane key="2" tab="补选人资料" force-render>
                                <span>【补选人基本信息】</span>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="姓名" prop="USER_NAME">
                                            <a-input v-model="isaddForm.USER_NAME" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="性别" prop="SEX">
                                            <a-select disabled v-model="isaddForm.SEX" allow-clear default-first-option>
                                                <a-select-option key="2" value="2">女</a-select-option>
                                                <a-select-option key="1" value="1">男</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="民族" prop="MZMC">
                                            <a-select disabled v-model="isaddForm.MZMC" allow-clear
                                                default-first-option>
                                                <a-select-option v-for="item in nation" :key="item.xh" :value="item.xh">
                                                    {{ item.mzmc }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="籍贯" prop="NATIVE_PLACE">
                                            <a-input v-model="isaddForm.NATIVE_PLACE" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="身份证号" prop="SFZ_DM">
                                            <a-input v-model="isaddForm.SFZ_DM" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="出生日期" prop="BIRTHDAY">
                                            <a-date-picker disabled value-format="YYYY-MM-DD" allow-clear
                                                style="width:100%" v-model="isaddForm.BIRTHDAY"></a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="党派" prop="POLITICS_STATUS_NAME">
                                            <a-select disabled v-model="isaddForm.POLITICS_STATUS_NAME" allow-clear
                                                default-first-option>
                                                <a-select-option v-for="item in parties" :key="item.zzmmDm"
                                                    :value="item.zzmmDm">{{ item.politicsStatusName }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="加入日期" prop="JOIN_TIME">
                                            <a-date-picker disabled value-format="YYYY-MM-DD HH:mm:ss" show-time
                                                style="width:100%" v-model="isaddForm.JOIN_TIME"></a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="参加工作日期" prop="JOIN_WORK_TIME">
                                            <a-date-picker disabled value-format="YYYY-MM-DD HH:mm:ss" show-time
                                                style="width:100%" v-model="isaddForm.JOIN_WORK_TIME"></a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="职业构成" prop="ZYGCMC">
                                            <a-input v-model="isaddForm.ZYGCMC" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="名额来源" prop="MELYMC">
                                            <!-- @change="selectGoodsByGroupId($event)" -->
                                            <a-select disabled v-model="isaddForm.MELYMC" allow-clear
                                                default-first-option>
                                                <a-select-option v-for="item in sjmelyDm" :key="item.melyDm"
                                                    :value="item.melyDm">{{ item.melymc }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <span>【教育情况】</span>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="全日制教育" prop="QRZXLMC">
                                            <a-input v-model="isaddForm.QRZXLMC" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="毕业院校" prop="FULL_SCHOOL">
                                            <a-input v-model="isaddForm.FULL_SCHOOL" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="系及专业" prop="FULL_MAJOR">
                                            <a-input v-model="isaddForm.FULL_MAJOR" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="在职教育" prop="ZZJYXLMC">
                                            <a-input v-model="isaddForm.ZZJYXLMC" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="毕业院校" prop="JOB_SCHOOL">
                                            <a-input v-model="isaddForm.JOB_SCHOOL" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="系及专业" prop="SERVICE_EDUCATION">
                                            <a-input v-model="isaddForm.SERVICE_EDUCATION" allow-clear disabled>
                                            </a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="工作单位" prop="WORK_UNIT">
                                            <a-input v-model="isaddForm.WORK_UNIT" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="职位" prop="DUTY">
                                            <a-input v-model="isaddForm.DUTY" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="职称" prop="DUTY_NAME">
                                            <a-input v-model="isaddForm.DUTY_NAME" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <span>联系方式</span>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="单位地址" prop="UNIT_ADDRESS">
                                            <a-input v-model="isaddForm.UNIT_ADDRESS" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="单位电话" prop="UNIT_PHONE">
                                            <a-input v-model="isaddForm.UNIT_PHONE" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="单位邮编" prop="UNIT_POSTAL_CODE">
                                            <a-input v-model="isaddForm.UNIT_POSTAL_CODE" allow-clear disabled>
                                            </a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="电子邮件" prop="EMAIL">
                                            <a-input v-model="isaddForm.EMAIL" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="家庭地址" prop="HOUSE_ADDRESS">
                                            <a-input v-model="isaddForm.HOUSE_ADDRESS" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="家庭电话" prop="HOUSE_PHONE">
                                            <a-input v-model="isaddForm.HOUSE_PHONE" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="家庭邮编" prop="HOUSE_POSTAL_CODE">
                                            <a-input v-model="isaddForm.HOUSE_POSTAL_CODE" allow-clear disabled>
                                            </a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="手机" prop="PHONE">
                                            <a-input v-model="isaddForm.PHONE" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-form-model-item>
                                        <a-col span="6" push="8">
                                            <a-checkbox disabled v-model="isaddForm.IS_RETU_OVER_CHIN" @change="change"
                                                value="IS_RETU_OVER_CHIN" name="IS_RETU_OVER_CHIN">是否归侨侨眷</a-checkbox>
                                        </a-col>
                                        <a-col span="6" push="10">
                                            <a-checkbox disabled v-model="isaddForm.IS_PEAS_WORK" @change="change"
                                                value="IS_PEAS_WORK" name="IS_PEAS_WORK">是否农民工</a-checkbox>
                                        </a-col>
                                        <a-col span="6" push="12">
                                            <a-checkbox disabled v-model="isaddForm.IS_ELEC_REPR" @change="change"
                                                value="IS_ELEC_REPR" name="IS_ELEC_REPR">是否连任代表</a-checkbox>
                                        </a-col>
                                        <a-col span="6" push="14">
                                            <a-checkbox disabled v-model="isaddForm.IS_TWO_OR_MORE_REPR"
                                                @change="change" value="IS_TWO_OR_MORE_REPR" name="IS_TWO_OR_MORE_REPR">
                                                是否同任两级以上代表</a-checkbox>
                                        </a-col>
                                    </a-form-model-item>
                                </a-row>
                                <a-row>
                                    <a-col span="24">
                                        <a-form-model-item label="简历" prop="jl" :label-col="{ span: 2 }"
                                            :wrapper-col="{ span: 22 }">
                                            <a-textarea v-model="isaddForm.RESUME" rows="4" allow-clear disabled>
                                            </a-textarea>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                            </a-tab-pane>
                        </a-tabs>
                    </a-form-model>
                </a-card>
            </a-row>
            <div slot="footer" class="dialog-footer">
                <a-button v-if="isRecordIn.ID == undefined" v-show="Istitle != '补选结果'" @click="draft">暂存为草稿</a-button>
                <a-button type="primary" @click="save" v-show="Istitle != '补选结果'" v-if="isRecordIn.ID == undefined">
                    保存并送审</a-button>
                <a-button type="primary" @click="shenheIn" v-if="isRecordIn.ID != undefined"
                    v-show="Istitle == '补选结果审核'">审核</a-button>
                <!--   v-if="isRecordIn.ID != undefined"   v-show="Istitle == '补选结果审核'"-->
                <!-- <a-button type="primary">送审</a-button> -->
                <a-button @click="quxiao">取消</a-button>
            </div>
        </a-modal>
        <tiaozhuan ref="tiaozhuan" @dataValue="dataValue"></tiaozhuan>
        <submitForCensorship ref="submitForCensorship" :procInstId="procInstId" @complete="handleComplete" :ids="ids">
        </submitForCensorship>
    </div>
</template>

<script>
import tiaozhuan from "@/views/ballot/tiaozhuan"
import submitForCensorship from '@/views/common/submitForCensorship';

import { getbxjgbcompleteApi } from "@/api/xjgw";
import { getLevelListApi, getNationApi, getPoliticalApi, getRecommendApi, getcommongetAddedselecListApi, getAdministrativeStateListApi, registerApi, getbyElectionInfogetByIdApi, getdictCodeElectionStateListApi, getgetAdministrativeRankStateListApi } from "@/api/representativeElection/candidateApi.js"
export default {
    components: { tiaozhuan, submitForCensorship },
    props: {
        isRecord: {
            type: Object,
        },
    },
    data() {
        return {
            Steps: 0,
            xzqhDmid: "",
            Istitle: "",
            isRecordIn: this.isRecord,
            currentState: '',
            isShowis: false,
            newIDs: this.neWid,
            xzqhDmList: {},
            isaddForm: {},
            //会议名称
            // areaNameList: {},
            // 上级名额来源
            sjmelyDm: {
                melyDm: "",
            },
            distinctionList: [
                { id: 1, name: "第一届" },
                { id: 2, name: "第二届" },
                { id: 3, name: "第三届" },
                { id: 4, name: "第四届" },
                { id: 5, name: "第五届" },
                { id: 6, name: "第六届" },
                { id: 7, name: "第七届" },
                { id: 8, name: "第八届" },
                { id: 9, name: "第九届" },
                { id: 10, name: "第十届" },
                { id: 11, name: "第十一届" },
                { id: 12, name: "第十二届" },
                { id: 13, name: "第十三届" },
                { id: 14, name: "第十四届" },
                { id: 15, name: "第十五届" },
                { id: 16, name: "第十六届" },
                { id: 17, name: "第十七届" },
                { id: 18, name: "第十八届" },
                { id: 19, name: "第十九届" },
                { id: 20, name: "第二十届" },
            ],
            areaNameList: [
                { id: 1, name: "常委会会议" },
                { id: 2, name: "人大代表大会" },
            ],
            ids: [],
            procInstId: '',
            //党派
            parties: {},
            //民族
            nation: {},
            //届别
            periods: [],
            visible: false,
            userid: '',
            addForm: {
                jcDm: "2",
                mzDm: "",
                unit: "",
                distinction: "",
                areaDistinction: "",
                areaName: "",
                areaTime: "",
                confirmUnit: "",
                cityDistinction: "",
                cityName: "",
                cityTime: "",
                personnel: "",
                attend: "",
                invoiceNumber: "",
                recycling: "",
                abandoned: "",
                agree: "",
                against: "",
                waiver: "",
                failure: "",
                supplementDate: "",
                results: "",
            },
            // 校验
            rules: {
                unit: [{ required: true, message: "请选择补选单位", trigger: "change" }],
                distinction: [{ required: true, message: "请选择区届别", trigger: "change" }],
                areaDistinction: [{ required: true, message: "区会议次别", trigger: "blur" }],
                areaName: [{ required: true, message: "请选择区会议名称", trigger: "change" }],
                areaTime: [{ required: true, message: "请选择区会议日期", trigger: "change" }],
                // confirmUnit: [{ required: true, message: "请填写确认单位", trigger: "blur" }],
                cityDistinction: [{ required: true, message: "请填写市会议次别", trigger: "blur" }],
                cityName: [{ required: true, message: "请选择市会议名称", trigger: "change" }],
                cityTime: [{ required: true, message: "请选择确认日期", trigger: "change" }],
                personnel: [{ required: true, message: "请填写实有人员", trigger: "blur" }],
                attend: [{ required: true, message: "请填写到会人员", trigger: "blur" }],
                invoiceNumber: [{ required: true, message: "请填写发出票数", trigger: "blur" }],
                recycling: [{ required: true, message: "请填写收回票数", trigger: "blur" }],
                abandoned: [{ required: true, message: "请填写废票数", trigger: "blur" }],
                agree: [{ required: true, message: "请填写赞成票数", trigger: "blur" }],
                against: [{ required: true, message: "请填写反对票数", trigger: "blur" }],
                waiver: [{ required: true, message: "请填写弃权票数", trigger: "blur" }],
                failure: [{ required: true, message: "请填写无效票数", trigger: "blur" }],
                supplementDate: [{ required: true, message: "请选择补选日期", trigger: "change" }],
                results: [{ required: true, message: "请填写补选结果", trigger: "blur" }],

            },
        }
    },
    created() {
        this.getLevelListFn()
        this.getNationfn()
        this.getPoliticalfn()
        this.getRecommendfn()
        // this.getcommongetAddedselecList()
        this.getAdministrativeStateList()
        this.shenhe()
        this.getdictCodeElectionStateList()
    },
    methods: {
        //生成结果
        scjg() {

            let strArr = [
                //区会议日期
                "areaTime",
                //区届别
                "distinction",
                //补选单位
                "unit",
                //区会议名称
                "areaName",
                //会议次别
                "areaDistinction",
                //届次
                // "JC_DM",
                //实有人员
                "personnel",
                //  到会人员
                "attend",
                //发出票数
                "invoiceNumber",
                // 收回票数
                "recycling",
                // 废票数
                "abandoned",
                //赞成票数
                "agree",
                // 反对票数
                "against",
                // 弃权票数
                "waiver",
                // 无效票数
                "failure",
            ];
            let allHas = true;
            strArr.map((item) => {
                this.addForm[item] === undefined || this.addForm[item] === ""
                    ? (allHas = false)
                    : "";
            });
            if (!allHas) return this.$message.error("请完善出缺投票结果信息");
            if (
                parseInt(this.addForm.recycling) !=
                parseInt(this.addForm.agree) +
                parseInt(this.addForm.against) +
                parseInt(this.addForm.waiver) +
                parseInt(this.addForm.failure)
            )
                return this.$message.error(
                    "赞成票数、反对票数、弃权票数、无效票数的总和要等于收回票数"
                );
            if (!this.isaddForm.USER_NAME) return this.$message.error("请选择人员数据");
            this.addForm.results = this.addForm.unit + this.addForm.distinction + this.addForm.areaDistinction + this.addForm.areaName + '于' + this.addForm.areaTime + '选举广州市' + '第十五届 ' + '人民代表大会代表。' + '会议应到代表' + this.addForm.personnel + '人;' + '实到代表' + this.addForm.attend + '人;' + '发出票数' + this.addForm.invoiceNumber + '张;' + '收回选票' + this.addForm.recycling + '张;' + '其中,' + '有效票' + this.addForm.recycling + '张,' + '废票' + this.addForm.abandoned + '张。' + '选举结果,' + '（候选人）得赞成票' + this.addForm.agree + '张,' + '反对票' + this.addForm.against + '张,' + '弃权票数' + this.addForm.waiver + '张,' + '无效票数' + this.addForm.failure + '张,' + '依法当选为广州市第十五届人民代表大会代表。'
            this.$forceUpdate();

        },
        //送审
        shenheIn() {
            this.$refs.submitForCensorship.visible = true;
        },
        //审核
        shenhe() {

            if (this.visible == true) {
                // console.log(this.isRecordIn.JC_DM, 'this.isRecordIn.JC_DM');
                if (this.isRecordIn.ID != undefined) {
                    let dbId = this.isRecordIn.USER_ID
                    let jcDm = this.isRecordIn.JC_DM
                    // console.log(jcDm, dbId);
                    let params = {
                        dbId,
                        jcDm,
                    }
                    getbyElectionInfogetByIdApi(params).then(res => {
                        // console.log(res);
                        console.log(res, 'resd-------ata');
                        if (res.data.code == '0000') {
                            this.addForm = res.data.data.byElection.addedSelect
                            this.addForm.unit = res.data.data.byElection.unit
                            this.addForm.distinction = res.data.data.byElection.distinction
                            this.addForm.areaDistinction = res.data.data.byElection.areaDistinction
                            this.addForm.areaName = res.data.data.byElection.areaName
                            this.addForm.areaTime = res.data.data.byElection.areaTime
                            this.addForm.cityDistinction = res.data.data.byElection.cityDistinction
                            this.addForm.cityName = res.data.data.byElection.cityName
                            this.addForm.cityTime = res.data.data.byElection.cityTime
                            this.addForm.confirmUnit = res.data.data.byElection.confirmUnit
                            this.isaddForm = res.data.data.conditions
                            this.isaddForm.IS_RETU_OVER_CHIN = res.data.data.conditions.IS_RETU_OVER_CHIN
                            this.isaddForm.IS_PEAS_WORK = res.data.data.conditions.IS_PEAS_WORK
                            this.isaddForm.IS_ELEC_REPR = res.data.data.conditions.IS_ELEC_REPR
                            this.isaddForm.IS_TWO_OR_MORE_REPR = res.data.data.conditions.IS_TWO_OR_MORE_REPR
                            this.procInstId = res.data.data.byElection.procInstId
                            this.ids.push(res.data.data.byElection.id)
                            // console.log(this.ids, 'this.procInstIdthis.procInstId');
                            // console.log(this.procInstId, 'this.procInstIdthis.procInstId22222');
                            if (this.currentState == '41') {
                                this.isShowis = false;
                            } else {
                                this.isShowis = true
                            }
                            console.log(res.data.data.byElection.currentState.currStateName, 'jd')
                            switch (res.data.data.byElection.currentState.currStateName) {
                                case '11':
                                    this.Steps = 0
                                    break;
                                case '21':
                                    this.Steps = 2
                                    break;
                                case '31':
                                    this.Steps = 3
                                    break;
                                case '41':
                                    this.Steps = 4
                                    break;
                                case '51':
                                    this.Steps = 4
                                    break;
                            }

                            if (this.isaddForm.IS_RETU_OVER_CHIN == '1') {
                                this.isaddForm.IS_RETU_OVER_CHIN = true
                            } else {
                                this.isaddForm.IS_RETU_OVER_CHIN = false
                            }
                            if (this.isaddForm.IS_PEAS_WORK == '1') {
                                this.isaddForm.IS_PEAS_WORK = true
                            } else {
                                this.isaddForm.IS_PEAS_WORK = false
                            }
                            if (this.isaddForm.IS_ELEC_REPR == '1') {
                                this.isaddForm.IS_ELEC_REPR = true
                            } else {
                                this.isaddForm.IS_ELEC_REPR = false
                            }
                            if (this.isaddForm.IS_TWO_OR_MORE_REPR == '1') {
                                this.isaddForm.IS_TWO_OR_MORE_REPR = true
                            } else {
                                this.isaddForm.IS_TWO_OR_MORE_REPR = false
                            }
                            // this.$message.success(res.data.msg)
                        } else {
                            // this.$message.error(res.data.msg)
                        }
                    })
                }
            }

        },
        //子组件传来的值
        dataValue(val) {
            for (var i = 0; i < val.length; i++) {
                this.isaddForm = val[i]
                this.userid = val[i].DB_ID
                // console.log(this.userid);
                this.isaddForm.IS_RETU_OVER_CHIN = val[i].IS_RETU_OVER_CHIN
                this.isaddForm.IS_PEAS_WORK = val[i].IS_PEAS_WORK
                this.isaddForm.IS_ELEC_REPR = val[i].IS_ELEC_REPR
                this.isaddForm.IS_TWO_OR_MORE_REPR = val[i].IS_TWO_OR_MORE_REPR
            }
            if (this.isaddForm.IS_RETU_OVER_CHIN == '1') {
                this.isaddForm.IS_RETU_OVER_CHIN = true
            } else {
                this.isaddForm.IS_RETU_OVER_CHIN = false
            }
            if (this.isaddForm.IS_PEAS_WORK == '1') {
                this.isaddForm.IS_PEAS_WORK = true
            } else {
                this.isaddForm.IS_PEAS_WORK = false
            }
            if (this.isaddForm.IS_ELEC_REPR == '1') {
                this.isaddForm.IS_ELEC_REPR = true
            } else {
                this.isaddForm.IS_ELEC_REPR = false
            }
            if (this.isaddForm.IS_TWO_OR_MORE_REPR == '1') {
                this.isaddForm.IS_TWO_OR_MORE_REPR = true
            } else {
                this.isaddForm.IS_TWO_OR_MORE_REPR = false
            }

        },
        //跳转
        tiaozhuan() {
            this.$refs.tiaozhuan.visible = true
        },
        change() { },
        //取消
        quxiao() {
            this.Istitle = ''
            this.visible = false
        },
        //清除绑定
        qcbd() {
            this.Steps = 0;
            this.addForm.mzDm = ''
            this.addForm.unit = ''
            this.addForm.distinction = ''
            this.addForm.areaDistinction = ''
            this.addForm.areaName = ''
            this.addForm.areaTime = ''
            this.addForm.confirmUnit = ''
            this.addForm.cityDistinction = ''
            this.addForm.cityName = ''
            this.addForm.ressume = ''
            this.addForm.cityTime = ''
            this.addForm.personnel = ''
            this.addForm.attend = ''
            this.addForm.invoiceNumber = ''
            this.addForm.recycling = ''
            this.addForm.abandoned = ''
            this.addForm.agree = ''
            this.addForm.against = ''
            this.addForm.waiver = ''
            this.addForm.failure = ''
            this.addForm.supplementDate = ''
            this.addForm.results = ''
            this.isaddForm.IS_RETU_OVER_CHIN = false
            this.isaddForm.IS_PEAS_WORK = false
            this.isaddForm.IS_ELEC_REPR = false
            this.isaddForm.IS_TWO_OR_MORE_REPR = false
            this.isaddForm.USER_NAME = ''
            this.isaddForm.SEX = ''
            this.isaddForm.MZMC = ''
            this.isaddForm.NATIVE_PLACE = ''
            this.isaddForm.SFZ_DM = ''
            this.isaddForm.BIRTHDAY = ''
            this.isaddForm.POLITICS_STATUS_NAME = ''
            this.isaddForm.JOIN_TIME = ''
            this.isaddForm.JOIN_WORK_TIME = ''
            this.isaddForm.ZYGCMC = ''
            this.isaddForm.MELYMC = ''
            this.isaddForm.QRZXLMC = ''
            this.isaddForm.FULL_SCHOOL = ''
            this.isaddForm.FULL_MAJOR = ''
            this.isaddForm.ZZJYXLMC = ''
            this.isaddForm.JOB_SCHOOL = ''
            this.isaddForm.SERVICE_EDUCATION = ''
            this.isaddForm.WORK_UNIT = ''
            this.isaddForm.DUTY = ''
            this.isaddForm.DUTY_NAME = ''
            this.isaddForm.UNIT_ADDRESS = ''
            this.isaddForm.UNIT_PHONE = ''
            this.isaddForm.UNIT_POSTAL_CODE = ''
            this.isaddForm.EMAIL = ''
            this.isaddForm.HOUSE_ADDRESS = ''
            this.isaddForm.HOUSE_PHONE = ''
            this.isaddForm.HOUSE_POSTAL_CODE = ''
            this.isaddForm.PHONE = ''
            this.isaddForm.RESUME = ''
        },
        //关闭
        close() {
            // Object.assign(this.$data, this.$options.data());
            this.ids = []
            this.visible = false
            this.Istitle = ''
            if (this.isRecordIn.ID != undefined) {
                this.isaddForm.JC_DM = ''
                this.qcbd()
                this.isRecordIn.ID = ''
                this.currentState = ''
                this.isShowis = false
                this.$refs["addForm"].clearValidate();
                this.$emit('handleClearId')
            }
            this.qcbd()
            this.currentState = ''
            this.isShowis = false
            this.$refs["addForm"].clearValidate();
            this.$emit('handleClearId')
            this.getLevelListFn()
        },
        // tabs框
        callback(e) { },
        //保存并送审
        save() {
            // addForm
            // this.addForm.jcDm = isjcDm
            this.$refs["addForm"].validate(async valid => {
                if (valid) {
                    let userId = this.userid
                    let addedSelect = {
                        userId,
                        jcDm: this.isaddForm.JC_DM,
                        personnel: this.addForm.personnel,
                        attend: this.addForm.attend,
                        invoiceNumber: this.addForm.invoiceNumber,
                        recycling: this.addForm.recycling,
                        abandoned: this.addForm.abandoned,
                        agree: this.addForm.agree,
                        against: this.addForm.against,
                        waiver: this.addForm.waiver,
                        failure: this.addForm.failure,
                        supplementDate: this.addForm.supplementDate,
                        results: this.addForm.results,
                    }
                    let jcDm = {
                        jcDm: this.isaddForm.JC_DM
                    }
                    let currentState = {
                        currStateDm: 11
                    }
                    let params = {
                        userId,
                        jcDm,
                        currentState,
                        mzDm: this.addForm.mzDm,
                        unit: this.addForm.unit,
                        distinction: this.addForm.distinction,
                        areaDistinction: this.addForm.areaDistinction,
                        areaName: this.addForm.areaName,
                        areaTime: this.addForm.areaTime,
                        confirmUnit: this.addForm.confirmUnit,
                        cityDistinction: this.addForm.cityDistinction,
                        cityName: this.addForm.cityName,
                        cityTime: this.addForm.cityTime,
                        addedSelect,
                    }
                    registerApi(params).then(res => {
                        // console.log(res, 77777777777);
                        if (res.data.code == '0000') {
                            this.procInstId = res.data.data.procInstId
                            this.ids.push(res.data.data.id)
                            this.$refs.submitForCensorship.visible = true;
                            this.$message.success(res.data.msg)
                        } else {
                            this.$message.error('补选人已存在')
                        }
                    })
                } else {
                    this.$message.error("校验失败")
                }

            })
        },
        //送审发送
        handleComplete(data) {
            getbxjgbcompleteApi(data).then(res => {
                if (res.data.code == '0000') {
                    this.$emit('handleClearId')
                    this.visible = false
                    this.ids = []
                    this.$refs.submitForCensorship.successComplete()
                    this.$message.success(res.data.msg)
                } else {
                    this.$message.error(res.data.msg)
                }
            })
        },
        //暂存为草稿
        draft() {
            this.$refs["addForm"].validate(async valid => {
                if (valid) {
                    let userId = this.userid
                    let addedSelect = {
                        userId,
                        jcDm: this.addForm.jcDm,
                        personnel: this.addForm.personnel,
                        attend: this.addForm.attend,
                        invoiceNumber: this.addForm.invoiceNumber,
                        recycling: this.addForm.recycling,
                        abandoned: this.addForm.abandoned,
                        agree: this.addForm.agree,
                        against: this.addForm.against,
                        waiver: this.addForm.waiver,
                        failure: this.addForm.failure,
                        supplementDate: this.addForm.supplementDate,
                        results: this.addForm.results,
                    }
                    let jcDm = {
                        jcDm: this.addForm.jcDm
                    }
                    let currentState = {
                        currStateDm: 11
                    }
                    let params = {
                        userId,
                        jcDm,
                        currentState,
                        mzDm: this.addForm.mzDm,
                        unit: this.addForm.unit,
                        distinction: this.addForm.distinction,
                        areaDistinction: this.addForm.areaDistinction,
                        areaName: this.addForm.areaName,
                        areaTime: this.addForm.areaTime,
                        confirmUnit: this.addForm.confirmUnit,
                        cityDistinction: this.addForm.cityDistinction,
                        cityName: this.addForm.cityName,
                        cityTime: this.addForm.cityTime,
                        addedSelect,
                    }
                    registerApi(params).then(res => {
                        // console.log(res, 77777777777);
                        if (res.data.code == '0000') {
                            this.$message.success(res.data.msg)
                        } else {
                            this.$message.error('补选人已存在')
                        }
                    })
                } else {
                    this.$message.error("校验失败")
                }

            })
        },
        //获取当前届次下拉数据列表
        async getLevelListFn() {
            const res = await getLevelListApi()
            if (res.data.code === "0000") {
                this.periods = res.data.data
                // console.log(this.periods[0].jcDm, '00');
                this.addForm.jcDm = this.periods[1].jcDm
            }
        },
        // 民族下拉数据列表
        async getNationfn() {
            let res = await getNationApi()
            if (res.data.code == "0000") {
                this.nation = res.data.data
            }
        },
        // 党派下拉数据列表
        async getPoliticalfn() {
            let res = await getPoliticalApi()
            if (res.data.code == "0000") {
                this.parties = res.data.data
            }
        },
        //根据上级名额来源代码查询名额来源，
        async getRecommendfn() {
            let res = await getRecommendApi()
            if (res.data.code == "0000") {
                this.sjmelyDm = res.data.data
            }
        },
        //获取名称
        // async getcommongetAddedselecList() {
        //     const res = await getcommongetAddedselecListApi()
        //     if (res.data.code === "0000") {
        //         this.areaNameList = res.data.data
        //         // console.log(this.periods[0].jcDm, '00');
        //         // this.addForm.areaName = this.areaNameList[0].areaName
        //         // this.addForm.cityName = this.areaNameList[0].cityName
        //     }
        // },
        //补选单位
        async getAdministrativeStateList() {
            let res = await getgetAdministrativeRankStateListApi()
            // console.log(res, 'resres');
            let xzqhDmListis = { xzqhDm: "440100", xzqhmc: "解放军" }
            if (res.data.code == '0000') {
                this.xzqhDmList = res.data.data
                this.xzqhDmList.unshift(xzqhDmListis)
                this.xzqhDmList.forEach(el => {
                    this.xzqhDmid = el
                })
                // console.log(this.xzqhDmList.xzqhDm, 'this.xzqhDmList');
                // $forceUpdate();
            }
        },
        //获取当选情况下拉数据列表
        async getdictCodeElectionStateList() {
            let res = await getdictCodeElectionStateListApi()
            if (res.data.code == "0000") {
                // console.log(res.data, 'data.....,,,4422');
                this.areaNameList = res.data.data
                this.addForm.areaName = this.areaNameList[0].electedStateDm
                this.addForm.cityName = this.areaNameList[0].electedStateDm
                // 会议名称
                // this.addForm.dxqkDm = this.dangxuan[0].electedStateDm
            }
        },
    },
    //监听父组件传过来的值
    // watch: {
    //     neWid: {
    //         immediate: true,    // 这句重要  立即执行handler里面的方法
    //         handler(val) {
    //             this.newIDs = val
    //             console.log(val);
    //             this.shenhe()
    //         }
    //     }
    // }
    //监听父组件传过来的值
    watch: {
        isRecord: {
            immediate: true,    // 这句重要  立即执行handler里面的方法
            handler(val) {
                this.isRecordIn = val
                this.shenhe()
            }
        },
        // isRecord() {
        // }
    }
}
</script>
 
<style lang = "scss" scoped>
.formBox {
    width: 100%;
    height: 100%;
}

.Steps {
    width: 80%;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 10px;
}
</style>
