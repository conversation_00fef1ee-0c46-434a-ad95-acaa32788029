<template>
    <div style="width: 100%;" class="multi-line-text-item">
      <div class="parent">
        <div class="child-left">
          <div class="child-left-label">
          {{ label }}
          </div>
        </div>
        <div class="child-right">
          {{ values }}
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      label: {
        type: String,
        default: '',
      },
      values: {
        type: String,
        default: '',
      },
    },
    components: {},
    data() {
      return {
  
      };
    },
    created() {
  
    },
    mounted() {
  
    },
    computed: {},
    watch: {},
    methods: {
  
    },
    beforeCreate() {},
    beforeMount() {},
    beforeUpdate() {},
    updated() {},
    beforeDestroy() {},
    destroyed() {},
    activated() {},
  }
  </script>
  
  <style lang='scss' scoped>
    .parent {
      display: flex;
    }
    .child-left {
      flex-wrap: wrap;
      border: 1px solid #e8e8e8;
      border-right: none;
      flex: 1;
      display: flex;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
  
    .child-left-label {
      padding: 5px 10px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fafafa;
      color: rgba(0, 0, 0, 0.85);
      font-weight: normal;
    }
  
    .child-right {
      flex-wrap: wrap;
      border: 1px solid #e8e8e8;
      flex: 5;
      display: inline-block;
      padding: 5px 10px;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      max-height: 60px;
      overflow: hidden;
      overflow-y: auto;
    }
    .multi-line-text-item {
      margin-bottom: 4px; /* 添加间距 */
      border: 1px solid #e8e8e8;
      border-radius: 4px; /* 添加圆角 */
      overflow: hidden; /* 确保圆角生效 */
    }
  </style>