<template>
  <div class=''>
    <a-input-search
      v-model="searchText"
      placeholder="输入搜索关键字"
      style="margin-bottom: 16px"
    />
    <div style="      height: 450px;
      overflow-y: auto;">
      <a-spin :indicator="indicator" :spinning="loading">
        <a-table
          ref="table"
          :columns="columns"
          :data-source="filteredDbList"
          :row-key="
            (record, index) => {
              return index;
            }
          "
          :pagination="false"
        >
        </a-table>
      </a-spin>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dbList: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
  data() {
    return {
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      loading: false,
      searchText: '',
      showLevelColumn: false,
    };
  },
  created() {
    this.checkDomain();
  },
  mounted() {

  },
  computed: {
    filteredDbList() {
      if (this.searchText.trim() === '') {
        return this.dbList;
      }
      return this.dbList.filter(item => {
        return item.name.includes(this.searchText)
      });
    },
    columns() {
      const baseColumns = [
        {
          title: "机构",
          align: "center",
          ellipsis: true,
          dataIndex: "orgName",
          width: 30,
        },
        {
          title: "部门",
          align: "center",
          ellipsis: true,
          dataIndex: "deptName",
          width: 30,
        },
        {
          title: "岗位",
          align: "center",
          ellipsis: true,
          dataIndex: "postName",
          width: 30,
        },
        {
          title: "姓名",
          align: "center",
          // ellipsis: true,
          dataIndex: "userName",
          width: 30,
        },
      ];
      
      // 根据域名条件判断是否展示"届次"列
      if (this.showLevelColumn) {
        baseColumns.push({
          title: "届次",
          align: "center",
          dataIndex: "levelName",
          width: 30,
        });
      }
      return baseColumns;
    },
  },
  watch: {},
  methods: {
    checkDomain() {
      // 获取当前域名
      const currentDomain = window.location.hostname;
      // 根据域名条件决定是否显示"届次"列
      // 这里示例判断条件，请根据实际需求修改
      // 验收的时候测试环境要展示出来
      this.showLevelColumn = currentDomain.includes('rdtest') ||
                            currentDomain.includes('localhost');
    }
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
}
</script>

<style lang='scss' scoped>

</style>