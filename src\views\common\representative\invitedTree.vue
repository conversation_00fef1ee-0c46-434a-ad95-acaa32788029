<template>
  <a-modal
    :title="orgTreeDialogTitle"
    :visible.sync="OrgTreeDialogVisible"
    width="800px"
    :dialog-style="{ top: '20px' }"
    destroy-on-close
    @cancel="close"

  >
    <div style="margin-bottom: 10px;">
      <div style="display: inline-block;">
        <a-upload
          name="file"
          accept=".xls,.xlsx"
          :multiple="true"
          :file-list="fileList"
          :before-upload="beforeUpload"
          @change="importRepresentationList"
        >
            <a-button type="primary">批量用户导入</a-button>
        </a-upload>
      </div>
      <a-button type="primary" style="margin-left: 15px;" :loading="downloadExcelLoading"
        @click="downloadExcel">下载导入模版</a-button>
    </div>

    <a-col span="14" offset="0">
    <a-input-search
      v-model="filterText"
      placeholder="输入关键字进行过滤"
      @search="onChange"
    ></a-input-search>
    </a-col>

      <a-modal
      title="导入结果"
      :visible="dialogVisible"
      @ok="userConfirm"
      @cancel="userClose"
      okText="下一步"
      :width="700"
      >
        <div v-if="isLoading" class="loading-indicator">
          <!-- 这里添加你的 loading 指示器，如一个旋转的图标 -->
          <a-icon type="loading" />  导入中...
        </div>
        <template v-else>
          <a-tabs :default-active-key="importDefaultKey" v-model="importDefaultKey">
            <a-tab-pane key="1" tab="正常用户">
              <a-table
                :columns="columns"
                :data-source="normalUserList"
                :pagination="false"
                :scroll="{ y: 290 }"
              />
            </a-tab-pane>
            <a-tab-pane key="2" tab="重复用户">
              <a-table
                :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                :columns="columns"
                :data-source="repeatUserList"
                :pagination="false"
                :scroll="{ y: 290 }"
              />
            </a-tab-pane>
            <a-tab-pane key="3" tab="不存在用户">
              <a-table
                :columns="columns"
                :data-source="absentUserList"
                :pagination="false"
                :scroll="{ y: 290 }"
              />
            </a-tab-pane>
          </a-tabs>
        </template>
      </a-modal>

      <a-modal
          title="实际导入结果"
          :visible="realityVisible"
          @ok="userRealityConfirm"
          @cancel="userRealityClose"
          :width="700"
          >
            <div class="all_Num">
              <span>总条数: {{ realityCheckList.length }}</span>
            </div>
            <a-table
              :columns="columns"
              :data-source="realityCheckList"
              :pagination="false"
              :scroll="{ y: 290 }"
            />
        </a-modal>
    <a-spin :indicator="indicator" :spinning="spinningShow">
      <a-tree
        ref="orgTree"
        v-model="checkIdentyData"
        checkable
        :selectable="false"
        multiple
        :expanded-keys="expandedKeys"
        :replace-fields="replaceFields"
        :tree-data="orgTreeData"
        @check="onCheck"
        @expand="onExpand"
        @select="onSelect"
        style="overflow-x: hidden;"
      >

        <template slot="title" slot-scope="{ name, orgLevel }">
          <a-icon
            :type="orgLevel == '1' ? 'apartment' : 'file'"
            style="margin-right: 10px;"
          />
          <span
            v-if="searchValue && name.indexOf(searchValue) > -1"
            style="color: #f50;background-color: #f5f5f5;padding-right:32vw;"
            class="name"
            >{{ name }}</span
          >
          <span v-else>{{ name }}</span>
        </template>
      </a-tree>
    </a-spin>
    <span slot="footer" class="dialog-footer">
      <a-button @click="close">取 消</a-button>
      <a-button style="padding-left: 10px;" type="primary" @click="confirm"
        >确 定</a-button
      >
    </span>
  </a-modal>
</template>

<script>
// import { findDbOU } from "@/api/registrationMage/tableIng.js";
import {findDbOrgUserTree, findDbOrgUserTreeOrderByXsbh} from "@/api/dbxx";  //代表展示自定义的树，替换getUserTree
import {importRepresentation, downloadUserImportExcel} from "@/api/communityschedule";
export default {
  props: {
    orgTreeDialogTitle: {
      type: String,
      default: "选择单位",
    },
    jcDm: {
      type: String,
      default: "",
    },
    defaultSelect: {
      type: Array,
      default: [],
    },
    components: {
    },
  },
  data() {
    return {
      dialogVisible: false,
      activeTabName: 'normalUsers',
      normalUserList:[],
      repeatUserList:[],
      absentUserList:[],
      checkedUserList:[],
      confirmUserList:[],
      isLoading: false,
      timer:null,
      downloadExcelLoading: false,
      spinningShow: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      filterText: "",
      name: "",
      userName: [],
      expandedKeys: [],
      searchValue: "",
      allChildData: [],
      selectedKeys: [],
      autoExpandParent: true,
      checked: [],
      checkIdentyData: [],
      orgTreeDialogVisible: false,
      orgTreeData: [],
      OrgTreeDialogVisible: false,
      newOrgTreeDialogVisible: null,
      orgTreeDefaultKey: [],
      name: "",
      replaceFields: {
        title: "name",
        key: "uniqueId",
        children: "children",
      },
      isRadio: true,
      relType: {
        type: Number,
        default: 1,
      },
      columns: [
        {
          title: '序号',
          dataIndex: 'id',
        },
        {
          title: '姓名',
          dataIndex: 'userName',
        },
        {
          title: '岗位名称',
          dataIndex: 'orgName',
        },
      ],
      selectedRowKeys: [],
      fileList: [],
      realityVisible: false,
      realityCheckList: [],
      importDefaultKey: '1',
    };
  },
  watch: {
    newOrgTreeDialogVisible(newVal) {
      if (newVal) {
        this.initOrgTree();
      }
    },
  },
  methods: {
    onSelect(item, data) {
    },
    // 树状搜索
    onChange(e) {
      this.spinningShow = true;
      const value = e;
      this.searchValue = value;
      if (value == "") {
        this.spinningShow = false;
        this.$message.info("请输入关键字");
      } else {
        this.expandedKeys = [];
        this.backupsExpandedKeys = [];
        const candidateKeysList = this.getkeyList(value, this.orgTreeData, []);
        candidateKeysList.forEach((item) => {
          const key = this.getParentKey(item, this.orgTreeData);
          if (key && !this.backupsExpandedKeys.some((item) => item === key))
            this.backupsExpandedKeys.push(key);
        });
        console.log(this.backupsExpandedKeys, "this.backupsExpandedKeys");
        const { length } = this.backupsExpandedKeys;
        for (let i = 0; i < length; i++) {
          this.getAllParentKey(this.backupsExpandedKeys[i], this.orgTreeData);
        }
        this.expandedKeys = this.backupsExpandedKeys.slice();
        console.log(this.expandedKeys, " this.expandedKeys ");
        if (candidateKeysList.length == 0) {
          this.spinningShow = false;

          this.$message.info("没有相关数据");
        }
        this.spinningShow = false;
      }
      setTimeout(() => {
         document.getElementsByClassName('name')[0].scrollIntoView()
      },1000);
    },
    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.name.indexOf(value) > -1) {
          keyList.push(node.id);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey;
      let temp;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          temp = this.getParentKey(key, node.children);
          if (node.children.some((item) => item.id === key)) {
            parentKey = node.id;
          } else if (temp) {
            parentKey = temp;
          }
        }
      }
      return parentKey;
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey;
      if (key) {
        parentKey = this.getParentKey(key, tree);
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey);
          }
          this.getAllParentKey(parentKey, tree);
        }
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    close() {
      this.OrgTreeDialogVisible = false;
      this.newOrgTreeDialogVisible = false;
    },
    initOrgTree() {
      this.OrgTreeDialogVisible = true;
      this.spinningShow = true;
      let params = {
        // sortOrder: sortOrder
      };
      findDbOrgUserTreeOrderByXsbh(params).then((res) => {
        this.orgTreeData = res.data;
        this.spinningShow = false;
        // if (this.defaultSelect.length > 0) {
        //   let arrData = [];
        //   this.defaultSelect.map((item) => {
        //     if (item.children) {
        //       item.children.map((son) => {
        //         arrData.push(son.id);
        //       });
        //     }
        //     if (item.entryType != "1") {
        //       arrData.push(item.id);
        //     }
        //   });
        //   this.checkIdentyData = arrData;
        //   this.checked = this.defaultSelect;
        //   this.$forceUpdate();
        // }
      });
    },

    // 树状选择
    onCheck(item, data) {
      this.checked = [];
      data.checkedNodes.forEach((item) => {
        this.checked.push(item.data.props.dataRef);
      });
    },

    findData(userId, item, key) {
      item.forEach((son, index) => {
        if (son.userId == userId && userId) {
          this.checked[key].children.splice(index, 1);
        } else {
          if (son.children) {
            this.findData(userId, son.children, key);
          }
        }
      });
    },
    confirm() {
      this.OrgTreeDialogVisible = false;
      this.newOrgTreeDialogVisible = false;
      this.$emit("confirm", this.checked);
      this.filterText = "";
    },
    // 上传文件限制 直接return
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xlsx|xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xlsx或.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },
    //导入代表姓名
    importRepresentationList({ file, fileList}) {
      var data = new FormData();
      data.append("file",file);
      importRepresentation(data).then((res) => {
        this.normalUserList=[];
        this.repeatUserList = [];
        this.absentUserList = [];
        let normalIndex = 1;
        let repeatIndex = 1;
        let absentIndex = 1;
        res.data.data.forEach((val, index) => {
          if(val.userType === 0 ){
            // 正常用户
            this.normalUserList.push({
              id: normalIndex++,
              userName: val.userName, //代表姓名
              orgName: val.orgName, //代表岗位名称
              userId: val.userId, //用户Id
              parentId: val.parentId //机构父Id
            });
          } else if (val.userType === 1) {
            // 重复用户
            this.repeatUserList.push({
              id: repeatIndex++,
              userName: val.userName, //代表姓名
              orgName: val.orgName, //代表岗位名称
              userId: val.userId, //用户Id
              parentId: val.parentId //机构父Id
            });
          } else if (val.userType === 2) {
            // 不存在用户
            this.absentUserList.push({
              id: absentIndex++,
              userName: val.userName, //代表姓名
              orgName: val.orgName, //代表岗位名称
              userId: val.userId, //用户Id
              parentId: val.parentId //机构父Id
            });
          }
        });
        if (res.data.code != "0000") {
          return this.$message.error(res.data.msg);
        }
        if (res.data.code == "0000") {
          //判断如果返回列表长度大于0
          if(res.data.data.length > 0 ){
            this.$alert('导入成功','提示',{
              confirmButtonText:'确定',
              callback: action => {
                this.dialogVisible = true;
             }
            });
          } else {
            this.$message.error("没有解析到数据，请检查该Excel文件是否存在数据");
          }
        }
      })
    },
    // 下载
    downloadExcel() {
      this.downloadExcelLoading = true;
      downloadUserImportExcel(this.orgTree).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `批量用户导入模板.xlsx`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadExcelLoading = false;
        }, 2500);
      });
    },
    //选择重复代表用户
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.checkedUserList = selectedRows
    },
    indexMethod(index) {
      return index + 1;
    },
    async userConfirm() {
      this.realityCheckList = [];
      this.confirmUserList = [];
      this.checkedUserList = [
        ...this.normalUserList,
        ...this.checkedUserList,
      ]
      if (this.checkedUserList.length === 0) {
        this.dialogVisible = false;
      } else {
        this.isLoading = true;
        this.checked = [];
        const dbResults = await findDbOrgUserTree(this.jcDm).then(res => res.data.data);
        this.checkedUserList.forEach((userVal, index) => {
          this.findAndPushUserIds(dbResults, userVal, this.confirmUserList, this.realityCheckList);
        });
        this.realityCheckList = this.realityCheckList.map((item, index)=> {
          return {
            id: index + 1,
            ...item,
          }
        })
        this.isLoading = false;
        this.dialogVisible = false;
        this.importDefaultKey = '1';
        this.realityVisible = true;
      }
    },
    async userRealityConfirm() {
      this.$nextTick(() => {
        this.checkIdentyData = this.confirmUserList;
        this.normalUserList = [];
        this.repeatUserList = [];
        this.checkedUserList = [];
        this.selectedRowKeys = [];
        this.absentUserList = [];
        this.realityCheckList = [];
        this.importDefaultKey = '1';
        this.realityVisible = false;
      })
    },
    // 取消功能
    userClose(){
      this.normalUserList = [];
      this.repeatUserList = [];
      this.checkedUserList = [];
      this.absentUserList = [];
      this.selectedRowKeys = [];
      this.realityCheckList = [];
      this.importDefaultKey = '1';
      this.dialogVisible = false;
    },
    userRealityClose() {
      // console.log("-------取消功能--------")
      this.normalUserList = [];
      this.repeatUserList = [];
      this.checkedUserList = [];
      this.absentUserList = [];
      this.selectedRowKeys = [];
      this.importDefaultKey = '1';
      this.realityVisible = false
    },
    // 封装成方法
    findAndPushUserIds(data, userVal, confirmUserList, realityCheckList) {
      data.forEach((val) => {
        this.traverseChildren(val.children, userVal, confirmUserList, realityCheckList);
      });
    },
    // 递归遍历 children 的辅助函数
    traverseChildren(children, userVal, confirmUserList, realityCheckList) {
      if (!Array.isArray(children)) {
        return; // 如果 children 不是数组，直接返回
      }
      children.forEach((cval) => {
        // 递归调用，检查下一层，但不进行匹配判断
        if (cval.children) {
          this.traverseChildren(cval.children, userVal, confirmUserList, realityCheckList);
        }
        // 只有在没有下一层 children 时，才进行匹配判断
        if (!cval.children) {
          if (userVal.userId === cval.userId && userVal.orgId === cval.parentId) {
            if (!confirmUserList.some(item => item === cval.id)) {
              confirmUserList.push(cval.id);
              realityCheckList.push({
                userName: userVal.userName,
                orgName: userVal.orgName,
              })
              this.checked.push(cval);
            }
          }
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-modal-content {
  height: 627.4px;
}
::v-deep .ant-spin-nested-loading {
  margin-top: 35px;
}
.all_Num {
  font-size: 16px;
  font-weight: 700;
  /* color: #d92b3e; */
  text-align: left;
}
.loading-indicator {
  text-align: center;
}
::v-deep .ant-tree {
  max-height: 500px;
  width: 100%;
  overflow-y: auto;
}
::v-deep .ant-modal-body {
  height: 455px;
  overflow: scroll;
}
/* 表格斑马样式 */
::v-deep .ant-table-tbody tr:nth-child(2n) {
  background-color: #fafafa;
}
</style>
