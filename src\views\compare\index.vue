<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <byui-query-form>
          <el-form
            ref="form"
            :model="queryForm"
            :inline="true"
            @submit.native.prevent
          >
            <el-form-item>
              <el-input
                v-model="queryForm.subjectName"
                placeholder="主题名称"
                class="table-input"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="queryForm.dataSourceName"
                placeholder="数据源名称"
                class="table-input"
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="queryForm.hasChange"
                filterable
                placeholder="对比情况"
                class="table-input"
              >
                <el-option
                  v-for="item in hasChangeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                icon="el-icon-search"
                type="primary"
                native-type="submit"
                @click="handleQuery"
                >查询</el-button
              >
            </el-form-item>
          </el-form>
        </byui-query-form>

        <el-table
          ref="collectTable"
          v-loading="listLoading"
          :data="list"
          border
          :element-loading-text="elementLoadingText"
          @selection-change="setSelectRows"
          @sort-change="tableSortChange"
        >
          <el-table-column label="序号" width="95">
            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column
            label="主题名称"
            prop="subjectName"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            label="对比更新时间"
            prop="compareUpdateTime"
            :formatter="dateFormat"
          ></el-table-column>
          <el-table-column label="新增数量" prop="compareNew"></el-table-column>
          <el-table-column
            label="更新数量"
            prop="compareModify"
          ></el-table-column>
          <el-table-column
            label="删除数量"
            prop="compareRemove"
          ></el-table-column>
          <el-table-column
            label="数据周期"
            prop="compareTimeDesc"
          ></el-table-column>
          <el-table-column
            label="数据源名称"
            prop="dataSourceName"
          ></el-table-column>
          <el-table-column
            label="数据类型"
            prop="sourceDataType.desc"
          ></el-table-column>
        </el-table>
        <el-pagination
          :background="background"
          :current-page="queryForm.page.page"
          :layout="layout"
          :page-size="queryForm.page.rows"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        ></el-pagination>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { getPage } from "@/api/compare";
import moment from "moment";
export default {
  name: "compareList",
  filters: {},
  data() {
    return {
      imgShow: true,
      list: [],
      listLoading: true,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      queryForm: {
        page: {
          page: 1,
          rows: 10,
        },
        sourceOrgId: null,
        sourceOrgName: "",
        hasNew: null,
        hasModify: null,
        hasRemove: null,
        hasChange: null,
      },
      hasChangeOptions: [
        {
          value: 0,
          label: "没有更新",
        },
        {
          value: 1,
          label: "有更新",
        },
      ],
    };
  },
  created() {
    this.fetchData();
  },
  beforeDestroy() {},
  mounted() {},
  methods: {
    tableSortChange(column) {},
    setSelectRows(val) {
      this.selectRows = val;
    },
    handleSizeChange(val) {
      this.queryForm.page.rows = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryForm.page.page = val;
      this.fetchData();
    },
    handleQuery() {
      this.queryForm.page.page = 1;
      this.fetchData();
    },
    fetchData() {
      this.listLoading = true;
      getPage(this.queryForm).then((res) => {
        this.list = res.data.records;
        this.total = res.data.total;
        setTimeout(() => {
          this.listLoading = false;
        }, 500);
      });
    },

    openOrgTreeDialog() {
      let keys = [];
      if (this.queryForm.sourceOrgId) {
        keys = [this.queryForm.sourceOrgId];
      }
      this.$refs.orgTreeDialog.open(keys);
    },
    confirmOrg(checkedNodes) {
      if (checkedNodes.length != 1) {
        this.queryForm.sourceOrgId = null;
        this.queryForm.sourceOrgName = "";
      } else {
        let data = checkedNodes[0];
        this.queryForm.sourceOrgId = data.orgId;
        this.queryForm.sourceOrgName = data.orgName;
      }
    },
    dateFormat(row, column, cellValue, index) {
      var date = row[column.property];
      if (date == undefined) {
        return "";
      }
      return moment(date).format("YYYY-MM-DD");
    },
  },
};
</script>
