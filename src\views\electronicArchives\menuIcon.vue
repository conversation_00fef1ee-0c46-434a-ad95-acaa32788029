<template>
  <div>
    <div class="TYcss">
      <!-- 大会期间参加会议 -->
      <div class="jbxx-contt">
        <div class="jbqkss"><span class="jbqkbz">大会期间参加会议</span></div>
        <div style="padding-bottom: 20px;">
          <a-row>
            <a-col v-for="(item, index) in meetingList"
                   :key="index"
                   :span="4"
                   @click="skiUrl(item.id, item.title, item.type, item.code)">
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.url"
                     :alt="item.title" />
                <div class="jbxxson_box">
                  <div class="tits"
                       v-html="item.title"
                       style="">{{ item.title }}</div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
  </div>
</template> 
<script>
import "@/styles/StatisticalReport/StatisticalReport.scss";
export default {
  name: "WorkIndex",
  components: {},
  data () {
    return {
      TBloading: false,
      meetingList: [{
        id: 1,
        title: "参加<br/>市人民代表大会",
        code: 1,
        color: "#be403d",
        type: "会议",
        url: require("@/assets/images/StatisticalReport/履职登记管理-登记管理-登记活动/参加市人民代表大会.png"),
      },],
    };
  },
  created () {
  },
  methods: {
    // @!@!@
    skiUrl (id, title, textBox, code) {
      this.$router.push({
        path: "/delegate/addRegister",
        query: { areaId: id, title: title, textBox: textBox, code: code },
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.register-box {
  padding: 20px;
  .titleBox {
    margin-top: 20px;
    font-weight: 700;
    padding-left: 15px;
    border-left: 4px solid red;
  }
  .register-fon {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    .register-item {
      cursor: pointer;
      margin-top: 20px;
      margin-left: 20px;
      border: 1px solid #ccc;
      height: 80px;
      width: 216px;
      padding: 0 10px;
      font-weight: 700;
      border-radius: 6px;
      // line-height:80px;
      display: flex;
      .img {
        width: 60px;
        height: 60px;
        line-height: 75px;
        img {
          width: 60px;
          height: 60px;
        }
      }
    }
  }
}
</style>
