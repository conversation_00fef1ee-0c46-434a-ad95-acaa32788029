<template>
  <div style="padding: 20px">
    <a-form-model
      ref="ruleForm"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-model-item ref="ssxzq" label="行政区划" required prop="ssxzq">
        <a-select
          v-model="form.ssxzq"
          placeholder="请选择行政区域"
          allow-clear
          @change="handleAdministrativeAreaChange"
        >
          <a-select-option
            v-for="item in administrativeAreas"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            >{{ item.name }}</a-select-option
          >
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="乡镇街道" required prop="xzjd">
        <a-select
          v-model="form.xzjd"
          placeholder="请选择乡镇街道"
          allow-clear
          show-search
        >
          <a-select-option
            v-for="item in streetTowns"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            >{{ item.name }}</a-select-option
          >
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="代表姓名" prop="userName">
        <a-input
          v-model="form.userName"
        />
      </a-form-model-item>
      <a-form-model-item label="性别" prop="sex">
        <a-input
          v-model="form.sex"
        />
      </a-form-model-item>
      <a-form-model-item label="单位及职务" prop="unit">
        <a-input
          v-model="form.unit"
        />
      </a-form-model-item>
      <a-form-model-item label="民族" prop="nation">
        <a-input
          v-model="form.nation"
        />
      </a-form-model-item>
      <a-form-model-item label="电话号码" prop="mobile">
        <a-input
          v-model="form.mobile"
        />
      </a-form-model-item>
      <a-form-model-item label="头像" prop="photo">
        <a-upload
          name="avatar"
          list-type="picture-card"
          class="avatar-uploader"
          :show-upload-list="false"
          action="#"
          accept="image/*"
          :before-upload="() => { return false }"
          @change="handleChange"
        >
          <img v-if="form.photo" width="200" :src="getImageUrl(form.photo)" alt="avatar" />
          <div v-else>
            <a-icon :type="loading ? 'loading' : 'plus'" />
            <div class="ant-upload-text">上传</div>
          </div>
        </a-upload>
      </a-form-model-item>
      <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
        <a-button type="primary" @click="onSubmit"> 保  存 </a-button>
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<script>
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
import { getById, edit } from "@/api/dbdistrict/db.js";
import { dbleaveuploadApi } from "@/api/representativeElection/candidateApi.js";
import Vue from "vue";
export default {
  data() {
    return {
      recordId: "",
      //行政区域列表
      administrativeAreas: [],
      streetTowns: [],
      action: Vue.prototype.GLOBAL.basePath_1 + "/dbxx",
      loading: false,
      imageUrl: "",
      labelCol: { span: 3 },
      wrapperCol: { span: 14 },
      other: "",
      form: {
        userName: "",
        ssxzq: ""
      },
      rules: {
        ssxzq: [
          {
            required: true,
            message: "请选择行政区划",
            trigger: "blur",
          },
        ],
        xzjd: [
          {
            required: true,
            message: "请选择乡镇街道",
            trigger: "blur",
          },
        ],
        userName: [
          {
            required: true,
            message: "请输入用户名称",
            trigger: "blur",
          },
        ],
        sex: [
          {
            required: true,
            message: "请输入性别",
            trigger: "blur",
          },
        ],
        unit: [
          {
            required: true,
            message: "请输入单位及职务",
            trigger: "blur",
          },
        ],
        nation: [
          {
            required: true,
            message: "请输入籍贯",
            trigger: "blur",
          },
        ],
        mobile: [
          {
            required: true,
            message: "请输入电话号码",
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {
    let { id } = this.$route.query;
    this.recordId = id;
    this.getById()
    this.listAdministrativeAreas();
  },
  methods: {
    getById() {
      getById({ id : this.recordId }).then(res => {
        this.form = res.data
        this.liststreetTowns(this.form.ssxzq)
      })
    },
    getImageUrl(url) {
      return this.action + url
    },
    listAdministrativeAreas() {
      getAdministrativeAreas().then((response) => {
        this.administrativeAreas = response.data;
      });
    },
    // 获取乡镇街道
    liststreetTowns(administrativeAreaId) {
      getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
        }
      );
    },
    handleAdministrativeAreaChange(val) {
      if (!val) return;
      this.form.xzjd = undefined;
      this.liststreetTowns(val);
    },
    onSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          edit(this.form).then(res => {
            if(res.code == '0000') {
              this.$message.success("修改成功");
            } else {
              this.$message.success("修改失败");
            }
            this.getById()
          })
        } else {
          this.$message.error("请完善代表信息资料");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },
    handleChange({ file, fileList }) {
      if(this.beforeUpload(file)) {
        var data = new FormData();
        data.append("file", file);
        dbleaveuploadApi(data).then((res) => {
          if (res.data.code == "0000") {
            this.form.photo = "/" + res.data.data.relativePath;
            this.$message.success("上传成功");
          }
        });
      }
      this.loading = false;
    },
    beforeUpload(file) {
      const isJpgOrPng =
        file.type === "image/jpeg" || file.type === "image/png";
      if (!isJpgOrPng) {
        this.$message.error("请上传jpeg或者png格式图片");
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error("上传大小限制2M!");
      }
      return isJpgOrPng && isLt2M;
    },
  },
};
</script>

<style scoped>
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
