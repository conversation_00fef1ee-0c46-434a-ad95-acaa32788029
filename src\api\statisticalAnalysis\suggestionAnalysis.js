import { instance_yajy } from "@/api/axiosRq";
// 建议分类统计报表
export function getFenLei(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/fenlei`,
    method: "post",
    params: queryForm,
  });
}
// 承办单位办理统计
export function zhuzhuangtu(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/zhuzhuangtu`,
    method: "post",
    params: queryForm,
  });
}
// 建议办理评分表-列表
export function pingfenbiao(meeting) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/pingfenbiao`,
    method: "post",
    params: { meeting },
  });
}
// 建议办理评分表-导出
export function exportPingfenbiao(meeting) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportPingfenbiao`,
    method: "post",
    responseType: "blob",
    params: { meeting },
  });
}
// 承办单位答复统计 列表
export function cbdw_df(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/cbdw_df`,
    method: "post",
    params: form,
  });
}
// 签收一览表 列表
export function qs_list(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/qs_list`,
    method: "post",
    params: form,
  });
}
// 答复一览表 列表
export function df_list(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/df_list`,
    method: "post",
    params: form,
  });
}
// 移动端反馈联名清单 列表
export function fklm_list(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/fklm_list`,
    method: "post",
    params: form,
  });
}
// 交办明细 列表
export function jbmx_list(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/jbmx_list`,
    method: "post",
    params: form,
  });
}
// 交办明细 导出
export function exportJbmx(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportJbmx`,
    method: "post",
    responseType: "blob",
    params: form,
  });
}
// 代表反馈时间 列表
export function dbfksj_list(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/dbfksj_list`,
    method: "post",
    params: form,
  });
}
// 代表反馈时间 导出
export function exportDbfksj(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportDbfksj`,
    method: "post",
    responseType: "blob",
    params: form,
  });
}
// 代表所提建议清单 列表
export function dbstya_list(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/dbstya_list`,
    method: "post",
    data: form,
  });
}
// 代表所提建议清单 导出
export function exportDbstya(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportDbstya`,
    method: "post",
    responseType: "blob",
    data: form,
  });
}
// 答复分类一览表 列表
export function dffl_list(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/dffl_list`,
    method: "post",
    params: form,
  });
}
// 交办清单 列表
export function jbqd_list(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/jbqd_list`,
    method: "post",
    params: form,
  });
}
// 交办清单 导出
export function exportJbqd(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportJbqd`,
    method: "post",
    responseType: "blob",
    params: form,
  });
}
// 办理情况 列表
export function blqk_list(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/blqk_list`,
    method: "post",
    params: form,
  });
}
// 办理情况 导出
export function exportBlqk(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportBlqk`,
    method: "post",
    responseType: "blob",
    params: form,
  });
}

// 建议来源统计表 列表
export function jyly_list(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/jyly_list`,
    method: "post",
    params: form,
  });
}

// 沟通方式及反馈统计 列表
export function goutongfangshi(meeting) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/goutongfangshi`,
    method: "post",
    params: { meeting },
  });
}
// 沟通方式及反馈统计 导出
export function exportGoutongfangshi(meeting) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportGoutongfangshi`,
    method: "post",
    responseType: "blob",
    params: { meeting },
  });
}
// 网上公开 列表
export function openList(data) {
  return instance_yajy({
    url: "/api/v1/appraise/formDetail/queryOnlineListPage",
    method: "post",
    data,
  });
}
//网上公开列表数据导出
export function onlineExportExcel(form) {
  return instance_yajy({
    url: `/api/v1/appraise/formDetail/onlineExportExcel`,
    method: "post",
    responseType: "blob",
    params: form.params,
    data: form.data,
  });
}
// 网上公开 获取数据
export function openListSaveOrUpdate(form) {
  return instance_yajy({
    url: "/api/v1/appraise/formDetail/saveOrUpdate",
    method: "post",
    data: form,
  });
}
// 评分排名表 列表
export function rankingList(data) {
  return instance_yajy({
    url: "/api/v1/appraise/rank/queryListPage",
    method: "post",
    data,
  });
}
// 评分排名表 导出编办考核单位/导出Excel 入参不同
export function exportExcel(data) {
  return instance_yajy({
    url: "/api/v1/appraise/rank/exportExcel",
    method: "post",
    responseType: "blob",
    data,
  });
}
// 评分排名表 获取排名表
export function updateOrgRank(form) {
  return instance_yajy({
    url: "/api/v1/appraise/rank/updateOrgRank",
    method: "post",
    params: form,
  });
}
//导出承办单位答复统计 导出全部excel
export function exportCbdwdf(form) {
  return instance_yajy({
    url: "/api/v1/proposal/suggestion/exportCbdwdf",
    method: "post",
    responseType: "blob",
    params: form,
  });
}
//承办单位办理统计 导出全部excel
export function export_zhuzhuangtu(form) {
  return instance_yajy({
    url: "/api/v1/proposal/suggestion/export_zhuzhuangtu",
    method: "post",
    responseType: "blob",
    params: form,
  });
}
//承办单位办理统计 导出全部excel
export function exportStatisticalSource(form) {
  return instance_yajy({
    url: "/api/v1/proposal/suggestion/exportStatisticalSource",
    method: "post",
    responseType: "blob",
    params: form,
  });
}


