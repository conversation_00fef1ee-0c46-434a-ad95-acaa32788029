import { instance_yajy } from "@/api/axiosRq";

// 议案建议数量统计
// queryForm 请求搜索数据
export function proposalNumStatisticsList(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/yajysltj`,
    method: "post",
    params: queryForm,
  });
}
// 导出excel
export function proposalNumStatisticsExportExcel(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportYajysltj`,
    method: "post",
    params: queryForm,
    responseType: "blob",
  });
}
