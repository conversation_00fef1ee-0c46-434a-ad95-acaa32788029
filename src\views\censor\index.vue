<template>
  <div class="app-container table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <a-row style="margin-left:1%">
          <a-col >
            <a-form-model ref="queryForm" :model="queryParams" layout="inline"   
            :labelCol="{ span: 6 }"
                      :wrapperCol="{ span: 18, offset: 0 }" >
              <a-row>
                <a-col :span="6">

                  <a-form-model-item label="关键字" prop="keyword"  style="width: 100%;">
                    <a-input v-model="queryParams.keyword" placeholder="请输入关键字" 
                    allow-clear style="width: 100%;"
                      @keyup.enter.native="handleQuery" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="6" style="float:right">
                  <span style="float:right;margin-top:3px">
                    <a-button type="primary" @click="handleQuery" style="margin-left: 12px;">搜索</a-button>
                    <a-button @click="resetQuery" style="margin-left: 12px;" class="pinkBoutton">重置</a-button>
                  </span>
                </a-col>
              </a-row>

            </a-form-model>
          </a-col>
        </a-row>
        <a-row style="margin: 5px 0px 10px 8px;">
          <a-col :span="12">
            <a-button type="primary" native-type="submit" @click="handleAdd">新增</a-button>
          </a-col>
        </a-row>
        <!-- <byui-query-form>
              <span style="float:left">
                  <a-button 
                      type="primary"
                      native-type="submit"
                      @click="handleAdd">新增</a-button>
            </span>
          <byui-query-form-right-panel :span="24">  
        <a-form-model ref="queryForm"
                      :model="queryParams"
                      layout="inline"
                     >
          <a-form-model-item label="关键字"
                             prop="keyword">
            <a-input v-model="queryParams.keyword"
                     placeholder="请输入关键字"
                     allow-clear
                      style="width: 200px;"
                     @keyup.enter.native="handleQuery" />
          </a-form-model-item>
          <a-form-item>
            <a-button type="primary" 
                      @click="handleQuery"
                      style="margin-left: 12px;">搜索</a-button>
            <a-button  
                      @click="resetQuery"
                      style="margin-left: 12px;" class="pinkBoutton" >重置</a-button>
         
          </a-form-item>
        </a-form-model>
          </byui-query-form-right-panel>
        </byui-query-form> -->

      </a-col>
    </a-row>

    <a-spin :indicator="indicator" :spinning="loading">
      <a-table class="directorySet-table" ref="table" size="small" :columns="columns" :pagination="pagination"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
        }" :data-source="censorList" :rowKey="
  (record, index) => {
    return index;
  }
" :scroll="{ x: 300, y: 0 }"></a-table>
    </a-spin>
    <!--
    <el-table
      v-loading="loading"
      :data="censorList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column label="关键字" align="center" prop="keyword" />
      <el-table-column
        label="创建人名称"
        align="center"
        prop="createUserName"
      />

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <a-button type="text" icon="edit" @click="handleUpdate(scope.row)"
            >修改</a-button
          >
          <a-button type="text" icon="delete" @click="handleDelete(scope.row)"
            >删除</a-button
          >
        </template>
      </el-table-column>
    </el-table> -->

    <!-- 添加或修改文本审核（敏感词管理）对话框 -->
    <a-modal :title="title" :visible.sync="open" width="40%" append-to-body @cancel="cancel">
      <a-form-model ref="form" :rule="rule" :model="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="关键字" prop="keyword">
          <a-input v-model="form.keyword" placeholder="请输入关键字" />
        </a-form-model-item>
      </a-form-model>
      <div slot="footer" class="dialog-footer">
        <a-button type="primary" @click="submitForm">确 定</a-button>
        <a-button @click="cancel">取 消</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { instance_2 } from "@/api/axiosRq";
import {
  listCensor,
  getCensor,
  delCensor,
  addCensor,
  updateCensor,
  exportCensor,
} from "@/api/censor";

export default {
  name: "Censor",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文本审核（敏感词管理）表格数据
      censorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 校验规则
      rule: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // qzhdTextCensor:{
        //      keyword: null,
        // },
        keyword: null,
        // deleted: null,
        // createPosId: null,
        // createUserName: null,
        // deleteTime: null,
        // deletePosId: null,
        // deleteUserName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      background: true,
      layout: "total, sizes, prev, pager, next, jumper",
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,

      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "关键字",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "keyword",
          customRender: (text, record, index) => {
            return text;
          },
        },
        {
          title: "创建人名称",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "createUserName",
          customRender: (text, record, index) => {
            return text;
          },
        },
        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 260,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleUpdate(record);
                    },
                  },
                },
                "修改"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created() {
    this.getList();
    this.$store.dispatch("navigation/breadcrumb1", "常用服务");
    this.$store.dispatch("navigation/breadcrumb2", "敏感词过滤");
  },
  methods: {
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryParams.pageNum = pageNum;
      this.queryParams.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.getList();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryParams.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.getList();
    },
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.ids = selectedRows.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查询文本审核（敏感词管理）列表 */
    getList() {
      this.loading = true;
      instance_2({
        url: "/censor/list",
        method: "get",
        params: this.queryParams,
      }).then((response) => {
        console.log("----", response);
        this.censorList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {

      console.log(this.queryParams);

      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        // qzhdTextCensor:{
        //      keyword: null,
        // },
        keyword: null,
        // deleted: null,
        // createPosId: null,
        // createUserName: null,
        // deleteTime: null,
        // deletePosId: null,
        // deleteUserName: null,
      },
        this.handleQuery();
    },
    // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map((item) => item.id);
    //   this.single = selection.length !== 1;
    //   this.multiple = !selection.length;
    // },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加（敏感词）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      instance_2({
        url: "/censor/" + id,
        method: "get",
      }).then((response) => {
        if ((response.data.code = "0000")) {
          this.form = response.data.data;
        } else {
          this.$message({
            showClose: true,
            message: response.data.msg,
            type: "error",
          });
        }
        this.open = true;
        this.title = "修改（敏感词）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            // updateCensor(this.form)
            instance_2({
              url: "/censor",
              method: "put",
              data: this.form,
            }).then((response) => {
              if ((response.data.code = "0000")) {
                this.$message.success("修改成功");
              } else {
                this.$message.error(response.data.msg);
              }
              this.open = false;
              this.getList();
            });
          } else {
            instance_2({
              url: "/censor",
              method: "post",
              data: this.form,
            }).then((response) => {
              if ((response.data.code = "0000")) {
                this.$message.success("新增成功");
              } else {
                this.$message.error(response.data.msg);
              }
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || that.ids;
      let delFun = () => {
        instance_2({
          url: "/censor/",
          method: "delete",
          params: {
            id: ids,
          },
        }).then((response) => {
          if ((response.data.code = "0000")) {
            this.$message.success("删除成功");
            this.getList();
          } else {
            this.$message.error(response.data.msg);
          }
        });
      };
      this.$baseConfirm(
        '是否确认删除（敏感词）编号为"' + ids + '"的数据项?',
        "警告",
        delFun
      );
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有文本审核（敏感词管理）数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportCensor(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    // handleSizeChange(val) {
    //   this.queryParams.pageSize = val;
    //   this.getList();
    // },
    // handleCurrentChange(val) {
    //   this.queryParams.pageNum = val;
    //   this.getList();
    // },
    resetForm(formName) {
      console.log("🤗🤗🤗, formName =>", formName);
      if (this.$refs[formName] !== undefined) {
        this.$refs[formName].resetFields();
      }
    },
  },
};
</script>
