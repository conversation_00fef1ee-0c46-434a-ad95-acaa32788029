import { instance_1 } from "@/api/axiosRq";
//查看新建岗位
export function getdutyAdjustAssistantlistApi(params) {
    return instance_1({
        url: `/dutyAdjustAssistant/list`,
        method: "post",
        params: { id: params },
    });
}
export function adjustComplete(data) {
    return request({
        url: "/dutyAdjust/complete",
        method: "post",
        data,
    });
}
export function getdutyAdjustAssistantlistfindApi(params) {
    return instance_1({
        url: `/dutyAdjust/find`,
        method: "get",
        params: { id: params },
    });
}

export function getstateChangescreateApi(data) {
    return instance_1({
        url: `/stateChanges/create`,
        method: "post",
        data,
    });
}
export function getstateChangescompleteApi(data) {
    return instance_1({
        url: `/stateChanges/complete`,
        method: "post",
        data,
    });
}

export function getstateChangesfindApi(params) {
    return instance_1({
        url: `/stateChanges/find`,
        method: "get",
        params: { id: params },
    });
}
export function getpersonalDetailscreateApi(data) {
    return instance_1({
        url: `/personalDetails/create`,
        method: "post",
        data,
    });
}
//代表信息管理修改
export function getcommitteeMemberupdateApi(data) {
    return instance_1({
        url: `/committeeMember/update`,
        method: "post",
        data,
    });
}
//修改的保存流程
export function getpersonalDetailscompleteApi(data) {
    return instance_1({
        url: `/personalDetails/complete`,
        method: "post",
        data,
    });
}
//修改个人信息的保存流程
export function getchangeInfocompleteApi(data) {
    return instance_1({
        url: `/changeInfo/complete`,
        method: "post",
        data,
    });
}
//修改的保存流程
export function getpersonalDetailsupdateApi(data) {
    return instance_1({
        url: `/personalDetails/update`,
        method: "post",
        data,
    });
}
//修改个人信息的的保存流程
export function getchangeInfocreateApi(data) {
    return instance_1({
        url: `/changeInfo/create`,
        method: "post",
        data,
    });
}
//修改回显
export function getpersonalDetailsfindApi(params) {
    return instance_1({
        url: `/personalDetails/find`,
        method: "get",
        params: { id: params },
    });
}
//个人信息的修改回显
export function getchangeInfofindApi(params) {
    return instance_1({
        url: `/changeInfo/find`,
        method: "get",
        params: { id: params },
    });
}
export function getcommitteeMgetByIdApi(params) {
    return instance_1({
        url: `/committeeMember/getById`,
        method: "get",
        params: { id: params },
    });
}
//登记表审核发送保存
export function gethxrdjbcompleteApi(data) {
    return instance_1({
        url: `hxrdjb/complete`,
        method: "post",
        data,
    });
}
//补选结果审核发送保存
export function getbxjgbcompleteApi(data) {
    return instance_1({
        url: `/bxjg/complete`,
        method: "post",
        data,
    });
}
//候选人分配表的送审发送保存
export function gethxrfpbscompleteApi(data) {
    return instance_1({
        url: `/hxrfpbs/complete`,
        method: "post",
        data,
    });
}
//候选人确认表保存发送
export function gethxrqrbscompleteApi(data) {
    return instance_1({
        url: `hxrqrbs/complete`,
        method: "post",
        data,
    });
}
//选举结果保存发送
export function getdbxjjghxrqrbscompleteApi(data) {
    return instance_1({
        url: `hjbd/dbxjjg/complete`,
        method: "post",
        data,
    });
}
// 获取个人信息
export function findPersonal(data) {
    return instance_1({
        url: `infoQuery/findPersonal`,
        method: "get",
        data,
    });
}
//活动登记保存发送
export function getdutyActivecompleteApi(data) {
    return instance_1({
        url: `/dutyActive/complete`,
        method: "post",
        data,
    });
}
//候选人分配表保存发送
export function getishxrqrbscompleteApi(data) {
    return instance_1({
        url: `/hxrqrbs/complete`,
        method: "post",
        data,
    });
}
//进社区活动审核发送
export function communityScheduleComplete(data) {
    return instance_1({
        url: `/communityScheduleActivity/complete`,
        method: "post",
        data,
    });
}
// 变动送审传的附件 stateChanges/saveFj
export function communityBDsaveFj(data) {
    return instance_1({
        url: `/stateChanges/saveFj`,
        method: "post",
        data,
    });
}
//  变动送审传的附件查询 stateChanges/findAllFj
export function communityBDfindAllFj(params) {
    return instance_1({
        url: `/stateChanges/findAllFj`,
        method: "get",
        params,
    });
}
//  变动送审传的附件删除 stateChanges/findAllFj
export function communityBDdeleteFile(params) {
    return instance_1({
        url: `/stateChanges/deleteFile`,
        method: "DELETE",
        params,
    });
}

// 修改手机号
export function getpersonCreatePhoneApi(data) {
    return instance_1({
        url: `/personalDetails/createPhone`,
        method: "post",
        data,
    });
}