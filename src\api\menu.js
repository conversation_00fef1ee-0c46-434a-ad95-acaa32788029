import request from "@/utils/request";
import qs from "qs";

export function getTree(data) {
  return request({
    url: "/api/v1/menu/manage/findTree/" + data.rootId,
    method: "post",
    data,
  });
}

export function getPage(data) {
  return request({
    url: "/api/v1/menu/manage/findPage",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/menu/manage/add",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/menu/manage/update",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/menu/manage/remove",
    method: "post",
    data,
  });
}
