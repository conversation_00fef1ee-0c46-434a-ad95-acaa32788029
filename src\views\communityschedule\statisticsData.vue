<template>
  <div>
    <div class="TYcss">
      <div class="jbxx-contt" style="min-height: 78vh">
        <div class="jbqkss"><span class="jbqkbz">统计报表</span></div>
        <div style="padding-bottom: 20px">
          <a-row>
            <a-col
              v-for="(item, index) in meetingList"
              :key="index"
              :span="24"
              :sm="12"
              :lg="8"
              :xl="4"
              @click="skiUrl(item)"
            >
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.url" :alt="item.title" />
                <div class="jbxxson_box">
                  <div class="tits" style="" v-html="item.title">
                    {{ item.title }}
                  </div>
                  <div style="" class="remarks" v-html="item.remarks"></div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import "@/styles/StatisticalReport/StatisticalReport.scss";
export default {
  name: "WorkIndex",
  components: {},
  data() {
    return {
      TBloading: false,
      meetingList: [
        {
          id: 2,
          title: "代表<br/>驻站情况",
          color: "#be403d",
          path: "/community/stationMember",
          url: require("@/assets/images/StatisticalReport/联系人民群众-代表进社区-统计报表/代表驻站情况.png"),
        },
        {
          id: 3,
          title: "年度<br/>活动计划情况",
          color: "#be403d",
          path: "/community/yearScheduled",
          url: require("@/assets/images/StatisticalReport/联系人民群众-代表进社区-统计报表/年度活动计划情况.png"),
        },
        {
          id: 4,
          title: "代表进社区<br/>活动次数统计",
          color: "#be403d",
          path: "/community/communityMember",
          url: require("@/assets/images/StatisticalReport/联系人民群众-代表进社区-统计报表/代表进社区 活动次数统计.png"),
        },
        {
          id: 5,
          title: "活动情况",
          color: "#be403d",
          path: "/community/liaisonStationActivity",
          url: require("@/assets/images/StatisticalReport/联系人民群众-代表进社区-统计报表/活动情况.png"),
        },
        {
          id: 6,
          title: "各区的代表<br/>进社区活动次数统计",
          color: "#be403d",
          path: "/community/stationActivityMember",
          url: require("@/assets/images/StatisticalReport/联系人民群众-代表进社区-统计报表/各区代表 进社区活动次数统计.png"),
        },
        {
          id: 7,
          title: "办理群众<br/>意见情况汇总",
          color: "#be403d",
          path: "/community/solveProblem",
          url: require("@/assets/images/StatisticalReport/联系人民群众-代表进社区-统计报表/办理群众 意见情况汇总.png"),
        },
        //   {
        //   id: 8,
        //   title: "代表对联络站工作的评价情况汇总",
        //   color: "#be403d",
        //   path: "/community/evaluationWork",
        //   url: require("@/assets/images/StatisticalReport/联系人民群众-代表进社区-统计报表/meeting.png"),
        // },
        {
          id: 9,
          title: "联络站活动情况<br/>查询统计",
          color: "#be403d",
          path: "/community/liaisonStationActivityStatistics",
          url: require("@/assets/images/StatisticalReport/联系人民群众-代表进社区-统计报表/办理群众 意见情况汇总.png"),
        },
      ],
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "进社区管理");
    this.$store.dispatch("navigation/breadcrumb2", "统计报表");
  },
  methods: {
    // @!@!@
    skiUrl(item) {
      this.$router.push({
        path: item.path,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 0px !important;
  background-color: rgb(246, 246, 246) !important;
}
</style>
