<template>
  <div class="table-container">
    <CommunityActivitySchedule
      v-model="form"
      :current="current"
      title="年度活动计划"
      :oper="oper"
    >
      <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
        <a-row :gutter="50" type="flex">
          <AdminStreetStationCascade
            v-model="form"
            :disabled="isDisabled"
            @liaisonStationSelect="onLiaisonStationSelect"
          />

          <FormInput
            v-model="form.contactName"
            prop="contactName"
            label="联系人名称"
            :disabled="isDisabled"
          />

          <FormInput
            v-model="form.contactPhoneNumber"
            label="联系人电话"
            prop="contactPhoneNumber"
            :disabled="isDisabled"
          />

          <FormInput
            v-model="form.year"
            prop="year"
            label="年度"
            type="year"
            :disabled="true"
            placeholder="自动填充"
          />

          <FormPicker
            v-model="form.serverTime"
            prop="serverTime"
            label="活动计划时间"
            type="date"
            :disabled="isDisabled"
          />

          <FormInput
            v-model="form.stationAgentName"
            prop="stationAgentName"
            label="站长、副站长姓名"
            :disabled="isDisabled"
          />

          <FormInput
            v-model="form.stationAgentPhoneNumber"
            label="站长、副站长电话"
            prop="stationAgentPhoneNumber"
            :disabled="isDisabled"
          />

          <FormItem label="受邀代表" prop="attendMembers" :span="24">
            <a-input
              v-if="oper == 'view'"
              :value="
                form.attendMembers?.map(
                  (it) =>
                    `${it.userName}（${
                      options.sfDm[it.sfDm || it.committeeMemberTypeId] || ''
                    }）`
                )
              "
              disabled
            />

            <a-tree-select
              v-else
              :value="form.attendMembers?.map((it) => it.userId)"
              style="width: 100%"
              :tree-data="attendMembers"
              tree-checkable
              show-checked-strategy="SHOW_CHILD"
              tree-default-expand-all
              :placeholder="
                !form.liaisonStationId ? '请先选择联络站' : '请选择接待代表'
              "
              :disabled="isDisabled || !form.liaisonStationId"
              @change="onAttendMembersChange"
            />
          </FormItem>

          <FormItem label="活动内容" prop="mark" :span="24">
            <a-input
              v-model="form.mark"
              type="textarea"
              auto-complete="off"
              :rows="5"
              placeholder="请输入活动内容"
              :disabled="isDisabled"
            />
          </FormItem>
        </a-row>
      </a-form-model>

      <template slot="actions">
        <a-button @click="onBack">取 消</a-button>
        <a-button
          v-if="['create', 'update'].includes(oper)"
          type="primary"
          @click="onSubmit()"
          >保 存</a-button
        >
        <a-button v-if="oper != 'create'" @click="onTurnover"
          >流程流转记录</a-button
        >
      </template>
    </CommunityActivitySchedule>

    <!-- 流程流转记录 -->
    <lczzjl ref="lczzjl" :proc-inst-id="form.procInstId"></lczzjl>
  </div>
</template>

<script>
import CommunityActivitySchedule from "@/components/CommunityActivitySchedule/index.vue";
import moment from "moment";
import {
  getCommunityScheduleById,
  save,
  update,
} from "@/api/communityschedule";
import { getById } from "@/api/communityscheduleactivity";
import { phoneValidator } from "@/utils/validator";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import FormPicker from "@/components/FormPicker/index.vue";
import FormItem from "@/components/FormItem/index.vue";
import lczzjl from "@/views/common/lczzjl.vue";
import { getMembers } from "@/api/station";

export default {
  name: "ActivityListDetail",
  components: {
    lczzjl,
    FormItem,
    FormPicker,
    FormInput,
    AdminStreetStationCascade,
    CommunityActivitySchedule,
  },
  data() {
    return {
      form: {
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        contactName: undefined,
        contactPhoneNumber: undefined,
        serverTime: undefined,
        stationAgentName: undefined,
        stationAgentPhoneNumber: undefined,
        attendMembers: [],
      },
      rules: {
        administrativeAreaId: { required: true, message: "请选择行政区划" },
        streetTownId: { required: true, message: "请选择街道乡镇" },
        liaisonStationId: { required: true, message: "请选择联络站" },
        contactName: { required: true, message: "请输入联系人名称" },
        contactPhoneNumber: {
          required: true,
          validator: phoneValidator("联系人电话"),
        },
        serverTime: { required: true, message: "请选择活动时间" },
        attendMembers: { required: true, message: "请选择受邀代表" },
        mark: { required: true, message: "请输入活动内容" },
        stationAgentPhoneNumber: {
          required: false,
          validator: phoneValidator("站长、副站长电话"),
        },
      },
      options: {
        attendMembers: [],
        sfDm: {
          1: "全国人大代表",
          2: "省人大代表",
          3: "市人大代表",
          4: "区人大代表",
          5: "镇街人大代表",
        },
      },
    };
  },
  computed: {
    oper() {
      const oper = this.$route.query.oper;
      const id = this.$route.query.id;
      if (!id) return "create";
      return oper || "update";
    },
    isDisabled() {
      return ["audit", "view"].includes(this.oper);
    },
    attendMembers() {
      if (!this.options.attendMembers.length) return [];

      return [
        {
          title: "全部",
          value: "__all",
          children: this.options.attendMembers.map((it) => ({
            value: it.userId,
            title: `${it.name}（${
              this.options.sfDm[it.sfDm || it.committeeMemberTypeId] || ""
            }）`,
          })),
        },
      ];
    },
    current() {
      if (this.$route.query.all) {
        return this.form.currStateDm == 41 ||
          this.form.currStateName == "已终审"
          ? 5
          : 3;
      }
      return 0;
    },
  },
  watch: {
    "form.serverTime": {
      handler(val) {
        this.form.year = val ? moment(val).get("year") : undefined;
      },
    },
  },
  created() {
    this.load();
  },
  methods: {
    load() {
      const id = this.$route.query.id;
      if (!id) return;
      const api = this.$route.query.all ? getById : getCommunityScheduleById;
      api({ id }).then((res) => {
        this.form = res.data;
        getMembers({ id: this.form.liaisonStationId }).then((res) => {
          this.options.attendMembers = res.data;
        });
      });
    },
    onBack() {
      this.$router.back();
    },
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return this.$message.warning("请完善必填项信息");

        const api = this.form.id ? update : save;
        api(this.form).then(() => {
          this.$message.success("保存成功");
          this.onBack();
        });
      });
    },
    onLiaisonStationSelect(it) {
      this.form.contactPhoneNumber = it.contactPhoneNumber || "";
      this.form.contactName = it.contactName || "";
      this.onLiaisonStationIdChange();
    },
    onTurnover() {
      this.$refs.lczzjl.xuanze(this.form.procInstId);
    },
    onLiaisonStationIdChange() {
      this.form.attendMembers = undefined;
      if (!this.form.liaisonStationId) return;

      getMembers({ id: this.form.liaisonStationId }).then((res) => {
        this.options.attendMembers = res.data;
        this.$forceUpdate();
      });
    },
    onAttendMembersChange(value) {
      this.form.attendMembers = value
        .map((userId) =>
          this.options.attendMembers.find((it) => it.userId == userId)
        )
        .filter((it) => it)
        .map((it) => ({
          userId: it.userId,
          inviteId: it.treeId,
          dhDm: it.dhDm,
          jcDm: it.jcDm,
          orgId: it.orgId,
          deptId: it.deptId,
          postId: it.postId,
          name: it.name,
          userName: it.name,
          sfDm: it.committeeMemberTypeId,
          committeeMemberTypeId: it.committeeMemberTypeId,
        }));
    },
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  padding-top: 10px;
}
</style>
