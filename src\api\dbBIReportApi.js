// dbBIReportApi.js
// 用于 dbBIReportList.vue 的增删改查接口封装
import request from '@/utils/requestTemp';

// 获取报告列表
export function getTableList(queryForm, pagination) {
  return request({
    url: '/BIReport/getTableList',
    method: 'post',
    params: queryForm,
    data: pagination
  });
}

// 获取报告详情
export function getDetail(id) {
  return request({
    url: `/BIReport/getDetail/${id}`,
    method: 'get',
    params: { id }
  });
}

// 新增报告
export function insert(data) {
  return request({
    url: '/BIReport/insert',
    method: 'post',
    data
  });
}

// 更新报告
export function update(data) {
  return request({
    url: '/BIReport/update',
    method: 'post',
    data
  });
}

// 批量删除报告
export function deletebatch(ids) {
  return request({
    url: '/BIReport/deletebatch',
    method: 'post',
    data: ids
  });
}