<template>
  <div class="table-container">
    <ZTable :list="list"
            :columns="columns" />
  </div>
</template>

<script>
export default {
  data () {
    return {
      TBloading: false,
      list: [
        {
          index: "1",
          name: "代表选举管理系统",
          url: "https://192/168.3.220/committee/sync",
          corn: "0 0 12 * * ? ",
          status: "有效",
          createTime: "2021.10.21",
        },
      ],
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "状态",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "系统名称",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "name",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "接口URL",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "url",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "同步周期",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "corn",
          customRender: (text, record, index) => {
            return record.corn;
          },
        },

        {
          title: "创建日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              // h(
              //   "span",
              //   {
              //     attrs: {
              //       type: "text",
              //     },
              //     style: {
              //       cursor: "pointer",
              //       marginLeft: "14px",
              //       color: "#DB3046",
              //     },
              //     on: {
              //       click: () => {
              //         // this.switMeeting(record);
              //       },
              //     },
              //   },
              //   "暂停"
              // ),
              // h(
              //   "span",
              //   {
              //     attrs: {
              //       type: "text",
              //     },
              //     style: {
              //       cursor: "pointer",
              //       marginLeft: "14px",
              //       color: "#DB3046",
              //     },
              //     on: {
              //       click: () => {
              //         // this.handleEdit(record);
              //       },
              //     },
              //   },
              //   "编辑"
              // ),
              // h(
              //   "span",
              //   {
              //     attrs: {
              //       type: "text",
              //     },
              //     style: {
              //       cursor: "pointer",
              //       marginLeft: "14px",
              //       color: "#DB3046",
              //     },
              //     on: {
              //       click: () => {
              //         // this.handleDelete(record);
              //       },
              //     },
              //   },
              //   "删除"
              // ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleDelete(record);
                    },
                  },
                },
                "成功"
              ),
            ]);
          },
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "代表新增");
    this.$store.dispatch("navigation/breadcrumb2", "同步新建代表");
  },
};
</script>
<style scoped></style>
