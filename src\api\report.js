import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/meetingReport/getManageReportList",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/meetingReport/addReport",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/meetingReport/updateReport",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/meetingReport/deleteReport",
    method: "post",
    data
  });
}

export function addReportInfo() {
  return request({
    url: "/api/v1/meetingReport/initReportId",
    method: "post",
  });
}

export function getReportInfo(param) {
  return request({
    url: "/api/v1/meetingReport/getReport/" + param,
    method: "post",
  });
}

export function getReportIdentityList(param) {
  return request({
    url: "/api/v1/meetingReport/getIdentityList/" + param,
    method: "post",
  });
}
