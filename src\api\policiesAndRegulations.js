import { instance_yajy } from "@/api/axiosRq";
// 政策法规 模块
// 政策法规库管理 列表 暂时不用
export function findRepositoryClass(formData) {
  return instance_yajy({
    url: `/api/v1/content/content/findRepositoryClass`,
    method: "post",
    data: formData,
  });
}
// 政策法规库管理 搜索显示列表
export function contentFindPage(formData) {
  return instance_yajy({
    url: `/api/v1/content/content/findPage`,
    method: "post",
    data: formData,
  });
}
// 政策法规库管理 保存
export function contentSave(formData) {
  return instance_yajy({
    url: `/api/v1/content/content/save`,
    method: "post",
    data: formData,
  });
}
// 政策法规库管理 删除
export function contentDel(params) {
  return instance_yajy({
    url: `/api/v1/content/content/delete`,
    method: "post",
    params,
  });
}
// 政策法规库管理 导出
export function contentExportExcel(formData) {
  return instance_yajy({
    url: `/api/v1/content/content/exportExcel`,
    method: "post",
    responseType: "blob",
    data: formData,
  });
}
// 政策法规库管理 下载
export function contentDownload(params) {
  return instance_yajy({
    url: `/api/v1/content/content/download`,
    method: "post",
    responseType: "blob",
    params,
  });
}

// 政策法规库管理 回显文件
export function findAttachment(params) {
  return instance_yajy({
    url: `/api/v1/system/attachment/findAttachment`,
    method: "post",
    params,
  });
}

// 政策法规分类 列表
export function classFindPage(formData) {
  return instance_yajy({
    url: `/api/v1/content/class/findPage`,
    method: "post",
    data: formData,
  });
}
// 政策法规分类 新增
export function classSave(formData) {
  return instance_yajy({
    url: `/api/v1/content/class/save`,
    method: "post",
    data: formData,
  });
}

// 政策法规分类 修改
export function classUpdate(formData) {
  return instance_yajy({
    url: `/api/v1/content/class/update`,
    method: "post",
    data: formData,
  });
}
// 政策法规分类 导出
export function classExportExcel(formData) {
  return instance_yajy({
    url: `/api/v1/content/class/exportExcel`,
    method: "post",
    data: formData,
    responseType: "blob",
  });
}
// 政策法规分类 删除
export function classDel(params) {
  return instance_yajy({
    url: `/api/v1/content/class/delete`,
    method: "post",
    params,
  });
}
