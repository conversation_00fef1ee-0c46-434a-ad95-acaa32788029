<template>
    <a-modal :title="orgTreeDialogTitle" :visible.sync="OrgTreeDialogVisible" width="50%" destroyOnClose
        @cancel="close">
        <!-- <a-input style="width:100%" placeholder="输入关键字进行过滤" v-model="filterText" @change="onChange"></a-input> -->
        <a-tree ref="orgTree" :replace-fields="replaceFields" :tree-data="orgTreeData" v-model="checkIdentyData"
            checkStrictly @select="onSelect">
            <template slot="title" slot-scope="{ fullName, orgLevel }">
                <a-icon :type="orgLevel == '1' ? 'apartment' : 'file'" style="margin-right: 10px;" />
                <span v-if="searchValue && fullName.indexOf(searchValue) > -1" style="color: #f50;">{{
                    fullName
                }}</span>
                <span v-else>{{ fullName }}</span>
            </template>
        </a-tree>

        <span slot="footer" class="dialog-footer">
            <a-button @click="close">取 消</a-button>
            <a-button style="padding-left: 10px;" type="primary" @click="confirm">确 定</a-button>
        </span>
    </a-modal>
</template>

<script>
import { getfindDbOUApi } from "@/api/registrationMage/tableIng.js";
import { log } from "@antv/g2plot/lib/utils";
export default {
    data() {
        return {
            filterText: "",
            name: "",
            expandedKeys: [],
            searchValue: "",
            allChildData: [],
            selectedKeys: [],
            autoExpandParent: true,
            checked: [],
            checkIdentyData: [],
            orgTreeDialogVisible: false,
            orgTreeData: [],
            OrgTreeDialogVisible: false,
            newOrgTreeDialogVisible: null,
            orgTreeDefaultKey: [],
            replaceFields: {
                title: "fullName",
                key: "id",
                children: "children",
            },
            isRadio: true,
            relType: {
                type: Number,
                default: 1,
            },
        };
    },
    props: {
        orgTreeDialogTitle: {
            type: String,
            default: "选择单位",
        },
        jcDm: {
            type: String,
            default: "",
        },
        defaultSelect: {
            type: Array,
            default: [],
        },
    },
    watch: {
        newOrgTreeDialogVisible(newVal) {
            if (newVal) {
                this.initOrgTree();
            } else {
                this.expandedKeys = [];
                this.checked = [];
                this.checkIdentyData = [];
            }
        },
    },
    methods: {
        onSelect(item, data) {
            this.ExpandedKeysData = item
            let ojb = {}
            ojb = data.node._props.dataRef
            this.checked.push(ojb);
        },
        // 树状搜索
        onChange(e) {
            console.log(this.orgTreeData, 'userTreeData');
            const value = e.target.value;
            this.searchValue = value
            if (value == '') return this.$message.info('请输入关键字')
            this.expandedKeys = [];
            this.backupsExpandedKeys = [];
            console.log(value, 'value');
            const candidateKeysList = this.getkeyList(value, this.orgTreeData, []);
            console.log(candidateKeysList, 'candidateKeysList');
            candidateKeysList.forEach(
                (item) => {
                    const key = this.getParentKey(item, this.orgTreeData);
                    if (key && !this.backupsExpandedKeys.some((item) => item === key)) this.backupsExpandedKeys.push(key);
                },
            );
            console.log(this.backupsExpandedKeys, 'this.backupsExpandedKeys');
            const { length } = this.backupsExpandedKeys;
            for (let i = 0; i < length; i++) {
                this.getAllParentKey(this.backupsExpandedKeys[i], this.orgTreeData);
            }
            this.expandedKeys = this.backupsExpandedKeys.slice();
            console.log(this.expandedKeys, ' this.expandedKeys ');
            if (candidateKeysList.length == 0) {
                this.$message.info('没有相关数据')
            }
        },
        getkeyList(value, tree, keyList) {
            for (let i = 0; i < tree.length; i++) {
                const node = tree[i];
                if (node.fullName.indexOf(value) > -1) {
                    keyList.push(node.id);
                }
                if (node.children) {
                    this.getkeyList(value, node.children, keyList);
                }
            }
            return keyList;
        },
        // 该递归主要用于获取key的父亲节点的key值
        getParentKey(key, tree) {
            let parentKey;
            let temp;
            for (let i = 0; i < tree.length; i++) {
                const node = tree[i];
                if (node.children) {
                    temp = this.getParentKey(key, node.children)
                    if (node.children.some((item) => item.id === key)) {
                        parentKey = node.id;
                    } else if (temp) {
                        parentKey = temp;
                    }
                }
            }
            return parentKey;
        },
        // 获取该节点的所有祖先节点
        getAllParentKey(key, tree) {
            let parentKey;
            if (key) {
                parentKey = this.getParentKey(key, tree);
                if (parentKey) {
                    if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
                        this.backupsExpandedKeys.push(parentKey);
                    }
                    this.getAllParentKey(parentKey, tree);
                }
            }
        },
        onExpand(expandedKeys) {
            this.expandedKeys = expandedKeys;
            this.autoExpandParent = false;
        },
        close() {
            this.OrgTreeDialogVisible = false;
            this.newOrgTreeDialogVisible = false;
            this.checked = [];
            this.filterText = ''
        },
        initOrgTree() {
            getfindDbOUApi(this.jcDm).then((res) => {
                this.orgTreeData = res.data.data;
                this.OrgTreeDialogVisible = true;
                if (this.defaultSelect.length > 0) {
                    this.checkIdentyData = this.defaultSelect.map((item) => item.id);
                    this.checked = this.defaultSelect;
                    this.$forceUpdate();
                }
            });
        },

        // 树状选择
        onCheck(item, data) {
            console.log(data);
            if (data.checked) {
                let value = data.node.dataRef;
                this.checked.push(value);
            } else {
                let orgId = data.node.dataRef.orgId;
                this.checked.forEach((item, index) => {
                    if (item.orgId == orgId) {
                        this.checked.splice(index, 1);
                    }
                });
            }
            if (data.checkedNodesPositions.length == '0') {
                this.checked = []
            }
        },
        // onExpand(expandedKeys) {
        //   this.expandedKeys = expandedKeys;
        // },
        confirm() {
            this.OrgTreeDialogVisible = false;
            this.newOrgTreeDialogVisible = false;
            console.log(this.checked);
            this.$emit("confirm", this.checked);
            this.checkIdentyData = [];
            this.filterText = ''
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-tree {
    max-height: 500px;
    width: 100%;
    overflow-y: auto;
}
</style>