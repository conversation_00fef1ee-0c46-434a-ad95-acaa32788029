<template>
  <el-dialog
    :before-close="close"
    :title="identityTreeDialogTitle"
    :visible.sync="identityTreeDialogVisible"
    width="30%"
  >
    <el-input
      placeholder="输入关键字进行过滤"
      v-model="filterText"
      @input="$forceUpdate()"
    ></el-input>
    <el-tree
      ref="identityTree"
      :data="identityTreeData"
      :props="identityTreeProps"
      node-key="id"
      @check-change="identityCheckChange"
      show-checkbox
      :check-on-click-node="true"
      :default-checked-keys="identityTreeDefaultKey"
      :default-expanded-keys="identityTreeExpandedKey"
      :filter-node-method="filterNode"
      :check-strictly="true"
    ></el-tree>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getListByTypes } from "@/api/resource";
import { getAllTypeList } from "@/api/identity";
export default {
  data() {
    return {
      filterText: "",
      identityTreeDialogVisible: false,
      treeCheckedKey: [],
      identityTreeData: [],
      identityTreeDefaultKey: [],
      identityTreeExpandedKey: ["root"],
      identityTreeProps: {
        children: "children",
        label: "name",
      },
      checkLimit: 0,
      checkLimitTips: "",
      doFilter: null,
      checkedDataList: [],
    };
  },
  props: {
    identityTreeDialogTitle: {
      type: String,
      default: "选择使用组别",
    },
  },
  created() {
    this.initTree();
  },
  watch: {
    filterText(val) {
      this.$refs.identityTree.filter(val);
    },
  },
  methods: {
    initTree() {
      //获取所有身份
      getAllTypeList().then((res1) => {
        let allIdentity = res1.data;
        let secondList = [];
        getListByTypes([3]).then((res2) => {
          res2.data.forEach((type) => {
            let thirdList = [];
            allIdentity.forEach((identity) => {
              if (identity.type == type.name) {
                if (this.doFilter && this.doFilter(identity)) {
                  return;
                }
                thirdList.push(identity);
              }
            });
            var second = {
              id: type.id,
              name: type.name,
              children: thirdList,
              disabled: true,
            };
            if (this.doFilter && this.doFilter(second)) {
              return;
            }
            secondList.push(second);
          });
        });
        this.identityTreeData.push({
          id: "root",
          name: "所有类型",
          children: secondList,
          disabled: true,
        });
      });
    },
    open(checkedData, checkLimit, checkLimitTips, doFilter) {
      let that = this;
      that.identityTreeDialogVisible = true;
      that.checkedDataList = checkedData;
      that.checkLimit = checkLimit;
      that.checkLimitTips = checkLimitTips;
      that.doFilter = doFilter;

      if (that.doFilter) {
        this.identityTreeData[0].children = this.identityTreeData[0].children.filter(
          function (resource) {
            return !that.doFilter(resource);
          }
        );
        this.identityTreeData[0].children.forEach((resource) => {
          resource.children = resource.children.filter(function (resource) {
            return !that.doFilter(resource);
          });
        });
      }

      that.doCheckedData();
      that.$forceUpdate();
    },

    doCheckedData() {
      let that = this;
      if (that.checkedDataList) {
        //清空旧数据
        that.treeCheckedKey = [];
        that.identityTreeDefaultKey = [];
        if (that.checkedDataList.length > 0) {
          that.checkedDataList.forEach((item) => {
            that.identityTreeExpandedKey.push(item);
            that.identityTreeDefaultKey.push(item);
            that.treeCheckedKey.push(item);
          });
          // that.$refs.identityTree.setCheckedKeys(that.treeCheckedKey, true);
        }
      }
    },

    identityCheckChange(data, checked, indeterminate) {
      if (checked) {
        if (this.checkLimit) {
          if (this.checkLimit == 1) {
            this.$refs.identityTree.setCheckedKeys([data.id]);
          } else if (
            this.$refs.identityTree.getCheckedNodes().length > this.checkLimit
          ) {
            this.$refs.identityTree.setChecked(data.id, false);
            this.$message({
              message: this.checkLimitTips,
              type: "warning",
            });
            return false;
          }
        }
      }
    },

    confirm() {
      if (
        this.checkLimit &&
        this.$refs.identityTree.getCheckedNodes().length > this.checkLimit
      ) {
        this.$message({
          message: this.checkLimitTips,
          type: "warning",
        });
        return;
      }

      this.$emit(
        "confirm",
        this.$refs.identityTree.getCheckedNodes(),
        this.relType
      );

      this.$refs.identityTree.setCheckedKeys([]);

      this.treeCheckedKey = [];
      this.identityTreeDefaultKey = [];

      this.identityTreeDialogVisible = false;
    },

    close() {
      this.$refs.identityTree.setCheckedKeys([]);
      this.treeCheckedKey = [];
      this.identityTreeDefaultKey = [];
      this.identityTreeDialogVisible = false;
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
  },
};
</script>
