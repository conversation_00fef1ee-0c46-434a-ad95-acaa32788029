<template>
  <div>
    <a-row>
      <a-col :md="24" :sm="24">
        <a-card title="代表相关报表">
          <a-row :gutter="[16, 16]" v-for="item in meetingList" :key="item.id">
            <a-col :md="4" v-for="col in item.col" :key="col.id" @click="skiUrl(col)">
              <a-card
                hoverable
                style="text-align: center;
              height: 260px; width: 180px;"
              >
                <img
                  slot="cover"
                  :src="col.url"
                  alt
                  style="
                    width: 80%;
                    height: 80%;
                    margin-left: 10%;
                    margin-top: 10%;
                    margin-bottom: 5%;
                  "
                />
                <a-card-meta :title="col.title"></a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
export default {
  name: "WorkIndex",
  components: {},
  data() {
    return {
      meetingList: [
        {
          id: 1,
          col: [
            {
              id: 1,
              title: "代表申报表",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/behalfDeclare",
            },
            {
              id: 2,
              title: "代表登记表",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/behalf-check",
            },
            {
              id: 3,
              title: "代表名册",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/behalf-muster",
            },
            {
              id: 4,
              title: "代表选举结果报告",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/RepresentativeForm",
            },
            {
              id: 5,
              title: "代表补选结果报表",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/by-election",
            },
            {
              id: 6,
              title: "代表基本情况统计报表",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/behalf-base",
            },
            {
              id: 7,
              title: "代表构成情况统计报表",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/Composition",
            },
            {
              id: 8,
              title: "代表名额分配及构成统计表",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/Distribution",
            },
            {
              id: 9,
              title: "代表出缺情况统报表",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/VacancyStatistics",
            },
            {
              id: 10,
              title: "补选结果",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/By_electionForm",
            },
            {
              id: 11,
              title: "代表出缺",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ballot/Vacancy",
            },
            // {
            //   id: 12,
            //   title: "代表选举结果报表",
            //   color: "#be403d",
            //   url: require("@/assets/images/indexPage/meeting.png"),
            //   path: "/ballot/entryElection",
            // },
          ],
        },
      ],
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "候选人管理");
    this.$store.dispatch("navigation/breadcrumb2", "统计报表");
  },
  methods: {
    skiUrl(col) {
      this.$router.push({
        path: col.path,
      });
    },
  },
};
</script>
