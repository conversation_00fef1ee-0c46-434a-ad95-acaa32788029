<template>
  <!-- 代表选举管理 用户树 -->
  <a-modal :title="electionTitle" :visible.sync="electionVisible" width="1200px" height="1200px" @cancel="close">
    <a-input placeholder="输入关键字进行过滤" v-model="filterText" @change="onChange"></a-input>
    <a-tree ref="orgTree" :checkable="false" selectable :expanded-keys="expandedKeys"
      :auto-expand-parent="autoExpandParent" @expand="onExpand" :replace-fields="replaceFields" :tree-data="orgTreeData"
      v-model="userTreeDefaultKey" @select="onSelect">
      <template slot="title" slot-scope="{ fullName, orgLevel }">
        <a-icon :type="orgLevel == '1' ? 'apartment' : 'file'" style="margin-right: 10px;" />
        <span v-if="searchValue && fullName.indexOf(searchValue) > -1" style="color: #f50;">
          {{ fullName }}
        </span>
        <span v-else>{{ fullName }}</span>
      </template>
    </a-tree>
    <span slot="footer" class="dialog-footer">
      <a-button @click="close">取 消</a-button>
      <a-button type="primary" @click="confirm">确 定</a-button>
    </span>
  </a-modal>
</template>

<script>
// import { getUTree } from "@/api/treeGroup";
import { findDbOU } from "@/api/registrationMage/tableIng.js";

export default {
  data() {
    return {
      filterText: "",
      electionVisible: false,
      checkedShow: false,
      orgTreeData: [],
      orgTreeDefaultKey: [],
      replaceFields: {
        title: "fullName",
        key: "entryId",
        children: "children",
      },
      checked: [],
      userTreeDefaultKey: [],
      allChildData: [],
      selectedKeys: [],
      // 搜索值
      searchValue: "",
      // 展开的节点
      expandedKeys: [],
      // 是否自动展开
      autoExpandParent: true,
      isRadio: {
        type: Boolean,
        default: true,
      },
      relType: {
        type: Number,
        default: 1,
      },
    };
  },
  props: {
    rootOrgId: {
      type: String,
      default: "root",
    },
    electionTitle: {
      type: String,
      default: "选择用户",
    },
    jcDm: {
      type: String,
      default: undefined,
    }
  },
  created() { },
  watch: {
    electionVisible(val) {
      if (val) {
        this.initOrgTree();
      }
    },
  },
  methods: {
    //展开点击节点下的子节点
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    // 树状选择
    onSelect(item, data) {
      if (data.selected) {
        this.checked = [data.node.dataRef];
        this.userTreeDefaultKey = [data.node.dataRef.orgId];
      }
    },

    // 树状搜索
    onChange(e) {
      const value = e.target.value;
      // const expandedKeys = this.allChildData.map((item) => {
      //   if (item.name.indexOf(value) > -1) {
      //     return this.getParentKey(item.id, this.userTreeData);
      //   }
      //   return null;
      // });
      const expandedKeys = this.getKeyList(
        value,
        this.orgTreeData,
        [],
        "fullName",
        "entryId"
      );

      Object.assign(this, {
        expandedKeys: expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      });
      console.log(expandedKeys, "aaaa", this.expandedKeys)
    },
    // 获取包含字段id的父节点
    getKeyList(value, tree, keyList, titleName, idName) {
      console.log(tree)
      // 获取包含搜索关键字的所有节点
      let getId = (value_id, tree_id, list_id) => {
        console.log("🤗🤗🤗, idName =>", idName, titleName);
        for (let i = 0; i < tree_id.length; i++) {
          const node = tree_id[i];
          if (node[titleName].indexOf(value_id) > -1) {
            list_id.push(node[idName]);
          }
          if (node.children) {
            getId(value_id, node.children, list_id);
          }
        }
        return list_id;
      };
      // 获取包含搜索关键字的所有节点的父节点
      let getParentKey = (value_par, tree_par) => {
        let parentKey;
        for (let index = 0; index < tree_par.length; index++) {
          const node = tree_par[index];
          if (node.children) {
            if (node.children.some((item) => item[idName] === value_par)) {
              parentKey = node[idName];
            } else if (getParentKey(value_par, node.children)) {
              parentKey = getParentKey(value_par, node.children);
            }
          }
        }
        return parentKey;
      };
      // 执行
      getId(value, tree, []).forEach((v) => {
        keyList.push(getParentKey(v, tree));
      });
      return keyList;
    },
    // 获取所在的父节点
    getParentKey(id, tree) {
      let parentKey;
      for (let index = 0; index < tree.length; index++) {
        const node = tree[index];
        if (node.children) {
          if (node.children.some((item) => item.id === id)) {
            parentKey = node.id;
          } else if (this.getParentKey(id, node.children)) {
            parentKey = this.getParentKey(id, node.children);
          }
        }
      }
      return parentKey;
    },
    // 关闭
    close() {
      this.checked = [];
      this.electionVisible = false;
    },
    // 获取数据
    initOrgTree() {
      findDbOU(this.jcDm).then((res) => {
        this.orgTreeData = res.data.data;
      });
    },
    // 提交
    confirm() {
      if (this.checked[0].entryType != 4) {
        return this.$message.error("请选择人员数据");
      }
      this.electionVisible = false;
      console.log("🤗🤗🤗, this.checked =>", this.checked);
      this.$emit("confirm", this.checked);
    },
  },
};
</script>
<style lang="css" scoped>
div>>>.ant-modal-body {
  height: 600px;
  width: 100%;
  overflow-y: auto;
}
</style>
