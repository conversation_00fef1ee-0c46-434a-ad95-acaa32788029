<template>
  <div>
    <div>
      <a-form layout="horizontal">
        <div>
          <a-row>
            <a-col :md="8"
                   :sm="24">
              <a-form-item label="身份名称关键字"
                           :labelCol="{span: 5}"
                           :wrapperCol="{span: 18}">
                <a-input v-model="queryForm.name"
                         allow-clear
                         placeholder="请输入关键字" />
              </a-form-item>
            </a-col>
            <span style="float: right; margin-top: 3px;">
              <a-button style="margin-right: 12px"
                        type="primary"
                        size="large"
                        @click="handleQuery">搜索</a-button>
              <a-button size="large"
                        @click="handleClear">重置</a-button>
            </span>
          </a-row>
        </div>

      </a-form>
    </div>
    <div>
      <a-space style="margin-bottom: 18px;">
        <a-button type="primary"
                  @click="addNew">新增</a-button>
        <!-- <a-button icon="delete">批量删除</a-button> -->
      </a-space>
      <standard-table :key="tableKey"
                      :columns="columns"
                      :data-source="dataSource"
                      :selected-rows.sync="selectedRows"
                      :loading="TBloading"
                      :pagination="pagination"
                      row-key="Id"
                      @clear="onClear"
                      @change="onChange"
                      @selectedRowChange="onSelectChange">
      </standard-table>
    </div>
    <!-- 新增/编辑 对话框-->
    <a-modal :title="dialogTitle"
             :visible="dialogVisible"
             :confirm-loading="confirmLoading"
             centered
             okText="保存"
             @ok="handleOk"
             @cancel="handleCancel">
      <a-form-model :model="formData"
                    :rules="rules"
                    ref="formDataRef"
                    :label-col="labelCol"
                    :wrapper-col="wrapperCol">
        <a-form-model-item label="身份名称"
                           prop="name">
          <a-input v-model="formData.name"
                   placeholder="" />
        </a-form-model-item>
        <a-form-model-item label="身份类别"
                           prop="type">
          <a-select v-model="formData.identityType"
                    @change="selectChange"
                    placeholder="请选择身份类别">
            <a-select-option v-for="item in arrsfList"
                             :key="item.id"
                             :value="item.id">{{item.name}}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="身份描述">
          <a-input v-model="formData.remark"
                   placeholder="" />
        </a-form-model-item>
        <a-form-model-item label="排序"
                           prop="sort">
          <a-input v-model="formData.sort"
                   placeholder="" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import StandardTable from "@/components/table/StandardTable";
import { myPagination } from "@/mixins/pagination.js";
import { getIdentityManagementList, addEditIdentityManagement, deleteEditIdentityManagement, getAllIdentityTypeList1 } from '@/api/convenTion/z_Management'
import { iShowNumber } from "@/utils/validate";

export default {
  name: 'user_role_type',
  components: { StandardTable },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    const validateNumber = (rule, value, callback) => {
      // 根据校验结果进行处理
      iShowNumber(value) ? callback() : callback(new Error('排序号为正整数'))
    }
    return {
      TBloading: false,
      arrsfList: [],
      columns: [
        {
          width: 80,
          title: '排序',
          align: "cente",
          dataIndex: 'sort',
          customRender: (text, record, index) => {
            return text || ++index
          }
        },
        {
          align: "cente",
          title: '身份名称',
          dataIndex: 'name',
        },
        {
          align: "cente",
          title: '身份类别',
          dataIndex: 'typeName',
          customRender: (text) => {
            return text || "—";
          }
        },
        {
          align: "cente",
          title: '身份描述',
          dataIndex: 'remark',
          customRender: (text) => {
            return text || '未说明'
          }
        },
        {

          title: "操作",
          align: "cente",
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    // marginLeft: "5px",
                    color: "#3296FA",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(record);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#3296FA",
                    // color: "#3296FA",
                  },
                  on: {
                    click: () => {
                      this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      rules: {
        name: [{ required: true, message: "请输入类别身份名称", trigger: "blur" }],
        identityType: [{ required: true, message: "请选择身份类别", trigger: "blur" }],
        sort: [{ required: true, message: "请输入序号", trigger: "blur" },
        { trigger: 'blur', validator: validateNumber }],
      },
      formType: "create", // 当前是新增或编辑，新增：create  编辑：update
      dialogVisible: false, // 是否弹出表单对话框
      dialogTitle: "新增", // 对话框标题
      confirmLoading: false, // 对话框 loading 标记
      dataSource: [],
      selectedRows: [],
      queryForm: {
        page: 1,
        rows: 10,
        order: "desc",
        searchValue: "",
      },
      tableKey: new Date().getTime(),
      formData: {
        name: "",
        id: "",
        identityType: "",
        remark: "",
        sort: "",
      },
    }
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "人员管理");
    this.$store.dispatch("navigation/breadcrumb2", "身份类别");
    this.fetchData()
    this.loadDistributionIdentityList()
  },
  methods: {
    async fetchData () {
      this.TBloading = true;
      getIdentityManagementList(this.queryForm).then((res)=> {
        this.TBloading = false;
        if (res.status == 200) {
          this.dataSource = res.data.data.records;
          this.pagination.total = res.data.data.total;
        }else {
          this.$message.error(res.data.message);
        }
      });
    },
    async loadDistributionIdentityList () {
      getAllIdentityTypeList1().then((res) =>{
        if (res.data.code == 200) {
          this.arrsfList = res.data.data
          console.log(this.arrsfList, 'arrsfList');
        }
      });
    },
    selectChange (id) {
      console.log(id);
      this.formData.identityType = id
    },
    // 编辑
    handleEdit (record) {
      // this.$refs['formDataRef'].clearValidate();
      //console.log(record)\
      // console.log(this.$refs)
      // console.log(this.$refs.toString);
      this.dialogTitle = "编辑";
      this.formType = "update"; // 当前为编辑状态
      this.formData.name = record.name;
      this.formData.remark = record.remark;
      this.formData.identityType = record.identityType;
      this.formData.typeName = record.typeName;
      this.formData.sort = record.sort;
      this.formData.id = record.id;
      this.dialogVisible = true;
    },
    // 新增
    addNew () {
      // 弹出新增数据对话框
      this.reset();
      this.formType = "create";
      this.dialogTitle = "新增";
      this.dialogVisible = true;
    },
    // 保存
    // 新增与编辑
    handleOk (e) {
      if (this.formData.identityType == '') {
        return this.$message.error('请选择身份类型')
      }
      this.confirmLoading = true;
      this.$refs['formDataRef'].validate( (valid) => {
        if (valid) {
          console.log("🤗🤗🤗, this.formData =>", this.formData);
          addEditIdentityManagement(this.formData).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
            } else {
              this.$message.error(res.data.message);
            }
            this.fetchData();
            this.dialogVisible = false;
            this.confirmLoading = false;
            this.formData = {};
          });
        } else {
          this.$message.error('请您输入完整信息');
        }
      });
    },
    reset () {
      this.formData = {
        id: "",
        name: "",
        remark: "",
        sort: "",
        identityType: "",
      };
    },
    handleDelete (row) {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "确定删除",
        content: "是否确定删除？",
        onOk: () => {
          deleteEditIdentityManagement(row.id).then((res) => {
            console.log('删除')
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.fetchData();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    handleCancel (e) {
      this.reset();
      this.dialogVisible = false;
      this.$refs['formDataRef'].clearValidate();
    },

    onClear () {
    },
    // 搜索
    handleQuery () {
      this.pagination.current = 1;
      this.pagination.current = 1
      this.fetchData();
      this.pagination.current = 1
    },
    // 重置
    handleClear () {
      this.queryForm = {page: 1, rows: 10, order: "desc"};
      this.pagination.pageSize = 10
      this.handleQuery();
    },
    onStatusTitleClick () {

    },
    onChange () {

    },
    onSelectChange () {
      //
    },
  }
}
</script>
<style scoped lang='less'>
</style>
