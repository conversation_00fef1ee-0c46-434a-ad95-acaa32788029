<template>
  <div class="nav-bar-container">
    <div class="nav-bar-container-box">
      <a-row :gutter="15">
        <el-col
          :xs="4"
          :sm="12"
          :md="12"
          :lg="12"
          :xl="12"
          style="display: flex;"
        >
          <div class="image">
            <img
              v-if="layout === 'horizontal' || layout === 'theme3'"
              class="qxj-head-logo"
              src="../logo2.png"
            />
          </div>

          <div class="navigationText-Databox">
            <div
              @click="fn(index, item)"
              class="navigationText-box"
              :class="{ bg: index == isactive }"
              v-for="(item, index) in navigationText"
              :key="index"
            >
              <div>
                <span>
                  <a-icon
                    :type="item.icon"
                    style="color: #ffde00; padding-right: 5px;"
                  />
                </span>
                <span class="titleSpan">{{ item.title }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col
          v-if="'horizontal' === layout || layout === 'theme3'"
          :xs="20"
          :sm="12"
          :md="12"
          :lg="12"
          :xl="12"
          class="qxj-head-right"
        >
          <div class="right-panel">
            <theme-bar></theme-bar>
            <el-avatar
              :size="30"
              src="https://i.gtimg.cn/club/item/face/img/8/15918_100.gif"
            ></el-avatar>
            <span class="qxj-user">{{ userName }}</span>
            <span class="qxj-user cp" @click="logout" style="padding-left: 0;">
              <i class="el-icon-switch-button ii"></i>
              <!--  style="font-size: 28px;" -->
            </span>
          </div>
        </el-col>
        <el-col v-else :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <div class="right-panel">
            <el-button type="text" @click="entrySjlxr">三级联系人</el-button>
            <byui-screenfull @refresh="refreshSelectedTag"></byui-screenfull>
            <theme-bar></theme-bar>
            <byui-icon
              title="重载路由"
              :pulse="pulse"
              :icon="['fas', 'redo']"
              @click="refreshSelectedTag"
            />
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link">
                <el-avatar
                  class="user-avatar"
                  :src="require('@/assets/user.gif')"
                ></el-avatar>
                <span class="user-name">{{ userName }}</span>
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>

              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <byui-icon :icon="['fas', 'user']"></byui-icon>个人中心
                </el-dropdown-item>
                <el-dropdown-item command="logout" divided>
                  <byui-icon :icon="['fas', 'sign-out-alt']"></byui-icon
                  >退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-col>
      </a-row>
    </div>
    <!-- 临时修改!!!  v-show="item.title == '代表建议管理'"-->
    <div class="navigationText">
      <!-- 第二级别 -->
      <div
        class="navigationchildren"
        :class="{ bgColor: index == iScolor }"
        @click="fn1(index, item)"
        v-for="(item, index) in navigationchildren"
        :key="index"
      >
        {{ item.title }}
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import events from "@/components/events";
import ErrorLog from "@/components/ErrorLog";
import ByuiScreenfull from "@/components/ByuiScreenfull";
import Breadcrumb from "@/layouts/components/Breadcrumb";
import ThemeBar from "@/layouts/components/ThemeBar";
import { log } from "@antv/g2plot/lib/utils";
export default {
  name: "NavBar",
  components: {
    Breadcrumb,
    ErrorLog,
    ByuiScreenfull,
    ThemeBar,
  },
  data() {
    return {
      pulse: false,
      isactive: 2,
      iScolor: "0",
      navigationText: [
        {
          title: "首页",
          icon: "bank",
          children: [
            { title: "平台门户" },
            { title: "联系人民群众" },
            { title: "联系人大常委会" },
            { title: "履职活动组织" },
            { title: "代表建议管理" },
            // { title: "大会议案管理" },
            { title: "履职登记管理" },
            { title: "代表选举管理" },
            { title: "代表信息管理" },
            { title: "在线学习培训" },
          ],
        },
        {
          title: "机关",
          icon: "star",
          children: [
            { title: "平台门户" },
            { title: "联系人民群众" },
            { title: "联系人大常委会" },
            { title: "履职活动组织" },
            { title: "代表建议管理" },
            // { title: "大会议案管理" },
            { title: "履职登记管理" },
            { title: "代表选举管理" },
            { title: "代表信息管理" },
            { title: "在线学习培训" },
          ],
        },
        {
          title: "代表",
          icon: "team",
          children: [
            { title: "平台门户" },
            { title: "联系人民群众" },
            { title: "联系人大常委会" },
            { title: "履职活动组织" },
            { title: "代表建议管理" },
            // { title: "大会议案管理" },
            { title: "履职登记管理" },
            { title: "代表选举管理" },
            { title: "代表信息管理" },
            { title: "在线学习培训" },
            { title: "数据驾驶舱" },
          ],
        },
        { title: "立法", icon: "fork", children: [{ title: "立法监督" }] },
        { title: "监督", icon: "eye", children: [{ title: "监督机制" }] },
        { title: "会务", icon: "contacts", children: [{ title: "会务信息" }] },
        { title: "应用", icon: "setting", children: [{ title: "应用信息" }] },
      ],
      navigationchildren: [
        {
          title: "平台门户",
          path: "/platformIndex/platformIndex",
          name: "门户首页",
          openKeys: [],
        },
        {
          title: "联系人民群众",
          path: "/Proelemasses/site/index",
          name: "人民群众首页",
          openKeys: [5],
        },
        {
          title: "联系人大常委会",
          path: "/inform1/InformList",
          name: "查阅信息",
          openKeys: [12],
        },
        {
          title: "履职活动组织",
          path: "/dutyActivityIndex/index",
          name: "代表首页",
          openKeys: [18],
        },
        {
          title: "代表建议管理",
          path: "/recommendedMage/recommendedMageIndex",
          name: "代表首页",
          openKeys: [26],
        },
        // { title: "大会议案管理" },
        {
          title: "履职登记管理",
          path: "/registrationMage/registrationMage",
          name: "代表首页",
          openKeys: [35],
        },
        {
          title: "代表选举管理",
          path: "/electoralMage/RepresentIndex",
          name: "代表首页",
          openKeys: [42],
        },
        {
          title: "代表信息管理",
          path: "/informationMage/workIndex",
          name: "代表首页",
          openKeys: [49],
        },
        {
          title: "在线学习培训",
          path: "/organizationIndex/organizationTraining",
          name: "代表首页",
          openKeys: [58],
        },
        {
          title: "数据驾驶舱",
          path: "/region/region",
          name: "数据驾驶舱",
          openKeys: [65],
        }
      ],
      userName: "",
    };
  },
  computed: {
    ...mapGetters([
      "avatar",
      "collapse",
      // "userName",
      "loginTimes",
      "lastLoginTime",
      "selectedTag",
      "device",
      "layout",
    ]),
  },
  created() {
    // 设置名称
    this.userName = JSON.parse(sessionStorage.getItem("USERID")).username;
    let title =
      sessionStorage.getItem("title2") && sessionStorage.getItem("index2");
    if (title) {
      this.$store.dispatch(
        "navigation/breadcrumb",
        sessionStorage.getItem("title2")
      );
      this.iScolor = sessionStorage.getItem("index2");
    }
    events.$on("sendIndex", (index) => {
      this.iScolor = index;
    });

    // 根据页签变化变化侧边栏
    events.$on("onSideBar", async (index, item) => {
      this.iScolor = index;
      // this.titleBox = item.title;
      sessionStorage.setItem("title2", item.title);
      sessionStorage.setItem("index2", index);
      this.$store.dispatch("navigation/breadcrumb", item.title);
      sessionStorage.getItem("selectKey");
      // 如果路由不同，切换页面
      if (this.$router.history.current.path != item.path) {
        this.$router.push({
          path: item.path,
        });
        // this.currentPath = item.path
        //  菜单栏定位到对应的页面
        events.$emit("sendEvenLeadig", {
          path: item.path,
          item: item,
        });
      } else {
        // 路由相同，切换页面，需要一个空白页面过度，否则数据不刷新
        this.$router.push({
          path: "/convenTionList/excessive",
        });
        //  菜单栏定位到对应的页面
        events.$emit("sendEvenLeadig", {
          path: item.path,
          item: item,
        });
        setTimeout(() => {
          this.$router.push({
            path: item.path,
          });
        });
      }
    });
  },
  beforeDestroy() {
    events.$off("onSideBar");
  },
  methods: {
    // 第一级别
    fn(index, item) {
      this.isactive = index;
      this.navigationchildren = item.children;
    },
    // 第二级别
    fn1(index, item) {
      sessionStorage.setItem("title2", item.title);
      sessionStorage.setItem("index2", index);
      this.$store.dispatch("navigation/breadcrumb", item.title);
      this.iScolor = index;
      // console.log('系统');
      // console.log(sessionStorage.getItem("title2"));
      // console.log(sessionStorage.getItem("index2"));
      // console.log(this.iScolor);
      // console.log(item);
      // 页面跳转
      this.$router.push({
        path: item.path,
      });
      //  菜单栏定位到对应的页面
      events.$emit("sendEvenLeadig", {
        path: item.path,
      });
      events.$emit(
        "onTabs",
        item.path,
        item.name,
        sessionStorage.getItem("title2"),
        this.iScolor,
        item.openKeys,
        "isMenu"
      );
    },

    handleCollapse() {
      this.$store.dispatch("settings/changeCollapse");
    },
    async logout() {
      await this.$baseConfirm(
        "您确定要退出" + this.$baseTitle + "吗?",
        null,
        () => {
          const fullPath = this.$route.fullPath;
          this.$store.dispatch("user/logout").then(() => {
            this.$router.push(`/login?redirect=${fullPath}`);
          });
        }
      );
    },
    refreshSelectedTag() {
      this.pulse = true;
      this.$router
        .replace({
          path: "/redirect" + this.$route.fullPath,
        })
        .then(() => {
          setTimeout(() => {
            this.pulse = false;
          }, 1000);
        })
        .catch(() => {});
    },
    entrySjlxr() {
      window.open(process.env.VUE_APP_SJLXR_URL, "_blank");
    },
    handleCommand(command) {
      switch (command) {
        case "logout":
          this.logout();
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ii{
   @include add-size($font_size_16);
}
.navigationText-Databox {
  height: 70px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 30px;
}
.nav-bar-container {
  position: relative;
  height: 70px;
  vertical-align: middle;
  // overflow: hidden;
  user-select: none;
  background: $base-color-white;
  box-shadow: $base-box-shadow;
  .nav-bar-container-box {
    height: 70px;
  }
  .left-panel {
    display: flex;
    align-items: center;
    justify-items: center;
    height: 50px;
    max-height: 50px;

    .fold-unfold {
      margin-left: 10px;
      // font-size: 20px;
      @include add-size($font_size_16);
      color: $base-color-gray;
      cursor: pointer;
    }

    ::v-deep {
      .breadcrumb-container {
        margin-left: 10px;
      }
    }
  }
  .navigationchildren {
    cursor: pointer;
    padding: 0 10px;
    font-family: pingFang-M;
    line-height: 50px;
    // font-size: 16px;
    @include add-size($font_size_16);
    color: #63676c;
  }

  .right-panel {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: flex-end;
    height: 70px;

    .user-avatar {
      margin-right: 5px;
      font-weight: 600;
      cursor: pointer;
    }

    .user-name {
      position: relative;
      top: -14px;
      margin-right: 35px;
      margin-left: 5px;
      font-weight: 600;
      cursor: pointer;
    }
    .user-name + i {
      position: absolute;
      top: 16px;
      right: 15px;
    }

    ::v-deep {
      svg {
        width: 1em;
        height: 1em;
        margin-right: 15px;
        font-size: $base-font-size-big;
        color: $base-color-gray;
        cursor: pointer;
        fill: $base-color-gray;
      }

      button {
        svg {
          margin-right: 0;
          color: $base-color-white;
          cursor: pointer;
          fill: $base-color-white;
        }
      }

      .el-badge {
        margin-right: 15px;
      }
    }
  }
}
.bgColor {
  border-bottom: 2px solid #d8293a;
  color: #d8293a !important;
}
</style>
<style lang="scss" scoped>
.el-dropdown-menu--small .el-dropdown-menu__item {
  padding: 0 15px;
  // font-size: 13px;
  @include add-size($font_size_16);
  line-height: 36px !important;
}
.mr10 {
  margin-right: 10px;
}

.navigationText {
  padding-left: 30px;
  display: flex;
  width: 100%;
  color: #fff;
  background-color: #fff;
  height: 42px;
  /* border-top: 8px solid #f0f2f5; */
  -webkit-box-shadow: 0px 1px 10px #b7b7b9;
  box-shadow: 0px 1px 10px #b7b7b9;
}

.navigationText-box {
  color: #fff;
  font-family: pingFang-M;
  // font-size: 20px;
  @include add-size($font_size_16);
  text-align: center;
  cursor: pointer;
  width: 100px;
  line-height: 70px;
  height: 70px;
}
.navigationText-box .titleSpan {
  // font-size: 16px;
  @include add-size($font_size_16);
}
.bg {
  /* background-color: #ad212d; */
}
.image {
  height: 70px;
  line-height: 70px;
}
.qxj-head-logo {
  height: 49px;
}
.qxj-head-logo img {
  width: 100%;
  height: 49px;
}
</style>
