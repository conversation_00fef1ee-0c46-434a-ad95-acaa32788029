<template>
  <div class="signData table-container">
    <SearchForm @onReset="reset" @onSearch="search">
      <template v-slot:topSearch>
        <DhJcCascade v-model="queryForm" allow-clear is-dbt @load="search" :levelRoleMainIdentify="levelRoleMainIdentify"/>
      </template>
      <template v-slot:moreSearch>
        <SingleSearch @onEnter="search" :title="'代表姓名'" :value.sync="queryForm.userName" />
      </template>
    </SearchForm>

    <a-modal
      :title="title"
      destroy-on-close
      :visible.sync="visibleShow"
      width="600px"
      @cancel="close"
      @ok="close"
    >
      <div
        slot="footer"
        class="dialog-footer"
        style="position: relative; padding-right: 15px; text-align: right;"
      >
        <a-button @click="close">关闭</a-button>
      </div>

      <a-form-model
        ref="noticeForm"
        :label-col="{ span: 5 }"
        :rules="rules"
        :wrapper-col="{ span: 16 }"
        :model="forValue"
      >
        <a-form-model-item label="届次" prop="level_name">
          <a-input
            v-model="forValue.level_name"
            :disabled="iShow"
            placeholder
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="代表姓名" prop="USER_NAME">
          <a-input
            v-model="forValue.USER_NAME"
            :disabled="iShow"
            placeholder
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="性别" prop="sex">
          <a-input
            v-model="forValue.sex"
            :disabled="iShow"
            placeholder
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="出生日期" prop="BIRTHDAY">
          <a-input
            v-model="forValue.BIRTHDAY"
            :disabled="iShow"
            placeholder
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="出生日期" prop="BIRTHDAY">
          <a-input
            v-model="forValue.BIRTHDAY"
            :disabled="iShow"
            placeholder
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="党派" prop="POLITICS_STATUS_NAME">
          <a-input
            v-model="forValue.POLITICS_STATUS_NAME"
            :disabled="iShow"
            placeholder
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="学历" prop="xlmc">
          <a-input
            v-model="forValue.xlmc"
            :disabled="iShow"
            placeholder
          ></a-input>
        </a-form-model-item>

        <a-form-model-item label="手机号码" prop="PHONE">
          <a-input
            v-model="forValue.PHONE"
            :disabled="iShow"
            placeholder
          ></a-input>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <standard-table
      :columns="columns"
      :data-source="list"
      row-key="id"
      :loading="TBloading"
      :pagination="pagination"
      @tableClick="clickRow"
    >
    </standard-table>
  </div>
</template>

<script>
import { myPagination } from "@/mixins/pagination.js";
import { signList, queryData, queryInfo } from "@/api/infoQuery.js";
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
import { DBLZ_DBXXGL } from '@/utils/enum/levelRoleMainIdentifyEnum'; // 导入配置文件
import DhJcCascade from "@/components/DhJcCascade/index.vue";

export default {
  mixins: [myPagination],
  components: {
    SearchForm,
    SingleSearch, 
    DhJcCascade
  },
  data() {
    return {
      levelRoleMainIdentify: DBLZ_DBXXGL,
      TBloading: false,
      visibleShow: false,
      iShow: false,
      list: [],
      title: "",
      // 列表
      columns: [
        {
          fixed: "left",
          title: "届次",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "level",
          customRender: (text, record, index) => {
            return record.level.levelName || "/";
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "性别",
          align: "center",
          width: 60,
          ellipsis: true,
          dataIndex: "genderName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "出生日期",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "birthday",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "党派",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "politicsStatusName",
          customRender: (text, record, index) => {
            return text || "无";
          },
        },
        {
          title: "学历",
          align: "center",
          width: 230,
          ellipsis: true,
          dataIndex: "educationName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "手机号码",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "phone",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 150,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      console.log(record);
                      this.handleData(record.userId);
                    },
                  },
                },
                "查看"
              ),
            ]);
          },
        },
      ],
      forValue: {
        level_name: "",
        USER_NAME: "",
        sex: "",
        BIRTHDAY: "",
        POLITICS_STATUS_NAME: "",
        xlmc: "",
        PHONE: "",
      },
      rules: {
        type: [
          { required: true, message: "活动类型不能为空", trigger: "blur" },
        ],
      },
      list: [],
      queryPage: {
        pageNum: 1,
        pageSize: 10,
      },
      queryForm: {
        userName: "",
        dhDm: "",
        jcDm: "",
        dbtId: "",
      },
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "代表信息查询");
    this.$store.dispatch("navigation/breadcrumb2", "信息查询");
    // this.fetchData();
  },
  methods: {
    // 点击行
    clickRow(event, record) {
      try {
        let state;
        let tableHeader = event.target.__vue__.column.key;
        tableHeader ? (state = true) : (state = false);
        if (tableHeader) {
          this.handleData(record.userId);
        }
      } catch (error) {}
    },
    //重置
    reset() {
      this.queryPage = {
        pageNum: 1,
        pageSize: 10,
      };
      this.queryForm = {
        userName: "",
        dhDm: "",
        jcDm: "",
        dbtId: "",
      };
      this.fetchData();
    },
    // confirm() {},
    close() {
      this.forValue = {
        level_name: "",
        USER_NAME: "",
        sex: "",
        BIRTHDAY: "",
        POLITICS_STATUS_NAME: "",
        xlmc: "",
        PHONE: "",
      };
      this.title = "";
      this.visibleShow = false;
      this.iShow = false;
    },
    async handleData(id, edit) {
      this.title = "查看数据";
      this.iShow = true;

      let res = await queryData(id);

      if (res.code == "0000") {
        // 回显
        let {
          LEVEL_NAME,
          USER_NAME,
          SEX,
          BIRTHDAY,
          POLITICS_STATUS_NAME,
          XLMC,
          PHONE,
        } = res.data;
        this.forValue.level_name = LEVEL_NAME;
        this.forValue.USER_NAME = USER_NAME;
        this.forValue.sex = SEX;
        this.forValue.BIRTHDAY = BIRTHDAY;
        this.forValue.POLITICS_STATUS_NAME = POLITICS_STATUS_NAME;
        this.forValue.xlmc = XLMC;
        this.forValue.PHONE = PHONE;
        this.visibleShow = true;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    search () {
      this.queryPage.pageNum = 1
      this.pagination.current = 1
      this.query()
    },
    async query() {
      let res = await queryInfo(this.queryPage, this.queryForm);
      if (res.code == 200) {
        this.list = res.rows;
        this.pagination.total = res.total;
      } else {
        this.$message({
          message: res.message,
          type: "error",
        });
      }
    },

    async fetchData() {
      this.TBloading = true;
      let res = await signList(this.queryPage, this.queryForm);
      if (res.code == 200) {
        this.list = res.rows;
        this.pagination.total = res.total;
      } else {
        this.$message({
          message: res.message,
          type: "error",
        });
      }
      this.TBloading = false;
    },
  },
};
</script>
<style scoped></style>
