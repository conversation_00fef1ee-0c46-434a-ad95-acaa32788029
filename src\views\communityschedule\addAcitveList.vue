<template>
  <div style="padding: 0 20px;">
    <!-- <ViewTips :tipValue="tipValue" /> -->
    <div style="padding: 0 40px;">
      <a-form-model 
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 15 }"
        :wrapper-col="{ span: 23 }">
          <div class="title_all">
            <span class="title_icon"></span>
            <a-collapse-panel
              header="年度活动计划安排"
              class="title_style"
            />
            <span class="plan_arrange" @click="toLink">本年年度活动计划安排总览</span>
          </div>
          <a-row>
            <!-- <a-col :span="8">
              <a-form-model-item label="届次"
                                prop="session">
                <a-select v-model="form.session"
                          placeholder="请选择届次"
                          allow-clear
                          show-search>
                  <a-select-option v-for="item in SessionList"
                                  :key="item.id"
                                  :label="item.id"
                                  :value="item.id">
                    {{  item.name  }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col> -->
            <a-col :span="6">
              <a-form-model-item label="联络站所属行政区划"
                                prop="administrativeAreaId">
                <a-select v-model="form.administrativeAreaId"
                          style="width: 100%;"
                          placeholder="请选择行政区划"
                          @change="handleAdministrativeAreaChange_add">
                  <a-select-option v-for="item in administrativeAreas"
                                  :key="item.id"
                                  :label="item.name"
                                  :value="item.id">
                    {{  item.name  }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="联络站所属乡镇街道"
                                prop="streetTownId">
                <a-select v-model="form.streetTownId"
                          style="width: 100%;"
                          placeholder="请选择乡镇街道"
                          allow-clear
                          show-search
                          :open="streetTownIdState_add"
                          @select="streetTownIdState_add = false"
                          @focus="streetTownIdFocus_add"
                          @change="handlestreetTownChange_add">
                  <a-select-option v-for="item in streetTowns"
                                  :key="item.id"
                                  :label="item.name"
                                  :value="item.id">{{item.name}}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item 
                label="联络站名称"
                prop="liaisonStationId"
                :label-col="{ span: 15 }"
                :wrapper-col="{ span: 24 }">
                <a-select v-model="form.liaisonStationId"
                          placeholder="请选择联络站"
                          @change="handleListLiaisonStationChange"
                          allow-clear
                          show-search
                          :open="liaisonStationIdState_add"
                          @select="liaisonStationIdState_add = false"
                          @focus="liaisonStationIdFocus_add">
                  <a-select-option v-for="item in liaisonStations"
                                  :key="item.id"
                                  :label="item.name"
                                  :value="item.id">{{
                    item.name
                    }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="所属年度">
                <a-input v-model="form.year"
                        disabled
                        auto-complete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="计划时间"
                                prop="serverTime">
                <a-date-picker v-model="form.serverTime"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              show-time
                              placeholder="选择日期时间"
                              @change="handleServerTime">
                </a-date-picker>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="联络站联系人姓名"
                                prop="contactName">
                <a-input v-model="form.contactName"
                        allow-clear
                        show-search
                        placeholder="请输入联系人姓名"
                        auto-complete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item 
                label="联络站联系人电话"
                allow-clear
                show-search
                placeholder="请输入联系人电话"
                prop="contactPhoneNumber"
                :label-col="{ span: 15 }"
                :wrapper-col="{ span: 24 }">
                <a-input v-model="form.contactPhoneNumber"
                        auto-complete="off" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item 
                label="受邀代表"
                prop="inviteRangeDesc"
                :label-col="{ span: 15 }"
                :wrapper-col="{ span: 24 }">
                <div class="searchStyle">
                  <a-input v-model="form.inviteRangeDesc"
                          disabled
                          enter-button></a-input>
                  <a-button type="primary"
                            icon="search"
                            @click="openattendMembers"></a-button>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item 
                label="活动内容"
                prop="mark"
                :label-col="{ span: 15 }"
                :wrapper-col="{ span: 24 }">
                <a-input v-model="form.mark"
                        allow-clear
                        show-search
                        placeholder="请输入活动内容"
                        type="textarea"
                        :rows="5"
                        auto-complete="off" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <div class="title_all">
                      <span class="title_icon"></span>
                      <a-collapse-panel
                        header="录入人信息"
                        class="title_style"
                      />
          </div>
          <a-row>
                    <a-col :span="8">
                      <a-form-model-item
                        label="层级"
                        prop="instituLevel"
                        :label-col="{ span: 7 }"
                        :wrapper-col="{ span: 23 }"
                        class="person_info"
                      >
                            <a-input
                              class="input_style"
                              v-model="personInfo1"
                              autocomplete="off"
                              placeholder="请输入处理人信息"
                              disabled
                            ></a-input>
                      </a-form-model-item>
                    </a-col> 
                    <a-col :span="8">
                      <a-form-model-item
                        label="机构"
                        prop="instituLevel"
                        :label-col="{ span: 7 }"
                        :wrapper-col="{ span: 23 }"
                        class="person_info"
                      >
                      <a-input
                        class="input_style"
                        v-model="personInfo2"
                        autocomplete="off"
                        placeholder="请输入处理人信息"
                        disabled
                      ></a-input>
                      </a-form-model-item>
                    </a-col> 
                    <a-col :span="8">
                      <a-form-model-item
                        label="姓名"
                        prop="instituLevel"
                        :label-col="{ span: 7 }"
                        :wrapper-col="{ span: 23 }"
                        class="person_info"
                      >
                        <a-input
                          class="input_style"
                          v-model="personInfo3"
                          autocomplete="off"
                          placeholder="请输入处理人信息"
                          disabled
                        ></a-input>
                      </a-form-model-item>
                    </a-col> 
          </a-row>
          <a-row 
            type="flex"
            justify="center"
            style="margin: 15px 0 40px 0;">
            <!-- <span slot="footer"
                  class="dialog-footer"> -->
              <a-button style="margin-right: 8px;" @click="onCancle">取 消</a-button>
              <a-button type="primary"
                        @click="confirm">保 存</a-button>
            <!-- </span> -->
          </a-row>
      </a-form-model>
      

      <!-- 受邀代表的树 -->
      <invitedTree ref="ShowinvitedTree"
                  :jc-dm="form.session"
                  :default-select="defaultSelect"
                  :orgTreeDialogTitle="orgTreeDialogTitle" 
                  @confirm="confirminvitedTree"></invitedTree>
    </div>
  </div>
</template>

<script>
import invitedTree from "@/views/common/representative/invitedTree.vue";
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
import { listForAdmin as getStations } from "@/api/station";
import ViewTips from '@/components/ViewTips/index';
export default {
  name: '',
  props: {},
  components: { 
    invitedTree,
    ViewTips
  },
  data() {
    var validateSession = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请填写届次信息"));
      }
      callback();
    };
    var validateServerTime = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请选择时间"));
      }
      callback();
    };
    var validateAdministrativeArea = (rule, value, callback) => {
      if (value === "" || value === undefined) {
        callback(new Error("请选择行政区县"));
      }
      callback();
    };
    var validateStreetTown = (rule, value, callback) => {
      if (value === "" || value === undefined) {
        callback(new Error("请选择乡镇街道"));
      }
      callback();
    };
    var validateLiaisonStation = (rule, value, callback) => {
      if (value === "" || value === undefined) {
        callback(new Error("请选择联络站"));
      }
      callback();
    };
    var validateContactName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请填写联系人名称"));
      }
      callback();
    };
    var validateContactPhoneNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请填写联系人电话"));
      }
      callback();
    };
    var inviteRangeDesc = (rule, value, callback) => {
      if (value.length == 0) {
        callback(new Error("请选择受邀代表"));
      }
      callback();
    };
    var validateMark = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请填写活动内容"));
      }
      callback();
    };
    return {
      form: {
        inviteRangeDesc: '',
        attendMembers: [],
        session: '3',
        serverTime: "",
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        contactPhoneNumber: "",
        contactName: "",
        year: "",
        countryMemberIds: [],
        liaisonStation: "",
      },
      SessionList: [
        { name: "第十四届", id: '1' },
        { name: "第十五届", id: '2' },
        { name: "第十六届", id: '3' },
      ],
      rules: {
        session: [{ validator: validateSession, trigger: "blur" }],
        serverTime: [{ required: true, validator: validateServerTime, trigger: "change" },],
        administrativeAreaId: [{ required: true, validator: validateAdministrativeArea, trigger: "blur", },],
        streetTownId: [{ required: true, validator: validateStreetTown, trigger: "change" },],
        liaisonStationId: [{ required: true, validator: validateLiaisonStation, trigger: "change", },],
        contactName: [{ required: true, validator: validateContactName, trigger: "blur" },],
        contactPhoneNumber: [{ required: true, validator: validateContactPhoneNumber, trigger: "blur", },],
        countryMemberIds: [{ required: true, validator: inviteRangeDesc, trigger: "blur" },],
        customMark: [{ required: true, validator: validateMark, trigger: "blur", },
        ],
      },
      //行政区域列表
      administrativeAreas: [],
      //街道数据
      streetTowns: [],
      //联络站数据
      liaisonStations: [],
      liaisonStationIdState: false,//联络站数组是否为空
      streetTownIdState: false,//街道数组是否为空
      liaisonStationIdState_add: false,//联络站数组是否为空
      streetTownIdState_add: false,//街道数组是否为空
      defaultSelect: [],
      personInfo1: '联络站',
      personInfo2: '广州市越秀区北京街道中心联络站',
      personInfo3: '扬*',
      // tipValue: '【测试展示内容】表单标签告诉用户该表单需要填写什么类型的信息，也涉及到对用户输入内容进行校验与反馈，保证用户信息填写的完整度。单域由文本框、密码框、隐藏域、多行文本框、 复选框、单选框、下拉选择框和文件上传框等组成。',
      orgTreeDialogTitle: '',
    }
  },
  created() {
    this.listAdministrativeAreas();
  },
  methods: {
    handleServerTime () {
      this.form.year = this.form.serverTime.substring(0, 4);
    },
    listAdministrativeAreas() {
      // 获取行政区县
      getAdministrativeAreas().then((response) => {
        this.administrativeAreas = response.data;
      });
    },
    liststreetTowns(administrativeAreaId) {
      if (!administrativeAreaId) {
        administrativeAreaId=""
      }
        getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
          this.form.streetTownId = undefined;
          this.form.liaisonStation = "";
        }
      );
    },
    // 获取联络站
    listLiaisonStations(streetTownId) {
      getStations({ streetTownId: streetTownId }).then((response) => {
        this.liaisonStations = response.data;
        this.form.liaisonStation = "";
      });
    },
    handleListLiaisonStationChange (id,state) {
      console.log('🤗🤗🤗,  =>', id,state)
      for (let index = 0; index < this.liaisonStations.length; index++) {
        const element = this.liaisonStations[index];
        // console.log("🤗🤗🤗, element =>", element);
        if (element.id == id) {
          // console.log("🤗🤗🤗, element =>", element);
          this.form.contactPhoneNumber = element.contactPhoneNumber;
          this.form.contactName = element.contactName;
          this.form.liaisonStation = element;
          this.$forceUpdate();
          break;
        }
        // console.log("🤗🤗🤗,  this.form =>", this.form);
      }
    },
    // 选择行政区域
    handleAdministrativeAreaChange(val) {
      this.listQuery.streetTownId = undefined;
      this.liststreetTowns(val);
    },
    // 选择乡镇街道
    handlestreetTownChange(val) {
      this.listQuery.liaisonStationId = undefined;
      this.listLiaisonStations(val);
    },
    // 选择行政区域 添加的
    handleAdministrativeAreaChange_add(val) {
      this.form.streetTownId = undefined;
      this.liststreetTowns(val);
    },
    // 选择乡镇街道 添加的
    handlestreetTownChange_add (val) {
      this.form.liaisonStationId = undefined;
      this.listLiaisonStations(val);
    },
    //提示选择
    liaisonStationIdFocus_add () {
      if (this.form.streetTownId && this.form.administrativeAreaId) {
        this.liaisonStationIdState_add = true;
      } else {
        this.$message.info("请选择行政区域和乡镇街道");
        this.liaisonStationIdState_add = false;
      }
    },
    //提示选择
    streetTownIdFocus_add () {
      if (this.form.administrativeAreaId) {
        this.streetTownIdState_add = true;
      } else {
        this.$message.info("请选择行政区域");
        this.streetTownIdState_add = false;
      }
    },
    openattendMembers () {
      this.orgTreeDialogTitle = "选择受邀代表";
      this.$refs.ShowinvitedTree.newOrgTreeDialogVisible = true;
    },
    confirminvitedTree (data) {
      console.log('收到的内容', data);
      
      if (data) {
        this.defaultSelect = data;
        this.form.attendMembers = [];
        this.form.inviteRangeDesc = "";
        this.form.attendMembers = this.recursionChild(data);
        this.form.attendMembers.forEach((item, index) => {
          if (index !== this.form.attendMembers.length - 1) {
            this.form.inviteRangeDesc =
              this.form.inviteRangeDesc + item.fullName + "、";
          } else {
            this.form.inviteRangeDesc = this.form.inviteRangeDesc + item.fullName;
          }
        });
        // this.form.attendMembers.forEach((item, index) => {
        //   if (index !== this.form.attendMembers.length - 1) {
        //     this.form.inviteRangeDesc =
        //       this.form.inviteRangeDesc + item.userName + "、";
        //   } else {
        //     this.form.inviteRangeDesc =
        //       this.form.inviteRangeDesc + item.userName;
        //   }
        // });
        // if (this.isshowIn) {
        //   //  如果新增后再改变自动修改一下
        //   this.$nextTick(() => {
        //     this.SubmitCensorship(true);
        //   });
        // }
      }
    },
    recursionChild(arr) {  
      console.log('进入了recursionChild方法');  
      return arr.filter(item => !(item.children && Array.isArray(item.children)));  
    },
    confirm() {
      if (this.checkMembers.length != 0) {
        // let ids = [];
        // this.checkMembers.forEach((item) => {
        //   ids.push(item.id);
        // });
        // this.form.countryMemberIds = ids;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.title == "新增年度安排") {
            save(this.form).then((response) => {
              this.$message.success("保存成功");
              this.addDialogVisible = false;
              this.fetchData();
              this.$refs.form.resetFields();
            });
          } else {
            update(this.form).then((response) => {
              this.$message.success("保存成功");
              this.addDialogVisible = false;
              this.fetchData();
              this.$refs.form.resetFields();
            });
          }
        } else {
          this.$message.warning("请完善必填项信息");
          reject("参数检验错误");//报错
        }
      });

      //联络站
      // this.form.liaisonStationId = this.form.liaisonStation.id;
      //代表id
    },
    onCancle() {
      this.$router.go(-1)
    },
    toLink() {
      console.log('跳转去总览');
      this.$router.push({
        path: "/representative/planOverview"
      });
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-calendar-picker {
  width: 100%;
}
::v-deep .ant-form-item-label {
  text-align: left;
}
::v-deep .ant-form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
}
.title_all {
  position: relative;
   margin-left: -15px;
   margin-right: -15px;

  .title_style {
    padding: 10px 0px 0 10px;
    background: rgba(190, 64, 61, 0.1);
    padding-bottom: 12px;
    border-bottom: 1px solid #cfcece;
  }
  .title_icon {
    position: absolute;
    top: 50%;
    transform: translate(0, -6px);
    display: inline-block;
    width: 6px;
    height: 10px;
    background-color: #d92b3e;
  }
}
.plan_arrange {
  position: absolute;
  top: 50%;
  transform: translate(0, -10px);
  left: 140px;
  font-size: 12px;
  color: #385ad6;
  border-bottom: 1px solid #385ad6;
  cursor: pointer;
}
</style>