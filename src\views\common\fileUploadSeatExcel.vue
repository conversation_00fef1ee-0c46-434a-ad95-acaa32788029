<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="800px"
    @close="close"
  >
    <div style="display: flex;">
      <div style="flex: 3; border: 1px solid #ebeef5; padding-top: 10px;">
        <el-form
          ref="resourceForm"
          :model="resourceForm"
          :rules="rules"
          label-width="250px"
        >
          <a-row style="display: inline-block;">
            <div v-if="isUpdate == 1">
              <el-form-item label="上传座次图片" prop="file">
                <el-upload
                  ref="clearFile"
                  :action="action"
                  :data="data1"
                  list-type="picture-card"
                  accept="image/jpg,image/jpeg,image/png,image/gif"
                  :limit="imgLimit"
                  :file-list="productImgs"
                  :multiple="isMultiple"
                  :on-preview="handlePictureCardPreview"
                  :on-remove="handleRemove"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload"
                  :on-exceed="handleExceed"
                  :on-error="imgUploadError"
                >
                  <i class="el-icon-plus"></i>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible">
                  <img width="100%" :src="dialogImageUrl" alt />
                </el-dialog>
              </el-form-item>

              <el-form-item label="座次显示时间" prop="displayTime">
                <el-date-picker
                  v-model="resourceForm.displayTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  autocomplete="off"
                  type="datetime"
                  placeholder="选择显示时间"
                  style="width: 100%;"
                ></el-date-picker>
              </el-form-item>

              <el-form-item
                v-if="showfloorButton"
                prop="resourceForm.floor"
                label="楼层(*默认一楼)"
              >
                <el-select
                  v-model="resourceForm.floor"
                  filterable
                  placeholder="请选择"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="item in floorOptions"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <!-- <div v-if="isUpdate != 1"> -->
            <div v-if="showUploadButton">
              <el-form-item label="上传座位表" prop="fileList1">
                <el-upload
                  class="upload-demo"
                  :data="resourceForm"
                  :file-list="fileList1"
                  :action="upLoadUrl"
                  :on-success="handleSuccess"
                  :on-remove="handleRemove1"
                  accept=".xlsx,.xls"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">
                    只能导入xlsx,xls文件，且不超过20mb
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item label="导入结果" v-show="resultList.length > 0">
                <a-row v-for="(result, index) in resultList" :key="index">
                  <a-row
                    >{{ index + 1 }}. 共上传{{ result.total }}条座次安排，{{
                      result.success
                    }}条保存成功，{{
                      result.duplicateRows.length
                    }}条座次安排重复，{{
                      result.errorRows.length
                    }}条数据存在问题
                    <el-button
                      type="text"
                      v-if="result.total != result.success"
                      v-show="!result.showDetail"
                      @click="
                        result.showDetail = true;
                        $forceUpdate();
                      "
                      >查看详情</el-button
                    >
                    <el-button
                      type="text"
                      v-if="result.total != result.success"
                      v-show="result.showDetail"
                      @click="
                        result.showDetail = false;
                        $forceUpdate();
                      "
                      >收起详情</el-button
                    >
                  </a-row>
                  <!--<table class="table" style="margin-bottom:10px" v-show="result.showDetail && result.successRows.length > 0">
                <colgroup>
                  <col width="20%" />
                  <col />
                  <col />
                </colgroup>
                <tr>
                  <th colspan="3">座次安排正确</th>
                </tr>
                <tr>
                  <th>行号</th>
                  <th>姓名</th>
                </tr>
                <tr
                  v-for="(successstr, index3) in result.successRows"
                  :key="index3"
                >
                  <td>{{ successstr.uploadRowNumber }}</td>
                  <td>{{ successstr.userName }}</td>
                </tr>
              </table>-->
                  <table
                    class="table"
                    style="margin-bottom: 10px;"
                    v-show="
                      result.showDetail && result.duplicateRows.length > 0
                    "
                  >
                    <colgroup>
                      <col width="20%" />
                      <col />
                      <col />
                    </colgroup>
                    <tr>
                      <th colspan="3">座次安排重复</th>
                    </tr>
                    <tr>
                      <th>行号</th>
                      <th>姓名</th>
                    </tr>
                    <tr
                      v-for="(duplicate, index2) in result.duplicateRows"
                      :key="index2"
                    >
                      <td>{{ duplicate.uploadRowNumber }}</td>
                      <td>{{ duplicate.userName }}</td>
                    </tr>
                  </table>
                  <table
                    class="table"
                    v-show="result.showDetail && result.errorRows.length > 0"
                  >
                    <colgroup>
                      <col width="20%" />
                      <col />
                    </colgroup>
                    <tr>
                      <th colspan="2">数据存在问题</th>
                    </tr>
                    <tr>
                      <th>行号</th>
                      <th>错误原因</th>
                    </tr>
                    <tr
                      v-for="(error, index3) in result.errorRows"
                      :key="index3"
                    >
                      <td>{{ error.uploadRowNumber }}</td>
                      <td>
                        <a-row
                          v-for="(errorMsg, index4) in error.uploadErrorMsg"
                          :key="index4"
                          >{{ errorMsg }}</a-row
                        >
                      </td>
                    </tr>
                  </table>
                </a-row>
              </el-form-item>
            </div>
            <!-- <el-form-item label="选择需要使用本次图片的相同会议日程">
          <el-select
            placeholder="请选择日程"
            filterable
            v-model="clearFile.id"
          >
            <el-option
              v-for="item in meetList"
              :label="item.calendarTitle"
              :value="item.id"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item> -->
          </a-row>
        </el-form>
      </div>
      <div style="width: 2px;"></div>
      <div v-if="seatingListShow" style="flex: 2; border: 1px solid #ebeef5;">
        <el-scrollbar
          wrapClass="scrollbar-wrap"
          :style="{ height: scrollHeight }"
          ref="scrollbarContainer"
        >
          <el-table
            ref="calendarTable"
            :data="impVehicle"
            border
            :element-loading-text="elementLoadingText"
            @selection-change="setSelectRows"
            @sort-change="tableSortChange"
          >
            <!-- <el-table-column type="selection" width="55"></el-table-column> -->
            <el-table-column label="序号" width="80">
              <template slot-scope="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>
            <el-table-column label="身份" prop="company"></el-table-column>
            <el-table-column label="姓名" prop="userName"></el-table-column>
            <el-table-column label="排" prop="horizontalLine"></el-table-column>
            <el-table-column
              label="号"
              prop="verticalLine"
              width="80"
            ></el-table-column>
            <!-- <el-table-column label="操作" width="150" fixed="right">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="danger"
                        plain
                        @click="handleDelete(scope.row)"
                        v-if="
                          checkPermission([
                            'DATA_ZHUXIUANPAI_ACCOMMODATION_OVERALL',
                            'DHZWGLY_ZS',
                            'I_XTGLY',
                            'MEETING_ADMIN',
                          ])
                        "
                        >删除</el-button
                      >
                    </template>
                  </el-table-column> -->
          </el-table>
        </el-scrollbar>
      </div>
    </div>

    <div slot="footer" class="dialog-footer2">
      <span
        v-if="flag == 1 && showUploadButton"
        style="text-align: left; float: left;"
      >
        <el-button
          @click="seatinginfo"
          v-if="
            checkPermission([
              'I_XTGLY',
              'MEETING_ADMIN',
              'MEETING_ORG_ADMIN',
              'HWGLY_QTHY',
              'HWGLY_ZXTHY',
              'HWGLY_FZHY',
              'HWGLY_QTAHY',
            ])
          "
          >座次管理</el-button
        >
      </span>
      <span
        v-if="showUploadButton"
        style="text-align: left; float: left; margin-left: 2px;"
      >
        <el-button @click="listshow">详情</el-button>
      </span>

      <el-button @click="close" style="margin-right: 2px;">关 闭</el-button>
      <span>
        <el-button type="primary" @click="save">确 定</el-button>
      </span>
    </div>
  </el-dialog>
</template>

<script>
import {
  getMeetingList,
  doSave,
  uploadFile,
  uploadexcel,
  isHide,
  getRestMeetingCalendar,
  getImg,
  delMeetingFileById,
  getDisplayTime,
} from "@/api/seating";
import { loginUserInfo } from "@/api/user";
import { mapGetters } from "vuex";
import checkPermission from "@/utils/permission";
export default {
  name: "TableEdit",
  computed: {
    ...mapGetters(["layout", "visitedViews", "routes"]),
  },
  data() {
    return {
      calendId: "",
      isHide: isHide,
      tempImg: "",
      name: "imgUpload",
      upLoadUrl:
        process.env.VUE_APP_BASE_API + "/api/v1/meetingSeating/newimpseatexcle",
      action: process.env.VUE_APP_BASE_API + "/api/v1/meetingFile/fileUpload",
      photoContent: {
        meetingId: "",
        relId: "",
        type: "0",
        encrypted: "false",
      },
      resourceForm: {
        displayTime: "",
        calendId: "",
        floor: "",
      },
      floorOptions: [
        {
          value: 1,
          label: "1楼",
        },
        {
          value: 2,
          label: "2楼",
        },
      ],
      meetingArray: [],
      resultList: [],
      title: "",
      dialogFormVisible: false,
      isNewData: true,
      activeName: "first",
      rules: {},
      imgLimit: 10,
      productImgs: [],
      isMultiple: false,
      dialogImageUrl: "",
      dialogVisible: false,
      data1: {
        relId: "",
        type: 2,
        encrypted: false,
      },
      dbtName: "",
      fileList1: [],
      fileList: [],
      fileData: {
        calendId: "",
        type: 2,
        encrypted: false,
      },
      impVehicle: [],
      meetList: [],
      clearFile: {
        id: "",
      },
      isUpdate: 1,
      flag: 1,
      seatingListShow: false,
      scrollHeight: "0px",
      elementLoadingText: "正在加载...",
      showUploadButton: false,
      showfloorButton: false,
    };
  },
  created() {
    this.isUpdate = 1;
    this.scrollHeight = window.innerHeight + "px";
  },
  mounted() {
    this.showUploadButton = false;
    this.showfloorButton = false;
    console.log("111111", this.showUploadButton);
  },
  methods: {
    handleRemove1(file, fileList) {
      // doFileDelete(file.id).then((res) => {});
      this.impVehicle = [];
      this.$baseMessage(`删除成功`, "success");
    },
    handleSuccess(response, file, fileList) {
      if (response.code !== 200) {
        this.$message.error("数据导入失败，请检查数据格式!");
      } else {
        this.$baseMessage(`上传完成! `, "success");
        console.log(response.data, "response.data");
        this.impVehicle = response.data.successRows;
        this.resultList.forEach((result) => {
          result.showDetail = false;
        });
        var result = response.data;
        result.showDetail = true;
        result.time = new Date();
        this.resultList.push(result);
      }
    },
    handlePictureCardPreview(file) {
      //预览图片时调用
      // console.log(file);
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleRemove(file, fileList) {
      // delMeetingFileById(file.uid).then((res) => {

      // });
      //移除图片
      this.productImgs = fileList;
    },
    beforeAvatarUpload(file) {
      //文件上传之前调用做一些拦截限制
      console.log(file);
      const isJPG = true;
      // const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 8;

      if (
        file.type != "image/jpg" &&
        file.type != "image/jpeg" &&
        file.type != "image/png" &&
        file.type != "image/gif"
      ) {
        this.$message.error("上传文件只能是 jpg、jpeg、png、gif!");
        return false;
      }
      // if (!isJPG) {
      //   this.$message.error('上传头像图片只能是 JPG 格式!');
      // }
      // var testmsg=file.name.substring(file.name.lastIndexOf('.')+1);
      // const extension = testmsg === 'xls'
      // const extension2 = testmsg === 'xlsx'
      // const isLt2M = file.size / 1024 / 1024 < 10
      // if(!extension && !extension2) {
      //     this.$message.error("上传文件只能是 xls、xlsx格式!");
      //     return false;
      // }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 8MB!");
      }
      return isJPG && isLt2M;
    },
    handleAvatarSuccess(res, file, fileList) {
      var id = res.data[0].id;
      // console.log(id, "222");
      this.productImgs = fileList;
      // console.log(this.fileList, "333");
      file.uid = id;
      // console.log(file,'file')
      // console.log(this.fileList, "333 file");
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    handleExceed(files, fileList) {
      //图片上传超过数量限制
      this.$message.error("上传图片不能超过10张!");
      // console.log(file, fileList);
    },
    imgUploadError(err, file, fileList) {
      //图片上传失败调用
      // console.log(err);
      this.$message.error("上传图片失败!");
    },
    handleShow(row) {
      // console.log(isHide);
      var that = this;
      that.showUploadButton = false;
      that.showfloorButton = false;
      console.log(row.calendarType);
      //showfloorButton
      var str = row.calendarType;
      if (str.indexOf("党员会议") != -1) {
        that.showUploadButton = true;
      } else if (str.indexOf("全体会议") != -1) {
        that.showUploadButton = true;
        that.showfloorButton = true;
      } else if (str.indexOf("主席团会议") != -1) {
        that.showUploadButton = true;
      }

      this.resultList = [];
      this.impVehicle = [];
      this.data1.relId = row.id;
      this.isHide = isHide;
      this.fileData.calendId = row.id;
      this.resourceForm.calendId = row.id;
      this.photoContent.relId = row.id;
      this.photoContent.meetingId = row.meetingId;
      this.calendId = row.id;
      const obj = {
        calendId: row.id,
      };
      // getDisplayTime
      getDisplayTime(obj).then((res) => {
        // console.log(res.data,"11111")
        this.resourceForm.displayTime = res.data.displayTime;
      });
      getImg(obj).then((res) => {
        let arr = [];
        let resss = {};
        res.data.forEach((item) => {
          arr.push({
            uid: item.id,
            name: item.name + "." + item.suffix,
            url:
              `${process.env.VUE_APP_BASE_API}` +
              "/api/v1/meetingFile/view/" +
              item.id,
          });
        });
        this.productImgs = arr;
        console.log(this.productImgs);
      });
      getRestMeetingCalendar([row.id]).then((res) => {
        this.meetList = res.data;
      });
      if (this.isUpdate == 1) {
        this.title = "上传座次图片";
        this.isNewData = true;
      } else {
        this.title = "上传座次EXCEL";
        this.isNewData = true;
      }
      this.dialogFormVisible = true;
    },
    handleShow2(row) {
      // console.log(isHide);
      // console.log(row);
      this.resultList = [];
      this.data1.relId = row.id;
      this.isHide = isHide;
      this.fileData.calendId = row.id;
      this.photoContent.relId = row.id;
      this.photoContent.meetingId = row.meetingId;
      this.calendId = row.id;
      this.flag = 2;
      const obj = {
        calendId: row.id,
      };
      // getDisplayTime
      getDisplayTime(obj).then((res) => {
        // console.log(res.data,"11111")
        this.resourceForm.displayTime = res.data.displayTime;
      });
      getImg(obj).then((res) => {
        let arr = [];
        let resss = {};
        res.data.forEach((item) => {
          arr.push({
            uid: item.id,
            name: item.name + "." + item.suffix,
            url:
              `${process.env.VUE_APP_BASE_API}` +
              "/api/v1/meetingFile/view/" +
              item.id,
          });
        });
        this.productImgs = arr;
        console.log(this.productImgs);
      });
      getRestMeetingCalendar([row.id]).then((res) => {
        this.meetList = res.data;
      });
      if (this.isUpdate == 1) {
        this.title = "上传座次图片";
        this.isNewData = true;
      } else {
        this.title = "上传座次EXCEL";
        this.isNewData = true;
      }
      this.dialogFormVisible = true;
    },
    close() {
      this.resourceForm = this.$options.data().resourceForm;
      this.dialogFormVisible = false;
      // this.$refs.clearFile.clearFiles();
      this.handleRemove();
      this.productImgs = [];
      this.fileList1 = [];
      this.clearFile.id = "";
      // this.$refs.clearFile.value = "";

      // this.$refs["clearFile"].clearFiles();
    },
    save() {
      console.log(this.productImgs, "999999");
      // if (this.productImgs.length == 0) {
      //   this.$message.error("请上传图片!");
      // } else {
      var that_ = this;
      let ids = [];
      this.productImgs.forEach((item) => {
        ids.push(item.uid);
      });
      const data2 = {
        calendarId: this.photoContent.relId,
        meetingId: this.photoContent.meetingId,
        ids: ids,
        list: this.impVehicle,
        displayTime: this.resourceForm.displayTime,
        floor: this.resourceForm.floor,
      };

      this.$refs["resourceForm"].validate((valid) => {
        if (valid) {
          doSave(data2).then(() => {
            this.dialogFormVisible = false;
            this.$message({
              message: "保存成功",
              type: "success",
            });
            this.$parent.fetchData();
          });
        }
      });
      //}
    },
    seatinginfo() {
      var calendId = this.calendId;
      var meetingId = this.photoContent.meetingId;
      this.$router
        .push({
          path: "/generalServices/seatingList",
          query: {
            calendId: calendId,
            meetingId: meetingId,
          },
        })
        .catch(() => {});
    },
    listshow: function () {
      this.seatingListShow = !this.seatingListShow;
    },
    tableSortChange() {},
    setSelectRows(val) {
      this.selectRows = val;
    },

    checkPermission,
  },
};
</script>
<style lang="scss" scoped>
.ninebox {
  width: 40% !important;
}
.fileParent {
  position: relative;
}
.fileParent img {
  position: absolute;
  width: 60px;
  height: 39px;
  left: 0;
  top: 0;
}
.file {
  position: absolute;
  left: 0;
  opacity: 0;
}

.upfile {
  display: inline-block;
  width: 100%;
  border: solid 1px #ddd;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 15px;
  margin-bottom: 5px;
  padding-left: 5px;
}

.table {
  width: 100%;
  color: #666;
  border-collapse: collapse;
  background-color: #fff;
}
.table th,
.table td {
  position: relative;
  min-height: 20px;
  padding: 3px 10px;
  // font-size: 14px;
   @include add-size($font_size_16);
  line-height: 20px;
  border-color: #e6e6e6;
  border-style: solid;
  border-width: 1px;
  text-align: center;
}
.table th {
  font-weight: bold;
  // font-size: 15px !important;
   @include add-size($font_size_16);
}

.el-scrollbar {
  height: 100%;
}

.el-scrollbar__wrap {
  overflow-x: hidden;
}
</style>
