import { instance_1 } from "@/api/axiosRq";

// 获取制度管理列表
export function getSystemList(params) {
  return instance_1({
    url: "/system/management/list",
    method: "get",
    params,
  });
}

// 创建制度
export function createSystem(data) {
  return instance_1({
    url: "/system/management/create",
    method: "post",
    data,
  });
}

// 更新制度
export function updateSystem(data) {
  return instance_1({
    url: "/system/management/update",
    method: "put",
    data,
  });
}

// 删除制度
export function deleteSystem(id) {
  return instance_1({
    url: `/system/management/delete/${id}`,
    method: "delete",
  });
}

// 获取制度详情
export function getSystemDetail(id) {
  return instance_1({
    url: `/system/management/detail/${id}`,
    method: "get",
  });
}

// 下载制度附件
export function downloadSystemAttachment(id) {
  return instance_1({
    url: `/system/management/download/${id}`,
    method: "get",
    responseType: "blob",
  });
}
