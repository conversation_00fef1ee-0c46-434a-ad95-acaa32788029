import request from "@/utils/request";
import qs from "qs";

export function getPage(data) {
    return request({
        url: "/api/v1/meetingDining/arrange/getPage",
        method: "post",
        data,
    });
}

export function getDateArrangeList(data) {
    return request({
        url: "/api/v1/meetingDining/arrange/getDateArrangeList",
        method: "post",
        data,
    });
}

export function checkHasDistribution(data) {
    return request({
        url: "/api/v1/meetingDining/arrange/checkHasDistribution",
        method: "post",
        data,
    });
}

export function updateDateArrange(data) {
    return request({
        url: "/api/v1/meetingDining/arrange/updateDateArrange",
        method: "post",
        data,
    });
}

export function checkForRemove(data) {
    return request({
        url: "/api/v1/meetingDining/arrange/checkForRemove",
        method: "post",
        data,
    });
}

export function removeDateArrange(data) {
    return request({
        url: "/api/v1/meetingDining/arrange/removeDateArrange",
        method: "post",
        data,
    });
}

export function addDateArrange(data) {
    return request({
        url: "/api/v1/meetingDining/arrange/addDateArrange",
        method: "post",
        data,
    });
}

export function getDiningUser(data) {
    return request({
        url: "/api/v1/meetingDining/arrange/getDiningUserByDate",
        method: "post",
        data,
    });
}
