<template>
  <a-col :xs="24" :sm="24" :md="24" :lg="span" :xl="span">
    <div class="right-panel">
      <slot></slot>
    </div>
  </a-col>
</template>

<script>
export default {
  name: "ByuiQueryFormRightPanel",
  props: {
    span: {
      type: Number,
      default: 12,
    },
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="css" scoped>
.right-panel >>> button + button,
.right-panel >>> div + div,
.right-panel >>> button + input {
  margin-left: 10px;
}
</style>
