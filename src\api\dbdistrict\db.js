import request from "@/utils/requestTemp";
import { instance_1 } from "@/api/axiosRq";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/district/list",
    method: "get",
    params: data,
  });
}

export function getById(data) {
  return request({
    url: "/district/getById",
    method: "get",
    params: data,
  });
}

export function edit(data) {
  return request({
    url: "/district/edit",
    method: "post",
    data: data,
  });
}

export function save(data) {
  return request({
    url: "/district/add",
    method: "post",
    data: data,
  });
}


//保存导入图片
export function uploadFilePlan(params, formData) {
  return instance_1({
    url: `/dbDistrictPlan/uploadFilePlan`,
    method: "post",
    params: params,
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

export function getPlanByLiaisonStation(data) {
  return request({
    url: "/dbDistrictPlan/getByLiaisonStation",
    method: "get",
    params: data
  });
}

export function deleteDbById(data) {
  return request({
    url: "/district/deleteById",
    method: "post",
    params: data,
  });
}
