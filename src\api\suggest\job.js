import { instance_yajy } from "@/api/axiosRq";


// 查询定时任务调度列表
export function listJob(query) {
  return instance_yajy({
    url: '/api/v1/quartz/queryList',
    method: 'post',
    data: query
  })
}

// 查询定时任务调度详细
export function getJob(jobId) {
  return instance_yajy({
    url: '/api/v1/quartz/find/' + jobId,
    method: 'post'
  })
}

// 新增定时任务调度
export function addJob(data) {
  return instance_yajy({
    url: '/api/v1/quartz/add',
    method: 'post',
    data: data
  })
}

// 修改定时任务调度
export function updateJob(data) {
  return instance_yajy({
    url: '/api/v1/quartz/update',
    method: 'post',
    data: data
  })
}

// 删除定时任务调度
export function delJob(jobIds) {
  return instance_yajy({
    url: '/api/v1/quartz/remove',
    method: 'post',
    data: jobIds
  })
}

// 导出定时任务
export function exportJob(jobIds) {
  return instance_yajy({
    url: '/api/v1/quartz/exportExcel',
    method: 'post',
    data: jobIds,
    responseType: 'blob'
  })
}

// 任务状态修改
export function changeJobStatus(jobId, status) {
  const data = {
    jobId,
    status
  }
  return instance_yajy({
    url: '/api/v1/quartz/changeStatus',
    method: 'post',
    data: data
  })
}


// 定时任务立即执行一次
export function runJob(jobId, jobGroup) {
  const data = {
    jobId,
    jobGroup
  }
  return instance_yajy({
    url: '/api/v1/quartz/run',
    method: 'post',
    data: data
  })
}
