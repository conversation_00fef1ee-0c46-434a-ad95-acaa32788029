<template>
  <div>
    <a-col :span="spanNum" :class="{ 'overLength': title.length > 5 && spanNum <= 8 }">
      <a-form-model-item  :label="title" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
       <div style="display: flex;align-items: center;">
         <a-input-number v-model="minNum" @change="getMinNum" style="width: 45%;" />
         <p style="width: 10%; text-align: center;">至</p>
         <a-input-number v-model="maxNum" @change="getMaxNum" style="width: 45%;" />
       </div>
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default:  () =>  '年龄范围',
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    minValue: {
      type: [String, Number],
      default: '',
    },
    maxValue: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      minNum: 0,
      maxNum: 0,
    };
  },
  watch: {   
    minValue: {  
      handler(val) {
        this.minNum = val;
      },
      deep: true,
      immediate: true,
    }, 
    maxValue: {  
      handler(val) {
        this.maxNum = val;
      },
      deep: true,
      immediate: true,
    }, 
  },
  methods: {
    getMinNum(val) {
      this.$emit('update:minValue', val)
    },
    getMaxNum(val) {
      this.$emit('update:maxValue', val)
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
