<template>
  <div class="table-container">
    <a-form layout="horizontal"
            :labelCol="{ span: 6 }"
            :wrapperCol="{ span: 18, offset: 0 }">

      <SearchForm @onReset="reset" @onSearch="fetchData" :noMore="true">
        <template v-slot:topSearch>
          <SingleSearch @onEnter="fetchData" :title="'届次名称'" :value.sync="queryForm.levelName" />
          <SingleSelect :title="'届次标识'" :selectList="sessionList" :value.sync="queryForm.levelFlag" />
          <SingleSelect :title="'层级'" :selectList="conventionValue" :showName="'dhmc'" :showValue="'dhDm'" :value.sync="queryForm.dhDm" />
        </template>
      </SearchForm> 

      <a-row>
        <a-col :md="6"
               :sm="24">
          <a-button style="margin: 10px;"
                    type="primary"
                    native-type="submit"
                    @click="handleAdd">新增</a-button>
        </a-col>
      </a-row>
    </a-form>
    <standard-table :columns="columns"
                    :data-source="list"
                    :row-key="(record, index) => { return index; }"
                    :pagination="pagination"
                    :loading="TBloading"
                    @tableClick="clickRow"></standard-table>
    <a-modal :title="title"
             destroy-on-close
             :visible.sync="visibleShow"
             width="600px"
             @cancel="close"
             @ok="confirm">
      <div slot="footer"
           class="dialog-footer"
           style="position: relative; padding-right: 15px; text-align: right;">
        <a-button @click="close">关闭</a-button>
        <a-button :disabled="iShow"
                  type="primary"
                  style="margin-left: 10px;"
                  @click="confirm">确定</a-button>
      </div>

      <a-form-model ref="noticeForm"
                    :label-col="{ span: 6 }"
                    :rules="rules"
                    :wrapper-col="{ span: 16 }"
                    :model="forValue">
        <a-form-model-item label="届次名称："
                           prop="levelName">
          <a-input v-model="forValue.levelName"
                   :disabled="iShow"
                   placeholder="请输入届次名称"></a-input>
        </a-form-model-item>
        <a-form-model-item label="届次数字名称："
                           prop="levelNumName">
          <a-input v-model="forValue.levelNumName"
                   :disabled="iShow"
                   placeholder="请输入届次数字名称"></a-input>
        </a-form-model-item>
        <a-form-model-item label="层级"
                           prop="dhDm">
          <a-select v-model="forValue.dhDm"
                    show-search
                    :disabled="iShow"
                    :filter-option="filterOption"
                    option-filter-prop="children"
                    allow-clear
                    placeholder="请选择层级">
            <a-select-option v-for="item in conventionValue"
                             :key="item.dhDm"
                             :value="item.dhDm">{{ item.dhmc }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="当前届次标识"
                           prop="currentLevel">
          <a-radio-group v-model="forValue.currentLevel"
                         :disabled="iShow">
            <a-radio value="0">非当前届次</a-radio>
            <a-radio value="1">当前届次</a-radio>
          </a-radio-group>
        </a-form-model-item>

        <a-form-model-item label="本届开始时间"
                           prop="levelStartTime">
          <a-date-picker v-model="forValue.levelStartTime"
                         :disabled="iShow"
                         allow-clear
                         value-format="YYYY-MM-DD HH:mm:ss"
                         placeholder="选择本届开始时间"
                         style="width: 100%;"
                         show-time
                         :disabled-date="
              (current) =>
                current && forValue.levelEndTime
                  ? current.valueOf() >=
                    moment(new Date(forValue.levelEndTime)).valueOf()
                  : false
            "></a-date-picker>
        </a-form-model-item>

        <a-form-model-item label="本届结束时间"
                           prop="levelEndTime">
          <a-date-picker v-model="forValue.levelEndTime"
                         :disabled="iShow"
                         allow-clear
                         show-time
                         value-format="YYYY-MM-DD HH:mm:ss"
                         placeholder="选择本届结束时间"
                         style="width: 100%;"
                         :disabled-date="
              (current) =>
                current && forValue.levelStartTime
                  ? moment(new Date(forValue.levelStartTime)).valueOf() >=
                    current.valueOf()
                  : false
            "></a-date-picker>
        </a-form-model-item>
        <a-form-model-item label="选用标志">
          <a-radio-group v-model="forValue.isUse"
                         :disabled="iShow">
            <a-radio value="0">非当前选用标志</a-radio>
            <a-radio value="1">当前选用标志</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { myPagination } from "@/mixins/pagination.js";
import { instance_1 } from "@/api/axiosRq";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
import {
  levelList,
  levelListData,
  addlevelList,
  editlevelList,
  delelevelList,
  conventionData,
} from "@/api/area.js";
export default {
  mixins: [myPagination],
  components:{
    SingleSelect,
    SearchForm,
    SingleSearch,
  },
  data () {
    return {
      TBloading: false,
      visibleShow: false,
      iShow: false,
      list: [],
      getLevel: [],
      conventionValue: [],
      title: "",
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "届次名称",
          align: "center",
          ellipsis: true,
          dataIndex: "levelName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "当前届次标识",
          align: "center",
          ellipsis: true,
          dataIndex: "currentLevel",
          customRender: (text, record, index) => {
            return text == "0" ? (text = "非当前届次") : "当前届次";
          },
        },
        {
          title: "本届开始时间",
          align: "center",
          ellipsis: true,
          dataIndex: "levelStartTime",
          customRender: (text, record, index) => {
            if (text != null || text == "") {
              text = text.slice(0, text.indexOf(" "));
              text = text.replace("T", " ").split("Z").join("").substr(0, 19);
            }
            return text || "/";
          },
        },
        {
          title: "本届结束时间",
          align: "center",
          ellipsis: true,
          dataIndex: "levelEndTime",
          customRender: (text, record, index) => {
            if (text != null || text == "") {
              text = text.slice(0, text.indexOf(" "));
              text = text.replace("T", " ").split("Z").join("").substr(0, 19);
            }
            return text || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleData(record.jcDm);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(record.jcDm);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleDelete(record.jcDm);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      forValue: {
        jcDm: "" /* .届次代码 */,
        dhDm: "" /* 人民代表大会代码 */,
        levelName: "" /* 届次名称 */,
        levelNumName: "" /* 届次数字名称 */,
        currentLevel: "0" /* 当前届次标识  0非当前届次，1当前届次*/,
        levelStartTime: "" /* 本届开始时间 */,
        levelEndTime: "" /* 本届结束时间 */,
        isUse: "0" /* 选用标志   0非当前选用标志，1当前选用标志*/,
      },
      rules: {
        facilityNum: [
          { required: true, message: "设备编号不能为空", trigger: "blur" },
        ],
        modelNumber: [
          { required: true, message: "设备型号不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "设备名称不能为空", trigger: "blur" },
        ],
        remark: [{ required: true, message: "备注不能为空", trigger: "blur" }],
        levelName: [
          { required: true, message: "届次名称不能为空", trigger: "blur" },
        ],
        levelNumName: [
          { required: true, message: "届次数字名称不能为空", trigger: "blur" },
        ],
        dhDm: [
          { required: true, message: "层级不能为空", trigger: "blur" },
        ],
        levelStartTime: [
          {
            required: true,
            message: "本届开始时间不能为空",
            trigger: "change",
          },
        ],
        levelEndTime: [
          {
            required: true,
            message: "本届结束时间不能为空",
            trigger: "change",
          },
        ],
      },
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        levelName: "",
        levelFlag: undefined,
      },
      sessionList: [
        {name: '非当前届次', id: '0'},
        {name: '当前届次', id: '1'},
      ]
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "系统管理");
    this.$store.dispatch("navigation/breadcrumb2", "届次管理");
    this.fetchData();
    this.convention();
    this.leveLData();
  },
  methods: {
    // 点击行
    clickRow (event, record) {
      try {
        let state;
        let tableHeader = event.target.__vue__.column.key;
        tableHeader ? (state = true) : (state = false);
        if (tableHeader) {
          this.handleData(record.jcDm);
        }
      } catch (error) { }
    },
    leveLData () {
      instance_1({
        url: "/queryStatistics/stateChanges/getLevel",
        method: "get",
      }).then((res) => {
        console.log(res);
        if (res.data.code == "0000") {
          this.getLevel = res.data.data;
        }
      });
    },
    async convention () {
      let res = await conventionData();
      if (res.code == "0000") {
        this.conventionValue = res.data;
        console.log(this.conventionValue);
      }
    },
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.fetchData();
    },
    handleAdd () {
      this.title = "新增数据";
      this.visibleShow = true;
      this.iShow = false;
    },
    async confirm () {
      this.$refs["noticeForm"].validate(async (valid) => {
        if (valid) {
          if (! this.forValue.jcDm) {
            let res = await addlevelList(this.forValue);
            if (res.code == "0000") {
              this.$message.success("请求成功");
              this.fetchData();
              this.visibleShow = false;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          } else {
            let res = await editlevelList(this.forValue);
            if (res.code == "0000") {
              this.$message.success("请求成功");
              this.fetchData();
              this.visibleShow = false;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          }
          this.close();
        }
      });
    },
    close () {
      this.forValue = {
        jcDm: "" /* .届次代码 */,
        dhDm: "" /* 人民代表大会代码 */,
        levelName: "" /* 届次名称 */,
        levelNumName: "" /* 届次数字名称 */,
        currentLevel: "0" /* 当前届次标识  0非当前届次，1当前届次*/,
        levelStartTime: "" /* 本届开始时间 */,
        levelEndTime: "" /* 本届结束时间 */,
        isUse: "0" /* 选用标志   0非当前选用标志，1当前选用标志*/,
        isDelete: "",
      };
      this.title = "";
      this.visibleShow = false;
      this.iShow = false;
    },
    async handleDelete (id) {
      this.$baseConfirm(
        "关联数据也将一并删除，你确定要删除选中项吗",
        null,
        () => {
          delelevelList({ id }).then((res) => {
            if (res.code == "0000") {
              this.$baseMessage(res.msg, "success");
              this.fetchData();
            } else {
              this.$baseMessage(res.msg, "error");
            }
          });
        }
      );
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    handleEdit (id) {
      this.title = "编辑数据";
      this.handleData(id, "edit");
    },
    async handleData (id, edit) {
      edit ? (this.title = "编辑数据") : (this.title = "查看数据");
      edit ? (this.iShow = false) : (this.iShow = true);

      let res = await levelListData({ id });
      if (res.code == "0000") {
        // 回显
        let {
          jcDm,
          levelName,
          levelNumName,
          dhDm,
          currentLevel,
          levelStartTime,
          levelEndTime,
          isUse,
          isDelete,
        } = res.data;
        this.forValue.jcDm = jcDm;
        this.forValue.levelName = levelName;
        this.forValue.levelNumName = levelNumName;
        this.forValue.dhDm = dhDm;
        this.forValue.currentLevel = currentLevel;
        this.forValue.levelStartTime = levelStartTime;
        this.forValue.levelEndTime = levelEndTime;
        this.forValue.isUse = isUse;
        this.forValue.isDelete = isDelete;

        this.visibleShow = true;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    async fetchData () {
      let res = await levelList(this.queryForm);
      if (res.code == "200") {
        this.list = res.rows;
        this.pagination.total = res.total;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
  },
};
</script>
<style scoped>
.signData {
  padding: 15px;
}
</style>
