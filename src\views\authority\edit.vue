<template>
  <div class="table-container">
    <a-col style="margin-bottom: 15px;">
      <titleBreacrumb headline="系统管理" title="权限管理" :text="title" />
    </a-col>
    <a-row :gutter="15">
      <a-col
        :xs="24"
        :sm="{ span: 20, offset: 2 }"
        :md="{ span: 20, offset: 2 }"
        :lg="{ span: 14, offset: 5 }"
        :xl="{ span: 12, offset: 6 }"
      >
        <a-form-model
          ref="authorityForm"
          :model="authorityForm"
          :rules="rules"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-model-item label="权限编码" prop="authCode">
            <a-input
              v-model="authorityForm.authCode"
              :disabled="!isNewData"
              autocomplete="off"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="权限名称" prop="authName">
            <a-input
              v-model="authorityForm.authName"
              autocomplete="off"
            ></a-input>
          </a-form-model-item>
          <a-form-model-item label="权限描述" prop="authDesc">
            <a-textarea
              v-model="authorityForm.authDesc"
              autocomplete="off"
            ></a-textarea>
          </a-form-model-item>
          <a-form-model-item label="权限类型" prop="authType">
            <a-select
              v-model="authorityForm.authType"
              placeholder="请选择权限类型"
              style="width: 100%;"
            >
              <a-select-option
                v-for="item in authTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                >{{ item.label }}</a-select-option
              >
            </a-select>
          </a-form-model-item>

          <!-- 权限类型为“数据类型”时，有以下选项 -->
          <el-divider v-if="authorityForm.authType == 3"
            >数据权限设置</el-divider
          >
          <a-form-model-item
            v-show="authorityForm.authType == 3"
            v-for="(target, index) in authorityForm.targets"
            :key="target.key"
            :label="'数据权限目标' + (index + 1)"
          >
            <a-row type="flex" :gutter="10" justify="start">
              <a-col :span="6">
                <a-select
                  v-model="target.targetType"
                  placeholder="请选择数据权限类型"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <a-select-option
                    v-for="item in dataTargetTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-col>
              <a-col :span="14">
                <a-select
                  v-show="target.targetType == 0"
                  v-model="target.dataLevel"
                  placeholder="请选择目标层级"
                  style="width: 100%;"
                  @change="$forceUpdate()"
                >
                  <a-select-option
                    v-for="item in dataLevelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
                 <div class="searchStyle">
                    <a-input
                    v-show="target.targetType == 1"
                    disabled
                    v-model="target.dataOrgName"
                    enter-button
                  >
                  </a-input>
                   <a-button type="primary" icon="search" @click="openOrgTreeDialog(target)">
                   </a-button>
                  </div> 
                <!-- <a-input-search
                  v-show="target.targetType == 1"
                  v-model="target.dataOrgName"
                  autocomplete="off"
                  placeholder="请选择目标机构"
                  :readonly="true"
                  @search="openOrgTreeDialog(target)"
                >
                </a-input-search> --> -->
              </a-col>
              <a-col :span="4">
                <a-button @click="removeDataTarget(target)">删除</a-button>
              </a-col>
            </a-row>
          </a-form-model-item>

          <a-row style="text-align: center;">
            <a-button
              @click="addDataTarget"
              v-show="authorityForm.authType == 3"
              >新增数据权限目标</a-button
            >
            <a-button type="primary" @click="save">确 定</a-button>
          </a-row>
        </a-form-model>
      </a-col>
    </a-row>

    <orgTreeDialog ref="orgTreeDialog" @confirm="confirmOrg"></orgTreeDialog>
  </div>
</template>

<script>
import { getById, doSave, doUpdate } from "@/api/authority";
import orgTreeDialog from "@/views/common/orgTreeDialog.vue";
export default {
  name: "TableEdit",
  data() {
    var checkSubParam = (value, callback, errorMsg, isCheck) => {
      if (!isCheck) {
        callback();
      }
      if (value == undefined || value === "") {
        callback(new Error(errorMsg));
      } else {
        callback();
      }
    };
    var dataTargetTypeValidator = (rule, value, callback) => {
      checkSubParam(
        value,
        callback,
        "请选择数据权限类型",
        this.authorityForm.authType == 3
      );
    };

    return {
      authorityForm: {
        tenantId: this.$tenantId,
        openMode: 0,
        targetValue: null,
        targets: [],
      },
      title: "",
      isNewData: true,
      curDataTargetIndex: null,
      hasDataRelative: false,
      rules: {
        authCode: [
          { required: true, message: "请输入权限编码", trigger: "blur" },
        ],
        authName: [
          { required: true, message: "请输入权限名称", trigger: "blur" },
        ],
        authType: [
          { required: true, message: "请选择权限类型", trigger: "blur" },
        ],
        dataTargetType: [
          { validator: dataTargetTypeValidator, trigger: "blur" },
        ],
      },
      authTypeOptions: [
        {
          value: 1,
          label: "菜单或查阅",
        },
        {
          value: 2,
          label: "操作",
        },
        {
          value: 3,
          label: "数据",
        },
      ],
      dataTargetTypeOptions: [
        {
          value: 0,
          label: "相对",
        },
        {
          value: 1,
          label: "绝对",
        },
      ],
      dataLevelOptions: [
        {
          value: 1,
          label: "所有数据",
        },
        {
          value: 2,
          label: "父级单位及子单位数据",
        },
        {
          value: 3,
          label: "本单位及子单位数据",
        },
        {
          value: 4,
          label: "子单位数据",
        },
      ],
    };
  },
  created() {
    const authId = this.$route.query.authId;
    const authOwnId = this.$route.query.authOwnId;
    const authOwnType = this.$route.query.authOwnType;
    if (!authId) {
      this.title = "新增权限";
      this.isNewData = true;
      this.authorityForm.authOwnId = authOwnId;
      this.authorityForm.authOwnType = authOwnType;
      this.authorityForm.targets.push({
        targetType: 0,
      });
    } else {
      this.title = "编辑权限";
      this.isNewData = false;
      this.initData(authId);
    }
  },
  components: {
    orgTreeDialog,
  },
  methods: {
    goBackFront() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router
        .push({
          path: "/systemManage/authorityManage",
          query: {
            menuId: this.authorityForm.authOwnId,
          },
        })
        .catch(() => {});
      // this.$router.go(-1);
    },
    save() {
      this.$refs["authorityForm"].validate((valid) => {
        if (valid) {
          this.authorityForm.authorityTargetList = [];
          if (this.authorityForm.targets) {
            this.authorityForm.targets.forEach((target) => {
              let targetValue = null;
              if (this.authorityForm.authType == 3) {
                if (target.targetType == 0) {
                  targetValue = target.dataLevel;
                } else {
                  targetValue = target.dataOrgId;
                }
              }
              if (targetValue == null || targetValue == "") {
                return;
              }

              this.authorityForm.authorityTargetList.push({
                targetType: target.targetType,
                targetValue: targetValue,
              });
            });
          }
          if (this.isNewData) {
            doSave(this.authorityForm).then(() => {
              this.$message.success("保存成功");
              this.goBackFront();
            });
          } else {
            doUpdate(this.authorityForm).then(() => {
              this.$message.success("保存成功");
              this.goBackFront();
            });
          }
        }
      });
    },
    initData(authId) {
      getById({ authId: authId }).then((res) => {
        this.authorityForm = res.data;
        this.authorityForm.authOwnType = res.data.authOwnType.value;
        this.authorityForm.authType = res.data.authType.value;
        this.authorityForm.targets = [];
        if (this.authorityForm.authType == 3) {
          if (res.data.authorityTargetList.length > 0) {
            res.data.authorityTargetList.forEach((authTarget) => {
              if (
                authTarget.targetValue == null ||
                authTarget.targetValue == ""
              ) {
                return;
              }
              let target = {
                targetType: authTarget.targetType.value,
              };
              if (authTarget.targetType.value == 0) {
                target.dataLevel = parseInt(authTarget.targetValue);
              } else {
                target.dataOrgId = authTarget.targetValue;
                target.dataOrgName = authTarget.targetExtValue;
              }
              this.authorityForm.targets.push(target);
            });
          }
        }
      });
    },

    // 数据目标
    addDataTarget() {
      this.authorityForm.targets.push({
        targetType: 0,
      });
      this.$forceUpdate();
    },
    removeDataTarget(item) {
      var index = this.authorityForm.targets.indexOf(item);
      if (index !== -1) {
        this.authorityForm.targets.splice(index, 1);
        this.$forceUpdate();
      }
    },

    // 机构选择器
    openOrgTreeDialog(target) {
      let keys = [];
      if (target.dataOrgId) {
        for (let i = 0; i < target.dataOrgId.length; i++) {
          keys.push(target.dataOrgId[i].orgId);
        }
      }
      this.$refs.orgTreeDialog.open(keys);
      this.curDataTargetIndex = this.authorityForm.targets.indexOf(target);
    },
    confirmOrg(checkedNodes) {
      let target = this.authorityForm.targets[this.curDataTargetIndex];
      target.dataOrgId = null;
      target.dataOrgName = null;
      if (checkedNodes.length > 0) {
        target.dataOrgId = checkedNodes[0].orgId;
        target.dataOrgName = checkedNodes[0].orgName;
      }
      this.$set(this.authorityForm.targets, this.curDataTargetIndex, target);
      this.$forceUpdate();
    },
  },
};
</script>
