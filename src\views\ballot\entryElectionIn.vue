<template>
    <div>
        <a-modal title="录入人选举结果" :visible.sync="visible" width="80%" @cancel="close">
            <!-- <a-button type="primary">
                <a-icon type="search" />
            </a-button>-->
            <div class="Steps">
                <a-steps :current="Steps">
                    <a-step title="录入"></a-step>
                    <a-step title="初审"></a-step>
                    <a-step title="终审"></a-step>
                    <a-step title="补选结果录入完成"></a-step>
                </a-steps>
            </div>
            <a-row class="formBox">
                <a-card style="margin-top: 20px;">
                    <a-tabs default-active-key="1" @change="callback" type="card">
                        <a-tab-pane key="1" tab="补选信息">
                            <a-form-model ref="queryForm" :model="addForm" :rules="rules" :label-col="{ span: 8 }"
                                :wrapper-col="{ span: 14 }">
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="届次">
                                            <a-select disabled v-model="isaddForm.JC_DM" allow-clear>
                                                <a-select-option v-for="item in jcDmList" :key="item.jcDm"
                                                    :value="item.jcDm">{{ item.levelName }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>

                                    <a-col span="6">
                                        <a-form-model-item label="姓名" prop="USER_NAME">
                                            <div class="searchStyle">
                                                <a-input v-model="isaddForm.USER_NAME" allow-clear disabled>
                                                </a-input>
                                                <a-button type="primary" icon="search" @click="tiaozhuan">
                                                </a-button>
                                            </div>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="性别" prop="sex">
                                            <a-select disabled v-model="isaddForm.SEX" allow-clear default-first-option>
                                                <a-select-option key="2" value="2">女</a-select-option>
                                                <a-select-option key="1" value="1">男</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="民族" prop="MZMC">
                                            <a-select disabled v-model="isaddForm.MZMC" allow-clear
                                                default-first-option>
                                                <a-select-option v-for="item in nation" :key="item.xh" :value="item.xh">
                                                    {{ item.mzmc }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="会议届别" prop="meetingDifference">
                                            <a-input v-model="addForm.meetingDifference" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="会议次别" prop="meetingNumber">
                                            <a-select v-model="addForm.meetingNumber" allow-clear default-first-option>
                                                <a-select-option v-for="item in meetingDifferenceList" :key="item.id"
                                                    :value="item.name">{{ item.name }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <span>【预选情况】</span>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="实有代表" prop="actualBehalf">
                                            <a-input v-model="addForm.actualBehalf" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="到会代表" prop="arriveBehalf">
                                            <a-input v-model="addForm.arriveBehalf" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="发出票数" prop="invoiceNumber">
                                            <a-input v-model="addForm.invoiceNumber" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="收回票数" prop="recycling">
                                            <a-input v-model="addForm.recycling" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="废票数" prop="abandoned">
                                            <a-input v-model="addForm.abandoned" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="赞成票数" prop="agree">
                                            <a-input v-model="addForm.agree" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="反对票数" prop="against">
                                            <a-input v-model="addForm.against" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="弃权票数" prop="waiver">
                                            <a-input v-model="addForm.waiver" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="无效票数" prop="failure">
                                            <a-input v-model="addForm.failure" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="当选情况" prop="dxqkDm">
                                            <a-select v-model="addForm.dxqkDm" allow-clear default-first-option>
                                                <a-select-option v-for="item in dangxuan" :key="item.electedStateDm"
                                                    :value="item.electedStateDm">{{ item.electedStateName }}
                                                </a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="预选日期" prop="yxrq">
                                            <a-date-picker value-format="YYYY-MM-DD HH:mm:ss" show-time
                                                style="width:100%" v-model="addForm.yxrq"></a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="24">
                                        <a-form-model-item label="预选备注" prop="remark" :label-col="{ span: 2 }"
                                            :wrapper-col="{ span: 22 }">
                                            <a-textarea v-model="addForm.remark" rows="5" allow-clear></a-textarea>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                            </a-form-model>
                            <span>【选举情况】</span>
                            <a-form-model ref="queryForm" :model="queryForm" :rules="rulesForm" :label-col="{ span: 8 }"
                                :wrapper-col="{ span: 14 }">
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="实有代表" prop="actualBehalf">
                                            <a-input v-model="queryForm.actualBehalf" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="到会代表" prop="arriveBehalf">
                                            <a-input v-model="queryForm.arriveBehalf" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="发出票数" prop="invoiceNumber">
                                            <a-input v-model="queryForm.invoiceNumber" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="收回票数" prop="recycling">
                                            <a-input v-model="queryForm.recycling" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>

                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="废票数" prop="abandoned">
                                            <a-input v-model="queryForm.abandoned" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="赞成票数" prop="agree">
                                            <a-input v-model="queryForm.agree" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="反对票数" prop="against">
                                            <a-input v-model="queryForm.against" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="弃权票数" prop="waiver">
                                            <a-input v-model="queryForm.waiver" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>

                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="无效票数" prop="failure">
                                            <a-input v-model="queryForm.failure" allow-clear></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="当选情况" prop="dxqkDm">
                                            <a-select v-model="queryForm.dxqkDm" allow-clear default-first-option>
                                                <a-select-option v-for="item in dangxuan" :key="item.electedStateDm"
                                                    :value="item.electedStateDm">{{ item.electedStateName }}
                                                </a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="选举日期" prop="electionTime">
                                            <a-date-picker value-format="YYYY-MM-DD HH:mm:ss" allow-clear show-time
                                                style="width:100%" v-model="queryForm.electionTime"></a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="选举情况" prop="electionSituation">
                                            <a-select v-model="queryForm.electionSituation" allow-clear
                                                default-first-option>
                                                <a-select-option v-for="item in electionSituationList"
                                                    :key="item.electionStateDm" :value="item.electionStateDm">{{
                                                            item.electionStateName
                                                    }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>

                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="决定单位" prop="verifyUnit">
                                            <a-select v-model="queryForm.verifyUnit" allow-clear>
                                                <a-select-option v-for="item in juedinList" :key="item.verifyUnitDm"
                                                    :value="item.verifyUnitDm">{{ item.verifyUnitName }}
                                                </a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="24">
                                        <a-form-model-item prop="electionResult" :label-col="{ span: 2 }"
                                            :wrapper-col="{ span: 22 }">
                                            <a-button slot="label" @click="scjg">生成结果</a-button>
                                            <a-textarea v-model="queryForm.electionResult" allow-clear rows="6">
                                            </a-textarea>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                            </a-form-model>
                        </a-tab-pane>
                        <a-tab-pane key="2" tab="补选人资料" force-render>
                            <span>【补选人基本信息】</span>
                            <a-form-model ref="isaddForm" :model="isaddForm" :rules="rulesisaddForm"
                                :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="姓名" prop="USER_NAME">
                                            <a-input v-model="isaddForm.USER_NAME" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="性别" prop="SEX">
                                            <a-select disabled v-model="isaddForm.SEX" allow-clear default-first-option>
                                                <a-select-option key="2" value="2">女</a-select-option>
                                                <a-select-option key="1" value="1">男</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="民族" prop="MZMC">
                                            <a-select disabled v-model="isaddForm.MZMC" allow-clear
                                                default-first-option>
                                                <a-select-option v-for="item in nation" :key="item.xh" :value="item.xh">
                                                    {{ item.mzmc }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="籍贯" prop="NATIVE_PLACE">
                                            <a-input v-model="isaddForm.NATIVE_PLACE" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="身份证号" prop="SFZ_DM">
                                            <a-input v-model="isaddForm.SFZ_DM" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="出生日期" prop="BIRTHDAY">
                                            <a-date-picker disabled value-format="YYYY-MM-DD" allow-clear show-time
                                                style="width:100%" v-model="isaddForm.BIRTHDAY"></a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="党派" prop="POLITICS_STATUS_NAME">
                                            <a-select disabled v-model="isaddForm.POLITICS_STATUS_NAME" allow-clear
                                                default-first-option>
                                                <a-select-option v-for="item in parties" :key="item.zzmmDm"
                                                    :value="item.zzmmDm">{{ item.politicsStatusName }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="加入日期" prop="JOIN_TIME">
                                            <a-date-picker disabled value-format="YYYY-MM-DD HH:mm:ss" show-time
                                                style="width:100%" v-model="isaddForm.JOIN_TIME"></a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="参加工作日期" prop="JOIN_WORK_TIME">
                                            <a-date-picker disabled value-format="YYYY-MM-DD HH:mm:ss" show-time
                                                style="width:100%" v-model="isaddForm.JOIN_WORK_TIME"></a-date-picker>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="职业构成" prop="ZYGCMC">
                                            <a-input v-model="isaddForm.ZYGCMC" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="名额来源" prop="MELYMC">
                                            <!-- @change="selectGoodsByGroupId($event)" -->
                                            <a-select disabled v-model="isaddForm.MELYMC" allow-clear
                                                default-first-option>
                                                <a-select-option v-for="item in sjmelyDm" :key="item.melyDm"
                                                    :value="item.melyDm">{{ item.melymc }}</a-select-option>
                                            </a-select>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <span>【教育情况】</span>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="全日制教育" prop="QRZXLMC">
                                            <a-input v-model="isaddForm.QRZXLMC" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="毕业院校" prop="FULL_SCHOOL">
                                            <a-input v-model="isaddForm.FULL_SCHOOL" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="系及专业" prop="FULL_MAJOR">
                                            <a-input v-model="isaddForm.FULL_MAJOR" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="在职教育" prop="ZZJYXLMC">
                                            <a-input v-model="isaddForm.ZZJYXLMC" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="毕业院校" prop="JOB_SCHOOL">
                                            <a-input v-model="isaddForm.JOB_SCHOOL" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="系及专业" prop="SERVICE_EDUCATION">
                                            <a-input v-model="isaddForm.SERVICE_EDUCATION" allow-clear disabled>
                                            </a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="工作单位" prop="WORK_UNIT">
                                            <a-input v-model="isaddForm.WORK_UNIT" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="职位" prop="DUTY">
                                            <a-input v-model="isaddForm.DUTY" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="职称" prop="DUTY_NAME">
                                            <a-input v-model="isaddForm.DUTY_NAME" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <span>联系方式</span>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="单位地址" prop="UNIT_ADDRESS">
                                            <a-input v-model="isaddForm.UNIT_ADDRESS" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="单位电话" prop="UNIT_PHONE">
                                            <a-input v-model="isaddForm.UNIT_PHONE" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="单位邮编" prop="UNIT_POSTAL_CODE">
                                            <a-input v-model="isaddForm.UNIT_POSTAL_CODE" allow-clear disabled>
                                            </a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="电子邮件" prop="EMAIL">
                                            <a-input v-model="isaddForm.EMAIL" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span="6">
                                        <a-form-model-item label="家庭地址" prop="HOUSE_ADDRESS">
                                            <a-input v-model="isaddForm.HOUSE_ADDRESS" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="家庭电话" prop="HOUSE_PHONE">
                                            <a-input v-model="isaddForm.HOUSE_PHONE" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="家庭邮编" prop="HOUSE_POSTAL_CODE">
                                            <a-input v-model="isaddForm.HOUSE_POSTAL_CODE" allow-clear disabled>
                                            </a-input>
                                        </a-form-model-item>
                                    </a-col>
                                    <a-col span="6">
                                        <a-form-model-item label="手机" prop="PHONE">
                                            <a-input v-model="isaddForm.PHONE" allow-clear disabled></a-input>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-form-model-item>
                                        <a-col span="6" push="8">
                                            <a-checkbox disabled v-model="isaddForm.IS_RETU_OVER_CHIN" @change="change"
                                                value="IS_RETU_OVER_CHIN" name="IS_RETU_OVER_CHIN">是否归侨侨眷</a-checkbox>
                                        </a-col>
                                        <a-col span="6" push="10">
                                            <a-checkbox disabled v-model="isaddForm.IS_PEAS_WORK" @change="change"
                                                value="IS_PEAS_WORK" name="IS_PEAS_WORK">是否农民工</a-checkbox>
                                        </a-col>
                                        <a-col span="6" push="12">
                                            <a-checkbox disabled v-model="isaddForm.IS_ELEC_REPR" @change="change"
                                                value="IS_ELEC_REPR" name="IS_ELEC_REPR">是否连任代表</a-checkbox>
                                        </a-col>
                                        <a-col span="6" push="14">
                                            <a-checkbox disabled v-model="isaddForm.IS_TWO_OR_MORE_REPR"
                                                @change="change" value="IS_TWO_OR_MORE_REPR" name="IS_TWO_OR_MORE_REPR">
                                                是否同任两级以上代表</a-checkbox>
                                        </a-col>
                                    </a-form-model-item>
                                </a-row>
                                <a-row>
                                    <a-col span="24">
                                        <a-form-model-item label="简历" prop="jl" :label-col="{ span: 2 }"
                                            :wrapper-col="{ span: 22 }">
                                            <a-textarea v-model="isaddForm.RESUME" rows="4" allow-clear disabled>
                                            </a-textarea>
                                        </a-form-model-item>
                                    </a-col>
                                </a-row>
                            </a-form-model>
                        </a-tab-pane>
                    </a-tabs>
                </a-card>
            </a-row>
            <div slot="footer" class="dialog-footer">
                <a-button v-if="isRecordIn.ID == undefined" @click="draft">暂存为草稿</a-button>
                <a-button type="primary" @click="save" v-if="isRecordIn.ID == undefined">保存并送审</a-button>
                <a-button type="primary" @click="shenheIn" v-if="isRecordIn.ID != undefined" v-show="Istitle == '选举结果'">
                    审核</a-button>
                <!-- <a-button v-if="isRecordIn.ID != undefined" @click="close">关闭</a-button>
                <a-button v-if="isRecordIn.ID == undefined" @click="quxiao">取消</a-button> -->

                <a-button @click="quxiao">取消</a-button>
            </div>
        </a-modal>
        <xuanjuejieguo ref="xuanjuejieguo" @dataValue="dataValue"></xuanjuejieguo>
        <submitForCensorship ref="submitForCensorship" :procInstId="procInstId" @complete="handleComplete" :ids="ids">
        </submitForCensorship>
    </div>
</template>

<script>
import xuanjuejieguo from "@/views/ballot/xuanjuejieguo"
import submitForCensorship from '@/views/common/submitForCensorship';

import { getbxjgbcompleteApi, getdbxjjghxrqrbscompleteApi } from "@/api/xjgw";
import { getLevelListApi, getNationApi, getPoliticalApi, getRecommendApi, getcommongetAddedselecListApi, getAdministrativeStateListApi, registerApi, getfindgetByIdApi, dbxjjgregisterApi, getdbxjjgfindbyidgetByIdApi, getgetVerifyUnitListApi, getElectionStateListApi, getdictCodeElectionStateListApi } from "@/api/representativeElection/candidateApi.js"
export default {
    components: { xuanjuejieguo, submitForCensorship },
    props: {
        // neWid: {
        //     type: String,
        //     default: "",
        // },
        isRecord: {
            type: Object
        }

    },
    data() {
        return {
            Steps: 0,//进度
            Istitle: "",
            isRecordIn: '',
            currentState: '',
            isShowis: false,
            xzqhDmList: {},
            isaddForm: {},
            //会议名称
            // areaNameList: {},
            // 上级名额来源
            sjmelyDm: {
                melyDm: "",
            },
            juedinList: [
                { id: 1, name: "越秀区人大" },
                { id: 2, name: "番禺区人大" },
                { id: 3, name: "南沙区人大" },
                { id: 4, name: "花都区人大" },
            ],
            dangxuan: [
                { id: 1, name: "候选人当选" },
                { id: 2, name: "代表联名" },
            ],
            electionSituationList: [
                { id: 1, name: "当选" },
                { id: 2, name: "落选" },
            ],
            distinctionList: [
                { id: 1, name: "第一届" },
                { id: 2, name: "第二届" },
                { id: 3, name: "第三届" },
                { id: 4, name: "第四届" },
                { id: 5, name: "第五届" },
                { id: 6, name: "第六届" },
                { id: 7, name: "第七届" },
                { id: 8, name: "第八届" },
                { id: 9, name: "第九届" },
                { id: 10, name: "第十届" },
                { id: 11, name: "第十一届" },
                { id: 12, name: "第十二届" },
                { id: 13, name: "第十三届" },
                { id: 14, name: "第十四届" },
                { id: 15, name: "第十五届" },
                { id: 16, name: "第十六届" },
                { id: 17, name: "第十七届" },
                { id: 18, name: "第十八届" },
                { id: 19, name: "第十九届" },
                { id: 20, name: "第二十届" },
            ],
            meetingDifferenceList: [
                { id: 1, name: "第一次会议" },
                { id: 2, name: "第二次会议" },
                { id: 3, name: "第三次会议" },
                { id: 4, name: "第四次会议" },
                { id: 5, name: "第五次会议" },
            ],
            areaNameList: [
                { id: 1, name: "常委会会议" },
                { id: 2, name: "人大代表大会" },
            ],
            ids: [],
            procInstId: '',
            //党派
            parties: {},
            //民族
            nation: {},
            //届别
            jcDmList: [],
            visible: false,
            userId: '',
            //预选情况
            addForm: {
                jcDm: "",
                meetingDifference: "",
                meetingNumber: "",
                actualBehalf: "",
                arriveBehalf: "",
                invoiceNumber: "",
                recycling: "",
                abandoned: "",
                agree: "",
                against: "",
                waiver: "",
                failure: "",
                dxqkDm: "",
                yxrq: "",
                remark: "",
            },
            //选举情况
            queryForm: {
                actualBehalf: "",
                arriveBehalf: "",
                invoiceNumber: "",
                recycling: "",
                abandoned: "",
                agree: "",
                against: "",
                waiver: "",
                failure: "",
                dxqkDm: "",
                verifyUnit: "",
                electionResult: "",
                electionTime: '',
                electionSituation: "",
            },
            // 校验
            rules: {
                meetingDifference: [{ required: true, message: "请填写会议届别", trigger: "blur" }],
                meetingNumber: [{ required: true, message: "请选择会议次别", trigger: "change" }],
            },
            rulesForm: {
                meetingDifference: [{ required: true, message: "请填写会议届别", trigger: "blur" }],
                meetingNumber: [{ required: true, message: "请选择会议次别", trigger: "change" }],
                actualBehalf: [{ required: true, message: "请填写实有代表", trigger: "blur" }],
                arriveBehalf: [{ required: true, message: "请填写到会代表", trigger: "blur" }],
                invoiceNumber: [{ required: true, message: "请填写发出票数", trigger: "blur" }],
                recycling: [{ required: true, message: "请填写收回票数", trigger: "blur" }],
                abandoned: [{ required: true, message: "请填写废票数", trigger: "blur" }],
                agree: [{ required: true, message: "请填写赞成票数", trigger: "blur" }],
                against: [{ required: true, message: "请填写反对票数", trigger: "blur" }],
                waiver: [{ required: true, message: "请填写弃权票数", trigger: "blur" }],
                failure: [{ required: true, message: "请填写无效票数", trigger: "blur" }],
                dxqkDm: [{ required: true, message: "请选择当选情况", trigger: "change" }],
                electionTime: [{ required: true, message: "请选择选举日期", trigger: "change" }],
                electionSituation: [{ required: true, message: "请选择选举情况", trigger: "change" }],
                verifyUnit: [{ required: true, message: "请选择决定单位", trigger: "change" }],
                electionResult: [{ required: true, message: "请选择选举结果", trigger: "change" }],
            },
            rulesisaddForm: {},
        }
    },
    created() {
        // this.isRecordIn = this.isRecord
        // console.log(this.isRecordIn, ' this.isRecordIn');
        this.getLevelListFn()
        this.getNationfn()
        this.getPoliticalfn()
        this.getRecommendfn()
        // this.getcommongetAddedselecList()
        // this.getAdministrativeStateList()
        this.shenhe()
        this.dataValue()
        this.getgetVerifyUnitList()
        this.getElectionStateList()
        this.getdictCodeElectionStateList()
    },
    methods: {
        // 
        //生成结果
        scjg() {
            console.log(1 + 2 + 3);
            console.log(this.queryForm.agree +
                this.queryForm.against +
                this.queryForm.waiver +
                this.queryForm.failure);
            let strArr = [
                //实有代表
                "actualBehalf",
                //到会代表
                "arriveBehalf",
                //发出票数
                "invoiceNumber",
                // 收回票数
                "recycling",
                // 废票数
                "abandoned",
                //赞成票数
                "agree",
                // 反对票数
                "against",
                // 弃权票数
                "waiver",
                // 无效票数
                "failure",
            ];
            let allHas = true;
            strArr.map((item) => {
                this.queryForm[item] === undefined || this.queryForm[item] === ""
                    ? (allHas = false)
                    : "";
            });
            if (!allHas) return this.$message.error("请完善出缺投票结果信息");
            if (
                parseInt(this.queryForm.recycling) !=
                parseInt(this.queryForm.agree) +
                parseInt(this.queryForm.against) +
                parseInt(this.queryForm.waiver) +
                parseInt(this.queryForm.failure)
            )
                return this.$message.error(
                    "赞成票数、反对票数、弃权票数、无效票数的总和要等于收回票数"
                );
            if (!this.isaddForm.USER_NAME) return this.$message.error("请选择人员数据");
            this.queryForm.electionResult = `人民代表大会常务委员会第次会议决定，接受${this.isaddForm.USER_NAME}辞去广州市第十届人民代表大会代表职务的请求。人民代表大会实有组成人员${this.queryForm.actualBehalf}名，实际出席本次会议到会人员${this.queryForm.arriveBehalf}名。表决结果，赞成${this.queryForm.agree}票，反对${this.queryForm.against}票，弃权${this.queryForm.waiver}票。接受杜传贵辞职请求的成票超过全体常务委员会组成人员的半数,符含法律规定。`;
            this.$forceUpdate();
        },
        //送审
        shenheIn() {
            this.$refs.submitForCensorship.visible = true;
        },
        //审核
        shenhe() {
            if (this.visible == true) {
                // console.log(this.newIDs);
                console.log(this.isRecordIn.JC_DM, 'this.isRecordIn.JC_DM');
                if (this.isRecordIn.ID != undefined) {
                    let dbId = this.isRecordIn.USER_ID
                    let jcDm = this.isRecordIn.JC_DM;

                    // let dbId = this.isRecordIn.dbId
                    // let jcDm = this.isRecordIn.jcDm
                    console.log(jcDm, dbId);

                    let params = {
                        dbId,
                        jcDm,
                    }
                    // let id = this.newIDs
                    getdbxjjgfindbyidgetByIdApi(params).then(res => {
                        console.log(res, 'res.data.datares.data.data');
                        if (res.data.code == '0000') {
                            this.isaddForm = res.data.data.candidate
                            // let a = ''
                            // a = res.data.data.election.jcDm.jcDm
                            // this.addForm.jcDm = a
                            this.isaddForm.IS_RETU_OVER_CHIN = res.data.data.candidate.IS_RETU_OVER_CHIN
                            this.isaddForm.IS_PEAS_WORK = res.data.data.candidate.IS_PEAS_WORK
                            this.isaddForm.IS_ELEC_REPR = res.data.data.IS_ELEC_REPR
                            this.isaddForm.IS_TWO_OR_MORE_REPR = res.data.data.candidate.IS_TWO_OR_MORE_REPR
                            if (this.isaddForm.IS_RETU_OVER_CHIN == '1') {
                                this.isaddForm.IS_RETU_OVER_CHIN = true
                            } else {
                                this.isaddForm.IS_RETU_OVER_CHIN = false
                            }
                            if (this.isaddForm.IS_PEAS_WORK == '1') {
                                this.isaddForm.IS_PEAS_WORK = true
                            } else {
                                this.isaddForm.IS_PEAS_WORK = false
                            }
                            if (this.isaddForm.IS_ELEC_REPR == '1') {
                                this.isaddForm.IS_ELEC_REPR = true
                            } else {
                                this.isaddForm.IS_ELEC_REPR = false
                            }
                            if (this.isaddForm.IS_TWO_OR_MORE_REPR == '1') {
                                this.isaddForm.IS_TWO_OR_MORE_REPR = true
                            } else {
                                this.isaddForm.IS_TWO_OR_MORE_REPR = false
                            }
                            console.log(res.data.data, 'res.data.data.election.choosesituation');
                            if (res.data.data.election.choosesituation != null) {
                                this.addForm = res.data.data.election.choosesituation;
                                this.addForm.meetingDifference = res.data.data.election.meetingDifference;//放外面
                                this.addForm.meetingNumber = res.data.data.election.meetingNumber;//放外面
                            }
                            if (res.data.data.election.electionsituation != null) {
                                this.queryForm = res.data.data.election.electionsituation
                            }

                            //有bug这里
                            this.currentState = res.data.data.election.currentState.currStateDm
                            console.log(this.currentState);
                            if (this.currentState == '41') {
                                this.isShowis = false
                            } else {
                                this.isShowis = true
                            }
                            switch (res.data.data.election.currentState.currStateDm) {
                                case '11':
                                    this.Steps = 0
                                    break;
                                case '21':
                                    this.Steps = 2
                                    break;
                                case '31':
                                    this.Steps = 3
                                    break;
                                case '41':
                                    this.Steps = 4
                                    break;
                                case '51':
                                    this.Steps = 4
                                    break;
                            }
                            this.procInstId = res.data.data.election.procInstId
                            this.ids.push(res.data.data.election.id)

                        } else {
                        }
                    })
                }
            }

        },
        //子组件传来的值
        dataValue(val) {
            if (val != undefined) {
                for (var i = 0; i < val.length; i++) {
                    this.isaddForm = val[i]
                    this.queryForm = val[i]
                    this.userId = val[i].DB_ID
                    this.isaddForm.IS_RETU_OVER_CHIN = val[i].IS_RETU_OVER_CHIN
                    this.isaddForm.IS_PEAS_WORK = val[i].IS_PEAS_WORK
                    this.isaddForm.IS_ELEC_REPR = val[i].IS_ELEC_REPR
                    this.isaddForm.IS_TWO_OR_MORE_REPR = val[i].IS_TWO_OR_MORE_REPR
                }
            }

            if (this.isaddForm.IS_RETU_OVER_CHIN == '1') {
                this.isaddForm.IS_RETU_OVER_CHIN = true
            } else {
                this.isaddForm.IS_RETU_OVER_CHIN = false
            }
            if (this.isaddForm.IS_PEAS_WORK == '1') {
                this.isaddForm.IS_PEAS_WORK = true
            } else {
                this.isaddForm.IS_PEAS_WORK = false
            }
            if (this.isaddForm.IS_ELEC_REPR == '1') {
                this.isaddForm.IS_ELEC_REPR = true
            } else {
                this.isaddForm.IS_ELEC_REPR = false
            }
            if (this.isaddForm.IS_TWO_OR_MORE_REPR == '1') {
                this.isaddForm.IS_TWO_OR_MORE_REPR = true
            } else {
                this.isaddForm.IS_TWO_OR_MORE_REPR = false
            }

        },
        //跳转到查询列表
        tiaozhuan() {
            this.$refs.xuanjuejieguo.visible = true
        },
        change() { },
        //取消
        quxiao() {
            this.ids = []
            this.Istitle = ''
            this.visible = false
        },
        //清除绑定
        qcbd() {
            this.Steps = 0;
            // this.isaddForm.JC_DM = ''
            this.addForm.meetingDifference = ''
            this.addForm.meetingNumber = ''
            this.addForm.actualBehalf = ''
            this.addForm.arriveBehalf = ''
            this.addForm.invoiceNumber = ''
            this.addForm.recycling = ''
            this.addForm.abandoned = ''
            this.addForm.agree = ''
            this.addForm.against = ''
            this.addForm.waiver = ''
            this.addForm.failure = ''
            this.addForm.supplementDate = ''
            this.addForm.results = ''
            this.addForm.dxqkDm = ''
            this.addForm.yxrq = ''
            this.addForm.remark = ''
            this.dxqkDm = ''
            this.yxrq = ''
            this.remark = ''
            this.queryForm.actualBehalf = ''
            this.queryForm.arriveBehalf = ''
            this.queryForm.invoiceNumber = ''
            this.queryForm.recycling = ''
            this.queryForm.abandoned = ''
            this.queryForm.agree = ''
            this.queryForm.against = ''
            this.queryForm.waiver = ''
            this.queryForm.failure = ''
            this.queryForm.dxqkDm = ''
            this.queryForm.verifyUnit = ''
            this.queryForm.electionResult = ''
            this.queryForm.electionTime = ''
            this.queryForm.electionSituation = ''
            this.isaddForm.IS_RETU_OVER_CHIN = false
            this.isaddForm.IS_PEAS_WORK = false
            this.isaddForm.IS_ELEC_REPR = false
            this.isaddForm.IS_TWO_OR_MORE_REPR = false
            this.isaddForm.USER_NAME = ''
            this.isaddForm.SEX = ''
            this.isaddForm.MZMC = ''
            this.isaddForm.NATIVE_PLACE = ''
            this.isaddForm.SFZ_DM = ''
            this.isaddForm.BIRTHDAY = ''
            this.isaddForm.POLITICS_STATUS_NAME = ''
            this.isaddForm.JOIN_TIME = ''
            this.isaddForm.JOIN_WORK_TIME = ''
            this.isaddForm.ZYGCMC = ''
            this.isaddForm.MELYMC = ''
            this.isaddForm.QRZXLMC = ''
            this.isaddForm.FULL_SCHOOL = ''
            this.isaddForm.FULL_MAJOR = ''
            this.isaddForm.ZZJYXLMC = ''
            this.isaddForm.JOB_SCHOOL = ''
            this.isaddForm.SERVICE_EDUCATION = ''
            this.isaddForm.WORK_UNIT = ''
            this.isaddForm.DUTY = ''
            this.isaddForm.DUTY_NAME = ''
            this.isaddForm.UNIT_ADDRESS = ''
            this.isaddForm.UNIT_PHONE = ''
            this.isaddForm.UNIT_POSTAL_CODE = ''
            this.isaddForm.EMAIL = ''
            this.isaddForm.HOUSE_ADDRESS = ''
            this.isaddForm.HOUSE_PHONE = ''
            this.isaddForm.HOUSE_POSTAL_CODE = ''
            this.isaddForm.PHONE = ''
            this.isaddForm.RESUME = ''
        },
        //关闭
        close() {
            this.ids = []
            // Object.assign(this.$data, this.$options.data());
            this.visible = false
            this.Istitle = ''
            if (this.isRecordIn.ID != undefined) {
                this.qcbd()
                this.isRecordIn.ID = ''
                this.currentState = ''
                this.$emit('handleClearId')
                this.$refs["queryForm"].clearValidate();
            }
            this.qcbd()
            this.currentState = ''
            this.$emit('handleClearId')
            this.$refs["queryForm"].clearValidate();
            // this.getLevelListFn()
        },
        callback(e) { },
        //保存并送审
        save() {
            console.log(this.isaddForm, 'isaddForm.JC_DMisaddForm.JC_DM');
            //校验
            this.$refs.queryForm.validate(async valid => {
                if (valid) {
                    let dbId = this.userId

                    //预选情况
                    let choosesituation = {
                        dbId,
                        jcDm: this.isaddForm.JC_DM,
                        // personnel: this.addForm.personnel,
                        meetingDifference: this.addForm.meetingDifference,
                        meetingNumber: this.addForm.meetingNumber,
                        actualBehalf: this.addForm.actualBehalf,
                        arriveBehalf: this.addForm.arriveBehalf,
                        invoiceNumber: this.addForm.invoiceNumber,
                        recycling: this.addForm.recycling,
                        abandoned: this.addForm.abandoned,
                        agree: this.addForm.agree,
                        against: this.addForm.against,
                        waiver: this.addForm.waiver,
                        failure: this.addForm.failure,
                        dxqkDm: this.addForm.dxqkDm,
                        yxrq: this.addForm.yxrq,
                        remark: this.addForm.remark,
                    }
                    //选举情况
                    let electionsituation = {
                        dbId,
                        jcDm: this.isaddForm.JC_DM,
                        actualBehalf: this.queryForm.actualBehalf,
                        arriveBehalf: this.queryForm.arriveBehalf,
                        invoiceNumber: this.queryForm.invoiceNumber,
                        recycling: this.queryForm.recycling,
                        abandoned: this.queryForm.abandoned,
                        agree: this.queryForm.agree,
                        against: this.queryForm.against,
                        waiver: this.queryForm.waiver,
                        failure: this.queryForm.failure,
                        dxqkDm: this.queryForm.dxqkDm,
                        verifyUnit: this.queryForm.verifyUnit,
                        electionResult: this.queryForm.electionResult,
                        electionTime: this.queryForm.electionTime,
                        electionSituation: this.queryForm.electionSituation,
                    }
                    let jcDm = {
                        jcDm: this.isaddForm.JC_DM
                    }
                    let currentState = {
                        currStateDm: 21
                    }
                    let params = {
                        dbId,
                        jcDm,
                        currentState,
                        electionsituation,
                        choosesituation,
                        meetingDifference: this.addForm.meetingDifference,//放外面
                        meetingNumber: this.addForm.meetingNumber,//放外面
                        // addedSelect,
                    }
                    dbxjjgregisterApi(params).then(res => {
                        console.log(res, 77777777777);
                        if (res.data.code == '0000') {
                            this.procInstId = res.data.data.procInstId
                            this.ids.push(res.data.data.id)
                            this.$refs.submitForCensorship.visible = true;
                            this.$message.success(res.data.msg)
                        } else {
                            this.$message.error('选举人已存在')
                        }
                    })
                } else {
                    this.$message.error("校验失败")
                }
            })

        },

        //送审发送
        handleComplete(data) {
            getdbxjjghxrqrbscompleteApi(data).then(res => {
                if (res.data.code == '0000') {
                    this.$emit('handleClearId')
                    this.visible = false
                    this.ids = []
                    this.$refs.submitForCensorship.successComplete()
                    this.$message.success(res.data.msg)
                } else {
                    this.$message.error(res.data.msg)
                }
            })
        },
        //暂存为草稿
        draft() {
            //校验
            this.$refs.queryForm.validate(async valid => {
                if (valid) {
                    let dbId = this.userId
                    //预选情况
                    let choosesituation = {
                        dbId,
                        jcDm: this.addForm.jcDm,
                        // personnel: this.addForm.personnel,
                        meetingDifference: this.addForm.meetingDifference,
                        meetingNumber: this.addForm.meetingNumber,
                        actualBehalf: this.addForm.actualBehalf,
                        arriveBehalf: this.addForm.arriveBehalf,
                        invoiceNumber: this.addForm.invoiceNumber,
                        recycling: this.addForm.recycling,
                        abandoned: this.addForm.abandoned,
                        agree: this.addForm.agree,
                        against: this.addForm.against,
                        waiver: this.addForm.waiver,
                        failure: this.addForm.failure,
                        dxqkDm: this.addForm.dxqkDm,
                        yxrq: this.addForm.yxrq,
                        remark: this.addForm.remark,
                    }
                    //选举情况
                    let electionsituation = {
                        dbId,
                        jcDm: this.addForm.jcDm,
                        actualBehalf: this.queryForm.actualBehalf,
                        arriveBehalf: this.queryForm.arriveBehalf,
                        invoiceNumber: this.queryForm.invoiceNumber,
                        recycling: this.queryForm.recycling,
                        abandoned: this.queryForm.abandoned,
                        agree: this.queryForm.agree,
                        against: this.queryForm.against,
                        waiver: this.queryForm.waiver,
                        failure: this.queryForm.failure,
                        dxqkDm: this.queryForm.dxqkDm,
                        verifyUnit: this.queryForm.verifyUnit,
                        electionResult: this.queryForm.electionResult,
                        electionTime: this.queryForm.electionTime,
                        electionSituation: this.queryForm.electionSituation,
                    }
                    let jcDm = {
                        jcDm: this.addForm.jcDm
                    }
                    let currentState = {
                        currStateDm: 21
                    }
                    let params = {
                        dbId,
                        jcDm,
                        currentState,
                        electionsituation,
                        choosesituation,
                        meetingDifference: this.addForm.meetingDifference,//放外面
                        meetingNumber: this.addForm.meetingNumber,//放外面
                        // addedSelect,
                    }
                    dbxjjgregisterApi(params).then(res => {
                        console.log(res, 77777777777);
                        if (res.data.code == '0000') {
                            this.$message.success(res.data.msg)
                        } else {
                            this.$message.error('选举人已存在')
                        }
                    })
                } else {
                    this.$message.error("校验失败")
                }
            })
        },
        //获取当前届次下拉数据列表
        async getLevelListFn() {
            const res = await getLevelListApi()
            if (res.data.code === "0000") {
                this.jcDmList = res.data.data
                // console.log(this.periods[0].jcDm, '00');
                this.addForm.jcDm = this.jcDmList[0].jcDm; //改过
            }
        },
        // 民族下拉数据列表
        async getNationfn() {
            let res = await getNationApi()
            if (res.data.code == "0000") {
                this.nation = res.data.data
            }
        },
        // 党派下拉数据列表
        async getPoliticalfn() {
            let res = await getPoliticalApi()
            if (res.data.code == "0000") {
                this.parties = res.data.data
            }
        },
        //根据上级名额来源代码查询名额来源，
        async getRecommendfn() {
            let res = await getRecommendApi()
            if (res.data.code == "0000") {
                this.sjmelyDm = res.data.data
            }
        },
        //获取名称
        // async getcommongetAddedselecList() {
        //     const res = await getcommongetAddedselecListApi()
        //     if (res.data.code === "0000") {
        //         this.areaNameList = res.data.data
        //         // console.log(this.periods[0].jcDm, '00');
        //         // this.addForm.areaName = this.areaNameList[0].areaName
        //         // this.addForm.cityName = this.areaNameList[0].cityName
        //     }
        // },
        //补选单位
        // async getAdministrativeStateList() {
        // let res = await getAdministrativeStateListApi()
        // if (res.data.code == '0000') {
        //     this.xzqhDmList = res.data.data
        // }
        // },
        //获取决定单位下拉数据列表
        async getgetVerifyUnitList() {
            let res = await getgetVerifyUnitListApi()
            if (res.data.code == "0000") {
                this.juedinList = res.data.data
                console.log(res.data, 'res.datares.data123');
                this.queryForm.verifyUnit = this.juedinList[0].verifyUnitDm
            }
        },
        //获取选举情况下拉数据列表
        async getElectionStateList() {
            let res = await getElectionStateListApi()
            if (res.data.code == "0000") {
                console.log(res, 'resresres77884455');
                this.electionSituationList = res.data.data
                this.queryForm.electionSituation = this.electionSituationList[0].electionStateDm
            }
        },

        //获取当选情况下拉数据列表
        async getdictCodeElectionStateList() {
            let res = await getdictCodeElectionStateListApi()
            if (res.data.code == "0000") {
                this.dangxuan = res.data.data
                this.queryForm.dxqkDm = this.dangxuan[0].electedStateDm
                this.addForm.dxqkDm = this.dangxuan[0].electedStateDm
            }
        },
    },
    //监听父组件传过来的值
    watch: {
        isRecord: {
            immediate: true,    // 这句重要  立即执行handler里面的方法
            handler(val) {
                this.isRecordIn = this.isRecord;
                //                     // let dbId = this.isRecordIn.dbId let jcDm = this.isRecordIn.jcDm

                this.isRecordIn.ID == undefined ? this.isRecordIn.ID = this.isRecord.id : ''
                this.isRecordIn.JC_DM == undefined ? this.isRecordIn.JC_DM = this.isRecord.jcDm : ''
                this.isRecordIn.USER_ID == undefined ? this.isRecordIn.USER_ID = this.isRecord.dbId : ''
                this.shenhe();
                this.getLevelListFn();
            }
        },
        // isRecord() {
        // }
    }
}
</script>
 
<style lang = "scss" scoped>
.formBox {
    width: 100%;
    height: 100%;
}

.Steps {
    width: 80%;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 10px;
}
</style>