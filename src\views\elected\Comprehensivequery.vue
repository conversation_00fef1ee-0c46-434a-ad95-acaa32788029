<template>
  <div class="table-container">
    <!-- <TJTable :list="list" :columns="columns"></TJTable> -->
    <!-- <a-row class="formBox">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18 }">
        <a-col span="6">
          <a-form-model-item label="届次"
                             style="width: 100%;">
            <a-select v-model="queryForm.jcDm"
                      placeholder="请选择届次"
                      allow-clear
                      style="width: 100%;">
              <a-select-option v-for="item in periods"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>

        <a-col span="6">
          <a-form-model-item label="姓名"
                             style="width: 100%;">
            <a-input v-model="queryForm.userName"
                     autocomplete="off"
                     allow-clear
                     placeholder="请输入姓名"
                     style="width: 100%;"
                     @keyup.enter="search"></a-input>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="性别"
                             prop="xbDm"
                             style="width: 100%;">
            <a-select v-model="queryForm.xbDm"
                      style="width: 100%;"
                      allow-clear
                      placeholder="请选择性别"
                      default-first-option>
              <a-select-option :key="2"
                               :value="2">女</a-select-option>
              <a-select-option :key="1"
                               :value="1">男</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-form-model>
      <a-col :span="6">
        <span style="float: right;">
          <a-button type="primary"
                    @click="search">搜索</a-button>
          <a-button style="margin-left: 12px;"
                    class="pinkBoutton"
                    @click="reset">重置</a-button>
        </span>
      </a-col>
    </a-row> -->

    <SearchForm @onReset="reset" @onSearch="search" :noMore="true">
      <template v-slot:topSearch>
        <SingleSelect :title="'届次'" :selectList="periods"  :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm" />
        <SingleSearch @onEnter="search" :title="'姓名'" :value.sync="queryForm.userName" />
        <SingleSelect :title="'性别'" :selectList="sexList" :value.sync="queryForm.xbDm" />
      </template>
    </SearchForm>

    <a-row style="margin: 10px 0px 10px 8px;">
      <a-col :span="6">
        <a-button type="primary"
                  style="margin-left: 12px;"
                  @click="download">导出</a-button>
      </a-col>
    </a-row>
    <a-row>
      <standard-table :bordered="bordered"
                      :columns="columns"
                      row-key="DB_ID"
                      :data-source="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"
                      @selectedRowChange="onSelectChange">
      </standard-table>
    </a-row>
  </div>
</template>
<script>
import StandardTable from "@/components/table/StandardTable";
import { myPagination } from "@/mixins/pagination.js";
import {
  getLevelListApi,
  getfindallselectApi,
} from "@/api/representativeElection/candidateApi.js";
import { instance_1 } from "@/api/axiosRq";
// import TJTable from "@/views/common/TJTable";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';

export default {
  components: {
    StandardTable,
    SingleSelect,
    SearchForm,
    SingleSearch,
  },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      bordered: true,
      // 届次
      periods: [],
      //表单状态
      state: "21",
      selectedRows: [],
      // pagination: [],
      queryForm: {
        jcDm: "3",
        pageNum: 1,
        pageSize: 10,
        xbDm: undefined,
        userName: "",
        // state: '21',
      },
      columns: [
        {
          title: "届次",
          ellipsis: true,
          dataIndex: "LEVEL_NAME",
        },
        {
          title: "选举单位",
          ellipsis: true,
          dataIndex: "XZQHMC",
        },
        {
          title: "姓名",
          ellipsis: true,
          dataIndex: "USER_NAME",
        },
        {
          title: "性别",
          ellipsis: true,
          dataIndex: "SEX",
          align: "center",
          width: 80,
          // customRender: (text, record, index) => {
          //   if (text == '1') {
          //     return "男"
          //   } else if (text == '2') {
          //     return '女'
          //   }
          // },
        },
        {
          title: "出生日期",
          ellipsis: true,
          dataIndex: "BIRTHDAY",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            if (text) {
              return text.replace("T", " ").split("Z").join("").substr(0, 11);
            } else {
              return "/";
            }
          },
        },
        {
          title: "候选人阶段",
          children: [
            {
              title: "申报表",
              ellipsis: true,
              dataIndex: "SBBID",
              customRender: (text, record, index) => {
                if (text == "0") {
                  return "已填报";
                } else if (text == "1") {
                  return "未填报";
                }
              },
            },
            {
              title: "登记表",
              ellipsis: true,
              dataIndex: "DJBID",
              customRender: (text, record, index) => {
                if (text == "0") {
                  return "已填报";
                } else if (text == "1") {
                  return "未填报";
                }
              },
            },

            {
              title: "预备人选",
              ellipsis: true,
              dataIndex: "YBRX",
              customRender: (text, record, index) => {
                if (text == "0") {
                  return "是";
                } else if (text == "1") {
                  return "否";
                }
              },
            },
            {
              title: "候选人",
              ellipsis: true,
              dataIndex: "CANDIDATE",
              customRender: (text, record, index) => {
                if (text == "0") {
                  return "是";
                } else if (text == "1") {
                  return "否";
                }
              },
            },
          ],
        },

        {
          title: "代表阶段",
          children: [
            {
              title: "落选候选人",
              ellipsis: true,
              dataIndex: "DEFEATED_CANDIDATE",
              customRender: (text, record, index) => {
                if (text == "0") {
                  return "是";
                } else if (text == "1") {
                  return "否";
                }
              },
            },
            {
              title: "当选代表",
              ellipsis: true,
              dataIndex: "ELECTED_BEHALF",
              customRender: (text, record, index) => {
                if (text == "0") {
                  return "是";
                } else if (text == "1") {
                  return "否";
                }
              },
            },
            {
              title: "代表团",
              ellipsis: true,
              dataIndex: "DBTMC",
            },
            {
              title: "代表出缺",
              ellipsis: true,
              dataIndex: "DBCQ",
              customRender: (text, record, index) => {
                if (text == "0") {
                  return "是";
                } else if (text == "1") {
                  return "否";
                }
              },
            },
          ],
        },
      ],
      tableData: [],
      selectedRows: [], tableKey: [],
      dataSource: [
        {
          index1: "十四届",
          index2: "越秀区人大",
          index3: "叶少阳",
          index4: "男",
          index5: "1990-07-25",
          index6: "已提交",
          index7: "已提交",
          index8: "李浩",
          index9: "李浩",
          index10: "郭丽",
          index11: "张守",
          index12: "越秀区代表团",
        },
        {
          index1: "十四届",
          index2: "天河区人大",
          index3: "张守",
          index4: "男",
          index5: "1985-06-05",
          index6: "已提交",
          index7: "已提交",
          index8: "李浩",
          index9: "李浩明",
          index10: "郭丽",
          index11: "张守",
          index12: "天河区代表团",
        },
        {
          index1: "十三届",
          index2: "越秀区人大",
          index3: "李晨",
          index4: "男",
          index5: "1977-03-15",
          index6: "已提交",
          index7: "已提交",
          index8: "李浩",
          index9: "李浩明",
          index10: "郭丽",
          index11: "张守",
          index12: "番禺区代表团",
        },
      ],
      sexList: [
        {name: '女', id: '2'},
        {name: '男', id: '1'},
      ]
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "预备人选管理");
    this.$store.dispatch("navigation/breadcrumb2", "综合查询");
    this.getLevelListFn();
    this.fetchData();
  },
  methods: {
    onSelectChange (key, data) {
      this.tableKey = key
      this.tableData = data
      console.log(this.tableData, 'tableData');
    },
    // 导出
    download () {
      let ids = [];
      ids = this.tableData.map((i) => i.DB_ID)
      instance_1({
        url: "/ComprehensiveSelect/export",
        method: "post",
        responseType: "blob",
        params: this.queryForm,
        data: ids,
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `综合查询.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    //获取数据
    fetchData () {
      this.TBloading = true;

      getfindallselectApi(this.queryForm).then((res) => {
        this.TBloading = false;

        // console.log(res.data);
        this.dataSource = res.data.rows;
        this.pagination.total = res.data.total;
      });
    },
    //重置
    reset () {
      this.queryForm = {
        jcDm: "2",
        pageNum: 1,
        pageSize: 10,
        xbDm: undefined,
        userName: "",
      };
      this.fetchData();
    },
    //搜索
    search () {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },

    //获取当前届次下拉数据列表
    async getLevelListFn () {
      const res = await getLevelListApi();
      if (res.data.code === "0000") {
        this.periods = res.data.data;
        // console.log(this.periods[0].jcDm, '00');
        this.queryForm.jcDm = this.periods[0].jcDm; //改过
        console.log(this.queryForm.jcDm);
      }
    },
  },
};
</script>
