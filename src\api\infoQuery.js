import request from "@/utils/requestTemp";

export function signList(params, data) {
  return request({
    url: "/infoQuery/findAllInfo",
    method: "post",
    params : params,
    data : data
  });
}

export function queryInfo(params, data) {
  return request({
    url: "/infoQuery/findAllInfo",
    method: "post",
    params : params,
    data : data
  });
}

export function queryData(params) {
  return request({
    url: "/infoQuery/findById?id=" + params,
    method: "get",
    params
  });
}
//修改个人信息（代表信息管理）
export function getfindCurrentUserInfoApi(params) {
  return request({
    url: "/infoQuery/findCurrentUserInfo",
    method: "get",
    params
  });
}

