import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/meetingCalendar/getManageCalendarList",
    method: "post",
    data,
  });
}

export function InitCalendarInfo(param) {
  return request({
    url: "/api/v1/meetingCalendar/getCalendar/" + param,
    method: "post",
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/meetingCalendar/addCalendar",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/meetingCalendar/updateCalendar",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/meetingCalendar/deleteCalendar",
    method: "post",
    data
  });
}

export function getSegmentList() {
  return request({
    url: "/api/v1/meetingCalendar/getSegmentList",
    method: "post",
  });
}

export function getByMeetingId(param) {
  return request({
    url: "/api/v1/meetingCalendar/getCalendarByMeetingId/" + param,
    method: "post",
  });
}

export function doSaveCalendarUser(param, data) {
  return request({
    url: "/api/v1/meetingCalendar/addCalendarUser/" + param,
    method: "post",
    data,
  });
}

export function doSaveCalendarIdentityUser(param, data) {
  return request({
    url: "/api/v1/meetingCalendar/addCalendarIdentityUser/" + param,
    method: "post",
    data,
  });
}

export function getCalendarUserList(param) {
  return request({
    url: "/api/v1/meetingCalendar/getCalendarUserList/" + param,
    method: "post",
  });
}

export function getCalendarIdentityList(param) {
  return request({
    url: "/api/v1/meetingCalendar/getCalendarIdentityList/" + param,
    method: "post",
  });
}

export function getCalendarAccreditList(param) {
  return request({
    url: "/api/v1/meetingCalendar/getCalendarAccreditList/" + param,
    method: "post",
  });
}

export function doSaveCalendarAccredit(param, data) {
  return request({
    url: "/api/v1/meetingCalendar/addCalendarAccredit/" + param,
    method: "post",
    data,
  });
}

export function doRelease(param) {
  return request({
    url: "/api/v1/meetingCalendar/releaseCalendar/" + param,
    method: "post",
  });
}

export function doRecall(param) {
  return request({
    url: "/api/v1/meetingCalendar/recallCalendar/" + param,
    method: "post",
  });
}
