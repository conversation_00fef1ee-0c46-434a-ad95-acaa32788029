import request from "@/utils/requestTemp";
// import qs from "qs";

export function getList(data) {
  return request({
    url: "/publicOpinion/listForAdmin",
    method: "get",
    params: data,
  });
}

export function getMemberByUserId(data) {
  return request({
    url: "/committeeMember/admin/getByUserId",
    method: "get",
    params: data,
  });
}


export function getById(data) {
  return request({
    url: "/publicOpinion/getById",
    method: 'get',
    params: data,
  });
}

export function save(data) {
  return request({
    url: "/publicOpinion/save",
    method: "post",
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }, // 请求头
    transformRequest: [(data) => {
      return JSON.stringify(data)
    }],
    data: data,
  });
}

export function update(data) {
  return request({
    url: "/publicOpinion/update",
    method: "post",
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }, // 请求头
    transformRequest: [(data) => {
      return JSON.stringify(data)
    }],
    data: data,
  });
}

export function saveCcommitteeMember(data) {
  return request({
    url: "/committeeMember/save",
    method: "post",
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }, // 请求头
    transformRequest: [(data) => {
      return JSON.stringify(data)
    }],
    data: data,
  });
}

export function listForMember(data) {
  return request({
    url: "/publicOpinion/listForMember",
    method: "get",
    params: data,
  });
}

export function listForStation(data) {
  return request({
    url: "/publicOpinion/listForStation",
    method: "get",
    params: data,
  });
}

