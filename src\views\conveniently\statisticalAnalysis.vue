<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <a-row style="margin-left: 1%">
          <a-form
            ref="queryForm"
            :form="queryForm"
            :model="queryForm"
            layout="horizontal"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 18, offset: 0 }"
          >
            <div>
              <a-row>
                <a-col :span="6">
                  <a-form-item label="主体">
                    <a-input
                      v-model="queryForm.subject"
                      placeholder="请输入主体"
                      clearable
                      allow-clear
                      class="table-input"
                      @keyup.enter="handleQuery"
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="地址">
                    <a-input
                      v-model="queryForm.address"
                      placeholder="请输入地址"
                      clearable
                      allow-clear
                      @keyup.enter="handleQuery"
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item style="display: flex" label="处理进度：">
                    <a-select
                      v-model="queryForm.status"
                      placeholder="请选择处理进度"
                      clearable
                      allow-clear
                    >
                      <a-select-option
                        v-for="(item, index) in arrOptions"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                        >{{ item.label }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <span style="float: right; margin-top: 3px">
                    <a style="margin-right: 8px" @click="toggleAdvanced">
                      {{ advanced ? "收起" : "高级搜索" }}
                      <a-icon :type="advanced ? 'up' : 'down'" />
                    </a>
                    <a-button type="primary" @click="handleQuery"
                      >搜索</a-button
                    >
                    <a-button
                      style="margin-left: 12px"
                      type="primary"
                      @click="sendRemindMsg()"
                      >评价提醒短信</a-button
                    >
                    <a-button
                      style="margin-left: 12px"
                      class="pinkBoutton"
                      @click="reset"
                      >重置</a-button
                    >
                  </span>
                </a-col>
              </a-row>
              <a-row v-if="advanced">
                <a-col :md="6" :sm="24">
                  <a-form-item label="姓名" prop="userName">
                    <a-input
                      v-model="queryForm.userName"
                      clearable
                      allow-clear
                      placeholder="请输入姓名"
                      @keyup.enter="handleQuery"
                    />
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="类型" prop="commentCategory">
                    <a-select
                      v-model="queryForm.commentCategory"
                      placeholder="请选择类型"
                      clearable
                      allow-clear
                      @keyup.enter="handleQuery"
                    >
                      <a-select-option
                        v-for="(item, index) in cityItems"
                        :key="index"
                        :label="item.title"
                        :value="item.title"
                      >
                        {{ item.title }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="事项编号" prop="userName">
                    <a-input
                      v-model="queryForm.proWoCode"
                      clearable
                      allow-clear
                      placeholder="请输入事项编号"
                      @keyup.enter="handleQuery"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="advanced">
                <a-col :md="6" :sm="24">
                  <a-form-item label="开始时间">
                    <a-date-picker
                      v-model="queryForm.startTime"
                      allow-clear
                      style="width: 100%"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      show-time
                      placeholder="选择开始时间"
                      :disabled-date="
                        (current) =>
                          current && queryForm.endTime
                            ? current.valueOf() >=
                              moment(new Date(queryForm.endTime)).valueOf()
                            : false
                      "
                    ></a-date-picker>
                  </a-form-item>
                </a-col>
                <a-col :md="6">
                  <a-form-item label="结束时间">
                    <a-date-picker
                      v-model="queryForm.endTime"
                      allow-clear
                      show-time
                      style="width: 100%"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      placeholder="选择结束时间"
                      :disabled-date="
                        (current) =>
                          current && queryForm.startTime
                            ? moment(new Date(queryForm.startTime)).valueOf() >=
                              current.valueOf()
                            : false
                      "
                    ></a-date-picker>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="编号">
                    <a-input
                      v-model="queryForm.issueNum"
                      placeholder="请输入编号"
                      clearable
                      allow-clear
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="办理单位" prop="userName">
                    <a-input
                      v-model="queryForm.bldw"
                      clearable
                      allow-clear
                      placeholder="请输入办理单位"
                      @keyup.enter="handleQuery"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="advanced">
                <!--                  <a-col :span="6">-->
                <!--                    <a-form-item label="意见内容">-->
                <!--                      <a-input v-model="queryForm.generalComments"-->
                <!--                               placeholder="请输入意见内容"-->
                <!--                               clearable-->
                <!--                               allow-clear-->
                <!--                               v-on:keyup.enter="handleQuery"-->
                <!--                               class="table-input"></a-input>-->
                <!--                    </a-form-item>-->
                <!--                  </a-col>-->
                <a-col :span="6">
                  <a-form-item label="是否评价">
                    <a-select
                      v-model="queryForm.sfpj"
                      placeholder="是否评价"
                      allow-clear
                    >
                      <a-select-option label="" value="">全部</a-select-option>
                      <a-select-option label="1" value="1">是</a-select-option>
                      <a-select-option label="0" value="0">否</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="第一次满意度">
                    <a-select
                      v-model="queryForm.appraise"
                      placeholder="第一次满意度"
                      allow-clear
                    >
                      <a-select-option label="1" value="1"
                        >满意</a-select-option
                      >
                      <a-select-option label="2" value="2"
                        >基本满意</a-select-option
                      >
                      <a-select-option label="14" value="14"
                        >不满意</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="第二次满意度">
                    <a-select
                      v-model="queryForm.secondAppraise"
                      placeholder="第二次满意度"
                      allow-clear
                    >
                      <a-select-option label="1" value="1"
                        >满意</a-select-option
                      >
                      <a-select-option label="2" value="2"
                        >基本满意</a-select-option
                      >
                      <a-select-option label="14" value="14"
                        >不满意</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="已办结未评价">
                    <a-select
                      v-model="queryForm.completedNotComment"
                      placeholder="已办结未评价"
                      allow-clear
                      @change="handleTypeChange"
                    >
                      <a-select-option label="" value="">全部</a-select-option>
                      <a-select-option label="1" value="1">是</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="advanced">
                <a-col :span="6">
                  <a-form-item label="意见内容">
                    <a-input
                      v-model="queryForm.generalComments"
                      placeholder="请输入意见内容"
                      clearable
                      allow-clear
                      class="table-input"
                      @keyup.enter="handleQuery"
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="待评价天数">
                    <a-select
                      v-model="queryForm.pjts"
                      placeholder="待评价天数"
                      allow-clear
                      @change="handleTypeChange"
                    >
                      <a-select-option label="-1" value="-1"
                        >全部待评价</a-select-option
                      >
                      <a-select-option label="1" value="1"
                        >1天待评价</a-select-option
                      >
                      <a-select-option label="2" value="2"
                        >2天待评价</a-select-option
                      >
                      <a-select-option label="3" value="3"
                        >3天及以上待评价</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="是否职务代表">
                    <a-select
                      v-model="queryForm.zwdb"
                      placeholder="是否职务代表"
                      allow-clear
                      @change="handleTypeChange"
                    >
                      <a-select-option label="" value="">全部</a-select-option>
                      <a-select-option label="1" value="1"
                        >职务代表</a-select-option
                      >
                      <a-select-option label="2" value="2"
                        >非职务代表</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="有效数据">
                    <a-select
                      v-model="queryForm.effectiveData"
                      placeholder="有效数据"
                      allow-clear
                      @change="handleTypeChange"
                    >
                      <a-select-option label="" value="">全部</a-select-option>
                      <a-select-option label="1" value="1">是</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="18">
                  <DhJcCascade v-model="queryForm" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </a-row>
        <a-row style="margin: 5px 0px 10px 8px">
          <a-col :span="6">
            <a-button type="primary" style="margin-left: 12px" @click="download"
              >导出</a-button
            >
          </a-col>
        </a-row>
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <!-- 更多查询条件抽屉 结束 -->
        <!-- table -->
        <a-spin :spinning="listLoading" :indicator="indicator">
          <a-table
            ref="table"
            :bordered="false"
            class="tableLimit"
            :columns="columns"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            :pagination="pagination"
            :data-source="list"
            :row-key="
              (record, index) => {
                return index;
              }
            "
            :custom-row="clickRow"
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
        </a-spin>
      </a-col>
    </a-row>
    <!-- 新增弹窗随手拍 -->
    <a-modal
      :title="titleText"
      :visible.sync="dialogFormVisible"
      width="1000px"
      destroy-on-close
      @cancel="modalClose"
    >
      <template slot="footer">
        <a-button @click="modalClose">取消</a-button>
        <a-button style="background-color: '#00a64d'" @click="modalOk(1)"
          >草稿</a-button
        >
        <a-button type="primary" @click="modalOk()">确定</a-button>
      </template>

      <a-tabs :default-active-key="active" @change="callback">
        <a-tab-pane key="1" tab="拍照上传">
          <div class="Data-value">
            <div class="title" style="width: 180px">上传图片:</div>
            <div class="upData">
              <a-upload
                ref="uploadPic"
                :action="action"
                list-type="picture-card"
                :multiple="false"
                :file-list="imgList"
                accept="image/*"
                :before-upload="beforeAvatarUpload"
                @change="handlePicChange"
                @preview="handlePreview"
              >
                <!-- 设置文件上传个数限制 -->
                <div v-if="Boolean(imgList) && imgList.length < 5">
                  <a-icon type="plus" />
                  <div>上传</div>
                </div>
              </a-upload>
              <a-modal
                :visible.sync="dialogVisible"
                :footer="null"
                @cancel="
                  () => {
                    dialogVisible = false;
                  }
                "
              >
                <img width="100%" :src="dialogImageUrl" alt />
              </a-modal>
            </div>
          </div>
          <div class="Data-value">
            <div class="title">所反映意见建议涉及的主体:</div>
            <div class="textarea">
              <a-textarea
                v-model="content"
                placeholder="店名或相关信息(必填）"
                auto-size
              />
            </div>
          </div>
          <div class="Data-value">
            <div class="title">所反映意见建议涉及的地址:</div>
            <div class="textarea">
              <a-textarea v-model="address" placeholder="反映地址" auto-size />
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 405px">意见建议内容(必填):</div>
            <div class="contentBox">
              <div
                v-for="(item, index) in cityItems"
                :key="index"
                :class="{ bgColor: index == isColor }"
                class="contents"
                @click="fnColor(index, item.title)"
              >
                {{ item.title }}
              </div>
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 170px">
              请根据需要填写您的意见:
            </div>
            <div class="textarea">
              <a-textarea
                v-model="comment"
                style="height: 160px"
                :rows="8"
                placeholder="请填写意见内容"
                auto-size
              />
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 180px">上传附件:</div>
            <div class="upData">
              <a-upload
                accept=".pdf"
                :action="action"
                :data="fileData"
                :remove="handleRemoveFU"
                :file-list="fileValue"
                :before-upload="beforeAvatarUploadFU"
                @preview="previewData"
                @change="uploadChange"
              >
                <a-button type="primary">点击上传</a-button>
                <div>只能上传pdf文件，且不超过20mb</div>
              </a-upload>
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 178px">选择转办地址:</div>
            <a-radio-group v-model="progressStatus" @change="onprogressStatus">
              <a-radio :value="2">转办联络站跟进办理</a-radio>
              <a-radio :value="3">转办12345热线办理</a-radio>
            </a-radio-group>
          </div>
        </a-tab-pane>

        <a-tab-pane key="2" tab="视频上传">
          <div class="Data-value">
            <div class="title" style="width: 180px">上传视频:</div>
            <div class="upData">
              <div class="upData">
                <a-upload
                  accept=".mp4"
                  :action="action"
                  :data="fileData"
                  :remove="handleRemoveFU"
                  :before-upload="beforeAvatarUploadMP4"
                  @change="uploadChange4"
                >
                  <a-button type="primary" :disabled="isDisabled"
                    >点击上传</a-button
                  >
                  <div>上传文件不能超过20mb,且只能上传一个</div>
                </a-upload>

                <div v-if="videoFujianData.length > 0" label="视频文件">
                  <div v-for="(video, index) in videoFujianData" :key="index">
                    <video
                      ref="video"
                      :src="video"
                      :controls="true"
                      class="video-js vjs-big-play-centered vjs-fluid"
                      webkit-playsinline="true"
                      playsinline="true"
                      x-webkit-airplay="allow"
                      x5-playsinline
                      style="height: 200px; width: 100%"
                    ></video>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="Data-value">
            <div class="title">所反映意见建议涉及的主体:</div>
            <div class="textarea">
              <a-textarea
                v-model="content"
                placeholder="店名或相关信息"
                auto-size
              />
            </div>
          </div>
          <div class="Data-value">
            <div class="title">所反映意见建议涉及的地址:</div>
            <div class="textarea">
              <a-textarea v-model="address" placeholder="反映地址" auto-size />
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 405px">意见建议内容(必填):</div>
            <div class="contentBox">
              <div
                v-for="(item, index) in cityItems"
                :key="index"
                :class="{ bgColor: index == isColor }"
                class="contents"
                @click="fnColor(index, item.title)"
              >
                {{ item.title }}
              </div>
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 170px">
              请根据需要填写您的意见:
            </div>
            <div class="textarea">
              <a-textarea
                v-model="comment"
                style="height: 160px"
                :rows="8"
                placeholder="请填写意见内容"
                auto-size
              />
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 180px">上传附件:</div>
            <div class="upData">
              <a-upload
                accept=".pdf"
                :action="action"
                :data="fileData"
                :remove="handleRemoveFU"
                :before-upload="beforeAvatarUploadFU"
                @preview="previewData"
                @change="uploadChange"
              >
                <a-button type="primary">点击上传</a-button>
                <div>只能上传pdf文件，且不超过20mb</div>
              </a-upload>
            </div>
          </div>
          <div class="Data-value">
            <div class="title" style="width: 178px">选择转办地址:</div>
            <a-radio-group v-model="progressStatus" @change="onprogressStatus">
              <a-radio :value="2">转办联络站跟进办理</a-radio>
              <a-radio :value="3">转办12345热线办理</a-radio>
            </a-radio-group>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-modal>
  </div>
</template>

<script>
import { instance_1, instance_2, instance_3 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
import Vue from "vue";
import DhJcCascade from "../../components/DhJcCascade/index.vue";
import { DBSSP } from "@/utils/enum/levelRoleMainIdentifyEnum";
const action =
  Vue.prototype.GLOBAL.basePath_1 + "/api/v1/meetingFile/fileUpload";

export default {
  name: "Table",
  components: { DhJcCascade },

  filters: {
    getState: function (state) {
      if (state == 1) {
        return "草稿";
      } else if (state == 2) {
        return "联络站处理";
      } else if (state == 3) {
        return "12345热线处理";
      } else if (state == 4) {
        return "已办结";
      }
    },
  },
  data() {
    return {
      levelRoleMainIdentify: DBSSP,
      TBloading: false,

      indicator: <a-icon type="loading" style="font-size: 24px" spin />, //转动的圈圈
      advanced: false,
      editShow: false,
      titleText: "",
      active: "1",
      videoList: [],
      videoFujianData: [],
      fujian: [],
      imgFujian: [],
      videoFujian: [],
      qitaFujian: [],
      isDisabled: false,
      content: "",
      address: "",
      comment: "",
      station: "",
      commentCategory: "城市建设",
      progressStatus: 2,
      fileList: [],
      fileValue: [],
      imgList: [],

      action: action,
      interfaceLocation: "",
      //  action: "http://************:8080/ilz/api/v1/" + "activity/upload",
      fileData: {
        relId: "",
        type: 0,
        encrypted: true,
      },
      urlData: "/memberComment/insert",
      dialogImageUrl: "",
      formDataFU: new FormData(),
      dialogVisible: false,
      formData: new FormData(),
      defaultfile: [],
      selectedRowKeys: [], // 选择的key值数组
      isColor: "0",
      dialogFormVisible: false,
      isCheck: false,
      badgeHide: true,
      drawer: false,
      drawerHeight: window.innerHeight - 75 + "px",
      list: [],
      listLoading: false,
      listHeight: window.innerHeight - 360 + "px",
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      background: true,
      selectRows: "",
      elementLoadingText: "正在加载...",
      filePath: "",
      cityItems: [
        { id: 400, title: "城市建设", status: false },
        { id: 500, title: "房产管理", status: false },
        { id: 600, title: "环境保护", status: false },
        { id: 700, title: "市容城管", status: false },
        { id: 800, title: "交通运输", status: false },
        { id: 900, title: "经济财贸", status: false },
        { id: 1000, title: "治安消防", status: false },
        { id: 1100, title: "交通管理", status: false },
        { id: 1200, title: "市场管理", status: false },
        { id: 1300, title: "民政救济", status: false },
        { id: 1400, title: "文教卫体", status: false },
        { id: 1500, title: "三农问题", status: false },
        { id: 1600, title: "重大突发事件", status: false },

        { id: 1800, title: "涉军涉警", status: false },

        { id: 2000, title: "退役事务", status: false },
        { id: 2100, title: "人力资源", status: false },
        { id: 9999, title: "其他事件", status: false },
      ],
      // area: [], // 区数据列表
      area: [
        {
          id: "全国人大代表",
          name: "全国人大代表",
        },
        {
          id: "省人大代表",
          name: "省人大代表",
        },
        {
          id: "越秀代表团",
          name: "越秀代表团",
        },
        {
          id: "海珠代表团",
          name: "海珠代表团",
        },
        {
          id: "荔湾代表团",
          name: "荔湾代表团",
        },
        {
          id: "天河代表团",
          name: "天河代表团",
        },
        {
          id: "白云代表团",
          name: "白云代表团",
        },
        {
          id: "黄埔代表团",
          name: "黄埔代表团",
        },
        {
          id: "花都代表团",
          name: "花都代表团",
        },
        {
          id: "番禺代表团",
          name: "番禺代表团",
        },
        {
          id: "南沙代表团",
          name: "南沙代表团",
        },
        {
          id: "从化代表团",
          name: "从化代表团",
        },
        {
          id: "增城代表团",
          name: "增城代表团",
        },
      ],
      street: [], // 街道数据
      memberCommentParamVO: {
        subject: null,
        status: null,
        address: null,
        commentCategory: null,
        startTime: null,
        endTime: null,
        // reflectTime: "",
        userName: null,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        ids: [],
      },
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        subject: null,
        status: undefined,
        address: null,
        commentCategory: undefined,
        notTime: undefined,
        pjts: null,
        // startTime: null,
        // endTime: null,
        // reflectTime: "",
        userName: null,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        sfpj: "",
        appraise: "",
        secondAppraise: "",
        issueNum: "",
        zwdb: "",
        effectiveData: "1",
        completedNotComment: "",
        proWoCode: null,
        bldw: null,
      },
      indexNum: 1,

      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 70,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "编号",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "issueNum",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "日期",
          align: "center",
          width: 160,
          ellipsis: true,
          dataIndex: "reflectTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "问题类型",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "commentCategory",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "处理进度",
          align: "center",
          width: 120,
          ellipsis: true,
          customRender: (text, record, index) => {
            if (record.annul == 1) {
              text = "(已撤单)";
            } else {
              text = "";
            }

            if (record.progressStatus == "1" || record.progressStatus == "0")
              return "草稿";
            if (record.progressStatus == "2") return "联络站处理" + text;
            if (record.progressStatus == "3") return "12345热线处理" + text;
            // if (record.progressStatus == "3" && record.status == "0")
            //   return "12345提交中";
            // if (record.progressStatus == "3" && record.status == "1")
            //   return "12345处理中";
            // if (record.progressStatus == "3" && record.status == "2")
            //   return "12345已回复";
            if (record.progressStatus == "4") return "已办结" + text;
          },
        },

        {
          title: "意见内容",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "generalComments",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        // {
        //   title: "代表手机",
        //   align: "center",
        //   width: 160,
        //   ellipsis: true,
        //   dataIndex: "userPhone",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "所属区域",
        //   align: "center",
        //   width: 100,
        //   ellipsis: true,
        //   dataIndex: "district",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "所属乡镇街道",
        //   align: "center",
        //   width: 170,
        //   ellipsis: true,
        //   dataIndex: "street",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "联络站",
        //   align: "center",
        //   width: 160,
        //   ellipsis: true,
        //   dataIndex: "interfaceLocation",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "站长姓名",
        //   align: "center",
        //   width: 100,
        //   ellipsis: true,
        //   dataIndex: "stationAgentName",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        // {
        //   title: "站长电话",
        //   align: "center",
        //   width: 160,
        //   ellipsis: true,
        //   dataIndex: "stationAgentPhoneNumber",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        {
          title: "涉事主体",
          align: "center",
          width: 350,
          ellipsis: true,
          dataIndex: "subject",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        // {
        //   title: "地址",
        //   align: "center",
        //   width: 300,
        //   ellipsis: true,
        //   dataIndex: "address",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },

        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 120,
          customRender: (text, record, index) => {
            let permission = checkPermission(["I_XTGLY", "I_DBLZ_XLGW"]);
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(record);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display:
                      permission &&
                      record.progressStatus == "3" &&
                      record.annul == 0
                        ? "inline-block"
                        : "none",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.saveCBOrder(record.id);
                    },
                  },
                },
                "撤单"
              ),
            ]);
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      arrOptions: [
        {
          value: "",
          label: "全部",
        },
        {
          value: 1,
          label: "草稿",
        },

        {
          value: 3,
          label: "12345热线处理",
        },
        {
          value: 4,
          label: "已办结",
        },
        {
          value: 5,
          label: "已撤单",
        },
      ],
      statusOptions: [
        {
          value: "",
          label: "全部",
        },
        {
          value: 0,
          label: "已转办",
        },
        {
          value: 1,
          label: "已回复",
        },
      ],

      // 限制结束日期不能大于开始日期
      pickerOptions0: {
        disabledDate: (time) => {
          const endDateVal = this.queryForm.endTime;
          if (endDateVal) {
            return time.getTime() >= new Date(endDateVal).getTime();
          }
        },
      },
      pickerOptions1: {
        disabledDate: (time) => {
          const beginDateVal = this.queryForm.startTime;
          if (beginDateVal) {
            return time.getTime() < new Date(beginDateVal).getTime();
          }
        },
      },
    };
  },
  computed: {
    conditionsCount: function () {
      let size = 0;
      console.log("🤗🤗🤗, this.queryForm =>", this.queryForm);
      for (const key in this.queryForm) {
        if (Object.hasOwnProperty.call(this.queryForm, key)) {
          if (key == "pageNum" || key == "pageSize") {
            continue;
          }
          const element = this.queryForm[key];
          if (
            Array.isArray(element)
              ? element.length > 0
              : element !== "" && element != null
          ) {
            size++;
          }
        }
      }
      return size;
    },
  },
  watch: {
    conditionsCount(newVal, oldVal) {
      this.badgeHide = newVal <= 0;
    },
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "代表随手拍");
    this.$store.dispatch("navigation/breadcrumb2", "统计分析");
    this.fetchData();
    // this.getArea();
  },
  mounted() {
    // 抽屉滚动区高度
    window.onresize = () => {
      return (() => {
        this.drawerHeight = window.innerHeight - 75 + "px";
        this.listHeight = window.innerHeight - 360 + "px";
      })();
    };
  },

  methods: {
    // 高级搜索
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    //重置
    reset() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        subject: null,
        status: undefined,
        progressStatus: null,
        address: null,
        commentCategory: undefined,
        notTime: undefined,
        startTime: null,
        endTime: null,
      };
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
    //导出
    download() {
      // console.log(this.queryForm);
      instance_1({
        url: "/memberComment/export",
        method: "get",
        responseType: "blob",
        params: this.queryForm,
        data: this.memberCommentParamVO,
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `随手拍统计分析表.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    // 获取区
    getArea: function () {
      instance_3({
        method: "get",
        url: "/peopleResponse/selectArea", //代表数据接口
        //
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {},
      }).then((data) => {
        this.area = data.data.data;
        this.getStreet(), (this.station = []); // 重新置空联络站
      });
    },
    // 根据区获取街道
    getStreet: function () {
      instance_3({
        method: "get",
        url: "/peopleResponse/selectStreet", //代表数据接口
        //
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          administrativeAreaId: this.queryForm.administrativeAreaId,
        },
      }).then((data) => {
        this.street = data.data.data;
        this.getStation();
      });
    },
    // 根据区街道获取联络站
    getStation: function () {
      instance_3({
        method: "get",
        url: "/peopleResponse/selectStation", //代表数据接口
        //
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          administrativeAreaId: this.queryForm.administrativeAreaId,
          streetTownId: this.queryForm.streetTownId,
        },
      }).then((data) => {
        console.log(data);
        this.station = data.data.data;
      });
    },
    // 编辑页面时
    handlerRedact(id) {
      this.editShow = true;
      this.titleText = "编辑随手拍";
      instance_1({
        method: "get",
        url: "/memberComment/details",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          id: id,
        },
      }).then((data) => {
        let {
          subject,
          address,
          commentCategory,
          generalComments,
          attachmentList,
        } = data.data.data;
        // 回显
        this.address = address;
        this.commentCategory = commentCategory;
        this.comment = generalComments;
        this.content = subject;
        this.dialogFormVisible = true;
        this.cityItems.forEach((item, index) => {
          if (item.title == commentCategory) {
            this.isColor = index;
          }
        });
        if (attachmentList.length > 0) {
          attachmentList.forEach((item) => {
            if (item.type == "2") {
              this.qitaFujian.push({
                fileName: item.fileName,
                uid: item.id,
                type: "2",
                path: item.path,
              });
              this.fileValue.push({
                uid: item.id,
                name: item.fileName,
                url: item.path,
              });
            } else {
              this.getFile(item.path, item.type, item);
            }
          });
        }
      });
    },
    getFile(id, type, item) {
      if (type == 1) {
        this.active = "2";
        this.videoFujian.push({
          fileName: item.fileName,
          path: item.path,
          type: type,
          src: item.path,
          uid: file.id,
        });
        instance_3({
          url: "/file/view",
          method: "get",
          responseType: "blob",
          params: { file: id },
        }).then((res) => {
          const dataInfo = res.data;
          let reader = new window.FileReader();
          // 使用readAsArrayBuffer读取文件, result属性中将包含一个 ArrayBuffer 对象以表示所读取文件的数据
          reader.readAsArrayBuffer(dataInfo);
          reader.onload = (e) => {
            const result = e.target.result;
            let contentType = "video/mp4";
            // 生成blob图片,需要参数(字节数组, 文件类型)
            const blob = new Blob([result], { type: contentType });
            // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
            const url = window.URL.createObjectURL(blob);
            this.videoFujianData.push(url);
          };
        });
      } else {
        let that = this;
        that.active = "1";
        instance_3({
          url: "/file/view",
          method: "get",
          responseType: "blob",
          params: { file: id },
        }).then((res) => {
          const dataInfo = res.data;
          let reader = new window.FileReader();
          // 使用readAsArrayBuffer读取文件, result属性中将包含一个 ArrayBuffer 对象以表示所读取文件的数据
          reader.readAsArrayBuffer(dataInfo);
          reader.onload = function (e) {
            const result = e.target.result;
            let contentType = dataInfo.type;
            //判断文件类型
            if (type == 1) {
              contentType = "video/mp4";
            }
            // 生成blob图片,需要参数(字节数组, 文件类型)
            const blob = new Blob([result], { type: contentType });
            // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
            const url = window.URL.createObjectURL(blob);
            if (url != "" && contentType != "video/mp4") {
              this.imgFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: item.id,
              });
              //构造数据显示图片
              that.imgList.push({
                url: url,
                nameL: item.fileName,
                uid: item.id,
              });
            }
          };
        });
      }
    },
    handleTypeChange() {
      this.memberCommentParamVO.ids = [];
      this.selectedRowKeys = [];
      this.fetchData();
    },
    previewData(item) {
      this.qitaFujian.forEach((item1) => {
        if (item.uid == item1.uid) {
          this.filePath = item1.path;
        }
      });
      let pdfUrl =
        Vue.prototype.GLOBAL.basePath_1 +
        "/file/view?file=" +
        this.filePath +
        "&token=" +
        Vue.prototype.GLOBAL.token;
      window.open(
        process.env.BASE_URL +
          "pdfjs/web/viewer.html?file=" +
          encodeURIComponent(pdfUrl)
      );
    },
    //  视频
    uploadChange4({ file, fileList }) {
      //删除文件
      if (file.status == "removed") {
        this.handleRemoveFU(file.uid);
      } else {
        let type = 1;
        // 图片0
        // 视频1
        let formData = new FormData();
        formData.append("type", type);
        formData.append("file", file);
        instance_1({
          method: "post",
          url: "/memberComment/upload",
          header: {
            "Content-type": "multipart/form-data", // 默认值
          },
          data: formData,
        }).then((res) => {
          res.data.data.forEach((item) => {
            if (type == 0) {
              this.imgFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
            }
            if (type == 1) {
              this.videoFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
              // 限制上传个数
              this.isDisabled = true;
              instance_3({
                url: "/file/view",
                method: "get",
                responseType: "blob",
                params: { file: item.path },
              }).then((res) => {
                const dataInfo = res.data;
                let reader = new window.FileReader();
                // 使用readAsArrayBuffer读取文件, result属性中将包含一个 ArrayBuffer 对象以表示所读取文件的数据
                reader.readAsArrayBuffer(dataInfo);
                reader.onload = (e) => {
                  const result = e.target.result;
                  let contentType = "video/mp4";
                  // 生成blob图片,需要参数(字节数组, 文件类型)
                  const blob = new Blob([result], { type: contentType });
                  // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
                  const url = window.URL.createObjectURL(blob);
                  console.log(url);
                  this.videoFujianData.push(url);
                };
              });
            }
            if (type == 2) {
              this.qitaFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
              });
            }
          });
        });
      }
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
      console.log("this.selectedRowKeys--->", this.selectedRowKeys);
      console.log("this.selectRows--->", this.selectRows);
      var arr = [];
      if (this.selectRows.length != 0) {
        // return this.$message.warning("请选择数据！");
        arr = this.selectRows.map((item) => item.id);
      }
      this.memberCommentParamVO.ids = arr;
      console.log("this.queryForm.ids--->", this.memberCommentParamVO.ids);
    },
    sendRemindMsg() {
      if (this.total == 0) {
        this.$message.error("暂无发送随手拍！");
        return;
      }
      if (this.queryForm.pjts == null || this.queryForm.pjts == "") {
        this.$message.error("请选择待评价天数！");
        return;
      }

      if (this.selectedRowKeys.length == 0) {
        console.log("  this.total --", this.total);
        this.$confirm({
          cancelText: "取消",
          okText: "确定",
          title: "温馨提示",
          content:
            "是否确定向这" + this.total + "条未评价的随手拍发送评价短信提醒",
          onOk: () => {
            this.listLoading = true;
            instance_2({
              method: "POST",
              url: "/memberComment/sendPjMsg",
              headers: {
                "Content-Type": "multipart/form-data",
              },
              params: this.queryForm,
              data: this.memberCommentParamVO,
            }).then((data) => {
              this.listLoading = false;
              if (data.data.code == "0000") {
                this.$message.info("发送成功！");
              } else {
                this.$message.error("发送失败！");
              }
            });
          },
          onCancel: () => {
            this.$message.info("您已取消操作！");
          },
        });
      } else {
        console.log("this.queryForm.ids--->", this.memberCommentParamVO.ids);
        this.$confirm({
          cancelText: "取消",
          okText: "确定",
          title: "温馨提示",
          content:
            "是否确定向勾选的" +
            this.memberCommentParamVO.ids.length +
            "条随手拍发送评价短信提醒",
          onOk: () => {
            this.listLoading = true;
            instance_2({
              method: "POST",
              url: "/memberComment/sendPjMsg",
              headers: {
                "Content-Type": "multipart/form-data",
              },
              params: this.queryForm,
              data: this.memberCommentParamVO,
            }).then((data) => {
              this.listLoading = false;
              if (data.data.code == "0000") {
                this.$message.info("发送成功！");
              } else {
                this.$message.error("发送失败！");
              }
            });
          },
          onCancel: () => {
            this.$message.info("您已取消操作！");
          },
        });
      }
    },

    //  附件
    uploadChange({ file, fileList }) {
      //删除文件
      if (file.status == "removed") {
        this.handleRemoveFU(file.uid);
      } else {
        let type = 2;
        // 图片0
        // 视频1
        let formData = new FormData();
        formData.append("type", type);
        formData.append("file", file);
        instance_1({
          method: "post",
          url: "/memberComment/upload",
          header: {
            "Content-type": "multipart/form-data", // 默认值
          },
          data: formData,
        }).then((res) => {
          res.data.data.forEach((item) => {
            if (type == 0) {
              this.imgFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
            }
            if (type == 1) {
              this.videoFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
            }
            if (type == 2) {
              this.qitaFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                uid: file.uid,
              });
            }
          });
        });
      }
    },
    //图片上传chage
    handlePicChange({ file, fileList }) {
      //删除文件
      if (file.status == "removed") {
        this.handleRemoveFU(file.uid);
      } else {
        let type = 0;
        console.log(fileList);
        this.imgList = fileList;
        // 图片0
        // 视频1
        let formData = new FormData();
        formData.append("type", type);
        formData.append("file", file);
        instance_1({
          method: "post",
          url: "/memberComment/upload",
          header: {
            "Content-type": "multipart/form-data", // 默认值
          },
          data: formData,
        }).then((res) => {
          res.data.data.forEach((item) => {
            if (type == 0) {
              this.imgFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
            }
            if (type == 1) {
              this.videoFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                src: item.path,
                uid: file.uid,
              });
            }
            if (type == 2) {
              this.qitaFujian.push({
                fileName: item.fileName,
                path: item.path,
                type: type,
                uid: file.uid,
              });
            }
          });
        });
        // this.formData.append("image", file);
      }
    },

    beforeAvatarUploadMP4(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /^.+(\/mp4)$/;
      const isMp4 = reg.test(file.type);
      if (!isMp4) {
        this.$message.error("上传的文件格式只能是mp4");
      }
      if (isLt20M && isMp4) return false;
    },
    // 上传大小限制
    beforeAvatarUploadFU(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /^.+(\/pdf)$/;
      const isPDF = reg.test(file.type);
      if (!isPDF) {
        this.$message.error("上传的文件格式只能是PDF");
      }
      if (isLt20M && isPDF) return false;
    },
    //图片格式限制
    beforeAvatarUpload(file, fileList) {
      var reg = /image\/(png|jpg|gif|jpeg|webp)$/;
      const isJPG = reg.test(file.type);
      const isLt8M = file.size / 1024 / 1024 < 8;
      if (!isJPG) {
        this.$message.error("文件格式不正确，请上传图片!");
      }
      if (!isLt8M) {
        this.$message.error("上传头像图片大小不能超过 8MB!");
      }
      return false;
    },
    // 图片预览
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await URL.createObjectURL(file.originFileObj);
      }
      this.dialogImageUrl = file.url || file.preview;
      this.dialogVisible = true;
    },
    //上传文件大小限制
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      // var reg = /^.+(\/pdf)$/;
      // const isPDF = reg.test(file.type);
      // if (!isPDF) {
      //   this.$message.error("上传的文件格式只能是PDF");
      // }
      // if (isLt20M && isPDF) return false;
    },
    handleRemoveFU(uid) {
      this.imgFujian.forEach((item, index) => {
        if (item.uid == uid) {
          this.imgList.splice(index, 1);
          this.imgFujian.splice(index, 1);
        }
      });
      this.videoFujian.forEach((item, index) => {
        if (item.uid == uid) {
          this.videoFujian.splice(index, 1);
          this.videoFujianData = [];
          this.isDisabled = false;
        }
      });

      this.qitaFujian.forEach((item, index) => {
        if (item.uid == uid) {
          this.qitaFujian.splice(index, 1);
        }
      });
    },

    // 新增数据
    handleAdd() {
      this.editShow = false;
      this.titleText = "新增随手拍";
      this.dialogFormVisible = true;
    },

    modalClose() {
      this.Close();
      this.dialogFormVisible = false;
    },
    Close() {
      (this.isColor = "0"), (this.imgList = []);
      this.progressStatus = 2;
      this.videoFujianData = [];
      this.imgFujian = [];
      this.fujian = [];
      this.address = "";
      this.commentCategory = "城市建设";
      this.comment = "";
      this.content = "";
    },
    modalOk(state) {
      if (
        this.address == "" ||
        this.commentCategory == "" ||
        this.comment == "" ||
        this.subject == ""
      ) {
        this.$baseMessage("内容不完整", "error");
      } else {
        // 判断图片还是视频上传 清空
        this.active == 1 ? (this.videoFujian = []) : (this.imgFujian = []);
        if (this.imgFujian.length != 0) {
          this.imgFujian.forEach((item) => {
            this.fujian.push({
              fileName: item.fileName,
              type: item.type,
              path: item.path,
            });
          });
        }
        if (this.videoFujian.length != 0) {
          this.videoFujian.forEach((item) => {
            this.fujian.push({
              fileName: item.fileName,
              type: item.type,
              path: item.path,
            });
          });
        }
        if (this.qitaFujian.length != 0) {
          this.qitaFujian.forEach((item) => {
            this.fujian.push({
              fileName: item.fileName,
              type: item.type,
              path: item.path,
            });
          });
        }

        instance_1({
          method: "get",
          url: "/liaisonStation/getMyLiaisonStation",
          data: {},
          header: {
            "Content-type": "application/x-www-form-urlencoded",
          },
        }).then((res) => {
          if (res.data.code == "0000") {
            this.interfaceLocation = res.data.data.name;
          }
        });

        //  判断是否为编辑页面
        if (this.editShow) {
          if (state == 1) {
            // 草稿时
            this.progressStatus = "1";
            this.urlData = "/memberComment/update";
          } else {
            this.progressStatus = "3"; //117
            this.urlData = "/memberComment/submit";
          }
        } else {
          // 草稿时
          if (state == 1) {
            this.progressStatus = state;
            this.urlData = "/memberComment/save";
          } else {
            this.progressStatus = "3"; //117
            this.urlData = "/memberComment/insert";
          }
        }

        instance_1({
          method: "post",
          url: this.urlData,
          data: {
            address: this.address,
            attachmentList: this.fujian,
            commentCategory: this.commentCategory, //意见建议内容
            generalComments: this.comment, //概括性意见
            interfaceLocation: this.interfaceLocation,
            // this.station, 联络站点
            subject: this.content, //涉及的主体相关信息
            progressStatus: this.progressStatus, //转发
          },
          header: {
            "Content-type": "application/x-www-form-urlencoded",
          },
        }).then((res) => {
          if (res.data.code == "0000") {
            this.fetchData();
            this.$baseMessage("新增成功", "success");
            this.Close();
            this.dialogFormVisible = false;
          } else {
            this.fujian = [];
            this.$baseMessage(res.data.msg, "error");
          }
        });
      }
    },
    callback(active) {
      this.active = active;
      // 切换
    },
    // 点击行
    clickRow(record) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            if ("__vue__" in event.target) {
              this.handleEdit(record);
            }
          },
        },
      };
    },
    //获取勾选的行数据
    setSelectRows(val) {
      this.selectRows = val;
    },
    rowClick(row, column, event) {
      if (column.label !== "操作") {
        this.handleEdit(row);
      }
    },

    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    //查询
    handleQuery() {
      this.drawer = false;
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },

    //重置查询参数
    handleClear() {
      if (this.$refs["drawerForm"] !== undefined) {
        this.$refs["drawerForm"].resetFields();
      }
    },

    handleEdit(row) {
      this.$router
        .push({
          path: "/recommended/details",
          query: {
            id: row.id,
          },
        })
        .catch(() => {});
    },
    saveCBOrder(id) {
      this.$confirm({
        cancelText: "取消",
        okText: "确定",
        title: "温馨提示",
        content:
          "本次操作没有短信通知，请自行和提出代表沟通，是否确定撤销该随手拍？",
        onOk: () => {
          this.listLoading = true;
          instance_2({
            method: "GET",
            url: "/memberComment/saveCBOrder",
            headers: {
              "Content-Type": "multipart/form-data",
            },
            params: { id: id },
          }).then((data) => {
            this.listLoading = false;
            if (data.data.code == "0000") {
              this.$message.info("撤销成功！");
            } else {
              this.$message.error("撤销失败！");
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },

    //删除
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          instance_1({
            method: "get",
            url: "/memberComment/delete",
            params: {
              id: row.id,
            },
          }).then((res) => {
            if (res.data.code == "0000") {
              this.$baseMessage("删除成功", "success");
              this.fetchData();
            } else {
              this.$baseMessage(res.data.msg, "success");
            }
          });
        });
      } else {
        //批量删除，暂无接口
        if (this.selectRows.length > 0) {
          const ids = this.selectRows.map((item) => item.id);
          this.$baseConfirm("你确定要删除选中项吗", null, () => {});
        } else {
          this.$baseMessage("未选中任何行", "error");
          return false;
        }
      }
    },

    //查询列表数据
    fetchData() {
      this.listLoading = true;
      instance_1({
        method: "post",
        url: "/memberComment/listReport",
        params: this.queryForm,
        data: this.memberCommentParamVO,
      }).then((res) => {
        this.list = res.data.rows;
        this.total = res.data.total;
        this.pagination.total = res.data.total;
        setTimeout(() => {
          this.listLoading = false;
        }, 500);
      });
    },
    onprogressStatus(value) {
      this.progressStatus = value.target.value;
    },
    fnColor(index, title) {
      this.commentCategory = title;
      this.isColor = index;
    },

    checkPermission,
  },
};
</script>
<style lang="scss" scoped>
.tableLimit tr td .cell {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important; /*可以显示的行数，超出部分用...表示 */
  -webkit-box-orient: vertical !important;
}
.tableLimit .el-button--small {
  padding: 0 !important;
}
.Data-value {
  display: flex;
  line-height: 34px;
  margin-top: 10px;
  .title {
    // font-size: 14px;
    @include add-size($font_size_16);
    font-family: PingFang-M;
  }
  .contentBox {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    height: 130px;
    .contents {
      width: 100px;
      height: 30px;
      line-height: 30px;
      margin-left: 10px;
      text-align: center;
      border-radius: 6px;
      border: 1px solid rgb(214, 208, 208);
    }
  }
}
.textarea {
  margin-left: 10px;
  width: 400px;
}
.bgColor {
  background-color: #cc3031;
  color: #fff;
}
.upload-list-inline >>> .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-right: 8px;
}
.upload-list-inline >>> .ant-upload-animate-enter {
  animation-name: uploadAnimateInlineIn;
}
.upload-list-inline >>> .ant-upload-animate-leave {
  animation-name: uploadAnimateInlineOut;
}
.ant-upload-picture-card-wrapper {
  display: flex;
}
</style>
