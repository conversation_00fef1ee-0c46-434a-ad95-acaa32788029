import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/meetingDining/canteen/getList",
    method: "post",
    data,
  });
}

export function getPage(data) {
  return request({
    url: "/api/v1/meetingDining/canteen/getPage",
    method: "post",
    data,
  });
}

export function getById(data) {
  return request({
    url: "/api/v1/meetingDining/canteen/getById",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function(data) {
          return qs.stringify(data);
      },
    ],
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/meetingDining/canteen/save",
    method: "post",
    data,
  });
}

export function doRemove(data) {
  return request({
    url: "/api/v1/meetingDining/canteen/remove",
    method: "post",
    data,
  });
}

export function getHotelList(data) {
  return request({
    url: "/api/v1/meetingDining/canteen/getHotelList",
    method: "post",
    data,
  });
}