<template>
  <div class="table-container">
    <a-form layout="horizontal"
            :labelCol="{ span: 6 }"
            :wrapperCol="{ span: 18, offset: 0 }">
      <!-- <a-row style="margin-left:1%">
        <a-col :md="6"
               :sm="24">
          <a-form-item label="大会名称"
                       :labelCol="{ span: 6 }"
                       :wrapperCol="{ span: 18, offset: 0 }">
            <a-input v-model="queryForm.dhmc"
                     allow-clear
                     placeholder="请输入大会名称"
                     v-on:keyup.enter="search"></a-input>
          </a-form-item>
        </a-col>
        <a-col :md="6"
               :sm="24">
          <a-form-item label="行政区域"
                       :label-col="{ span: 7 }"
                       :wrapper-col="{ span: 16, offset: 1 }">
            <a-select v-model="queryForm.administrativeAreaId"
                      allow-clear
                      placeholder="请选择行政区域">
              <a-select-option v-for="item in administrativeAreaList"
                               :key="item.id"
                               :value="item.id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="6"
               :sm="24">
          <span style="float: right; margin-top: 3px;">
            <a-button type="primary"
                      @click="search">搜索</a-button>
            <a-button style="margin-left: 12px;"
                      @click="reset"
                      class="pinkBoutton">重置</a-button>
          </span>
        </a-col>
      </a-row> -->

      <SearchForm @onReset="reset" @onSearch="search" :noMore="true">
        <template v-slot:topSearch>
          <SingleSearch @onEnter="handleEnter" :title="'层级名称'" :value.sync="queryForm.dhmc" />
          <SingleSelect :title="'行政区域'" :selectList="administrativeAreaList" :value.sync="queryForm.administrativeAreaId" />
        </template>
      </SearchForm>

      <a-row>
        <a-col :md="6"
               :sm="24">
          <a-button style="margin: 10px;"
                    type="primary"
                    native-type="submit"
                    @click="handleAdd">新增</a-button>
        </a-col>
      </a-row>
    </a-form>
    <standard-table :columns="columns"
                :data-source="list"
                :pagination="pagination"
                :loading="TBloading"
                @tableClick="clickRow"></standard-table>


    <a-modal :title="title"
             destroy-on-close
             :visible.sync="visibleShow"
             width="600px"
             @cancel="close">
      <div slot="footer"
           class="dialog-footer"
           style="position: relative; padding-right: 15px; text-align: right;">
        <a-button @click="close">关闭</a-button>
        <a-button :disabled="isShow"
                  type="primary"
                  style="margin-left: 10px;"
                  @click="confirm">确定</a-button>
      </div>

      <a-form-model ref="noticeForm"
                    :label-col="{ span: 6 }"
                    :rules="rules"
                    :wrapper-col="{ span: 16 }"
                    :model="forValue">
        <a-form-model-item label="层级名称："
                           prop="dhmc">
          <a-input v-model="forValue.dhmc"
                   :disabled="isShow"
                   placeholder="请输入层级名称"></a-input>
        </a-form-model-item>
        <a-form-model-item label="所属行政区域"
                           prop="xzqhDm">
          <a-select v-model="forValue.xzqhDm"
                    show-search
                    :disabled="isShow"
                    :filter-option="filterOption"
                    option-filter-prop="children"
                    allow-clear
                    placeholder="请选择身份">
            <a-select-option v-for="item in administrativeAreaList"
                             :key="item.id"
                             :value="item.id">{{ item.name }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
            label="关联单位"
            prop="orgList"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
        >
          <a-input-search
            class="input-search"
            v-model="forValue.orgNameDesc"
            enter-button
            @search="handlePublish"
          ></a-input-search>
        </a-form-model-item>
        <a-form-model-item label="身份类型"
                           prop="sfDm">
          <a-select v-model="forValue.sfDm"
                    show-search
                    :disabled="isShow"
                    :filter-option="filterOption"
                    option-filter-prop="children"
                    allow-clear
                    placeholder="请选择身份">
            <a-select-option v-for="item in sfDmList"
                             :key="item.sfDm"
                             :value="item.sfDm">{{ item.sfmc }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="选用标志">
          <a-radio-group v-model="forValue.isUse"
                         :disabled="isShow">
            <a-radio value="0">非当前选用标志</a-radio>
            <a-radio value="1">当前选用标志</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <orgTreeDialog
      ref="orgTreeDialog"
      :root-org-id="unit"
      @confirm="confirmOrg"
      :checkStrictly="true"
      :checkable="true"
    ></orgTreeDialog>


    <a-modal title="设置可添加代表的节点"
             destroy-on-close
             :visible.sync="visibleCanAddDbShow"
             width="80%"
             @cancel="close">
      <div slot="footer"
           class="dialog-footer"
           style="position: relative; padding-right: 15px; text-align: right;">
        <a-button @click="close">关闭</a-button>
        <a-button :disabled="isShow"
                  type="primary"
                  style="margin-left: 10px;"
                  @click="confirmCanAdd">确定</a-button>
      </div>
        <a-form-model>
          <a-form-model-item>
            <a-checkbox-group v-model="checkedList" style="width: 100%;max-height:300px;overflow-y:scroll;">
              <a-row>
                <a-col :span="6" :key="item.id" v-for="(item) in forValue.orgList" style="padding: 10px 0;">
                  <a-checkbox :value="item.id" >{{item.orgName}}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-model-item>
        </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { myPagination } from "@/mixins/pagination.js";
import {
  getList,
  getById,
  getSfDmList,
  create,
  updateById,
  saveCanAddDbNode
} from "@/api/system/dbdh.js";
import { getList as getAdministrativeAreaList } from "@/api/administrativearea.js";
import orgTreeDialog from "@/views/common/orgTreeDialog.vue";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';
export default {
  mixins: [myPagination],
  components: {
    orgTreeDialog, SingleSelect, SearchForm, SingleSearch
  },
  data () {
    return {
      queryPage: {
        pageNum: 1,
        pageSize: 10,
      },
      queryForm: {
        dhmc: "",
        administrativeAreaId: ""
      },
      title: '',
      isShow: false,
      TBloading: false,
      forValue: {
        dhmc: "",
        xzqhDm: "",
        orgNameDesc: "",
        orgList: [],
        sfDm: "",
      },
      sfDmList: [],
      visibleShow: false,
      visibleCanAddDbShow: false,
      checkedList: [],
      rules: {
        dhmc: [
          { required: true, message: "请填写层级名称", trigger: "blur" },
        ],
        xzqhDm: [
          { required: true, message: "请选择所属行政区域", trigger: "blur" },
        ],
        orgList: [
          { required: true, message: "请选择层级所对应的挂载代表节点", trigger: "blur" },
        ],
        sfDm: [
          { required: true, message: "请选择层级所对应代表身份", trigger: "blur" },
        ],
        isUse: [
          { required: true, message: "请选择选用标志", trigger: "blur" },
        ],
      },
      administrativeAreaList: [],
      // 回显id
      orgIdList: [],
      unit: "root",
      columns: [
        {
          title: "层级名称",
          align: "center",
          ellipsis: true,
          dataIndex: "dhmc",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 300,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleData(record.dhDm, false);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(record.dhDm);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleSetCanAddDb(record.dhDm);
                    },
                  },
                },
                "设置可添加代表节点"
              ),
            ]);
          },
        },
      ],
      list: [],
      sessionIdenList: [
        {name:'非当前届次', id:'0'},
        {name:'当前届次', id:'1'},
      ],
      searchDebounced: _.debounce(this.search, 500),
    }
  },
  mounted() {
    this.getSfDmList()
    this.getAdministrativeStateList()
    this.fetchData()
  },
  methods: {
    // 点击行
    clickRow (event, record) {

    },
    async fetchData () {
      let res = await getList(this.queryPage, this.queryForm);
      if (res.code == "200") {
        this.list = res.rows;
        this.pagination.total = res.total;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    async getSfDmList() {
      getSfDmList().then(res => {
        this.sfDmList = res.data
      })
    },
    handleAdd () {
      this.title = "新增数据";
      this.visibleShow = true;
      this.iShow = false;
    },
    handleEdit (id) {
      this.title = "编辑数据";
      this.handleData(id, true);
      this.visibleShow = true;
    },
    search () {
      this.queryPage.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    async handleData (id, edit) {
      edit ? (this.title = "编辑数据") : (this.title = "查看数据");
      edit ? (this.isShow = false) : (this.isShow = true);

      let res = await getById({ dhDm : id });
      if (res.code == "0000") {
        // 回显
        let {
          dhDm,
          dhmc,
          jgId,
          isUse,
          xzqhDm,
          dbsf,
          orgList
        } = res.data;
        this.orgIdList = []
        this.forValue.dhDm = dhDm;
        this.forValue.dhmc = dhmc;
        this.forValue.jgId = jgId;
        this.forValue.isUse = isUse;
        this.forValue.xzqhDm = xzqhDm;
        this.forValue.sfDm = dbsf.sfDm;
        this.forValue.orgList = orgList;

        // 回显checkbox
        this.checkedList = [];
        orgList.forEach(item => {
          this.forValue.orgNameDesc += item.orgName + "；";
          this.orgIdList.push(item.orgId)
          if(item.canAddDb) {
            this.checkedList.push(item.id)
          }
        });
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },

    close () {
      this.forValue = {
        dhDm: "",
        dhmc: "",
        jgId: "",
        xybz: "",
        xzqhDm: "",
        sfDm: "",
        orgList: [],
        orgNameDesc: ""
      };
      this.title = "";
      this.visibleShow = false;
      this.visibleCanAddDbShow = false;
      this.isShow = false;
    },

    confirm() {
      this.$refs["noticeForm"].validate(async (valid) => {
        if (valid) {
          if (this.title == "新增数据") {
            let res = await create(this.forValue);
            if (res.code == "0000") {
              this.$message.success("请求成功");
              this.fetchData();
              this.visibleShow = false;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          } else {
            console.log(this.forValue)
            let res = await updateById(this.forValue);
            if (res.code == "0000") {
              this.$message.success("请求成功");
              this.fetchData();
              this.visibleShow = false;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          }
          this.close();
        }
      });
    },
    reset () {
      this.queryPage = {
        pageNum: 1,
        pageSize: 10,
      };
      this.queryForm = {
        dhmc: "",
        administrativeAreaId: ""
      }
      this.fetchData();
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    //补选单位
    async getAdministrativeStateList () {
      let res = await getAdministrativeAreaList()
        this.administrativeAreaList = res.data
    },
    // 机构选择器
    handlePublish() {
      this.$refs.orgTreeDialog.open(this.orgIdList);
    },
    confirmOrg(checkedNodes) {
      //清空原有的发布单位
      if (checkedNodes.length > 0) {
        this.forValue.orgNameDesc = ""
        checkedNodes.forEach(item => {
          this.forValue.orgNameDesc += item.orgName + "；";
        });
        this.forValue.orgList = checkedNodes;
      }
    },
    handleSetCanAddDb(id) {
      this.handleData(id, true);
      this.visibleCanAddDbShow = true
    },
    confirmCanAdd() {
      // this.checkedList
      // this.forValue.dhDm
      // this.
      let data = {
        dhDm: this.forValue.dhDm,
        ids: this.checkedList
      }
      saveCanAddDbNode(data).then(res => {
        if (res.code == "0000") {
          this.$message.success("设置成功");
          this.visibleCanAddDbShow = false
        }
      })
    },
    handleEnter() {
      this.searchDebounced();
    },
  }
}
</script>

<style lang="scss" scoped>
.input-search {
  ::v-deep {
    .ant-input {
      pointer-events: none !important;
    }
  }
}
</style>