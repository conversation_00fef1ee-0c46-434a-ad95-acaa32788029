import request from "@/utils/requestTemp";
import { instance_1 } from "@/api/axiosRq";
import qs from "qs";

export function getAreaList(data) {
  return request({
    url: "/administrativeArea/list",
    method: "get",
    params: data,
  });
}

export function getStreetList(data) {
  //data = qs.stringify(data)
  return request({
    url: "/streetTown/list",
    method: "get",
    params: data,
    //headers: {"Content-Type":"application/x-www-form-urlencoded"}
  });
}

export function backlogList(params) {
  return request({
    url: "/pushdata/task/handled",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}

export function repList(params) {
  return request({
    url: "/queryStatistics/change/query",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}

export function FormOrg(params) {
  return request({
    url: "/behalfQuery/findFormOrg",
    method: "post",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}

export function backlogData(params) {
  return request({
    url: "/task/todo",
    method: "post",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}
export function backlogValue(params, data) {
  return request({
    url: "/dutyActive/todo",
    method: "post",
    params,
    data: data
  });
}

export function backlogHandle(params, data) {
  return request({
    url: "/dutyActive/handled",
    method: "post",
    params,
    data: data

  });
}

export function backlogHandleOrg(params, data) {
  return request({
    url: "/dutyActive/handledOrg",
    method: "post",
    params,
    data: data

  });
}

export function firstAudit(params) {
  return request({
    url: "/dutyActive/firstAudit",
    method: "post",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}

export function finalAudit(params) {
  return request({
    url: "/dutyActive/finalAudit",
    method: "post",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}

export function handledData(params) {
  return request({
    url: "/task/handled",
    method: "post",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}

export function baseList(params) {
  return request({
    url: "/queryStatistics/BasicStatistics/list",
    method: "post",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}

export function rosterList(params) {
  return request({
    url: "/queryStatistics/ListOfStatistics/list",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}
export function periodData(params) {
  return request({
    url: "/organization/findLevelList",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}

// 基础信息下拉框的值
export function baseSelect() {
  return request({
    url: "/queryStatistics/BasicStatistics/getComboBoxData",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

export function SettingList(params) {
  return request({
    url: "/SysUserSetting/list",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}

export function AddSetting(data) {
  return request({
    url: "/SysUserSetting/add",
    method: "post",
    data,
  });
}

export function EditSetting(data) {
  return request({
    url: "/SysUserSetting/edit",
    method: "post",
    data,
  });
}

export function getUserInfo(params) {
  return request({
    url: "/SysUserSetting/getUserInfo",
    method: "get",
    params,
  });
}

export function conditionList(params) {
  return request({
    url: "/queryStatistics/change/query",
    method: "get",
    params,
  });
}

export function signList(params) {
  return request({
    url: "/management/list",
    method: "get",
    params,
  });
}

export function addSignList(data) {
  return request({
    url: "/management",
    method: "post",
    data,
  });
}

export function SignData(data) {
  let id = data;
  return request({
    url: `/management/${id}`,
    method: "get",
  });
}

export function editSignList(data) {
  return request({
    url: "/management",
    method: "PUT",
    data,
  });
}

export function deleSignList(data) {
  let id = data;
  return request({
    url: `/management/${id}`,
    method: "delete",
  });
}
export function deleAsDynamicList(data) {
  let id = data;
  return request({
    url: `/information/${id}`,
    method: "delete",
  });
}

export function remindList(params) {
  return request({
    url: "/activityMsg/list",
    method: "get",
    params,
  });
}

export function remindData(params) {
  return request({
    url: "/activityMsg/selectByid",
    method: "get",
    params,
  });
}

export function stateList(params) {
  return request({
    url: "/queryStatistics/stateChanges/list",
    method: "get",
    params,
  });
}

// 单独获取届次接口
export function selectList() {
  return request({
    url: "/queryStatistics/stateChanges/getLevel",
    method: "get",
  });
}

// 状态下拉框
export function selectState() {
  return request({
    url: "/queryStatistics/stateChanges/getCurrentState",
    method: "get",
  });
}

// 代表状态页面变更下拉框
export function repreState() {
  return request({
    url: "/queryStatistics/stateChanges/getState",
    method: "get",
  });
}

// 分析数据
export function analyzeList(params) {
  return request({
    // url: "/activityReport/selectBylist",
    url: "/activityOrgReport/selectBylist",
    method: "get",
    params,
  });
}

export function addAsDynamicList(data) {
  return request({
    url: "/information",
    method: "post",
    data,
  });
}

export function AsDynamicList(params) {
  return request({
    url: "/information/list",
    method: "get",
    params,
  });
}

export function AsDynamicData(data) {
  let id = data;
  return request({
    url: `/information/${id}`,
    method: "get",
  });
}

export function editAsDynamicList(data) {
  return request({
    url: "/information",
    method: "PUT",
    data,
  });
}

// 届次管理
export function levelList(params) {
  return request({
    url: "/sysManager/level/findAllLevel",
    method: "post",
    params,
  });
}

export function addlevelList(params) {
  return request({
    url: "/sysManager/level/add",
    method: "post",
    params,
  });
}

export function editlevelList(params) {
  return request({
    url: "/sysManager/level/update",
    method: "post",
    params,
  });
}
export function levelListData(params) {
  return request({
    url: "/sysManager/level/findLevel",
    method: "get",
    params,
  });
}

export function delelevelList(params) {
  return request({
    url: "/sysManager/level/delete",
    method: "get",
    params,
  });
}
export function conventionData(params) {
  return request({
    url: "/sysManager/level/findAllPlenary",
    method: "get",
    params
  });
}

export function dynamicList(params) {
  return request({
    url: "/representative/list",
    method: "get",
    params,
  });
}

export function occupationEcharts(params) {
  return request({
    url: "/mobile/representativeInfo/queryProfessionConstitute",
    method: "get",
    params,
  });
}

export function synthesizeEcharts(params) {
  return request({
    url: "/mobile/representativeInfo/querySynthesizeConstitute",
    method: "get",
    params,
  });
}
//代表类别统计图
export function getqueryBehalfTypeApi(params) {
  return request({
    url: "/mobile/representativeInfo/queryBehalfType",
    method: "get",
    params,
  });
}

export function batchCommitteeList(params) {
  return request({
    url: "/personalDetails/list",
    method: "get",
    params,
  });
}

// 下载代表管理模板

export function downloadElx(params) {
  return instance_1({
    url: "/personalDetails/templateDownload",
    method: "get",
    responseType:"blob",
    params : params,
  });
}

export function batchDelete(params) {
  return request({
    url: "/personalDetails/deleteUploadInfo",
    method: "post",
    params,
  });
}

export function batchData(params) {
  return request({
    url: "/personalDetails/details",
    method: "get",
    params,
  });
}

export function batchInc(params) {
  return request({
    url: "/personalDetails/uploadInfo",
    method: "post",
    params,
  });
}
//人民代表大会登记表
export function getBehalfMeetingDataApi(params) {
  return request({
    url: "/mobile/representativeInfo/getBehalfMeetingData",
    method: "get",
    params,
  });
}
//2
export function getgetBehalfMeetingDataTwoApi(params) {
  return request({
    url: "/mobile/representativeInfo/getBehalfMeetingDataTwo",
    method: "get",
    params,
  });
}
//3
export function getBehalfMeetingDataThreeApi(params) {
  return request({
    url: "/mobile/representativeInfo/getBehalfMeetingDataThree",
    method: "get",
    params,
  });
}
//状态变动
export function gettimelineApi(params) {
  return request({
    url: "/representative/timeline",
    method: "post",
    params,
  });
}
//广州市人大常委会会议出勤情况表
export function getcwhhycqqkApi(params) {
  return request({
    url: "/dblz/lzbb/cwhzccylzsc/cwhhycqqk?jc=2&nf=2017&cwhzccyId=45929",
    method: "get",
    params,
  });
}
//重要会议和活动出勤情况表
export function getzyhyhdcqqkApi(params) {
  return request({
    url: "/dblz/lzbb/cwhzccylzsc/zyhyhdcqqk?jc=2&nf=2017&cwhzccyId=46095",
    method: "get",
    params,
  });
}
//提出议案及意见、批评和建议情况一览表
export function getfindByDbApi(params) {
  return request({
    url: "/statistics/legacy/yajy/findByDb?jcDm=3&startTime=2020-01-01&endTime=2022-12-31&dbId=0cd7eaa7033d4088837096baae3797dc",
    method: "get",
    params,
  });
}

//导入表文件
export function uplaodDBApi(fromData, id) {
  return instance_1({
    url: `/personalDetails/uploadInfo`,
    method: "post",
    data: fromData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

export function getNewRelList(param, data) {
  return request({
    url: "/streetTownUser/getTree" ,
    method: "get",
  });
}

export function getRelList(param) {
  return request({
    url: "/streetTownUser/getStreetTownUserList" ,
    method: "get",
    params:param
  });
}

export function doSaveRel(data) {
  return request({
    url: "/streetTownUser/insertStreetTownUser",
    method: "post",
    data,
  });
}

export function doRelOldData(data) {
  return request({
    url: "/streetTownUser/deleteByIds",
    method: "post",
    data,
  });
}


export function listForDuty(params) {
  return request({
    url: "/queryStatistics/ListOfStatistics/listForDuty",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params,
  });
}
