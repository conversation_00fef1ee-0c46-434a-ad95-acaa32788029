import request from "@/utils/requestTemp";

export function getMsgTemplateList(params) {
  return request({
    url: "/msg/template/list",
    method: "get",
    params
  });
}

export function findParams(params) {
  return request({
    url: "/msg/template/findParams",
    method: "get",
    params
  });
}

export function updateContent(params) {
  return request({
    url: "/msg/template/updateContent",
    method: "post",
    params
  });
}
