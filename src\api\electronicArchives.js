import request from "@/utils/requestTemp";
import { instance_1 } from "@/api/axiosRq";
import qs from "qs";

export function getInfo(data) {
  return request({
    url: "/electronicArchives/getInfo",
    method: "post",
    data: data,
  });
}

// --------------------------------意见建议被《人大代表情况反映》情况--------------------------------
export function getAchievementListVo(params) {
  return request({
    url: "/achievement/listVo",
    method: "get",
    params: params,
  });
}

export function getAchievementById(params) {
  return request({
    url: "/achievement/getById",
    method: "get",
    params: params,
  });
}

export function createAchievement(data) {
  return request({
    url: "/achievement/create",
    method: "post",
    data: data,
  });
}

export function updateAchievement(data) {
  return request({
    url: "/achievement/update",
    method: "post",
    data: data,
  });
}

//下载意见建议被《人大代表情况反映》情况模版
export function downloadAchievementExcelTemplete(params) {
  return instance_1({
    url: "/achievement/downloadTemplate",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//导入意见建议被《人大代表情况反映》情况
export function importAchievementByExcel(data) {
  return instance_1({
    url: "/achievement/importByExcel",
    method: "post",
    data: data,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}

export function exportAchievementExcel(params) {
  return instance_1({
    url: `/achievement/export`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function deleteAchievementById(params) {
  return request({
    url: "/achievement/deleteById",
    method: "post",
    params: params
  });
}

export function getAchievementItemListVo(params) {
  return request({
    url: "/achievement/listItemVo",
    method: "get",
    params: params
  });
}
export function addAchievementItem(data) {
  return request({
    url: "/achievement/addItem",
    method: "post",
    data: data,
  });
}

export function deleteAchievementItemById(params) {
  return request({
    url: "/achievement/deleteItemById",
    method: "post",
    params: params
  });
}
// --------------------------------意见建议被《人大代表情况反映》情况--------------------------------

// --------------------------------获奖情况--------------------------------
export function getDutyActivityAwardListVo(params) {
  return request({
    url: "/dutyActivityAward/listVo",
    method: "get",
    params: params,
  });
}
export function getDutyActivityAwardItemListVo(params) {
  return request({
    url: "/dutyActivityAward/listItemVo",
    method: "get",
    params: params
  });
}
export function getDutyActivityAwardById(params) {
  return request({
    url: "/dutyActivityAward/getById",
    method: "get",
    params: params,
  });
}

export function createDutyActivityAward(data) {
  return request({
    url: "/dutyActivityAward/create",
    method: "post",
    data: data,
  });
}

export function updateDutyActivityAward(data) {
  return request({
    url: "/dutyActivityAward/update",
    method: "post",
    data: data,
  });
}

export function addDutyActivityAwardItem(data) {
  return request({
    url: "/dutyActivityAward/addItem",
    method: "post",
    data: data,
  });
}

export function deleteDutyActivityAwardItemById(params) {
  return request({
    url: "/dutyActivityAward/deleteItemById",
    method: "post",
    params: params
  });
}

//下载获奖情况模版
export function downloadDutyActivityAwardExcelTemplete(params) {
  return instance_1({
    url: "/dutyActivityAward/downloadTemplate",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//导入获奖情况
export function importDutyActivityAwardByExcel(data) {
  return instance_1({
    url: "/dutyActivityAward/importByExcel",
    method: "post",
    data: data,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}

export function exportDutyActivityAwardExcel(params) {
  return instance_1({
    url: `/dutyActivityAward/export`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function deleteDutyActivityAwardById(params) {
  return request({
    url: "/dutyActivityAward/deleteById",
    method: "post",
    params: params
  });
}
// --------------------------------获奖情况--------------------------------

// --------------------------------履职事迹被《人大代表履职周报》刊登次数--------------------------------
export function getResumptionWeekUserRecordListVo(params) {
  return request({
    url: "/resumptionWeekUserRecord/listVo",
    method: "get",
    params: params,
  });
}

export function getResumptionWeekItemListVo(params) {
  return request({
    url: "/resumptionWeekUserRecord/listItemVo",
    method: "get",
    params: params,
  });
}

export function getResumptionWeekUserRecordById(params) {
  return request({
    url: "/resumptionWeekUserRecord/getById",
    method: "get",
    params: params,
  });
}

export function createResumptionWeekUserRecord(data) {
  return request({
    url: "/resumptionWeekUserRecord/create",
    method: "post",
    data: data,
  });
}

export function updateResumptionWeekUserRecord(data) {
  return request({
    url: "/resumptionWeekUserRecord/update",
    method: "post",
    data: data,
  });
}

export function addItem(data) {
  return request({
    url: "/resumptionWeekUserRecord/addItem",
    method: "post",
    data: data,
  });
}

//下载获奖情况模版
export function downloadResumptionWeekUserRecordExcelTemplete(params) {
  return instance_1({
    url: "/resumptionWeekUserRecord/downloadTemplate",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//导入获奖情况
export function importResumptionWeekUserRecordByExcel(data) {
  return instance_1({
    url: "/resumptionWeekUserRecord/importByExcel",
    method: "post",
    data: data,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}

export function exportResumptionWeekUserRecordExcel(params) {
  return instance_1({
    url: `/resumptionWeekUserRecord/export`,
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function deleteResumptionWeekPeriodById(params) {
  return request({
    url: "/resumptionWeekUserRecord/deleteById",
    method: "post",
    params: params
  });
}

export function deleteResumptionWeekUserRecordById(params) {
  return request({
    url: "/resumptionWeekUserRecord/deleteItemById",
    method: "post",
    params: params
  });
}

//导入获奖情况
export function importResumptionWeekUserRecordByWord(data) {
  return instance_1({
    url: "/resumptionWeekUserRecord/importByWord",
    method: "post",
    data: data,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}

//导入获奖情况
export function insertResumptionWeekUserRecordByWord(data) {
  return request({
    url: "/resumptionWeekUserRecord/insertByWord",
    method: "post",
    data: data,
  });
}

// --------------------------------履职事迹被《人大代表履职周报》刊登次数--------------------------------



export function downloadInfoExcel(data) {
  return instance_1({
    url: `/electronicArchives/downloadInfo`,
    method: "post",
    responseType: "blob",
    data: data,
  });
}

export function batchDownloadInfo(data) {
  return instance_1({
    url: `/electronicArchives/batchDownloadInfo`,
    method: "post",
    responseType: "blob",
    data: data,
  });
}


export function getAllRank(data,params) {
  return request({
    url: "/electronicArchives/getAllRank",
    method: "post",
    data: data,
    params: params
  });
}

export function exportExcel(data) {
  return instance_1({
    url: "/electronicArchives/exportExcel",
    method: "post",
    responseType: "blob",
    data: data,
  });
}


