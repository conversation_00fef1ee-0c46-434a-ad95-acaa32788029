<template>
  <!-- <div v-if="this.currentActivity.id != ''"> -->
  <div>
    <a-modal :dialog-style="{ top: '0px' }"
             title="送审"
             :model="addForm"
             :visible.sync="visible"
             width="50%"
             @cancel="close">
      <a-spin :spinning="iSpinning">
      <a-row class="liucheng lcmcs"
             style="padding-left: 5px;">
        <a-col span="4"
               class="lcmcsss">流程名称</a-col>
        <a-col span="20">{{ processDefinition.name }}</a-col>
      </a-row>
      <a-row class="dqjc">
        <a-col span="4"
               class="lcmcsss">当前进度</a-col>
        <a-col span="8"
               class="lcmcsss">{{ currentActivity.name }}</a-col>
        <a-col span="4"
               class="lcmcsss">上一节点</a-col>
        <a-col span="8">
          <span>{{ prevActivity.name }}</span>
        </a-col>
      </a-row>
      <a-row class="xbcl"
             style="padding-left: 5px;">
        <a-col span="4"
               class="lcmcsss">下步处理</a-col>
        <a-col span="20">
          <a-radio-group v-for="item in nextActivities"
                         :key="item.id"
                         v-model="nextActivityId"
                         :style="radioColor"
                         @change="onChange">
            <a-radio :value="item">{{ item.name }}</a-radio>
          </a-radio-group>
        </a-col>
      </a-row>
      <a-row class="liucheng cyren">
        <a-col span="12"
               class="cyrss"
               style="height: 200px;">
          <a-tabs type="card"
                  default-active-key="3"
                  style="user-select: none;"
                  @change="callback">
            <!-- <a-tab-pane key="1" tab="常用人" :style="keys == '1' ? cy : ''">
              <a-radio-group
                v-model="value"
                v-for="item in lists"
                :key="item.posId"
                @change="isChange"
              >
                <a-radio
                  :style="radioStyle"
                  :value="item.posId"
                >{{ item.assignee.fullName }}，{{ item.assignee.posName }}</a-radio>
              </a-radio-group>
            </a-tab-pane>
            <a-tab-pane key="2" tab="机构人员" force-render :style="keys == '2' ? jg : ''">
              <a-tree
                :disable-branch-nodes="true"
                v-model="reviewOrgId"
                show-checkbox
                checkable
                ref="tree"
                :check-strictly="true"
                :replace-fields="radioStyle"
                :tree-data="treeData"
                @check="onSelect"
              >
                <template slot="title" slot-scope="{ fullName }">
                  <span>{{ fullName }}</span>
                </template>
              </a-tree>
            </a-tab-pane>-->
            <a-tab-pane key="3"
                        tab="机构人员"
                        force-render
                        :style="hx">
              <div v-if="!isTaskClaim">
                <a-radio-group v-for="item in hxrList"
                               :key="item.userId"
                               v-model="hxrvalue"
                               @change="hxrChange">
                  <a-radio :style="radioStyle"
                           :value="item.userId">{{ item.realName ? item.realName + "，" : ""
                    }}{{ item.deptName }}</a-radio>
                </a-radio-group>
              </div>
              <div v-if="isTaskClaim">
                <div style="width: 100%;">
                  <a @click="isShowTaskClaimUser = !isShowTaskClaimUser">由该部门相关管理员审核</a>
                </div>
                <a-checkbox-group v-show="isShowTaskClaimUser"
                                  v-model="hxrvalue">
                  <a-checkbox v-for="item in hxrList"
                              :key="item.userId"
                              :value="item.userId"
                              disabled="disabled"
                              style="margin-left: 0; width: 100%;">
                    {{ item.realName ? item.realName + "，" : ""
                    }}{{ item.deptName }}
                  </a-checkbox>
                </a-checkbox-group>
              </div>
            </a-tab-pane>
          </a-tabs>
        </a-col>
        <a-col span="12"
               style="margin-top: 75px; padding-left: 15px;">
          <a-radio-group v-for="item in lists"
                         :key="item.posId"
                         v-model="value">
            <span v-show="value == item.posId"
                  :style="radioStyle"
                  :value="item.posId"
                  @change="qwe">{{ item.assignee.fullName ? item.assignee.fullName + "，" : ""
              }}{{ item.assignee.posName }}</span>
          </a-radio-group>

          <a-radio-group v-model="isReviewOrgId">
            <span v-show="isReviewOrgId.fullName != ''">{{ isReviewOrgId.fullName ? isReviewOrgId.fullName + "，" : ""
              }}{{ isReviewOrgId.posName }}</span>
          </a-radio-group>

          <a-radio-group v-for="item in hxrList"
                         :key="item.userId"
                         v-model="hxrvalue">
            <span v-show="hxrvalue == item.userId"
                  :value="item.userId"
                  @change="qwe">{{ item.realName }},{{ item.deptName }}</span>
          </a-radio-group>
        </a-col>
      </a-row>
      <a-row class="liucheng ssyj">
        <a-col span="2"
               style="padding: 5px 0 0 5px;">
          <span class="">送审意见</span>
        </a-col>
        <a-col span="20"
               style="padding: 8px 15px 0 0;">
          <a-form-model-item prop="comment">
            <a-input v-model="comment"
                     style="height: 100px;"
                     type="textarea" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row class="xxts"
             style="padding: 8px 0;">
        <a-col span="2"
               style="padding-left: 5px;">提醒信息</a-col>
        <a-col span="13">
          <a-form-model-item>
            <a-input v-model="message"
                     style="width: 400px; height: 100px;"
                     type="textarea" />
          </a-form-model-item>
        </a-col>

        <a-col span="9"
               style="margin-top: 0px;">
          <a-checkbox v-model="isSendSMS"
                      value="sendSMS"
                      @change="asd">发送微信或短信消息提醒</a-checkbox>
          <p class="wxts">
            【温馨提示】若对方没有关注“广州人大服务号”无法收到提醒时，将自动发送手机短信提醒。
          </p>
        </a-col>
      </a-row>
      </a-spin>
      <a-row slot="footer">
        <a-spin :spinning="iSpinning" :indicator="indicator">
        <a-col :span="8"
               push="8">
          <a-space>
            <a-button @click="save">
              <a-icon type="check" />发送
            </a-button>
            <a-button @click="isclose">
              <a-icon type="close" />取消
            </a-button>
            <a-button @click="lczzjl">流程流转记录</a-button>
          </a-space>
        </a-col>
        </a-spin>
      </a-row>
    </a-modal>
    <lczzjl ref="lczzjl"
            :proc-inst-id="procInstId"></lczzjl>
  </div>
</template>

<script>
//树形控件
import lczzjl from "./lczzjl";
import {
  getprocessApi,
  gethistoryAssigneesApi,
  getfindApprovableODUApi,
  getcompleteApi,
  getgetNextUsersApi,
  getgetHistoryTasksApi,
} from "@/api/representativeElection/candidateApi.js";
import { log } from "@antv/g2plot/lib/utils";
import { red } from "color-name";

export default {
  components: { lczzjl },
  props: ["procInstId", "ids"],
  data () {
    return {
      radioColor: {
        borderColor: "black",
      },
      indicator: <a-icon/>,
      iSpinning: false,
      activeKey: "3",
      isSendSMS: false,
      visible: false,
      showIcon: false,
      showLine: true,
      threalName: "",
      childrenName: "",
      // 上一节点
      prevActivity: {},
      //流程名称
      processDefinition: {},
      //当前进度
      currentActivity: {},
      // 下步处理
      nextActivities: [],
      lists: [],
      treelist: [],
      treeData: [],
      value: 0,
      hxrvalue: [],
      duanxing: 1,
      hx: {
        height: "145px",
        overflow: "auto",
        paddingLeft: "5px",
      },
      jg: {
        height: "145px",
        overflow: "auto",
        paddingLeft: "5px",
      },
      cy: {
        height: "145px",
        overflow: "auto",
        paddingLeft: "5px",
      },
      radioStyle: {
        display: "block",
        height: "30px",
        lineHeight: "30px",
        title: "fullName",
        key: "id",
      },
      keys: "",
      hxrList: [],
      taskDefinitionKey: "",
      taskId: "",
      addForm: {},
      sendSMS: "",
      message: "",
      comment: "",
      flowNo: "",
      nextActivityId: "",
      procDefKey: "",
      assignee: "",
      currentActivityId: "",
      reviewOrgId: [],
      isReviewOrgId: {},
      nextId: "",
      isTaskClaim: false,
      isShowTaskClaimUser: true,
      textData: "待审核",
      tenantId: "",
    };
  },
  computed: {},
  //监听父组件传过来的值
  watch: {
    procInstId: {
      // immediate: true, // 这句重要  立即执行handler里面的方法
      handler (val) {
        this.procInstId = val;
        this.getprocessFn();
      },
    },
  },

  created () {
    // this.getprocessFn()
    // console.log(this.ids);
    // console.log(this.$forceUpdate);
  },

  methods: {
    //流程流转记录
    lczzjl () {
      console.log(123);
      this.$refs.lczzjl.visible = true;
      // this.$refs.lczzjl.id = this.procInstId
    },
    qwe (e) { },

    //关闭窗口
    close () {
      this.message = "";
      this.visible = false;
      this.hxrvalue = [];
      this.hxrList = [];
      // this.ids = []
      this.comment = "";
      this.getprocessFn();
      this.iSpinning = false
      this.visible = false;
    },

    //关闭
    isclose () {
      this.visible = false;
    },

    asd (val) { },
    //预备人选基本情况申报送审
    async getprocessFn () {
      if (this.procInstId != "") {
        let res = await getprocessApi(this.procInstId);
        console.log(res);
        if (res.data.code == "0000") {
          if (res.data.data != null) {
            var userName = localStorage.getItem("userName");
            var orgName = localStorage.getItem("orgName") || "";
            if (
              res.data.data.nextActivities[0].taskDefinitionKey ==
              "user_task_modify"
            ) {
              this.textData = "待处理";
            }
            if (
              res.data.data.nextActivities[0].taskDefinitionKey ==
              "end_event"
            ) {
              this.message = "";
            }
            this.tenantId = res.data.data.tenantId;
            this.processDefinition = res.data.data.processDefinition;
            this.currentActivity = res.data.data.currentActivity;
            this.nextActivities = res.data.data.nextActivities;
            this.prevActivity = res.data.data.prevActivity;
            this.taskId = res.data.data.taskId;
            this.nextActivityId = this.nextActivities[0];
            this.flowNo = this.nextActivities[0].flowNo;
            let nextId = this.nextActivities[0].taskDefinitionKey;
            let flowId = this.nextActivities[0].id;
            let params = {
              taskDefinitionKey: nextId,
              taskId: this.taskId,
              flowId: flowId,
            };
            getgetNextUsersApi(params).then((res) => {
              if (res.data.code == "0000") {
                this.isTaskClaim = false;
                this.hxrList = res.data.data.users;
                if (this.hxrList && this.hxrList.length == "1") {
                  this.hxrvalue.push(this.hxrList[0].userId);
                }
                if (
                  flowId.startsWith("RKW_XLGW") ||
                  flowId.startsWith("RKW_DISTRICT")
                ) {
                  this.hxrList.forEach((item) => {
                    this.hxrvalue.push(item.userId);
                  });
                  this.isTaskClaim = true;
                }
              } else {
                // this.$message.error(res.data.msg)
              }
            });

          }
        } else {
          this.$message.error(res.data.msg);
        }
      }

    },
    //常用人/机构人员
    callback (e) {
      this.keys = e;
      this.isReviewOrgId.fullName = [];
      this.isReviewOrgId.posName = [];
      // this.assignee.fullName = []
      // this.assignee.posName = []
      if (this.activeKey == "2") {
        for (var i = 0; i < this.lists.length; i++) {
          console.log(this.lists[i].assignee);
          this.lists[i].assignee.fullName = [];
          this.lists[i].assignee.posName = [];
        }
      }
      if (this.activeKey != "3") {
        //     for (let index = 0; index < this.hxrList.length; index++) {
        //         this.hxrList[index] = []
        //         this.hxrList[index] = []
        //     }
        this.hxrvalue = [];
      }
    },

    //树形控件
    onCheck (checkedKeys, info) {
      // console.log('onCheck', checkedKeys, info);
    },
    //单选框 //常用人和人员机构
    onChange (e) {
      this.iSpinning = true
      if (e.target.value.taskDefinitionKey == "user_task_modify") {
        this.textData = "待处理";
      } else {
        this.textData = "待审核";
      }
      var userName = localStorage.getItem("userName");
      var orgName = localStorage.getItem("orgName") || "";
      if (this.tenantId == "DBLZ" && userName && this.hxrvalue.length !== "0") {
        this.message = `您有一份${orgName} ${userName}发送的代表履职活动${this.textData}，请您登录代表履职系统处理。`;
      } else if (
        this.tenantId == "DBJSQ" &&
        userName &&
        this.hxrvalue.length !== "0"
      ) {
        this.message = `您有一份${orgName} ${userName}发送的代表进社区活动${this.textData}，请您登录代表进社区系统处理。`;
      }
      if (
        e.target.value.taskDefinitionKey ==
        "end_event"
      ) {
        this.message = "";
      }
      // console.log('radio checked', e.target.value);
      this.flowNo = e.target.value.flowNo;
      this.taskDefinitionKey = e.target.value.taskDefinitionKey;
      const params = {
        currentActivityId: this.currentActivity.id,
        nextActivityId: e.target.value.id,
        procDefKey: this.processDefinition.key,
      };

      //预备人选基本情况申报查询流程审核常用联系人
      // gethistoryAssigneesApi(params).then((res) => {
      //   if (res.data.code == "0000") {
      //     this.lists = res.data.data;

      //     console.log(this.lists, 23231);
      //     for (let i = 0; i < this.lists.length; i++) {
      //       // console.log(this.lists[i]);
      //       this.assignee = this.lists[i].assignee.userId;
      //     }
      //     // this.$message.success(res.data.msg);
      //   } else {
      //     // this.$message.error(res.data.msg);
      //   }
      // });
      this.nextActivities.forEach((el) => {
        // console.log(el);
        // this.flowNo = el.flowNo
        // console.log(this.flowNo);
        if (el.id == e.target.value) {
          // console.log(el.candidateGroups.join(","));
          // let rootIds = el.candidateGroups.join(",")
          if (el.candidateGroups != null) {
            let rootIds = el.candidateGroups.join(",");
            // console.log(el.candidateGroups);
            // let rootIds = el.candidateGroups
            //人员机构获取树
            // getfindApprovableODUApi({ rootIds: rootIds }).then((res) => {
            //   if (res.data.code == "0000") {
            //     this.treeData = res.data.data;
            //     console.log(this.treeData);

            //     for (let i = 0; i < this.treeData.length; i++) {
            //       console.log(this.treeData[i].children);
            //       for (
            //         let index = 0;
            //         index < this.treeData[i].children.length;
            //         index++
            //       ) {
            //         // console.log(this.treeData[i].children[i].children);
            //         this.treelist = this.treeData[i].children[i].children;
            //         console.log(this.treelist);
            //       }
            //     }
            //     //父节点不可选
            //     this.treeData.forEach((el) => {
            //       if (el.children != null) {
            //         el.disabled = true;
            //         el.children.forEach((e) => {
            //           if (e.children != null) {
            //             e.disabled = true;
            //           }
            //         });
            //       }
            //     });
            //   }
            // });
          }
        }
      });
      //获取候选人
      let flowId = e.target.value.id;
      let inparams = {
        taskDefinitionKey: this.taskDefinitionKey,
        taskId: this.taskId,
        flowId: flowId,
      };
      getgetNextUsersApi(inparams).then((res) => {
        if (res.data.code == "0000") {
          this.isTaskClaim = false;
          this.hxrList = res.data.data.users;
          if (this.hxrList && this.hxrList.length == "1") {
            this.hxrvalue = []
            this.hxrvalue.push(this.hxrList[0].userId)
          }
          if (
            flowId.startsWith("RKW_XLGW") ||
            flowId.startsWith("RKW_DISTRICT")
          ) {
            this.hxrvalue = [];
            this.hxrList.forEach((item) => {
              this.hxrvalue.push(item.userId);
            });
            this.isTaskClaim = true;
          }
          this.iSpinning = false
        } else {
          this.iSpinning = false
          // this.$message.error(res.data.msg)
        }
      });
      console.log(this.hxrvalue, this.hxrvalue.length);
    },

    //候选人
    hxrChange (e) {
      // this.message = "";
    },
    //复选改单选
    onSelect (selectedKeys, e) {
      this.message = "";
      if (selectedKeys.checked.length == 2) {
        this.reviewOrgId = [].concat(
          selectedKeys.checked[selectedKeys.checked.length - 1]
        );
      }
      console.log(this.reviewOrgId);
      e.checkedNodes.forEach((item) => {
        this.isReviewOrgId = item.data.props;
      });
    },
    isChange (e) {
      this.message = "";
      console.log(e);
    },
    //发送
    save () {
      let arr = [];
      arr.push(this.ids[0]);
      const params = {
        currentActivityId: this.currentActivity.id,
        procDefKey: this.processDefinition.key,
        flowNo: this.flowNo,
        nextActivityId: this.nextActivityId.taskDefinitionKey,
        sendSMS: this.isSendSMS == true ? 1 : 0,
        message: this.message,
        comment: this.comment,
        // assignee: this.assignee,
        assignee:
          this.hxrvalue instanceof Array
            ? this.hxrvalue.join(",")
            : this.hxrvalue,
        // reviewOrgId: this.isReviewOrgId.toString(),
        ids: arr,
      };
      this.$emit("complete", params);
    },
    successComplete () {
      // this.$message.success(res.data.msg)
      this.message = "";
      // this.ids = []
      this.visible = false;
      this.hxrvalue = [];
      this.hxrList = [];
      this.comment = "";
      this.getprocessFn();
    },
  },
};
</script>

<style lang="scss" scoped>
.liucheng {
  border-bottom: 1px solid #ccc;
}

.xxts {
  border: 1px solid #ccc;
  border-top: none;
}

.ssyj {
  border: 1px solid #ccc;
  border-top: none;
}

.cyren {
  border: 1px solid #ccc;
  border-top: none;
}

.cyrss {
  border-right: 1px solid #ccc;
}

.lcmcs {
  border: 1px solid #ccc;
  height: 30px;
  line-height: 30px;
}

.lcmcsss {
  border-right: 1px solid #ccc;
  height: 30px;
  line-height: 30px;
}

.dqjc {
  border: 1px solid #ccc;
  border-top: none;
  border-bottom: none;
  height: 30px;
  line-height: 30px;
  padding-left: 5px;
}

.xbcl {
  border: 1px solid #ccc;
  height: 30px;
  line-height: 30px;
  padding-left: 5px;
}

.ant-tabs-bar {
  margin: 0 !important;
}

.requireds::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  // font-size: 14px;
  @include add-size($font_size_16);
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}

.wxts {
  padding-top: 5%;
  margin-left: 0%;
  width: 100%;
  color: red;
}
</style>
