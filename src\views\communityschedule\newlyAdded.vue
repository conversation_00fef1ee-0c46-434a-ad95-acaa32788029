<template>
  <!-- 社情民意的详情、修改、添加 -->
  <div>
    <a-collapse-panel
      :header="headerTitle"
      style="
        margin: 0px 20px 40px 20px;
        padding: 10px 0px;
        background: rgba(190, 64, 61, 0.1);
        padding-bottom: 12px;
        border-bottom: 1px solid #cfcece;
      "
    />
    <div class="table-container">
      <div style="text-align: center;">
        <h1>代表联系群众登记表</h1>
      </div>
      <div>
        <a-radio-group v-model="addForm.bizType" style="width: 100%">
          <a-row>
            <a-col :span="4">
              <a-radio :disabled="disabled" :value="1">
                <span style="font-size: 16px;font-weight: 600;">需转办</span>
              </a-radio>
            </a-col>
            <a-col :span="4">
              <a-radio :disabled="disabled" :value="2">
                <span style="font-size: 16px;font-weight: 600;">
                  现场答复
                </span></a-radio>
            </a-col>
          </a-row>
        </a-radio-group>
      </div>
      <div>
        <a-form-model ref="addForm" :model="addForm" :rules="rules">
          <table style="width: 100%">
            <tr>
              <td class="title-label" colspan="1">
                <div class="bt title-field">人大代表</div>
              </td>
              <td class="body-wapper" colspan="1">
                <a-form-model-item class="form-item-wrapper">
                  <div class="searchStyle">
                    <a-input
                      v-model="dbName_list"
                      disabled
                      style="width: 300px"
                      enter-button
                    >
                    </a-input>
                    <a-button
                      type="primary"
                      icon="search"
                      @click="opendbInformation"
                    >
                    </a-button>
                  </div>
                </a-form-model-item>
              </td>
              <td class="title-label" colspan="1">
                <div class="title-field">联系电话</div>
              </td>
              <td style="width: 25%" colspan="1">
                <a-form-model-item class="form-item-wrapper">
                  <a-input
                    v-model="dbName_list_mobile"
                    disabled
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item>
              </td>
            </tr>
            <tr>
              <td class="title-label" colspan="1">
                <div class="bt title-field">人民群众</div>
              </td>
              <td style="width: 25%" colspan="1">
                <a-form-model-item prop="voterName" class="form-item-wrapper">
                  <a-input
                    v-model="addForm.voterName"
                    :disabled="disabled"
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item>
              </td>
              <td class="title-label" colspan="1">
                <div class="bt title-field">联系电话</div>
              </td>
              <td style="width: 25%" colspan="1">
                <a-form-model-item prop="voterMobile" class="form-item-wrapper">
                  <a-input
                    v-model="addForm.voterMobile"
                    :disabled="disabled"
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item>
              </td>
            </tr>
            <tr>
              <td class="title-label" colspan="1">
                <div class="title-field">记录人姓名</div>
              </td>
              <td style="width: 25%" colspan="1">
                <a-form-model-item class="form-item-wrapper">
                  <a-input
                    v-model="addForm.recorder"
                    :disabled="disabled"
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item>
              </td>
              <td class="title-label" colspan="1">
                <div class="title-field">联系电话</div>
              </td>
              <td style="width: 25%" colspan="1">
                <a-form-model-item class="form-item-wrapper">
                  <a-input
                    v-model="addForm.recorderMobile"
                    :disabled="disabled"
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item>
              </td>
            </tr>
            <tr>
              <!-- <td class="title-label" colspan="1">
                <div class="title-field">群众身份证号码</div>
              </td>
              <td style="width: 25%" colspan="1">
                <a-form-model-item class="form-item-wrapper">
                  <a-input
                    v-model="addForm.voterCredentialNo"
                    :disabled="disabled"
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item>
              </td> -->
              <td class="title-label" colspan="1">
                <div class="title-field">居住地址</div>
              </td>
              <td style="" colspan="3">
                <a-form-model-item class="form-item-wrapper">
                  <a-input
                    v-model="addForm.voterAddress"
                    :disabled="disabled"
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item>
              </td>
            </tr>
            <tr>
              <td v-if="addForm.bizType == 1" class="title-label" colspan="1">
                <div class="bt title-field">群众提出的意见和要求</div>
              </td>
              <td v-else class="title-label" colspan="1">
                <div class="bt title-field">群众提出的意见和要求</div>
              </td>
              <td style="width: 75%" colspan="3">
                <a-form-model-item class="form-item-wrapper" prop="content">
                  <a-textarea
                    v-model="addForm.content"
                    :disabled="disabled"
                    rows="7"
                  ></a-textarea>
                </a-form-model-item>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" rowspan="2" colspan="1">
                <div class="title-field">
                  代表意见
                </div>
              </td>
              <td colspan="3">
                <!-- <div
                  style="
                    float: left;
                    width: 30%;
                    line-height: 30px;
                    display: flex;
                    align-items: center;
                  "
                >
                  建议交办的办理部门：
                </div> -->
                <!-- <a-form-model-item style="float: right; width: 70%">
                  <a-input
                    v-model="addForm.adviceDept"
                    :disabled="disabled"
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item> -->
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td colspan="3">
                <a-form-model-item
                  style="padding-top: 10px"
                  class="form-item-wrapper"
                  prop="committeeOpinion"
                  :label-col="{ span: 1 }"
                  :wrapper-col="{ span: 24 }"
                >
                  <a-textarea
                    v-model="addForm.committeeOpinion"
                    :disabled="disabled"
                    rows="6"
                    allow-clear
                  ></a-textarea>

                  <div style="display: flex; justify-content: space-between">
                    <div></div>
                    <div style="padding-right: 10px">
                      <a-form-model-item label="" prop="signUpStart">
                        <a-date-picker
                          v-model="addForm.committeeOpinionDate"
                          value-format="YYYY-MM-DD"
                          placeholder="选择日期"
                        >
                        </a-date-picker>
                      </a-form-model-item>
                    </div>
                  </div>
                </a-form-model-item>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" colspan="1">
                <div class="title-field">
                  镇（街道）人大工作机构意见
                </div>
              </td>
              <td style="width: 75%" colspan="3">
                <a-form-model-item
                  prop="committeeStreetOpinion"
                  class="form-item-wrapper"
                  :label-col="{ span: 2 }"
                  :wrapper-col="{ span: 24 }"
                >
                  <a-textarea
                    v-model="addForm.committeeStreetOpinion"
                    style="padding-top: 10px"
                    :disabled="disabled"
                    rows="6"
                  ></a-textarea>
                  <div style="display: flex; justify-content: space-between">
                  <div></div>
                  <div style="padding-right: 10px">
                    <a-form-model-item label="" prop="signUpStart">
                      <a-date-picker
                        v-model="addForm.committeeStreetOpinionDate"
                        value-format="YYYY-MM-DD"
                        placeholder="选择日期"
                      >
                      </a-date-picker>
                    </a-form-model-item>
                  </div>
                </div>
                </a-form-model-item>
              </td>
            </tr>
            <!-- <tr v-show="addForm.bizType == 1">
              <td class="title-label" colspan="1">
                <div class="title-field">
                  镇党委、街道党工委意见
                </div>
              </td>
              <td style="width: 75%" colspan="3">
                <a-form-model-item
                  prop="partyStreetOpinion"
                  :label-col="{ span: 2 }"
                  :wrapper-col="{ span: 24 }"
                >
                  <a-textarea
                    v-model="addForm.partyStreetOpinion"
                    style="padding-top: 10px"
                    :disabled="disabled"
                    rows="2"
                    allow-clear
                  ></a-textarea>
                </a-form-model-item>
                <div style="display: flex; justify-content: space-between">
                  <div></div>
                  <div style="padding-right: 10px">
                    <a-form-model-item label="" prop="signUpStart">
                      <a-date-picker
                        v-model="addForm.partyStreetOpinionDate"
                        value-format="YYYY-MM-DD"
                        placeholder="选择日期"
                      >
                      </a-date-picker>
                    </a-form-model-item>
                  </div>
                </div>
              </td>
            </tr> -->
            <tr v-show="addForm.bizType == 1">
              <td
                colspan="8"
                style="text-align: center; font-weight: bold; padding: 20px"
              >
                <span>群众反映问题、意见建议的处理情况</span>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" colspan="1" rowspan="5">
                <div class="title-field">交办转办情况</div>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td colspan="3">
                <a-form-model-item
                  prop="committeeStreetOpinion"
                  class="form-item-wrapper"
                  :label-col="{ span: 2 }"
                  :wrapper-col="{ span: 24 }"
                >
                  <a-radio-group v-model="addForm.transType" style="width: 100%">
                    <a-row span="24">
                      <a-row>
                        <a-col :span="8">
                          <a-radio :disabled="disabled" :value="1"
                            >口头解释答复</a-radio
                          >
                        </a-col>
                        <a-col :span="8">
                          <a-radio :disabled="disabled" :value="2"
                            >直接交镇（街）有关单位处理</a-radio
                          >
                        </a-col>
                        <a-col :span="8">
                          <a-radio :disabled="disabled" :value="3"
                            >送区人大常委会转交有关机关、组织研究处理</a-radio
                          >
                        </a-col>
                      </a-row>
                      <a-row>
                        <a-col :span="8">
                          <a-radio :disabled="disabled" :value="4"
                            >增加形成代表建议、批评和意见</a-radio
                          >
                        </a-col>
                        <a-col :span="8">
                          <a-radio :disabled="disabled" :value="5"
                            >增加引导群众通过法定程序和途径处理</a-radio
                          >
                        </a-col>
                        <a-col :span="8">
                          <a-radio :disabled="disabled" :value="8">其他</a-radio>
                        </a-col>
                      </a-row>
                    </a-row>
                  </a-radio-group>
                </a-form-model-item>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td colspan="4" style="">
                <a-row>
                  <a-col :span="6" style="border-right: 1px solid #000;
                  ">
                    <div class="bt col-split" style="">交办时间 <a>办结必填项</a></div>
                  </a-col>
                  <a-col :span="6" style="border-right: 1px solid #000;">
                    <div class="col-split form-item-wrapper">
                      <a-date-picker
                      v-model="addForm.transTime"
                      style="width:100%"
                      :disabled="disabled"
                      placeholder="请选择反馈时间"
                      value-format="YYYY-MM-DD"
                    ></a-date-picker>
                    </div>
                  </a-col>
                  <a-col :span="6" style="border-right: 1px solid #000;">
                    <div class="bt col-split">接收单位 <a>办结必填项</a></div>
                  </a-col>
                  <a-col :span="6">
                    <div class="col-split form-item-wrapper">
                      <a-input
                        v-model="addForm.receiveDept"
                        :disabled="disabled"
                        autocomplete="off"
                        allow-clear
                      ></a-input>
                    </div>
                  </a-col>
                </a-row>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td colspan="4">
                <a-row>
                  <a-col :span="6" style="border-right: 1px solid #000;">
                    <div class="col-split">
                      交转方式
                    </div>
                  </a-col>
                  <a-col :span="6" style="border-right: 1px solid #000;">
                    <div class="col-split form-item-wrapper">
                      <a-input
                        v-model="addForm.transWay"
                        :disabled="disabled"
                        autocomplete="off"
                        allow-clear
                      ></a-input>
                    </div>
                  </a-col>
                  <a-col :span="6" style="border-right: 1px solid #000;">
                    <div class="col-split">
                      接收时间
                    </div>
                  </a-col>
                  <a-col :span="6">
                    <div class="col-split form-item-wrapper">
                      <a-date-picker
                        style="width:100%"
                        v-model="addForm.receiveTime"
                        allow-clear
                        :disabled="disabled"
                        placeholder="请选择反馈时间"
                        value-format="YYYY-MM-DD"
                      ></a-date-picker>
                    </div>
                  </a-col>
                </a-row>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td colspan="4">
                <a-row>
                  <a-col :span="6" style="border-right: 1px solid #000;">
                    <div class="col-split">
                      接收人员
                    </div>
                  </a-col>
                  <a-col :span="6" style="border-right: 1px solid #000;">
                    <div class="col-split form-item-wrapper">
                      <a-input
                      v-model="addForm.receiveUserName"
                      :disabled="disabled"
                      autocomplete="off"
                      allow-clear
                    ></a-input>
                    </div>
                  </a-col>
                  <a-col :span="6" style="border-right: 1px solid #000;">
                    <div class="col-split">
                      联系电话
                    </div>
                  </a-col>
                  <a-col :span="6">
                    <div class="col-split form-item-wrapper">
                      <a-input
                        v-model="addForm.receiveUserMobile"
                        :disabled="disabled"
                        autocomplete="off"
                        allow-clear
                      ></a-input>
                    </div>
                  </a-col>
                </a-row>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" colspan="1">
                <div class="title-field">办理期限</div>
              </td>
              <td style="width: 75%" colspan="3">
                <div class="form-item-wrapper">
                该事项建议于
                <a-date-picker
                  v-model="addForm.dueDate"
                  allow-clear
                  :disabled="disabled"
                  value-format="YYYY-MM-DD"
                ></a-date-picker>
                前办结，办理结果由承办单位答复镇人大（人大街道工委），并由镇人大（人大街道工委）通报联络站反馈代表和群众。
                </div>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" colspan="1">
                <div class="title-field">跟踪督办情况</div>
              </td>
              <td style="width: 75%" colspan="3">
                <a-form-model-item label="" prop="situation" class="form-item-wrapper">
                  <a-textarea
                    v-model="addForm.situation"
                    rows="3"
                    :disabled="disabled"
                  ></a-textarea>
                </a-form-model-item>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" colspan="1">
                <div class="bt title-field">
                  办理详情
                </div>
              </td>
              <td style="width: 75%" colspan="3">
                <a-form-model-item
                  label=""
                  prop="handlingContent"
                  style="padding: 10px"
                >
                  <a-textarea
                    v-model="addForm.handlingContent"
                    rows="3"
                    :disabled="disabled"
                  ></a-textarea>
                  <a>办结必填项</a>
                </a-form-model-item>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" rowspan="2" colspan="1">
                <div class="title-field">答复反馈情况</div>
              </td>
              <td colspan="3">
                <a-row class="form-item-wrapper">
                  <a-col span="5">
                    <a-form-model-item label="" prop="replyStreetPartyDate">
                      <a-date-picker
                        v-model="addForm.replyStreetPartyDate"
                        allow-clear
                        :disabled="disabled"
                        placeholder="请选择答复时间"
                        value-format="YYYY-MM-DD"
                      >
                      </a-date-picker>
                    </a-form-model-item>
                  </a-col>
                  <a-col span="5" style="margin-top: 5px">
                    <a-form-model-item
                      prop="replyStreetParty"
                      :label-col="{ span: 1 }"
                      :wrapper-col="{ span: 24 }"
                    >
                      <a-textarea
                        v-model="addForm.replyStreetParty"
                        rows="1"
                        allow-clear
                        :disabled="disabled"
                      ></a-textarea>
                    </a-form-model-item>
                  </a-col>
                  <p style="line-height: 40px">
                    已将该事项办理情况答复镇人大（人大街道工委）。
                  </p>
                </a-row>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td colspan="3">
                <a-row class="form-item-wrapper">
                  <a-col span="5">
                    <a-form-model-item label="" prop="replyPeopleDate">
                      <a-date-picker
                        v-model="addForm.replyPeopleDate"
                        allow-clear
                        :disabled="disabled"
                        placeholder="请选择反馈时间"
                        value-format="YYYY-MM-DD"
                      >
                      </a-date-picker>
                    </a-form-model-item>
                  </a-col>
                  <a-col span="5" style="margin-top: 5px">
                    <a-form-model-item
                      prop="replyPeople"
                      :label-col="{ span: 1 }"
                      :wrapper-col="{ span: 24 }"
                    >
                      <a-textarea
                        v-model="addForm.replyPeople"
                        rows="1"
                        allow-clear
                        :disabled="disabled"
                      ></a-textarea>
                    </a-form-model-item>
                  </a-col>
                  <p style="line-height: 40px">
                    已将该事项办理情况反馈代表和群众。
                  </p>
                </a-row>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" rowspan="2" colspan="1">
                <div class="title-field">代表评价</div>
              </td>
              <td colspan="3">
                <div class="form-item-wrapper">
                  <a-row style="display: flex;align-items: center;">
                    <a-col :span="3">
                      <div style="text-align: center;">
                        <div class="bt">对办理结果:</div>
                        <div><a>办结必填项</a></div>
                      </div>
                    </a-col>
                    <a-col :span="3">
                      <a-radio-group
                        v-model="addForm.handlingInfo.committeeGradeForResult"
                        style="width: 100%"
                      >
                            <a-radio :disabled="disabled" :value="1">满意</a-radio>
                            <a-radio :disabled="disabled" :value="2" style="margin-top: 5px"
                              >基本满意</a-radio
                            >
                            <a-radio :disabled="disabled" :value="3" style="margin-top: 5px"
                              >不满意</a-radio
                            >
                      </a-radio-group>
                    </a-col>
                    <a-col :span="18">
                      <a-textarea
                        v-model="addForm.handlingInfo.committeeCommentForResult"
                        rows="3"
                        allow-clear
                        :disabled="disabled"
                      ></a-textarea>
                    </a-col>
                  </a-row>
                </div>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td colspan="3">
                <div class="form-item-wrapper">
                  <a-row style="display: flex;align-items: center;">
                    <a-col :span="3">
                      <div style="text-align: center;">
                        <div class="bt">对办理部门:</div>
                        <div><a>办结必填项</a></div>
                      </div>
                    </a-col>
                    <a-col :span="3">
                      <a-radio-group
                        v-model="addForm.handlingInfo.committeeGradeForDept"
                        style="width: 100%"
                      >
                            <a-radio :disabled="disabled" :value="1">满意</a-radio>
                            <a-radio :disabled="disabled" :value="2" style="margin-top: 5px"
                              >基本满意</a-radio
                            >
                            <a-radio :disabled="disabled" :value="3" style="margin-top: 5px"
                              >不满意</a-radio
                            >
                      </a-radio-group>
                    </a-col>
                    <a-col :span="18">
                      <a-textarea
                        v-model="addForm.handlingInfo.committeeCommentForDept"
                        rows="3"
                        :disabled="disabled"
                      ></a-textarea>
                    </a-col>
                  </a-row>
                </div>
              </td>
            </tr>
            <tr>
              <td class="title-label" rowspan="2" colspan="1">
                <div class="title-field">群众评价</div>
              </td>
              <td colspan="3">
                <div class="form-item-wrapper">
                  <a-row style="display: flex;align-items: center;">
                    <a-col :span="3">
                      <div style="text-align: center;">
                        <div class="bt">对办理结果:</div>
                        <div><a>办结必填项</a></div>
                      </div>
                    </a-col>
                    <a-col :span="3">
                      <a-radio-group
                        v-model="addForm.handlingInfo.peopleGradeForResult"
                        style="width: 100%"
                      >
                            <a-radio :disabled="disabled" :value="1">满意</a-radio>
                            <a-radio :disabled="disabled" :value="2" style="margin-top: 5px"
                              >基本满意</a-radio
                            >
                            <a-radio :disabled="disabled" :value="3" style="margin-top: 5px"
                              >不满意</a-radio
                            >
                      </a-radio-group>
                    </a-col>
                    <a-col :span="18">
                      <a-textarea
                        v-model="addForm.handlingInfo.peopleCommentForResult"
                        rows="3"
                        :disabled="disabled"
                      ></a-textarea>
                    </a-col>
                  </a-row>
                </div>
              </td>
            </tr>
            <tr v-if="addForm.bizType == 1">
              <td colspan="3">
                <div class="form-item-wrapper">
                  <a-row style="display: flex;align-items: center;">
                    <a-col :span="3">
                      <div style="text-align: center;">
                        <div class="bt">对办理部门:</div>
                        <div><a>办结必填项</a></div>
                      </div>
                    </a-col>
                    <a-col :span="3">
                      <a-radio-group
                        v-model="addForm.handlingInfo.peopleGradeForDept"
                        style="width: 100%"
                      >
                            <a-radio :disabled="disabled" :value="1">满意</a-radio>
                            <a-radio :disabled="disabled" :value="2" style="margin-top: 5px"
                              >基本满意</a-radio
                            >
                            <a-radio :disabled="disabled" :value="3" style="margin-top: 5px"
                              >不满意</a-radio
                            >
                      </a-radio-group>
                    </a-col>
                    <a-col :span="18">
                      <a-textarea
                        v-model="addForm.handlingInfo.peopleCommentForDept"
                        rows="3"
                        :disabled="disabled"
                      ></a-textarea>
                    </a-col>
                  </a-row>
                </div>
              </td>
            </tr>
            <tr v-else style="border: 1px solid transparent">
              <td colspan="3" style="border: 1px solid transparent"></td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" colspan="1">
                <div class="title-field">
                  再次办理或解释情况
                </div>
              </td>
              <td style="width: 75%" colspan="3">
                <div class="form-item-wrapper">
                    <a-textarea
                      v-model="addForm.secondHandlingContent"
                      rows="3"
                      allow-clear
                      :disabled="disabled"
                    ></a-textarea>
                </div>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" colspan="1">
                <div class="title-field">代表再次评价</div>
              </td>
              <td style="width: 75%" colspan="3">
                <div class="form-item-wrapper">
                  <a-row style="display: flex;align-items: center;">
                    <a-col :span="3">
                      <a-radio-group
                        v-model="addForm.committeeOnceMoreGrade"
                        style="width: 100%"
                      >
                            <a-radio :disabled="disabled" :value="1">满意</a-radio>
                            <a-radio :disabled="disabled" :value="2" style="margin-top: 5px"
                              >基本满意</a-radio
                            >
                            <a-radio :disabled="disabled" :value="3" style="margin-top: 5px"
                              >不满意</a-radio
                            >
                      </a-radio-group>
                    </a-col>
                    <a-col :span="21">
                      <a-textarea
                        v-model="addForm.committeeOnceMoreComment"
                        rows="3"
                        :disabled="disabled"
                      ></a-textarea>
                    </a-col>
                  </a-row>
                </div>
              </td>
            </tr>
            <tr v-show="addForm.bizType == 1">
              <td class="title-label" colspan="1">
                <div class="title-field">群众再次评价</div>
              </td>
              <td style="width: 75%" colspan="3">
                <div class="form-item-wrapper">
                  <a-row style="display: flex;align-items: center;">
                    <a-col :span="3">
                      <a-radio-group
                        v-model="addForm.peopleOnceMoreGrade"
                        style="width: 100%"
                      >
                            <a-radio :disabled="disabled" :value="1">满意</a-radio>
                            <a-radio :disabled="disabled" :value="2" style="margin-top: 5px"
                              >基本满意</a-radio
                            >
                            <a-radio :disabled="disabled" :value="3" style="margin-top: 5px"
                              >不满意</a-radio
                            >
                      </a-radio-group>
                    </a-col>
                    <a-col :span="21">
                      <a-textarea
                        v-model="addForm.peopleOnceMoreComment"
                        rows="3"
                        :disabled="disabled"
                      ></a-textarea>
                    </a-col>
                  </a-row>
                </div>
              </td>
            </tr>
            <tr>
              <td class="title-label" rowspan="2" colspan="1">
                <div class="title-field">办结情况</div>
              </td>
              <td class="title-label" colspan="3">
                <a-row>
                  <a-radio-group
                    v-model="addForm.isCompleted"
                    style="width: 100%"
                  >
                  <a-col :span="12" style="border-right: 1px solid #000;">
                    <div style="height: 50px;display: flex;align-items: center;justify-content: center;">
                      <a-radio :disabled="disabled" :value="1" class="bt">办结（办结时间）</a-radio>
                      <div><a>办结必填项</a></div>
                    </div>
                  </a-col>
                  <a-col :span="12">
                    <div style="height: 50px;display: flex;align-items: center;justify-content: center;">
                      <a-radio :disabled="disabled" :value="0">未办结（原因或后续处理计划等）</a-radio>
                    </div>
                  </a-col>
                  </a-radio-group>
                </a-row>
              </td>
            </tr>
            <tr>
              <td class="title-label" colspan="3">
                <a-row>
                  <a-col :span="12" style="border-right: 1px solid #000;">
                    <div class="" style="height: 100px;display: flex;align-items: center;justify-content: center;">
                      <a-date-picker
                            v-model="addForm.completedTime"
                            value-format="YYYY-MM-DD"
                            placeholder="选择日期"
                          >
                      </a-date-picker>
                      <div style="margin-left: 5px"><a>办结必填项</a></div>
                    </div>
                  </a-col>
                  <a-col :span="12">
                    <a-form-model-item label="" prop="situation" class="form-item-wrapper">
                      <a-textarea
                        v-model="addForm.completeContent"
                        rows="3"
                        :disabled="disabled"
                      ></a-textarea>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </td>
            </tr>
            <!-- <tr colspan="1"  rowspan="1">
              大叔大婶
            </tr> -->
              <!-- <td style="width: 75%" colspan="3">
                办结情况 
              </td> -->
              <!-- <tr> -->

              <!-- <td style="width: 75%" colspan="3">
                <div style="display: flex">
                  <a-radio-group
                    v-model="addForm.isCompleted"
                    style="width: 100%"
                  >
                    <a-row>
                      <a-col :span="6">
                        <a-radio :disabled="disabled" :value="0"
                          >未办结(原因或后续处理计划等)</a-radio
                        >
                      </a-col>
                      <a-col :span="6" offset="3">
                        <a-input
                          v-model="addForm.completeContent"
                          :disabled="addForm.isCompleted == '1'"
                          autocomplete="off"
                          allow-clear
                        ></a-input>
                      </a-col>
                    </a-row>
                  </a-radio-group>
                </div>
              </td> -->
          </table>
          <a-row :style="{ textAlign: 'center', marginTop: '20px' }">
            <!-- 已办结 -->
            <a-button
              type="primary"
              style="margin-right: 10px"
              :disabled="isCompletedType"
              @click="tableSaveYJB"
              >办结</a-button
            >
            <a-button
              type="primary"
              @click="tableSave"
              >保存</a-button
            >
          </a-row>
        </a-form-model>
      </div>
      <!-- 代表弹出框 -->
      <a-modal
        title="选择代表"
        :visible.sync="DialogVisible"
        width="50%"
        @cancel="DialogVisible = false"
      >
        <div>
          <a-row>
            <standard-table
              :columns="columns"
              :row-key="
                (record, index) => {
                  return record.userId;
                }
              "
              :loading="TBloading"
              :data-source="dataSource"
              :pagination="pagination"
              :selected-rows.sync="selectedRows"
              @selectedRowChange="onSelectChange"
            ></standard-table>
          </a-row>
        </div>
        <span slot="footer" class="dialog-footer">
          <a-button type="primary" @click="confirm">确 定</a-button>
          <a-button @click="DialogVisible = false">取 消</a-button>
        </span>
      </a-modal>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { myPagination } from "@/mixins/pagination.js";
import submitForCensorship from "@/views/common/submitForCensorship";
import {
  newPublicOpinionControllerSave,
  getByCommunityScheduleId,
  newPublicOpinionControllerUpdate,
} from "@/api/representativeElection/candidateApi.js";
import {
  OpinionControllerSave,
  OpinionControllerUpdata,
  getMembers,
} from "@/api/communityschedule";
import { log } from "@antv/g2plot/lib/utils";
export default {
  components: { submitForCensorship },
  mixins: [myPagination],
  props: {
    neWid: {
      type: String,
    },
  },

  data() {
    // 根据证件号校验生日
    var validateDateOfBirth = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请选择出生日期"));
      } else {
        if (this.addForm.birthday) {
          let year = this.addForm.birthday.slice(6, 10);
          let month = this.addForm.birthday.slice(10, 12);
          let day = this.addForm.birthday.slice(12, 14);
          let temp_date = new Date(
            year,
            parseFloat(month) - 1,
            parseFloat(day)
          );
          if (temp_date) {
            callback();
          } else {
            callback(new Error("请选择正确的出生日期"));
          }
        } else {
          this.$message.error("出生日不能大于当天");
        }
      }
    };

    //校验手机号
    var validatePhone = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入手机号"));
      } else {
        let reg = /^([1][3,4,5,6,7,8,9])\d{9}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的手机号"));
      }
    };
    //校验证件号
    var getCardTypeNumber = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入身份证号码"));
      } else {
        const reg =
          /^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确格式的身份证号码"));
      }
    };
    return {
      headerTitle: "",
      TBloading: false,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        {
          title: "代表姓名",
          align: "center",
          ellipsis: true,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联系方式",
          align: "center",
          ellipsis: true,
          dataIndex: "mobile",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
      ],
      selectedRows: [],
      dataSource: [],
      dbName_list: "", //代表姓名
      dbName_list_mobile: "", //代表姓名
      DialogVisible: false,
      FormType: "",
      isCompletedType: false,
      disabled: false,
      state: "",
      checked: true,
      to_type: null,
      addForm: {
        adviceDept: null,
        committeeOpinion: null,
        committeeOpinionDate: null,
        committeeStreetOpinion: null,
        committeeStreetOpinionDate: null,
        communityScheduleId: null,
        completeContent: null,
        content: null,
        createTime: null,
        creator: null,
        deleteTime: null,
        deleter: null,
        dueDate: null,
        handlingContent: null,
        modifier: null,
        partyStreetOpinion: null,
        partyStreetOpinionDate: null,
        receiveDept: null,
        receiveTime: null,
        receiveUserMobile: null,
        receiveUserName: null,
        recordTime: null,
        recorder: null,
        recorderMobile: null,
        replyPeople: null,
        replyPeopleDate: null,
        replyStreetParty: null,
        replyStreetPartyDate: null,
        secondHandlingContent: null,
        isCompleted: null,
        situation: null,
        transTime: null,
        transWay: null,
        updateTime: null,
        voterAddress: null,
        voterAge: null,
        voterCredentialNo: null,
        voterGender: null,
        voterMobile: null,
        voterName: null,
        members: [],
        //
        handlingInfo: {
          committeeCommentForDept: null,
          committeeGradeForDept: null,
          committeeCommentForResult: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        },
        transType: null,
        committeeOnceMoreGrade: null,
        peopleOnceMoreGrade: null,
        bizType: 1,
      },
      bljgclList: [
        { id: 1, bljgname: "满意" },
        { id: 2, bljgname: "基本满意" },
        { id: 3, bljgname: "不满意(原因)" },
      ],
      bjqkList: [
        { id: 1, bjqkname: "已办结" },
        { id: 0, bjqkname: "未办结" },
      ],
      checkBoxArr: [
        { id: "1", value: "是否拥有外国国籍" },
        { id: "2", value: "是否拥有国(境)外永久居留权" },
        { id: "3", value: "是否拥有国(境)外长期居留许可" },
        { id: "4", value: "是否有直系亲属担任同级人大代表" },
        { id: "5", value: "配偶子女是否移居国（境）外" },
        { id: "6", value: "是否担任无隶属关系行政区域的人大代表/政协委员" },
        { id: "7", value: "是否是香港或澳门市民" },
        { id: "8", value: "是否担任企业高管" },
        {
          id: "9",
          value:
            "是否直接或者间接接受境外机构、组织、个人提供的与选举有关的任何形式的资助",
        },
      ],

      periods: [
        { id: 16, name: "十五届六次" },
        { id: 15, name: "十五届一次" },
        { id: 1, name: "十五届二次" },
        { id: 2, name: "十五届三次" },
        { id: 3, name: "十五届四次" },
        { id: 4, name: "十五届五次" },
      ],
      rules: {
        content: [
          { required: true, message: "请输入反映情况", trigger: "blur" },
        ],
        dbName_list: [
          { required: true, message: "请选择代表姓名", trigger: "blur" },
        ],
        recorder: [
          { required: true, message: "请输入记录人姓名", trigger: "blur" },
        ],
        voterName: [
          {
            required: true,
            message: "请输入所联系的群众姓名",
            trigger: "blur",
          },
        ],
        committeeMobile: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],
        recorderMobile: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],
        voterMobile: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],
        receiveUserMobile: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],

        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
        voterAddress: [
          { required: true, message: "请填写住址", trigger: "blur" },
        ],

        birthday: [
          { required: true, trigger: "change", validator: validateDateOfBirth },
        ],
        voterCredentialNo: [
          { required: true, trigger: "blur", validator: getCardTypeNumber },
        ],
      },
      disabled: false,
    };
  },

  created() {
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "社情民意列表详情");
    let { data, to_type, communityScheduleId } = this.$route.query;
    this.to_type = to_type;
    this.addForm.communityScheduleId = communityScheduleId;
    this.queryForm.id = communityScheduleId;
    if (to_type) {
      this.headerTitle = "修改社情民意";
      // 修改
      this.disabled = false;
      // this.getEdit(this.queryForm.id);
    } else {
      // 详情
      this.headerTitle = "社情民意详情";
      this.disabled = true;
    }
    if (data) {
      this.queryForm.id = data.communityScheduleId;
      this.addForm = data;
      this.FormType = "update";
      let name_list = [];
      let mobile_list = [];
      this.dbName_list = "";
      this.dbName_list_mobile = "";
      this.addForm.members.forEach((item, index) => {
        name_list.push(item.userName);
      });
      this.addForm.members.forEach((item, index) => {
        mobile_list.push(item.mobile);
      });

      this.dbName_list = name_list.toString();
      this.dbName_list_mobile = mobile_list.toString();
      // this.addForm.transType = data.transType ? data.transType.toString() : "";

      if (data.handlingInfo) {
        this.addForm.handlingInfo = data.handlingInfo;
      } else {
        this.addForm.handlingInfo = {
          committeeCommentForDept: null,
          committeeGradeForDept: null,
          committeeCommentForResult: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        };
      }
      if (data.handlingInfo) {
        this.addForm.handlingInfo = data.handlingInfo;
      } else {
        this.addForm.handlingInfo = {
          committeeCommentForDept: null,
          committeeCommentForResult: null,
          committeeGradeForDept: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        };
      }
      if (data.isCompleted == 1) {
        this.isCompletedType = true;
      }
      //; 查看详情
    } else {
      this.FormType = "add";
      this.disabled = false;
    }
  },

  methods: {
    // 编辑获取数据
    getEdit(id) {
      getByCommunityScheduleId(id).then((res) => {
        if (res.data.code == "0000") {
          this.addForm = Object.assign(this.addForm, res.data.data);
        }
      });
    },
    confirm() {
      this.dbName_list = "";
      this.addForm.members = [];
      this.addForm.members = this.selectedRows;
      let name_list = [];
      this.selectedRows.forEach((item, index) => {
        name_list.push(item.userName);
        this.addForm.members[index].userName = item.userName;
      });
      this.dbName_list = name_list.toString();
      this.dbName_list_mobile = this.selectedRows
        .map((item) => item.mobile)
        .toString();
      this.DialogVisible = false;
    },
    // 代表列表名单
    getdbList() {
      getMembers(this.queryForm).then((res) => {
        if (res.code == "200") {
          this.dataSource = res.rows;
        }
      });
    },
    onSelectChange() {},
    opendbInformation() {
      this.getdbList();
      this.DialogVisible = true;
    },
    //
    changerCompletedType() {
      console.log(this.addForm.isCompleted);
      if (this.addForm.isCompleted == 1) {
        this.isCompletedType = true;
      } else {
        this.isCompletedType = false;
      }
    },
    //不能选择今天以后的日期
    disabledDate(current) {
      return current && current > moment().endOf("day");
    },

    change(item, e) {
      console.log(item);
      if (item.target.checked) {
        this.addForm[item.target.value] = true;
      } else {
        this.addForm[item.target.value] = false;
      }
    },

    // 修改
    onUpdate() {
      this.$refs["addForm"].validate(async (valid) => {
        if (valid) {
          let data = this.addForm;
          // data.transType = Number(data.transType);
          OpinionControllerUpdata(data).then((res) => {
            if (res.code == "0000") {
              this.$message.success("修改成功");
              this.$router.go(-1);
            }
          });
        } else {
          this.$message.error("校验失败");
        }
      });
    },
    // 已结办
    tableSaveYJB() {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确认办结？办结后将不能修改表单信息。",
        onOk: () => {
          this.addForm.isCompleted = "1";
          // 判断是否为需转办 ,现场答复
          if (this.addForm.bizType == "1") {
            //需转办
            if (
              this.addForm.transTime == null ||
              this.addForm.transTime == "" ||
              this.addForm.receiveDept == null ||
              this.addForm.receiveDept == "" ||
              this.addForm.handlingContent == null ||
              this.addForm.handlingContent == "" ||
              this.addForm.handlingInfo.committeeGradeForResult == null ||
              this.addForm.handlingInfo.committeeGradeForResult == "" ||
              this.addForm.handlingInfo.committeeGradeForDept == null ||
              this.addForm.handlingInfo.committeeGradeForDept == "" ||
              this.addForm.handlingInfo.peopleGradeForResult == null ||
              this.addForm.handlingInfo.peopleGradeForResult == "" ||
              this.addForm.handlingInfo.peopleGradeForDept == null ||
              this.addForm.handlingInfo.peopleGradeForDept == "" ||
              this.addForm.completedTime == null
            ) {
              this.$message.error("请填写办结必填项");
            } else if (
              this.addForm.handlingInfo.peopleGradeForResult == "3" &&
              this.addForm.handlingInfo.peopleCommentForResult == null
            ) {
              this.$message.error("请填写不满意原因");
            } else if (
              this.addForm.handlingInfo.committeeGradeForResult == "3" &&
              this.addForm.handlingInfo.committeeCommentForResult == null
            ) {
              this.$message.error("请填写不满意原因");
            } else if (
              this.addForm.handlingInfo.committeeGradeForDept == "3" &&
              this.addForm.handlingInfo.committeeCommentForDept == null
            ) {
              this.$message.error("请填写不满意原因");
            } else if (
              this.addForm.handlingInfo.peopleGradeForDept == "3" &&
              this.addForm.handlingInfo.peopleCommentForDept == null
            ) {
              this.$message.error("请填写不满意原因");
            } else {
              this.tableSave();
            }
          } else {
            //现场答复
            if (this.addForm.handlingInfo.peopleGradeForResult == null) {
              this.$message.error("请填写办结必填项");
            } else if (
              this.addForm.handlingInfo.peopleGradeForResult == "3" &&
              this.addForm.handlingInfo.peopleCommentForResult == null
            ) {
              this.$message.error("请填写不满意原因");
            } else {
              this.tableSave();
            }
          }
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    //保存
    tableSave() {
      if (this.to_type) {
        this.$refs["addForm"].validate(async (valid) => {
          if (valid) {
            if (this.dbName_list) {
              let data = this.addForm;
              // data.transType = Number(data.transType);
              newPublicOpinionControllerUpdate(data).then((res) => {
                if (res.data.code == "0000") {
                  this.$message.success("修改成功");
                  this.$router.go(-1);
                }
              });
            } else {
              this.$message.error("请选择代表");
            }
          } else {
            this.$message.error("校验失败");
          }
        });
      } else {
        this.$refs["addForm"].validate(async (valid) => {
          if (valid) {
            if (this.dbName_list) {
              let data = this.addForm;
              // data.transType = Number(data.transType);
              newPublicOpinionControllerSave(data).then((res) => {
                if (res.data.code == "0000") {
                  this.$message.success("保存成功");
                  this.$router.go(-1);
                }
              });
            } else {
              this.$message.error("请选择代表");
            }
          } else {
            this.$message.error("校验失败");
          }
        });
      }
    },
    // 复选框
    onChanger(even) {
      console.log(even);
      console.log(this.addForm.transType);
    },
    // 关闭
    close(e) {
      this.$router.go(-1);
    },
    quxiao() {
      this.$router.go(-1);
    },
    //监听父组件传过来的值
    // watch: {
    //   neWid: {
    //     immediate: true,    // 这句重要  立即执行handler里面的方法
    //     handler(val) {
    //     },
    //   },
    // },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-input-affix-wrapper {
  line-height: null !important;
}
.tit {
  margin-bottom: 15px;
  color: #000;
  //  font-size: 16px;
  @include add-size($font_size_16);
}
.formBox {
  padding: 20px;
}

.shangchuang {
  margin-left: 140px;
}

.reds {
  color: red;
  margin: 10px;
}
.jic {
  position: relative;
}
.jicson {
  color: red;
  // font-size: 2px;
  @include add-size($font_size_16);
  position: absolute;
  left: -65px;
  z-index: 999;
}
.table-container {
  width: 80%;
  margin: 0 auto;
}
.table-container table td {
  border: 1px solid #000;
}

.label {
  // margin-top: 6px;
}

.title-label {
  width: 25%;
  text-align: center;
  height: 50px;
}

.body-wapper {
  width: 25%;
  text-align: center;
  height: 50px;
  margin: 5px;
}

.ant-form-item {
  margin-bottom: 0px !important;
}

.form-item-wrapper {
  padding: 10px;
}
.bt::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}

.title-field {
  font-size: 16px;
  font-weight: 600;
}

.col-split {
  display: flex;
  align-items: center; 
  justify-content: center;
  height: 40px;
}
</style>
