import request from "@/utils/request";
import qs from "qs";

export function getPage(data) {
  return request({
    url: "/api/v1/display/supervise/queryPage",
    method: "post",
    data,
  });
}

export function getPageBySource(data) {
  return request({
    url: "/api/v1/display/supervise/queryPageBySource",
    method: "post",
    data,
  });
}

export function getPageByTarget(data) {
  return request({
    url: "/api/v1/display/supervise/queryPageByTarget",
    method: "post",
    data,
  });
}
