<template>
  <div class="table-container">
    <a-modal title="选举结果查询列表"
             :visible.sync="visible"
             width="70%"
             @cancel="close">
      <a-row class="formBox">
        <a-form-model ref="queryForm"
                      :model="queryForm"
                      layout="inline">
          <a-col span="6">
            <a-form-model-item label="届次">
              <a-select v-model="queryForm.jcDm"
                        allow-clear
                        style="width: 200px;"
                        placeholder="请选择届次">
                <a-select-option v-for="item in jcDmList"
                                 :key="item.jcDm"
                                 :value="item.jcDm">{{
                                        item.levelName
                                }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="补选单位"
                               prop="xzqhDm">
              <a-select v-model="queryForm.xzqhDm"
                        allow-clear
                        style="width: 200px"
                        placeholder="请选择补选单位">
                <!-- placeholder="please select your zone" -->
                <a-select-option v-for="item in xzqhDmList"
                                 :key="item.xzqhDm"
                                 :value="item.xzqhDm">{{
                                        item.xzqhmc
                                }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="姓名"
                               prop="userName">
              <a-input v-model="queryForm.userName"
                       autocomplete="off"
                       allow-clear
                       placeholder="请输入姓名"
                       v-on:keyup.enter="search"></a-input>
            </a-form-model-item>
          </a-col>

          <a-col span="4">
            <a-button type="primary"
                      @click="search">搜索</a-button>
            <a-button style="margin-left: 12px;"
                      @click="reset"
                      class="pinkBoutton">重置</a-button>
          </a-col>
        </a-form-model>
      </a-row>
      <a-row>
        <a-table class="directorySet-table"
                 ref="table"
                 :columns="columns"
                 :pagination="pagination"
                 :row-selection="rowSelection"
                 :data-source="dataSource"
                 :scroll="{ x: 300, y: 0 }"></a-table>
      </a-row>
      <div slot="footer"
           class="dialog-footer">
        <a-button @click="closeAdd">取 消</a-button>
        <a-button type="primary"
                  @click="xuanze">选 择</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { getAddeDselecselectApi, getAdministrativeStateListApi, getLevelListApi, getFormStateListApi, getfindByConditionPageableApi } from "@/api/representativeElection/candidateApi.js";
import additionSignIn from "@/views/ballot/additionSignIn"
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
export default {
  name: "RepresentativeForm",
  // 候选人申报表
  components: { StandardTable, additionSignIn },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      visible: false,
      //获取届次
      jcDmList: [],
      //表单状态
      statesList: [],
      //补选单位
      xzqhDmList: [],
      //状态
      status: [
        { id: 1, name: "草稿" },
        { id: 2, name: "退回" },
        { id: 3, name: "待初审" },
        { id: 4, name: "待初审" },
        { id: 5, name: "待初审" },
        { id: 6, name: "待终审" },
        { id: 7, name: "已终审" },
        { id: 8, name: "已归档" },
      ],
      //单位
      be_primary: [
        { id: 0, name: "全部" },
        { id: 1, name: "解放军" },
        { id: 2, name: "越秀区" },
        { id: 3, name: "海珠区" },
        { id: 4, name: "荔湾区" },
        { id: 5, name: "天河区" },
        { id: 6, name: "白云区" },
        { id: 7, name: "黄埔区" },
        { id: 8, name: "花都区" },
        { id: 9, name: "番禺区" },
        { id: 10, name: "南沙区" },
        { id: 11, name: "从化区" },
        { id: 12, name: "增城区" },
      ],
      queryForm: {
        jcDm: '3',
        xzqhDm: undefined,
        userName: "",
        ztbz: '6',
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        {
          title: "姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "USER_NAME",
        },
        {
          title: "性别",
          align: "center",
          width: 90,
          ellipsis: true,
          dataIndex: "SEX",
        },
        {
          title: "出生日期",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "BIRTHDAY",
        },
        {
          title: "民族",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "MZMC",
        },
        {
          title: "工作单位",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "WORK_UNIT",
        },
        {
          title: "职务",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "DUTY",
        },
        {
          title: "职业构成",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "ZYGCMC",
        },
        {
          title: "推荐单位",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "MELYMC",
        },

      ],
      dataSource: [],
      dataSourceList: [],
      selectedRows: [],
      isselectedRows: [],
    };
  },
  created () {
    // this.$store.dispatch("navigation/breadcrumb1", "候选人管理");
    // this.$store.dispatch("navigation/breadcrumb2", "补选结果");
    this.fetchData();
    this.getAdministrativeStateList()
    // this.getFormStateListFn()
    this.getLevelListFn()
  },
  computed: {

    //复选框
    rowSelection () {
      return {
        onChange: (selectedRowKeys, selectedRows) => {
          console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
          if (selectedRows.length > 1) {
            return this.$baseMessage("只能选择一条数据", "error");
          } else {
            this.dataSourceList = selectedRows
          }
        },
        getCheckboxProps: record => ({
          props: {
            disabled: record.name === 'Disabled User', //  
            name: record.name,
          },
        }),
      };
    },

  },
  methods: {
    // 搜索
    search () {
      this.fetchData()
    },
    //选择
    xuanze () {
      if (this.dataSourceList.length == 1) {
        console.log(this.dataSourceList, 887);
        this.$emit("dataValue", this.dataSourceList)
        this.visible = false
      } else if (this.dataSourceList.length == '') {
        return this.$baseMessage("请选择一条数据", "error");
      } else if (this.dataSourceList.length > 1) {
        return this.$baseMessage("只能选择一条数据", "error");
      }
    },
    //取消
    closeAdd () {
      this.visible = false
    },
    //关闭
    close () {
      this.visible = false
    },
    //重置
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        jcDm: this.jcDmList[0].jcDm,
        state: '',
        userName: "",
        xzqhDm: undefined,
      }
      this.fetchData()
    },
    //搜索
    // search() {
    //     this.queryForm.pageNum = 1
    //     this.pagination.current = 1
    //     this.fetchData()
    // },
    // 获取数据
    fetchData () {
      getfindByConditionPageableApi(this.queryForm).then(res => {
        console.log(res);
        this.dataSource = res.data.rows
        this.pagination.total = res.data.total;
      })
      // this.fetchData()
    },
    //补选单位
    async getAdministrativeStateList () {
      let res = await getAdministrativeStateListApi()
      if (res.data.code == '0000') {
        this.xzqhDmList = res.data.data
      }
    },
    //获取当前届次下拉数据列表
    async getLevelListFn () {
      const res = await getLevelListApi()
      if (res.data.code === "0000") {
        this.jcDmList = res.data.data
        // console.log(this.periods[0].jcDm, '00');
        this.queryForm.jcDm = this.jcDmList[0].jcDm;//改过
      }
    },



  },
};
</script>

<style>
</style>