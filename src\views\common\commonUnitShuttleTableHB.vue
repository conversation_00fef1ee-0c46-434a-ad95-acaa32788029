<template>
  <!-- 13楼 单位数据列表穿梭框 -->
  <a-modal destroyOnClose
           :visible.sync="unitTableVisible"
           width="80%"
           title="查找"
           @cancel="close"
           @ok="unitTableOk"
           :zIndex="2000">
    <a-spin :indicator="indicator"
            :spinning="listLoading">
      <div style="justify-content: center; display: flex; align-items: center;">
        <a-form layout="inline">
          <a-form-item label="单位名称">
            <a-row>
              <a-input v-model="queryForm.orgName"
                       allow-clear
                       style="width: 400px;"></a-input>
            </a-row>
          </a-form-item>
        </a-form>
        <a-button type="primary"
                  @click="getProposalUndertakesList">查询</a-button>
      </div>
      <a-row>
        <p style="padding:10px">提示：单位名称用逗号分割查询（例如：xx单位,xx单位）</p>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-table bordered
                   :columns="columns"
                   :dataSource="dataSource"
                   rowKey="orgCode"
                   :customRow="clickRow"
                   :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onSelect: onSelect,
            type: multiple ? 'checkbox' : 'radio',
              getCheckboxProps: (record) => ({
          props: { 
            // disabled: true, 
            disabled: record.disabledXUANX == '0',
            
          },
        })
          }"
                   :pagination="false"
                   :scroll="{ x: '50%', y: 338 }"></a-table>
        </a-col>
        <a-col :span="12">
          <a-table bordered
                   :dataSource="rightData"
                   rowKey="orgCode"
                   :columns="rightColumns"
                   :pagination="false"
                   :scroll="{ x: '50%', y: 338 }">
            <div slot="action"
                 slot-scope="text, record">
              <span style="color: #d92b3e; cursor: pointer;"
                    @click="delRightData(record)">删除</span>
            </div>
          </a-table>
        </a-col>
      </a-row>
    </a-spin>
  </a-modal>
</template>
<script>
import { getProposalUndertakes } from "@/api/myJob/myProposal.js";
export default {
  data () {
    return {
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      listLoading: false,
      unitTableVisible: false,
      searchValue: "",
      dataSource: [],
      columns: [
        {
          title: "单位名称",
          dataIndex: "orgName"
        }
      ],
      rightColumns: [
        {
          title: "单位名称",
          dataIndex: "orgName"
        },
        {
          width: 100,
          align: "center",
          fixed: "right",
          title: "操作",
          scopedSlots: { customRender: "action" }
        }
      ],
      queryForm: {
        orgName: ""
      },
      multiple: true, //多选
      rightData: [],
      selectedRowKeys: []
    };
  },
  props: {
    defaultUnit: Array,
    disableSelectedRowKeys: Array,
    unitType: {
      type: String,
      default: () => {
        return undefined;
      }
    }
  },
  watch: {
    unitTableVisible (newVal) {
      if (newVal) {
        // console.log(this.disableSelectedRowKeys, "数据", this.defaultUnit);
        this.getProposalUndertakesList();
        this.selectedRowKeys = this.defaultUnit;
      } else {
        this.disableSelectedRowKeys = [];
        this.defaultUnit = [];
      }
    }
  },
  methods: {
    // 点击行
    clickRow (record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: event => {
            if (!this.multiple) {
              this.rightData = [];
            }
            // 
            if (record.disabledXUANX != '0') {
              this.rightData.push(record);
            }


            // 存在即取消选中，不存在即选中
            if (this.selectedRowKeys.includes(record.orgCode)) {
              this.onSelect(record, false, this.rightData);
            } else {
              this.onSelect(record, true, this.rightData);
            }
          }
        }
      };
    },
    // 右边table 删除
    delRightData (row) {
      this.rightData = this.rightData.filter(
        item => item.orgCode != row.orgCode
      );
      this.selectedRowKeys = this.rightData.map(item => item.orgCode);
    },
    // 保存
    unitTableOk () {
      this.$emit("emitUnitTable", this.rightData);
      this.unitTableVisible = false;
      Object.assign(this.$data, this.$options.data());
    },
    onSelect (record, selected, selectedRows, nativeEvent) {
      // 如果有搜索值
      if (this.queryForm.orgName) {
        // 选中
        if (selected) {
          // 去重
          let newData = [...this.rightData, ...selectedRows];
          newData = newData.reduce(function (prev, cur) {
            let has = false;
            prev.map(v => {
              if (v.orgCode == cur.orgCode) {
                has = true;
              }
            });
            if (!has) {
              prev.push(cur);
            }
            return prev;
          }, []);
          this.rightData = newData;
        } else {
          // 取消选中
          this.rightData = this.rightData.filter(
            item => item.orgCode != record.orgCode
          );
        }
      } else {
        if (selected) {
          this.rightData = selectedRows;
        } else {
          // 取消选中
          this.rightData = selectedRows.filter(
            item => item.orgCode != record.orgCode
          );
        }
      }
      // 设置选中值
      this.selectedRowKeys = this.rightData.map(item => item.orgCode);
      console.log("🤗🤗🤗, this.rightData =>", this.rightData);
    },
    // 关闭
    close () {
      this.unitTableVisible = false;
      this.selectedRowKeys = [];
      Object.assign(this.$data, this.$options.data());
    },
    // 获取议案建议的承办单位列表
    getProposalUndertakesList () {
      this.listLoading = true;
      let orgList = [];
      if (this.queryForm.name && this.queryForm.name?.indexOf(" ")) {
        this.queryForm.name = this.queryForm.name
          .toString()
          .replace(/\s/g, "，");
      }
      if (this.queryForm.orgName && this.queryForm.orgName?.indexOf(",")) {
        this.queryForm.orgName = this.queryForm.orgName
          .toString()
          .replace(/,/gi, "，");
        orgList = this.queryForm.orgName.split("，");
      } else {
        this.queryForm.orgName = "";
      }
      orgList = orgList.filter(i => {
        return i && i != null && i.trim();
      });
      this.queryForm.orgName = orgList.toString() || [];

      // console.log(this.queryForm.orgName, "参数修改", orgList);
      getProposalUndertakes(this.queryForm).then(res => {
        if (res.data.code == 200) {
          this.dataSource = res.data.data;
          // 将不可选的选项进行禁用
          if (this.disableSelectedRowKeys && this.disableSelectedRowKeys.length > 0) {
            this.dataSource.map(item => {
              this.disableSelectedRowKeys.map(i => {
                if (i == item.orgCode) {
                  item.disabledXUANX = "0";
                }
              });
            });
          }
          if (this.unitType) {
            // 解决1
            if (!this.queryForm.orgName && this.defaultUnit) {
              this.rightData = [];
              this.selectedRowKeys = this.defaultUnit;
              this.rightData = this.dataSource.filter(item =>
                this.defaultUnit.includes(item.orgCode)
              );
            }
          } else {
            if (!this.queryForm.orgName && this.defaultUnit) {
              this.rightData = [];
              this.selectedRowKeys = this.defaultUnit;
              this.rightData = this.dataSource.filter(item =>
                this.defaultUnit.includes(item.orgCode)
              );
            }
          }

        }
        this.listLoading = false;
      });
    }
  }
};
</script>
