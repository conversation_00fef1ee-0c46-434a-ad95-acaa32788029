import request from "@/utils/request";
import qs from "qs";

export function getPage(data) {
    return request({
        url: "/api/v1/meetingDining/apply/getPage",
        method: "post",
        data,
    });
}

export function doSave(data) {
    return request({
        url: "/api/v1/meetingDining/apply/save",
        method: "post",
        data,
    });
}

export function doRemove(data) {
    return request({
        url: "/api/v1/meetingDining/apply/remove",
        method: "post",
        data,
    });
}

export function updatePassNumber(data) {
    return request({
        url: "/api/v1/meetingDining/apply/updatePassNumber",
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        transformRequest: [
            function(data) {
                return qs.stringify(data);
            },
        ],
    });
}

export function getById(data) {
    return request({
        url: "/api/v1/meetingDining/apply/getById",
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        transformRequest: [
            function(data) {
                return qs.stringify(data);
            },
        ],
    });
}
