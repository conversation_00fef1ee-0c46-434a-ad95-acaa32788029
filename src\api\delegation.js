import request from "@/utils/requestTemp";
export function adjustingDelegationComplete(data) {
    return request({
      url: "/adjustingDelegation/complete",
      method: "post",
      data: data,
    });
}

export function adjustingDelegationCreate(data) {
  return request({
    url: "/adjustingDelegation/create",
    method: "post",
    data: data,
  });
}

export function findAdjustingDelegation(params) {
  return request({
    url: "/adjustingDelegation/findAdjustingDelegation",
    method: "get",
    params: params,
  });
}

export function adjustComplete(data) {
  return request({
    url: "/dutyAdjust/complete",
    method: "post",
    data,
  });
}

export function dutyAdjustComplete(data, params) {
  return request({
    url: "/dutyAdjust/complete",
    method: "post",
    data,
    params: params
  });
}

export function dutyAdjustSave(data) {
  return request({
    url: "/dutyAdjust/save",
    method: "post",
    data: data,
  });
}

export function dutyAdjustDeleteIds(data) {
  return request({
    url: "/dutyAdjust/delete",
    method: "post",
    data: data,
  });
}

export function findByJcDmAndDbId(data) {
  return request({
    url: "/stateChanges/findByJcDmAndDbId",
    method: "get",
    params: data,
  });
}
//  +
export function findByDbId(data) {
  return request({
    url: "/stateChanges/findByDbId",
    method: "get",
    params: data,
  });
}

export function createStateChange(data) {
  return request({
    url: "/stateChanges/create",
    method: "post",
    data,
  });
}

// export function updateState(data) {
//   return request({
//     url: "/stateChanges/updateState",
//     method: "post",
//     params: data,
//   });
// }

export function getAdjustingDelegationList(params, data) {
  return request({
    url: "/adjustingDelegation/getAdjustingDelegationList",
    method: "post",
    params: params,
    data: data,
  });
}

export function getDutyAdjustList(params, data) {
  return request({
    url: "/dutyAdjust/getDutyAdjustList",
    method: "post",
    params: params,
    data: data,
  });
}

export function dutyAdjustAssistantList(data) {
  return request({
    url: "/dutyAdjustAssistant/list",
    method: "post",
    data: data,
  });
}

export function findDutyAdjustById(params) {
  return request({
    url: "/dutyAdjust/find",
    method: "get",
    params: params,
  });
}

export function cancelTodoById(data) {
  return request({
    url: "/task/cancelTodoById",
    method: "post",
    data: data,
  });
}
