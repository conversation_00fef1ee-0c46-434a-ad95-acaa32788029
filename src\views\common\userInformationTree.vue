<template>
  <!-- 归属栏目树 -->
  <a-modal
    :title="userInformationTree"
    :visible.sync="userInformationTreeVisible"
    width="50%"
    destroyOnClose
    @cancel="close"
  >
    <a-input
      style="width: 300px"
      v-model="keyword"
      placeholder="输入关键字过滤"
      allow-clear
      @change="onChange"
    />
    <a-tree
      ref="orgTree"
      checkable
      :selectable="false"
      style="height: 800px; overflow-y: auto"
      :default-expanded-keys="expandedKeys"
      :replace-fields="replaceFields"
      :tree-data="orgTreeData"
      @check="onCheck"
      :selected-keys="selectedKeys"
    >
      <template slot="title" slot-scope="{ name }">
        <span v-if="keyword && name.indexOf(keyword) > -1" style="color: #f50">
          {{ name }}
        </span>
        <span v-else>{{ name }}</span>
      </template>
    </a-tree>

    <span slot="footer" class="dialog-footer">
      <a-button style="padding-left: 10px" type="primary" @click="confirm"
        >确 定</a-button
      >

      <a-button @click="close">取 消</a-button>
    </span>
  </a-modal>
</template>

<script>
import { instance_1 } from "@/api/axiosRq";
export default {
  data() {
    return {
      userName: [],
      userNameList:[],
      // 选择的节点
      selectedKeys: [],
      keyword: "",
      name: "",
      expandedKeys: [],
      userInformationTreeVisible: false,
      orgTreeData: [],
      replaceFields: {
        title: "name",
        key: "id",
        children: "user",
      },
    };
  },
  props: {
    userInformationTree: {
      type: String,
      default: "选择发送短信用户",
    },
  },
  watch: {
    userInformationTreeVisible(newVal) {
      if (newVal) {
        this.initOrgTree();
      } else {
        this.expandedKeys = [];
      }
    },
  },
  methods: {
    // 关键字搜索
    onChange() {},
    // 关闭
    close() {
      this.userInformationTreeVisible = false;
      this.keyword = "";
    },
    //获取数据
    initOrgTree() {
      instance_1({
        // url: "contentmanager/issue/columnTree",
        url: "srd/srdMessagelog/treeData",
        method: "post",
      }).then((res) => {
        if (res.data.code == "0000") {
          this.orgTreeData = res.data.data;
          this.userInformationTreeVisible = true;
          this.orgTreeData.forEach((item) => {
            delete item.root;
          });
        }
      });
    },
    // 获取所在的父节点
    getParentKey(id, tree) {
      let parentKey = "";
      for (let index = 0; index < tree.length; index++) {
        const node = tree[index];
        if (node.child) {
          if (node.child.some((item) => item.id === id)) {
            parentKey += node.id + ",";
          } else if (this.getParentKey(id, node.child)) {
            parentKey = this.getParentKey(id, node.child);
          }
        }
      }
      return parentKey;
    },
    //   // 树状选择
    // onCheck(item, data) { 
    //   this.userInformationTreeVisible = false; 
    //   // this.getParentKey(item[0],this.orgTreeData); 
    //   this.$emit("confirm", data);
    // }, 
    // 树状选择
    onCheck(item, info) {
      if ((info.selected = true)) { 
        console.log(info.node.dataRef);
        if (!this.userName.includes(info.node.dataRef.name)) {
          
          this.userName.push(info.node.dataRef.name);
          // 判断loginName!=undefined||info.node.dataRef.user.length!=0
          if(info.node.dataRef.createDate){ 
            info.node.dataRef.user.forEach((item) => { 
          this.userNameList.push(item);
            });
          }else{
              this.userNameList.push(info.node.dataRef);
          }
        }
      } else {
        for (let i = 0; i < this.userName.length; i++) {
          if (this.userName[i] == info.node.dataRef.name) {
            this.userName.splice(i, 1);
            this.userNameList.splice(i, 1);
            break; 
          }
        } 
      }
    },
    confirm() {
      this.userInformationTreeVisible = false;
      this.$emit("confirm", this.userNameList,this.userName);
    },
  },
};
</script>
