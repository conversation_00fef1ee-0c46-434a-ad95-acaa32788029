<template>
  <a-col :span="getSpanNum" style="padding: 0">
    <FormSelect
      :value="administrativeAreaId"
      label="行政区县"
      :prop="administrativeAreaIdKey"
      :disabled="disabled"
      :allow-clear="allowClear"
      :options="options.administrativeAreaId"
      :span="innerSpan"
      @change="onAdministrativeAreaIdChange"
      @select="$emit('administrativeAreaSelect', $event)"
    />

    <FormSelect
      v-if="cascade > 1"
      :value="streetTownId"
      label="乡镇街道"
      :prop="streetTownIdKey"
      :options="options.streetTownId"
      :disabled="disabled"
      :allow-clear="allowClear"
      :cascade-label="!administrativeAreaId && '行政区县'"
      :span="innerSpan"
      @change="onStreetTownIdChange"
      @select="$emit('administrativeSelect', $event)"
    />

    <FormSelect
      v-if="cascade > 2"
      :value="liaisonStationId"
      label="联 络 站"
      :prop="liaisonStationIdKey"
      :options="options.liaisonStationId"
      :disabled="disabled"
      :allow-clear="allowClear"
      :cascade-label="!streetTownId && '乡镇街道'"
      :span="innerSpan"
      @change="onLiaisonStationIdChange"
      @select="$emit('liaisonStationSelect', $event)"
    />
  </a-col>
</template>

<script>
import FormSelect from "@/components/FormSelect/index.vue";
import {
  getPermList as getAdministrativePermList,
  getList as getAdministrativeList,
} from "@/api/administrativearea";
import {
  getPermList as getStreetTownPermList,
  getList as getStreetTownsList,
} from "@/api/streettown";
import { listForAdmin as getStations } from "@/api/station";

/**
 * 行政区县、乡镇街道、联络站联动选择器
 * 1.基础使用 <AdminStreetStationCascade v-model="queryForm" />
 * 2.联动输入框个数 <AdminStreetStationCascade v-model="queryForm" :cascade="1" />
 */
export default {
  name: "AdminStreetStationCascade",
  components: { FormSelect },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    administrativeAreaIdKey: {
      type: String,
      default: "administrativeAreaId",
    },
    streetTownIdKey: {
      type: String,
      default: "streetTownId",
    },
    liaisonStationIdKey: {
      type: String,
      default: "liaisonStationId",
    },
    allowClear: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // 联动个数 1 ｜ 2 ｜ 3
    cascade: {
      type: Number,
      default: 3,
    },
    allPerm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      options: {
        administrativeAreaId: [],
        streetTownId: [],
        liaisonStationId: [],
        innerSpan: '',
      },
    };
  },
  computed: {
    getSpanNum() {
      if (this.cascade === 1) {
        this.innerSpan = 24
        return 8
      } else if (this.cascade === 2) {
        this.innerSpan = 12
        return 16
      } else {
        this.innerSpan = 8
        return 24
      }
    },
    administrativeAreaId() {
      return this.value[this.administrativeAreaIdKey];
    },
    streetTownId() {
      return this.value[this.streetTownIdKey];
    },
    liaisonStationId() {
      return this.value[this.liaisonStationIdKey];
    },
  },
  watch: {
    administrativeAreaId: {
      immediate: true,
      handler(val) {
        this.options.streetTownId = [];
        if (val && this.cascade > 1) {
          if (this.allPerm) {
            getStreetTownsList({ administrativeAreaId: val }).then(
              (response) => {
                this.options.streetTownId = response.data;
              }
            );
          } else {
            getStreetTownPermList({
              administrativeAreaId: val,
            }).then((response) => {
              this.options.streetTownId = response.data;
            });
          }
        }
      },
    },
    streetTownId: {
      immediate: true,
      handler(val) {
        this.options.liaisonStationId = [];
        if (val && this.cascade > 2) {
          getStations({ streetTownId: val }).then((response) => {
            this.options.liaisonStationId = response.data;
          });
        }
      },
    },
  },
  created() {
    this.load();
  },
  methods: {
    load() {
      // 获取行政区县
      if (this.allPerm) {
        getAdministrativeList().then((response) => {
          this.options.administrativeAreaId = response.data;
        });
      } else {
        getAdministrativePermList().then((response) => {
          this.options.administrativeAreaId = response.data;
        });
      }
    },
    onChange(value) {
      Object.assign(this.value, value);
      this.$emit("update:value", value);
      this.$emit("change", value);
    },
    onAdministrativeAreaIdChange(value) {
      this.onChange({
        ...this.value,
        [this.administrativeAreaIdKey]: value,
        [this.streetTownIdKey]: undefined,
        [this.liaisonStationIdKey]: undefined,
      });
    },
    onStreetTownIdChange(value) {
      this.onChange({
        ...this.value,
        [this.streetTownIdKey]: value,
        [this.liaisonStationIdKey]: undefined,
      });
    },
    onLiaisonStationIdChange(value) {
      this.onChange({
        ...this.value,
        [this.liaisonStationIdKey]: value,
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
