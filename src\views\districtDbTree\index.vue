<template>
    <div class="table-container">
      <a-spin :indicator="indicator" :spinning="listLoading">
        <a-table
            ref="noticeTable"
            :bordered="false"
            class="tableLimit"
            :columns="columns"
            :pagination="false"
            :data-source="list"
            :scroll="{ x: 300, y: 0 }"
        >
        </a-table>
      </a-spin>

      <setDistrictDbTree ref="setDistrictDbTreeRef"></setDistrictDbTree>
    </div>
  </template>
  
  <script>
  import {
    getList as getAdministrativeAreas
  } from "@/api/administrativearea";
  import setDistrictDbTree from "./setDistrictDbTree";

  export default {
    name: "Table",
    components: {
      setDistrictDbTree
    },
    data() {
      return {
        listLoading: false,
        indicator: <a-icon type="loading" style="font-size: 24px" spin />,
        list: [],
        indexNum: 1,
        // 列表
        columns: [
          {
            fixed: "left",
            title: "序号",
            key: "index",
            align: "center",
            width: 60,
            ellipsis: true,
            customRender: (text, record, index) =>
              `${(this.indexNum - 1) * this.indexNum + index + 1}`,
          },
          {
            title: "行政区划",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "name",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 150,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleSettingTree(record)
                    },
                  },
                },
                "设置区级权限树"
              ),
            ]);
          },
        },
        ],
      };
    },
    created() {
      this.fetchData();
      this.$store.dispatch("navigation/breadcrumb1", "网上联络站");
      this.$store.dispatch("navigation/breadcrumb2", "联络站列表");
    },
    methods: {
      fetchData() {
        this.listLoading = true;
        getAdministrativeAreas().then((response) => {
          this.list = response.data;
          this.listLoading = false;
        });
      },
      handleSettingTree(record){
        this.$refs["setDistrictDbTreeRef"].handleShow(record.id);
      },
    },
  };
  </script>
  <style scoped>
  </style>
  