import request from "@/utils/requestTemp";

// 查询列表
export function getList(params, data) {
  return request({
    url: "/dbdh/getList",
    method: "post",
    params: params,
    data: data
  });
}

export function getById(params) {
    return request({
      url: "/dbdh/getById",
      method: "get",
      params: params,
    });
}

export function getSfDmList(params) {
    return request({
      url: "/codeParam/dbxx/dbsf/findAll",
      method: "get",
    });
}

export function create(data) {
  return request({
    url: "/dbdh/create",
    method: "post",
    data: data,
  });
}

export function updateById(data) {
  return request({
    url: "/dbdh/updateById",
    method: "post",
    data: data,
  });
}

export function getDbdhByOrgId(params) {
  return request({
    url: "/dbdh/getDbdhByOrgId",
    method: "get",
    params: params,
  });
}

export function saveCanAddDbNode(data) {
  return request({
    url: "/dbdh/saveCanAddDbNode",
    method: "post",
    data: data,
  });
}

export function getCanAddDbNode(params) {
  return request({
    url: "/dbdh/getCanAddDbNode",
    method: "get",
    params: params,
  });
}

// 获取管理人员个人可管理的代表大会范围
export function getMyDbdhList(params) {
  return request({
    url: "/dbxx/dbdh/getMyDbdhList",
    method: "get",
    params: params,
  });
}
// 获取管理人员个人可管理的代表大会范围（新的分级分权方案）
export function getMyDbdhListForLayer(params) {
  return request({
    url: "/dbxx/dbdh/getMyDbdhListForLayer",
    method: "get",
    params: params,
  });
}

export function findAllJcBydhDm(params) {
  return request({
    url: "/codeParam/dbxx/jc/findAllBydhDm",
    method: "get",
    params: params,
  });
}


// 获取代表个人的代表大会
export function getDbDbdhList(params) {
	return request({
		url: "/dbxx/dbdh/getDbDbdhList",
		method: "get",
		params: params,
	});
}

export function getListNoPage(params) {
  return request({
    url: "/dbdh/getListNoPage",
    method: "post",
    params: params
  });
}