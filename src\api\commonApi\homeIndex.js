import { instance_1 } from "@/api/axiosRq";
import { data } from "jquery";

// 代表选举管理工作人员首页
export function getfindCountApi(params) {
    return instance_1({
        url: `/home/<USER>
        method: "get",
        params,
    });
}
// 代表信息管理工作人员首页
export function getfindInfoCountApi(params) {
    return instance_1({
        url: `/infoCount/findInfoCount`,
        method: "get",
        params,
    });
}
// 履职登记管理工作人员首页
export function getgetIndexCountNumApi(params) {
    return instance_1({
        url: `/activityIndex/getIndexCountNum`,
        method: "get",
        params,
    });
}
