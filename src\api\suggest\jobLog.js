import { instance_yajy } from "@/api/axiosRq";
// 查询调度日志列表
export function listJobLog(query) {
  return instance_yajy({
    url: '/api/v1/quartzLog/queryList',
    method: 'post',
    data: query
  })
}

// 删除调度日志
export function delJobLog(jobLogId) {
  return instance_yajy({
    url: '/api/v1/quartzLog/remove',
    method: 'post',
    data: jobLogId
  })
}

// 清空调度日志
export function cleanJobLog() {
  return instance_yajy({
    url: '/api/v1/quartzLog/clean',
    method: 'post'
  })
}

// 导出调度日志
export function exportJobLog(query) {
  return instance_yajy({
    url: '/api/v1/quartzLog/exportExcel',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}
