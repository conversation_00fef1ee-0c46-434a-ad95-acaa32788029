{"name": "ilz", "version": "1.0.0", "private": true, "author": "lxp", "participants": [], "homepage": "https://ilz.rd.gz.cn", "scripts": {"dev": "vue-cli-service serve", "preview": "node preview/index.js", "build": "vue-cli-service build", "build:test": "vue-cli-service build --mode test", "globle": "npm install -g cnpm --registry=https://registry.npm.taobao.org&&cnpm i rimraf npm-check-updates nrm -g&&rimraf node_modules&&cnpm i", "lint": "stylelint **/*.{vue,css,scss} --fix&&vue-cli-service lint --fix", "inspect": "vue-cli-service inspect", "template": "plop", "clear": "rimraf node_modules&&cnpm i&&increase-memory-limit", "update": "nrm use taobao&&ncu -u&&cnpm i", "svgo": "svgo -f src/icons/svg --config=svgo.yml&&svgo -f src/remixIcon/svg --config=svgo.yml", "push": "start ./push.sh", "deploy": "start ./deploy.sh", "increase-memory-limit": "increase-memory-limit"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "dependencies": {"@antv/g2plot": "^2.3.40", "ant-design-vue": "^1.7.8", "axios": "^0.19.2", "clipboard": "^2.0.6", "codemirror": "^5.53.2", "core-js": "^3.6.5", "dayjs": "^1.8.27", "docxtemplater": "^3.17.6", "echarts": "^4.7.0", "echarts-wordcloud": "^1.1.3", "element-ui": "^2.13.2", "file-saver": "^2.0.5", "jquery": "^3.5.1", "js-cookie": "^2.2.1", "jsencrypt": "^3.0.0-rc.1", "jsonlint": "^1.6.3", "jszip-utils": "^0.1.0", "lodash": "^4.17.15", "moment": "^2.26.0", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pdfjs-dist": "^2.8.335", "pizzip": "^3.0.6", "qrcodejs2": "0.0.2", "qs": "^6.9.4", "screenfull": "^5.0.2", "vue": "^2.6.11", "vue-echarts": "^5.0.0-beta.0", "vue-moment": "^4.1.0", "vue-print-nb": "^1.7.5", "vue-qart": "^2.2.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.1.6", "vue-waterfall-easy": "^2.4.4", "vuedraggable": "^2.23.2", "vuex": "^3.4.0", "xlsx": "^0.16.9", "zx-comparison": "^1.0.3", "zx-count": "^0.3.4", "zx-icon": "1.0.2", "zx-keel": "^0.9.0", "zx-magnifie": "^0.4.0", "zx-markdown-editor": "^0.0.1", "zx-player": "^0.9.6", "zx-swiper": "^0.5.5", "zx-verify": "^0.0.1"}, "devDependencies": {"@babel/register": "^7.9.0", "@vue/cli-plugin-babel": "^4.3.1", "@vue/cli-plugin-eslint": "^4.3.1", "@vue/cli-plugin-router": "^4.3.1", "@vue/cli-plugin-vuex": "^4.3.1", "@vue/cli-service": "^4.3.1", "@vue/eslint-config-prettier": "^6.0.0", "autoprefixer": "^9.8.0", "babel-eslint": "^10.1.0", "babel-polyfill": "^6.26.0", "compression-webpack-plugin": "^4.0.0", "connect": "^3.7.0", "eslint": "^7.0.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "filemanager-webpack-plugin": "^2.0.5", "html-webpack-plugin": "^4.3.0", "husky": "^4.2.5", "increase-memory-limit": "^1.0.7", "lint-staged": "^10.2.4", "mockjs": "^1.1.0", "plop": "^2.6.0", "prettier": "^2.0.5", "sass": "^1.26.5", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "^2.1.4", "script-loader": "^0.7.2", "serve-static": "^1.14.1", "stylelint": "^13.4.1", "stylelint-config-recess-order": "^2.0.4", "stylelint-config-standard": "^20.0.0", "stylelint-order": "^4.0.0", "svg-sprite-loader": "^5.0.0", "svgo": "^1.3.2", "tasksfile": "^5.1.1", "vue-template-compiler": "^2.6.11", "webpack-dev-server": "^2.6.1", "webpackbar": "^4.0.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}}