<template>
  <a-modal title="我的笔记"
           :visible.sync="visible"
           width="80%"
           @cancel="close">
    <a-card>
      <div>
        <a-form layout="horizontal">
          <div>
            <a-row>
              <a-col :md="8"
                     :sm="24">
                <a-form-item label="笔记内容"
                             :labelCol="{ span: 6 }"
                             :wrapperCol="{ span: 18, offset: 0 }">
                  <a-input placeholder="请输入笔记内容"
                           v-model="queryForm.content"
                           allow-clear
                           v-on:keyup.enter="fetchData" />
                </a-form-item>
              </a-col>
              <a-col :md="8"
                     :sm="24">
                <span style="margin-left: 10px;margin-top:4px">
                  <a-button type="primary"
                            @click="fetchData">搜索</a-button>
                  <a-button style="margin-left: 12px;"
                            @click="reset">刷新</a-button>
                </span>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </div>
      <div>
        <standard-table :columns="columns"
                        :dataSource="dataSource"
                        :loading="TBloading"
                        rowKey="id"
                        @clear="onClear"
                        @change="onChange"
                        @selectedRowChange="onSelectChange"
                        @tableClick="tableClick">
          <div slot="action"
               slot-scope="{  record }">
            <a-button type="link"
                      @click.stop="dellist(record)">
              删除
            </a-button>
          </div>
        </standard-table>
      </div>
    </a-card>
    <!-- 定义了插槽 -->
    <slot slot="footer"
          name="userFooter">
      <a-button type="primary"
                @click="addnotes">新增笔记</a-button>
      <a-button style="margin-left: 12px;"
                @click="close">关闭</a-button>
    </slot>
    <!-- 详情页内容 -->
    <a-modal title="我的笔记"
             :visible="dialogFormVisible"
             width="1000px"
             @cancel="close"
             destroyOnClose>
      <div>
        <a-form-model ref="detailsForm"
                      :model="detailsForm">
          <a-row span="24">
            <a-col span="24">
              <a-form-model-item prop="title">
                <a-input v-model="detailsForm.title"
                         autocomplete="off"
                         placeholder="笔记标题" />
              </a-form-model-item>
            </a-col>

            <a-col span="24">
              <a-form-model-item prop="content">
                <a-textarea placeholder="笔记内容"
                            autocomplete="off"
                            v-model="detailsForm.content"
                            rows="8"
                            allow-clear></a-textarea>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span="24">
              <a-button @click="closexq">返回</a-button>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <!-- 定义了插槽 -->
      <slot slot="footer"
            name="userFooter">
        <a-row>
          <a-col sapn="24">
            <a-button type="primary"
                      @click="myaddnotes">新建笔记</a-button>
            <a-button @click="closexq">关闭</a-button>
          </a-col>
        </a-row>
      </slot>
    </a-modal>
  </a-modal>
</template>
<script>
import { instance_1 } from "@/api/axiosRq";
export default {
  name: "noted",
  data () {
    return {
      TBloading: false,
      visible: false,
      contentId: "",
      queryForm: {},
      columns: [
        {
          title: "笔记标题",
          align: "center",
          dataIndex: "title",
        },
        {
          title: "笔记内容",
          align: "center",
          ellipsis: true,
          dataIndex: "content",
        },
        {
          title: "更改时间",
          align: "center",
          ellipsis: true,
          dataIndex: "updateDate",
        },
        {
          title: "操作",
          align: "center",
          scopedSlots: { customRender: "action" },
        },
      ],
      dataSource: [],
      //详情
      dialogFormVisible: false,
      detailsForm: {},
    };
  },
  created () {
    // this.fetchData()
  },
  methods: {
    // 删除
    async dellist (e) {
      let data = {
        id: e.id,
      };
      await this.$baseConfirm("确认要删除吗?", null, () => {
        instance_1({
          url: "home/notice/deleteNote",
          method: "get",
          params: data,
        }).then((res) => {
          if (res.data.code == "0000") {
            this.dataSource = res.data.data;
            this.fetchData()
          }
        });
      })
    },
    //详情笔记
    async tableClick (event, record) {
      this.detailsForm = record;
      this.dialogFormVisible = true;
    },

    //获取数据
    fetchData () {
      this.queryForm["contentId"] = this.contentId;

      instance_1({
        url: "home/notice/noteList",
        method: "get",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.dataSource = res.data.data;
        }
      });
    },
    //详情关闭
    closexq () {
      this.dialogFormVisible = false;
    },
    // 关闭窗口
    close () {
      this.visible = false;
      Object.assign(this.$data, this.$options.data());
    },
    myaddnotes () {
      this.closexq();
      this.addnotes();
    },

    //新增笔记
    addnotes () {
      this.$emit("noted", this.contentId)
      // this.$refs.notes.contentId = this.contentId;
      //  this.visible = false;
      // this.$refs.notes.visible = true;

    },
    //刷新
    reset () {
      this.queryForm = {
        contentId: this.contentId,
      };
      this.fetchData();
    },
    onClear () { },
    onChange () { },
    onSelectChange () { },
  },
};
</script>
<style scoped>
.formBox {
  padding: 20px;
}
</style>
