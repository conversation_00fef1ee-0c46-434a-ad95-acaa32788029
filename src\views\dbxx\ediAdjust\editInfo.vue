<template>
  <div class="table-container">
    <ZTable :list="list"
            :columns="columns" />
  </div>
</template>

<script>
export default {
  data () {
    return {
      TBloading: false,
      list: [

      ],
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "届次",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "thNum",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "当前状态",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "currStatus",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "原代表状态",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "代表类型",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "type",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "name",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "性别",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "sex",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "出生日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "birthdayTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "党派",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "party",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "学历",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "education",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "工作单位和职务",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "unit",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },


        {
          title: "手机号码",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "phone",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "变更日期",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          fixed: 'right',
          title: "操作",
          align: "center",
          width: 180,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.switMeeting(record);
                    },
                  },
                },
                "查看"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleEdit(record);
                    },
                  },
                },
                "编辑"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      // this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "代表信息管理");
    this.$store.dispatch("navigation/breadcrumb2", "个人信息修改");
  },
};
</script>
<style scoped></style>
