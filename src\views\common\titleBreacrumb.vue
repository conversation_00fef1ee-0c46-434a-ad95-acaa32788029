<template>
  <a-col style="margin-bottom: 15px; display: none;">
    <a-breadcrumb>
      <a-breadcrumb-item
        ><router-link :to="{ path: '/' }">首页</router-link></a-breadcrumb-item
      >
      <a-breadcrumb-item>{{ headline }}</a-breadcrumb-item>
      <a-breadcrumb-item v-if="title != ''">{{ title }}</a-breadcrumb-item>
      <a-breadcrumb-item v-if="text != ''">{{ text }}</a-breadcrumb-item>
      <a-breadcrumb-item v-if="text2 != ''">{{ text2 }}</a-breadcrumb-item>
    </a-breadcrumb>
  </a-col>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    headline: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    text: {
      type: String,
      default: "",
    },
    text2: {
      type: String,
      default: "",
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="scss" scoped>
::v-deep .ant-breadcrumb a {
  font-weight: 700 !important;
  text-decoration: none !important;
  color: #303133 !important;
}
.spin {
  position: absolute;
  z-index: 9999999;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  display: flex;
}
</style>
<style lang="scss" scoped>
.ant-Table {
  // 表头
  ::v-deep .ant-table-thead > tr > th {
    background-color: #f5f7fa !important;
  }
  ::v-deep.ant-table-small > .ant-table-content > .ant-table-body {
    margin: 0px !important;
  }
}
</style>
