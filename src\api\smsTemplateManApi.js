import { instance_yajy } from "@/api/axiosRq";

// 系统管理-短信模板管理
// 列表
export function querySmsTemplateList(form) {
  return instance_yajy({
    url: `/api/v1/system/smsTemplate/querySmsTemplateList`,
    method: "post",
    data: form,
  });
}
// 新增或更新短信模板
export function SmsTempSaveOrUpdate(form) {
  return instance_yajy({
    url: `/api/v1/system/smsTemplate/saveOrUpdate`,
    method: "post",
    data: form,
  });
}
// 删除短信模板
export function SmsTempDelete(form) {
  return instance_yajy({
    url: `/api/v1/system/smsTemplate/delete`,
    method: "post",
    params: form,
  });
}
