<template>
  <a-modal
    :title="orgTreeDialogTitle"
    :visible.sync="newOrgTreeDialogVisible"
    width="50%"
    destroyOnClose
    @cancel="close"
  >
    <a-input style="width:100%" placeholder="输入关键字进行过滤" v-model="filterText" @change="onChange"></a-input>
    <a-tree
      ref="orgTree"
      checkable
      @expand="onExpand"
      :expanded-keys="expandedKeys"
      :replace-fields="replaceFields"
      :tree-data="orgTreeData"
      v-model="checkIdentyData"
      @check="onCheck"
      checkStrictly
      @select="onSelect"
    >
      <template slot="title" slot-scope="{ fullName, orgLevel }">
        <a-icon :type="orgLevel == '1' ? 'apartment' : 'file'" style="margin-right: 10px;" />
        <span
          v-if="searchValue && fullName.indexOf(searchValue) > -1"
          style="color: #f50;"
        >{{ fullName }}</span>
        <span v-else>{{ fullName }}</span>
      </template>
    </a-tree>

    <span slot="footer" class="dialog-footer">
      <a-button @click="close">取 消</a-button>
      <a-button style="padding-left: 10px;" type="primary" @click="confirm">确 定</a-button>
    </span>
  </a-modal>
</template>

<script>
import { findOD } from "@/api/registrationMage/tableIng.js";
import { log } from "@antv/g2plot/lib/utils";
export default {
  data() {
    return {
      filterText: "",
      name: "",
      expandedKeys: [],
      searchValue: "",
      allChildData: [],
      selectedKeys: [],
      autoExpandParent: true,
      newOrgTreeDialogVisible: null,
      checked: [],
      checkIdentyData: [],
      orgTreeDialogVisible: false,
      orgTreeData: [],
      orgTreeDefaultKey: [],
      replaceFields: {
        title: "fullName",
        key: "id",
        children: "children",
      },
      isRadio: true,
      relType: {
        type: Number,
        default: 1,
      },
      noCheckKeys: [],
    };
  },
  props: {
    orgTreeDialogTitle: {
      type: String,
      default: "选择单位",
    },
  },
  watch: {

    orgTreeDialogVisible(newVal) {
      if (newVal) {
        this.initOrgTree();
      } else {
        this.expandedKeys = [];
        this.checked = [];
        // this.checkIdentyData = [];
      }
    },
    //通过该方法可控制选择一项，实现单选
    checkIdentyData(val) {
      if (val.checked.length > 1) {
        val.checked.halfChecked = val.checked.shift();
      }
    },
    filterText(val) { },
  },
  methods: {
    onSelect(item, data) {
      // if (data.selected) {
      //   this.checked = [data.node.dataRef];
      //   this.checkIdentyData = [data.node.dataRef.orgId];
      // }
    },
    // 树状搜索
    onChange(e) {
      console.log(this.orgTreeData, 'userTreeData');
      const value = e.target.value;
      this.searchValue = value
      if (value == '') return this.$message.info('请输入关键字')
      this.expandedKeys = [];
      this.backupsExpandedKeys = [];
      console.log(value, 'value');
      const candidateKeysList = this.getkeyList(value, this.orgTreeData, []);
      console.log(candidateKeysList, 'candidateKeysList');
      candidateKeysList.forEach(
        (item) => {
          const key = this.getParentKey(item, this.orgTreeData);
          if (key && !this.backupsExpandedKeys.some((item) => item === key)) this.backupsExpandedKeys.push(key);
        },
      );
      console.log(this.backupsExpandedKeys, 'this.backupsExpandedKeys');
      const { length } = this.backupsExpandedKeys;
      for (let i = 0; i < length; i++) {
        this.getAllParentKey(this.backupsExpandedKeys[i], this.orgTreeData);
      }
      this.expandedKeys = this.backupsExpandedKeys.slice();
      console.log(this.expandedKeys, ' this.expandedKeys ');
      if (candidateKeysList.length == 0) {
        this.$message.info('没有相关数据')
      }
    },
    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.fullName.indexOf(value) > -1) {
          keyList.push(node.id);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey;
      let temp;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          temp = this.getParentKey(key, node.children)
          if (node.children.some((item) => item.id === key)) {
            parentKey = node.id;
          } else if (temp) {
            parentKey = temp;
          }
        }
      }
      return parentKey;
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey;
      if (key) {
        parentKey = this.getParentKey(key, tree);
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey);
          }
          this.getAllParentKey(parentKey, tree);
        }
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },

    close() {
      this.orgTreeDialogVisible = false;
      this.newOrgTreeDialogVisible = false
      this.checked = [];
      this.filterText = ''
    },
    initOrgTree() {
      findOD().then((res) => {
        this.orgTreeData = res.data.data;
        this.newOrgTreeDialogVisible = true
      });
    },

    // 树状选择
    onCheck(item, data) {
      if (data.checked) {
        let value = data.node.dataRef;
        this.checked.push(value);
      } else {
        let orgId = data.node.dataRef.orgId;
        this.checked.forEach((item, index) => {
          if (item.orgId == orgId) {
            this.checked.splice(index, 1);
          }
        });
      }
    },

    confirm() {
      this.noCheckKeys = [];
      this.orgTreeDialogVisible = false;
      this.newOrgTreeDialogVisible = false
      let checkedData = this.checked[this.checked.length - 1]
      this.$emit("confirm", checkedData);
      // this.checkIdentyData = [];
      this.filterText = ''
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-tree {
  max-height: 500px;
  width: 100%;
  overflow-y: auto;
}
</style>