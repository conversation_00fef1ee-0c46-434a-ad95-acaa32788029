<template>
  <div class="table-container">
    <!-- <a-row style="margin-left:1%">
      <a-form-model ref="queryForm"
                    :model="queryForm"
                    layout="inline"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 18 }">
        <a-col span="6">
          <a-form-model-item label="届次"
                             style="width: 100%;">
            <a-select v-model="queryForm.jcDm"
                      allow-clear
                      style="width: 100%;"
                      placeholder="请选择届次">
              <a-select-option v-for="item in periods"
                               :key="item.jcDm"
                               :value="item.jcDm">{{ item.levelName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="表单状态"
                             style="width: 100%;">
            <a-select v-model="queryForm.state"
                      placeholder="请选择表单状态"
                      allow-clear
                      style="width: 100%;">
              <a-select-option v-for="item in states"
                               :key="item.dqztDm"
                               :value="item.dqztDm">{{ item.state }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col span="6">
          <a-form-model-item label="姓名"
                             style="width: 100%;">
            <a-input v-model="queryForm.userName"
                     autocomplete="off"
                     allow-clear
                     placeholder="请输入姓名"
                     v-on:keyup.enter="fetchData"></a-input>
          </a-form-model-item>
        </a-col>
      </a-form-model>
      <div style="float:right">
        <a-button type="primary"
                  @click="fetchData">搜索</a-button>
        <a-button style="margin-left: 12px;"
                  @click="reset"
                  class="pinkBoutton">重置</a-button>
      </div>
    </a-row> -->

    <SearchForm @onReset="reset" @onSearch="fetchData" :noMore="true">
      <template v-slot:topSearch>
        <SingleSelect :title="'届次'" :selectList="periods"  :showName="'levelName'" :showValue="'jcDm'" :value.sync="queryForm.jcDm" />
        <SingleSelect :title="'表单状态'" :selectList="states"  :showName="'state'" :showValue="'dqztDm'" :value.sync="queryForm.state" />
        <SingleSearch @onEnter="fetchData" :title="'姓名'" :value.sync="queryForm.userName" />
      </template>
    </SearchForm>

    <a-row>
      <a-col :span="6">
        <a-button type="primary"
                  style="margin-left: 12px;margin-bottom: 5px;"
                  @click="opencandidateDistributeEdit">分配</a-button>
        <a-button type="primary"
                  style="margin-left: 12px;"
                  @click="download">导出</a-button>
      </a-col>
    </a-row>
    <a-row>
      <standard-table :columns="columns"
                      rowKey="ID"
                      :dataSource="dataSource"
                      :loading="TBloading"
                      :pagination="pagination"
                      :selectedRows.sync="selectedRows"
                      @selectedRowChange="onSelectChange"
                      @change="onChange">
        <div class="operationStyle"
             slot="operation"
             slot-scope="{text, record}">
          <span @click="chakan(record)">查看</span>
          <span @click="xiugai(record)"
                v-if="record.STATE != '已终审'">编辑</span>
          <a-dropdown v-if="record.STATE != '已终审'">
            <a class="dropdown-link"
               @click="e => e.preventDefault()">
              更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item v-if="record.STATE != '已终审'">
                <a @click="shenhe(record)">审核</a>
              </a-menu-item>
              <a-menu-item @click="shanchu(record)"
                           v-if="(record.STATE == '已终审') ? false : true">
                删除</a-menu-item>
              <!--  v-show="record.TYPE_ID != '8'"
                            v-if="record.TYPE_ID != '3'"-->
            </a-menu>
          </a-dropdown>
        </div>
      </standard-table>
    </a-row>
    <candidateDistributeEdit ref="candidateDistributeEdit"></candidateDistributeEdit>
    <!--  -->
    <candidateDistributeSS ref="candidateDistributeSS"
                           :neWid="neWid"
                           @handleClearId="fetchData">
    </candidateDistributeSS>
    <candidateDistributemodify ref="candidateDistributemodify"
                               :neWid="neWid"
                               @handleClearId="fetchData">
    </candidateDistributemodify>
  </div>
</template>
<script>
import StandardTable from "@/components/table/StandardTable";
import candidateDistributemodify from "@/views/candidate/candidateDistributemodify";
import candidateDistributeSS from "@/views/candidate/candidateDistributeSS"
import candidateDistributeEdit from "./candidateDistributeEdit.vue";
import ZTable from "@/components/table/ZTable";
import { myPagination } from "@/mixins/pagination.js";
import { instance_1 } from "@/api/axiosRq";
import { gethxrfpbsesentdeleteApi, } from "@/api/representativeElection/candidateApi.js";
import {
  getLevelListApi,
  gebdQueryApi,
  getFormStateListApi,
} from "@/api/representativeElection/candidateApi.js";
import SingleSelect from '@/components/SingleSelect/index';
import SearchForm from '@/components/SearchForm/index';
import SingleSearch from '@/components/SingleSearch/index';

export default {
  components: { 
    candidateDistributeEdit, 
    ZTable, 
    StandardTable, 
    candidateDistributeSS, 
    candidateDistributemodify,
    SingleSelect,
    SearchForm,
    SingleSearch,  },
  // 引入分页器配置
  mixins: [myPagination],
  data () {
    return {
      tableKey: [],
      tableData: [],
      selectedRows: [],
      TBloading: false,
      neWid: '',
      states: [],
      periods: [],
      queryForm: {
        jcDm: "3",
        state: undefined,
        userName: "",
        sort: 'createTime',
        order: 'descend',
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        {
          title: "届次",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "LEVEL_NAME",
        },
        {
          title: "表单状态",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "STATE",
          sorter: {
            compare: (a, b) => a.STATE - b.STATE,
            multiple: 1,
          },
        },
        {
          title: "名单",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "USERNAME",
        },
        {
          title: "录入人",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "CREATOR",
        },
        {
          title: "录入日期",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "CREATE_TIME",
        },
        {
          fixed: 'right',
          title: "操作",
          width: 200,
          align: "center",
          scopedSlots: { customRender: 'operation' }
        },
      ],
      dataSource: [],
      selectedRows: [],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "候选人管理");
    this.$store.dispatch("navigation/breadcrumb2", "候选人分配");
    this.getDeputyListData();
    this.getFormStateListFn();
  },
  methods: {
    // 导出
    download () {
      console.log(this.tableKey, this.tableData)
      let ids = [];
      ids = this.tableData.map((i) => i.ID)
      instance_1({
        url: "/hxrfpbs/fpbbdcx/export",
        method: "post",
        responseType: "blob",
        params: this.queryForm,
        data: [...ids],
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `候选分配表.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      })
    },
    //筛选
    onChange (pagination, filters, sorter, extra) {
      console.log(sorter.order, 'sorter');
      if (sorter.order != undefined) {
        this.queryForm.sort = 'dqztDm'
        this.queryForm.order = sorter.order
      } else {
        this.queryForm.sort = 'createTime'
        this.queryForm.order = 'descend'
      }
      gebdQueryApi(this.queryForm).then((res) => {
        this.dataSource = res.data.rows;
        this.pagination.total = res.data.total;
      });
    },
    // 删除
    shanchu (record) {
      var that = this
      //候选人分配表
      let id = record.ID
      this.$confirm({
        title: '警告',
        content: '确定要删除吗？',
        onOk () {
          gethxrfpbsesentdeleteApi(id).then(res => {
            if (res.data.code == '0000') {
              that.fetchData()
              that.$message.success(res.data.msg)
            } else {
              that.$message.error(res.data.msg)
            }
          })
        },
        cancelText: '取消',
      });
    },
    //审核
    shenhe (record) {
      console.log(record);
      //候选人分配表
      this.neWid = record.ID;
      this.$refs.candidateDistributeSS.Istitle = "候选人分配表";
      this.$refs.candidateDistributeSS.visible = true;
    },
    //修改
    xiugai (record) {
      //候选人分配表
      this.neWid = record.ID;
      this.$refs.candidateDistributemodify.Istitle = "候选人分配表";
      this.$refs.candidateDistributemodify.jointTableVisible = true;
    },
    //查看
    chakan (record) {
      //候选人分配表
      console.log(record);
      this.neWid = record.ID;
      this.$refs.candidateDistributeSS.Istitle = "候选人分配表查看";
      this.$refs.candidateDistributeSS.visible = true;
    },
    //重置
    reset () {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        jcDm: "",
        userName: "",
        state: undefined,
      };

      this.selectedRows = [];
      this.fetchData();
    },

    onSelectChange (key, data) {
      this.tableKey = key
      this.tableData = data
    },
    // 打开
    opencandidateDistributeEdit () {
      this.$refs.candidateDistributeEdit.jointTableVisible = true;
    },
    // 获取数据
    getDeputyListData () {
      //获取届次下拉框
      getLevelListApi().then((res) => {
        if (res.data.code === "0000") {
          this.periods = res.data.data;
          this.queryForm.jcDm = this.periods[0].jcDm; //改过
          this.fetchData();
        }
      });
    },
    // 获取数据
    fetchData () {
      this.TBloading = true;

      gebdQueryApi(this.queryForm).then((res) => {
        this.dataSource = res.data.rows;
        this.pagination.total = res.data.total;

        this.TBloading = false;
      });
    },

    //获取表单状态下拉数据列表
    async getFormStateListFn () {
      const res = await getFormStateListApi();
      if (res.data.code === "0000") {
        this.states = res.data.data;
      }
    },
  },
};
</script>
<style scoped>
.formBox {
  padding: 20px;
}
</style>
