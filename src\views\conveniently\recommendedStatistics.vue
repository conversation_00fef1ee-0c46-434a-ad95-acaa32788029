<template>
  <div class="table-container">
    <SearchForm
      :value="queryForm"
      :no-more="true"
      @onReset="reset"
      @onSearch="search"
    >
      <template v-slot:topSearch>
        <DhJcCascade v-model="queryForm" allow-clear is-dbt @load="search" />

        <FormRangePicker
          v-model="queryForm"
          start-prop="beginTime"
          end-prop="endTime"
          label="时间范围"
          allow-clear
        />
      </template>
    </SearchForm>

    <a-row style="margin: 5px 0px 10px 8px">
      <a-col :span="6">
        <a-button
          type="primary"
          style="margin-left: 12px; margin-top: 2px"
          :loading="downloadLoading"
          @click="download"
          >导出</a-button
        >
      </a-col>
    </a-row>
    <a-spin :spinning="listLoading" :indicator="indicator">
      <standard-table
        :columns="columns"
        :row-key="
          (record, index) => {
            return record.USER_ID;
          }
        "
        :loading="TBloading"
        :data-source="dataSource"
        :pagination="pagination"
      >
        <!-- :selectedRows.sync="selectedRows"
        @selectedRowChange="onSelectChange" -->
      </standard-table>
    </a-spin>
  </div>
</template>
<script>
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
import { instance_1 } from "@/api/axiosRq";
import SearchForm from "@/components/SearchForm/index";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import FormRangePicker from "@/components/FormRangePicker/index.vue";

export default {
  name: "Statistics",
  components: {
    FormRangePicker,
    DhJcCascade,
    StandardTable,
    SearchForm,
  },
  // 引入分页器配置
  mixins: [myPagination],
  data() {
    return {
      TBloading: false,

      indicator: <a-icon type="loading" style="font-size: 24px" spin />, //转动的圈圈
      listLoading: false,
      rules: {
        beginTime: [
          { required: true, message: "请选择开始时间", trigger: "blur" },
        ],
        endTime: [
          { required: true, message: "请选择结束时间", trigger: "blur" },
        ],
      },
      timeScope: [
        // 议案届别
        // 议案届别
        // { id: 2, name: "最近三个月" },
        { id: 3, name: "今年1~6月" },
        { id: 4, name: "今年7~12月" },
        { id: 5, name: "今年内" },
        { id: 6, name: "自定义" },
      ],
      showDisabled: false,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        timeRange: "今年1~6月",
        endTime: null,
        beginTime: null,
      },
      inviteRangeDesc: "",
      //选中tableKey
      tableKey: [],
      //tableData
      tableData: [],
      visible: false,
      dataSource: [],
      selectedRows: [],
      articleId: "",
      indexNum: 1,
      //列头
      //列头
      columns: [
        // {
        //   title: "时间",
        //   align: "center",
        //   ellipsis: true,
        //   dataIndex: "USERNAME",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        {
          title: "序号",
          key: "index",
          align: "center",
          // width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            // `${(this.indexNum - 1) * this.indexNum + index + 1}`,
            `${
              (this.queryForm.pageNum - 1) * this.queryForm.pageSize + index + 1
            }`,
        },
        {
          title: "意见分类",
          align: "center",
          ellipsis: true,
          dataIndex: "commentCategory",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "数量",
          align: "center",
          ellipsis: true,
          sorter: true,
          dataIndex: "count",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        //      {
        //   title: "学习开始时间",
        //   align: "center",
        //   ellipsis: true,
        //   dataIndex: "CREATE_DATE",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
      ],
      downloadLoading: false,
    };
  },
  created() {
    // 随手拍分类统计结果
    // 统计表单
    // this.handleTime("今年1~6月");
    this.fetchData();
  },
  methods: {
    handleTime(name) {
      var time = new Date();
      let Year = time.getFullYear(); /* 当前年份 */
      if (name == "最近三个月") {
        let endTime = time.toLocaleDateString(); /* 当前时间  */
        this.queryForm.endTime = endTime;
        this.queryForm.endTime = this.queryForm.endTime.replace(/\//g, "-");
        time.setMonth(time.getMonth() - 3);
        this.queryForm.beginTime = time.toLocaleDateString();
        this.queryForm.beginTime = this.queryForm.beginTime.replace(/\//g, "-");
        this.showDisabled = true;
      } else if (name == "今年1~6月") {
        this.queryForm.beginTime = `${Year}-01-01`;
        this.queryForm.endTime = `${Year}-06-30`;
        this.showDisabled = true;
      } else if (name == "今年7~12月") {
        this.queryForm.beginTime = `${Year}-07-01`;
        this.queryForm.endTime = `${Year}-12-31`;
        this.showDisabled = true;
      } else if (name == "今年内") {
        this.queryForm.beginTime = `${Year}-01-01`;
        this.queryForm.endTime = `${Year}-12-31`;
        this.showDisabled = true;
      } else if (name == "自定义") {
        this.queryForm.beginTime = "";
        this.queryForm.endTime = "";
        this.showDisabled = false;
      } else if (name == "本届") {
        this.queryForm.beginTime = "";
        this.queryForm.endTime = "";
        this.showDisabled = true;
      }
      if (name != "自定义") {
        this.fetchData();
      }
    },
    // 导出
    download(e) {
      let data = this.queryForm;
      this.downloadLoading = true;
      instance_1({
        url: "/memberComment/countCommentCategoryExport",
        method: "get",
        responseType: "blob",
        params: data,
      }).then((res) => {
        // console.log("🤗🤗🤗, res =>", res);
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `随手拍分类统计表` + `.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000);
      });
    },
    //重置
    reset(value) {
      this.queryForm = value;
      this.pagination.current = 1;
      this.pagination.pageSize = 10;

      this.handleTime("今年1~6月");
      this.fetchData();
    },
    // 查看
    toDetailed() {},
    //复选框选改变
    onSelectChange(key, data) {
      this.selectedRows = data;
      this.tableKey = key;
      this.tableData = data;
    },
    // 数据渲染
    fetchData() {
      if (this.queryForm.beginTime == "" || this.queryForm.endTime == "") {
        this.$message.warning("请输入开始时间和结束时间");
      } else {
        this.listLoading = true;
        instance_1({
          url: "/memberComment/CountcommentCategory",
          method: "get",
          params: this.queryForm,
        }).then((res) => {
          if (res.data.code == "200") {
            this.listLoading = false;
            this.dataSource = res.data.rows;
            this.pagination.total = res.data.total;
            //  this.pagination.pageSizeOptions= ["5","10", "20", "50", "100", "200", "500"];
          }
        });
      }
    },

    //搜索
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    // 关闭窗口
    close() {
      this.articleId = "";
      this.dataSource = [];
      this.visible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
// 自定义搜索框样式
.searchStyle {
  display: flex !important;

  .ant-input {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }

  .ant-btn-primary {
    width: 66px;
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
</style>
