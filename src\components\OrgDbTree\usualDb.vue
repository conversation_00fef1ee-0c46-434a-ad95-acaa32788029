<template>
  <div style="padding: 0 20px;">
    <div class="table_container">
      <div class="group_item">
        <!-- <a-input-search placeholder="请输入" @search="onSearch" /> -->
        <a-checkbox-group
          v-model="checkedUsualValue"
          @change="onUsualChange"
        >
          <div class="cjry">
            <a-row>
              <a-col
                v-for="item in usualTreeData"
                :key="item.id"
                :span="24"
              >
                <a-checkbox :value="item" @change="onUsualItemChange"> {{ item.name }}</a-checkbox>
              </a-col>
            </a-row>
          </div>
        </a-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script>
import { getFrequentDbList } from "@/api/frequentDb";
export default {
  props: {
    allTreeData: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
  data() {
    return {
      usualTreeData: [],
      checkedUsualValue: [],
    };
  },
  created() {
    this.initFrequentDbList()
  },
  mounted() {

  },
  computed: {},
  watch: {
    allTreeData: {
      handler(newVal, oldVal) {
        this.handlerTreeChange(newVal)
      },
      deep: true
    }
  },
  methods: {
    initFrequentDbList() {
        getFrequentDbList().then(res => {
          this.usualTreeData = res.data
          this.handlerTreeChange(this.allTreeData)
        })
      },
    onUsualItemChange(e) {
      if(e.target.value) {
        this.$emit("changeItem", e.target.checked ,e.target.value)
      }
    },
    onSearch() {

    },
    onUsualChange(e, a) {
      
    },
    handlerTreeChange(allTreeData) {
      let effectIds = []
      this.usualTreeData.forEach(item => {
        allTreeData.forEach(it => {
          if(it.postId == item.postId) {
            effectIds.push(item)
          }
        })
      });
      this.checkedUsualValue = effectIds
    }
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
}
</script>

<style lang='scss' scoped>

</style>