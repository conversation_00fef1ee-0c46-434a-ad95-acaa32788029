import request from "@/utils/requestTemp";

export function levelRoleLayerList(params, data) {
  return request({
    url: '/levelRoleLayer/list',
    method: 'post',
    params: params,
    data: data
  })
}

export function addLevelRoleLayer(data) {
  return request({
    url: '/levelRoleLayer/add',
    method: 'post',
    data: data
  })
}

export function updateLevelRoleLayer(data) {
  return request({
    url: '/levelRoleLayer/updateById',
    method: 'post',
    data: data
  })
}

export function levelRoleLayerRelOrgIds(data) {
  return request({
    url: '/levelRoleLayer/relOrgIds',
    method: 'post',
    data: data
  })
}

export function levelRoleLayerRelUnitOrgIds(data) {
  return request({
    url: '/levelRoleLayer/relUnitOrgIds',
    method: 'post',
    data: data
  })
}

export function levelRoleLayerGetOrgIds(params) {
  return request({
    url: '/levelRoleLayer/getOrgIds',
    method: 'post',
    params: params
  })
}

export function levelRoleLayerGetUnitOrgIds(params) {
  return request({
    url: '/levelRoleLayer/getUnitOrgIds',
    method: 'post',
    params: params
  })
}

export function saveLayerLiaisonStation(data) {
  return request({
    url: "/levelRoleLayer/saveLayerLiaisonStation",
    method: "post",
    data: data
  });
}

export function getLiaisonStationIds(params) {
  return request({
    url: "/levelRoleLayer/getLiaisonStationIds",
    method: "post",
    params: params
  });
}
