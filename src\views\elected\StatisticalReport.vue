<template>
  <div>
    <div class="TYcss">
      <!-- 预备人选相关报表 -->
      <div class="jbxx-contt">
        <div class="jbqkss"><span class="jbqkbz">预备人选相关报表</span></div>
        <div style="padding-bottom: 20px;">
          <a-row>
            <a-col v-for="(item, index) in ybrx_meetingList"
                   :key="index"
                   :span="4"
                   @click="skiUrl(item)">
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.url"
                     :alt="item.title" />
                <div class="jbxxson_box">
                  <div class="tits"
                       v-html="item.title"
                       style="">{{ item.title }}</div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
      <!-- 候选人相关报表 -->
      <div class="jbxx-contt">
        <div class="jbqkss"><span class="jbqkbz">候选人相关报表</span></div>
        <div style="padding-bottom: 20px;">
          <a-row>
            <a-col v-for="(item, index) in hxr_meetingList"
                   :key="index"
                   :span="4"
                   @click="skiUrl(item)">
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.url"
                     :alt="item.title" />
                <div class="jbxxson_box">
                  <div class="tits"
                       v-html="item.title"
                       style="">{{ item.title }}</div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
      <!-- 代表相关报表 -->
      <div class="jbxx-contt">
        <div class="jbqkss"><span class="jbqkbz">代表相关报表</span></div>
        <div style="padding-bottom: 20px;">
          <a-row>
            <a-col v-for="(item, index) in meetingList"
                   :key="index"
                   :span="4"
                   @click="skiUrl(item)">
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.url"
                     :alt="item.title" />
                <div class="jbxxson_box">
                  <div class="tits"
                       v-html="item.title"
                       style="">{{ item.title }}</div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
  </div>
</template> 

<script>
export default {
  name: "WorkIndex",
  components: {},
  data () {
    return {
      TBloading: false,
      ybrx_meetingList: [
        {
          id: 1,
          title: "预备人选<br/>申报统计报表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/预备人选 申报统计报表.png"),
          path: "/ready/declarationReport",
        },

        { // 第8条bug放开的
          id: 2,
          title: "预备人选<br/>登记统计报表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/预备人选 登记统计报表.png"),
          path: "/ready/registerReport",
        },
        {
          id: 3,
          title: "预备<br/>人选名册",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/预备 人选名册.png"),
          path: "/ready/rosterReport",
        },
      ],
      hxr_meetingList: [
        {
          id: 1,
          title: "候选人申报表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/候选人申报表.png"),
          path: "/ready/candidate/declare",
        },
        {
          id: 2,
          title: "候选人登记表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/候选人登记表.png"),
          path: "/ready/candidate/check",
        },
        {
          id: 3,
          title: "候选人名册",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/候选人名册.png"),
          path: "/ready/candidate/muster",
        },
      ],
      meetingList: [
        {
          id: 1,
          title: "代表申报表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/代表申报表.png"),
          path: "/ballot/behalfDeclare",
        },
        {
          id: 2,
          title: "代表登记表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/代表登记表.png"),
          path: "/ballot/behalf-check",
        },
        {
          id: 3,
          title: "代表名册",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/代表名册.png"),
          path: "/ballot/behalf-muster",
        },
        {
          id: 4,
          title: "代表<br/>选举结果报告",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/代表 选举结果报告.png"),
          path: "/ballot/RepresentativeForm",
        },
        {
          id: 5,
          title: "代表<br/>补选结果报表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/代表 补选结果报表.png"),
          path: "/ballot/by-election",
        },
        {
          id: 6,
          title: "代表基本<br/>情况统计报表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/代表基本情况统计报表.png"),
          path: "/ballot/behalf-base",
        },
        {
          id: 7,
          title: "代表构成<br/>情况统计报表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/代表构成 情况统计报表.png"),
          path: "/ballot/Composition",
        },
        {
          id: 8,
          title: "代表名额<br/>分配及构成统计表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/代表名额 分配及构成.png"),
          path: "/ballot/Distribution",
        },
        {
          id: 9,
          title: "代表<br/>出缺情况统报表",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/代表 出缺统报表.png"),
          path: "/ballot/VacancyStatistics",
        },
        {
          id: 10,
          title: "补选结果",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/补选结果.png"),
          path: "/ballot/By_electionForm",
        },
        {
          id: 11,
          title: "代表出缺",
          color: "#be403d",
          url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/代表出缺.png"),
          path: "/ballot/Vacancy",
        },
        // {
        //   id: 12,
        //   title: "代表选举结果报表",
        //   color: "#be403d",
        //   url: require("@/assets/images/StatisticalReport/代表选举管理-统计查询-统计报表/meeting.png"),
        //   path: "/ballot/entryElection",
        // },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "代表选举管理");
    this.$store.dispatch("navigation/breadcrumb2", "统计报表");
  },
  methods: {

    // @!@!@
    skiUrl (col) {
      this.$router.push({
        path: col.path,
      });
    },
  },
};
</script>
