<template>
  <div>
    <a-row>
      <a-button type="primary" @click="addSocialconditions()" style="margin-bottom: 5px;"
        :disabled="communityScheduleId == ''">新增社情民意</a-button>
      <!-- table -->
      <a-spin :indicator="indicator" :spinning="listLoading">
        <a-table :bordered="false" class="directorySet-table" ref="table" size="small" :columns="columns"
          :pagination="pagination" :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }" :data-source="list" :rowKey="
  (record, index) => {
    return index;
  }
" :scroll="{ x: 300, y: 0 }"></a-table>
      </a-spin>
    </a-row>
  </div>
</template>

<script>
import {
  OpinionControllerList,
  OpinionControllerDel,
  OpinionControllerDetails,
  downloadSocialConditionsExcel,
  addSocialConditionsExcel,
  getByCommunityScheduleId,
} from "@/api/communityschedule";
export default {
  props: {
    communityScheduleId: "",
  },
  filters: {},
  data() {
    return {
      addDialogVisible: false,
      auditDialogVisible: false,
      selectMemberDialogVisible: false,
      statusList: [
        { id: "", name: "全部" },
        { id: 0, name: "待审核" },
        { id: 1, name: "已审核" },
      ],
      //list-----
      elementLoadingText: "正在加载...",
      list: [],
      listQuery: {
        pageNum: 1,
        pageSize: 5,
      },

      listLoading: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 5,
      background: true,
      //list-----
      selectRows: [],
      formLabelWidth: "120px",
      form: {
        session: "第十六届",
        serverTime: "",
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        contactPhoneNumber: "",
        contactName: "",
        year: "",
        countryMemberIds: [],
        liaisonStation: "",
      },
      //行政区域列表
      administrativeAreas: [],
      //街道数据
      streetTowns: [],
      //联络站数据
      liaisonStations: [],
      checkMembers: [],
      desc: "",
      multipleSelection: [],
      treeIds: [],
      auditOpinion: "",
      currId: "",
      auditForm: {},

      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,

      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "代表姓名",
          align: "center",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            let names = "";
            if (record.members) {
              if (record.members.length > 2) {
                names =
                  record.members[0].userName +
                  "、" +
                  record.members[1].userName +
                  "...";
              } else {
                record.members.forEach((item) => {
                  names += item.userName + "、";
                });
                names = names.substring(0, names.length - 1);
              }
            }
            return names || "/";
          },
        },
        {
          title: "联系的群众姓名",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "voterName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "群众意见建议",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "content",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "办理进度",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "progressStatus",
          customRender: (text, record, index) => {
            if (text == 1) {
              text = "意见录入";
            } else if (text == 2) {
              text = "交办";
            } else if (text == 3) {
              text = "答复";
            } else if (text == 4) {
              text = "评价";
            }
            return text || "/";
          },
        },
        {
          title: "行政区划",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "administrativeArea",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "街道乡镇",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "streetTown",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站名称",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "liaisonStation",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "录入日期",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            return text.substring(0, 10) || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 200,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.details(record);
                    },
                  },
                },
                "详情"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.details(record, "1");
                    },
                  },
                },
                record.isCompleted != 1 ? "修改" : ""
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.deleNewPublicOpinionController(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      fileList: [],
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 5, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      isDb: null,
    };
  },
  created() {
    // // 判断是否是路由跳转过来 
    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "社情民意列表");
  },
  watch: {
    checkMembers: {
      handler() {
        (this.desc = ""), this.showMemberDesc();
      },
    },
    communityScheduleId(newVal, oldVal) {
      this.listQuery.pageNum = 1;
      this.listQuery.pageSize = 5;
      this.listQuery.communityScheduleId = newVal;
      this.fetchData();
    },
  },
  methods: {
    // 下载模板
    downloadExcel() {
      downloadSocialConditionsExcel(this.queryForm).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `社情民意导入模板.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    // 上传文件限制 直接return
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },

    //导入社情民意表
    downImport({ file, fileList }) {
      // console.log(file);
      var data = new FormData();
      var params = { communityScheduleId: this.listQuery.communityScheduleId };
      data.append("file", file);
      addSocialConditionsExcel(params, data).then((res) => {
        console.log(res, "res");
        if (res.data.code == "500") {
          return this.$message.error("上传失败！请选择正确的xls文件！");
        }
        if (res.data.code == "0000") {
          this.$baseMessage("上传成功", "success");
          this.fetchData();
        }
      });
    },
    //新增社情民意
    addSocialconditions() {
      this.$router.push({
        path: "/community/newlyAdded",
        query: {
          communityScheduleId: this.listQuery.communityScheduleId,
        },
      });
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.listQuery.pageNum = pageNum;
      this.listQuery.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.listQuery.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    /**
     * 预加载数据
     */

    //列表
    fetchData() {
      this.listLoading = true;
      getByCommunityScheduleId(this.listQuery).then((response) => {
        //   console.log(response,"列表数据")
        this.list = response.rows;
        this.total = response.total;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    details(res, to_type) {
      //  详情数据
      OpinionControllerDetails({ id: res.id }).then((response) => {
        this.$router.push({
          path: "/community/newlyAdded",
          query: {
            data: response.data,
            to_type,
            communityScheduleId: this.listQuery.communityScheduleId,
          },
        });
      });
    },
    //    删除
    async deleNewPublicOpinionController(res) {
      await this.$baseConfirm("确认要删除吗?", null, () => {
        OpinionControllerDel({ id: res.id }).then((response) => {
          if (response.code == "0000") {
            this.$message.success("删除成功");
            this.fetchData();
          }
        });
      });
    },
    handleStatusQuery(val) {
      this.listQuery.status = val;
      this.fetchData();
    },

    handleAudit() {
      let list = this.selectRows;
      // let list=this.selectedRows[this.selectRows.length-1]
      if (list == null || list.length == 0 || list.length > 1) {
        this.$message.warning("请选择一条数据");
        return;
      } else {
        this.currId = list[0].id;
        this.auditDialogVisible = true;
      }
    },

    fanhui() {
      this.$router.go(-1);
    },
    showMemberDesc() {
      this.treeIds = [];
      this.checkMembers.forEach((item) => {
        this.desc += item.name + "; ";
        this.treeIds.push(item.uniqueId);
      });
    },
  },
};
</script>
