import request from "@/utils/requestTemp";
import { instance_1 } from "@/api/axiosRq";
import { data } from "jquery";
// import qs from "qs";

export function getList(data) {
  return request({
    url: "/communitySchedule/admin/list",
    method: "get",
    params: data,
  });
}

export function save(data) {
  return request({
    url: "/communitySchedule/save",
    method: "post",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    }, // 请求头
    transformRequest: [
      (data) => {
        return JSON.stringify(data);
      },
    ],
    data: data,
  });
}
export function getSignInfoByPage(data) {
  return request({
    url: "/communitySchedule/getSignInfoByPage",
    method: "get",
    params: data,
  });
}

export function listReady(data) {
  return request({
    url: "/communitySchedule/listReady",
    method: "get",
    params: data,
  });
}

export function getCommunityScheduleById(data) {
  return request({
    url: "/communitySchedule/getById",
    method: "get",
    params: data,
  });
}

export function update(data) {
  return request({
    url: "/communitySchedule/update",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    }, // 请求头
    transformRequest: [
      (data) => {
        return JSON.stringify(data);
      },
    ],
  });
}
export function audit(data) {
  return request({
    url: "/communitySchedule/audit",
    method: "post",
    params: data,
  });
}

export function todo(params, data) {
  return request({
    url: "/communityScheduleActivity/todo",
    method: "post",
    params,
    data: data,
  });
}

export function handled(params, data) {
  return request({
    url: "/communityScheduleActivity/handled",
    method: "post",
    params,
    data: data,
  });
}

// 社情民意新增

export function OpinionControllerSave(data) {
  return request({
    url: "/newPublicOpinionController/save",
    method: "post",
    // params,
    data: data,
  });
}
// 社情民意代表列表
export function getMembers(params) {
  return request({
    url: "/communityScheduleActivity/listMember",
    method: "get",
    params,
    // data: data
  });
}

// 社情民意列表
export function OpinionControllerList(params) {
  return request({
    url: "/newPublicOpinionController/list",
    method: "get",
    params,
    // data: data
  });
}
//社情民意详情
export function OpinionControllerDetails(params) {
  return request({
    url: "/newPublicOpinionController/getById",
    method: "get",
    params,
    // data: data
  });
}
//社情民意修改
export function OpinionControllerUpdata(data) {
  return request({
    url: "/newPublicOpinionController/update",
    method: "post",
    // params,
    data: data,
  });
}

//社情民意删除
export function OpinionControllerDel(params) {
  return request({
    url: "/newPublicOpinionController/deleteById",
    method: "post",
    params,
    // data: data
  });
}

//年度安排表情况
export function yearScheduledList(params) {
  return request({
    url: "/statistics/yearScheduled/list",
    method: "get",
    params: params,
  });
}
//年度安排表情况导出
export function yearScheduledExport(params) {
  return instance_1({
    url: "/statistics/yearScheduled/export",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//代表驻站情况
export function stationMemberList(params) {
  return request({
    url: "/statistics/stationMember/list",
    method: "get",
    params: params,
  });
}
//代表驻站情况导出
export function stationMemberExport(params) {
  return instance_1({
    url: "/statistics/stationMember/export",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//代表进社区活动次数统计
export function communityMemberList(params) {
  return request({
    url: "/statistics/communityMember/list",
    method: "get",
    params: params,
  });
}

//代表进社区活动次数统计
export function communityMemberExport(params) {
  return instance_1({
    url: "/statistics/communityMember/export",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//活动情况
export function liaisonStationActivityList(params) {
  return request({
    url: "/statistics/liaisonStationActivity/list",
    method: "get",
    params: params,
  });
}

//活动情况
export function liaisonStationActivityExport(params) {
  return instance_1({
    url: "/statistics/liaisonStationActivity/export",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//查询联络站活动人员
export function stationActivityMemberList(params) {
  return request({
    url: "/statistics/stationActivityMember/list",
    method: "get",
    params: params,
  });
}

//查询联络站活动人员
export function stationActivityMemberExprot(params) {
  return instance_1({
    url: "/statistics/stationActivityMember/export",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//查询问题解决情况报表
export function solveProblemList(params) {
  return request({
    url: "/statistics/solveProblem/list",
    method: "get",
    params: params,
  });
}

export function solveProblemExport(params) {
  return instance_1({
    url: "/statistics/solveProblem/export",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//代表生日祝福列表
export function BirthdayList(params) {
  return instance_1({
    url: "/birthdayMsg/list",
    method: "get",
    responseType: "blob",
    data: params,
  });
}
//代表生日祝福列表
export function BirthdayDetails(params) {
  return instance_1({
    url: "/birthdayMsg/getByid",
    method: "get",
    responseType: "blob",
    data: params,
  });
}

//下载驻站代表年度安排表导入模板
export function downloadCommunityScheduleExcel(params) {
  return instance_1({
    url: "/communitySchedule/download",
    method: "get",
    responseType: "blob",
    params: params,
  });
}
//下载社情民意导入模板
export function downloadSocialConditionsExcel(params) {
  return instance_1({
    url: "/newPublicOpinionController/download",
    method: "get",
    responseType: "blob",
    params: params,
  });
}
// 办理群众意见情况汇总 导出excel
export function downloadPublicOpinionExcel(params) {
  return instance_1({
    url: "/newPublicOpinionController/export",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//导入社情民意列表
export function addSocialConditionsExcel(params, data) {
  return instance_1({
    url: "/newPublicOpinionController/import",
    method: "post",
    // responseType: "blob",
    params: params,
    data: data,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}

//下载进社区活动导入模板
export function downloadCommunityActivitiesExcel(params) {
  return instance_1({
    url: "/communityScheduleActivity/download",
    method: "get",
    responseType: "blob",
    params: params,
  });
}
//导入进社区活动
export function addCommunityActivitiesExcel(data) {
  return instance_1({
    url: "/communityScheduleActivity/import",
    method: "post",
    // responseType: "blob",
    data: data,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}

//下载进社区计划导入模板
export function downloadCommunityPlanExcel(params) {
  return instance_1({
    url: "/communitySchedule/download",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//导入进社区计划
export function addCommunityPlanExcel(data) {
  return instance_1({
    url: "/communitySchedule/import",
    method: "post",
    // responseType: "blob",
    data: data,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}

export function getByCommunityScheduleId(params) {
  return request({
    url: "/newPublicOpinionController/getByCommunityScheduleId",
    method: "get",
    params: params,
  });
}
// 社情民意列表 导出
export function exportNew(id) {
  return instance_1({
    url: "/newPublicOpinionController/exportNew",
    method: "get",
    responseType: "blob",
    params: { id },
  });
}
//下载社情民意操作指引
export function downloadGuide(params) {
  return instance_1({
    url: "/newPublicOpinionController/downloadCZZN",
    method: "get",
    responseType: "blob",
    params: params,
  });
}
//下载进社区活动操作指引
export function downloadCommunityActivitiesGuide(params) {
  return instance_1({
    url: "/newPublicOpinionController/downloadHDZN",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//代表对联络站工作的评价情况
export function evaluationWorkList(params) {
  return request({
    url: "/communityScheduleActivityDBFeedback/getDBToLLZFeedback",
    method: "get",
    params: params,
  });
}
//代表对联络站工作的评价情况导出
export function evaluationWorkExport(params) {
  return instance_1({
    url: "/communityScheduleActivityDBFeedback/export",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

export function exportDetail(id) {
  return instance_1({
    url: "/memberComment/exportMemberComment",
    method: "get",
    responseType: "blob",
    params: { id },
  });
}

export function ExportExcelData(data) {
  return instance_1({
    url: `/communitySchedule/exportNew`,
    method: "post",
    responseType: "blob",
    data,
  });
}

export function ExportExcelValue(params) {
  return instance_1({
    url: `/communityScheduleActivity/export`,
    method: "get",
    responseType: "blob",
    params,
  });
}

export function ExportZipValue(params) {
  return instance_1({
    url: `/communityScheduleActivity/summaryExport`,
    method: "get",
    responseType: "blob",
    params,
  });
}

export function checkSummaryExport(params) {
  return instance_1({
    url: `/communityScheduleActivity/checkSummaryExport`,
    method: "get",
    params,
  });
}

//撤回
export function taskReject(params) {
  return request({
    url: "/communityScheduleActivity/taskReject",
    method: "get",
    params: params,
  });
}

//导入代表姓名
export function importRepresentation(data) {
  return instance_1({
    url: "/dmcs/common/ours/importrepresentationList",
    method: "post",
    data: data,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}

//下载批量用户导入模板
export function downloadUserImportExcel(params) {
  return instance_1({
    url: "/dmcs/common/ours/download",
    method: "get",
    responseType: "blob",
    params: params,
  });
}

//联络站活动情况查询统计
export function liaisonStationActivityList2(params) {
  return request({
    url: "statistics/liaisonStationActivity/list2",
    method: "get",
    params: params,
  });
}

//联络站活动情况查询统计导出
export function liaisonStationActivityExport2(params) {
  return instance_1({
    url: "/statistics/liaisonStationActivity/export2",
    method: "get",
    responseType: "blob",
    params: params,
  });
}
