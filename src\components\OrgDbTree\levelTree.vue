<template>
  <div class='app-container'>
    <a-tree
      ref="userTree"
      v-model="treeDefaultKey"
      :checkable="checkable"
      :expanded-keys="expandedKeys"
      :multiple="multiple"
      :selectable="selectable"
      :replace-fields="replaceFields"
      :tree-data="userTreeData"
      @expand="onExpand"
      @check="onCheck"
      @select="onSelect">
      <template slot="title" slot-scope="{ name }">
          <span
          v-if="searchValue && name.indexOf(searchValue) > -1"
          style="color: #f50"
          >{{ name }}</span
          >
          <span v-else>{{ name }}</span>
      </template>
    </a-tree>
  </div>
</template>

<script>
export default {
  props: {
    userTreeData: {
      type: Array,
      default: () => [],
    },
    treeDefaultKeyProp: {
      type: Array,
      default: () => [],
    },
    checkable: {
      type: Boolean,
      default: true,
    },
    selectable: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
  },
  components: {},
  data() {
    return {
      expandedKeys: [],
      treeDefaultKey: [],
      replaceFields: {
        title: "name",
        key: "uniqueId",
        children: "children",
      },
      searchValue: "",
      listLoading: false,
      backupsExpandedKeys: [],
    };
  },
  created() {

  },
  mounted() {

  },
  computed: {},
  watch: {
    treeDefaultKeyProp: {
      handler(val) {
        // this.treeDefaultKey = val;
        let keys = val.map(it => it.postId)
        this.treeDefaultKey = keys;
      },
      immediate: true,
    },
    userTreeData: {
      handler(newVal) {
        if (newVal.length > 0) {
          this.expandedKeys = [newVal[0].uniqueId];
        }
      },
      immediate: true,
    },
  },
  methods: {
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
    },
    onCheck(item, data) {
      // 清空参加人员
      this.checkList = [];
      this.checked = [];
      data.checkedNodesPositions
        .filter(
          (it) => it.node.componentOptions.propsData.dataRef.itemType == 2
        )
        .forEach((item1) => {
          this.checked.push(item1.node.componentOptions.propsData.dataRef);
        });
      this.$emit("checked", this.checked)
    },
    onSelect(item, data) {
      this.checkList = [];
      this.checked = [];
      if(data.selected) {
        let item = data.selectedNodes[0].data.props
        if(item.itemType == 2) {
          this.$emit("checked", [ item ])
        }
      }
    },
    // 树状搜索
    onSearch(e) {
      this.listLoading = true;
      const value = e;
      this.searchValue = value;
      if (value == "") {
        this.listLoading = false;
        this.$message.info("请输入关键字");
      } else {
        this.searchValue = value;
        this.expandedKeys = [];
        this.backupsExpandedKeys = [];
        const candidateKeysList = this.getkeyList(value, this.userTreeData, []);
        if (candidateKeysList.length == 0) {
          this.$message.destroy();
          this.listLoading = false;
          this.$message.info("没有相关数据");
          return;
        }
        candidateKeysList.forEach((item) => {
          const key = this.getParentKey(item, this.userTreeData);
          if (key && !this.backupsExpandedKeys.some((item) => item === key))
            this.backupsExpandedKeys.push(key);
        });
        const { length } = this.backupsExpandedKeys;
        for (let i = 0; i < length; i++) {
          this.getAllParentKey(this.backupsExpandedKeys[i], this.userTreeData);
        }
        this.expandedKeys = this.backupsExpandedKeys.slice();
        this.listLoading = false;
      }
    },
    getkeyList(value, tree, keyList) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.name.indexOf(value) > -1) {
          keyList.push(node.uniqueId);
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList);
        }
      }
      return keyList;
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey;
      let temp;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          temp = this.getParentKey(key, node.children);
          if (node.children.some((item) => item.uniqueId === key)) {
            parentKey = node.uniqueId;
          } else if (temp) {
            parentKey = temp;
          }
        }
      }
      return parentKey;
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey;
      if (key) {
        parentKey = this.getParentKey(key, tree);
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey);
          }
          this.getAllParentKey(parentKey, tree);
        }
      }
    },
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
}
</script>

<style lang='scss' scoped>
  .app-container {
    height: 380px;
  }
</style>