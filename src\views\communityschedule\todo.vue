<template>
  <div class="table table-container">
    <SearchForm :value="queryForm" @onReset="reset" @onSearch="search">
      <template v-slot:topSearch>
        <DhJcCascade v-model="queryForm" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>

      </template>
      <template v-slot:moreSearch>
        <AdminStreetStationCascade v-model="queryForm" allow-clear all-perm />

        <FormInput
          v-model="queryForm.userName"
          :label="'代表姓名'"
          allow-clear
          @enter="handleEnter"
        />

        <FormRangePicker
          v-model="queryForm"
          start-prop="startTime"
          end-prop="endTime"
          label="时间范围"
          allow-clear
        />

        <FormInput
          v-model="queryForm.contactName"
          :label="'联系人姓名'"
          allow-clear
          @enter="handleEnter"
        />
      </template>
    </SearchForm>

    <a-row style="margin: 5px 0px 10px 8px">
      <a-col :span="6">
        <a-button
          v-if="isBatchAudit"
          type="primary"
          style="margin-bottom: 10px"
          @click="handleBatchAudit()"
          >批量审核</a-button
        >
        <a-button
          type="primary"
          :loading="downloaDataLoading"
          style="margin-left: 8px"
          @click="downloaData"
          >导出</a-button
        >
        <!-- <a-button type="primary"
                          style="margin-left: 10px;"
                          @click="messageSinkData()">消息接收设置</a-button> -->
      </a-col>
    </a-row>
    <a-spin :indicator="indicator" :spinning="loading">
      <standard-table
        :pagination="pagination"
        row-key="id"
        :columns="columns"
        :loading="TBloading"
        :data-source="list"
        :selected-rows.sync="selectedRowKeys"
        @tableClick="clickRow"
        @onChange="onSelectChange"
      >
        <div
          slot="operation"
          slot-scope="{ text, record }"
          class="operationStyle"
        >
          <span @click="handleData(record)">查看</span>
          <span @click="handleEditData(record)">编辑</span>
          <span
            v-if="record.currStateDm !== '11' && record.currStateDm !== '12'"
            @click="handleSwitMeeting(record, '审核')"
            >审核</span
          >
          <span
            v-if="record.currStateDm == '11' || record.currStateDm == '12'"
            @click="handleSwitMeeting(record, '送审')"
            >送审</span
          >
          <span @click="handleDel(record)">删除</span>

          <!-- <a-dropdown> -->
          <!-- <a class="dropdown-link" @click="e => e.preventDefault()">
              更多
              <a-icon type="down" />
            </a> -->
          <!-- <a-menu slot="overlay">
              <a-menu-item> -->
          <!-- <a v-if="record.currStateDm == '11' || record.currStateDm == '12'"
                  @click="handleSwitMeeting(record, '送审')">送审</a> -->
          <!-- <a v-else @click="handleSwitMeeting(record, '审核')">审核</a> -->
          <!-- </a-menu-item>
              <a-menu-item @click="handleDel(record)">删除</a-menu-item>
            </a-menu> -->
          <!-- </a-dropdown> -->
        </div>
      </standard-table>
    </a-spin>
    <a-modal
      v-if="isBatchAudit"
      :visible="batchAuditVisible"
      title="批量审核"
      @ok="batchAudit()"
      @cancel="batchAuditVisible = false"
    >
      <a-textarea
        v-model="comment"
        placeholder="请输入审核意见"
        :auto-size="{ minRows: 2, maxRows: 5 }"
      />
    </a-modal>
    <a-modal
      :visible="messageSink"
      title="消息接收设置"
      @ok="batchMessageSink()"
      @cancel="messageSink = false"
    >
      <a-radio-group v-model="radioData">
        <a-radio :value="3"> 手机短信 </a-radio>
        <a-radio :value="1"> 微信公众号 </a-radio>
      </a-radio-group>

      <div style="margin-top: 20px">
        提示：选择微信公众号方式接收审核消息，需在微信关注"广州人大服务号"
      </div>
    </a-modal>
  </div>
</template>
<script>
import { ExportExcelValue } from "@/api/communityschedule";
import { myPagination } from "@/mixins/pagination.js";
import { todo } from "@/api/communityschedule.js";
import { instance_1 } from "@/api/axiosRq";
import {
  getLevelList,
  getActivityTypeList,
} from "@/api/registrationMage/tableIng.js";
import StandardTable from "@/components/table/StandardTable";
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
import { listForAdmin as getStations } from "@/api/station";
import { getFormState } from "@/api/registrationMage/tableIng.js";
import SingleSelect from "@/components/SingleSelect/index";
import SearchForm from "@/components/SearchForm/index";
import SingleSearch from "@/components/SingleSearch/index";
import TimeRangePicker from "@/components/TimeRangePicker/index";
import AdminStreetSelect from "@/components/AdminStreetSelect/index";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import FormSelect from "@/components/FormSelect/index.vue";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormRangePicker from "@/components/FormRangePicker/index.vue";
import FormDictSelect from "@/components/FormDictSelect/index.vue";
import checkPermission from "@/utils/permission";
import { DBLZ_DBJSQ } from "@/utils/enum/levelRoleMainIdentifyEnum";

export default {
  components: {
    FormRangePicker,
    AdminStreetStationCascade,
    FormSelect,
    FormInput,
    DhJcCascade,
    StandardTable,
    SearchForm,
  },
  mixins: [myPagination],
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBJSQ,
      TBloading: false,
      downloaDataLoading: false,
      radioData: null,
      messageSink: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      loading: false, // 高级搜索展开标记，默认为 false 不展开
      batchAuditVisible: false,
      comment: "",
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        dhDm: undefined,
        jcDm: undefined,
        dbtId: undefined,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        userName: undefined,
        startTime: "",
        endTime: "",
        contactName: "",
      },
      selectedRowKeys: [],
      list: [],
      indexNum: 1,
      timeScope: [
        // 议案届别
        { id: 1, name: "本届" },
        { id: 2, name: "最近三个月" },
        { id: 3, name: "今年1~6月" },
        { id: 4, name: "今年7~12月" },
        { id: 5, name: "今年内" },
        { id: 6, name: "自定义" },
      ],
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 60,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
          //  `${(this.queryForm.pageNum - 1) * this.queryForm.pageSize+index + 1}`, //序号累加
        },
        {
          title: "当前状态",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "currStateName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        // {
        //   align: "center",
        //   title: "活动编号",
        //   width: 180,
        //  ellipsis: true,
        //  dataIndex: "activityNo",
        //   customRender: (text, record, index) => {
        //     if (text) {
        //       return text;
        //     } else {
        //       return "/";
        //     }
        //   },
        // },
        {
          align: "center",
          title: "活动时间",
          width: 180,
          ellipsis: true,
          dataIndex: "serverTime",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "行政区划",
          width: 100,

          ellipsis: true,
          dataIndex: "administrativeAreaName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "街道名称",
          width: 110,

          ellipsis: true,
          dataIndex: "streetTownName",
          customRender: (text, record, index) => {
            if (text) {
              return text.replace("T", " ").split("Z").join("").substr(0, 19);
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "联络站名称",
          width: 300,

          ellipsis: true,
          dataIndex: "liaisonStationName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "联系人",
          width: 180,

          ellipsis: true,
          dataIndex: "contactName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "电话",
          width: 180,

          ellipsis: true,
          dataIndex: "contactPhoneNumber",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "代表",
          width: 180,

          ellipsis: true,
          dataIndex: "inviteRangeDesc",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "备注",
          ellipsis: true,
          width: 180,

          dataIndex: "mark",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "录入日期",
          width: 180,

          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            if (text) {
              return text.replace("T", " ").split("Z").join("").substr(0, 19);
            } else {
              return "/";
            }
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 240,
          scopedSlots: { customRender: "operation" },
          // customRender: (text, record, index) => {
          //   let h = this.$createElement;
          //   return h("div", [
          //     h(
          //       "span",
          //       {
          //         attrs: {
          //           type: "text",
          //         },
          //         style: {
          //           cursor: "pointer",
          //           marginLeft: "14px",
          //           color: "#DB3046",
          //         },
          //         on: {
          //           click: () => {
          //             this.$router.push({
          //               path: "/community/intoTheCommunityActivityWrapper",
          //               query: { id: record.id, oper: 'view' },
          //             });
          //           },
          //         },
          //       },
          //       "查看"
          //     ),
          //     h(
          //       "span",
          //       {
          //         attrs: {
          //           type: "text",
          //         },
          //         style: {
          //           cursor: "pointer",
          //           marginLeft: "14px",
          //           color: "#DB3046",
          //         },
          //         on: {
          //           click: () => {
          //             this.$router.push({
          //               path: "/community/intoTheCommunityActivityWrapper",
          //               query: { id: record.id, oper: 'update' },
          //             });

          //           },
          //         },
          //       },
          //       "编辑"
          //     ), h(
          //       "span",
          //       {
          //         attrs: {
          //           type: "text",
          //         },
          //         style: {
          //           cursor: "pointer",
          //           marginLeft: "14px",
          //           color: "#DB3046",
          //         },
          //         on: {
          //           click: () => {
          //             this.handleDel(record);

          //           },
          //         },
          //       },
          //       "删除"
          //     ),
          //     h(
          //       "span",
          //       {
          //         attrs: {
          //           type: "text",
          //         },
          //         style: {
          //           cursor: "pointer",
          //           marginLeft: "14px",
          //           color: "#DB3046",
          //         },
          //         on: {
          //           click: () => {
          //             this.$router.push({
          //               path: "/community/intoTheCommunityActivityWrapper",
          //               query: { id: record.id, oper: 'audit', fromType: 'dbz' },
          //               // fromType指定在待办事项中跳转过去
          //             });
          //           },
          //         },
          //       },
          //       (record.currStateDm == '11' || record.currStateDm == '12') ? '送审' : '审核'
          //     ),
          //     // h(
          //     //   "span",
          //     //   {
          //     //     attrs: {
          //     //       type: "text",
          //     //     },
          //     //     style: {
          //     //       display:
          //     //         record.currentState.currStateDm == "41"
          //     //           ? "none"
          //     //           : "inline ",
          //     //       cursor: "pointer",
          //     //       marginLeft: "14px",
          //     //       color: "#DB3046",
          //     //     },
          //     //     on: {
          //     //       click: () => {
          //     //         this.handleDelete(record);
          //     //       },
          //     //     },
          //     //   },
          //     //   "删除"
          //     // ),
          //   ]);
          // },
        },
      ],
      isDb: 0,
      liaisonStations: [],
      searchDebounced: _.debounce(this.search, 500),
      isKeepAlive: false,
    };
  },
  computed: {
    isBatchAudit() {
      return ! checkPermission(['I_DBLZ_JDXZ','I_DBLZ_LLZ']);
      // const permissions =
      //   JSON.parse(sessionStorage.getItem("USERID"))?.userIdentityList?.map(
      //     (it) => it.code
      //   ) || [];
      // return !permissions.includes('I_DBLZ_JDXZ') && !permissions.includes('I_DBLZ_LLZ')
    }
  },
  created() {
    // // 判断是否是路由跳转过来
    // this.isDb = this.$route.query.isDb
    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));

    let queryForm = JSON.parse(localStorage.getItem("tongjifenzi"));
    if (queryForm) {
      this.queryForm = queryForm;
      this.pagination.current = this.queryForm.pageNum;
    }
    this.fetchData();
  },
  activated() {
    if (this.isKeepAlive) {
      this.fetchData();
    } else {
      this.isKeepAlive = true;
    }
  },
  beforeDestroy() {
    window.localStorage.setItem("tongjifenzi", JSON.stringify(this.queryForm));
  },
  methods: {
    handleEnter() {
      this.searchDebounced();
    },
    // 导出
    downloaData() {
      if (this.selectedRowKeys.length == 0)
        return this.$message.error("请选择数据");
      this.downloaDataLoading = true;
      ExportExcelValue({
        ids: this.selectedRowKeys.map((son) => son.id).join(),
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区活动数据.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloaDataLoading = false;
        }, 1000);
      });
    },
    // 删除
    async handleDel(recode) {
      await this.$baseConfirm("确认要删除吗?", null, () => {
        //
        instance_1({
          url: "/communityScheduleActivity/deleteById",
          method: "post",
          params: { id: recode.id },
        }).then((res) => {
          if (res.data.code == "0000") {
            this.$message.success("删除成功");
            this.fetchData();
          }
        });
      });
    },
    onDetail(record, oper) {
      this.$router.push({
        path: "/community/intoTheCommunityActivityDetail",
        query: {
          id: record.id,
          oper,
          title: this.$route.meta.title || "待办事项",
        },
      });
    },
    // 查看
    handleData(record) {
      this.onDetail(record, "view");
    },
    // 编辑
    handleEditData(record) {
      this.onDetail(record, "update");
    },
    //送审· 审核
    handleSwitMeeting(record) {
      this.onDetail(record, "audit");
    },
    // 点击行
    clickRow(event, record) {
      try {
        let state;
        let tableHeader = event.target.__vue__.column.key;
        // tableHeader ? state = true : state = false
        if (tableHeader == "activityName") {
          this.seedata(record);
        }
      } catch (error) {}
    },
    async fetchData() {
      this.loading = true;
      let res = await todo({ ...this.queryForm, isDb: this.isDb }, this.form);
      if (res.code == 200) {
        this.loading = false;
        this.list = res.rows;
        this.pagination.total = res.total;
      }
    },
    handleDelete(row) {
      console.log(row, "row");
      if (row.id) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          instance_1({
            method: "post",
            url: "dutyActive/delete",
            params: {
              id: row.id,
            },
          }).then((res) => {
            if (res.data.code == "0000") {
              this.$message.success("删除成功");
              this.fetchData();
            } else {
              this.$message.error("删除失败");
            }
          });
        });
      }
    },
    // 查看
    seedata(record) {
      this.$router.push({
        path: "/delegate/addRegister",
        query: {
          areaId: record.id,
          title: "查看",
          titleBox: "查看所有",
          seeTitle: true,
          queryForm: this.queryForm,
          form: this.form,
        },
      });
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    batchMessageSink() {
      if (!this.radioData) {
        this.$baseMessage("暂未选中任何信息", "error");
      } else {
        instance_1({
          url: "/flowAbleAuditMsgSetting/saveOrUpdate",
          method: "post",
          params: { msgType: this.radioData },
        }).then((res) => {
          if (res.data.code == "0000") {
            this.messageSink = false;
            this.$baseMessage("操作成功", "success");
          }
        });
      }
    },
    handleBatchAudit() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.error("请选择需要审核的数据");
        return;
      }
      this.batchAuditVisible = true;
    },
    batchAudit() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.error("请选择需要审核的数据");
        return;
      }
      let ids = [];
      this.selectedRowKeys.forEach((item) => {
        // debugger
        ids.push(item.activityId);
      });
      let comment = this.comment;
      this.loading = true;
      instance_1({
        url: "/communityScheduleActivity/batchAudit",
        method: "post",
        data: { ids: ids, comment: comment },
      }).then((res) => {
        if (res.data.code == "0000") {
          let msg = res.data.msg;
          this.batchAuditVisible = false;
          this.loading = false;
          this.$message.success(msg);
          this.fetchData();
        }
      });
    },
    search() {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    reset(value) {
      this.queryForm = value;

      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
  margin-bottom: 10px;
}

.table {
  padding: 15px;
}
</style>
