import { instance_yajy } from "@/api/axiosRq";

// 承办单位建议办理状态清单统计分析
// queryForm 请求搜索数据
export function getListData(queryForm) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/cbwdblztqd`,
    method: "post",
    params: queryForm,
  });
}
// 建议分数查询 获取列表
export function getScoreListPage(form) {
  return instance_yajy({
    url: `/api/v1/appraise/form/getScoreListPage`,
    method: "post",
    data: form,
  });
}
// 导出excel
export function exportCbwdblztqd(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/exportCbwdblztqd`,
    method: "post",
    responseType: "blob",
    params: form,
  });
}
