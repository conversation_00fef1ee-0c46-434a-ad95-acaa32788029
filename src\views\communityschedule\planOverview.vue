<template>
  <div class="table_all">
    <div class="table_title">市人大代表进联络站</div>
    <div class="table_arrange">接待群众活动一周安排</div>
    <div class="table_time">(7月1日-7月5日)</div>
    <div class="table_info">
      <a-table rowClassName="table_list" bordered style="width: 800px;" :columns="columns" :data-source="data" :pagination="false">
        <a slot="name" slot-scope="text">{{ text }}</a>
      </a-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "newInfo",
  components: {
  },
  data() {
    const columns = [
      {
        title: '日期',
        dataIndex: 'date',
        customRender: (value, row, index) => {
          const positionHistory = value.split('br').map(item => <li>{item}</li>);
          const obj = {
            children: (  
              <div>  
                {positionHistory}  
              </div>  
            ),
            attrs: {},
          };
          if (index === 2) {
            obj.attrs.rowSpan = 6;
          }
          if (index === 3 || index === 4 || index === 5 || index === 6 || index === 7 ) {
            obj.attrs.rowSpan = 0;
          }
          return obj;
        },
        align: 'center'
      },
      {
        title: '时间',
        dataIndex: 'time',
        width: 150,
        align: 'center'
      },
      {
        title: '代表联络站',
        dataIndex: 'liaisonStation',
        // align: 'center'
      },
      {
        title: '接待群众的市人大代表',
        dataIndex: 'peopleCongress',
        width: 110,
        align: 'center',
        customRender: (value, row, index) => {
          const positionHistory = value.split('br').map(item => <li>{item}</li>);
          const obj = {
            children: (  
              <div>  
                {positionHistory}  
              </div>  
            ),
            attrs: {},
          };
          return obj;
        }
      }
    ];
    const data = [
      {
        key: '1',
        date: '7月1日br（星期一）',
        time: '上午9:30-11:00',
        liaisonStation: '白云区钟落潭镇五龙岗片区人大代表联络站(地址:白云区钟落潭镇广从七路768号)',
        peopleCongress: '王靖夫',
      },
      {
        key: '2',
        date: '7月2日br(星期二)',
        time: '上午9:30-11:00',
        liaisonStation: '从化区江埔街道人大代表中心联络站(地址:从化区江埔街道七星路14号)',
        peopleCongress: '何蔚云',
      },
      {
        key: '3',
        date: '77月3日br(星期三)',
        time: '上午9:30-10:30',
        liaisonStation: '花都区新雅街道雅瑶片区人大代表联络站(地址:花都区新雅街道邝家北街10号邝家庄村村委会)',
        peopleCongress: '林平',
      },
      {
        key: '4',
        // date: '7月1日（星期一）',
        time: '上午10:00-12:00',
        liaisonStation: '天河区棠下街道华景片区人大代表联络站(地址:天河区棠下街道华景北路279号棠下街党群服务中心)',
        peopleCongress: '卓峻',
      },
      {
        key: '5',
        // date: '7月1日（星期一）',
        time: '上午10:00-12:00',
        liaisonStation: '天河区棠下街道枫叶片区人大代表联络站(地址:天河区棠下街道华景东路210号枫叶社区居委会)',
        peopleCongress: '吴翔br赵海舟',
      },
      {
        key: '6',
        // date: '7月1日（星期一）',
        time: '下午2:30-3:30',
        liaisonStation: '花都区新雅街道南部片区人大代表联络站(地址:花都区新雅街道石塘村四社四巷1号石塘村村委会)',
        peopleCongress: '林纯',
      },
      {
        key: '7',
        // date: '7月1日（星期一）',
        time: '下午3:00-4:30',
        liaisonStation: '天河区元岗街道南兴片区人大代表联络站(地址:天河区元岗街道元岗路311号)',
        peopleCongress: '王志非',
      },
      {
        key: '8',
        // date: '7月1日（星期一）',
        time: '下午3:00-4:30',
        liaisonStation: '黄埔区云埔街道人大代表中心联络站(地址:黄埔区云埔街道开创大道140号)',
        peopleCongress: '张宾',
      },
      {
        key: '9',
        date: '7月4日(星期四)',
        time: '下午2:30-4:00',
        liaisonStation: '天河区沙河街道人大代表中心联络站(地址:天河区沙河街道龙岗路36号)',
        peopleCongress: '程钢',
      },
    ];
    return {
      data,
      columns,
    }
  },
  methods: {
    
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-table-thead > tr > th {
  background-color: #f7ccad!important;
  text-align: center;
}
.table_all {
  padding: 0 50px;
  text-align: center;
  color: #1c1c1c;
  .table_title,
  .table_arrange {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 6px;
  }
  .table_time {
    font-size: 22px;
    color: #898989;
    margin-bottom: 30px;
  }
  .table_info {
    display: flex;
    justify-content: center;
    margin-bottom: 50px;
  }
}
</style>