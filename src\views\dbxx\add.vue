<template>
  <div style="display: flex;">
    <div style="flex: 8;">
      <div v-if="currentTab === 4"><Add /></div>
    </div>
    <!-- 时间线 -->
    <!-- <TimeLine :lineList="lineList" :tab.sync="currentTab" /> -->
    <TimeLine :lineList="lineList" :tab.sync="currentTab" :disabled="true" /> <!-- 暂时不要点击切换功能 -->
  </div>
</template>

<script>
import TimeLine from '@/components/TimeLine/index';
import Add from './addInfo.vue';
export default {
  name: "newInfo",
  components: {
    TimeLine,
    Add
  },
  data() {
    return {
      currentTab: 0,
      lineList: [
        {
          content: '预备人选基本情况申报',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
          // status: 'finished',
        }, {
          content: '预备人选信息登记',
          timestamp: '2018-04-11',
          status: 'unPass',
          type: '审核通过',
          color: '#0bbd87',
          remark: '备注：审核通过'
        }, {
          content: '候选人确定',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '候选人分配',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '代表信息补录',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '同步到代表信息库',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }
      ],
      // tab: 4,
    }
  },
  created() {
    let { tabIndex } = this.$route.query;
    this.currentTab = Number(tabIndex);
    console.log(this.currentTab,'this.currentTabthis.currentTab');
  },
  methods: {
    newIndex(index) {
      this.currentTab = index
      console.log(index,'当前点击index');
    },
    newData(data) {
      this.titleDetail =  data.content
    }
  }
}
</script>

<style>

</style>