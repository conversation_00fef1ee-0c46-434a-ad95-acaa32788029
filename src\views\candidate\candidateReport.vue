<template>
  <div>
    <a-row>
      <a-col :md="24"
             :sm="24">
        <a-card title="候选人相关报表">
          <a-row :gutter="[16, 16]"
                 v-for="item in meetingList"
                 :key="item.id">
            <a-col :md="4"
                   v-for="col in item.col"
                   :key="col.id"
                   @click="skiUrl(col)">
              <a-card hoverable
                      class="a-card-flex">
                <img slot="cover"
                     :src="col.url"
                     alt=""
                     style="width:50px" />
                <a-card-meta :title="col.title"></a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
export default {
  name: "WorkIndex",
  components: {},
  data () {
    return {
      TBloading: false,
      meetingList: [
        {
          id: 1,
          col: [
            {
              id: 1,
              title: "候选人申报表",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ready/candidate/declare",
            },
            {
              id: 2,
              title: "候选人登记表",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ready/candidate/check",
            },
            {
              id: 3,
              title: "候选人名册",
              color: "#be403d",
              url: require("@/assets/images/indexPage/meeting.png"),
              path: "/ready/candidate/muster",
            },
          ],
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "候选人管理");
    this.$store.dispatch("navigation/breadcrumb2", "统计报表");
  },
  methods: {
    skiUrl (col) {
      this.$router.push({
        path: col.path,
      });
    },
  },
};
</script>
