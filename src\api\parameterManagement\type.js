// import instance_1 from '@/utils/instance_1'
import { instance_1 } from "@/api/axiosRq";

// 查询字典类型列表 
export function listType(query) {
  return instance_1({
    url: '/system/dict/type/list',
    method: 'get',
    params: query
  })
}

// 查询字典类型详细
export function getType(dictId) {
  return instance_1({
    url: '/system/dict/data/' + dictId,
    method: 'get'
  })
}

// 新增字典类型
export function addType(data) {
  return instance_1({
    url: '/system/dict/data',
    method: 'post',
    data: data
  })
}

// 修改字典类型
export function updateType(data) {
  return instance_1({
    url: '/system/dict/data',
    method: 'put',
    data: data
  })
}

// 删除字典类型
export function delType(dictId) {
  return instance_1({
    url: '/system/dict/data/' + dictId,
    method: 'delete'
  })
}

// 刷新字典缓存
export function refreshCache() {
  return instance_1({
    url: '/system/dict/type/refreshCache',
    method: 'delete'
  })
}

// 导出字典类型 (新无)
export function exportType(query) {
  return instance_1({
    url: '/system/dict/type/export',
    method: 'get',
    params: query
  })
}

// 获取字典选择框列表 
export function optionselect() {
  return instance_1({
    url: '/system/dict/type/optionselect',
    method: 'get'
  })
}