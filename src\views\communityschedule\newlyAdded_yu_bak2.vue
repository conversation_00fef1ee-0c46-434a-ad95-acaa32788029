<template>
  <!-- 社情民意的详情、修改、添加 -->
  <div class="table-container">
    <div>人大代表联系群众登记表</div>
    <div>
        <a-form-model         
          ref="addForm"
          :model="addForm"
          :rules="rules">
        <table style="width:100%;">
          <tr>
              <td class="title-label" colspan="1">
                  代表姓名
              </td>
              <td class="body-wapper" colspan="1">
                  aa
              </td>
              <td class="title-label" colspan="1">
                  联系电话
              </td>
              <td class="body-wapper" colspan="1">
                aa
              </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
                记录人姓名
            </td>
            <td style="width:25%" colspan="1">
              <a-form-model-item prop="recorder" class="form-item-wrapper">
                <a-input
                  :disabled="disabled"
                  v-model="addForm.recorder"
                  autocomplete="off"
                  allow-clear
                ></a-input>
              </a-form-model-item>
            </td>
            <td class="title-label" colspan="1">
              联系电话
            </td>
            <td style="width:25%" colspan="1">
              <a-form-model-item prop="recorderMobile" class="form-item-wrapper">
              <a-input
                :disabled="disabled"
                v-model="addForm.recorderMobile"
                autocomplete="off"
                allow-clear
              ></a-input>
              </a-form-model-item>
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
                所联系的群众姓名
            </td>
            <td style="width:25%" colspan="1">
              <a-form-model-item prop="voterName" class="form-item-wrapper">
                <a-input
                  :disabled="disabled"
                  v-model="addForm.voterName"
                  autocomplete="off"
                  allow-clear
                ></a-input>
              </a-form-model-item>
            </td>
            <td class="title-label" colspan="1">
                联系电话
            </td>
            <td style="width:25%" colspan="1">
              <a-form-model-item prop="voterMobile" class="form-item-wrapper">
                <a-input
                  :disabled="disabled"
                  v-model="addForm.voterMobile"
                  autocomplete="off"
                  allow-clear
                ></a-input>
              </a-form-model-item>
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
                群众身份证号码
            </td>
            <td style="width:25%" colspan="1">
            <a-form-model-item prop="voterCredentialNo" class="form-item-wrapper">
              <a-input
                :disabled="disabled"
                v-model="addForm.voterCredentialNo"
                autocomplete="off"
                allow-clear
              ></a-input>
            </a-form-model-item>
            </td>
            <td class="title-label" colspan="1">
                住址
            </td>
            <td style="width:25%" colspan="1">
              <a-form-model-item prop="voterAddress" class="form-item-wrapper">
                <a-input
                  :disabled="disabled"
                  v-model="addForm.voterAddress"
                  autocomplete="off"
                  allow-clear
                ></a-input>
              </a-form-model-item>
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
               群众所反映问题、意见建议的详细情况
            </td>
            <td style="width:75%" colspan="3">
              <a-form-model-item class="form-item-wrapper">
                <a-textarea
                  :disabled="disabled"
                  rows="3"
                  allow-clear
                  v-model="addForm.content"
                ></a-textarea>
              </a-form-model-item>
            </td>
          </tr>
          <tr>
            <td class="title-label" rowspan="2"  colspan="1">
               代表意见
            </td>
            <td colspan="3">
              <div style="float: left; width:30%;line-height:30px;display: flex;align-items: center;">建议交办的办理部门：</div>
              <a-form-model-item style="float:right; width:70%">
                <a-input
                  :disabled="disabled"
                  v-model="addForm.adviceDept"
                  autocomplete="off"
                  allow-clear
                ></a-input>
              </a-form-model-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <a-form-model-item
                prop="committeeOpinion"
                :label-col="{ span: 1 }"
                :wrapper-col="{ span: 24 }"
              >
                <a-textarea
                  :disabled="disabled"
                  rows="3"
                  allow-clear
                  v-model="addForm.committeeOpinion"
                ></a-textarea>
              </a-form-model-item>
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
               镇人大、人大街道工委意见
            </td>
            <td style="width:75%" colspan="3">
              <a-form-model-item
                prop="committeeStreetOpinion"
                :label-col="{ span: 2 }"
                :wrapper-col="{ span: 24 }"
              >
                <a-textarea
                  :disabled="disabled"
                  rows="4"
                  allow-clear
                  v-model="addForm.committeeStreetOpinion"
                ></a-textarea>
              </a-form-model-item>
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
              镇党委、街道党工委意见
            </td>
            <td style="width:75%" colspan="3">
              <a-form-model-item
                prop="partyStreetOpinion"
                :label-col="{ span: 2 }"
                :wrapper-col="{ span: 24 }"
              >
                <a-textarea
                  :disabled="disabled"
                  rows="1"
                  allow-clear
                  v-model="addForm.partyStreetOpinion"
                ></a-textarea>
              </a-form-model-item>
            </td>
          </tr>
          <tr>
            <td colspan="4" style="text-align: center; font-weight: bold;">
              <span>群众反映问题、意见建议的处理情况</span>
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1" rowspan="5">
              交办转办情况
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <a-radio-group v-model="addForm.transType" style="width: 100%">
                <a-row span="24">
                  <a-row>
                    <a-col :span="6"
                      ><a-radio :disabled="disabled" value="1"
                        >口头解释答复</a-radio
                      ></a-col
                    >
                    <a-col :span="6"
                      ><a-radio :disabled="disabled" value="2"
                        >转镇、街有关科室处理</a-radio
                      ></a-col
                    >
                    <a-col :span="6"
                      ><a-radio :disabled="disabled" value="3"
                        >转请区政府有关职能部门处理</a-radio
                      ></a-col
                    >
                    <a-col :span="6"
                      ><a-radio :disabled="disabled" value="4"
                        >商请区代表提出建议</a-radio
                      ></a-col
                    >
                  </a-row>
                  <a-row>
                    <a-col :span="6"
                      ><a-radio :disabled="disabled" value="5"
                        >组织代表开展专题视察调研</a-radio
                      ></a-col
                    >
                    <a-col :span="6"
                      ><a-radio :disabled="disabled" value="6"
                        >商请上级人大代表向相应层级人大反映</a-radio
                      ></a-col
                    >
                    <a-col :span="6"
                      ><a-radio :disabled="disabled" value="7"
                        >商请上级人大代表提出建议</a-radio
                      ></a-col
                    >
                    <a-col :span="6"
                      ><a-radio :disabled="disabled" value="8">其他</a-radio></a-col
                    >
                  </a-row>
                </a-row>
            </a-radio-group>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <a-row>
                <a-col span="12">
                  <a-form-model-item label="转交时间" prop="transTime">
                    <a-date-picker
                      allow-clear
                      :disabled="disabled"
                      v-model="addForm.transTime"
                      placeholder="请选择反馈时间"
                      value-format="YYYY-MM-DD"
                    ></a-date-picker>
                  </a-form-model-item>
                </a-col>
                <a-col span="12">
                  <a-form-model-item label="接收单位" prop="receiveDept">
                    <a-input
                      :disabled="disabled"
                      v-model="addForm.receiveDept"
                      autocomplete="off"
                      allow-clear
                    ></a-input>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <a-col span="6">
                <a-form-model-item label="交转方式" prop="transWay">
                  <a-input
                    :disabled="disabled"
                    v-model="addForm.transWay"
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col span="6">
                <a-form-model-item label="接收时间" prop="receiveTime">
                  <a-date-picker
                    allow-clear
                    :disabled="disabled"
                    v-model="addForm.receiveTime"
                    placeholder="请选择反馈时间"
                    value-format="YYYY-MM-DD"
                  ></a-date-picker>
                </a-form-model-item>
              </a-col>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <a-col span="6">
                <a-form-model-item label="接收人员" prop="receiveUserName">
                  <a-input
                    :disabled="disabled"
                    v-model="addForm.receiveUserName"
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col span="6">
                <a-form-model-item label="联系电话" prop="receiveUserMobile">
                  <a-input
                    :disabled="disabled"
                    v-model="addForm.receiveUserMobile"
                    autocomplete="off"
                    allow-clear
                  ></a-input>
                </a-form-model-item>
              </a-col>
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
              办理期限
            </td>
            <td style="width:75%" colspan="3">
              该事项建议于
                <a-form-model-item prop="dueDate">
                  <a-date-picker
                    allow-clear
                    :disabled="disabled"
                    v-model="addForm.dueDate"
                    value-format="YYYY-MM-DD"
                  ></a-date-picker>
                </a-form-model-item>
              前办结，办理结果由承办单位答复镇人大、人大街道工委，并由镇人大、人大街道工委通报联络站反馈接待代表和反映情况的群众。
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
              跟踪督办情况
            </td>
            <td style="width:75%" colspan="3">
              <a-form-model-item label="跟踪督办情况" prop="situation">
                <a-textarea
                  rows="2"
                  :disabled="disabled"
                  allow-clear
                  v-model="addForm.situation"
                ></a-textarea>
              </a-form-model-item>
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
              办理详情
            </td>
            <td style="width:75%" colspan="3">
              <a-form-model-item
                label="办理详情"
                prop="handlingContent"
                :wrapper-col="{ span: 14 }"
              >
                <a-textarea
                  rows="2"
                  allow-clear
                  :disabled="disabled"
                  v-model="addForm.handlingContent"
                ></a-textarea>
              </a-form-model-item>
            </td>
          </tr>
          <tr>
            <td class="title-label" rowspan="2"  colspan="1">
              答复反馈情况
            </td>
            <td colspan="3">
              <a-row>
                <a-col span="5">
                  <a-form-model-item label="答复情况" prop="replyStreetPartyDate">
                    <a-date-picker
                      allow-clear
                      :disabled="disabled"
                      v-model="addForm.replyStreetPartyDate"
                      placeholder="请选择答复时间"
                      value-format="YYYY-MM-DD"
                    >
                    </a-date-picker>
                  </a-form-model-item>
                </a-col>
                <a-col span="5" style="margin-top: 5px">
                  <a-form-model-item
                    prop="replyStreetParty"
                    :label-col="{ span: 1 }"
                    :wrapper-col="{ span: 24 }"
                  >
                    <a-textarea
                      rows="1"
                      allow-clear
                      :disabled="disabled"
                      v-model="addForm.replyStreetParty"
                    ></a-textarea>
                  </a-form-model-item>
                </a-col>
                <a-col :span="5">
                  <p style="line-height: 40px">
                    已将该事项办理情况答复镇人大、人大街道工委。
                  </p>
                </a-col>
              </a-row>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <a-row>
                <a-col span="5">
                  <a-form-model-item label="反馈情况" prop="replyPeopleDate">
                    <!-- :disabled="disabled" :disabledDate="disabledDate" -->
                    <a-date-picker
                      allow-clear
                      :disabled="disabled"
                      v-model="addForm.replyPeopleDate"
                      placeholder="请选择反馈时间"
                      value-format="YYYY-MM-DD"
                    >
                    </a-date-picker>
                  </a-form-model-item>
                </a-col>
                <a-col span="5" style="margin-top: 5px">
                  <a-form-model-item
                    prop="replyPeople"
                    :label-col="{ span: 1 }"
                    :wrapper-col="{ span: 24 }"
                  >
                    <a-textarea
                      rows="1"
                      allow-clear
                      :disabled="disabled"
                      v-model="addForm.replyPeople"
                    ></a-textarea>
                  </a-form-model-item>
                </a-col>
                <a-col span="5">
                  <p style="line-height: 40px">已将该事项办理情况反馈代表和群众。</p>
                </a-col>
              </a-row>
            </td>
          </tr>
          <tr>
            <td class="title-label" rowspan="2"  colspan="1">
              代表评价
            </td>
            <td colspan="3">
              对办理结果
                <a-col span="5">
                  <a-form-model-item>
                    <a-select
                      v-model="addForm.handlingInfo.committeeGradeForResult"
                      allow-clear
                      style="width: 175px"
                      :disabled="disabled"
                    >
                      <a-select-option
                        v-for="item in bljgclList"
                        :key="item.id"
                        :value="item.id"
                        >{{ item.bljgname }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col span="5" style="margin-top: 5px">
                  <a-form-model-item
                    prop="yy"
                    :label-col="{ span: 1 }"
                    :wrapper-col="{ span: 24 }"
                  >
                    <a-textarea
                      rows="1"
                      :disabled="disabled"
                      allow-clear
                      v-model="addForm.handlingInfo.committeeCommentForResult"
                    ></a-textarea>
                  </a-form-model-item>
                </a-col>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              对办理部门
                <a-col span="5">
                  <a-form-model-item label="对办理部门">
                    <a-select
                      v-model="addForm.handlingInfo.committeeGradeForDept"
                      allow-clear
                      style="width: 175px"
                      :disabled="disabled"
                    >
                      <a-select-option
                        v-for="item in bljgclList"
                        :key="item.id"
                        :value="item.id"
                        >{{ item.bljgname }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col span="5" style="margin-top: 5px">
                  <a-form-model-item
                    prop="yy"
                    :label-col="{ span: 1 }"
                    :wrapper-col="{ span: 24 }"
                  >
                    <a-textarea
                      rows="1"
                      allow-clear
                      :disabled="disabled"
                      v-model="addForm.handlingInfo.committeeCommentForDept"
                    ></a-textarea>
                  </a-form-model-item>
                </a-col>
            </td>
          </tr>
          <tr>
            <td class="title-label" rowspan="2"  colspan="1">
              群众评价
            </td>
            <td colspan="3">
                aa
            </td>
          </tr>
          <tr>
            <td colspan="3">
                aa
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
               再次办理或解释情况（代表或群众不满意）
            </td>
            <td style="width:75%" colspan="3">
                aa
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
              代表再次评价
            </td>
            <td style="width:75%" colspan="3">
                aa
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
               群众再次评价
            </td>
            <td style="width:75%" colspan="3">
                aa
            </td>
          </tr>
          <tr>
            <td class="title-label" colspan="1">
               办结情况
            </td>
            <td style="width:75%" colspan="3">
                aa
            </td>
          </tr>
        </table>
        </a-form-model>
    </div>
  </div>
</template>
<script>
import Vue from "vue";
import moment from "moment";
import { myPagination } from "@/mixins/pagination.js";
import submitForCensorship from "@/views/common/submitForCensorship";
import { getcandidateApplicationtsaveApi } from "@/api/representativeElection/candidateApi.js";
import {
  OpinionControllerSave,
  OpinionControllerUpdata,
  getMembers,
} from "@/api/communityschedule";
import { log } from "@antv/g2plot/lib/utils";
export default {
  components: { submitForCensorship },
  mixins: [myPagination],
  props: {
    neWid: {
      type: String,
    },
  },

  data() {
    // 根据证件号校验生日
    var validateDateOfBirth = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请选择出生日期"));
      } else {
        if (this.addForm.birthday) {
          let year = this.addForm.birthday.slice(6, 10);
          let month = this.addForm.birthday.slice(10, 12);
          let day = this.addForm.birthday.slice(12, 14);
          let temp_date = new Date(
            year,
            parseFloat(month) - 1,
            parseFloat(day)
          );
          if (temp_date) {
            callback();
          } else {
            callback(new Error("请选择正确的出生日期"));
          }
        } else {
          this.$message.error("出生日不能大于当天");
        }
      }
    };

    //校验手机号
    var validatePhone = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入手机号"));
      } else {
        let reg = /^([1][3,4,5,6,7,8,9])\d{9}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的手机号"));
      }
    };
    //校验证件号
    var getCardTypeNumber = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入身份证号码"));
      } else {
        const reg =
          /^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确格式的身份证号码"));
      }
    };
    return {
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        {
          title: "代表姓名",
          align: "center",
          ellipsis: true,
          dataIndex: "name",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联系方式",
          align: "center",
          ellipsis: true,
          dataIndex: "mobile",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
      ],

      selectedRows: [],
      dataSource: [],
      dbName_list: "", //代表姓名
      DialogVisible: false,
      FormType: "",
      isCompletedType: false,
      disabled: false,
      state: "",
      checked: true,
      addForm: {
        adviceDept: null,
        committeeOpinion: null,
        committeeOpinionDate: null,
        committeeStreetOpinion: null,
        committeeStreetOpinionDate: null,
        communityScheduleId: null,
        completeContent: null,
        content: null,
        createTime: null,
        creator: null,
        deleteTime: null,
        deleter: null,
        dueDate: null,
        handlingContent: null,
        modifier: null,
        partyStreetOpinion: null,
        partyStreetOpinionDate: null,
        receiveDept: null,
        receiveTime: null,
        receiveUserMobile: null,
        receiveUserName: null,
        recordTime: null,
        recorder: null,
        recorderMobile: null,
        replyPeople: null,
        replyPeopleDate: null,
        replyStreetParty: null,
        replyStreetPartyDate: null,
        secondHandlingContent: null,
        isCompleted: null,
        situation: null,
        transTime: null,
        transWay: null,
        updateTime: null,
        voterAddress: null,
        voterAge: null,
        voterCredentialNo: null,
        voterGender: null,
        voterMobile: null,
        voterName: null,
        members: [],
        //
        handlingInfo: {
          committeeCommentForDept: null,
          committeeGradeForDept: null,
          committeeCommentForResult: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        },
        secondHandlingInfo: {
          committeeCommentForDept: null,
          committeeCommentForResult: null,
          committeeGradeForDept: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        },
        transType: null,
      },
      bljgclList: [
        { id: 1, bljgname: "满意" },
        { id: 2, bljgname: "基本满意" },
        { id: 3, bljgname: "不满意(原因)" },
      ],
      bjqkList: [
        { id: 1, bjqkname: "已办结" },
        { id: 0, bjqkname: "未办结" },
      ],
      checkBoxArr: [
        { id: "1", value: "是否拥有外国国籍" },
        { id: "2", value: "是否拥有国(境)外永久居留权" },
        { id: "3", value: "是否拥有国(境)外长期居留许可" },
        { id: "4", value: "是否有直系亲属担任同级人大代表" },
        { id: "5", value: "配偶子女是否移居国（境）外" },
        { id: "6", value: "是否担任无隶属关系行政区域的人大代表/政协委员" },
        { id: "7", value: "是否是香港或澳门市民" },
        { id: "8", value: "是否担任企业高管" },
        {
          id: "9",
          value:
            "是否直接或者间接接受境外机构、组织、个人提供的与选举有关的任何形式的资助",
        },
      ],

      periods: [
        { id: 16, name: "十五届六次" },
        { id: 15, name: "十五届一次" },
        { id: 1, name: "十五届二次" },
        { id: 2, name: "十五届三次" },
        { id: 3, name: "十五届四次" },
        { id: 4, name: "十五届五次" },
      ],
      rules: {
        committeeName: [
          { required: true, message: "请输入代表姓名", trigger: "blur" },
        ],
        recorder: [
          { required: true, message: "请输入记录人姓名", trigger: "blur" },
        ],
        voterName: [
          {
            required: true,
            message: "请输入所联系的群众姓名",
            trigger: "blur",
          },
        ],
        committeeMobile: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],
        recorderMobile: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],
        voterMobile: [
          { required: true, trigger: "blur", validator: validatePhone },
        ],

        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
        voterAddress: [
          { required: true, message: "请填写住址", trigger: "blur" },
        ],

        birthday: [
          { required: true, trigger: "change", validator: validateDateOfBirth },
        ],
        voterCredentialNo: [
          { required: true, trigger: "blur", validator: getCardTypeNumber },
        ],
      },
      disabled: false,
    };
  },

  created() {
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "社情民意列表详情");
    let { data, to_type, communityScheduleId } = this.$route.query;
    this.addForm.communityScheduleId = communityScheduleId;
    this.queryForm.id = communityScheduleId;
    if (to_type) {
      // 修改
      this.disabled = false;
    } else {
      // 详情
      this.disabled = true;
    }
    if (data) {
      this.addForm = data;
      this.FormType = "update";
      let name_list = [];
      this.dbName_list = "";
      this.addForm.members.forEach((item, index) => {
        name_list.push(item.userName);
      });
      this.dbName_list = name_list.toString();
      console.log(data.transType);
      this.addForm.transType =data.transType? data.transType.toString():'';

      if(data.handlingInfo){
        this.addForm.handlingInfo=data.handlingInfo;
      }else{
          this.addForm.handlingInfo= {
          committeeCommentForDept: null,
          committeeGradeForDept: null,
          committeeCommentForResult: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        };
      }
      if(data.secondHandlingInfo){
         this.addForm.secondHandlingInfo=data.secondHandlingInfo;
      }else{
          this.addForm.secondHandlingInfo= {
          committeeCommentForDept: null,
          committeeCommentForResult: null,
          committeeGradeForDept: null,
          committeeGradeForResult: null,
          peopleCommentForDept: null,
          peopleCommentForResult: null,
          peopleGradeForDept: null,
          peopleGradeForResult: null,
        };
      }
      if (data.isCompleted == 0) {
        this.isCompletedType = true;
      }
      //; 查看详情
    } else {
      this.FormType = "add";
      this.disabled = false;
    }
  },

  methods: {
    confirm() {
      this.dbName_list = "";
      this.addForm.members = [];
      this.addForm.members = this.selectedRows;
      let name_list = [];
      this.selectedRows.forEach((item, index) => {
        name_list.push(item.name);
        this.addForm.members[index].userName = item.name;
      });
      this.dbName_list = name_list.toString();
      this.DialogVisible = false;
    },
    // 代表列表名单
    getdbList() {
      getMembers(this.queryForm).then((res) => {
        if (res.code == "0000") {
          this.dataSource = res.data;
        }
      });
    },
    onSelectChange() {},
    opendbInformation() {
      this.getdbList();
      this.DialogVisible = true;
    },
    //
    changerCompletedType() {
      console.log(this.addForm.isCompleted);
      if (this.addForm.isCompleted == 0) {
        this.isCompletedType = true;
      } else {
        this.isCompletedType = false;
      }
    },
    //不能选择今天以后的日期
    disabledDate(current) {
      return current && current > moment().endOf("day");
    },

    change(item, e) {
      console.log(item);
      if (item.target.checked) {
        this.addForm[item.target.value] = true;
      } else {
        this.addForm[item.target.value] = false;
      }
    },
    // 获取数据
    fetchData() {},
    // 修改
    onUpdate() {
      this.$refs["addForm"].validate(async (valid) => {
        if (valid) {
          let data = this.addForm;

          data.transType = Number(data.transType);
          OpinionControllerUpdata(data).then((res) => {
            if (res.code == "0000") {
              this.$message.success("修改成功");
              this.$router.go(-1);
            }
          });
        } else {
          this.$message.error("校验失败");
        }
      });
    },
    //保存
    save() {
      this.$refs["addForm"].validate(async (valid) => {
        if (valid) {
          let data = this.addForm;
          data.transType = Number(data.transType);
          OpinionControllerSave(data).then((res) => {
            // console.log(res.code);
            if (res.code == "0000") {
              this.$message.success("保存成功");
              this.$router.go(-1);
            }
          });
        } else {
          this.$message.error("校验失败");
        }
      });

      //打开送审
    },
    // 复选框
    onChanger(even) {
      console.log(even);
      console.log(this.addForm.transType);
    },
    // 关闭
    close(e) {
      this.$router.go(-1);
    },
    quxiao() {
      this.$router.go(-1);
    },
    //监听父组件传过来的值
    // watch: {
    //   neWid: {
    //     immediate: true,    // 这句重要  立即执行handler里面的方法
    //     handler(val) {
    //     },
    //   },
    // },
  },
};
</script>
<style lang="scss" scoped>
.tit{
 margin-bottom: 15px; color: #000; 
//  font-size: 16px;
 @include add-size($font_size_16);
}
.formBox {
  padding: 20px;
}

.shangchuang {
  margin-left: 140px;
}

.reds {
  color: red;
  margin: 10px;
}
.jic {
  position: relative;
}
.jicson {
  color: red;
  // font-size: 2px;
   @include add-size($font_size_16);
  position: absolute;
  left: -65px;
  z-index: 999;
}

// .xx-title {
//     width: 30px;
//     padding-left: 5px;
//     padding-right: 5px;
//     word-wrap: break-word;
// }
.table-container {
  width: 80%;
}
.table-container table td {
    border: 1px solid #000;
}

.label {
    // margin-top: 6px;
}

.title-label {
  width: 25%;
  text-align: center;
  height:50px
}

.body-wapper {
  width: 25%;
  text-align: center;
  height:50px;
  margin: 5px;
}

.ant-form-item {
  margin-bottom: 0px !important;
}

.form-item-wrapper {
  padding: 10px;
}

</style>
