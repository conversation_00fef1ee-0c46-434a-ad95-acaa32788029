<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <SearchForm :value="queryForm" @onReset="reset" @onSearch="fetchData">
          <template v-slot:topSearch>
            <AdminStreetStationCascade v-model="queryForm" allow-clear />
          </template>
          <template v-slot:moreSearch>
            <DhJcCascade v-model="queryForm" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>

            <FormInput
              v-model="queryForm.userName"
              :label="'代表姓名'"
              allow-clear
              @enter="fetchData"
            />

            <FormPicker
              v-model="queryForm.year"
              label="年度"
              type="year"
              allow-clear
            />

            <FormSelect
              v-model="queryForm.statisticType"
              :label="'统计方式'"
              :options="statisticTypeList"
            />
          </template>
        </SearchForm>

        <a-row style="margin: 5px 0px 10px 8px">
          <a-col span="6">
            <!-- <a-button style="margin-left:20px;" @click="$router.go(-1)"> 返回</a-button> -->
            <a-button
              type="primary"
              style="margin-left: 10px"
              @click="exportExcel"
              >导出</a-button
            >
          </a-col>
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <a-table
            ref="table"
            :bordered="false"
            class="directorySet-table"
            size="small"
            :columns="columns"
            :pagination="pagination"
            :data-source="list"
            :row-key="
              (record, index) => {
                return index;
              }
            "
            :scroll="{ x: 300, y: 0 }"
          ></a-table>
          <!--  :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"  -->
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import {
  stationActivityMemberList,
  stationActivityMemberExprot,
} from "@/api/communityschedule";
import SearchForm from "@/components/SearchForm/index";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import FormSelect from "@/components/FormSelect/index.vue";
import FormPicker from "@/components/FormPicker/index.vue";
import { DBLZ_DBJSQ } from "@/utils/enum/levelRoleMainIdentifyEnum";

export default {
  components: {
    FormPicker,
    FormSelect,
    FormInput,
    AdminStreetStationCascade,
    DhJcCascade,
    SearchForm,
  },
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBJSQ,
      statisticTypeList: [
        { name: "按活动次数统计", id: "1" },
        { name: "按区统计", id: "2" },
        { name: "按街道统计", id: "3" },
        { name: "按联络站统计", id: "4" },
      ],
      list: [],
      listLoading: false,
      queryForm: {
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        dhDm: undefined,
        jcDm: undefined,
        dbtId: undefined,
        userName: undefined,
        year: "",
        statisticType: "1",
      },
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      // 列表
      columns: [],
      columnsGroup: {
        1: [
          {
            title: "序号",
            align: "center",
            width: 100,
            ellipsis: true,
            customRender: (text, record, index) => {
              return index + 1 || "/";
            },
          },
          {
            title: "代表类型",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "sfmc",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "参加活动人员名单（参加次数）",
            align: "center",
            ellipsis: true,
            dataIndex: "userNames",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
        ],
        2: [
          {
            title: "序号",
            align: "center",
            width: 100,
            ellipsis: true,
            customRender: (text, record, index) => {
              return index + 1 || "/";
            },
          },
          {
            title: "代表类型",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "sfmc",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "行政区划",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "administrativeAreaName",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "参加活动人员名单（参加次数）",
            align: "center",
            ellipsis: true,
            dataIndex: "userNames",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
        ],
        3: [
          {
            title: "序号",
            align: "center",
            width: 100,
            ellipsis: true,
            customRender: (text, record, index) => {
              return index + 1 || "/";
            },
          },
          {
            title: "代表类型",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "sfmc",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "行政区划",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "administrativeAreaName",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "街道名称",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "streetTownName",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "参加活动人员名单（参加次数）",
            align: "center",
            ellipsis: true,
            dataIndex: "userNames",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
        ],
        4: [
          {
            title: "序号",
            align: "center",
            width: 100,
            ellipsis: true,
            customRender: (text, record, index) => {
              return index + 1 || "/";
            },
          },
          {
            title: "代表类型",
            align: "center",
            width: 100,
            ellipsis: true,
            dataIndex: "sfmc",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "行政区划",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "administrativeAreaName",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "街道名称",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "streetTownName",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "联络站名称",
            align: "center",
            width: 200,
            ellipsis: true,
            dataIndex: "liaisonStationName",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
          {
            title: "参加活动人员名单（参加次数）",
            align: "center",
            ellipsis: true,
            dataIndex: "userNames",
            customRender: (text, record, index) => {
              return text || "/";
            },
          },
        ],
      },

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
    };
  },
  created() {
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "代表进社区活动次数统计");
  },
  methods: {
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    //列表
    fetchData() {
      this.listLoading = true;
      stationActivityMemberList(this.queryForm).then((response) => {
        this.list = response.data;
        this.listLoading = false;
        this.pagination.total = response.total || response.data.length;
        this.columns = this.columnsGroup[this.queryForm.statisticType] || [];
      });
    },
    handleSizeChange(val) {
      this.queryForm.size = val;
      this.fetchData();
    },
    reset(value) {
      this.queryForm = value;
      this.fetchData();
    },
    exportExcel() {
      //  var arr = []
      // if (this.selectRows.length != 0) {
      //   // return this.$message.warning("请选择数据！");
      //   arr = this.selectRows.map((item) => item.id)
      // }
      stationActivityMemberExprot({
        ...this.queryForm,
        // ids:arr.toString()
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `年度活动计划情况.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
  },
};
</script>
