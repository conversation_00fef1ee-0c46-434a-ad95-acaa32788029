<template>
  <div class="represen-box table-container">
    <div style="display: flex;">
      <div class="db-content" style="flex: 8;padding-right: 20px;">
        <!-- <ViewTips :tipValue="tipValue" /> -->
        <div class="represen">
          <a-form-model
            layout="vertical"
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }">
            <h3 class="db-title">代表基本信息</h3>
            <div class="db-form">
              <a-row>
                  <a-col :md="24" :lg="24" :xl="20">
                    <a-row type="flex">
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                            <a-form-model-item label="层级"
                                              prop="jcNr"
                                              class="jiec">
                              <a-input v-model="ruleForm.dhmc" disabled></a-input>
                            </a-form-model-item>
                          </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item
                                          prop="jcDm"
                                          class="jiec">
                          <template slot="label">
                            届次
                          </template>
                          <!-- <span class="jicson">*</span> -->
                          <a-input v-model="ruleForm.levelName" disabled></a-input>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item label="所属代表团"
                                          class="jiec">

                          <a-input v-model="ruleForm.orgName" disabled></a-input>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.userName == this.ruleForm.userName
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.userName }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="姓名"
                                              prop="userName">
                              <a-input v-model="ruleForm.userName"
                                      :disabled="oper == constat.VIEW">></a-input>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="姓名"
                                          prop="userName">
                          <a-input v-model="ruleForm.userName"
                                  :disabled="oper == constat.VIEW">></a-input>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom && this.submitRrom.dmSex &&
                            this.submitRrom.dmSex.xbDm == this.ruleForm.xbDm
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span v-if="this.submitRrom && this.submitRrom.dmSex">{{ submitRrom.dmSex.sex }}</span>
                              </div>
                            </template>
                            <a-form-model-item prop="xbDm"
                                              label="性别">
                              <a-select v-model="ruleForm.xbDm"
                                        :disabled="oper == constat.VIEW"
                                        placeholder="请选择">
                                <a-select-option v-for="(item, index) in sex"
                                                :key="index"
                                                :value="item.xbDm">{{ item.sex }}
                                </a-select-option>
                              </a-select>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          prop="xbDm"
                                          label="性别">
                          <a-select v-model="ruleForm.xbDm"
                                    :disabled="oper == constat.VIEW"
                                    placeholder="请选择">
                            <a-select-option v-for="(item, index) in sex"
                                            :key="index"
                                            :value="item.xbDm">{{ item.sex }}
                            </a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.dbzHm == this.ruleForm.dbzHm
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.dbzHm }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="代表证号">
                              <a-input v-model="ruleForm.dbzHm"
                                      :disabled="oper == constat.VIEW"></a-input>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="代表证号">
                          <a-input v-model="ruleForm.dbzHm"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.mzDm == this.ruleForm.mzDm
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.mzmc }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="民族"
                                              prop="mzDm">
                              <a-select v-model="ruleForm.mzDm"
                                        :disabled="oper == constat.VIEW"
                                        placeholder="请选择">
                                <a-select-option v-for="(item, index) in nationality"
                                                :key="index"
                                                :value="item.mzDm">{{ item.mzmc }}</a-select-option>
                              </a-select>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="民族"
                                          prop="mzDm">
                          <a-select v-model="ruleForm.mzDm"
                                    :disabled="oper == constat.VIEW"
                                    placeholder="请选择">
                            <a-select-option v-for="(item, index) in nationality"
                                            :key="index"
                                            :value="item.mzDm">{{ item.mzmc }}</a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.zjlxDm == this.ruleForm.zjlxDm
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.zjlxmc }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="证件类型">
                              <!-- prop="zjlxDm" 10-20改 -->
                              <a-select v-model="ruleForm.zjlxDm"
                                        :disabled="oper == constat.VIEW"
                                        placeholder="请选择">
                                <a-select-option v-for="(item, index) in cardType"
                                                :key="index"
                                                :value="item.zjlxDm">{{ item.zjlxmc }}</a-select-option>
                              </a-select>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="证件类型">
                          <!--   prop="zjlxDm" 10-20改-->
                          <a-select v-model="ruleForm.zjlxDm"
                                    :disabled="oper == constat.VIEW"
                                    @change="changezjlxDm"
                                    placeholder="请选择">
                            <a-select-option v-for="(item, index) in cardType"
                                            :key="index"
                                            :value="item.zjlxDm">{{ item.zjlxmc }}</a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.sfzDm == this.ruleForm.sfzDm
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.sfzDm }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="证件号">
                              <!-- prop="sfzDm" 10-20改 -->
                              <a-input v-model="ruleForm.sfzDm"
                                      :disabled="oper == constat.VIEW"></a-input>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="证件号">
                          <!--   prop="sfzDm" 10-20改-->
                          <a-input v-model="ruleForm.sfzDm"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-col>

                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.birthday == this.ruleForm.birthday
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.birthday }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="出生日期" prop="birthday">
                              <a-date-picker v-model="ruleForm.birthday"
                                            value-format="YYYY-MM-DD"
                                            allow-clear
                                            :disabled="oper == constat.VIEW"
                                            :disabled-date="disabledDate" />
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="出生日期"
                                          :rules="[
                                                { required: this.ruleForm.birthday, message: '请选择正确的出生日期'},
                                                { trigger: 'change', validator: validateDateOfBirth },
                                              ]"
                                          prop="birthday">
                          <a-date-picker v-model="ruleForm.birthday"
                                        value-format="YYYY-MM-DD"
                                        :disabled-date="disabledDate"
                                        :showToday="false"
                                        :allowClear="false"
                                        :disabled="oper == constat.VIEW" >
                                        <div style="text-align: center; margin-top : 5px" slot="renderExtraFooter" slot-scope="mode">
                                          <a-button @click="cleanBirthDay">置空</a-button>
                                        </div>
                                        </a-date-picker>
                        </a-form-model-item>
                      </a-col>

                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.nativePlace == this.ruleForm.nativePlace
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.nativePlace }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="籍贯"
                                              prop="nativePlace">
                              <a-input v-model="ruleForm.nativePlace"
                                      :disabled="oper == constat.VIEW"></a-input>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="籍贯"
                                          prop="nativePlace">
                          <a-input v-model="ruleForm.nativePlace"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-col>


                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.zzmmDm == this.ruleForm.zzmmDm
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.zzmmmc }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="党派"
                                              prop="zzmmDm">
                              <a-select v-model="ruleForm.zzmmDm"
                                        :disabled="oper == constat.VIEW"
                                        placeholder="请选择">
                                <a-select-option v-for="(item, index) in politicalAffiliation"
                                                :key="index"
                                                :value="item.zzmmDm">{{ item.politicsStatusName }}</a-select-option>
                              </a-select>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="党派"
                                          prop="zzmmDm">
                          <a-select v-model="ruleForm.zzmmDm"
                                    :disabled="oper == constat.VIEW"
                                    placeholder="请选择"
                                    @change="changeZzmmDm">
                            <a-select-option v-for="(item, index) in politicalAffiliation"
                                            :key="index"
                                            :value="item.zzmmDm">{{ item.politicsStatusName }}</a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.joinTime == this.ruleForm.joinTime
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{
                                  moment(new Date(submitRrom.joinTime)).format(
                                    "YYYY-MM-DD"
                                  )
                                }}</span>
                              </div>
                            </template>
                            <a-form-model-item v-if="isNeedJoninTime"
                                              label="加入日期"
                                              prop="joinTime">
                              <a-date-picker v-model="ruleForm.joinTime"
                                            value-format="YYYY-MM-DD "
                                            allow-clear
                                            :disabled="oper == constat.VIEW" />
                              <!-- <a-date-picker
                            :disabled="oper == constat.VIEW"
                            allow-clear
                            v-model="ruleForm.joinTime"
                            value-format="YYYY-MM-DD"
                              ></a-date-picker>-->
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT' && isNeedJoninTime"
                                          label="加入日期"
                                          prop="joinTime">
                          <a-date-picker v-model="ruleForm.joinTime"
                                        value-format="YYYY-MM-DD "
                                        allow-clear
                                        :disabled="oper == constat.VIEW" />
                          <!-- <a-date-picker
                            :disabled="oper == constat.VIEW"
                            allow-clear
                            v-model="ruleForm.joinTime"
                            value-format="YYYY-MM-DD"
                          ></a-date-picker>-->
                        </a-form-model-item>
                      </a-col>

                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.joinWorkTime ==
                            this.ruleForm.joinWorkTime
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{
                                  moment(new Date(submitRrom.joinWorkTime)).format(
                                    "YYYY-MM-DD"
                                  )
                                }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="参加工作日期">
                              <a-date-picker v-model="ruleForm.joinWorkTime"
                                            value-format="YYYY-MM-DD "
                                            allow-clear
                                            :disabled="oper == constat.VIEW" />
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="参加工作日期">
                          <a-date-picker v-model="ruleForm.joinWorkTime"
                                        value-format="YYYY-MM-DD "
                                        allow-clear
                                        :disabled="oper == constat.VIEW" />
                        </a-form-model-item>
                      </a-col>
                      <!-- <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.duty == this.ruleForm.duty
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.duty }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="(弃用)职位"
                                              prop="duty">
                              <a-input v-model="ruleForm.duty"
                                      :disabled="oper == constat.VIEW"></a-input>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="(弃用)职位"
                                          prop="duty">
                          <a-input v-model="ruleForm.duty"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-col> -->
                      
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.dutyName == this.ruleForm.dutyName
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.dutyName }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="职称"
                                              prop="dutyName">
                              <a-input v-model="ruleForm.dutyName"
                                      :disabled="oper == constat.VIEW"></a-input>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="职称"
                                          prop="dutyName">
                          <a-input v-model="ruleForm.dutyName"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-col>
                      
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.zygcDm == this.ruleForm.zygcDm
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.zygcmc }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="代表类别"
                                              prop="zygcDm">
                              <a-select v-model="ruleForm.zygcDm"
                                        :disabled="oper == constat.VIEW"
                                        placeholder="请选择">
                                <a-select-option v-for="(item, index) in occupationalSelect"
                                                :key="index"
                                                :value="item.zygcDm">{{ item.zygcmc }}</a-select-option>
                              </a-select>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="代表类别"
                                          prop="zygcDm">
                          <a-select v-model="ruleForm.zygcDm"
                                    :disabled="oper == constat.VIEW"
                                    placeholder="请选择">
                            <a-select-option v-for="(item, index) in occupationalSelect"
                                            :key="index"
                                            :value="item.zygcDm">{{ item.zygcmc }}</a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                      
                      <a-col class="span-class" :span="24">
                        <div v-if="oper == 'constat.SUBMIT'"
                            id="components-popover-demo-placement"
                            :class="
                            this.submitRrom.workUnit == this.ruleForm.workUnit
                              ? ''
                              : 'nameN'
                          ">
                          <a-popover placement="top">
                            <template slot="content">
                              <div style="display: flex;">
                                <span>旧值：</span>
                                <span>{{ this.submitRrom.workUnit }}</span>
                              </div>
                            </template>
                            <a-form-model-item label="单位及职务"
                                              prop="workUnit">
                              <a-input v-model="ruleForm.workUnit"
                                      :disabled="oper == constat.VIEW"></a-input>
                            </a-form-model-item>
                          </a-popover>
                        </div>
                        <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                          label="单位及职务"
                                          prop="workUnit">
                          <a-input v-model="ruleForm.workUnit"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-col>
                    </a-row>
                  </a-col>
                  <a-col  :class="handleResize()" :md="24" :lg="12" :xl="4">
                    <a-form-model-item
                      label="代表照片"
                      prop=""
                      :label-col="{ span: 24 }"
                      :wrapper-col="{ span: 24 }"
                      style="line-height: 38px;">
                      <div class="userImage">
                        <a-upload v-model="ruleForm.imageUrl"
                                  :file-list="isfileList"
                                  list-type="picture-card"
                                  :action="action"
                                  accept="image/*"
                                  :before-upload="beforeAvatarUpload"
                                  @change="handleAvatarSuccess"
                                  @preview="handlePreview">
                          <img v-if="imageUrl"
                              :src="imageUrl"
                              alt="avatar" />
                          <div v-else-if="isfileList.length < 1">
                            <a-icon :type="loading ? 'loading' : 'plus'" />
                            <div class="ant-upload-text">上传头像</div>
                          </div>
                        </a-upload>
                        <a-modal :visible.sync="dialogVisible"
                                :footer="null"
                                @cancel="
                            () => {
                              dialogVisible = false;
                            }
                          ">
                          <img v-if="imageUrl"
                              :disabled="oper == constat.VIEW"
                              :src="imageUrl"
                              alt="avatar" />
                        </a-modal>
                      </div>
                    </a-form-model-item>
                  </a-col>
              </a-row>
            </div>
            <h3 class="db-title">代表教育情况</h3>
            <div class="db-form">
              <a-row type="flex">
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.qrzjyxlDm == this.ruleForm.qrzjyxlDm
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.qrzxlmc }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="全日制教育学历"
                                          prop="qrzjyxlDm">
                          <a-select v-model="ruleForm.qrzjyxlDm"
                                    :disabled="oper == constat.VIEW"
                                    placeholder="请选择">
                            <a-select-option v-for="(item, index) in education"
                                            :key="index"
                                            :value="item.xlDm">{{ item.xlmc }}</a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="全日制教育学历"
                                      prop="qrzjyxlDm">
                      <a-select v-model="ruleForm.qrzjyxlDm"
                                :disabled="oper == constat.VIEW"
                                placeholder="请选择">
                        <a-select-option v-for="(item, index) in education"
                                        :key="index"
                                        :value="item.xlDm">{{ item.xlmc }}
                        </a-select-option>
                      </a-select>
                    </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.qrzjyxwDm == this.ruleForm.qrzjyxwDm
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.qrzxwmc }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="全日制教育学位"
                                          prop="qrzjyxwDm">
                          <a-select v-model="ruleForm.qrzjyxwDm"
                                    :disabled="oper == constat.VIEW"
                                    placeholder="请选择">
                            <a-select-option v-for="(item, index) in degree"
                                            :key="index"
                                            :value="item.xwDm">{{ item.xwmc }}
                            </a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="全日制教育学位"
                                      prop="qrzjyxwDm">
                      <a-select v-model="ruleForm.qrzjyxwDm"
                                :disabled="oper == constat.VIEW"
                                placeholder="请选择">
                        <a-select-option v-for="(item, index) in degree"
                                        :key="index"
                                        :value="item.xwDm">{{ item.xwmc }}
                        </a-select-option>
                      </a-select>
                    </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.fullSchool == this.ruleForm.fullSchool
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.fullSchool }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="毕业院校"
                                          prop="fullSchool">
                          <a-input v-model="ruleForm.fullSchool"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="毕业院校"
                                      prop="fullSchool">
                      <a-input v-model="ruleForm.fullSchool"
                              :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.fullMajor == this.ruleForm.fullMajor
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.fullMajor }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="系及专业"
                                          prop="fullMajor">
                          <a-input v-model="ruleForm.fullMajor"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="系及专业"
                                      prop="fullMajor">
                      <a-input v-model="ruleForm.fullMajor"
                              :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.zzjyxlDm == this.ruleForm.zzjyxlDm
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.zzxlmc }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="在职教育学历"
                                          prop="zzjyxlDm">
                          <a-select v-model="ruleForm.zzjyxlDm"
                                    :disabled="oper == constat.VIEW"
                                    placeholder="请选择">
                            <a-select-option v-for="(item, index) in education"
                                            :key="index"
                                            :value="item.xlDm">{{ item.xlmc }}</a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="在职教育学历"
                                      prop="zzjyxlDm">
                      <a-select v-model="ruleForm.zzjyxlDm"
                                :disabled="oper == constat.VIEW"
                                placeholder="请选择">
                        <a-select-option v-for="(item, index) in education"
                                        :key="index"
                                        :value="item.xlDm">{{ item.xlmc }}
                        </a-select-option>
                      </a-select>
                    </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.zzjyxwDm == this.ruleForm.zzjyxwDm
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.zzxwmc }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="在职教育学位"
                                          prop="zzjyxwDm">
                          <a-select v-model="ruleForm.zzjyxwDm"
                                    :disabled="oper == constat.VIEW"
                                    placeholder="请选择">
                            <a-select-option v-for="(item, index) in degree"
                                            :key="index"
                                            :value="item.xwDm">{{ item.xwmc }}
                            </a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="在职教育学位"
                                      prop="zzjyxwDm">
                      <a-select v-model="ruleForm.zzjyxwDm"
                                :disabled="oper == constat.VIEW"
                                placeholder="请选择">
                        <a-select-option v-for="(item, index) in degree"
                                        :key="index"
                                        :value="item.xwDm">{{ item.xwmc }}
                        </a-select-option>
                      </a-select>
                    </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.jobSchool == this.ruleForm.jobSchool
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.jobSchool }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="毕业院校"
                                          prop="jobSchool">
                          <a-input v-model="ruleForm.jobSchool"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="毕业院校"
                                      prop="jobSchool">
                      <a-input v-model="ruleForm.jobSchool"
                              :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.serviceEducation ==
                        this.ruleForm.serviceEducation
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.serviceEducation }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="系及专业"
                                          prop="serviceEducation">
                          <a-input v-model="ruleForm.serviceEducation"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="系及专业"
                                      prop="serviceEducation">
                      <a-input v-model="ruleForm.serviceEducation"
                              :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                </a-col>
              </a-row>
            </div>
            <h3 class="db-title">代表联系方式</h3>
            <div class="db-form">
              <a-row type="flex">
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.unitAddress == this.ruleForm.unitAddress
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.unitAddress }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="单位地址"
                                          prop="unitAddress">
                          <a-input v-model="ruleForm.unitAddress"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                  </div>
                  <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="单位地址"
                                      prop="unitAddress">
                      <a-input v-model="ruleForm.unitAddress"
                              :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.unitPhone == this.ruleForm.unitPhone
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.unitPhone }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="单位电话">
                          <a-input v-model="ruleForm.unitPhone"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                        prop="unitPhone"
                                        :rules="[
                                          { required: this.ruleForm.unitPhone, message: '请输入正确的单位电话'},
                                          { trigger: 'blur', validator: validatenuitPhone },
                                        ]"
                                      label="单位电话">
                      <a-input v-model="ruleForm.unitPhone"
                              :disabled="oper == constat.VIEW"></a-input>
                    </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.unitPostalCode ==
                        this.ruleForm.unitPostalCode
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.unitPostalCode }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="单位邮编">
                          <a-input v-model="ruleForm.unitPostalCode"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                  </div>
                  <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                        prop="unitPostalCode"
                                        :rules="[
                                          { required: this.ruleForm.unitPostalCode, message: '请输入正确的单位邮编'},
                                          { trigger: 'blur', validator: validatenuitPostalCode },
                                        ]"
                                      label="单位邮编">
                      <a-input v-model="ruleForm.unitPostalCode"
                              :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.houseAddress == this.ruleForm.houseAddress
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.houseAddress }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="家庭地址">
                          <a-input v-model="ruleForm.houseAddress"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                  </div>
                  <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="家庭地址">
                      <a-input v-model="ruleForm.houseAddress"
                              :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.housePhone == this.ruleForm.housePhone
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.housePhone }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="家庭电话">
                          <a-input v-model="ruleForm.housePhone"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                  </div>
                  <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="家庭电话">
                      <a-input v-model="ruleForm.housePhone"
                              :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.housePostalCode ==
                        this.ruleForm.housePostalCode
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.housePostalCode }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="家庭邮编">
                          <a-input v-model="ruleForm.housePostalCode"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                  </div>
                  <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                        prop="housePostalCode"
                                        :rules="[
                                          { required: this.ruleForm.housePostalCode, message: '请输入正确的家庭邮编' },
                                          { trigger: 'blur', validator: validatehouseCode },
                                        ]"
                                      label="家庭邮编">
                      <a-input v-model="ruleForm.housePostalCode"
                              :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.phone == this.ruleForm.phone ? '' : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.phone }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="手机"
                                          prop="phone">
                          <a-input v-model="ruleForm.phone"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                  </div>
                  <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="手机"
                                      prop="phone">
                      <a-input v-model="ruleForm.phone"
                              :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.twoPhone == this.ruleForm.twoPhone
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.twoPhone }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="手机2"
                                          prop="twoPhone">
                          <a-input v-model="ruleForm.twoPhone"
                                  :disabled="oper == constat.VIEW"
                                  @change="changtwoPhone"></a-input>
                        </a-form-model-item>
                      </a-popover>
                  </div>
                  <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="手机2"
                                      prop="twoPhone">
                      <a-input v-model="ruleForm.twoPhone"
                              :disabled="oper == constat.VIEW"
                              @change="changtwoPhone"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.email == this.ruleForm.email ? '' : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.email }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="电子邮件">
                          <a-input v-model="ruleForm.email"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-popover>
                  </div>
                  <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                        prop="email"
                                        :rules="[
                                          { required: this.ruleForm.email, message: '请输入正确的电子邮件'},
                                          { trigger: 'blur', validator: validateemail },
                                        ]"
                                      label="电子邮件">
                      <a-input v-model="ruleForm.email"
                              :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
                <!-- 暂不删除、可能需要用到 -->
                <!-- <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <div v-if="oper == 'constat.SUBMIT'"
                        id="components-popover-demo-placement"
                        :class="
                        this.submitRrom.threePhone == this.ruleForm.threePhone
                          ? ''
                          : 'nameN'
                      ">
                      <a-popover placement="top">
                        <template slot="content">
                          <div style="display: flex;">
                            <span>旧值：</span>
                            <span>{{ this.submitRrom.threePhone }}</span>
                          </div>
                        </template>
                        <a-form-model-item label="手机3"
                                          prop="threePhone">
                          <a-input v-model="ruleForm.threePhone"
                                  :disabled="oper == constat.VIEW"
                                  @change="changethreePhone"></a-input>
                        </a-form-model-item>
                      </a-popover>
                    </div>
                    <a-form-model-item v-if="oper != 'constat.SUBMIT'"
                                      label="手机3"
                                      prop="threePhone">
                      <a-input v-model="ruleForm.threePhone"
                              :disabled="oper == constat.VIEW"
                              @change="changethreePhone"></a-input>
                    </a-form-model-item>
                </a-col> -->
              </a-row>
            </div>
            <h3 class="db-title">选举信息</h3>
            <div class="db-form">
              <a-row type="flex">
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <a-form-model-item label="职业构成"
                                    prop>
                    <a-input :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <a-form-model-item label="选举单位"
                                    prop>
                    <a-input :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </div>
            <template v-if="oper == constat.VIEW">
              <h3 class="db-title">其他</h3>
              <div class="db-form">
                <a-form-model-item
                  prop="isOverseascn"
                  :label-col="{ span: 2 }">
                  <a-row type="flex">
                    <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-checkbox v-model="ruleForm.isOverseascn"
                                    :disabled="oper == constat.VIEW">是否归侨眷属</a-checkbox>
                    </a-col>
                    <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-checkbox v-model="ruleForm.isPeasant"
                                    :disabled="oper == constat.VIEW">是否农民工</a-checkbox>
                    </a-col>
                    <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-checkbox v-model="ruleForm.isCivilServant"
                                    :disabled="oper == constat.VIEW">是否公务员</a-checkbox>
                    </a-col>
                    <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-checkbox v-model="ruleForm.isReappointment"
                                    :disabled="oper == constat.VIEW">是否连任代表</a-checkbox>
                    </a-col>
                  </a-row>
                  <a-row type="flex">
                    <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-checkbox v-model="ruleForm.isRepresentative"
                                    :disabled="oper == constat.VIEW">是否同任两级以上代表
                        </a-checkbox>
                    </a-col>
                    <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-checkbox v-model="ruleForm.isUnitLeader"
                                    :disabled="oper == constat.VIEW">是否事业单位负责人</a-checkbox>
                    </a-col>
                    <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-checkbox v-model="ruleForm.isPartyBranch"
                                    :disabled="oper == constat.VIEW">是否村委会村党支部组成人员
                        </a-checkbox>
                    </a-col>
                  </a-row>
                </a-form-model-item>
                <a-form-model-item
                  label="简历"
                  prop="resume"
                  :label-col="{ span: 2 }"
                  class="span-class">
                    <a-textarea
                      v-model="ruleForm.resume"
                      placeholder="简历"
                      :rows="4"
                      :disabled="oper == constat.VIEW" />
                </a-form-model-item>
                <a-form-model-item
                  label="备注"
                  prop="remark"
                  :label-col="{ span: 2 }"
                  class="span-class">
                    <a-textarea
                      v-model="ruleForm.remark"
                      placeholder="备注"
                      :rows="4"
                      :disabled="oper == constat.VIEW" />
                </a-form-model-item>
              </div>
            </template>
            <h3 class="db-title">录入人信息</h3>
            <div class="db-form">
                  <a-row>
                    <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item
                          label="录入人层级"
                          prop="instituLevel"
                          class="person_info"
                        >
                          <a-input
                            class="input_style"
                            v-model="personInfo1"
                            autocomplete="off"
                            placeholder="系统自动生成"
                            disabled
                          ></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item
                          label="录入人机构"
                          prop="instituLevel"
                          class="person_info"
                        >
                          <a-input
                            class="input_style"
                            v-model="personInfo2"
                            autocomplete="off"
                            placeholder="系统自动生成"
                            disabled
                          ></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item
                          label="录入人姓名"
                          prop="instituLevel"
                          class="person_info"
                        >
                          <a-input
                            class="input_style"
                            v-model="personInfo3"
                            autocomplete="off"
                            placeholder="系统自动生成"
                            disabled
                          ></a-input>
                        </a-form-model-item>
                    </a-col>
                  </a-row>
            </div>
            <a-row
              v-if="oper != constat.VIEW"
              type="flex"
              justify="center"
              style="margin-top: 16px;">
              <a-form-model-item>
                    <a-space v-if="recordName != 'LookSee'">
                      <a-button v-show="isShowInTo"
                                type="primary"
                                @click="lczzjl">查询进度</a-button>
                      <a-button v-show="!isShowInTo"
                                @click="resetForm('ruleForm')">暂存为草稿</a-button>
                      <a-button v-show="!isShowInTo"
                                type="primary"
                                @click="submitForm('ruleForm')">保存并送审</a-button>
                      <a-button v-show="isShowInTo"
                                type="primary"
                                @click="handleAudit">送审</a-button>
                      <a-button @click="cancel">取消</a-button>
                    </a-space>
                    <a-space v-if="recordName == 'LookSee'">
                      <a-button v-show="isShowInTo"
                                type="primary"
                                @click="lczzjl">查询进度</a-button>
                      <a-button @click="cancel">取消</a-button>
                    </a-space>
              </a-form-model-item>
            </a-row>
          </a-form-model>
        </div>
      </div>
      <TimeLine :lineList="lineList" :tab="currentTab" :disabled="true" /> <!-- 暂时不要点击切换功能 -->
    </div>
    <submitForCensorship
      ref="submitForCensorship"
      :proc-inst-id="procInstId"
      :ids="ids"
      @complete="handleComplete">
    </submitForCensorship>
    <lczzjl
      ref="lczzjl"
      :proc-inst-id="procInstId">
    </lczzjl>
  </div>
</template>

<script>
import { getfindCurrentUserInfoApi } from "@/api/infoQuery.js";
import moment from "moment";
import lczzjl from "@/views/common/lczzjl";
import Vue from "vue";
import {  baseSelect } from "@/api/area.js";
import { dbleaveuploadApi } from "@/api/representativeElection/candidateApi.js";
import submitForCensorship from "@/views/common/submitForCensorship";
import { checkIdNumberValid } from "@/utils/validate";
import ViewTips from '@/components/ViewTips/index';
import TimeLine from '@/components/TimeLine/index';
import {
  getInfo,
  getOldInfo,
  update,
  getBaseInfo,
  insertWithWorkFlow,
} from "@/api/dbxx";
import {
  getpersonalDetailscreateApi,
  getpersonalDetailscompleteApi,
  getpersonalDetailsfindApi,
} from "@/api/xjgw";
import { findLevelByJcDm } from "@/api/organization";
import constat from "@/utils/constat";

export default {
  components: {
    submitForCensorship,
    lczzjl,
    ViewTips,
    TimeLine
  },
  filters: {},
  data () {
    // 根据证件号校验生日
    var validateDateOfBirth = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        if (this.ruleForm.birthday) {
          let year = this.ruleForm.birthday.slice(6, 10);
          let month = this.ruleForm.birthday.slice(10, 12);
          let day = this.ruleForm.birthday.slice(12, 14);
          // console.log(value);
          // let isYear = value.split("-")[0] == year;
          // let isMonth = value.split("-")[1] == month;
          // let isDay = value.split("-")[2] == day;
          let temp_date = new Date(
            year,
            parseFloat(month) - 1,
            parseFloat(day)
          );
          if (temp_date) {
            callback();
          } else {
            callback(new Error("请选择正确的出生日期"));
          }
        }
      }
    };
    // 根据证件类型校验证件号
    var validateIdNum = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        if (this.ruleForm.zjlxDm === "01") {
          let reg = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
          reg.test(value)
            ? callback()
            : callback(new Error("请输入正确的证件号"));
        }
        if (this.ruleForm.zjlxDm === "02") {
          // let reg = /^[\u4E00-\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$/;
          // reg.test(value)
          //   ? callback()
          //   : callback(new Error("请输入正确的证件号"));
        }
      }
    };
    // 校验手机号
    var validatePhone = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入手机号"));
      } else {
        let reg = /^([1][3,4,5,6,7,8,9])\d{9}$/;
        (reg.test(value) || value == '10000000000')
          ? callback()
          : callback(new Error("请输入正确的手机号"));
      }
    };
    //校验单位邮政编码
    var validatenuitPostalCode = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        let reg = /^[1-9]\d{5}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的单位邮编"));
      }
    };
    //校验家庭邮政编码
    var validatehouseCode = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        let reg = /^[1-9]\d{5}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的家庭邮编"));
      }
    };
    //校验电子邮件
    var validateemail = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        let reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的电子邮件"));
      }
    };
    // 校验单位电话
    var validatenuitPhone = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        let reg = /^([1][3,4,5,6,7,8,9])\d{9}$/;
        let tel_reg = /^0\d{2,3}-?\d{7,8}$/;
        reg.test(value) || tel_reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的单位电话"));
      }
    };
    return {
      occupationalSelect: [],
      validatehouseCode,
      validatenuitPostalCode,
      validatenuitPhone,
      validateemail,
      validateDateOfBirth,
      dialogVisible: false,
      isfileList: [],
      imageUrl: "",
      loading: false,
      action: Vue.prototype.GLOBAL.basePath_1 + "/dbxx/",
      submitRrom: {
        dbzHm: "",
        userName: "",
        xbDm: "",
        birthday: "",
        mzDm: "",
        nativePlace: "",
        zjlxDm: "",
        sfzDm: "",
        zzmmDm: "",
        joinTime: "",
        workUnit: "",
        joinWorkTime: "",
        duty: "",
        dutyName: "",
        qrzjyxlDm: "",
        qrzjyxwDm: "",
        fullSchool: "",
        fullMajor: "",
        zzjyxlDm: "",
        zzjyxwDm: "",
        jobSchool: "",
        serviceEducation: "",
        unitAddress: "",
        unitPhone: "",
        unitPostalCode: "",
        email: "",
        houseAddress: "",
        housePhone: "",
        housePostalCode: "",
        phone: "",
        birthday1: "",
        joinWorkTime1: "",
        joinTime1: "",
        zzxlmc: "",
        zzxwmc: "",
        zzmmmc: "",
        zjlxmc: "",
        qrzxwmc: "",
        qrzxlmc: "",
        mzmc: "",
      },
      dbdh: {},
      dbtOptions: [
        {name: '越秀区'},
        {name: '海珠区'},
        {name: '荔湾区'},
        {name: '天河区'},
        {name: '白云区'},
        {name: '黄埔区'},
        {name: '番禺区'},
        {name: '花都区'},
        {name: '南沙区'},
        {name: '从化区'},
        {name: '增城区'},
      ],
      isShowInTo: false,
      ismemberId: "",
      procInstId: "",
      ids: [],
      activeKey: ["k1", "k2", "k3", "k4", "k5"],
      customStyle: "background:rgba(190,64,61,0.1);",
      active: 0,
      oper: constat.ADD,
      ruleForm: {},
      sex: [],
      nationality: [],
      cardType: [],
      treeData: [],
      politicalAffiliation: [],
      education: [],
      degree: [],
      rules: {
        dbzHm: [{ required: true, message: "请输入代表证号", trigger: "blur" }],
        userName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        xbDm: [{ required: true, message: "请输入性别", trigger: "change" }],
        mzDm: [{ required: false, message: "请输入民族", trigger: "change" }],
        nativePlace: [
          { required: false, message: "请输入籍贯", trigger: "blur" },
        ],
        zjlxDm: [
          { message: "请输入证件类型", trigger: "change"},
        ],
        //
        sfzDm: [{ trigger: "blur", validator: validateIdNum }],
        zzmmDm: [{ required: false, message: "请输入党派", trigger: "change" }],
        joinTime: [
          { required: false, message: "请输入加入日期", trigger: "change" },
        ],
        workUnit: [
          { required: true, message: "请输入工作单位", trigger: "blur" },
        ],
        joinWorkTime: [
          { required: false, message: "请输入参加工作日期", trigger: "change" },
        ],
        duty: [{ required: false, message: "请输入职位", trigger: "blur" }],
        dutyName: [{ required: false, message: "请输入职称", trigger: "blur" }],
        qrzjyxlDm: [{ required: false, message: "请输入全日制教育学历", trigger: "change", },],
        qrzjyxwDm: [{ required: false, message: "请输入全日制教育学位", trigger: "change", },
        ],
        fullSchool: [
          { required: false, message: "请输入毕业院校历", trigger: "blur" },
        ],
        fullMajor: [
          { required: false, message: "请输入系及专业", trigger: "blur" },
        ],
        unitAddress: [
          { required: false, message: "请输入单位地址", trigger: "blur" },
        ],
        houseAddress: [
          { required: false, message: "请输入家庭地址", trigger: "blur" },
        ],
        housePhone: [
          { required: false, message: "请输入家庭电话", trigger: "blur" },
        ],
        phone: [
          { required: true, trigger: "change", validator: validatePhone, },
        ],
        // isOverseascn: [
        //   { required: true, message: "请输入选举单位", trigger: "blur" },
        // ],
        // resume: [
        //   { required: true, message: "请输入选举单位", trigger: "blur" },
        // ],
      },
      constat,
      jcDm: "",
      sfDm: "",
      orgId: "",
      inids: [],
      inprocInstId: "",
      levelOptions: [],
      recordName: "",
      isNeedJoninTime: true,
      personInfo1: '',
      personInfo2: '',
      personInfo3: '',
      // tipValue: '【测试展示内容】表单标签告诉用户该表单需要填写什么类型的信息，也涉及到对用户输入内容进行校验与反馈，保证用户信息填写的完整度。单域由文本框、密码框、隐藏域、多行文本框、 复选框、单选框、下拉选择框和文件上传框等组成。',
      currentClass: '',
      lineList: [
        {
          content: '预备人选基本情况申报',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
          // status: 'finished',
        }, {
          content: '预备人选信息登记',
          timestamp: '2018-04-11',
          status: 'unPass',
          type: '审核通过',
          color: '#0bbd87',
          remark: '备注：审核通过'
        }, {
          content: '候选人确定',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '候选人分配',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '代表信息补录',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }, {
          content: '同步到代表信息库',
          timestamp: '2018-04-13',
          type: '提交成功',
          status: 'unPass',
        }
      ],
      currentTab: 4,
    };
  },
  mounted () {
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  created () {
    this.SelectData();
    this.$store.dispatch("navigation/breadcrumb1", "代表信息");
    this.$store.dispatch("navigation/breadcrumb2", "管理代表");
    this.$store.dispatch("navigation/breadcrumb3", "个人信息修改");
    this.oper = this.$route.query.oper;
    this.recordName = this.$route.query.recordName;
    if (this.oper == constat.VIEW || this.oper == constat.EDIT) {
      const jcDm = this.$route.query.jcDm;
      const dbId = this.$route.query.dbId;
      const sfDm = this.$route.query.sfDm;
      this.jcDm = jcDm;
      this.sfDm = sfDm;
      this.getInfo(jcDm, dbId, sfDm);
      this.findLevelByJcDm();
    } else if (this.oper == constat.ADD) {
      this.jcDm = this.$route.query.jcDm;
      this.sfDm = this.$route.query.sfDm;
      this.orgId = this.$route.query.orgId;
      this.getBaseInfo();
      this.findLevelByJcDm();
    } else if (this.oper == "constat.SUBMIT") {
      const jcDm = this.$route.query.jcDm;
      const dbId = this.$route.query.dbId;
      const sfDm = this.$route.query.sfDm;
      this.subgetInfo(jcDm, dbId, sfDm);
      this.huiXianSons();
    } else {
      getfindCurrentUserInfoApi().then((res) => {
        if (res.code == "0000") {
          let { JC_DM, SF_DM, DB_ID, GW_ID } = res.data;
          this.oper = constat.EDIT;
          this.JC_DM = JC_DM;
          this.SF_DM = SF_DM;
          this.DB_ID = DB_ID;
          this.GW_ID = GW_ID;
          const jcDm = this.JC_DM;
          const dbId = this.DB_ID;
          const sfDm = this.SF_DM;
          this.jcDm = jcDm;
          this.sfDm = sfDm;
          this.getInfo(jcDm, dbId, sfDm);
          this.findLevelByJcDm();
        }
      });
    }
    this.getBaseInfo();
    this.findLevelByJcDm();
  },
  methods: {
        async SelectData() {
      //  下拉框的数据
      let res = await baseSelect();
      let {
        occupational
      } = res.data;
      this.occupationalSelect = occupational; /* 职业构成 */
    },
    handleResize() {
      let className = '';
      if (window.innerWidth < 991) {
        className = 'span-class'
      } else if (window.innerWidth > 992 && window.innerWidth < 1200) {
        className = 'span-class'
      } else {
        className = ''
      }
      this.currentClass = className
      return this.currentClass
    },
    cleanBirthDay() {
      this.ruleForm.birthday = null
    },
    changezjlxDm () {
      if (this.ruleForm.zjlxDm === "02") {
        this.ruleForm.sfzDm = '0'
      } else {
        this.ruleForm.sfzDm = null
      }
    },
    changeZzmmDm () {
      if (this.ruleForm.zzmmDm == "12" || this.ruleForm.zzmmDm == "16") {
        this.isNeedJoninTime = false;
      } else {
        this.isNeedJoninTime = true;
      }
    },
    changethreePhone (e) {
      this.ruleForm.threePhone = e.target.value;
      this.$forceUpdate();
    },
    changtwoPhone (e) {
      this.ruleForm.twoPhone = e.target.value;
      this.$forceUpdate();
    },
    // +
    // 图片预览
    async handlePreview (file) {
      if (!file.url && !file.preview) {
        file.preview = await URL.createObjectURL(file.originFileObj);
      }
      this.imageUrl = file.url || file.preview;
    },
    //上传头像成功回调方法 @change
    handleAvatarSuccess ({ file, fileList }) {
      this.isfileList = fileList;
      var data = new FormData();
      data.append("file", file);
      dbleaveuploadApi(data).then((res) => {
        if (res.data.code == "0000") {
          this.ruleForm.photograph = res.data.data.relativePath;
          this.imageUrl = "";
          this.$message.success("上传成功");
        }
      });
      this.loading = false;
    },
    //上传头像图片格式限制
    beforeAvatarUpload (file, fileList) {
      var reg = /image\/(png|jpg|gif|jpeg|webp)$/;
      const isJPG = reg.test(file.type);
      const isLt8M = file.size / 1024 / 1024 < 8;
      if (!isJPG) {
        this.$message.error("文件格式不正确，请上传图片!");
      }
      if (!isLt8M) {
        this.$message.error("上传头像图片大小不能超过 8MB!");
      }
      if (isJPG && isLt8M) {
        return false;
      } else {
        return true;
      }
    },
    //不能选择今天以后的日期
    disabledDate (current) {
      return current && current > moment().endOf("day");
    },
    //查询进度
    lczzjl () {
      this.$refs.lczzjl.visible = true;
      this.$refs.lczzjl.id = this.procInstId;
    },
    //送审
    handleAudit () {
      this.$refs.submitForCensorship.visible = true;
    },
    //回显送审
    huiXianSons () {
      if (this.$route.query.id != "") {
        this.ismemberId = this.$route.query.id;
      }
      if (this.ismemberId != undefined) {
        this.isShowInTo = true;
        getpersonalDetailsfindApi(this.ismemberId).then((res) => {
          if (res.data.code == "0000") {
            this.ruleForm = res.data.data;
            this.procInstId = res.data.data.procInstId;
            this.ids.push(res.data.data.id);
          }
        });
      } else {
        this.isShowInTo = false;
      }
    },

    //取消
    cancel () {
      this.isShowInTo = "";
      this.$router.push("/delegate/manage");
      if (this.ismemberId != undefined) {
        this.$router.push("/delegate/backlog");
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
            if (this.oper == constat.EDIT) {
              this.handleUpdate();
            }
            if (this.oper == constat.ADD) {
              this.handleAdd();
            }
          // }
        } else {
          console.log("错误!!");
          this.$message.error('请输入完整的信息')
          return false;
        }
      });
    },
    resetForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let dmSex = {
            xbDm: this.ruleForm.xbDm,
          };
          this.ruleForm.dmSex = dmSex;
          let dqztDm = "11";
          this.ruleForm.dqztDm = dqztDm;
          let zjlx = {
            zjlxDm: this.ruleForm.zjlxDm,
          };
          this.ruleForm.zjlx = zjlx;
          getpersonalDetailscreateApi(this.ruleForm).then((response) => {
            if (response.data.code == "0000") {
              console.log(response.data.data, "response.data");

              this.$baseMessage("已存为草稿", "info");
            }
          });
        } else {
          console.log("错误!!");
          return false;
        }
      });
    },
    getInfo (jcDm, dbId, sfDm) {
      getInfo({ jcDm: jcDm, dbId: dbId, sfDm: sfDm, id: dbId }).then(
        (response) => {
          this.ruleForm = response.data.info;
          this.sex = response.data.sex;
          this.nationality = response.data.nationality;
          this.cardType = response.data.cardType;
          this.politicalAffiliation = response.data.politicalAffiliation;
          this.education = response.data.education;
          this.degree = response.data.degree;
          this.imageUrl = this.action + response.data.info.photograph;
          this.ruleForm.photograph = response.data.info.photograph;
          if (response.data.info.phone.indexOf(",") != "-1") {
            var phonelist = response.data.info.phone.split(",");
            this.ruleForm.phone = phonelist[0];

            if (phonelist.length > 1) {
              this.ruleForm.twoPhone = phonelist[1];
            } else {
              this.ruleForm.twoPhone = undefined;
            }
            if (phonelist.length > 2) {
              this.ruleForm.threePhone = phonelist[2];
            } else {
              this.ruleForm.threePhone = undefined;
            }
            this.$forceUpdate();
            phonelist = [];
          }
          if (this.ruleForm.zzmmDm == "16" || this.ruleForm.zzmmDm == "12") {
            this.isNeedJoninTime = false;
          }
        }
      );
    },
    // 获取旧值
    subgetInfo (jcDm, dbId, sfDm) {
      getOldInfo({ jcDm: jcDm, dbId: dbId, sfDm: sfDm, id: dbId }).then(
        (res) => {
          console.log(res.data.info, "旧值");
          this.submitRrom = res.data.info;
          // 特殊修改
          if (res.data.info.phone.indexOf(",") != "-1") {
            var phonelist = res.data.info.phone.split(",");
            this.submitRrom.phone = phonelist[0];

            if (phonelist.length > 1) {
              this.submitRrom.twoPhone = phonelist[1];
            } else {
              this.submitRrom.twoPhone = undefined;
            }
            if (phonelist.length > 2) {
              this.submitRrom.threePhone = phonelist[2];
            } else {
              this.submitRrom.threePhone = undefined;
            }
            this.$forceUpdate();
            phonelist = [];
          }
        }
      );
    },
    handleUpdate () {
      console.log(123);
      //保存并送审
      let dmSex = {
        xbDm: this.ruleForm.xbDm,
      };
      this.ruleForm.dmSex = dmSex;
      let dqztDm = "11";
      this.ruleForm.dqztDm = dqztDm;
      let zjlx = {
        zjlxDm: this.ruleForm.zjlxDm,
      };
      this.ruleForm.zjlx = zjlx;
      var data = this.ruleForm;

      if (this.ruleForm.twoPhone) {
        data.phone += "," + data.twoPhone;
      }
      if (this.ruleForm.threePhone) {
        data.phone += "," + data.threePhone;
      }
      getpersonalDetailscreateApi(data).then((response) => {
        if (response.data.code == "0000") {
          if (response.data.data == null) {
            this.$router.push({
              path: "/delegate/Handling",
            });
          }
          this.procInstId = response.data.data.procInstId;
          this.ids.push(response.data.data.id);
          this.$baseMessage("更新成功", "info");
          this.$refs.submitForCensorship.visible = true;
        }
      });
    },
    //审核保存
    handleComplete (data) {
      getpersonalDetailscompleteApi(data, { tzlx: 0 }).then((res) => {
        if (res.data.code == "0000") {
          this.$emit("handleClearId");
          this.ids = [];
          this.$router.push("/delegate/Handling");
          this.$refs.submitForCensorship.successComplete();
          this.$message.success(res.data.msg);
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    handleAdd () {
      this.ruleForm.sfDm = this.sfDm;
      this.ruleForm.jcDm = this.jcDm;
      this.ruleForm.orgId = this.orgId;
      let dmSex = {
        xbDm: this.ruleForm.xbDm,
      };
      this.ruleForm.dmSex = dmSex;
      insertWithWorkFlow(this.ruleForm).then((response) => {
        if (response.data == null) {
          this.$router.push({
            path: "/delegate/manage",
          });
        }
        this.procInstId = response.data.procInstId;
        this.ids.push(response.data.id);
        this.$baseMessage("更新成功", "info");
        this.$refs.submitForCensorship.visible = true;
        this.$router.push("/delegate/Handling");
      });
    },
    getBaseInfo () {
      getBaseInfo({ jcDm: this.jcDm }).then((response) => {
        this.sex = response.data.sex;
        this.nationality = response.data.nationality;
        this.cardType = response.data.cardType;
        this.politicalAffiliation = response.data.politicalAffiliation;
        this.education = response.data.education;
        this.degree = response.data.degree;
      });
    },
    findLevelByJcDm () {
      if (this.ismemberId != "") {
        this.jcDm = this.$route.query.jcDm;
        // this.jcDm = this.$route.query.jcDm;
      }
      findLevelByJcDm({ jcDm: this.jcDm }).then((response) => {
        this.levelOptions = response.data;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.table-container {
  padding: 0px 20px 20px 20px;
}
::v-deep .ant-upload.ant-upload-select-picture-card {
  width: 100%;
  height: 190px;
}
::v-deep .ant-calendar-picker {
  width: 100%;
}
.db-content {
  .db-title {
    margin-top: 20px;
    height: 30px;
    line-height: 30px;
    border-left: 4px solid #c71c33;
    padding-left: 18px;
    font-weight: bold;
    font-size: 18px;
    box-sizing: border-box;
  }
  .db-form {
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    padding: 20px 0;
  }
}
.span-class {
  padding: 0 25px;
}
.newClas {
  color: blue;
}

.represen-box {
  width: 100%;

  .Steps {
    width: 80%;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .userImage {
    height: 200px;
    width: 150px;

    img {
      height: 200px;
      width: 150px;
    }
  }
}

.jic {
  position: relative;
}

.jicson {
  color: red;
  // font-size: 2px;
  @include add-size($font_size_16);
  position: absolute;
  top: -9px;
  left: -51px;
  z-index: 999;
}

.nameN {
  ::v-deep .ant-form-item label {
    color: red;
  }
}
</style>
<style lang="scss">
::v-deep .ant-popover-placement-top {
  margin-top: 40px;
  color: red !important;
}
</style>
