<template>
  <div class="table-container">
    <a-modal title="审核进度"
             :visible.sync="visible"
             @ok="close"
             width="70%"
             @cancel="close">
      <a-row>
        <a-table class="directorySet-table"
                 ref="table"
                 :columns="columns"
                 :pagination="pagination"
                 :row-selection="rowSelection"
                 :data-source="dataSource"
                 :scroll="{ x: 300, y: 0 }"></a-table>
      </a-row>
    </a-modal>
  </div>
</template>

<script>
import { getgetHistoryTasksApi } from "@/api/representativeElection/candidateApi.js";
// import additionSignIn from "@/views/ballot/additionSignIn"
import { myPagination } from "@/mixins/pagination.js";
// import StandardTable from "@/components/table/StandardTable";
export default {
  // name: "RepresentativeForm",
  // 候选人申报表
  // components: { StandardTable, additionSignIn },
  // 引入分页器配置
  mixins: [myPagination],
  props: {
    procInstId: {
      type: String,
    },
  },
  data () {
    return {
      TBloading: false,
      isShowis: true,
      newIDs: '',
      //ID
      // procInstId: "",
      //IDS
      ids: [],
      visible: false,

      columns: [
        {
          title: "状态",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "status",
        },
        {
          title: "处理人",
          align: "center",
          width: 120,
          ellipsis: true,
          dataIndex: "assignee",
        },
        {
          title: "处理环节",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "name",
        },
        {
          title: "开始时间",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "startTime",
        },
        {
          title: "完成时间",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "endTime",
        },
        {
          title: "处理意见",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "comment",
        },



      ],
      dataSource: [

      ],
      dataSourceList: [],
      selectedRows: [],
      isselectedRows: [],
    };
  },
  created () {
    // this.$store.dispatch("navigation/breadcrumb1", "候选人管理");
    // this.$store.dispatch("navigation/breadcrumb2", "补选结果");
    // this.fetchData();
  },
  computed: {

    //复选框
    rowSelection () {
      // return {
      //     onChange: (selectedRowKeys, selectedRows) => {
      //         console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
      //         if (selectedRows.length > 1) {
      //             return this.$baseMessage("只能选择一条数据", "error");
      //         } else {
      //             this.dataSourceList = selectedRows
      //         }
      //     },
      //     getCheckboxProps: record => ({
      //         props: {
      //             disabled: record.name === 'Disabled User', //
      //             name: record.name,
      //         },
      //     }),
      // };
    },

  },
  methods: {

    // 搜索
    search () {
      // this.queryForm.pageNum = 1
      // this.pagination.current = 1
      // this.fetchData()
    },
    //选择
    xuanze (data) {
      console.log(data);
      this.visible = true
      getgetHistoryTasksApi(data).then(res => {
        if (res.data.code == '0000') {
          this.dataSource = res.data.data || []
          // console.log(this.dataSource, ' this.dataSource z');
          // this.pagination.total = res.data.total;
        }
      })

    },
    //取消
    closeAdd () {
      this.visible = false
    },
    //关闭
    close () {
      this.visible = false
    },
    // 获取数据  回显
    fetchData () {
      if (this.newIDs != '') {
        let id = this.newIDs
        getgetHistoryTasksApi(id).then(res => {
          if (res.data.code == '0000') {
            this.dataSource = res.data.data || []
            // console.log(this.dataSource, ' this.dataSource z');
            // this.pagination.total = res.data.total;
          }
        })
      }
    },

  },
  watch: {

    procInstId: {
      immediate: true,    // 这句重要  立即执行handler里面的方法
      handler (val) {
        this.newIDs = this.procInstId
        this.fetchData()
      }
    }
  }
};
</script>

<style>
</style>
