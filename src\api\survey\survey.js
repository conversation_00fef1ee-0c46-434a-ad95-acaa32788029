import request from "@/utils/requestTemp";
// import request from "@/utils/surveyRequestTemp";//无token

import { data } from "jquery";

// 查询问卷列表
export function listSurvey (query) {
  return request({
    url: "/survey/survey/list",
    method: "get",
    params: query,
  });
}

// 查询问卷详细
export function getSurvey (surveyId) {
  return request({
    url: "/survey/survey/" + surveyId,
    method: "get",
  });
}

// 查询问卷发送范围详细
export function getSurveyFWhuixian (params) {
  return request({
    url: "/survey/survey/huixian",
    method: "get",
    params,
  });
}

// 新增问卷
export function addSurvey (data) {
  return request({
    url: "/survey/survey",
    method: "post",
    data: data,
  });
}

// 修改问卷
export function updateSurvey (data) {
  return request({
    url: "/survey/survey",
    method: "put",
    data: data,
  });
}

// 删除问卷
export function removeSurvey (surveyId) {
  return request({
    url: "/survey/survey/remove/" + surveyId,
    method: "put",
  });
}

// 永久删除问卷
export function delSurvey (surveyId) {
  return request({
    url: "/survey/survey/" + surveyId,
    method: "delete",
  });
}

// 发布问卷
export function publishSurvey (surveyId) {
  return request({
    url: "/survey/survey/publish/" + surveyId,
    method: "put",
  });
}

// 撤销发布问卷
export function revokeSurvey (surveyId) {
  return request({
    url: "/survey/survey/revoke/" + surveyId,
    method: "put",
  });
}

// 还原问卷
export function restoreSurvey (surveyId) {
  return request({
    url: "/survey/survey/restore/" + surveyId,
    method: "put",
  });
}

// 导出问卷
export function exportSurvey (query) {
  return request({
    url: "/survey/survey/export",
    method: "get",
    params: query,
  });
}

// 发布问卷+发送范围
export function publishRange (data) {
  return request({
    url: "/survey/survey/publishRange",
    method: "post",
    data: data,
  });
}

// 提交答案
export function SubmitAnswer (data, params) {
  return request({
    url: "/survey/answer",
    method: "post",
    data: data,
    params,
  });
}
// 提交答案完请求
// /survey/survey/write/{surveyIds}
export function Write (surveyIds, data) {
  console.log("data", data)
  return request({
    url: "/survey/survey/write/" + surveyIds,
    method: "put",
    // params:data,
    data: data
  });
}

// 查看答案
export function seeOwnAnswer (params) {
  return request({
    url: "/survey/answer/seeOwnAnswer",
    method: "get",
    params: params,
  });
}

// 查看个人问卷接口
export function ownsurveylist (query) {
  return request({
    // url: "survey/survey/ownsurveylist",
    url: "survey/survey/currentUserQuestionnaireList",
    method: "get",
    params: query,
  });
}
// 结束调查问卷

export function stopSurvey (surveyIds) {
  return request({
    url: "/survey/survey/stopSurvey",
    method: "get",
    params: surveyIds,
    data: surveyIds,
  });
}

export function exportWordApi (surveyId, personIds) {
  var obj = {
    personIds,
  };
  return request({
    url: "/survey/survey/exportWord",
    method: "post",
    params: { surveyId },
    data: personIds,
    responseType: "blob",
  });
}
