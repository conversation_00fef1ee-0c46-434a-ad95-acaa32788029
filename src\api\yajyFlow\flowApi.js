import { instance_yajy } from "@/api/axiosRq";

//根据节点对应主表状态值获取流程节点信息
export function getprocessApi(form) {
  return instance_yajy({
    url: `/api/v1/proposal/processEngine/getFlowNodeInfo`,
    method: "post",
    params: form,
  });
}

//获取对应操作人
export function getgetNextUsersApi(form) {
  return instance_yajy({
    url: `/api/v1/proposal/processEngine/getNextOperInfo`,
    method: "post",
    params: form,
  });
}

//获取所有流程节点信息
export function getAllFlowNode(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/processEngine/getAllFlowNodeInfo`,
    method: "post",
    params: { proposalId },
  });
}

export function getAllFlowNodeOther(proposalId) {
  return instance_yajy({
    url: `/api/v1/proposal/processEngine/getAllFlowNodeInfoOther`,
    method: "post",
    params: { proposalId },
  });
}

//退回处理
export function doReturn(form) {
  return instance_yajy({
    url: `/api/v1/proposal/suggestion/doReturn`,
    method: "post",
    params: form,
  });
}
