<template>
  <FormItem
    :label="label"
    :prop="prop || startProp"
    :span="span"
    :xs="xs"
    :sm="sm"
    :md="md"
    :lg="lg"
    :xl="xl"
    :xxl="xxl"
  >
    <a-range-picker
      :value="input"
      :format="formatPrimary"
      :show-time="type === 'datetime'"
      :allow-clear="allowClear"
      :ranges="ranges"
      style="width: 100%"
      @change="onChange"
    />
  </FormItem>
</template>

<script>
import FormItem from "@/components/FormItem/index.vue";
import moment from "moment/moment";

/**
 * 表单范围选择器
 * 1. 日期 <FormRangePicker v-model="listQuery" label="时间范围" start-prop="startTime" end-prop="endTime" />
 * 2. 日期时间 <FormRangePicker v-model="listQuery" label="时间范围" start-prop="startTime" end-prop="endTime" type="datetime" />
 */
export default {
  name: "FormRangePicker",
  components: { FormItem },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    // <a-input v-model="" />
    value: {
      type: Object,
      required: true,
    },
    // 验证使用， 默认 startProp, <a-form-model-item prop="" />
    prop: {
      type: String,
      default: undefined,
    },
    label: {
      type: String,
      required: true,
      default: "",
    },
    // <a-form-model-item prop="" />
    startProp: {
      type: String,
      required: true,
    },
    // <a-form-model-item prop="" />
    endProp: {
      type: String,
      required: true,
    },
    // <a-col span="" />
    span: {
      type: [Number, String],
      default: undefined,
    },
    xs: {
      type: [Number, String],
      default: undefined,
    },
    sm: {
      type: [Number, String],
      default: undefined,
    },
    md: {
      type: [Number, String],
      default: undefined,
    },
    lg: {
      type: [Number, String],
      default: undefined,
    },
    xl: {
      type: [Number, String],
      default: undefined,
    },
    xxl: {
      type: [Number, String],
      default: undefined,
    },
    // 级联标题
    cascadeLabel: {
      type: [String, Boolean],
      required: false,
      default: false,
    },
    // <a-input :allow-clear="false" />
    allowClear: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: undefined,
    },
    // 输入框类型 datetime | date
    // todo 未完成 time | year | money | number
    type: {
      type: String,
      default: "date",
    },
    format: {
      type: String,
      default: undefined,
    },
    // 值转换
    valueFormat: {
      type: String,
      default: undefined,
    },
    ranges: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    input() {
      const range = [this.value[this.startProp], this.value[this.endProp]];
      return range.map((date) => {
        if (!date) return undefined;
        const val = moment(date, this.valueFormatPrimary);
        return val.isValid() ? val : undefined;
      });
    },
    valueFormatPrimary() {
      if (this.valueFormat) return this.valueFormat;
      switch (this.type) {
        case "date":
          return "YYYY-MM-DD";
        case "time":
          return "HH:mm:ss";
        case "year":
          return "YYYY";
        case "month":
          return "M";
        case "datetime":
        default:
          return "YYYY-MM-DD HH:mm:ss";
      }
    },
    formatPrimary() {
      if (this.format) return this.format;
      return this.valueFormatPrimary;
    },
  },
  methods: {
    onChange(range) {
      const value = {
        ...this.value,
        [this.startProp]: range?.[0]?.format(this.valueFormatPrimary),
        [this.endProp]: range?.[1]?.format(this.valueFormatPrimary),
      };
      this.$emit("update:value", value);
      this.$emit("change", value);
    },
  },
};
</script>

<style lang="scss" scoped></style>
