import Layout from "@/layouts";

// {
//   path: "staging",
//   name: "InformStaging",
//   fUrl: "meeting/testPage/index",
//   meta: {
//     title: "待办任务",
//     permissions: ["I_XTGLY", "I_XXFBY"],
//   },
// },
// 平台门户
export default [
  {
    path: "/",
    component: Layout,
    redirect: "/index",
    children: [
      {
        path: "/index",
        name: "Index",
        fUrl: "login/list",
        meta: {
          title: "首页",
          icon: "database",
          affix: false,
        },
      },
    ],
  },
  {
    path: "/portalIndex",
    component: Layout,
    name: "portalIndex",
    redirect: "/portalIndex/index",
    title: "",
    meta: {
      title: "门户首页",
    },
    children: [
      {
        path: "index",
        name: "index",
        hidden: true,
        fUrl: "portalIndex/index",
        meta: {
          title: "首页",
        },
      },
    ],
  },
  {
    path: "/representative",
    component: Layout,
    redirect: "noRedirect",
    name: "representative",
    title: "平台门户",
    meta: {
      title: "常用服务",
      permissions: [
        "I_XTGLY",
        "I_DBSLXGLY",
        "I_XXFBY",
        "I_JSQAPFBY",
        "I_DBHDGLY",
        "I_MHWYGLY",
        "I_YJZJGLY",
        "I_DBLXYSY",
        "I_SSPGLY",
        "I_MHWY(LLY)SHY",
        "I_SSP(LLZ)SHY",
        "I_QRDGZRY",
        "I_SXXFBY",
        "I_QXXFBY",
        "I_SXXFBGLY",
        "I_QXXFBGLY",
      ],
    },
    alwaysShow: false,
    children: [
      {
        path: "InformList",
        name: "InformList",
        fUrl: "meeting/inform/list",
        meta: {
          title: "通知公告列表",
          permissions: [
            "I_XTGLY",
            "I_SXXFBY",
            "I_QXXFBY",
            "I_SXXFBGLY",
            "I_QXXFBGLY",
          ],
        },
      },
      {
        path: "Newlist",
        name: "newInformList",
        fUrl: "meeting/inform/Newlist",
        meta: {
          title: "区人大通知公告列表",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
      },
      {
        path: "/inform/edit",
        name: "InformEdit",
        fUrl: "meeting/inform/edit",
        meta: {
          title: "新增通知公告",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/inform/edit2",
        name: "InformEdit",
        fUrl: "meeting/inform/edit2",
        meta: {
          title: "新增通知公告2",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/inform/Data",
        name: "InformEditStatistics",
        fUrl: "meeting/inform/data",
        meta: {
          title: "查看详情",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/inform/statistics",
        name: "InformStatistics",
        fUrl: "meeting/inform/statisticsList",
        meta: {
          title: "通知统计",
          permissions: ["I_XTGLY", "I_XXFBY", "I_QRDGZRY"],
        },
        hidden: true,
      },

      {
        path: "OpinionList",
        name: "OpinionList",
        fUrl: "meeting/opinion/list",
        meta: {
          title: "意见征集列表",
          permissions: ["I_XTGLY", "I_YJZJGLY"],
        },
      },
      {
        path: "Newlist1",
        name: "NewOpinionList",
        fUrl: "meeting/opinion/Newlist",
        meta: {
          title: "区人大意见征集列表",
          permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
        },
      },
      {
        path: "/opinion/edit",
        name: "OpinionEdit",
        fUrl: "meeting/opinion/edit",
        meta: {
          title: "新增意见征集",
          permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/opinion/tongji",
        name: "OpinionTongji",
        fUrl: "meeting/opinion/tongji",
        meta: {
          title: "征集统计",
          permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "/opinion/data",
        name: "OpinionData",
        fUrl: "meeting/opinion/data",
        meta: {
          title: "意见详情",
          permissions: ["I_XTGLY", "I_YJZJGLY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "suishouList",
        name: "suishouList",
        fUrl: "meeting/suishou/index",
        meta: {
          title: "我的随手拍",
          permissions: ["I_XTGLY", "I_SSPGLY"],
        },
      },
      {
        path: "QUsuishouList",
        name: "QUsuishouList",
        fUrl: "meeting/suishou/NewList",
        meta: {
          title: "区人大随手拍",
          permissions: ["I_XTGLY", "I_SSPGLY", "I_QRDGZRY"],
        },
      },
      {
        path: "/suishou/details",
        name: "suishouDetails",
        fUrl: "meeting/suishou/details",
        meta: {
          title: "随手拍详情",
          permissions: ["I_XTGLY", "I_SSPGLY", "I_SSP(LLZ)SHY", "I_QRDGZRY"],
        },
        hidden: true,
      },
      {
        path: "lianluo",
        name: "lianluosuishouList",
        fUrl: "meeting/suishou/lianluo_index",
        meta: {
          title: "联络站随手拍",
          permissions: ["I_XTGLY", "I_SSPGLY", "I_SSP(LLZ)SHY"],
        },
      },
      {
        path: "callMe",
        name: "callMe",
        fUrl: "meeting/callMe/list",
        meta: {
          title: "民呼我应列表",
          permissions: ["I_XTGLY", "I_MHWYGLY"],
        },
      },
      {
        path: "NewcallMe",
        name: "callMe",
        fUrl: "meeting/callMe/Newlist",
        meta: {
          title: "区人大民呼我应列表",
          permissions: ["I_XTGLY", "I_MHWYGLY", "I_QRDGZRY"],
        },
      },
      {
        path: "checkList",
        name: "checkList",
        fUrl: "meeting/callMe/checkList",
        meta: {
          title: "民呼我应预审列表",
          permissions: ["I_XTGLY", "I_MHWYGLY", "I_DBLXYSY"],
        },
      },
      {
        path: "censorList",
        name: "censorList",
        fUrl: "censor/index",
        meta: {
          title: "敏感词过滤",
          permissions: ["I_XTGLY", "I_MHWYGLY", "I_DBLXYSY"],
        },
      },
      {
        path: "daibiaocallMe",
        name: "daibiaocallMe",
        fUrl: "meeting/callMe/daibiaolist",
        meta: {
          title: "代表民呼我应列表",
          permissions: ["I_XTGLY", "I_MHWYGLY", "I_DBLXYSY"],
        },
        hidden: true,
      },
      {
        path: "lianluocallMe",
        name: "lianluocallMe",
        fUrl: "meeting/callMe/lianluolist",
        meta: {
          title: "联络站民呼我应列表",
          permissions: ["I_XTGLY", "I_MHWY(LLY)SHY"],
        },
      },
      {
        path: "/statistics/callMeDetails",
        name: "callMe",
        fUrl: "meeting/callMe/details",
        meta: {
          title: "民呼我应详情",
          permissions: [
            "I_XTGLY",
            "I_MHWYGLY",
            "I_DBLXYSY",
            "I_MHWY(LLY)SHY",
            "I_QRDGZRY",
          ],
        },
        hidden: true,
      },
      {
        path: "/statistics/checkDetails",
        name: "checkDetails",
        fUrl: "meeting/callMe/checkDetails",
        meta: {
          title: "民呼我应审核详情",
          permissions: ["I_XTGLY", "I_MHWYGLY", "I_DBLXYSY", "I_MHWY(LLY)SHY"],
        },
        hidden: true,
      },
      // { // 去掉  改成在履职活动组织-所有活动列表
      //   path: "huoDongList",
      //   name: "huoDongList",
      //   fUrl: "meeting/huoDong/index",
      //   meta: {
      //     title: "代表活动列表",
      //     permissions: ["I_XTGLY", "I_DBHDGLY"],
      //   },
      // },
      // { // 去掉  改成在履职活动组织-区人大所有活动列表
      //   path: "QUList",
      //   name: "QUhuoDongList",
      //   fUrl: "meeting/huoDong/QUindex",
      //   meta: {
      //     title: "区人大代表活动列表",
      //     permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY"],
      //   },
      // },

      {
        path: "/huoDong/baoming",
        name: "huoDongbaoming",
        fUrl: "meeting/huoDong/baoming",
        meta: {
          title: "活动报名统计",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/add",
        name: "huoDongbaoming",
        fUrl: "meeting/huoDong/add",
        meta: {
          title: "新增活动",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      // {
      //   path: "/huoDong/newDetail",
      //   name: "huoDongbaoming2",
      //   fUrl: "meeting/huoDong/newDetail",
      //   meta: {
      //     title: "新增活动2",
      //     permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
      //   },
      //   hidden: true,
      // },
      {
        path: "/huoDong/details",
        name: "huoDongdetails",
        fUrl: "meeting/huoDong/details",
        meta: {
          title: "活动详情",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/Newedit",
        name: "huoDongdetails",
        fUrl: "meeting/huoDong/Newedit",
        meta: {
          title: "活动编辑",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/edit",
        name: "huoDongdetails",
        fUrl: "meeting/huoDong/edit",
        meta: {
          title: "活动详情",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/edit2",
        name: "huoDongdetails2",
        fUrl: "meeting/huoDong/edit2",
        meta: {
          title: "活动详情2",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/showStyle",
        name: "huoDongdeShowStyle",
        fUrl: "meeting/huoDong/showStyle",
        meta: {
          title: "查看样式",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      {
        path: "/huoDong/newDetail",
        name: "details",
        fUrl: "meeting/huoDong/newDetail",
        meta: {
          title: "详情",
          permissions: ["I_XTGLY", "I_DBHDGLY", "I_QRDGZRY", "I_JCYH"],
        },
        hidden: true,
      },
      // {
      //   path: "/huoDong/attendance",
      //   name: "attendance",
      //   fUrl: "meeting/huoDong/attendance",
      //   meta: {
      //     title: "出勤情况",
      //     permissions: ["I_QRDGZRY"],
      //   },
      //   hidden: true,
      // },
      {
        path: "list",
        name: "communityscheduleList",
        fUrl: "communityschedule/list",
        meta: {
          title: "代表进社区活动列表",
          icon: "database",
          permissions: ["I_XTGLY", "I_JSQAPFBY"],
        },
      },
      {
        path: "memberList",
        name: "MemberList",
        fUrl: "communityschedule/memberList",
        hidden: true,
        meta: {
          title: "进社区代表列表",
          permissions: ["I_XTGLY", "I_JSQAPFBY"],
        },
      },
      {
        path: "dbslEdit",
        name: "DbslEdit",
        fUrl: "dbsl/rel",
        meta: {
          title: "代表双联",
          permissions: ["I_XTGLY", "I_DBSLXGLY"],
        },
      },
      {
        path: "dbslList",
        name: "DbslList",
        fUrl: "dbsl/list",
        meta: {
          title: "代表双联列表",
          permissions: ["I_XTGLY", "I_DBSLXGLY"],
        },
      },
      {
        path: "Suggestions",
        name: "Suggestions",
        fUrl: "meeting/commonly/Suggestions",
        meta: {
          title: "建议办理",
          permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },
      {
        path: "BudgetAuditing",
        name: "BudgetAuditing",
        fUrl: "meeting/commonly/BudgetAuditing",
        meta: {
          title: "预算审核监督",
          permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },
      {
        path: "TrainingStudy",
        name: "TrainingStudy",
        fUrl: "meeting/commonly/TrainingStudy",
        meta: {
          title: "在线学习培训",
          permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },
      {
        path: "Imeeting",
        name: "Imeeting",
        fUrl: "meeting/commonly/Imeeting",
        meta: {
          title: "i会议",
          permissions: ["I_XTGLY", "I_XXFBY"],
        },
      },
    ],
  },
  // { // 暂时隐藏
  //   path: "/serviceZone",
  //   component: Layout,
  //   redirect: "noRedirect",
  //   name: "serviceZone",
  //   title: "平台门户",
  //   meta: {
  //     title: "代表首页 (服务专区)",
  //   },
  //   children: [
  //     {
  //       path: "serviceZone",
  //       name: "serviceZone",
  //       fUrl: "meeting/testPage/index",
  //       meta: {
  //         title: "立法工作专区",
  //       },
  //     },
  //     {
  //       path: "serviceZone1",
  //       name: "serviceZone1",
  //       fUrl: "meeting/testPage/index",
  //       meta: {
  //         title: "联网监督专区",
  //       },
  //     },
  //     {
  //       path: "serviceZone2",
  //       name: "serviceZone2",
  //       fUrl: "meeting/testPage/index",
  //       meta: {
  //         title: "个人履职专区",
  //       },
  //     },
  //     {
  //       path: "serviceZone3",
  //       name: "serviceZone3",
  //       fUrl: "meeting/testPage/index",
  //       meta: {
  //         title: "会议服务专区",
  //       },
  //     },
  //   ],
  // },

  {
    path: "/staff",
    component: Layout,
    redirect: "noRedirect",
    name: "staff",
    title: "平台门户",
    meta: {
      title: "工作人员首页",
    },
    children: [
      {
        path: "staffIndex",
        name: "staffIndex",
        fUrl: "staff/staffIndex",
        meta: {
          title: "首页",
        },
      },
      {
        path: "Management",
        name: "Management",
        fUrl: "staff/Management",
        meta: {
          title: "管理",
        },
      },
      {
        path: "My",
        name: "My",
        fUrl: "staff/My",
        meta: {
          title: "我的",
        },
      },
    ],
  },
];
