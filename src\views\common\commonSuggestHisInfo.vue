<template>
  <a-modal :visible="visible"
           :title="showOperate ? '处理建议' : '建议详情'"
           width="80%"
           :centered="true"
           :footer="null"
           @cancel="close"
           :bodyStyle="{ maxHeight: '600px', overflowY: 'auto' }">
    <a-tabs type="card"
            v-model="tabsKey"
            @change="changeTab">
      <a-tab-pane key="1"
                  tab="议案建议信息">
        <div :style="tabStyle">
          <a-descriptions bordered
                          size="small">
            <a-descriptions-item label="标题"
                                 :span="3">{{
              record.proposalTitle
            }}</a-descriptions-item>
            <a-descriptions-item label="建议号">{{
              record.proposalNum
            }}</a-descriptions-item>
            <a-descriptions-item label="类别">{{
              record.proposalType | filterProposalType
            }}</a-descriptions-item>
            <a-descriptions-item label="领衔代表">{{
              record.headPer
            }}</a-descriptions-item>
            <!-- 电话 短信 邮件 微信：0或1或4      2  座谈会  调研    3   代表表示无需见面沟通 -->
            <a-descriptions-item label="正文">
              <a-space>
                <a @click="showFile(record, 's')">查看正文</a>
              </a-space>
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions title="主办单位"
                          v-if="majorUndertakeDfList.length > 0"></a-descriptions>
          <template v-if="majorUndertakeDfList.length > 0"
                    v-for="item in majorUndertakeDfList">
            <a-descriptions size="small"
                            bordered
                            class="mt2"
                            :key="item.undertakeId"
                            :column="{ xxl: 8, xl: 8, lg: 8, md: 4, sm: 4, xs: 1 }">
              <a-descriptions-item label="单位名称"
                                   :span="8">{{
                item.uname
              }}</a-descriptions-item>
              <a-descriptions-item label="答复时间"
                                   :span="3">{{
                item.dfrq
              }}</a-descriptions-item>
              <a-descriptions-item label="答复类型"
                                   :span="3">{{
                item.dflb
              }}</a-descriptions-item>
              <a-descriptions-item label="答复内容"
                                   :span="2"><a v-show="item.pdfContent"
                   @click="showFile(item, 'd')">【查看答复】</a></a-descriptions-item>
              <a-descriptions-item label="反馈时间"
                                   v-if="item.submitTime"
                                   :span="3">{{ item.submitTime }}</a-descriptions-item>
              <a-descriptions-item label="反馈意见"
                                   v-if="item.evaluationValue"
                                   :span="3">{{
                  item.evaluationValue | evaluationValueFilter
                }}</a-descriptions-item>
              <a-descriptions-item label="反馈内容"
                                   v-if="item.evaluationValue"
                                   :span="2"><a v-if="item.filterString"
                   @click="showFile(item, 'f')">【查看反馈】</a></a-descriptions-item>
            </a-descriptions>
          </template>
          <a-descriptions title="会办单位"
                          v-if="minorUndertakeDfList.length > 0"></a-descriptions>
          <template v-if="minorUndertakeDfList.length > 0"
                    v-for="item in minorUndertakeDfList">
            <a-descriptions size="small"
                            bordered
                            :key="item.undertakeId"
                            :column="{ xxl: 8, xl: 8, lg: 8, md: 4, sm: 4, xs: 1 }">
              <a-descriptions-item label="单位名称"
                                   :span="8">{{
                item.uname
              }}</a-descriptions-item>
              <a-descriptions-item label="答复时间"
                                   :span="3">{{
                item.dfrq
              }}</a-descriptions-item>
              <a-descriptions-item label="答复类型"
                                   :span="3">{{
                item.dflb
              }}</a-descriptions-item>
              <a-descriptions-item label="答复内容"
                                   :span="2"><a v-show="item.pdfContent"
                   @click="showFile(item, 'd')">【查看答复】</a></a-descriptions-item>
              <a-descriptions-item label="反馈时间"
                                   v-if="item.submitTime"
                                   :span="3">{{ item.submitTime }}</a-descriptions-item>
              <a-descriptions-item label="反馈意见"
                                   v-if="item.evaluationValue"
                                   :span="3">{{
                  item.evaluationValue | evaluationValueFilter
                }}</a-descriptions-item>
              <a-descriptions-item label="反馈内容"
                                   v-if="item.evaluationValue"
                                   :span="2"><a v-if="item.filterString"
                   @click="showFile(item, 'f')">【查看反馈】</a></a-descriptions-item>
            </a-descriptions>
          </template>
        </div>
      </a-tab-pane>
    </a-tabs>
    <jbrTable ref="jbrTable"
              @emitJbrTable="getJbrTable"></jbrTable>
    <evaluationModal ref="evaluationModal"
                     @emitClose="close"></evaluationModal>
    <adjustListEdit ref="adjustListEdit"
                    @close="close"></adjustListEdit>
    <a-modal width="70%"
             v-model="showFileVisible"
             title="查看内容"
             @cancel="showFileVisible = false"
             :footer="null">
      <p v-html="showFileContent"></p>
    </a-modal>
  </a-modal>
</template>
<script>
import commonUnitShuttleTable from "@/views/common/commonUnitShuttleTable.vue";
import evaluationModal from "@/views/meeting/suggesting/evaluationModal.vue";
import commonUserTable from "@/views/common/commonUserTable.vue";
import jbrTable from "@/views/common/jbrTable.vue";
import { fileUpload } from "@/api/commonApi/file.js";
import adjustListEdit from "@/views/meeting/suggesting/adjustListEdit.vue";
import {
  proposalHisById,
  doAssign,
  doAudit,
  doCheck,
  getJointlyList,
  addJointly,
  delJointly,
  againProduce,
  getLocalHistory,
  getProposalUnder,
  doReply,
  getDelayUndertakeListById,
  doDelay,
  getReviseUndertakeListById,
  getAdjustMinorUndertakeListById,
  keepRevise,
  keepDelay,
  seeText,
  getUndertakeInfo,
  doRevise,
  getJbrList,
  updateJbr,
  getUndertakeDfAndFeedbackList,
  againUploadTextFile,
  downloadProposal,
  saveCBDW,
  doSignedSave,
  getUndertakeStatus,
  getUndertakeDfInfo,
  applyDelay,
  showContent,
} from "@/api/myJob/myProposal.js";

import { downFile, getUserData_yajy } from "@/api/commonApi/file.js";
export default {
  components: {
    commonUnitShuttleTable,
    commonUserTable,
    evaluationModal,
    adjustListEdit,
    jbrTable,
  },
  data () {
    return {
      tabStyle: {}, // 对话框中的 tab 内容高度及垂直滚动条设置
      visible: false,
      proposalId: "", //父组件直接赋值 议题id
      fkVisible: false,
      fkEvaluation: "",
      fkContent: "",
      majorUndertakeDfList: [],
      minorUndertakeDfList: [],
      record: { unIfmainList: [], ifmainList: [] },
      userData: {},
      resourceForm: {
        meetName: "",
        meet: "",
        host: "",
        hostName: "",
        adjust: "",
        adjustName: "",
        isPublic: "",
        dffs: [],
        jbrName: "",
        jbrDept: "",
        jbrJob: "",
        jbrPhone: "",
        jbrOph: "",
        dflb: null,
        orgCode: "",
        mainHandleLastTimeStr: "",
        minorHandleLastTimeStr: "",
        contentType: "",
      },
      rules: {
        jbrName: [
          { required: true, message: "请输入经办人姓名", trigger: "blur" },
        ],
        jbrDept: [
          { required: true, message: "请输入经办人部门", trigger: "blur" },
        ],
        jbrJob: [
          { required: true, message: "请输入经办人职务", trigger: "blur" },
        ],
        jbrPhone: [
          { required: true, message: "请输入经办人手机", trigger: "blur" },
        ],
        jbrOph: [
          { required: true, message: "请输入经办人办公电话", trigger: "blur" },
        ],
        dflb: [
          { required: true, message: "请输入答复类型", trigger: "change" },
        ],
        dffs: [
          { required: true, message: "请输入沟通方式", trigger: "change" },
        ],
        isPublic: [
          { required: true, message: "请输入答复是否公开", trigger: "change" },
        ],
      },
      unitColumns: [
        // 承办单位表格头定义
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "ifmain",
          customRender: (text, record, index) =>
            text == 1 ? "主办单位" : "会办单位",
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
      ],
      unitDataSource: [
        // 承办单位表格数据
      ],
      processColumns: [
        // 流程跟踪表格头定义
        {
          title: "状态",
          ellipsis: true,
          dataIndex: "replyType",
        },
        {
          title: "处理人",
          ellipsis: true,
          dataIndex: "reply_by_name",
        },
        {
          title: "处理时间",
          ellipsis: true,
          dataIndex: "replyTime",
        },
        {
          title: "办理意见",
          ellipsis: true,
          dataIndex: "replyComment",
        },
      ],
      processDataSource: [
        // 流程跟踪数据
      ],
      deputationColumns: [
        // 提出代表表格头定义
        {
          title: "类别",
          ellipsis: true,
          dataIndex: "ifpublic",
          customRender: (text, record, index) => {
            if (text == 1) return "领衔";
            if (text == 0) return "联名";
          },
        },
        {
          title: "代表姓名",
          ellipsis: true,
          dataIndex: "name",
        },
        {
          title: "通讯地址",
          ellipsis: true,
          dataIndex: "unitAddress",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "unitPhone",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "mobilePhone",
        },
        {
          title: "Email",
          ellipsis: true,
          dataIndex: "",
        },
        {
          title: "邮政编码",
          ellipsis: true,
          dataIndex: "",
        },
        {
          title: "申请时间",
          ellipsis: true,
          dataIndex: "createTime",
        },
        {
          title: "反馈时间",
          ellipsis: true,
          dataIndex: "agreen",
        },
        {
          title: "联名状态",
          ellipsis: true,
          dataIndex: "ifagreen",
          customRender: (text, record, index) => {
            if (text == 1) return "同意";
            if (text == 0) return "不同意";
            if (text == null) return "未处理";
          },
        },
      ],
      // 提出列表table
      deputationDataSource: [],
      // 提出列表选中数据
      deputationSelectedRows: [],
      unitType: "", //单位类型
      returnValue: false,
      tabsKey: "1",
      fileTextList: [], //答复附件
      perType: "", //打开代表列表的场景
      scenesStatus: "", // 场景默认是 处理延期
      dataSourcePostpone: [], //处理延期数据
      selectedRowsPostpone: [], //处理延期数据-已选
      columnsPostpone: [
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "",
          customRender: (text, record, index) => {
            return record.ifmain == 1 ? "主办单位" : "会办单位";
          },
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "申请时间",
          ellipsis: true,
          dataIndex: "applyDelayTime",
        },
        {
          title: "延期时长",
          ellipsis: true,
          dataIndex: "delayTimeTo",
        },
        {
          title: "调整原因",
          ellipsis: true,
          dataIndex: "applyCommnent",
        },
        {
          title: "附件名称",
          scopedSlots: { customRender: "action" },
        },
      ], //处理延期数据/单位调整 - 表头
      unitModalVisibleColumns: [
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "ifmain",
          customRender: (text, record, index) => {
            if (text == "1") return "主办单位";
            if (text == "0") return "会办单位";
          },
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
        {
          title: "签收状态",
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            if (text == 0) return "调整中";
            if (text == 1) return "签收中";
            if (text == 2) return "答复中";
            if (text == 3) return "待反馈";
          },
        },
      ], //弹出的单位调整列表
      undertakeId: "", //子流程id
      visiblePostpone: false,
      keepType: "",
      showOperate: true, //是否显示操作 默认显示
      modalTitle: "处理延期", //延期/调整弹出框
      unitModalVisible: false, //单位调整弹出框
      unitModalVisibleSelectedRows: [], //单位调整已选数据
      unitModalVisibleDataSource: [], //单位调整列表
      changeIfMainVisible: false, //单位调整弹出框 修改主办/会办
      fileVisible: false, //重新上传文件窗口开关
      fileTextListReset: [], //重新上传文件列表
      contentTypeList: [
        // 内容分类
        { id: "JYFL01", name: "法制" },
        { id: "JYFL02", name: "监察和司法" },
        { id: "JYFL09", name: "预算" },
        { id: "JYFL03", name: "经济" },
        { id: "JYFL04", name: "城建环资" },
        { id: "JYFL05", name: "农村农业" },
        { id: "JYFL06", name: "教科文卫" },
        { id: "JYFL07", name: "华侨外事民族宗教" },
        { id: "JYFL10", name: "社会建设" },
        { id: "JYFL08", name: "其他" },
      ],
      jointlyId: "", //联名id
      undertakeStatus: false, //显示反馈的依据，获取子流程状态
      delayVisible: false,
      hbDataSource: [], //会办数据
      minorLastTime: "",
      majorLastTime: "",
      fileTextList_7: [],
      fileTextList_8: [],
      delayForm: { delayDay: "", comment: "" },
      defaultUnit: [],
      // 申请调整
      changeIfMainTitle: "修改承办类别",
      changeIfMainForm: {},
      // 联名按钮loading
      lmLoading: false,
      // 交办按钮loading
      jbLoading: false,
      // 校核按钮loading
      jhLoading: false,
      // 审核按钮loading
      shLoading: false,
      // 答复按钮loading
      sfLoading: false,
      showFileContent: "",
      showFileVisible: false,
    };
  },

  watch: {
    visible (val) {
      if (val) {
        this.getOneUser();
      }
    },
  },
  filters: {
    evaluationValueFilter (text) {
      console.log("🤗🤗🤗, text =>", text);
      // 1为满意,2为一般,3为不满意,-2为数据缺失
      if (text === 1) {
        return "满意";
      }
      if (text === 2) {
        return "基本满意";
      }
      if (text === 3) {
        return "不满意";
      }
      if (text === -2) {
        return "数据缺失";
      }
    },
    filterProposalContentType (text) {
      if (text == "JYFL01") return "法制";
      if (text == "JYFL02") return "监察和司法";
      if (text == "JYFL03") return "经济";
      if (text == "JYFL04") return "城建环资";
      if (text == "JYFL05") return "农村农业";
      if (text == "JYFL06") return "教科文卫";
      if (text == "JYFL07") return "华侨外事民族宗教";
      if (text == "JYFL08") return "其他";
      if (text == "JYFL09") return "预算";
      if (text == "JYFL10") return "社会建设";
    },
    filterProposalType (text) {
      if (text == "1") return "大会议案";
      if (text == "2") return "大会建议";
      if (text == "3") return "闭会建议";
      if (text == "4") return "供参考建议";
    },
  },
  methods: {
    // 显示答复，反馈，正文
    showFile (item, type) {
      let form = {};
      if (type == "s") {
        form.id = item.proposalId;
      } else {
        form.id = item.dfId;
      }
      form.type = type;
      showContent(form).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.showFileVisible = true;
          this.showFileContent = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 校核弹窗 关闭
    cancelReturn () {
      this.returnValue = false;
      this.resourceForm.comment = "";
    },
    // 正式提交
    formallySubmit () {
      this.$router.push({
        path: "/meSuggestv/addSuggestv",
        query: {
          showUser: false,
          proposalId: this.proposalId,
        },
      });
    },
    // 取消调整
    cancelUnitModal () {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "如果关闭当前页面，将会丢失已编辑的承办单位，确认要关闭?",
        onOk: () => {
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 申请调整
    adjust () {
      this.$refs.adjustListEdit.modalVisible = true;
      this.$refs.adjustListEdit.proposalId = this.proposalId;
    },
    // 申请延期 保存
    adjustSubmit () {
      let form = {};
      form.comment = this.delayForm.comment;
      form.delayDay = this.delayForm.delayDay;
      form.proposalId = this.proposalId;
      this.$refs["delayForm"].validate((valid) => {
        if (valid) {
          // 申请延期
          applyDelay(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.cancelModal();
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 关闭窗口
    cancelModal () {
      this.delayVisible = false;
      this.delayForm = { delayDay: "", comment: "" };
      this.delayFileTextList = [];
    },
    // 申请延期
    postpone () {
      this.delayVisible = true;
    },
    // 处理联名
    changeJoin (status) {
      this.lmLoading = true;
      let form = {};
      form.jointlyid = this.jointlyId;
      form.state = status;
      doSignedSave(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作完成");
          this.close();
        } else {
          this.$message.error(res.data.data);
        }
        this.lmLoading = false;
      });
    },
    // 下载建议纸
    downMotion () {
      let downFile = (res) => {
        let a = window.document.createElement("a");
        let resData = URL.createObjectURL(res.data);
        a.href = resData;
        let proposalNum = this.record.proposalNum
          ? this.record.proposalNum
          : "";
        let fileName = proposalNum + this.record.proposalTitle + "建议纸.doc";
        a.download = fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(resData);
      };
      downloadProposal({ id: this.proposalId, type: 10 }).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        let errmsg = "";
        if (res.headers.errmsg) {
          errmsg = this.$str2utf8(res.headers.errmsg);
          return this.$message.error(errmsg);
        } else {
          downFile(res);
        }
      });
    },

    //重新上传正文附件 关闭
    closeFileModal () {
      this.fileVisible = false;
      this.fileTextListReset = [];
      this.delayFileTextList = [];
      this.resourceForm = {
        meetName: "",
        meet: "",
        host: "",
        hostName: "",
        adjust: "",
        adjustName: "",
        isPublic: "",
        dffs: [],
        jbrName: "",
        jbrDept: "",
        jbrJob: "",
        jbrPhone: "",
        jbrOph: "",
      };
      this.fileTextList = [];
      this.fileTextList_7 = [];
      this.fileTextList_8 = [];
    },
    //重新上传正文附件 保存
    resetSubmit () {
      let form = {};
      form.proposalId = this.proposalId;
      form.type = "9";
      form.encrypted = false;
      form.file = this.resourceForm.file;
      againUploadTextFile(form).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.$message.success("操作完成");
          this.closeFileModal();
        } else {
          this.$message.error(res.data.data);
        }
      });
    },
    //重新上传正文附件 删除
    handleTextRemoveReset () {
      this.resourceForm.file = "";
      this.fileTextListReset = [];
      this.$baseMessage(`删除成功`, "success");
    },
    //重新上传正文附件 上传操作
    uploadChangeReset ({ file, fileList }) {
      this.resourceForm.file = "";
      this.resourceForm.file = file;
      this.fileTextListReset = fileList;
    },
    //重新上传正文附件 上传前
    beforeUploadReset (file) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      // var reg = /^.+(docx|doc)$/;
      // const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      // if (!isDoc) {
      //   this.$message.error("上传的文件格式只能是doc或docx");
      //   return;
      // }
      return false;
    },
    // 重新上传正文附件
    resetFile () {
      // 打开上传文件窗口
      this.fileVisible = true;
    },
    // 重新交办
    resetManage () {
      if (this.unitModalVisibleDataSource.length == 0) {
        return this.$message.error("承办单位不能为空");
      }
      let form = {};
      let undertakeList = [];
      this.unitModalVisibleDataSource.map((item) => {
        undertakeList.push({
          ifmain: item.ifmain,
          orgCode: item.orgCode,
          orgName: item.orgName,
          undertakeId: item.undertakeId || "",
          proposalId: this.proposalId,
        });
      });
      form.undertakeList = undertakeList;
      form.proposalId = this.proposalId;
      form.contentType = this.record.proposalContentType;
      form.proposalType = this.record.proposalType;
      doRevise(undertakeList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 单位调整弹出框 新增 打开
    openChange () {
      this.changeIfMainVisible = true;
      this.changeIfMainTitle = "添加承办单位";
    },
    // 单位调整弹出框 删除
    unitModalVisibleDel () {
      if (this.unitModalVisibleSelectedRows.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.unitModalVisibleSelectedRows.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      // 单位调整-删除逻辑
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定删除？",
        onOk: () => {
          this.unitModalVisibleDataSource = this.unitModalVisibleDataSource.filter(
            (item) =>
              item.orgCode != this.unitModalVisibleSelectedRows[0].orgCode
          );
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 单位调整弹出框 修改主办/会办 保存
    saveChangeIfMain () {
      if (this.changeIfMainTitle == "修改承办类别") {
        this.unitModalVisibleSelectedRows.forEach((item) => {
          this.unitModalVisibleDataSource.map((v) => {
            if (v.orgCode == item.orgCode) {
              v.ifmain = this.changeIfMainForm.ifmain;
            }
          });
        });

        this.changeIfMainVisible = false;
        this.$forceUpdate();
        delete this.changeIfMainForm.ifmain;
        this.unitModalVisibleSelectedRows = [];
      } else {
        // 新增
        this.changeIfMainForm.rows.forEach((item) => {
          item.ifmain = this.changeIfMainForm.ifmain;
          item.orgName = item.orgName;
          item.jbrName = item.orgDispname;
          item.jbrPhone = item.orgDispphone;
          item.jbrOph = item.orgDisptelephone;
        });
        let orgCodeList = this.unitModalVisibleDataSource.map(
          (item) => item.orgCode
        );
        let rowsOrgCode = this.changeIfMainForm.rows.map(
          (item) => item.orgCode
        );
        for (let index = 0; index < rowsOrgCode.length; index++) {
          const element = rowsOrgCode[index];
          if (orgCodeList.includes(element)) {
            return this.$message.error("单位已存在！");
          }
        }

        this.unitModalVisibleDataSource = this.unitModalVisibleDataSource.concat(
          this.changeIfMainForm.rows
        );
        this.changeIfMainForm = {};
        this.changeIfMainVisible = false;
      }
    },
    // 单位调整弹出框 修改主办/会办
    changeIfMain () {
      if (this.unitModalVisibleSelectedRows.length == 0) {
        return this.$message.error("请选择数据");
      }
      this.changeIfMainVisible = true;
      this.changeIfMainTitle = "修改承办类别";
    },
    // 单位调整弹出框 确定保存
    saveUnitModal () {
      let form = {};
      let undertakeList = [];
      this.unitModalVisibleDataSource.map((item) => {
        undertakeList.push({
          ifmain: item.ifmain,
          orgCode: item.orgCode,
          orgName: item.orgName,
          undertakeId: item.undertakeId || "",
          proposalId: this.proposalId,
        });
      });
      form.undertakeList = undertakeList;
      form.proposalId = this.proposalId;
      form.contentType = this.record.proposalContentType;
      form.proposalType = this.record.proposalType;
      doRevise(undertakeList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 下载
    downFileDataDf (itemdf, type) {
      let form = {};
      let attName = "";
      let attSuffix = "";
      if (type === 6) {
        form.relId = itemdf.attId;
        form.type = type;
        attName = itemdf.attName;
        attSuffix = itemdf.attSuffix;
      } else {
        itemdf.fujianList.forEach((item) => {
          if (item.attType === type) {
            form.relId = item.relId;
            form.type = item.attType;
            attName = item.attName;
            attSuffix = item.attSuffix;
          }
        });
      }
      console.log("🤗🤗🤗, form =>", form);
      downFile(form).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `${attName}.${attSuffix}`;

        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 下载附件
    downFileData (type, relId) {
      let form = { relIdList: [] };
      if (type == 2) {
        form.relId = relId;
      } else {
        form.relId = this.proposalId;
      }
      let name = "";
      if (type == "1") {
        name = "附件.zip";
      }
      if (type == "2") {
        name = "答复附件.pdf";
      }
      if (type == "9") {
        name = "正文附件.docx";
      }
      form.type = type;
      console.log("🤗🤗🤗, form =>", form);
      downFile(form).then((res) => {
        console.log("🤗🤗🤗, res =>", res, 'headers', res.headers, res.headers?.['content-disposition']);
        // let filename = res.headers?.['content-disposition']?.split(';')[1].split("filename=")[1];
        // if (type == "1" && filename) {
        //   let hz = filename?.substr(filename.lastIndexOf('.') + 1);
        //   name = "附件." + hz;
        // }
        if (type == "1") {
          let fjlis = this.record.attachmentList.filter((i) => i.attType == 1);
          if (fjlis.length == 1) {
            name = "附件." + fjlis[0].attSuffix;
          }
        }
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `${this.record.proposalTitle}${name}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 查看正文
    lookWord () {
      seeText(this.proposalId).then((res) => {
        if (res.data.code == 200 && res.data.data) {
          let url = this.GLOBAL.basePath_3 + "/" + res.data.data.url;
          window.open(url);
        } else {
          this.$message.error("请求出错!");
        }
      });
    },
    // 打开反馈
    openEvaluationModal () {
      this.$refs.evaluationModal.proposalId = this.proposalId;
      this.$refs.evaluationModal.getFeedUndertakeList();
      this.$refs.evaluationModal.modalVisible = true;
    },

    // 打开单位调整弹出框
    openUnitModalVisible () {
      this.unitModalVisible = true;
      getUndertakeInfo(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitModalVisibleDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 维持原交办
    maintainAPrimaryOffice () {
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.visiblePostpone = true;
      this.modalTitle = "维持原交办";
    },
    // 处理延期/调整/维持原交办 -弹窗保存
    submitDelay () {
      let form = {};
      form.comment = this.resourceForm.comment || "";
      form.undertakeId = this.selectedRowsPostpone[0].undertakeId;
      if (this.modalTitle == "处理延期") {
        form.delayDay = this.resourceForm.delayDay;
        doDelay(form).then((res) => {
          if (res.data.code == 200) {
            this.$message.success("操作成功");
            this.resourceForm.comment = "";
            this.getASubflow();
            this.visiblePostpone = false;
          } else {
            this.$message.error(res.data.message);
          }
        });
      } else if (this.modalTitle == "维持原交办") {
        if (this.keepType == "revise") {
          keepRevise(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.resourceForm.comment = "";
              this.getAdjust();
              this.visiblePostpone = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        }
        if (this.keepType == "delay") {
          keepDelay(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.getASubflow();
              this.visiblePostpone = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        }
      }
    },
    // 处理延期-事件
    treatmentDelay () {
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.visiblePostpone = true;
      this.modalTitle = "处理延期";
      // this.resourceForm.delayDay = "";
      // this.resourceForm.delayDay = Number(
      //   this.selectedRowsPostpone[0].delayTimeTo
      // );
      this.$set(
        this.resourceForm,
        "delayDay",
        this.selectedRowsPostpone[0].delayTimeTo
      );
    },
    // 处理延期-获取子流程 父组件调用
    getASubflow () {
      getDelayUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "delay";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 处理调整-获取子流程 父组件调用
    getAdjust () {
      getReviseUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "revise";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    getAdjustMinor () {
      getAdjustMinorUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "reviseMinor";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 上传之前
    beforeUpload (file, fileList) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10M!");
      }
      // var reg = /^.+(docx|doc)$/;
      // const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      // if (!isDoc) {
      //   this.$message.error("上传的文件格式只能是doc或docx");
      //   return;
      // }
      return false;
    },
    // 答复 上传删除
    handleTextRemove (file) {
      this.resourceForm.fuJianIds = [];
      this.fileTextList = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 无需见面沟通证据 上传删除
    handleTextRemove7 (file) {
      this.resourceForm.forumFile = [];
      this.fileTextList_7 = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 座谈会调研 上传删除
    handleTextRemove8 (file) {
      this.resourceForm.communFile = [];
      this.fileTextList_8 = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 上传操作
    uploadChange (val, type) {
      // 1、议案附件2、回复的附件3、代表大会议附件
      // 4、常委会议附件5、市政府(两院)宙议附件6
      // 承单位退回附件7、代表无需沟通证据8、座
      // 谈会证据9、建议正文10、建议纸
      if (val.file.status == "removed") return;
      let formData = new FormData();
      formData.append("file", val.file);
      formData.append("type", type);
      fileUpload(formData).then((res) => {
        if (type == "2") {
          // 附件
          this.resourceForm.fuJianIds = [];
          this.resourceForm.fuJianIds.push(res.data.data[0].attId);
          this.fileTextList = val.fileList;
        } else if (type == "7") {
          this.resourceForm.forumFile = [];
          this.resourceForm.forumFile.push(res.data.data[0].attId);
          this.fileTextList_7 = val.fileList;
        } else if (type == "8") {
          this.resourceForm.communFile = [];
          this.resourceForm.communFile.push(res.data.data[0].attId);
          this.fileTextList_8 = val.fileList;
        }
      });
    },
    // 答复
    replyData (type) {
      if (!this.fileTextList || this.fileTextList.length == 0) {
        return this.$message.error("请上传答复附件！");
      }
      if (
        this.record.ifMain == 1 &&
        this.resourceForm.dffs.includes("3") &&
        (!this.fileTextList_7 || this.fileTextList_7.length == 0)
      ) {
        return this.$message.error("请上传代表无需沟通证据！");
      }
      if (!this.resourceForm.dffs.includes("3")) {
        this.resourceForm.forumFile = [];
        this.fileTextList_7 = [];
      }
      if (!this.resourceForm.dffs.includes("2")) {
        this.resourceForm.communFile = [];
        this.fileTextList_8 = [];
      }
      let forumFile = "";
      let communFile = "";
      this.$refs["resourceForm"].validate((valid) => {
        if (valid) {
          let jbrData = {};
          jbrData.jbrName = this.resourceForm.jbrName;
          jbrData.jbrDept = this.resourceForm.jbrDept;
          jbrData.jbrJob = this.resourceForm.jbrJob;
          jbrData.jbrOph = this.resourceForm.jbrOph;
          jbrData.jbrPhone = this.resourceForm.jbrPhone;
          jbrData.proposalId = this.proposalId;

          //先更新经办人信息
          updateJbr(jbrData).then((res) => {
            if (res.data.code != 200) {
              this.$message.error(res.data.message);
              return false;
            }
          });

          if (
            this.resourceForm.forumFile &&
            this.resourceForm.forumFile.length > 0
          ) {
            forumFile = this.resourceForm.forumFile.toString();
          }
          if (
            this.resourceForm.communFile &&
            this.resourceForm.communFile.length > 0
          ) {
            communFile = this.resourceForm.communFile.toString();
          }
          let form = {};
          // form.dfr = this.userData.username;
          // form.dfrId = this.userData.userId;
          form.actType = type;
          form.dflb = this.resourceForm.dflb;
          form.dffs = this.resourceForm.dffs.toString();
          form.isPublic = this.resourceForm.isPublic;
          form.remark = this.resourceForm.remark;
          form.xtpfList = this.hbDataSource;
          form.dfId = this.resourceForm.dfId;
          if (
            this.resourceForm.fuJianIds != "" &&
            this.resourceForm.fuJianIds != undefined
          ) {
            form.fuJianIds = this.resourceForm.fuJianIds.toString();
          }
          if (communFile != "" && communFile != undefined) {
            form.fuJianIds += "," + communFile;
          }
          if (forumFile != "" && forumFile != undefined) {
            form.fuJianIds += "," + forumFile;
          }
          this.sfLoading = true;
          doReply(form, this.proposalId).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.visible = false;
              this.fileTextList = [];
              this.close();
              this.sfLoading = false;
            } else {
              this.$message.error(res.data.message);
              this.sfLoading = false;
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 重新生成正文
    // regenerateText() {
    //   againProduce(this.proposalId).then((res) => {
    //     console.log("重新生成正文🤗🤗🤗, res =>", res);
    //     if (res.data.code == 200) {
    //       this.$message.success(res.data.message);
    //     } else {
    //       this.$message.error(res.data.message);
    //     }
    //   });
    // },
    // 删除提出代表
    delJointlyList () {
      if (this.deputationSelectedRows.length == 0) {
        return this.$message.error("请选择数据！");
      }
      let jointlyIdsList = [];
      this.deputationSelectedRows.map((item) => {
        jointlyIdsList.push({ jointlyIds: item.jointlyId });
      });
      delJointly(jointlyIdsList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success(res.data.message);
          this.deputationSelectedRows = []
          this.getJointlyListData();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 接受提出代表
    getJoinTable (rows) {
      // 判断当前选择人的类型
      if (this.perType == "提出") {
        let form = {
          operids: rows.map((item) => item.userId).toString(),
          proposalId: this.proposalId,
        };
        // 新增
        addJointly(form).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.message);
            this.getJointlyListData();
          } else {
            this.$message.error(res.data.message);
          }
        });
      } else {
        console.log("🤗🤗🤗 接受提出代表-答复, rows =>", rows);
        this.resourceForm = Object.assign({}, this.resourceForm, rows[0]);
      }
    },
    // 打开提出代表进行新增/答复选择经办人
    showUserTable (type) {
      this.perType = type;
      this.$refs.commonUserTable.jointTableVisible = true;
    },
    // 打开选择经办人
    showJbrTable (orgCode) {
      this.$refs.jbrTable.queryForm.jbrName = "";
      this.$refs.jbrTable.queryForm.jbrPhone = "";
      this.$refs.jbrTable.queryForm.orgCode = orgCode;
      this.$refs.jbrTable.showDesc();
      this.$refs.jbrTable.jbrTableVisible = true;
    },
    // 接受提出代表
    getJbrTable (row) {
      this.resourceForm.jbrName = row.jbrName;
      this.resourceForm.jbrDept = row.jbrDept;
      this.resourceForm.jbrJob = row.jbrJob;
      this.resourceForm.jbrOph = row.jbrOph;
      this.resourceForm.jbrPhone = row.jbrPhone;
    },
    // tab点击
    changeTab (key) {
      // 获取提出代表
      // if (key == "3") {
      // this.getJointlyListData();
      // }
      // 流程跟踪
      // if (key == "5") {
      // this.getLocalHistoryData();
      // }
      // 获取议案建议的承办单位
      // if (key == "4") {
      // this.getProposalUndertakesData();
      // }
      // 获取反馈答复列表
      // if (key == "2") {
      // this.feedbackReply();
      // }
    },
    // 获取反馈答复列表、
    feedbackReply () {
      getUndertakeDfAndFeedbackList(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitDataSource = [];
          this.majorLastTime = "";
          this.minorLastTime = "";
          this.majorUndertakeDfList = [];
          this.minorUndertakeDfList = [];
          if (res.data.data != undefined && res.data.data.length > 0) {
            res.data.data.forEach((item) => {
              this.unitDataSource.push(item);
              // 主办单位
              if (item.ifmain == 1) {
                // 答复反馈tab 显示答复方式
                if (item.dfList) {
                  this.record.dffs = item.dfList[0].dffs;
                }
                this.majorLastTime = item.handleLastTime;
                this.majorUndertakeDfList.push(item);
              } else {
                this.minorLastTime = item.handleLastTime;
                this.minorUndertakeDfList.push(item);
              }
            });
          }
          console.log(
            "🤗🤗🤗, this.majorUndertakeDfList =>",
            this.majorUndertakeDfList
          );
          console.log(
            "🤗🤗🤗, this.minorUndertakeDfList =>",
            this.minorUndertakeDfList
          );
        } else {
          // this.$message.error(res.data.message);
        }
        this.$forceUpdate();
      });
    },
    // 获取办理跟踪列表
    getLocalHistoryData () {
      getLocalHistory(this.proposalId).then((res) => {
        // console.log("获取办理跟踪列表🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.processDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 获取提出代表
    getJointlyListData () {
      getJointlyList(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.deputationDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 提交校核 打开
    openModal (status) {
      this.returnValue = true;
      if (status == "pass") {
        this.returnValueTitle = "校核意见";
      } else {
        this.returnValueTitle = "退回意见";
      }
    },
    // 提交校核
    submitTheSubject () {
      let form = {};
      form.proposalIds = this.proposalId;
      if (this.returnValueTitle == "退回意见") {
        form.actType = "return";
      } else {
        form.actType = "pass";
      }
      form.comment = this.resourceForm.comment || "";
      //提交校核 通过
      let content = "";
      if (this.returnValueTitle == "校核意见") {
        content = "是否确定校核通过？";
      } else {
        content = "是否确定退回？";
      }
      //提交校核 退回
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: content,
        onOk: () => {
          this.jhLoading = true;
          doCheck(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment: "",
              };
              this.visible = false;
              this.returnValue = false;
              this.jhLoading = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 提交审核
    submitReview (status) {
      this.resourceForm.proposalIds = this.proposalId;
      this.resourceForm.actType = status;
      let tips = "pass" == status ? "是否确定审核通过？" : "是否确定退回？";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: tips,
        onOk: () => {
          this.shLoading = true;
          doAudit(this.resourceForm).then((res) => {
            console.log("🤗🤗🤗, res =>", res);
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
              };
              this.shLoading = false;
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          // this.$message.info("您已取消操作！");
        },
      });
    },
    // 关闭窗口
    close () {
      this.visible = false;
      this.showOperate = true;
      for (const key in this.$data) {
        if (!Array.isArray(this.$data[key])) {
          this[key] = this.$options.data()[key];
        }
      }
      this.fileTextList_8 = [];
      this.fileTextList = [];
      this.fileTextList_7 = [];
      this.$emit("parentFetchData");
    },
    // 退回
    back () { },
    // 交办-提交交办
    manage () {
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }

      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定交办？",
        onOk: () => {
          let hostData = [];
          let meetData = [];
          this.resourceForm.host.map((item) => {
            hostData.push({
              ifmain: 1,
              orgCode: item.orgCode,
              orgName: item.orgName,
              proposalId: this.proposalId,
            });
          });
          if (this.resourceForm.meet.length > 0) {
            this.resourceForm.meet.map((item) => {
              meetData.push({
                ifmain: 0,
                orgCode: item.orgCode,
                orgName: item.orgName,
                proposalId: this.proposalId,
              });
            });
          }
          let undertakeList = [];
          undertakeList = hostData.concat(meetData);
          let newForm = {};
          newForm.undertakeList = undertakeList;
          newForm.proposalId = this.proposalId;
          newForm.contentType = this.resourceForm.contentType || "";
          newForm.proposalType = this.record.proposalType;
          newForm.minorHandleLastTimeStr = this.resourceForm.minorHandleLastTimeStr;
          newForm.mainHandleLastTimeStr = this.resourceForm.mainHandleLastTimeStr;
          doAssign(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("交办成功");
              // 交办后重新获取数据，显示答复
              this.showDesc();
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
      });
    },
    //转为参考建议
    changeRefer () {
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
      let hostData = [];
      let meetData = [];
      this.resourceForm.host.map((item) => {
        hostData.push({
          ifmain: 1,
          orgCode: item.orgCode,
          orgName: item.orgName,
        });
      });
      if (this.resourceForm.meet.length > 0) {
        this.resourceForm.meet.map((item) => {
          meetData.push({
            ifmain: 0,
            orgCode: item.orgCode,
            orgName: item.orgName,
          });
        });
      }
      let undertakeList = [];
      undertakeList = hostData.concat(meetData);
      let newForm = {};
      newForm.undertakeList = undertakeList;
      newForm.proposalId = this.proposalId;
      newForm.contentType = this.resourceForm.contentType || "";
      newForm.proposalType = "4";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "确认要转为供参考建议？",
        onOk: () => {
          doAssign(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("转为参考建议成功");
              Object.assign(this.$data, this.$options.data());
              this.close();
              this.visible = false;

            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 交办-保存
    save () {
      this.jbLoading = true;
      let newForm = { params: {}, data: {} };
      newForm.params.contentType = this.resourceForm.contentType || "";
      newForm.params.proposalType = this.record.proposalType;
      newForm.params.proposalId = this.proposalId;
      newForm.params.mainHandleLastTimeStr = this.resourceForm.mainHandleLastTimeStr;
      newForm.params.minorHandleLastTimeStr = this.resourceForm.minorHandleLastTimeStr;
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
      let hostData = [];
      let meetData = [];
      this.resourceForm.host.map((item) => {
        hostData.push({
          ifmain: 1,
          orgCode: item.orgCode,
          orgName: item.orgName,
          proposalId: this.proposalId,
        });
      });
      if (this.resourceForm.meet.length > 0) {
        this.resourceForm.meet.map((item) => {
          meetData.push({
            ifmain: 0,
            orgCode: item.orgCode,
            orgName: item.orgName,
            proposalId: this.proposalId,
          });
        });
      }
      let data = [];
      data = hostData.concat(meetData);
      newForm.data = data;
      saveCBDW(newForm).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("保存成功");
          this.jbLoading = false;
          this.close();
        } else {
          this.jbLoading = false;
          this.$message.error(res.data.message);
        }
      });
    },
    // 打开单位table
    openUnitTable (type) {
      // 再次点击 设置默认值
      if (this.resourceForm[type]) {
        this.defaultUnit = this.resourceForm[type].map((item) => item.orgCode);
      } else {
        this.defaultUnit = [];
      }
      this.unitType = type;
      this.$refs.commonUnitShuttleTable.unitTableVisible = true;
    },
    // 接受单位
    getUnitTable (rows) {
      // 交办选择主/会办单位
      if (this.unitType == "meet" || this.unitType == "host") {
        let name = rows.map((item) => item.orgName).toString();
        this.resourceForm[this.unitType] = rows;
        this.resourceForm[this.unitType + "Name"] = name;
        this.defaultUnit = rows.map((item) => item.orgCode);
      } else if (this.unitType == "单位调整") {
        console.log("🤗🤗🤗, rows =>", rows);
        this.changeIfMainForm.orgName = "";
        this.changeIfMainForm.orgName = rows
          .map((item) => item.orgName)
          .toString();
        // 储存
        this.changeIfMainForm.rows = [];
        this.changeIfMainForm.rows = rows;
        this.changeIfMainVisible = true;
        this.$forceUpdate();
      }
    },
    // 获取信息
    async showDesc () {
      let res = await proposalHisById({ proposalId: this.proposalId });
      if (res.data.code == 200) {
        this.record = res.data.data.suggestionHis;
        this.majorUndertakeDfList = [];
        this.minorUndertakeDfList = [];
        if (
          res.data.data.undertakeDfHisList != undefined &&
          res.data.data.undertakeDfHisList.length > 0
        ) {
          res.data.data.undertakeDfHisList.forEach((item) => {
            this.unitDataSource.push(item);
            // 主办单位
            if (item.dflb != "null") {
              this.majorUndertakeDfList.push(item);
            } else {
              this.minorUndertakeDfList.push(item);
            }
          });
        }
        return;
        this.resourceForm.contentType = this.record.proposalContentType;
        // 答复时获取经办信息
        if (
          (this.record.undertakeStatus == 2 &&
            this.userData.authorities[0].authority == "YAJY_CBDW") ||
          (this.scenesStatus == "答复修改" &&
            this.userData.authorities[0].authority == "YAJY_CBDW")
        ) {
          getUndertakeDfInfo(this.proposalId).then((res) => {
            if (res.data.code == 200) {
              if (res.data.data.undertakeDf) {
                this.resourceForm.dfId = res.data.data.undertakeDf.dfId;
              }
              //初始化经办人数据
              this.resourceForm.orgCode = res.data.data.undertake.orgCode;
              this.resourceForm.jbrName = res.data.data.undertake.jbrName;
              this.resourceForm.jbrDept = res.data.data.undertake.jbrDept;
              this.resourceForm.jbrJob = res.data.data.undertake.jbrJob;
              this.resourceForm.jbrPhone = res.data.data.undertake.jbrPhone;
              this.resourceForm.jbrOph = res.data.data.undertake.jbrOph;
              if (
                res.data.data.undertakeDf != undefined &&
                res.data.data.undertakeDf != null
              ) {
                if (
                  res.data.data.undertakeDf.dflb != undefined ||
                  res.data.data.undertakeDf.dflb != null
                ) {
                  this.resourceForm.dflb = res.data.data.undertakeDf.dflb;
                }
                if (
                  res.data.data.undertakeDf.dffs &&
                  res.data.data.undertakeDf.dffs.indexOf(",") >= 0
                ) {
                  this.resourceForm.dffs = res.data.data.undertakeDf.dffs.split(
                    ","
                  );
                }
                if (
                  res.data.data.undertakeDf.dffs &&
                  res.data.data.undertakeDf.dffs.indexOf(",") < 0
                ) {
                  this.resourceForm.dffs = res.data.data.undertakeDf.dffs;
                }
                this.resourceForm.isPublic = res.data.data.undertakeDf.isPublic;
                this.resourceForm.remark = res.data.data.undertakeDf.remark;
              }

              //政务协同
              if (
                res.data.data.undertakeDfHisList.ifmain == 1 &&
                res.data.data.xtpfList.length > 0
              ) {
                this.hbDataSource = res.data.data.xtpfList;
              }

              //附件
              if (
                res.data.data.fujianList != undefined &&
                res.data.data.fujianList != null &&
                res.data.data.fujianList.length > 0
              ) {
                res.data.data.fujianList.forEach((item) => {
                  if (item.attType == 2) {
                    // 附件
                    this.resourceForm.fuJianIds = [];
                    this.resourceForm.fuJianIds.push(item.attId);
                    let file = {
                      uid: item.attId,
                      name: item.attName + "." + item.attSuffix,
                      status: "done",
                      url: "",
                    };
                    this.fileTextList = [];
                    this.fileTextList.push(file);
                  } else if (item.attType == 7) {
                    this.resourceForm.forumFile = [];
                    this.resourceForm.forumFile.push(item.attId);
                    let file = {
                      uid: item.attId,
                      name: item.attName + "." + item.attSuffix,
                      status: "done",
                      url: "",
                    };
                    this.fileTextList_7 = [];
                    this.fileTextList_7.push(file);
                  } else if (item.attType == 8) {
                    this.resourceForm.communFile = [];
                    this.resourceForm.communFile.push(item.attId);
                    let file = {
                      uid: item.attId,
                      name: item.attName + "." + item.attSuffix,
                      status: "done",
                      url: "",
                    };
                    this.fileTextList_8 = [];
                    this.fileTextList_8.push(file);
                  }
                });
              }
              this.$forceUpdate();
            }
          });
        }
        //交办时获取已保存的承办单位数据
        if (this.record.status == 40) {
          getProposalUnder(this.proposalId).then((res) => {
            console.log("获取存在的承办单位数据, res =>", res);
            if (res.data.code == 200) {
              if (res.data.data.length > 0) {
                let hostNames = [];
                let meetNames = [];
                let hostData = [];
                let meetData = [];
                let minorHandleLastTimeStr = "";
                let mainHandleLastTimeStr = "";

                this.resourceForm["hostName"] = hostNames.join(",");
                this.resourceForm["meetName"] = meetNames.join(",");
                this.resourceForm.host = hostData;
                this.resourceForm.meet = meetData;
                this.resourceForm.mainHandleLastTimeStr = "";
                this.resourceForm.mainHandleLastTimeStr = mainHandleLastTimeStr;
                this.resourceForm.minorHandleLastTimeStr = "";
                this.resourceForm.minorHandleLastTimeStr = minorHandleLastTimeStr;
              }
            } else {
              this.$message.error(res.data.message);
            }
          });
        }
        //获取答复、反馈数据
        this.feedbackReply();

        //获取代表数据
        this.getJointlyListData();

        //获取流程跟踪数据
        this.getLocalHistoryData();
      } else {
        this.$message.error(res.data.message);
      }

      // 获取子流程状态值
      // this.getUndertakeStatusData();
    },
    // 获取登录人数据
    getOneUser () {
      getUserData_yajy().then((res) => {
        if (res.data.code == 200) {
          // 获取登录人的权限
          this.userData = res.data.data;
          console.log("🤗🤗🤗, this.userData =>", this.userData);
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 答复时获取经办信息
    getJbrData () {
      getJbr(this.proposalId).then((res) => {
        console.log("🤗🤗🤗, res =>答复时获取经办信息", res);
        if (res.data.code == 200) {
          this.resourceForm.jbrName = res.data.data[0].jbrName;
          this.resourceForm.jbrDept = res.data.data[0].jbrDept;
          this.resourceForm.jbrJob = res.data.data[0].jbrJob;
          this.resourceForm.jbrPhone = res.data.data[0].jbrPhone;
          this.resourceForm.jbrOph = res.data.data[0].jbrOph;
        }
      });
    },

    viewFkEvaluation (data) {
      if (data) {
        this.fkContent = data.gdjy;
        if (data.evaluationValue == 1) {
          this.fkEvaluation = "不满意";
        }
        if (data.evaluationValue == 2) {
          this.fkEvaluation = "基本满意";
        }
        if (data.evaluationValue == 3) {
          this.fkEvaluation = "满意";
        }
        this.fkVisible = true;
      }
    },
    closeFkVisible () {
      this.fkContent = "";
      this.fkEvaluation = "";
      this.fkVisible = false;
    },
  },
};
</script>
