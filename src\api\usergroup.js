import request from "@/utils/request";
import qs from "qs";

export function getTree(data) {
  return request({
    url: "/api/v1/manage/usergroup/findTree/" + data.rootId,
    method: "post",
    data,
  });
}

export function getPage(data) {
  return request({
    url: "/api/v1/manage/usergroup/findPage",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/manage/usergroup/add",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/manage/usergroup/update",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/manage/usergroup/remove",
    method: "post",
    data,
  });
}

export function getUserPage(data) {
  return request({
    url: "/api/v1/manage/usergroup/findUserPage",
    method: "post",
    data,
  });
}

export function doChangeRel(data, groupId) {
  return request({
    url: "/api/v1/manage/usergroup/changeRelBatch/" + groupId,
    method: "post",
    data,
  });
}

export function getRelList(data) {
  return request({
    url: "/api/v1/manage/usergroup/findRelList",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    transformRequest: [
      function (data) {
        return qs.stringify(data);
      },
    ],
  });
}
