import Vue from "vue";
import store from "../../store";
import {
  sendLogInfo,
  getInfo,
  mobileLogin,
  yzyonelogin,
  onelogin,
  login,
  logout, getClickMenu,
} from "@/api/user";
import {
  getAccessToken,
  removeAccessToken,
  setAccessToken,
} from "@/utils/accessToken";
import { resetRouter } from "@/router";
import defaultSettings from "@/config/settings";
import cookie from "js-cookie";
// Vue.use(Vuex);
const state = {
  accessToken: getAccessToken(),
  userName: "",
  permissions: [],
};
const mutations = {
  setAccessToken: (state, accessToken) => {
    state.accessToken = accessToken;
  },
  setUserName: (state, userName) => {
    state.userName = userName;
  },
  setPermissions: (state, permissions) => {
    state.permissions = permissions;
  },
};
const actions = {
  mobileLogin({ commit }, userInfo) {
    const { mobile, smscode } = userInfo;
    return new Promise((resolve, reject) => {
      mobileLogin({
        mobile,
        smscode,
      })
        .then((response) => {
          console.log("返回的值", response);
          const { token, username, userId } = response.data;
          sessionStorage.setItem("myUserId", userId);

          commit("setAccessToken", token);
          setAccessToken(token);
          const hour = new Date().getHours();

          if (
            response.data.mobileToken == "" ||
            response.data.mobileToken == undefined
          ) {
            const thisTime =
              hour < 8
                ? "早上好"
                : hour <= 11
                ? "上午好"
                : hour <= 13
                ? "中午好"
                : hour < 18
                ? "下午好"
                : "晚上好";
            Vue.prototype.$baseNotify(
              `欢迎登录${defaultSettings.title}`,
              `${thisTime}！`
            );
          }

          //用户主题绑定
          changeTheme("horizontal");

          resolve(response);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  login({ commit }, userInfo) {
    // 去除登录限制 start
    // 手动设置假token
    // return new Promise((resolve, reject) => {
    //   localStorage.setItem("title2", "平台门户");
    //   localStorage.setItem("index2", 0);
    //   let token = "111111";
    //   commit("setAccessToken", token);
    //   setAccessToken(token);
    //   changeTheme("horizontal");
    //   resolve();
    // });
    // 去除登录限制 end
    const { userName, password, key, code, path } = userInfo;
    return new Promise((resolve, reject) => {
      console.log(resolve, "---", reject);
      login({
        userName,
        password,
        key,
        code,
        path
      })
        .then((response) => {
          console.log("🤗xxxx🤗🤗, response =>", response);
          // localStorage.setItem("title2", "平台门户");
          // 临时修改!!! localStorage.setItem("title2", "代表建议管理"); 初始化显示代表建议管理
          localStorage.setItem("title2", "代表建议管理");
          localStorage.setItem("index2", 0);
          const { token, username, userId } = response.data;
          console.log(token, "新token");
          commit("setAccessToken", token);
          setAccessToken(token); //token 存入token
          commit("setUserName", username);
          // 保存USERID 用于获取数据 !!!
          sessionStorage.setItem("USERID", JSON.stringify(response.data));
          sessionStorage.setItem(
            "isDb",
            JSON.stringify(response.data.isDb || "")
          );

          sessionStorage.setItem(
            "account",
            JSON.stringify(response.data.account)
          );
          localStorage.setItem("userId", userId);

          const hour = new Date().getHours();
          const thisTime =
            hour < 8
              ? "早上好"
              : hour <= 11
              ? "上午好"
              : hour <= 13
              ? "中午好"
              : hour < 18
              ? "下午好"
              : "晚上好";
          Vue.prototype.$baseNotify(
            `欢迎登录${defaultSettings.title}`,
            `${username}，${thisTime}！`
          );
          //用户主题绑定
          // if (!response.data.viewTheme) {
          //   //默认使用horizontal
          //   response.data.viewTheme = "horizontal";
          //   changeTheme(response.data.viewTheme);
          // } else {
          //   changeTheme(response.data.viewTheme);
          // }
          changeTheme("horizontal");

          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  onelogin({ commit }, data) {
    const username = data.data.data.userInfo.userName;
    console.log('输出data')
    console.log(data)
    return new Promise((resolve, reject) => {
      commit("setAccessToken", data.data.data.token);
      setAccessToken(data.data.data.token);
      console.log("token？？", data.data.data.token);

      const hour = new Date().getHours();
      const thisTime =
        hour < 8
          ? "早上好"
          : hour <= 11
          ? "上午好"
          : hour <= 13
          ? "中午好"
          : hour < 18
          ? "下午好"
          : "晚上好";
      Vue.prototype.$baseNotify(
        `欢迎登录${defaultSettings.title}`,
        `${username}，${thisTime}！`
      );
      //用户主题绑定
      // if (!response.data.viewTheme) {
      //   //默认使用horizontal
      //   response.data.viewTheme = "horizontal";
      //   changeTheme(response.data.viewTheme);
      // } else {
      //   changeTheme(response.data.viewTheme);
      // }
      changeTheme("horizontal");
      resolve();
    });
  },

  yzyonelogin({ commit }, data) {
    const username = data.userName;
    return new Promise((resolve, reject) => {
      commit("setAccessToken", data.token);
      setAccessToken(data.token);
      const hour = new Date().getHours();
      const thisTime =
        hour < 8
          ? "早上好"
          : hour <= 11
          ? "上午好"
          : hour <= 13
          ? "中午好"
          : hour < 18
          ? "下午好"
          : "晚上好";
      Vue.prototype.$baseNotify(
        `欢迎登录${defaultSettings.title}`,
        `${username}，${thisTime}！`
      );
      changeTheme("horizontal");
      resolve();
    });
  },

  casLogin({ commit }, loginInfo) {
    const { userName, jsessionid } = loginInfo;
    return new Promise((resolve, reject) => {
      cookie.set("jsessionid", jsessionid, {
        path: process.env.VUE_APP_BASE_API,
      });
      commit("setAccessToken", "token");
      setAccessToken("token");
      const hour = new Date().getHours();
      const thisTime =
        hour < 8
          ? "早上好"
          : hour <= 11
          ? "上午好"
          : hour <= 13
          ? "中午好"
          : hour < 18
          ? "下午好"
          : "晚上好";
      Vue.prototype.$baseNotify(
        `欢迎登录${defaultSettings.title}`,
        `${userName}，${thisTime}！`
      );
      resolve();
    });
  },
  getInfo({ commit, state }, value) {
    return new Promise((resolve, reject) => {
      // 去除登录限制 start
      let data = {};
      data.permissions = ["I_XTGLY"];
      commit("setPermissions", data.permissions);
      resolve(data);

      return;
      // 去除登录限制 end
      getInfo(state.accessToken)
        .then((response) => {
          const { data } = response;
          if (!data) {
            reject("验证失败，请重新登录...");
          }
          let { identityList, userName, orgName } = data;
          localStorage.setItem("userName", userName);
          localStorage.setItem("orgName", orgName);

          let permissionList = [];
          identityList.forEach((permission) => {
            permissionList.push(permission.code);
          });

          if (permissionList.length == 0) {
            Vue.prototype.$baseMessage(
              "您尚未拥有权限，请联系系统管理员",
              "error"
            );
            reject("您尚未拥有权限，请联系系统管理员");
          }
          data.permissions = permissionList;
          commit("setPermissions", permissionList);
          commit("setUserName", userName);
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.accessToken)
        .then(() => {
          commit("setAccessToken", "");
          commit("setPermissions", []);
          removeAccessToken();
          // 登出
          if (process.env.VUE_APP_DEFAULT_AUTHENTICATION_METHOD === "cas") {
            // 单点登录
            // window.location.href = process.env.VUE_APP_API_CAS_URL;
            window.close();
          } else {
            // 返回登录页
            resetRouter();
          }
          dispatch("tagsView/delAllViews", null, {
            root: true,
          });
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  sendLogInfo({ commit, state }, data) {
    return new Promise((resolve, reject) => {
      sendLogInfo(data)
        .then((response) => {
          console.log(response);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  resetAccessToken({ commit }) {
    return new Promise((resolve) => {
      commit("setAccessToken", "");
      removeAccessToken();
      resolve();
    });
  },
  getClick({commit},from){
    return new Promise((resolve, reject) => {
      getClickMenu(JSON.parse(JSON.stringify(from)))
        .then(() => {

        })
        .catch((error) => {
          reject(error);
        });
    });
  }
};
export default {
  state,
  mutations,
  actions,
};

function changeTheme(v) {
  store.dispatch("settings/changeLayout", v);
  if (v == "theme3") {
    const obj = {
      buttonBackground: "#1890ff",
      header: "fixed",
      layout: "theme3",
      menuBackground: "#ffffff",
      menuBackgroundActive: "#f6f8f9",
      menuChildrenBackground: "#ffffff",
      menuColor: "#1f2e4c",
      paginationBackgroundActive: "#1890ff",
      tagViewsBackgroundActive: "#1890ff",
      tagsView: "true",
    };
    window.localStorage.setItem("BYUI-VUE-THEME", JSON.stringify(obj));
    let viewThemeObj = JSON.parse(
      window.localStorage.getItem("BYUI-VUE-THEME")
    );
    viewThemeObj.layout = v;
    window.localStorage.setItem("BYUI-VUE-THEME", JSON.stringify(viewThemeObj));
    console.log(viewThemeObj, "v");
  } else if (v == "horizontal") {
    const obj = {
      buttonBackground: "#1890ff",
      header: "fixed",
      layout: "horizontal",
      menuBackground: "#fff",
      menuBackgroundActive: "#f9f9f9",
      menuChildrenBackground: "#f9f9f9",
      menuColor: "#596B8C",
      paginationBackgroundActive: "#1890ff",
      tagViewsBackgroundActive: "#1890ff",
      tagsView: "true",
    };
    window.localStorage.setItem("BYUI-VUE-THEME", JSON.stringify(obj));
  } else {
    const obj = {
      buttonBackground: "#1890ff",
      header: "fixed",
      layout: "horizontal",
      menuBackground: "#fff",
      menuBackgroundActive: "#f9f9f9",
      menuChildrenBackground: "#f9f9f9",
      menuColor: "#596B8C",
      paginationBackgroundActive: "#1890ff",
      tagViewsBackgroundActive: "#1890ff",
      tagsView: "true",
    };
    window.localStorage.setItem("BYUI-VUE-THEME", JSON.stringify(obj));
  }
}
