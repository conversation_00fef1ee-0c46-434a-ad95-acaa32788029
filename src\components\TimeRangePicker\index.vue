<template>
  <div>
    <a-col :span="spanNum" :class="{ 'overLength': title.length > 5 && spanNum <= 8 }">
      <a-form-model-item  :label="title" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-range-picker style="width: 100%;"
          :ranges="{
            最近三个月: [
              moment(new Date()).subtract(2, 'months'),
              moment(),
            ],
            '今年1-6月': [
              moment(moment().startOf('year')).startOf('month'),
              moment(moment().startOf('year'))
                .add(5, 'months')
                .endOf('month'),
            ],
            '今年7-12月': [
              moment(moment().startOf('year'))
                .add(6, 'months')
                .startOf('month'),
              moment(moment().startOf('year'))
                .add(11, 'months')
                .endOf('month'),
            ],
            今年内: [
              moment(moment().startOf('year')).startOf('month'),
              moment(moment().startOf('year'))
                .add(11, 'months')
                .endOf('month'),
            ],
          }"
          v-model="dateRange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="onTimeChange" />
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '时间范围',
      required: true
    },
    spanNum: {
      type: Number,
      default: () => 8,
    },
    startTimeValue: {
      type: String,
      default: () => '',
    },
    endTimeValue: {
      type: String,
      default: () => '',
    },
  },
  data() {
    return {
      dateRange: [] 
    };
  },
  watch: {  
    startTimeValue: {  
      handler(val) {
        this.dateRange = [val, this.endTimeValue]; 
      },
      deep: true,
      immediate: true,
    }, 
    endTimeValue: {  
      handler(val) {
        this.dateRange = [this.startTimeValue, val];
      },
      deep: true,
      immediate: true,
    }, 
  },
  methods: {
    onTimeChange(val) {
      this.$emit('update:startTimeValue', val[0])
      this.$emit('update:endTimeValue', val[1])
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
