<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24"
             :sm="24"
             :md="24"
             :lg="24"
             :xl="24">
        <a-row style="margin-left:1%"
               class="formBox">
          <a-form-model ref="queryForm"
                        :model="queryForm"
                        :label-col="{ span: 5 }"
                        :wrapper-col="{ span: 18 }">
            <!-- <a-col span="6" >
              <a-form-model-item label="届次">
                <a-select
                  v-model="queryForm.jcDm"
                  placeholder="请选择"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in SessionList"
                    :key="item.id"
                    :value="item.id"
                    >{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>  -->
            <a-col span="6">
              <a-form-model-item label="代表姓名"
                                 prop="userName">
                <a-input placeholder="请输入代表姓名"
                         v-model="queryForm.userName"
                         v-on:keyup.enter="search"
                         autocomplete="off"
                         allow-clear></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="评价状态">
                <a-select v-model="queryForm.grade"
                          placeholder="请选择评价状态"
                          allow-clear
                          @change="handleTime">
                  <a-select-option v-for="item in gradeList"
                                   :key="item.name"
                                   :value="item.id">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="活动时间"
                           prop="serverTime">
                <a-date-picker v-model="queryForm.serverTime"
                               allow-clear
                               key=""
                               value-format="YYYY-MM-DD"
                               placeholder="选择活动时间"
                               style="width: 100%;"></a-date-picker>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <span style="float: right; margin-top: 3px;">
                <a @click="toggleAdvanced">
                  {{ advanced ? "收起" : "高级搜索" }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
                <a-button style="margin-left: 12px;"
                          type="primary"
                          @click="search()">搜索</a-button>
                <a-button style="margin-left: 12px;"
                          @click="reset"
                          class="pinkBoutton">重置</a-button>
              </span>
            </a-col>
          </a-form-model>
        </a-row>
        <a-row style="margin-left:1%"
               v-if="advanced">
          <a-form-model ref="queryForm"
                        :model="queryForm"
                        :label-col="{ span: 5 }"
                        :wrapper-col="{ span: 18 }">
            <!-- 更多查询条件抽屉 -->
            <a-col :span="6">
              <a-form-model-item label="行政区县"
                                 prop="administrativeAreaId">
                <a-select allow-clear
                          v-model="queryForm.administrativeAreaId"
                          placeholder="请选择行政区县"
                          @change="queryAdministrativeAreaChange">
                  <a-select-option v-for="item in administrativeAreas"
                                   :key="item.id"
                                   :label="item.name"
                                   :value="item.id">{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="街道乡镇"
                                 prop="streetTownId">
                <a-select allow-clear
                          v-model="queryForm.streetTownId"
                          @change="handlestreetTownChange"
                          placeholder="请选择街道乡镇">
                  <a-select-option v-for="item in streetTowns"
                                   :key="item.id"
                                   :label="item.name"
                                   :value="item.id">{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="联络站"
                                 prop="liaisonStationId">
                <a-select allow-clear
                          style="width: 100%;"
                          v-model="queryForm.liaisonStationId"
                          @change="handleListLiaisonStationChange"
                          placeholder="请选择联络站">
                  <a-select-option v-for="item in liaisonStations"
                                   :key="item.id"
                                   :label="item.name"
                                   :value="item.id">{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <!-- <a-col span="6"  >
              <a-form-model-item label="时间范围">
                <a-select
                  v-model="queryForm.timeRange"
                  placeholder="请选择"
                  allow-clear
                  @change="handleTime"
                >
                  <a-select-option
                    v-for="item in timeScope"
                    :key="item.name"
                    :value="item.name"
                    >{{ item.name }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col> 
            <a-col span="6" >
              <a-form-item label="开始时间" prop="startTime">
                <a-date-picker
                  v-model="queryForm.startTime"
                  :disabled="showDisabled"
                  allow-clear
                  value-format="YYYY-MM-DD "
                  placeholder="选择开始时间"
                  style="width: 100%;"
                  :disabled-date="
                    (current) =>
                      current && queryForm.endTime
                        ? current.valueOf() >=
                          moment(new Date(queryForm.endTime)).valueOf()
                        : false
                  "
                ></a-date-picker>
              </a-form-item>
            </a-col>
            <a-col span="6">
              <a-form-item label="结束时间" prop="endTime">
                <a-date-picker
                  v-model="queryForm.endTime"
                  allow-clear
                  :disabled="showDisabled"
                  value-format="YYYY-MM-DD"
                  placeholder="选择结束时间"
                  style="width: 100%;"
                  :disabled-date="
                    (current) =>
                      current && queryForm.startTime
                        ? moment(new Date(queryForm.startTime)).valueOf() >=
                          current.valueOf()
                        : false
                  "
                ></a-date-picker>
              </a-form-item>
            </a-col> -->
          </a-form-model>
        </a-row>
        <a-row style="margin: 5px 0px 10px 8px;">
          <a-col :span="6">
            <a-button type="primary"
                      style="margin-left:10px;"
                      :disabled="exportExcelLoading"
                      @click="exportExcel">导出</a-button>
          </a-col>
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator"
                :spinning="listLoading">
          <standard-table ref="table"
                          :bordered="false"
                          class="directorySet-table"
                          size="small"
                          :columns="columns"
                          :loading="TBloading"
                          :pagination="pagination"
                          :data-source="list"
                          :selectedRows.sync="selectedRows"
                          rowKey="id"
                          :scroll="{ x: 300, y: 0 }"></standard-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import {
  evaluationWorkList,
  evaluationWorkExport,
} from "@/api/communityschedule";
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
import { listForAdmin as getStations } from "@/api/station";
export default {
  name: "evaluationWork",
  data () {
    return {
      TBloading: false,
      SessionList: [
        { name: "第十五届", key: 1, id: "2" },
        { name: "第十六届", key: 2, id: "3" },
      ],
      //list-----
      elementLoadingText: "正在加载...",
      list: [],
      listLoading: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 10,
      //list-----
      timeScope: [
        { id: 1, name: "本届" },
        { id: 2, name: "最近三个月" },
        { id: 3, name: "今年1~6月" },
        { id: 4, name: "今年7~12月" },
        { id: 5, name: "今年内" },
        { id: 6, name: "自定义" },
      ],
      // 时间范围是否自定义
      showDisabled: false,
      gradeList: [
        { id: 1, name: "满意" },
        { id: 2, name: "基本满意" },
        { id: 3, name: "不满意" },
      ],
      //行政区域列表
      administrativeAreas: [],
      //街道数据
      streetTowns: [],
      //联络站数据
      liaisonStations: [],
      queryForm: {
        administrativeAreaId: undefined,
        streetTownId: undefined,
        pageNum: 1,
        pageSize: 10,
        userName: "",
        serverTime: "",
      },
      advanced: false,
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      // 列表
      columns: [
        {
          title: "序号",
          align: "center",
          width: 100,
          ellipsis: true,
          customRender: (text, record, index) => {
            return index + 1 || "/";
          },
        },
        {
          align: "center",
          title: "代表姓名",
          width: 150,
          dataIndex: "userName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          title: "活动时间",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "serverTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "活动内容",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "content",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "行政区划",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "administrativeAreaName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "街道乡镇",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "streetTownName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站名称",
          align: "center",
          width: 200,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.liaisonStationName;
          },
        },
        {
          title: "评价",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "grade",
          customRender: (text, record, index) => {
            if (text == "1") {
              text = "满意";
            } else if (text == "2") {
              text = "基本满意";
            } else if (text == "3") {
              text = "不满意";
            }

            return text || "/";
          },
        },
        {
          title: "备注",
          align: "center",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.userComment || "/";
          },
        },
      ],
      exportExcelLoading: false,
      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `共${total}条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      selectedRows: [],
    };
  },
  created () {
    this.fetchData();
    this.listAdministrativeAreas();
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "进社区活动");
  },
  methods: {
    // 切换页数
    changePageSize (pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange (pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    /**
     * 预加载数据
     */

    // 获取行政区县
    listAdministrativeAreas () {
      getAdministrativeAreas().then((response) => {
        this.administrativeAreas = response.data;
      });
    },
    // 获取街道
    liststreetTowns (administrativeAreaId) {
      getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
          this.form.streetTownId = '';
          this.form.liaisonStation = '';
        }
      );
    },
    // 选择行政区县
    queryAdministrativeAreaChange (val) {
      this.queryForm.streetTownId = undefined;
      this.liststreetTowns(val);
    },
    // 选择街道乡镇
    handlestreetTownChange (val) {
      this.queryForm.liaisonStationId = undefined;
      this.listLiaisonStations(val);
    },
    // 获取联络站下拉列表
    listLiaisonStations (streetTownId) {
      getStations({ streetTownId: streetTownId }).then((response) => {
        this.liaisonStations = response.data;
      });
    },
    // 选择联络站
    handleListLiaisonStationChange (id) {
      for (let index = 0; index < this.liaisonStations.length; index++) {
        const element = this.liaisonStations[index];
        if (element.id == id) {
          // if(element.contactPhoneNumber) {
          //   this.queryForm.contactPhoneNumber = element.contactPhoneNumber;
          // }
          // if(element.contactName) {
          //   this.queryForm.contactName = element.contactName;
          // }
          // if(element) {
          //   this.queryForm.liaisonStation = element;
          // }
          this.$forceUpdate();
          break;
        }
      }
    },
    //列表
    fetchData () {
      this.listLoading = true;
      evaluationWorkList(this.queryForm).then((response) => {
        this.list = response.rows;
        this.total = response.total;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    //搜索
    search () {
      this.queryForm.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    //重置
    reset () {
      this.queryForm = {
        administrativeAreaId: undefined,
        streetTownId: undefined,
        pageNum: 1,
        pageSize: 10,
        userName: "",
        serverTime: "",
      };
      this.pagination.pageSize = 10;
      this.pagination.current = 1;
      this.fetchData();
    },
    //高级搜索
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    handleTimeRangeChange () { },

    //时间范围改变
    handleTime (name) {
      var time = new Date();
      let Year = time.getFullYear(); /* 当前年份 */
      if (name == "最近三个月") {
        let startTime = time.toLocaleDateString(); /* 当前时间  */
        this.queryForm.startTime = startTime;
        time.setMonth(time.getMonth() + 3);
        this.queryForm.endTime = time.toLocaleDateString();
        this.showDisabled = true;
      } else if (name == "今年1~6月") {
        this.queryForm.startTime = `${Year}-01-01`;
        this.queryForm.endTime = `${Year}-06-30`;
        this.showDisabled = true;
      } else if (name == "今年7~12月") {
        this.queryForm.startTime = `${Year}-07-01`;
        this.queryForm.endTime = `${Year}-12-31`;
        this.showDisabled = true;
      } else if (name == "今年内") {
        this.queryForm.startTime = `${Year}-01-01`;
        this.queryForm.endTime = `${Year}-12-31`;
        this.showDisabled = true;
      } else if (name == "自定义") {
        this.queryForm.startTime = "";
        this.queryForm.endTime = "";
        this.showDisabled = false;
      } else if (name == "本届") {
        this.queryForm.startTime = "";
        this.queryForm.endTime = "";
        this.showDisabled = true;
      }
    },
    // 导出表格
    exportExcel () {
      this.exportExcelLoading = true;
      if (this.selectedRows.length == 0) {
        this.exportExcelLoading = false;
        return this.$message.warning("请选择数据！");
      }
      if (this.selectedRows.length > 0) {
        this.queryForm.ids = this.selectedRows
          .map((item) => item.id)
          .toString();
      }
      evaluationWorkExport(this.queryForm).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `代表对联络站工作的评价情况.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        this.selectedRows = [];
      });
      setTimeout(() => {
        this.exportExcelLoading = false;
      }, 1000);
    },
  },
};
</script>
