<template>
  <el-dialog :title="title" :visible.sync="dialogFormVisible" width="1000px" @close="close">
    <el-form ref="dataSubjectForm" :model="dataSubjectForm" :rules="rules" label-width="160px">
      <el-form-item label="主题名称" prop="subjectName">
        <el-input v-model="dataSubjectForm.subjectName" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="主题描述" prop="subjectDesc">
        <el-input v-model="dataSubjectForm.subjectDesc" autocomplete="off" type="textarea"></el-input>
      </el-form-item>
      <el-form-item label="主题状态" prop="subjectStatus">
        <el-select v-model="dataSubjectForm.subjectStatus" placeholder="请选择主题状态">
          <el-option
            v-for="item in subjectStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSourceId">
        <el-select v-model="dataSubjectForm.dataSourceId" filterable placeholder="请选择数据来源">
          <el-option
            v-for="item in dataSourceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上传单位" prop="sourceOrgId">
        <el-input v-model="dataSubjectForm.sourceOrgId" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="更新频率" prop="sourceUploadFrequency">
        <el-select v-model="dataSubjectForm.sourceUploadFrequency" placeholder="请选择更新频率">
          <el-option
            v-for="item in sourceUploadFrequencyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="需求单位" prop="targetOrgId">
        <el-input v-model="dataSubjectForm.targetOrgId" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="数据类型" prop="sourceDataType">
        <el-select
          v-model="dataSubjectForm.sourceDataType"
          default-first-option
          placeholder="请选择数据类型"
        >
          <el-option
            v-for="item in sourceDataTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 数据类型为“数据表” -->
      <el-form-item label="数据表名" prop="tableName" v-show="dataSubjectForm.sourceDataType == 0">
        <el-input v-model="dataSubjectForm.tableName" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item
        label="“创建时间”字段名"
        prop="fieldCreateTime"
        v-show="dataSubjectForm.sourceDataType == 0"
      >
        <el-input v-model="dataSubjectForm.fieldCreateTime" autocomplete="off"></el-input>
        <el-tooltip
          content="请填写数据表中记录数据“创建时间”的字段名，如“create_time”等"
          placement="bottom"
          effect="light"
        >
          <byui-icon :icon="['fas', 'info-circle']" style="margin-left: 5px;"></byui-icon>
        </el-tooltip>
      </el-form-item>
      <el-form-item
        label="“归属时间”字段名"
        prop="fieldDataTime"
        v-show="dataSubjectForm.sourceDataType == 0"
      >
        <el-input v-model="dataSubjectForm.fieldDataTime" autocomplete="off"></el-input>
        <el-tooltip
          content="请填写数据表中记录数据“归属时间”的字段名，“归属时间”描述了某条数据归属的时间段，比如用户在2020-02-01上传了一条《2020年1月外出统计总数》的数据，其中“2020-02-01”为创建时间，“2020年1月”为数据归属时间。"
          placement="bottom"
          effect="light"
        >
          <byui-icon :icon="['fas', 'info-circle']" style="margin-left: 5px;"></byui-icon>
        </el-tooltip>
      </el-form-item>
      <!-- 数据类型为“文件”或“目录“ -->
      <el-form-item
        label="文件路径"
        prop="path"
        v-show="dataSubjectForm.sourceDataType == 1 || dataSubjectForm.sourceDataType == 2"
      >
        <el-input v-model="dataSubjectForm.path" autocomplete="off"></el-input>
      </el-form-item>
      <!-- 数据类型为“API“ -->
      <el-form-item label="接口上下文" prop="uri" v-show="dataSubjectForm.sourceDataType == 3">
        <el-input v-model="dataSubjectForm.uri" autocomplete="off"></el-input>
      </el-form-item>

      <el-form-item label="督办开始时间" prop="superviseStartTime">
        <el-date-picker
          v-model="dataSubjectForm.superviseStartTime"
          type="date"
          placeholder="选择督办开始时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="督办状态" prop="superviseStatus">
        <el-select
          v-model="dataSubjectForm.superviseStatus"
          default-first-option
          placeholder="请选择督办状态"
        >
          <el-option
            v-for="item in superviseStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { doSave, doUpdate } from "@/api/datasource";
export default {
  name: "TableEdit",
  data() {
    return {
      dataSubjectForm: {
        subjectStatus: 0,
        sourceUploadFrequency: 3,
      },
      title: "",
      dialogFormVisible: false,
      isNewData: true,
      activeName: 'first',
      rules: {
        dsCode: [
          { required: true, message: "请输入数据源编号", trigger: "blur" },
        ],
        dsName: [
          { required: true, message: "请输入数据源名称", trigger: "blur" },
        ],
      },
      subjectStatusOptions: [{
        value: 0,
        label: "启用"
      },{
        value: 1,
        label: "禁用"
      }],
      dataSourceOptions: [],
      sourceUploadFrequencyOptions: [{
        value: 1,
        label: "日"
      },{
        value: 2,
        label: "周"
      },{
        value: 3,
        label: "月"
      },{
        value: 4,
        label: "季"
      },{
        value: 5,
        label: "半年"
      },{
        value: 6,
        label: "年"
      },],
      sourceDataTypeOptions: [{
        value: 0,
        label: "数据表"
      },{
        value: 1,
        label: "文件"
      },{
        value: 2,
        label: "目录"
      },{
        value: 3,
        label: "API"
      }],
      superviseStatusOptions: [{
        value: 0,
        label: "启用"
      },{
        value: 1,
        label: "禁用"
      }],
    };
  },
  created() {},
  methods: {
    showEdit(row, dataSourceOptions) {
      if(this.$refs["dataSubjectForm"]!==undefined){
        //清除表单验证用   
        this.$refs["dataSubjectForm"].clearValidate();
      }
      if (!row) {
        this.title = "新增数据源";
        this.isNewData = true;
      } else {
        this.title = "编辑数据源";
        this.dataSubjectForm = row;
        this.isNewData = false;
      }
      this.dialogFormVisible = true;
      this.dataSourceOptions = dataSourceOptions;
    },
    close() {
      this.dataSubjectForm = this.$options.data().dataSubjectForm;
      this.dialogFormVisible = false;
    },
    save() {
      this.$refs["dataSubjectForm"].validate(valid => {
        if (valid) {
          if(this.isNewData){
            doSave(this.dataSubjectForm).then(() => {
                this.dialogFormVisible = false;
                this.$parent.fetchData();
            });
          }else{
            doUpdate(this.dataSubjectForm).then(() => {
              this.dialogFormVisible = false;
              this.$parent.fetchData();
            });
          }
        }
      });
    },
    handleClick(tab, event) {
      console.log(tab, event);
    }
  },
};
</script>
