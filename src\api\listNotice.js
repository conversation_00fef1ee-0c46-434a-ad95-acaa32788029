import request from "@/utils/requestTemp";
import { instance_1, instance_3 } from "@/api/axiosRq";
import { instance_2 } from "@/api/axiosRq";
import qs from "qs";

export function getLiaisonStation(query) {
  return request({
    url: "/liaisonStationNotice/getLiaisonStation",
    method: "get",
    params: query,
  });
}
export function selectByList(query) {
  return request({
    url: "/liaisonStationNotice/selectByList",
    method: "get",
    params: query,
  });
}
//导出联络站公告详情（增加地址列）
export function selectByListAddress(query) {
  return request({
    url: "/liaisonStationNotice/selectByListWithAddress",
    method: "get",
    params: query,
  });
}
// 查询联络站公告列表
export function listNotice(query) {
  return request({
    url: "/liaisonStationNotice/list",
    method: "get",
    params: query,
  });
}

export function newListNotice(url, query) {
  return request({
    url,
    method: "get",
    params: query,
  });
}

// 查询联络站公告详细
export function getNotice(id) {
  return request({
    url: "/liaisonStationNotice/getInfo?id=" + id,
    method: "get",
  });
}

// 新增联络站公告
export function addNotice(data) {
  return request({
    url: "/liaisonStationNotice/add",
    method: "post",
    data: data,
  });
}

// 修改联络站公告
export function updateNotice(data) {
  return request({
    url: "/liaisonStationNotice/edit",
    method: "post",
    data: data,
  });
}

// 删除联络站公告
export function delNotice(id) {
  return request({
    url: "/liaisonStationNotice/del?id=" + id,
    method: "get",
  });
}

// 导出联络站公告
export function exportNotice(query) {
  return request({
    url: "/system/notice/export",
    method: "get",
    params: query,
  });
}

export function getSLianluoList(data) {
  return request({
    url: "/onlineLiaisonMember/list",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params: data,
  });
}
export function downloadWordTemplete() {
  return instance_1({
    url: "/contactFirm/download",
    method: "get",
    responseType: "blob",
  });
}
export function tongExportExcel(params, data) {
  return instance_2({
    url: `/station/export`,
    method: "post",
    responseType: "blob",
    params: params,
    data: data,
  });
}
export function getSLianluoTongList(data) {
  return request({
    url: "/station/listRport",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params: data,
  });
}

export function getSLianluoListManage(data) {
  return request({
    url: "/station/getMylist",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params: data,
  });
}

export function btnTong(data) {
  return request({
    url: "/station/audit",
    method: "post",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    data,
  });
}
export function updateQuestionType(data) {
  return request({
    url: "/station/updateQuestionType",
    method: "post",
    params: data,
  });
}
export function selectDbByid(id) {
  return request({
    url: "/onlineLiaisonMember/selectDbByid?id=" + id,
    method: "get",
  });
}

// 查询联络站公告详细
export function selectById(id) {
  return request({
    url: "/liaisonStationNotice/selectById?id=" + id,
    method: "get",
  });
}

export function selectByMap(data) {
  return request({
    url: "/liaisonStationNotice/selectByMap",
    method: "post",
    data,
  });
}


//预览
export function onPreview(id) {
  return request({
    url: "/liaisonStationNotice/preview?id=" + id,
    method: "get",
  });
}


export function GetWps(id) {
  return request({
    url: "/liaisonStationNotice/newPreview?id=" + id,
    method: "get",
  });
}

//下载公告
export function downloadDoc(id) {
  return request({
    url: "/liaisonStationNotice/downloadDoc?id=" + id,
    method: "get",
    responseType: "blob",
  });
}

//上传公告
export function uploadDoc(data,id) {
  return request({
    url: "/liaisonStationNotice/upload?id=" + id,
    method: "post",
    headers: {
      "Content-type": "multipart/form-data",
    },
    data: data,
  });
}

//查询建言献策列表
export function getAdviseList(data) {
  return request({
    url: "/station/getAdviseList",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params: data,
  });
}

export function exportAdvise(params, data) {
  return instance_2({
    url: `/station/exportAdvise`,
    method: "post",
    responseType: "blob",
    params: params,
    data: data,
  });
}

