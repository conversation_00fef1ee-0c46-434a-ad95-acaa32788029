<template>
  <div>
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <a-row class="formBox">

          <a-form-model ref="queryForm" :model="queryForm" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
            <!-- <a-col span="6">
              <a-form-model-item label="届次">
                <a-select v-model="queryForm.jcDm"
                          placeholder="请选择"
                          allow-clear>
                  <a-select-option v-for="item in SessionList"
                                   :key="item.id"
                                   :value="item.id">{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col> -->
            <!-- <a-col span="1" style="margin-right: 10px">
              <a-form-model-item>
                <a-button @click="fanhui()">返回</a-button>
              </a-form-model-item>
            </a-col> -->
            <a-col span="6">
              <a-form-model-item label="代表姓名" prop="activityName">
                <a-input v-model="queryForm.userName" placeholder="请请输入代表姓名" @keyup.native="fetchData"
                  autocomplete="off" allow-clear></a-input>
              </a-form-model-item>
            </a-col>

          </a-form-model>
          <a-form-model ref="queryForm" :model="queryForm" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
            <a-col :span="6">
              <a-form-model-item label="行政区县" prop="administrativeAreaId">
                <a-select v-model="queryForm.administrativeAreaId" placeholder="请选择行政区县"
                  @change="queryAdministrativeAreaChange">
                  <a-select-option v-for="item in administrativeAreas" :key="item.id" :label="item.name"
                    :value="item.id">{{  item.name  }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="街道乡镇" prop="streetTownId">
                <a-select v-model="queryForm.streetTownId" placeholder="请选择街道乡镇">
                  <a-select-option v-for="item in streetTowns" :key="item.id" :label="item.name" :value="item.id">{{
                     item.name
                    }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col span="5">
              <span style="float: right; margin-top: 3px;">
                <a-button type="primary" @click="fetchData()">搜索</a-button>
                <a-button style="margin-left: 12px;" @click="reset" class="pinkBoutton">重置</a-button>
              </span>
            </a-col>
          </a-form-model>
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <a-table ref="table" :bordered="false" class="directorySet-table" size="small" :columns="columns"
            :pagination="pagination"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio' }"
            :data-source="list" :row-key="(record, index) => { return index; }" :scroll="{ x: 300, y: 0 }"></a-table>
        </a-spin>
      </a-col>
    </a-row>
    <!-- 新增表单 -->
    <a-modal title="新增进社区活动" :visible.sync="addDialogVisible" width="50%" :footer="null" @cancel="close()">
      <activityFormModel :form="form" @close="addDialogVisible = false" @handleOk="fetchData()" oper="create">
      </activityFormModel>
    </a-modal>
  </div>
</template>

<script>
import { downloadCommunityActivitiesExcel, addCommunityActivitiesExcel } from "@/api/communityschedule";
import { mineList } from "@/api/communityscheduleactivity";
import activityFormModel from "./activityFormModel";
import { getList as getAdministrativeAreas } from "@/api/administrativearea";
import { getList as getstreetTowns } from "@/api/streettown";
import { getFormState } from "@/api/registrationMage/tableIng.js";
export default {
  components: { activityFormModel },
  filters: {},
  data() {
    return {
      SessionList: [
        { name: "第十五届", key: 1, id: "2" },
        { name: "第十六届", key: 2, id: "3" },
      ],
      addDialogVisible: false,
      auditDialogVisible: false,
      selectMemberDialogVisible: false,
      //list-----
      elementLoadingText: "正在加载...",
      list: [],
      listLoading: false,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      page: 1,
      size: 5,
      background: true,
      //list-----
      selectRows: [],
      form: {
        jcDm: "3",
        serverTime: "",
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        contactPhoneNumber: "",
        contactName: "",
        countryMemberIds: [],
        liaisonStation: "",
        inviteRangeDesc: "" /*  受邀范围 字符串*/,
      },
      queryForm: {
        startTime: "",
        endTime: "",
        streetTownId: undefined,
        pageNum: 1,
        pageSize: 5,
        timeRange: undefined,
      },
      periodList: [] /* 获取届次 */,
      stateList: [] /* 状态数据 */,
      administrativeAreas: [],
      streetTowns: [],
      showDisabled: false,
      advanced: false,

      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,
      // 列表
      columns: [
        {
          title: "当前状态",
          align: "center",
          width: 180,
          dataIndex: "currStateName",
          ellipsis: true,
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          title: "活动时间",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "serverTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "行政区划",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "administrativeAreaName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "街道乡镇",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "streetTownName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站名称",
          align: "center",
          width: 200,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.liaisonStationName;
          },
        },
        {
          title: "联系人",
          align: "center",
          width: 100,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.contactName || "/";
          },
        },
        {
          title: "联系人电话",
          align: "center",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.contactPhoneNumber || "/";
          },
        },
        {
          align: "center",
          title: "代表",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.inviteRangeDesc || "/";
          },
        },
        {
          align: "center",
          title: "活动内容",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.mark || "/";
          },
        },
        {
          align: "center",
          title: "录入日期",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.createTime || "/";
          },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 5, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["5", "10", "20", "30", "40"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      fileList: [],
      isDb: null,
    };
  },
  created() {
    // // 判断是否是路由跳转过来
    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));
    this.fetchData();
    this.$store.dispatch("navigation/breadcrumb1", "代表进社区");
    this.$store.dispatch("navigation/breadcrumb2", "进社区活动");
    // 获取状态下拉
    this.getFormStateList();
    this.listAdministrativeAreas();
    // this.queryAdministrativeAreaChange(this.queryForm.administrativeAreaId);
    // this.handlestreetTownChange(this.queryForm.streetTownId);
  },
  methods: {
    /**
     * 预加载数据
     */
    listAdministrativeAreas() {
      // 获取行政区县
      getAdministrativeAreas().then((response) => {
        this.administrativeAreas = response.data;
      });
    },
    liststreetTowns(administrativeAreaId) {
      getstreetTowns({ administrativeAreaId: administrativeAreaId }).then(
        (response) => {
          this.streetTowns = response.data;
        }
      );
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.queryForm.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.queryForm.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    // 多选选择
    onSelectChange(selectedRowKeys, rows) {
      this.selectRows = rows
      this.selectedRowKeys = selectedRowKeys
      this.$emit('changeSelected', rows[0].id)
    },
    // 下载
    downloadExcel() {
      downloadCommunityActivitiesExcel(this.queryForm).then(res => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区活动导入模板.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      })
    },
    // 上传文件限制 直接return
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },

    //导入活动表
    downImport({ file, fileList }) {
      // console.log(file);
      var data = new FormData();
      data.append("file", file);
      addCommunityActivitiesExcel(data).then((res) => {
        console.log(res, "res");
        if (res.data.code == "500") {
          return this.$message.error("上传失败！请选择正确的xls文件！");
        }
        if (res.data.code == "0000") {
          this.$baseMessage("上传成功", "success");
          this.fetchData()
        }
      });
    },
    //列表
    fetchData() {
      this.listLoading = true;
      mineList({ ...this.queryForm, isDb: this.isDb }).then((response) => {
        this.list = response.rows;
        this.total = response.total;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    handleSizeChange(val) {
      this.queryForm.size = val;
      this.fetchData();
    },
    handleAdd() {
      this.addDialogVisible = true;
    },
    // 新增关闭
    close() {
      this.addDialogVisible = false;
      this.initForm();
    },
    initForm() {
      this.form.jcDm = "3";
      this.form.serverTime = "";
      this.form.administrativeAreaId = "";
      this.form.streetTownId = undefined;
      this.form.liaisonStationId = undefined;
      this.form.contactPhoneNumber = "";
      this.form.contactName = "";
      this.form.countryMemberIds = [];
      this.form.currMember = [];
      this.$refs.form.resetFields();
      this.$refs.form.clearValidate();
    },
    queryAdministrativeAreaChange(val) {
      this.queryForm.streetTownId = undefined;
      this.liststreetTowns(val);
    },
    details(row) { },
    reset() {
      this.queryForm = {
        startTime: "",
        endTime: "",
        streetTownId: undefined,
        pageNum: 1,
        pageSize: 5,
      };
      this.fetchData();
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    handleTimeRangeChange() { },
    handleTime(name) {
      var time = new Date();
      let Year = time.getFullYear(); /* 当前年份 */
      if (name == "最近三个月") {
        let endTime = time.toLocaleDateString(); /* 当前时间  */
        this.queryForm.endTime = endTime;
        time.setMonth(time.getMonth() - 3);
        this.queryForm.startTime = time.toLocaleDateString();
        this.showDisabled = true;
      } else if (name == "今年1~6月") {
        this.queryForm.startTime = `${Year}-01-01`;
        this.queryForm.endTime = `${Year}-06-30`;
        this.showDisabled = true;
      } else if (name == "今年7~12月") {
        this.queryForm.startTime = `${Year}-07-01`;
        this.queryForm.endTime = `${Year}-12-31`;
        this.showDisabled = true;
      } else if (name == "今年内") {
        this.queryForm.startTime = `${Year}-01-01`;
        this.queryForm.endTime = `${Year}-12-31`;
        this.showDisabled = true;
      } else if (name == "自定义") {
        this.queryForm.startTime = "";
        this.queryForm.endTime = "";
        this.showDisabled = false;
      } else if (name == "本届") {
        this.queryForm.startTime = "";
        this.queryForm.endTime = "";
        this.showDisabled = true;
      }
    },
    // 获取状态
    getFormStateList() {
      getFormState({ jcDm: this.queryForm.jcDm }).then((res) => {
        if (res.data.data) {
          this.stateList = res.data.data;
        }
      });
    },
    //新增社情民意
    addSocialconditions(id) {
      this.$router.push({
        path: "/community/newlyAddedList",
        query: {
          communityScheduleId: id,
        },
      });
    },
    fanhui() {
      this.$router.go(-1);
    },
  },
};
</script>
