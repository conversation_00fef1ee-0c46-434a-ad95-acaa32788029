<template>
  <div class="table-container">
    <div>
      <div class="jbxx-contt">
        <div class="jbqkss"><span class="jbqkbz">待办事项</span></div>
        <!-- <div class="fengexian"></div> -->
        <div style="padding: 18px; 0">
          <a-row>
            <a-col v-for="(item, index) in ToDoList"
                   :key="index"
                   :span="6"
                   :offset="1"
                   @click="clickIndex(item)">
              <div class="jbxxson_Big">
                <img :src="item.img"
                     :alt="item.name" />
                <div class="jbxxson_Big_box">
                  <div class="tits">
                    {{ item.name }}
                    <!-- <span v-show="item.num != null" class="Numbers"
                      >({{ item.num }})</span
                    > -->
                  </div>
                  <!-- <span style="font-size: 16px;font-weight: 400;letter-spacing: 0px;color: rgba(56, 56, 56, 1);">个</span> -->
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
      <div class="jbxx-contt"
           style="padding-bottom: 20px;">
        <div class="jbqkss"><span class="jbqkbz">数据查询</span></div>
        <div>
          <a-row>
            <a-col v-for="(item, index) in DataqueryList"
                   :key="index"
                   :span="4"
                   @click="clickIndex(item)">
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.img"
                     :alt="item.name" />
                <div class="jbxxson_box">
                  <div class="tits"
                       style="">{{ item.name }}</div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
                <div v-if="item.size"
                     class="sizeData">{{ item.size }}</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
      <div class="jbxx-contt"
           style="padding-bottom: 20px;">
        <div class="jbqkss"><span class="jbqkbz">统计报表</span></div>
        <!-- <div class="fengexian"></div> -->
        <div>
          <a-row>
            <a-col v-for="(item, index) in OtheServicesList"
                   :key="index"
                   :span="4"
                   @click="clickIndex(item)">
              <div class="jbxxson Heightjbxxson cardHvover">
                <img :src="item.img"
                     :alt="item.name" />
                <div class="jbxxson_box">
                  <div class="tits"
                       style="">{{ item.name }}</div>
                  <div style=""
                       class="remarks"
                       v-html="item.remarks"></div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getLevelList } from "@/api/registrationMage/tableIng.js";
import { instance_1, instance_yajy, instance_3 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
import { backlogValue } from "@/api/area.js";
import { backlogHandle } from "@/api/area.js";
export default {
  components: {},
  data () {
    return {
      TBloading: false,
      // ===)代表选举工作人员首页~
      // 待办事项
      ToDoList: [
        {
          name: "我的待办",
          remarks: "",
          num: 0,
          img: require("@/assets/images/indexImage/代表选举管理/我的待办.png"),
          system: false,
          path: "/electoralMage/information",
        },
        {
          name: "我的已办",
          remarks: "",
          num: 0,
          img: require("@/assets/images/indexImage/代表选举管理/我的已办.png"),
          system: false,
          path: "/electoralMage/matters",
        },
      ],
      // 数据查询
      DataqueryList: [
        {
          name: "预备人选查询",
          remarks: "工作人员<br/>查询预备人选",
          img: require("@/assets/images/indexImage/代表选举管理/预备人选查询.png"),
          system: false,
          path: "/comprehensivequery/Comprehensivequery",
          size: null,
        },
        {
          name: "筛选人选查询",
          remarks: "工作人员<br/>查询筛选人选",
          img: require("@/assets/images/indexImage/代表选举管理/筛选人选查询.png"),
          system: false,
          path: "/comprehensivequery/Comprehensivequery", //跳转平台门户
          size: null,
          systemCode: "lzptmh",
        },
        {
          name: "投票选举查询",
          remarks: "工作人员查询投票选举情况",
          img: require("@/assets/images/indexImage/代表选举管理/投票选举查询.png"),
          system: false,
          path: "/comprehensivequery/Comprehensivequery",
          size: null,
        },
        {
          name: "当选代表查询",
          remarks: "工作人员查询当选代表",
          img: require("@/assets/images/indexImage/代表选举管理/当选查询.png"),
          system: false,
          path: "/comprehensivequery/Comprehensivequery",
          size: null,
        },
        {
          name: "出缺申报查询",
          remarks: "工作人员查询出缺情况",
          img: require("@/assets/images/indexImage/代表选举管理/出缺申报查询.png"),
          system: false,
          path: "/lackData/distributioReport",
          size: null,
        },
      ],
      OtheServicesList: [
        {
          name: "预备人选统计报表",
          remarks: "预备人选<br/>的统计报表",
          img: require("@/assets/images/indexImage/代表选举管理/预备人选统计报表.png"),
          path: "/ready/preparatoryPersonReport",
          size: null,
        },
        {
          name: "候选人选统计报表",
          remarks: "候选人选<br/>的统计报表",
          img: require("@/assets/images/indexImage/代表选举管理/候选人选统计报表.png"),
          size: null,
          system: false,
          path: "/candidate/candidateReport",
        },
        {
          name: "投票选举统计报表",
          remarks: "投票选举<br/>的统计报表",
          img: require("@/assets/images/indexImage/代表选举管理/投票选举统计报表.png"),
          size: null,
          system: false,
          path: "/ballot/enrollReport",
        },
        {
          name: "当选代表统计报表",
          remarks: "当选代表的统计报表",
          img: require("@/assets/images/indexImage/代表选举管理/当选统计报表.png"),
          size: null,
          system: false,
          path: "/elected/electedStatistics",
        },
        {
          name: "出缺申报统计报表",
          remarks: "出缺情况的统计报表",
          img: require("@/assets/images/indexImage/代表选举管理/出缺申报统计报表.png"),
          size: null,
          system: false,
          path: "/lackData/repStatistics",
        },
        {
          name: "代表统计表",
          remarks: "查询代表选举情况",
          img: require("@/assets/images/indexImage/代表选举管理/代表统计表.png"),
          size: null,
          system: false,
          path: "/comprehensivequery/Comprehensivequery",
        },
      ],
      isDb: null,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      form: {
        jcDm: null,
        activityName: null,
        startTime: null,
        endTime: null,
      },
    };
  },

  created () {
    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));
    // this.getLevelListData(); //获取待办统计数
  },
  methods: {
    checkPermission,
    // 获取节次
    getLevelListData () {
      getLevelList({}).then((res) => {
        if (res.data.code == "0000") {
          this.form.jcDm = res.data.data[0].jcDm;
          this.getNotRead();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    async getNotRead () {
      //  未办统计数
      // let res = await backlogValue(this.queryForm, this.form);
      // if (res.code == 200) {
      //   this.ToDoList[0].num = res.total; //待办总数
      //   this.$forceUpdate();
      // }
      // this.getRead();
    },
    async getRead () {
      //  已办统计数
      // let res = await backlogHandle(this.queryForm, this.form);
      // if (res.code == 200) {
      //   this.ToDoList[1].num = res.total; //已办总数
      //   this.$forceUpdate();
      // }
    },
    clickIndex (event) {
      // if (
      //   this.isDb == "0" &&
      //   (event.name == "代表随手拍" || event.name == "社情民意专报")
      // ) {
      //   this.$message.info("对不起此模块仅向人大代表开放！");
      // }

      // 跨子系统跳转
      if (event.system) {
        let locationSystem = {
          path: this.$router.history.current.path,
          systemCode: this.$router.history.current.meta.systemId,
        };
        sessionStorage.setItem(
          "locationSystem",
          JSON.stringify(locationSystem)
        );
        this.$nextTick(() => {
          window.top.postMessage(
            {
              event: "locationSystem", // 约定的消息事件
              args: {
                path: event.path, //路径
                systemCode: event.systemCode, //子系统编码
              }, // 参数
            },
            "*"
          );
        });
      }
      //其他情况
      else if (event.name == "工作动态") {
        window.open("https://www.rd.gz.cn/");
      }
      // 本子系统路由跳转
      else {
        var isDb = event.type ? true : null;

        this.$router.push({ path: event.path, query: { isDb: isDb } });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 0px !important;
  background-color: rgb(246, 246, 246) !important;
}

.tits {
  // font-size: 16px;
  @include add-size($font_size_16);
  color: rgba(56, 56, 56, 1);
  font-weight: 700;
  margin-top: 15px;
  cursor: pointer;
}

.wddb1 {
  float: left;
  // font-size: 20px;
  @include add-size($font_size_16);
  color: black;
  font-weight: 700;
}

.jbxxson_Big {
  cursor: pointer;
  display: flex;
  align-content: center;
  flex-direction: row;
  padding: 10px;
  margin: 5% 10px;
  height: 20%;
  border-radius: 10px;
  align-items: center;
  justify-content: center;

  img {
    width: 18%;
    margin-right: 12%;
  }

  .jbxxson_Big_box {
    .Numbers {
      text-align: center;
      color: #cc3131;
      // font-size: 24px;
      // font-weight: 600;
    }

    .tits {
      font-size: 18px !important;
      font-weight: 400;
      letter-spacing: 0px;
      color: rgba(56, 56, 56, 1);
      text-align: left;
      vertical-align: top;
      margin: 0 auto;
      width: 100%;
      text-align: center;
      font-weight: 500; //小标题加粗
    }

    .remarks {
      width: 100%;
      text-align: center;
      margin: 1% auto;
    }

    color: rgba(153, 153, 153, 1);
  }
}

.jbxxson {
  cursor: pointer;
  box-shadow: 1.03px 1.85px 4px 0px rgb(166 166 166 / 51%);
  display: flex;
  align-content: center;
  flex-direction: column;
  padding: 10px;
  margin: 8% 6%;
  height: 20%;
  // border-radius: 10px;
  justify-content: flex-start;

  img {
    width: 38px;
    height: 38px;
    margin: 0% auto;
    margin-top: 10px;
  }

  .jbxxson_box {
    .tits {
      margin: 0 auto;
      width: 100%;
      text-align: center;
      color: rgba(56, 56, 56, 1);
      margin: 10px auto;
      font-weight: 600; //盒子标题
    }

    .remarks {
      width: 100%;
      text-align: center;
      margin: 1% auto;
    }

    color: rgba(153, 153, 153, 1);
  }

  .sizeData {
    position: absolute;
    right: 0%;
    color: #f4efef;
    font-family: pingFang-M;
    top: -5px;
    height: 37px;
    width: 37px;
    line-height: 37px;
    border-radius: 30px;
    background-color: #d52838;
    text-align: center;
  }
}

.a1 {
  color: #999999;
}

.jbxx-contt {
  background-color: #fff;
  width: 100%;
  border: 5px solid #f6f6f6;
  border-radius: 10px;
}

.rightCyygn {
  width: 20%;
  margin-right: 50px;
}

.cygn1 {
  width: 100%;
  display: flex;
  // justify-content: space-between;
  flex-wrap: wrap;
}

.imgss1 {
  width: 27%;
  margin-left: 20px;
  text-align: center;
  margin-bottom: 25px;

  div {
    height: 85px;
  }

  img {
    width: 80%;
    height: 80%;
  }
}

::v-deep {
  .ant-badge-status-dot {
    width: 10px !important;
    height: 10px !important;
  }
}

.xxssdtcont {
  display: flex;
  width: 100%;
}

.xxssdtLeft {
  width: 30%;
  color: #999999;
}

.dcl12 {
  margin-left: 15px;
  // font-size: 16px;
  @include add-size($font_size_16);
  color: #f50;
}

.xxssdtRight {
  // display: flex;
  width: 65%;
}

.slr12 {
  // float: right;
  margin-top: 5px;
  margin-left: 20px;
  color: #999999;
}

.slr15 {
  margin-top: 5px;
  margin-left: 20px;
}

.jbqkbz {
  padding-left: 18px;
  border-left: 4px solid rgba(199, 28, 51, 1);
  // font-size: 16px;
  @include add-size($font_size_16);
  font-weight: 700;
}

.sjcx_son {
  display: flex;
  flex-wrap: wrap;
  margin-left: 35px;
}

.fengexian {
  width: 100%;
  height: 1px;
  background: rgba(237, 237, 237, 1);
  margin: 25px 10px 20px 10px;
}

.jbqkss {
  padding: 10px 1%;
}

.cardHvover:hover {
  // background-color: #fbeeef;
  outline: 1px solid #d43030;
}
.Heightjbxxson {
  min-height: 160px;
}

// 备注有俩行给固定高度
@media screen and (max-width: 1920px) {
  .Heightjbxxson {
    min-height: 160px;
  }
}

@media screen and (max-width: 1600px) {
  .Heightjbxxson {
    min-height: 160px;
  }
}

@media screen and (max-width: 1280px) {
  .Heightjbxxson {
    min-height: 180px;
  }
}
</style>
