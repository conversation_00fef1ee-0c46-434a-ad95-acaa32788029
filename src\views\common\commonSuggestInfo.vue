<template>
  <div>
    <a-modal :visible="visible"  width="80%" :centered="true" :footer="null"
      :body-style="{ height: '600px', overflowY: 'auto' }" @cancel="close">
        <div  class="title-container" slot="title">
          <div>
            <span class="title-text" style="font-size:18px; display:block; text-align:center; margin-bottom: 5px;">{{ record.proposalTitle }}</span>
          </div>
<!--          <span class="tip-message"  style="font-size:12px" >{{ tipMessage }}</span>-->
<!--          <span class="tip-message"  v-html="tipMessage" style="font-size:13px" ></span>-->
          <!-- 其他内容 -->
          <div style="display: grid; place-items: center;  margin-bottom: 5px">
            <a-steps v-model="current" class="custom-steps" >
              <a-step disabled v-for="(step, index) in flowNodeDataSource" :key="index" :title="step.showDec" />
           </a-steps>
          </div>
        </div>
<!--      <div>-->

<!--      </div>-->
<!--      <div slot="title" v-else>-->
<!--        <p class="tip-x">建议详情</p>-->
<!--      </div>-->
      <EnterContent :alert="tipMessage"
      />
      <a-tabs v-model="tabsKey" type="card" @change="changeTab">
        <a-tab-pane key="1" v-if="record.proposalType != '2'" tab="议案建议信息">
<!--          <div style="display: flex; justify-content: space-between; width: 100%; margin-bottom: 10px;">-->
<!--            <span  style="font-weight: bold; margin-left: auto">建议号：{{ record.proposalNum }}</span>-->
<!--            <span  style="font-weight: bold; ">状态：{{ record.status | filterStatus }}</span>-->
<!--          </div>-->
          <div style="display: flex; justify-content: center; align-items: center; width: 100%; margin-bottom: 10px;">
            <span style="font-weight: bold; margin-right: 990px;">建议号：{{ record.proposalNum }}</span>
            <span style="font-weight: bold;">状态：{{ record.status | filterStatus }}</span>
          </div>
          <div :style="tabStyle">
            <a-descriptions bordered size="small">
              <a-descriptions-item label="届次" :labelStyle="{ width:'150px' }">{{
                  record.periodDesc
                }}</a-descriptions-item>
              <a-descriptions-item label="议案建议类型">{{
                  record.proposalType | filterProposalType
                }}</a-descriptions-item>
              <a-descriptions-item label="内容所属类别">{{
                  record.proposalContentType | filterProposalContentType
                }}</a-descriptions-item>
              <a-descriptions-item label="领衔代表">{{
                  record.headPer
                }}</a-descriptions-item>
              <a-descriptions-item label="所在代表团">{{
                  record.deputyOrg
                }}</a-descriptions-item>
              <a-descriptions-item label="代表本人是否同意公开">{{
                  record.ifPublice == "0"
                    ? "不"
                    : record.ifPublice == "1"
                    ? "是"
                    : record.ifPublice == "2"
                      ? "已公开"
                      : ""
                }}</a-descriptions-item>
              <a-descriptions-item label="代表建议答复方式">{{
                  record.codeHaveOrNo == "1"
                    ? "书面"
                    : record.codeHaveOrNo == "2"
                    ? "网上"
                    : record.codeHaveOrNo == "0"
                    ? "只做工作参考用，无需正式答复"
                    : ""
                }}</a-descriptions-item>
              <a-descriptions-item label="代表与承办单位沟通方式">
                {{
                  record.ifContect == "1"
                    ? "需要见面座谈和调研"
                    : record.ifContect == "0"
                    ? "只需电话微信沟通"
                    : record.ifContect == "2"
                      ? "不需要沟通，直接答复"
                      : record.ifContect == "3"
                        ? "工作参考用，不需要正式书面答复"
                        : ""
                }}
              </a-descriptions-item>
              <a-descriptions-item label="多年多次提出、尚未解决">{{
                  record.overTheYearsNotResolved == "1"
                    ? "是"
                    : record.overTheYearsNotResolved == "0"
                    ? "否"
                    : record.overTheYearsNotResolved == "2"
                      ? "未详"
                      : ""
                }}</a-descriptions-item>
<!--              <a-descriptions-item label="标题" :span="2">{{-->
<!--                  record.proposalTitle-->
<!--                }}</a-descriptions-item>-->
              <a-descriptions-item label="意向承办单位" :span="3">{{
                  record.intentOrgName || ""
                }}</a-descriptions-item>
              <a-descriptions-item label="当前经办人" :span="3">
                <div v-if="record.yajyOrgList && record.yajyOrgList.length > 0">
                    <span v-for="(item, index) in record.yajyOrgList" :key="index">
                      {{
                        item.orgName +
                        "(" +
                        item.orgDispname +
                        item.orgDispphone +
                        ");"
                      }}
                    </span>
                </div>
                <div v-if="record.yajyOrgOperDTOList &&
                    record.yajyOrgOperDTOList.length > 0
                    ">
                    <span v-if="record.yajyOrgOperDTOList[0].orgCode == '387'">
                      <span>
                        {{
                          record.yajyOrgOperDTOList[0].orgName +
                          "(" +
                          record.yajyOrgOperDTOList[0].telNo +
                          ")"
                        }}
                      </span>
                      <br />
                    </span>
                  <span v-else>
                      <span v-for="(item, index) in record.yajyOrgOperDTOList" :key="index">
                        {{ item.operName + "(" + item.telNo + ")" }}
                      </span>
                    </span>
                </div>
                <div v-if="record.identityAllocationList && record.identityAllocationList.length > 0">
                    <span v-for="(item, index) in record.identityAllocationList" :key="index">
                      {{
                        item.orgName +
                        "(" +
                        item.userName +
                        item.mobile +
                        ");"
                      }}
                    </span>
                </div>

              </a-descriptions-item>
              <a-descriptions-item label="交办日期">{{
                  record.handleDate
                }}</a-descriptions-item>
              <a-descriptions-item label="办理期限至">主办：{{
                  majorLastTime ? majorLastTime.substring(0, 10) : ""
                }}
                会办：{{
                  minorLastTime ? minorLastTime.substring(0, 10) : ""
                }}</a-descriptions-item>
              <!-- 后续调整 -->
<!--                <a-descriptions-item v-if="userData.authorities[0].authority == 'YAJY_XLW' ||-->
<!--                userData.authorities[0].authority == 'YAJY_DBC' || userData.authorities[0].authority == 'YAJY_DB'" label="办理期限至">主办：{{-->
<!--                  majorLastTime ? majorLastTime.substring(0, 10) : ""-->
<!--                }}-->
<!--                会办：{{-->
<!--                  minorLastTime ? minorLastTime.substring(0, 10) : ""-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item v-else label="办理期限至">{{ onlyMajorLastTime ? onlyMajorLastTime.substring(0, 10) : "" }}</a-descriptions-item>-->
              <a-descriptions-item label="办理情况">{{
                  record.proposalHandle
                }}</a-descriptions-item>
              <a-descriptions-item label="代表工委是否公开意见" :span="1">{{
                  record.xlwPublic == "0" || record.xlwPublic == null
                    ? "未提出意见"
                    : record.xlwPublic == "1"
                    ? "建议公开"
                    : record.xlwPublic == "2"
                      ? "建议不公开"
                      : ""
                }}
                <a-button type="primary" @click="handleComment('xlw')" style="margin-left: 70px;">查看出具意见</a-button>
              </a-descriptions-item>
              <a-descriptions-item label="研究室建议是否公开" :span="1">{{
                  record.yjsPublic == "0" || record.yjsPublic == null
                    ? "未提出意见"
                    : record.yjsPublic == "1"
                    ? "建议公开"
                    : record.yjsPublic == "2"
                      ? "建议不公开"
                      : ""
                }}
                  <a-button type="primary" @click="handleComment('yjs')" style="margin-left: 70px;">查看出具意见</a-button>
              </a-descriptions-item>
<!--              <a-descriptions-item label="代表工委是否公开意见" :span="1" v-if="isMeeting ==1">{{-->
<!--                  record.examineXlwSugg == "0" || record.examineXlwSugg == null-->
<!--                    ? "未提出意见"-->
<!--                    : record.examineXlwSugg == "1"-->
<!--                    ? "建议公开"-->
<!--                    : record.examineXlwSugg == "2"-->
<!--                      ? "建议不公开"-->
<!--                      : ""-->
<!--                }}-->
<!--              </a-descriptions-item>-->
<!--              <a-descriptions-item label="研究室建议是否公开" :span="1" v-if="isMeeting ==1">{{-->
<!--                  record.examineXcc == "0" || record.examineXcc == null-->
<!--                    ? "未提出意见"-->
<!--                    : record.examineXcc == "1"-->
<!--                    ? "建议公开"-->
<!--                    : record.examineXcc == "2"-->
<!--                      ? "建议不公开"-->
<!--                      : ""-->
<!--                }}-->
<!--              </a-descriptions-item>-->
              <a-descriptions-item label="建议审核是否公开发布" :span="1">{{
                  record.examineXlgw == "0"
                    ? "未审核"
                    : record.examineXlgw == "1"
                    ? "审核公开"
                    : record.examineXlgw == "2"
                      ? "审核不公开"
                      : ""
                }}
              </a-descriptions-item>
              <a-descriptions-item label="建议纸" :span="1" v-if="this.mediaType != '其他建议'"><a
                @click="downMotion">下载建议纸</a>
              </a-descriptions-item>
              <a-descriptions-item label="附件列表" :span="2" v-if="mediaType != '其他建议'"><span
                v-if="record.existFile == true">附件：<a @click="downFileData('1', '')">附件下载</a></span>
              </a-descriptions-item>
              <a-descriptions-item label="正文" :span="2">
                <a-space>
                  <a @click="lookWord">查看正文</a>
                  <a @click="downFileData('9', '')" v-if="this.mediaType != '其他建议'">下载正文附件</a>
                  <a @click="resetFile" v-if="this.mediaType != '其他建议'">重新上传正文附件</a>
                  <!-- <a @click="regenerateText">重新生成正文</a> -->
                </a-space>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-tab-pane>
        <a-tab-pane key="1" v-if="record.proposalType == '2' " tab="议案建议信息">

          <div style="display: flex; justify-content: center; align-items: center; width: 100%; margin-bottom: 10px;">
            <span style="font-weight: bold; margin-right: 990px;">建议号：{{ record.proposalNum }}</span>
            <span style="font-weight: bold;">状态：{{ record.status | filterStatus }}</span>
          </div>
          <div :style="tabStyle">
            <a-descriptions bordered size="small">
              <a-descriptions-item label="届次" :labelStyle="{ width:'150px' }">{{
                  record.periodDesc
                }}</a-descriptions-item>
              <a-descriptions-item label="议案建议类型">{{
                  record.proposalType | filterProposalType
                }}</a-descriptions-item>
              <a-descriptions-item label="内容所属类别">{{
                  record.proposalContentType | filterProposalContentType
                }}</a-descriptions-item>
              <a-descriptions-item label="领衔代表">{{
                  record.headPer
                }}</a-descriptions-item>
              <a-descriptions-item label="所在代表团">{{
                  record.deputyOrg
                }}</a-descriptions-item>
              <!-- <a-descriptions-item label="代表本人公开意向">{{
                  record.ifPublice == "0"
                    ? "不公开"
                    : record.ifPublice == "1"
                    ? "公开"
                    : record.ifPublice == "2"
                      ? "已公开"
                      : ""
                }}</a-descriptions-item> -->
              <a-descriptions-item label="代表建议答复方式">{{
                  record.codeHaveOrNo == "1"
                    ? "书面"
                    : record.codeHaveOrNo == "2"
                    ? "网上"
                    : record.codeHaveOrNo == "0"
                    ? "只做工作参考用，无需正式答复"
                    : ""
                }}</a-descriptions-item>
              <a-descriptions-item label="代表与承办单位沟通方式">
                {{
                  record.ifContect == "1"
                    ? "需要见面座谈和调研"
                    : record.ifContect == "0"
                    ? "只需电话微信沟通"
                    : record.ifContect == "2"
                      ? "不需要沟通，直接答复"
                      : record.ifContect == "3"
                        ? "工作参考用，不需要正式书面答复"
                        : ""
                }}
              </a-descriptions-item>
              <a-descriptions-item label="多年多次提出、尚未解决">{{
                  record.overTheYearsNotResolved == "1"
                    ? "是"
                    : record.overTheYearsNotResolved == "0"
                    ? "否"
                    : record.overTheYearsNotResolved == "2"
                      ? "未详"
                      : ""
                }}</a-descriptions-item>

              <a-descriptions-item label="意向承办单位" :span="3">{{
                  record.intentOrgName || ""
                }}</a-descriptions-item>
              <a-descriptions-item label="当前经办人" :span="3">
                <div v-if="record.yajyOrgList && record.yajyOrgList.length > 0">
                    <span v-for="(item, index) in record.yajyOrgList" :key="index">
                      {{
                        item.orgName +
                        "(" +
                        item.orgDispname +
                        item.orgDispphone +
                        ");"
                      }}
                    </span>
                </div>
                <div v-if="record.yajyOrgOperDTOList &&
                    record.yajyOrgOperDTOList.length > 0
                    ">
                    <span v-if="record.yajyOrgOperDTOList[0].orgCode == '387'">
                      <span>
                        {{
                          record.yajyOrgOperDTOList[0].orgName +
                          "(" +
                          record.yajyOrgOperDTOList[0].telNo +
                          ")"
                        }}
                      </span>
                      <br />
                    </span>
                  <span v-else>
                      <span v-for="(item, index) in record.yajyOrgOperDTOList" :key="index">
                        {{ item.operName + "(" + item.telNo + ")" }}
                      </span>
                    </span>
                </div>
                <div v-if="record.identityAllocationList && record.identityAllocationList.length > 0">
                    <span v-for="(item, index) in record.identityAllocationList" :key="index">
                      {{
                        item.orgName +
                        "(" +
                        item.userName +
                        item.mobile +
                        ");"
                      }}
                    </span>
                </div>

              </a-descriptions-item>
              <a-descriptions-item label="交办日期">{{
                  record.handleDate
                }}</a-descriptions-item>
              <a-descriptions-item label="办理期限至">主办：{{
                  majorLastTime ? majorLastTime.substring(0, 10) : ""
                }}
                会办：{{
                  minorLastTime ? minorLastTime.substring(0, 10) : ""
                }}</a-descriptions-item>

              <a-descriptions-item label="办理情况">{{
                  record.proposalHandle
                }}</a-descriptions-item>



              <!-- <a-descriptions-item label="代表工委是否公开意见" :span="1">{{
                  record.xlwPublic == "0" || record.xlwPublic == null
                    ? "未提出意见"
                    : record.xlwPublic == "1"
                    ? "建议公开"
                    : record.xlwPublic == "2"
                      ? "建议不公开"
                      : ""
                }}
                <a-button type="primary" @click="handleComment('xlw')" style="margin-left: 70px;">查看出具意见</a-button>
              </a-descriptions-item> -->
              <!-- <a-descriptions-item label="研究室建议是否公开" :span="1">{{
                  record.yjsPublic == "0" || record.yjsPublic == null
                    ? "未提出意见"
                    : record.yjsPublic == "1"
                    ? "建议公开"
                    : record.yjsPublic == "2"
                      ? "建议不公开"
                      : ""
                }}
                  <a-button type="primary" @click="handleComment('yjs')" style="margin-left: 70px;">查看出具意见</a-button>
              </a-descriptions-item> -->

              <a-descriptions-item label="建议审核是否公开发布" :span="1">{{
                  record.examineXlgw == "0"
                    ? "未审核"
                    : record.examineXlgw == "1"
                    ? "审核公开"
                    : record.examineXlgw == "2"
                      ? "审核不公开"
                      : ""
                }}
              </a-descriptions-item>
            </a-descriptions>

            <!-- 建议公开信息专栏标题 -->
            <div class="section-header">建议公开信息</div>

            <a-descriptions bordered size="small">
              <a-descriptions-item label="代表本人公开意向">{{
                  record.ifPublice == "0"
                    ? "不公开"
                    : record.ifPublice == "1"
                    ? "公开"
                    : record.ifPublice == "2"
                      ? "已公开"
                      : ""
                }}
                <a-button type="primary" @click="handleComment('db')" style="margin-left: 70px;" v-if="record.dbPublic">查看出具意见</a-button>
              </a-descriptions-item>
              <a-descriptions-item label="建议交办小组公开意见">{{
                  record.ifPublice == "0"
                    ? "不公开"
                    : record.jbxzIfPublic == "0" || record.jbxzIfPublic == null
                    ? "未提出意见"
                    : record.jbxzIfPublic == "1"
                    ? "公开"
                    : record.jbxzIfPublic == "2"
                      ? "不公开"
                      : ""
                }}
                <a-button type="primary" @click="handleComment('jbxz')" style="margin-left: 70px;" v-if="record.jbxzPublic && record.ifPublice == '1'">查看出具意见</a-button>
                </a-descriptions-item>
              <a-descriptions-item label="代表工委公开意见">{{
                 record.ifPublice == "0"
                    ? "建议不公开"
                    : record.examineXlwSugg == "0" || record.examineXlwSugg == null
                    ? "未提出意见"
                    : record.examineXlwSugg == "1"
                    ? "建议公开"
                    : record.examineXlwSugg == "2"
                      ? "建议不公开"
                      : ""
                }}
                <a-button type="primary" @click="handleComment('dbgw')" style="margin-left: 70px;" v-if="record.examineXlwSuggPublic && record.ifPublice == '1'">查看出具意见</a-button>
                </a-descriptions-item>

              <a-descriptions-item label="研究室公开意见">{{
                  record.ifPublice == "0"
                    ? "建议不公开"
                    :
                  record.examineXcc == "0" || record.examineXcc == null
                    ? "未提出意见"
                    : record.examineXcc == "1"
                    ? "建议公开"
                    : record.examineXcc == "2"
                      ? "建议不公开"
                      : ""
                }}
                 <a-button type="primary" @click="handleComment('onlineyjs')" style="margin-left: 70px;" v-if="record.examineYjsPublic">查看出具意见</a-button>
                </a-descriptions-item>

              <a-descriptions-item label="常委会领导审定公开意见">{{
                 record.ifPublice == "0"
                    ? "审定不公开"
                    : record.examineCwhldsd == "0" || record.examineCwhldsd == null
                    ? "未审定"
                    : record.examineCwhldsd == "1"
                    ? "审定公开"
                    : record.examineCwhldsd == "2"
                      ? "审定不公开"
                      : ""
                }}
                </a-descriptions-item>

              <a-descriptions-item label="确认是否已在门户网站公开发布">
                {{
                record.ifPublice == "0" ? "否" :
                record.ifWebPublic == "1" || record.ifWebPublic == 1
                    ? "是"
                    : record.ifWebPublic == "0" || record.ifWebPublic == 0
                      ? "否"
                      : "待发布" }}
              </a-descriptions-item>


              <a-descriptions-item label="建议纸" :span="1" v-if="this.mediaType != '其他建议'"><a
                @click="downMotion">下载建议纸</a>
              </a-descriptions-item>
              <a-descriptions-item label="附件列表" :span="2" v-if="mediaType != '其他建议'"><span
                v-if="record.existFile == true">附件：<a @click="downFileData('1', '')">附件下载</a></span>
              </a-descriptions-item>
              <a-descriptions-item label="正文" :span="2">
                <a-space>
                  <a @click="lookWord">查看正文</a>
                  <a @click="downFileData('9', '')" v-if="this.mediaType != '其他建议'">下载正文附件</a>
                  <a @click="resetFile" v-if="this.mediaType != '其他建议'">重新上传正文附件</a>
                  <!-- <a @click="regenerateText">重新生成正文</a> -->
                </a-space>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="答复、反馈">
          <div style="display: flex; justify-content: space-between; width: 100%;">
            <span  style="font-weight: bold;">建议号：{{ record.proposalNum }}</span>
          </div>
          <div :style="tabStyle">
            <a-descriptions bordered size="small" style="margin-bottom: 10px;">
<!--              <a-descriptions-item label="标题" :span="3">{{-->
<!--                  record.proposalTitle-->
<!--                }}</a-descriptions-item>-->
<!--              <a-descriptions-item label="建议号">{{-->
<!--                  record.proposalNum-->
<!--                }}</a-descriptions-item>-->
              <a-descriptions-item label="议案建议类型">{{
                  record.proposalType | filterProposalType
                }}</a-descriptions-item>
              <a-descriptions-item label="领衔代表">{{
                  record.headPer
                }}</a-descriptions-item>
              <!-- 电话 短信 邮件 微信：0或1或4      2  座谈会  调研    3   代表表示无需见面沟通 -->
              <a-descriptions-item label="答复1沟通方式">{{
                  record.dffs == "1" || record.dffs == "0" || record.dffs == "4"
                    ? "电话 短信 邮件 微信"
                    : record.dffs == "2"
                    ? "座谈会 调研"
                    : record.dffs == "3"
                      ? "代表表示无需见面沟通"
                      : ""
                }}</a-descriptions-item>
            </a-descriptions>
                <a-descriptions v-if="majorUndertakeDfList.length > 0" class="red-line" title="主办单位"></a-descriptions>
            <template v-for="item in majorUndertakeDfList" v-if="majorUndertakeDfList.length > 0">
              <a-descriptions  :key="item.undertakeId" size="small" bordered >
                <a-descriptions-item labale label="单位名称" :span="3">{{
                    item.orgName
                  }}</a-descriptions-item>
                <a-descriptions-item label="签收人" :span="1">{{
                    item.signName
                  }}</a-descriptions-item>
                <a-descriptions-item label="签收人手机" :span="1"><a>{{ item.signPhone }}</a></a-descriptions-item>
                <a-descriptions-item label="是否签收" :span="1">{{
                    item.signTime ? item.signTime : "未签收"
                  }}</a-descriptions-item>
                <a-descriptions-item label="经办人" :span="2">部门：{{ item.jbrDept }} 职务：{{ item.jbrJob }} 姓名：{{
                    item.jbrName
                  }}</a-descriptions-item>
                <a-descriptions-item label="经办人手机" :span="1"><a>{{ item.jbrPhone }}</a></a-descriptions-item>
              </a-descriptions>
              <template v-for="(itemdf, index) in item.dfList">
                <a-descriptions :key="itemdf.dfId" size="small" bordered>
                  <a-descriptions-item :label="`第${index + 1}次答复时间`" :span="1">
                    {{ itemdf.dfrq }}
                  </a-descriptions-item>
                  <a-descriptions-item label="答复类型" :span="1">
                    <span v-if="itemdf.dflb == 'A'">已解决或基本解决的问题</span>
                    <span v-if="itemdf.dflb == 'B'">列入年度计划解决的问题</span>
                    <span v-if="itemdf.dflb == 'C'">列入规划逐步解决的问题</span>
                    <span v-if="itemdf.dflb == 'D'">受法规、政策、财力等客观条件限制，难以实现</span>
<!--                    <span>{{ itemdf.dflb }}</span>-->
                  </a-descriptions-item>
                  <a-descriptions-item label="答复公开" :span="1"><span>{{ itemdf.isPublic == "1" ? "是" : "否" }}</span>
                  </a-descriptions-item>
                  <a-descriptions-item v-if="itemdf.isPublic == '0'" label="答复不公开理由" :span="3"><span>{{
                      itemdf.remarkText }}</span>
                  </a-descriptions-item>
                  <a-descriptions-item label="答复附件" :span="3"><a v-if="itemdf.fujianList.length > 0"
                                                                 @click="downFileDataDf(itemdf, 2)">【下载答复附件】</a>
                  </a-descriptions-item>
                  <a-descriptions-item v-if="itemdf.hisEvaluation" :label="`第${index + 1}次反馈时间`" :span="1">
                    {{ itemdf.hisEvaluation.submittime }}
                  </a-descriptions-item>
                  <a-descriptions-item v-if="itemdf.hisEvaluation" label="反馈意见" :span="1"><span
                    v-if="itemdf.hisEvaluation.score >= 80">满意</span>
                    <span v-if="itemdf.hisEvaluation.score >= 60 &&
                        itemdf.hisEvaluation.score < 80
                        ">基本满意</span>
                    <span v-if="itemdf.hisEvaluation.score < 60">不满意</span>
                  </a-descriptions-item>
                  <a-descriptions-item v-if="itemdf.hisEvaluation" label="反馈内容" :span="1"><a
                    @click="viewFkEvaluation(itemdf.hisEvaluation)">【查看反馈内容】</a>
                  </a-descriptions-item>
                </a-descriptions>
              </template>
            </template>
            <a-descriptions v-if="minorUndertakeDfList.length > 0" class="red-line" title="会办单位"></a-descriptions>
            <template v-for="item in minorUndertakeDfList" v-if="minorUndertakeDfList.length > 0">
              <a-descriptions :key="item.undertakeId" size="small" bordered >
                <a-descriptions-item  label="单位名称" :span="3">{{
                    item.orgName
                  }}</a-descriptions-item>
                <a-descriptions-item label="签收人" :span="1">{{
                    item.signName
                  }}</a-descriptions-item>
                <a-descriptions-item  label="签收人手机" :span="1"><a>{{ item.signPhone }}</a></a-descriptions-item>
                <a-descriptions-item  label="是否签收" :span="1">{{
                    item.signTime ? item.signTime : "未签收"
                  }}</a-descriptions-item>
                <a-descriptions-item  label="经办人" :span="2">部门：{{ item.jbrDept }} 职务：{{ item.jbrJob }} 姓名：{{
                    item.jbrName
                  }}</a-descriptions-item>
                <a-descriptions-item  label="经办人手机" :span="1"><a>{{ item.jbrPhone }}</a></a-descriptions-item>
              </a-descriptions>
              <template v-for="itemdf in item.dfList">
                <a-descriptions class="hbdwDfInfo" :key="itemdf.dfId" size="small" bordered>
                  <a-descriptions-item label="答复时间" :span="1">
                    {{ itemdf.dfrq }}
                  </a-descriptions-item>
                  <a-descriptions-item label="附件名称" :span="2"><a v-if="itemdf.fujianList.length > 0"
                                                                 @click="downFileDataDf(itemdf, 2)">【下载答复附件】</a>
                  </a-descriptions-item>
                </a-descriptions>
              </template>
            </template>
          </div>
        </a-tab-pane>
        <a-tab-pane key="3" tab="提出代表">
          <div :style="tabStyle">
            <!-- v-if="
            ((record.status == 10 || record.status < 20) &&
              authority == 'YAJY_QRD') ||
            authority == 'YAJY_XLW' ||
            authority == 'YAJY_DBC'
          " -->
            <a-space v-if="userData.authorities
                ? record.status < 90 &&
                (userData.authorities[0].authority == 'YAJY_QRD' ||
                  userData.authorities[0].authority == 'YAJY_XLW' ||
                  userData.authorities[0].authority == 'YAJY_DBC' ||
                  userData.authorities[0].authority == 'I-XTGLY' ||
                  userData.authorities[0].authority == 'YAJY_DB')
                : false
                " class="operator">
              <a-button type="primary" icon="plus-circle" @click="showUserTable('提出')"
                        v-if="this.mediaType != '其他建议'">新建</a-button>
              <a-button icon="delete" @click="delJointlyList" v-if="this.mediaType != '其他建议'">删除</a-button>
            </a-space>
            <standard-table row-key="jointlyId" :columns="deputationColumns"
                            :selected-rows.sync="deputationSelectedRows" :data-source="deputationDataSource" :show-alert="userData.authorities
                  ? record.status < 90 &&
                  (userData.authorities[0].authority == 'YAJY_QRD' ||
                    userData.authorities[0].authority == 'YAJY_XLW' ||
                    userData.authorities[0].authority == 'YAJY_DBC' ||
                    userData.authorities[0].authority == 'I-XTGLY' ||
                    userData.authorities[0].authority == 'YAJY_DB')
                  : false
                  ">
            </standard-table>
          </div>
        </a-tab-pane>
        <a-tab-pane key="4" tab="承办单位">
          <div :style="tabStyle">
            <standard-table row-key="undertakeId" :columns="unitColumns" :data-source="unitDataSource">
            </standard-table>
          </div>
        </a-tab-pane>
        <a-tab-pane key="5" tab="办理进度和意见">
          <div :style="tabStyle">
            <!-- <standard-table :columns="processColumns" row-key="historyId" :data-source="processDataSource">
            </standard-table> -->

            <!-- 步骤条 -->
            <div class="step-container">
              <div v-for="(step, index) in processDataSource" :key="step.historyId" class="step"
                   :style="{ position: 'relative', marginBottom: index < processDataSource.length - 1 ? '20px' : '0' }">
                <!-- <div
                  :class="[
                    'step-circle',
                    { 'step-active': index <= currentStep, 'step-completed': index < currentStep }
                  ]"
                > -->
                <div class="step-circle step-active step-completed">
                  <!-- <span v-if="index <= currentStep" class="step-number">{{ index + 1 }}</span> -->
                </div>

                <div class="step-content">
                  <!-- replyType在最上面，居中显示 -->
                  <div class="step-reply-type">{{ step.replyType }}</div>
                  <div style="margin-left:20px">
                    <div class="step-reply-time">{{ step.replyTime }}</div>

                    <!-- 时间、提交人、办理意见在一行内，用灰色背景包裹，使用竖线分隔 -->
                    <div class="step-info">
                      <!-- 提交人、处理人 -->
                      <div class="info-item" style="width:20%">
                          <span v-if="index == 0">
                            <strong style="margin-right:10px">提交人:</strong> {{ step.reply_by_name }}
                          </span>
                        <span v-else>
                            <strong style="margin-right:10px">处理人:</strong> {{ step.reply_by_name }}
                          </span>
                      </div>
                      <!-- 岗位 -->
<!--                      <div class="info-item" style="width:20%">-->
<!--                          <span>-->
<!--                            <strong style="margin-right:10px">岗位:</strong>-->
<!--                            暂无数据-->
<!--                          </span>-->
<!--                      </div>-->
                      <!-- 办理意见 -->
                      <div class="info-item" style="width:60%">
                             <span v-if="step.replyComment" class="comment-text" :title="step.replyComment" @click="clickRowData(step)">
                              <div class="text-container"><strong style="margin-right:10px">办理结果:</strong>
                               {{ step.replyComment.length >= 90 ? step.replyComment.slice(0, 87)+"..."  : step.replyComment  }} </div>
                          </span>
                          <span v-else>
                            <strong style="margin-right:10px">办理结果:</strong> ------
                          </span>
                        <a v-if="step.fujianList" @click="downFileDataUnder(step, 50)">【下载不予立案理由】</a>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 连接线 -->
                <!-- :style="{ height: getLineHeight(index) + 'px' }" -->
                <div v-if="index < processDataSource.length - 1" class="step-line" style="height: 100%"></div>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="6" tab="公文答复函">
          <div :style="tabStyle">
            <standard-table :columns="dfhColumns" row-key="dfhId" :data-source="dfhDataSource">
            </standard-table>
          </div>
        </a-tab-pane>
      </a-tabs>

      <a-spin :indicator="indicator" :spinning="listLoading" style="margin-top: 30px;">
        <div v-if="showOperate">
          <!-- 联名 -->
          <div v-if="(record.status == 11 &&
            userData.authorities[0].authority == 'YAJY_DB' &&
            userData.userId != record.createUserId) ||
            scenesStatus == '处理联名'
            ">
            <div style="text-align: center; margin: 10px 0">
              <a-space class="operator">
                <a-button :loading="lmLoading" @click="changeJoin(1)">同意联名</a-button>
                <a-button :loading="lmLoading" @click="changeJoin(0)">不同意联名</a-button>
              </a-space>
            </div>
          </div>
          <!-- 交办 大会期间 -->
        <div v-if="isRendered">
          <div v-if="record.status == 40 && scenesStatus == '' && isMeeting  == 1 && (userData.authorities[0].authority == 'YAJY_XLW' ||
            userData.authorities[0].authority == 'YAJY_XLWFB' ||userData.authorities[0].authority == 'YAJY_DBC' || userData.authorities[0].authority == 'YAJY_DBCZZ')">
            <div>
              <a-tabs v-model="activeTabKey">
                <!-- 当proposalType==4（仅供参考建议）不显示立案tab -->
                <a-tab-pane key="basic" v-if="record.proposalType != 4" tab="立案">
            <div>
              <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                :wrapper-col="{ span: 16 }">
                <a-row>

                  <a-col span="24">
                    <!-- <div style="display: flex; width: 1286px;  justify-content: center;">
                      <a-alert message="系统分办规则：1、单主办多协办 2、多主办无协办 " banner />
                      <br />
                    </div> -->
<!--                    <a-form-model-item>-->
<!--                      <a-alert message="系统分办规则：1、单主办多协办 2、多主办无协办 " banner style="width: 50%;margin-left:28%"/>-->
<!--                    </a-form-model-item>-->
                    <!--                    <a-form-model-item label="内容分类" prop="contentType">-->
                    <!--                      <a-select v-model="resourceForm.contentType" allow-clear style="width: 400px">-->
                    <!--                        <a-select-option v-for="item in contentTypeList" :key="item.id" :value="item.id">{{ item.name }}-->
                    <!--                        </a-select-option>-->
                    <!--                      </a-select>-->
                    <!--                    </a-form-model-item>-->
                    <a-form-model-item label="正式交办时间" prop="finalAssignTimeStr">
                      <a-date-picker v-model="resourceForm.finalAssignTimeStr" :style="{ width: '50%' }" allow-clear
                                     value-format="YYYY-MM-DD"  disabled></a-date-picker>
                      <span style="color:red" :style="{ width: '50%' }" >(由本级人大代表工作部门统一设定)</span>
                    </a-form-model-item>
                    <a-form-model-item  prop="enrollTime">
                      <template #label>
                        <span style="color: red;">*</span> 主办单位
                      </template>
                      <div class="searchStyle">
                        <a-input v-model="resourceForm.hostName" disabled enter-button>
                        </a-input>
                        <a-button type="primary" icon="search" @click="openUnitTable('host')">
                        </a-button>
                      </div>
                    </a-form-model-item>
                    <a-form-model-item label="主办单位办理期限" prop="mainHandleLastTimeStr">
                      <a-date-picker v-model="resourceForm.mainHandleLastTimeStr" :style="{ width: '50%' }" allow-clear
                        value-format="YYYY-MM-DD" placeholder="大会交办之日起三个月办理期限" disabled></a-date-picker>
                      <span style="color:red" :style="{ width: '50%' }" >(备注:按照有关规定，主办单位应在正式交办后，三个月内办理完毕)</span>
                    </a-form-model-item>
                    <a-form-model-item label="会办单位">
                      <div class="searchStyle">
                        <a-input v-model="resourceForm.meetName" disabled enter-button>
                        </a-input>
                        <a-button type="primary" icon="search" v-if="isBanMeet" disabled>
                        </a-button>
                        <a-button type="primary" icon="search" v-else @click="openUnitTable('meet')" >
                        </a-button>
                      </div>
                    </a-form-model-item>
                    <a-row v-if="isBanMeet" style="margin-left:25%; margin-bottom:10px">
                      <a-col span="24">
                        <span style="color: #bd3124; font-size: 12px;">
                          在选定两个以上主办单位以后，不能选择会办
                        </span>
                      </a-col>
                    </a-row>
                    <a-form-model-item label="会办单位办理期限" prop="minorHandleLastTimeStr">
                     <a-date-picker v-model="resourceForm.minorHandleLastTimeStr" allow-clear :style="{ width: '50%' }"
                                     value-format="YYYY-MM-DD" placeholder="大会交办之日起一个月办理期限" disabled></a-date-picker>
                      <span style="color:red" :style="{ width: '50%' }" >(备注:按照有关规定，会办单位应在正式交办后，一个月内办理完毕)</span>
                    </a-form-model-item>
                    <!-- 12-13-- -->
                    <!--                  <a-form-model-item-->
                    <!--                    label="是否公开"-->
                    <!--                    prop="ifPublice"-->
                    <!--                    :label-col="{ span: 6 }"-->
                    <!--                    :wrapper-col="{ span: 9 }"-->
                    <!--                  >-->
                    <!--                    <a-radio-group-->
                    <!--                      v-model="resourceForm.ifPublice"-->
                    <!--                      @change="changeifPublice(resourceForm.ifPublice)"-->
                    <!--                      :disabled="isPublicDisable"-->
                    <!--                    >-->
                    <!--                      <a-radio value="1"> 是 </a-radio>-->
                    <!--                      <a-radio value="0"> 否 </a-radio>-->
                    <!--                    </a-radio-group>-->
                    <!--                  </a-form-model-item>-->
                  </a-col>
                </a-row>
              </a-form-model>
            </div>
            <div style="text-align: center; margin: 10px 0 30px 0">
              <a-space class="operator">
                <!-- <a-button :loading="jhLoading" @click="submitTheSubject('return')">退回</a-button> -->
<!--                <a-button :loading="jhLoading" @click="shenheIn('doAssign')">退回</a-button>-->
                <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
                <a-button :loading="jbLoading" :hidden="userData.authorities[0].authority == 'YAJY_XLWBC' ||
                  userData.authorities[0].authority == 'YAJY_DBCBC'
                  " @click="save" style="margin-left:25px">保存</a-button>
                <!-- <a-button :loading="jbLoading" @click="manage">交办</a-button> -->
                <a-button :loading="jbLoading" style="margin-left:25px" @click="manage" type="primary">分办</a-button>
<!--                <a-button :loading="jhLoading" style="margin-left:25px" @click="refuseLian">不予立案</a-button>-->

                <!--              <a-button :loading="jbLoading||zwCKyj"
                        :hidden="
                userData.authorities[0].authority == 'YAJY_XLWBC' ||
                userData.authorities[0].authority == 'YAJY_DBCBC'
              "
                        @click="changeRefer">转为参考建议</a-button>-->
              </a-space>
            </div>
                </a-tab-pane>
                <a-tab-pane key="other" tab="不予立案">
            <div style="margin-left:10%">
              <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                            :wrapper-col="{ span: 16 }">
              <a-row  align="top">
                  <a-form-model-item  prop="enrollTime" style="width:907px">
                    <template #label>
                      <span style="color: red;">*</span> <span style="font-weight:bold">主办单位</span>
                    </template>
                    <div class="searchStyle">
                      <a-input v-model="resourceForm.hostName" disabled enter-button>
                      </a-input>
                      <a-button type="primary" icon="search" @click="openUnitTable('host')">
                      </a-button>
                    </div>
                  </a-form-model-item>
                <!-- 理由填写 -->
                <!-- 左侧的"理由："文本 -->
                <a-col :span="4" style="display: flex; align-items: flex-start;justify-content: flex-end;">
                  <a-text style="font-weight:bold">理由：</a-text>
                </a-col>

                <!-- 右侧的文本框 -->
                <a-col :span="20" style="padding-left: 8px;">
                  <a-textarea style="width: 80%" v-model="resourceForm.remarkText" :rows="5" placeholder="请输入理由" maxlength="500"></a-textarea>
                  <div  style="margin-top: 5px;">
                    限制长度为500个字符
                  </div>
                </a-col>
                <!-- 上传附件 -->
                  <a-col :span="4" style="display: flex; align-items: flex-start;justify-content: flex-end;margin-top: 20px;font-weight:bold">
<!--                    <span style="color: #f5222d;margin-top:3px">*</span>-->
                    附件上传：
                  </a-col>
                  <a-col span="20" style="margin-top:20px;">
                    <a-form-model-item :colon="false">
                      <!-- <div slot="label">
                        <span style="color: #f5222d">*</span>
                        附件上传：
                      </div> -->
                      <!-- 上传按钮 -->
                      <a-upload action="" accept=".docx,.doc,.pdf,accept,image/*,video/*,audio/*" :remove="handleRegisterRemove"
                                :before-upload="beforeUpload" :file-list="fileUnRegisterList" @change="uploadChange($event, '50')">
                        <a-button :disabled="fileUnRegisterList.length == 1" type="primary">点击上传</a-button>
                        <div :hidden="fileUnRegisterList.length == 1" style="margin-top: 5px;">
                          请上传文档、图片、音视频，且附件大小不能超过10mb
                        </div>
                      </a-upload>
                    </a-form-model-item>
                  </a-col>
<!--                <a-col :span="20" style="margin-left: 18%; margin-top: 20px;">-->
<!--                  &lt;!&ndash; 上传 &ndash;&gt;-->
<!--                  <a-upload :action="action" accept=".pdf, .ofd" :data="fileData" :remove="handleRemove"-->
<!--                    :before-upload="beforeAvatarUpload" :file-list="fileUrl" @preview="previewData"-->
<!--                    @change="uploadChange">-->
<!--                    <a-button :disabled="diState">点击上传</a-button>-->
<!--                  </a-upload>-->
<!--                </a-col>-->
                <a-col :span="20" style="margin-left: 40%; margin-top: 30px;margin-bottom:50px;">
                  <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
                  <a-button :loading="jhLoading" style="margin-left:25px" @click="putUnRegister" type="primary">提交</a-button>
                </a-col>
              </a-row>
              </a-form-model>
            </div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </div>
        </div>

          <!-- 交办闭会期间 -->
          <div v-if="isRendered">
            <div v-if="record.status == 40 && scenesStatus == '' && isMeeting  == 0 && userData.authorities[0].authority == 'YAJY_XLW'">
              <div>
                <a-tabs v-model="activeTabKey">
                  <!-- 当proposalType==4（仅供参考建议）不显示立案tab -->
                  <a-tab-pane key="basic" v-if="record.proposalType != 4" tab="立案">
                    <div>
                      <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                                    :wrapper-col="{ span: 16 }">
                        <a-row>
                          <a-col span="24">
                            <a-form-model-item  prop="enrollTime">
                              <template #label>
                                <span style="color: red;">*</span> 主办单位
                              </template>
                              <div class="searchStyle">
                                <a-input v-model="resourceForm.hostName" disabled enter-button>
                                </a-input>
                                <a-button type="primary" icon="search" @click="openUnitTable('host')">
                                </a-button>
                              </div>
                            </a-form-model-item>
                            <a-form-model-item label="主办单位办理期限" prop="mainHandleLastTimeStr">
                              <a-date-picker v-model="resourceForm.mainHandleLastTimeStr" :style="{ width: '50%' }" allow-clear
                                             value-format="YYYY-MM-DD" placeholder="闭会交办之日起三个月办理期限"></a-date-picker>
                            </a-form-model-item>
                            <a-form-model-item label="会办单位">
                              <div class="searchStyle">
                                <a-input v-model="resourceForm.meetName" disabled enter-button>
                                </a-input>
                                <a-button type="primary" icon="search" v-if="isBanMeet" disabled>
                                </a-button>
                                <a-button type="primary" icon="search" v-else @click="openUnitTable('meet')" >
                                </a-button>
                              </div>
                            </a-form-model-item>
                            <a-row v-if="isBanMeet" style="margin-left:25%; margin-bottom:10px">
                              <a-col span="24">
                        <span style="color: #bd3124; font-size: 12px;">
                          在选定两个以上主办单位以后，不能选择会办
                        </span>
                              </a-col>
                            </a-row>
                            <a-form-model-item label="会办单位办理期限" prop="minorHandleLastTimeStr">
                              <a-date-picker v-model="resourceForm.minorHandleLastTimeStr" allow-clear :style="{ width: '50%' }"
                                             value-format="YYYY-MM-DD" placeholder="闭会交办之日起一个月办理期限"></a-date-picker>
                            </a-form-model-item>
                          </a-col>
                        </a-row>
                      </a-form-model>
                    </div>
                    <div style="text-align: center; margin: 10px 0 30px 0">
                      <a-space class="operator">

                        <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
                        <a-button :loading="jbLoading" @click="save" style="margin-left:25px">保存</a-button>
                        <a-button :loading="jbLoading" style="margin-left:25px" @click="manageBh" type="primary">分办</a-button>
                      </a-space>
                    </div>
                  </a-tab-pane>
                  <a-tab-pane key="other" tab="不予立案">
                    <div style="margin-left:10%">
                      <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                                    :wrapper-col="{ span: 16 }">
                        <a-row  align="top">
                          <a-form-model-item  prop="enrollTime" style="width:907px">
                            <template #label>
                              <span style="color: red;">*</span> <span style="font-weight:bold">主办单位</span>
                            </template>
                            <div class="searchStyle">
                              <a-input v-model="resourceForm.hostName" disabled enter-button>
                              </a-input>
                              <a-button type="primary" icon="search" @click="openUnitTable('host')">
                              </a-button>
                            </div>
                          </a-form-model-item>
                          <!-- 理由填写 -->
                          <!-- 左侧的"理由："文本 -->
                          <a-col :span="4" style="display: flex; align-items: flex-start;justify-content: flex-end;">
                            <a-text style="font-weight:bold">理由：</a-text>
                          </a-col>

                          <!-- 右侧的文本框 -->
                          <a-col :span="20" style="padding-left: 8px;">
                            <a-textarea style="width: 80%" v-model="resourceForm.remarkText" :rows="5" placeholder="请输入理由" maxlength="500"></a-textarea>
                            <div  style="margin-top: 5px;">
                              限制长度为500个字符
                            </div>
                          </a-col>
                          <!-- 上传附件 -->
                          <a-col :span="4" style="display: flex; align-items: flex-start;justify-content: flex-end;margin-top: 20px;font-weight:bold">
                            <!--                    <span style="color: #f5222d;margin-top:3px">*</span>-->
                            附件上传：
                          </a-col>
                          <a-col span="20" style="margin-top:20px;">
                            <a-form-model-item :colon="false">
                              <!-- <div slot="label">
                                <span style="color: #f5222d">*</span>
                                附件上传：
                              </div> -->
                              <!-- 上传按钮 -->
                              <a-upload action="" accept=".docx,.doc,.pdf,accept,image/*,video/*,audio/*" :remove="handleRegisterRemove"
                                        :before-upload="beforeUpload" :file-list="fileUnRegisterList" @change="uploadChange($event, '50')">
                                <a-button :disabled="fileUnRegisterList.length == 1" type="primary">点击上传</a-button>
                                <div :hidden="fileUnRegisterList.length == 1" style="margin-top: 5px;">
                                  请上传文档、图片、音视频，且附件大小不能超过10mb
                                </div>
                              </a-upload>
                            </a-form-model-item>
                          </a-col>
                          <a-col :span="20" style="margin-left: 40%; margin-top: 30px;margin-bottom:50px;">
                            <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
                            <a-button :loading="jhLoading" style="margin-left:25px" @click="putUnRegisterBh" type="primary">提交</a-button>
                          </a-col>
                        </a-row>
                      </a-form-model>
                    </div>
                  </a-tab-pane>
                </a-tabs>
              </div>
            </div>
          </div>

          <!-- 校核 -->
          <div v-if="record.status == 20 &&
            scenesStatus == '' &&
            (userData.authorities[0].authority == 'YAJY_QRD' ||
              userData.authorities[0].authority == 'YAJY_XLW')
            ">
            <div>
              <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                :wrapper-col="{ span: 16 }">
                <a-row>
                  <a-col span="6" style="text-align: end; height: 105px; line-height: 105px;">
                    退回意见：
                  </a-col>
                  <a-col span="18">
                    <!-- 注释 isOtheryj   @change="changeTEXT"-->
                    <a-checkbox-group v-model="resourceForm.text">
                      <a-row>
                        <a-col v-for="(item, index) in OpinionOptionList" :key="item">
                          <a-checkbox :value="item">{{ item }}<br /></a-checkbox>
                        </a-col>
                      </a-row>
                      <!-- <a-checkbox :value="'其他'">其他<br></br></a-checkbox> -->
                    </a-checkbox-group>
                    <!-- -->
                    <a-textarea style="width: 80%" v-model="resourceForm.qt" v-show="isOtheryj" :rows="5"></a-textarea>
                  </a-col>
                </a-row>
              </a-form-model>
            </div>
            <div style="text-align: center; margin: 10px">
              <a-space class="operator" style="margin-right:25px">
                <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
                <a-button :loading="shLoading" @click="submitTheSubject('return')">退回</a-button>
                <a-button :loading="shLoading" @click="submitTheSubject('pass')" type="primary">校核通过</a-button>
              </a-space>
<!--              <a-space class="operator" style="margin-left:15px">-->
<!--                -->
<!--              </a-space>-->
            </div>
            <div class="tip" style="text-align: center; margin: 18px 315px">
              <a-alert message="" description="请点击底部“下载正文附件”按钮下载建议正文，按照《文件格式》(点击下方“下载文件格式”按钮下载)
                          的要求调整建议正文内容格式后点击底部“重新上传正文附件”按钮上传建议正文。" type="info" show-icon />
            </div>
            <div style="text-align: center; margin: 10px 0">
              <a-space class="operator">
                <a-button :loading="jhLoading" type="primary"
                  @click="downFileData('32', 'shiliwenjianxx')">下载文件格式</a-button>
              </a-space>
            </div>
          </div>

          <!-- 分类 -->
          <div v-if="tabIndex == 1 && record.status == 21 &&
            scenesStatus == '' &&
            (userData.authorities[0].authority == 'YAJY_XLW')
            ">
            <div>
              <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                :wrapper-col="{ span: 16 }">
                <a-row>
                  <a-form-model-item label="内容分类" prop="contentType" :label-col="{ span: 8 }" >
                    <a-select v-model="resourceForm.contentType" allow-clear style="width: 400px">
                      <a-select-option v-for="item in contentTypeList" :key="item.id" :value="item.id">{{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
<!--                  <a-col span="6" style="text-align: end; height: 105px; line-height: 105px;">-->
<!--                    退回意见：-->
<!--                  </a-col>-->
<!--                  <a-col span="18">-->
<!--                    &lt;!&ndash; 注释 isOtheryj   @change="changeTEXT"&ndash;&gt;-->
<!--                    <a-checkbox-group v-model="resourceForm.text">-->
<!--                      <a-row>-->
<!--                        <a-col v-for="(item, index) in OpinionOptionList" :key="item">-->
<!--                          <a-checkbox :value="item">{{ item }}<br /></a-checkbox>-->
<!--                        </a-col>-->
<!--                      </a-row>-->
<!--                      &lt;!&ndash; <a-checkbox :value="'其他'">其他<br></br></a-checkbox> &ndash;&gt;-->
<!--                    </a-checkbox-group>-->
<!--                    &lt;!&ndash; &ndash;&gt;-->
<!--                    <a-textarea style="width: 80%" v-model="resourceForm.qt" v-show="isOtheryj" :rows="5"></a-textarea>-->
<!--                  </a-col>-->
                </a-row>
              </a-form-model>
            </div>
            <div style="text-align: center; margin: 10px 0">
              <a-space class="operator" style="margin: 0 25px">
              <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
              </a-space>
              <a-space class="operator" style="margin: 0 25px">
              <a-button :loading="jhLoading" @click="shenheIn('classify')">退回</a-button>
              </a-space>
              <a-space class="operator" style="margin: 0 25px">
              <a-button :loading="jhLoading" @click="saveContentSubject('pass')" type="primary">提交初审</a-button>
              </a-space>
            </div>
          </div>
          <!-- 大会期间初审 -->
          <div v-if="tabIndex == 1 && record.status == 22 && scenesStatus == '' &&isMeeting ==1
            && (userData.authorities[0].authority == 'YAJY_XLWCH' ||
            userData.authorities[0].authority == 'YAJY_XLW')">
<!--            <div>-->
<!--              <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"-->
<!--                :wrapper-col="{ span: 16 }">-->
<!--                <a-row>-->
<!--                  <a-col span="6" style="text-align: end; height: 105px; line-height: 105px;">-->
<!--                    退回意见：-->
<!--                  </a-col>-->
<!--                  <a-col span="18">-->
<!--                    <a-checkbox-group v-model="resourceForm.text">-->
<!--                      <a-row>-->
<!--                        <a-col v-for="(item, index) in OpinionOptionList" :key="item">-->
<!--                          <a-checkbox :value="item">{{ item }}<br /></a-checkbox>-->
<!--                        </a-col>-->
<!--                      </a-row>-->
<!--                      &lt;!&ndash; <a-checkbox :value="'其他'">其他<br></br></a-checkbox> &ndash;&gt;-->
<!--                    </a-checkbox-group>-->
<!--                    &lt;!&ndash; &ndash;&gt;-->
<!--                    <a-textarea style="width: 80%" v-model="resourceForm.qt" v-show="isOtheryj" :rows="5"></a-textarea>-->
<!--                  </a-col>-->
<!--                </a-row>-->
<!--              </a-form-model>-->
<!--            </div>-->
            <div style="text-align: center; margin: 10px 0">
              <a-space class="operator">
                <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
                <a-button :loading="shLoading" @click="shenheIn('firstCheck')">退回</a-button>
                <a-button :loading="shLoading" @click="submitFirstCheck('pass')" type="primary">初审通过</a-button>
              </a-space>
            </div>
          </div>

          <!-- 闭会期间审核 -->
          <div v-if="tabIndex == 1 && record.status == 22 && scenesStatus == '' &&isMeeting ==0
            && (userData.authorities[0].authority == 'YAJY_XLWCH' ||
            userData.authorities[0].authority == 'YAJY_XLW')">
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                          :wrapper-col="{ span: 16 }">
            <a-row>
              <a-form-model-item label="内容分类" prop="contentType" :label-col="{ span: 9 }" >
                <a-select v-model="resourceForm.contentType" allow-clear style="width: 400px">
                  <a-select-option v-for="item in contentTypeList" :key="item.id" :value="item.id">{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-row>
            <a-row>
              <a-form-model-item
                label="是否公开"
                prop="public"
                :label-col="{ span: 9 }"
              >
                <a-radio-group
                  v-model="resourceForm.xlwIfPublice"
                  @change="changePublic(resourceForm.xlwIfPublice)"
                >
                  <a-radio value="1"> 是 </a-radio>
                  <a-radio value="2"> 否 </a-radio>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item label="出具意见" prop="comment" class="form-item" :label-col="{ span: 9 }">
                <a-textarea v-model="resourceForm.publicComment" rows="6" style="width: 400px" ></a-textarea>
              </a-form-model-item>
            </a-row>
            <div style="text-align: center; margin: 10px 0">
              <a-space class="operator">
                <a-button :loading="shLoading" @click="shenheIn('firstCheck')">退回</a-button>
                <a-button :loading="shLoading" @click="submitFirstCheckBh('pass')" type="primary">初审通过</a-button>
              </a-space>
            </div>
            </a-form-model>
          </div>


          <!-- 退回建议审核 -->
          <div v-if=" (record.status == 25||record.status == 35) && scenesStatus == ''
            && userData.authorities[0].authority == 'YAJY_XLW'">
            <div>
<!--                <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"-->
<!--                              :wrapper-col="{ span: 16 }">-->
<!--                  <a-row>-->
<!--                    <a-col span="22">-->
<!--                      <a-form-model-item label="出具意见" prop="comment" class="form-item">-->
<!--                        <a-textarea v-model="resourceForm.comment" rows="6" ></a-textarea>-->
<!--                      </a-form-model-item>-->
<!--                    </a-col>-->
<!--                  </a-row>-->
<!--                </a-form-model>-->
            </div>
            <div style="text-align: center; margin: 10px 0">
              <a-space class="operator">
                <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
                <a-button :loading="shLoading" @click="shenheIn('auditReturn')">审核通过</a-button>
                <a-button :loading="shLoading" @click="auditReturnF()">驳回</a-button>
              </a-space>
            </div>
          </div>

          <!-- 填写备注 -->
          <!--          <a-modal-->
          <!--            :body-style="{ height: '200px' }"-->
          <!--            :visible="returnValue"-->
          <!--            :title="returnValueTitle"-->
          <!--            @ok="submitTheSubject"-->
          <!--            @cancel="cancelReturn"-->
          <!--          >-->
          <!--            <a-form-model-->
          <!--              ref="resourceForm"-->
          <!--              :model="resourceForm"-->
          <!--              :rules="rules"-->
          <!--              :label-col="{ span: 6 }"-->
          <!--              :wrapper-col="{ span: 16 }"-->
          <!--            >-->
          <!--              <a-row>-->
          <!--                <a-col span="24">-->
          <!--                  <a-form-model-item label="回复意见" prop="comment">-->
          <!--                    <a-textarea-->
          <!--                      v-model="resourceForm.comment"-->
          <!--                      :rows="5"-->
          <!--                    ></a-textarea>-->
          <!--                  </a-form-model-item>-->
          <!--                </a-col>-->
          <!--              </a-row>-->
          <!--            </a-form-model>-->
          <!--          </a-modal>-->
        </div>
        <!-- 不予立案审核 -->
        <div v-if="showOperate">
          <div v-if="tabIndex == 1 && record.status == 41 && scenesStatus == ''
            && (userData.authorities.some(authority => authority.authority === 'YAJY_XLWCH') ||
              userData.authorities[0].authority == 'YAJY_XLW')">
  <!--          <div>-->
  <!--            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"-->
  <!--              :wrapper-col="{ span: 16 }">-->
  <!--              <a-row>-->
  <!--                <a-col span="6" style="text-align: end; height: 105px; line-height: 105px;">-->
  <!--                  退回意见：-->
  <!--                </a-col>-->
  <!--              </a-row>-->
  <!--            </a-form-model>-->
  <!--          </div>-->
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                          :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col :span="12">
                  <a-form-model-item
                    label="对口专委意见"
                    prop="zwSug"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 16 }"
                  >
                    <a-radio-group v-model="resourceForm.zwSug" @change="changeZwSug(resourceForm.zwSug)">
                      <a-radio value="1"> 同意不予立案 </a-radio>
                      <a-radio value="0"> 不同意 </a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                  <a-form-model-item v-if="resourceForm.zwSug === '1'" label="出具意见" prop="zwSug" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
                  <a-select v-if="resourceForm.zwSug === '1'" v-model="resourceForm.zwReason" style="width: 200px; margin-top: 16px;">
                      <a-select-option value="退回代表">退回代表</a-select-option>
                      <a-select-option value="转为供参考建议">转为供参考建议</a-select-option>
                  </a-select>
                  </a-form-model-item>
                  <a-form-model-item label="出具意见" prop="comment" class="form-item">
                    <a-textarea v-model="resourceForm.zwComment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item
                    label="代表意见"
                    prop="dbSug"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 16 }"
                  >
                    <a-radio-group v-model="resourceForm.dbSug" @change="changeDbSug(resourceForm.dbSug)">
                      <a-radio value="1"> 同意不予立案 </a-radio>
                      <a-radio value="0"> 不同意 </a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                  <a-form-model-item
                    label="出具意见"
                    prop="zwSug"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 16 }"
                    v-if="resourceForm.dbSug === '1'"
                  >
                    <a-select v-if="resourceForm.dbSug === '1'" v-model="resourceForm.dbReason" style="width: 200px; margin-top: 16px;">
                      <a-select-option value="退回本人">退回本人</a-select-option>
                      <a-select-option value="转为供参考建议">转为供参考建议</a-select-option>
                    </a-select>
                  </a-form-model-item>
                  <a-form-model-item label="出具意见" prop="comment" class="form-item">
                    <a-textarea v-model="resourceForm.dbComment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
              </a-row>
            <div style="text-align: center; margin: 10px 0">
              <a-space class="operator">
                <a-button :loading="shLoading" @click="submitSug('pass')" type="primary">提交复核</a-button>
              </a-space>
            </div>
            </a-form-model>
          </div>


        <!-- 专题分办小组长确认 -->
        <div v-if="tabIndex == 1 && record.status == 42 && scenesStatus == ''
          && (userData.authorities[0].authority == 'YAJY_XLW' || userData.authorities[0].authority == 'YAJY_XLWFB'
          || userData.authorities[0].authority == 'YAJY_DBC' || userData.authorities[0].authority == 'YAJY_DBCZZ')">
          <div>
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                          :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col span="22">
                  <a-form-model-item label="出具意见" prop="comment" class="form-item">
                    <a-textarea v-model="resourceForm.comment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
              <a-button :loading="shLoading" @click="shenheIn('assginConfirm')">退回</a-button>
              <a-button :loading="shLoading" type="primary" @click="submitAssignConfirm('pass')">签字确认</a-button>
            </a-space>
          </div>
        </div>

        <!-- 分办小组长二次确认分办 -->
        <div v-if="tabIndex == 1 && record.status == 43 && scenesStatus == ''
          && (userData.authorities[0].authority == 'YAJY_DBC')">
          <div>
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                          :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col span="22">
                  <a-form-model-item label="出具意见" prop="comment" class="form-item">
                    <a-textarea v-model="resourceForm.comment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row v-if ="record.ifPublice === '1'">
                <a-col span="22">
                  <a-form-model-item label="交办小组公开意见" prop="jbxzIfPublic" class="form-item">
                    <a-radio-group v-model="resourceForm.jbxzIfPublic" @change="onJbxzIfPublicChange">
                      <a-radio :value="'1'">公开</a-radio>
                      <a-radio :value="'2'">不公开</a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row v-if="resourceForm.jbxzIfPublic === '2' && record.ifPublice === '1'">
                <a-col span="22">
                  <a-form-model-item prop="jbxzPublic" class="form-item">
                    <template slot="label">
                      <span style="color: red;">*</span>不公开理由
                    </template>
                    <a-select v-model="resourceForm.jbxzPublic" placeholder="请选择不公开理由" allow-clear>
                      <a-select-option v-for="item in noPublicType" :key="item.id" :value="item.id">
                        {{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
              <a-button :loading="shLoading" @click="shenheIn('assginDbcConfirm')">退回</a-button>
              <a-button :loading="shLoading" type="primary" @click="submitAssignDbcConfirm('pass')">签字确认</a-button>
            </a-space>
          </div>
        </div>

        <!-- 议案建议组负责人确认签字 -->
        <div v-if="tabIndex == 1 && record.status >= 60 && scenesStatus == '建议组负责人确认'
          && (userData.authorities[0].authority == 'YAJY_XLW')">
          <div>
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                          :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col span="22">
                  <a-form-model-item label="出具意见" prop="comment" class="form-item">
                    <a-textarea v-model="resourceForm.comment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button :loading="shLoading"  type="primary" @click="submitXlwConfirm('pass')">签字确认</a-button>
            </a-space>
          </div>
        </div>

        <!-- 建议交办小组组长确认签字 -->
        <div v-if="tabIndex == 1 && record.status >= 60 && scenesStatus == '交办小组确认'
          && (userData.authorities[0].authority == 'YAJY_DBC' || userData.authorities[0].authority == 'YAJY_DBCZZ')">
          <div>
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                          :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col span="22">
                  <a-form-model-item label="出具意见" prop="comment" class="form-item">
                    <a-textarea v-model="resourceForm.comment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button :loading="shLoading" type="primary" @click="submitDbcConfirm('pass')">签字确认</a-button>
            </a-space>
          </div>
        </div>

        <!-- 市委编办工作人员确认签字 -->
        <div v-if="tabIndex == 1 && record.status >= 60 && scenesStatus == '编办确认'
          && (userData.authorities[0].authority == 'YAJY_SWBB')">
          <div>
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                          :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col span="22">
                  <a-form-model-item label="出具意见" prop="comment" class="form-item">
                    <a-textarea v-model="resourceForm.comment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <!--              <a-button :loading="shLoading" @click="shenheIn('unRegister')">退回</a-button>-->
              <a-button :loading="shLoading" type="primary" @click="submitBbConfirm('pass')">签字确认</a-button>
            </a-space>
          </div>
        </div>

        <!-- 代表工委确认不予立案 -->
        <div v-if="tabIndex == 1 && record.status == 45 && scenesStatus == '' &&isMeeting ==1
          && (userData.authorities[0].authority == 'YAJY_XLW')">
          <div>
<!--            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"-->
<!--                          :wrapper-col="{ span: 16 }">-->
<!--              <a-row>-->
<!--                <a-col span="22">-->
<!--                  <a-form-model-item label="出具意见" prop="comment" class="form-item">-->
<!--                    <a-textarea v-model="resourceForm.comment" rows="6" ></a-textarea>-->
<!--                  </a-form-model-item>-->
<!--                </a-col>-->
<!--              </a-row>-->
<!--            </a-form-model>-->
          </div>
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button :loading="shLoading" @click="shenheIn('unRegister')">不予立案并退回</a-button>
              <a-button :loading="shLoading" @click="submitToUnRegister('pass')" type="primary">不予立案转供参考</a-button>
            </a-space>
          </div>
        </div>

          <!-- 代表工委确认不予立案闭会 -->
          <div v-if="tabIndex == 1 && record.status == 45 && scenesStatus == '' &&isMeeting ==0
          && (userData.authorities[0].authority == 'YAJY_XLW')">
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                          :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col :span="12">
                  <a-form-model-item
                    label="代表工委意见"
                    prop="zwSug"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 16 }"
                  >
                    <a-radio-group v-model="resourceForm.zwSug" @change="changeZwSug(resourceForm.zwSug)">
                      <a-radio value="1"> 同意不予立案 </a-radio>
                      <a-radio value="0"> 不同意 </a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                  <a-form-model-item v-if="resourceForm.zwSug === '1'" label="出具意见" prop="zwSug" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
                    <a-select v-if="resourceForm.zwSug === '1'" v-model="resourceForm.zwReason" style="width: 200px; margin-top: 16px;">
                      <a-select-option value="退回代表">退回代表</a-select-option>
                      <a-select-option value="转为供参考建议">转为供参考建议</a-select-option>
                    </a-select>
                  </a-form-model-item>
                  <a-form-model-item label="出具意见" prop="comment" class="form-item">
                    <a-textarea v-model="resourceForm.zwComment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item
                    label="代表意见"
                    prop="dbSug"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 16 }"
                  >
                    <a-radio-group v-model="resourceForm.dbSug" @change="changeDbSug(resourceForm.dbSug)">
                      <a-radio value="1"> 同意不予立案 </a-radio>
                      <a-radio value="0"> 不同意 </a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                  <a-form-model-item
                    label="出具意见"
                    prop="zwSug"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 16 }"
                    v-if="resourceForm.dbSug === '1'"
                  >
                    <a-select v-if="resourceForm.dbSug === '1'" v-model="resourceForm.dbReason" style="width: 200px; margin-top: 16px;">
                      <a-select-option value="退回本人">退回本人</a-select-option>
                      <a-select-option value="转为供参考建议">转为供参考建议</a-select-option>
                    </a-select>
                  </a-form-model-item>
                  <a-form-model-item label="出具意见" prop="comment" class="form-item">
                    <a-textarea v-model="resourceForm.dbComment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <div style="text-align: center; margin: 10px 0">
                <a-space class="operator">
                  <a-button :loading="shLoading" @click="shenheIn('unRegister')">不予立案并退回</a-button>
                  <a-button :loading="shLoading" @click="submitToUnRegisterBh('pass')" type="primary">不予立案转供参考</a-button>
                </a-space>
              </div>
            </a-form-model>
          </div>

        <!-- 复审 大会期间 -->
        <div v-if="tabIndex == 1 && record.status == 30 && scenesStatus == '' &&isMeeting ==1
          && (userData.authorities[0].authority == 'YAJY_XLW')">
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button :loading="shLoading" @click="changeOnTab('5')">查阅流转意见</a-button>
              <a-button :loading="shLoading" @click="shenheIn('doAudit')">退回</a-button>
              <a-button :loading="shLoading"  type="primary" @click="submitReview('pass')">复审通过</a-button>
            </a-space>
          </div>
        </div>

          <!-- 复审 闭会期间 -->
          <div v-if="tabIndex == 1 && record.status == 30 && scenesStatus == '' &&isMeeting ==0
          && (userData.authorities[0].authority == 'YAJY_XLW')">
            <div style="text-align: center; margin: 10px 0">
              <a-space class="operator">
                <a-button :loading="shLoading" @click="shenheIn('doAudit')">退回</a-button>
                <a-button :loading="shLoading"  type="primary" @click="submitReviewBh('pass')">复审通过</a-button>
              </a-space>
            </div>
          </div>
          <!-- 研究室出具意见 闭会期间 -->
          <div v-if="tabIndex == 1 && record.status == 33 && scenesStatus == ''
            && userData.authorities[0].authority == 'YAJY_XCC'">
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                          :wrapper-col="{ span: 16 }">
              <a-row>
                <a-form-model-item
                  label="是否公开"
                  prop="public"
                  :label-col="{ span: 9 }"
                >
                  <a-radio-group
                    v-model="resourceForm.yjsIfPublice"
                    @change="changePublic(resourceForm.yjsIfPublice)"
                  >
                    <a-radio value="1"> 是 </a-radio>
                    <a-radio value="2"> 否 </a-radio>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item label="出具意见" prop="comment" class="form-item" :label-col="{ span: 9 }">
                  <a-textarea v-model="resourceForm.publicComment" rows="6" style="width: 400px" ></a-textarea>
                </a-form-model-item>
              </a-row>
              <div style="text-align: center; margin: 10px 0">
                <a-space class="operator">
                  <a-button :loading="shLoading" @click="submitYjsPublic()" type="primary">提交意见</a-button>
                </a-space>
              </div>
            </a-form-model>
          </div>

          <!-- 闭会期间领导审定 由代表工委进行处理-->
          <div v-if="tabIndex == 1 && record.status ==39 && scenesStatus == '领导审定'
            && (userData.authorities[0].authority == 'YAJY_XLW')">
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
                          :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col :span="8">
                  <a-form-model-item
                    label="代表工作部门处室审核"
                    prop="xlwCheck"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 16 }"
                  >
                    <a-radio-group v-model="resourceForm.xlwCheck" @change="xlwCheck(resourceForm.xlwCheck)">
                      <a-radio value="1"> 审核通过 </a-radio>
                      <a-radio value="0"> 审核不通过 </a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                  <a-form-model-item label="出具意见" prop="xlwCheckComment" class="form-item">
                    <a-textarea v-model="resourceForm.xlwCheckComment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    label="常委会分管副主任审核"
                    prop="subLeaderCheck"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 16 }"
                  >
                    <a-radio-group v-model="resourceForm.subLeaderCheck" @change="subLeaderCheck(resourceForm.subLeaderCheck)">
                      <a-radio value="1"> 审核通过 </a-radio>
                      <a-radio value="0"> 审核不通过 </a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                  <a-form-model-item label="出具意见" prop="subLeaderComment" class="form-item">
                    <a-textarea v-model="resourceForm.subLeaderComment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    label="常委会主任审定"
                    prop="leaderCheck"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 16 }"
                  >
                    <a-radio-group v-model="resourceForm.leaderCheck" @change="leaderCheck(resourceForm.leaderCheck)">
                      <a-radio value="1"> 审核通过 </a-radio>
                      <a-radio value="0"> 审核不通过 </a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                  <a-form-model-item label="出具意见" prop="leaderComment" class="form-item">
                    <a-textarea v-model="resourceForm.leaderComment" rows="6" ></a-textarea>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <div style="text-align: center; margin: 10px 0">
                <a-space class="operator">
                  <a-button :loading="shLoading" @click="leaderCheckBh('pass')" type="primary">批量提交</a-button>
                </a-space>
              </div>
            </a-form-model>
          </div>

          <!-- 签收界面处理按钮 申请单位调整 会办单位调整申请 按钮 -->
          <div v-if="tabIndex == 1 && (((record.status == 50|| record.status == 60 )  &&
          userData.authorities[0].authority == 'YAJY_CBDW' && !(scenesStatus == '建议组负责人确认' ||
          scenesStatus == '交办小组确认'||scenesStatus == '编办确认') &&
          !(scenesStatus == '处理调整' && record.ifObjection) &&
          !(scenesStatus == '审核调整' && record.ifMinor) &&
          !(scenesStatus == '处理延期' && record.ifDelay)) ||
          (record.undertakeStatus == 1 &&
            userData.authorities[0].authority == 'YAJY_CBDW' && scenesStatus == '签收' ) ||
          (scenesStatus == '答复修改' &&
            userData.authorities[0].authority == 'YAJY_CBDW' ) ) && !(this.mediaType == '其他建议')
          ">
            <div style="text-align: center; margin: 10px 0">
              <a-space class="operator">
              <a-button v-if="scenesStatus != '答复修改' &&
                record.undertakeStatus == 1 &&
                userData.authorities[0].authority == 'YAJY_CBDW'
                && (this.detailsForm &&this.detailsForm.status == 0)"
                :loading="sfLoading" @click="openAdjustModal('unit')">申请调整</a-button>
              </a-space>
            </div>
            <!-- <div style="text-align: center; margin: 10px 0">
              <a-space class="operator">
                <a-button v-if="scenesStatus != '答复修改' &&
                record.undertakeStatus == 1 &&
                userData.authorities[0].authority == 'YAJY_CBDW' && record.ifMain == 1
                " :loading="sfLoading" @click="adjust(1)">申请调整</a-button>
                <a-button v-if="scenesStatus != '答复修改' &&
                record.undertakeStatus == 1 &&
                userData.authorities[0].authority == 'YAJY_CBDW'  && record.ifMain == 0
                " :loading="sfLoading" @click="adjust(0)">会办申请调整</a-button>
                <a-button
                  v-if="
                    scenesStatus != '答复修改' &&
                    record.undertakeStatus == 1 &&
                    userData.authorities[0].authority == 'YAJY_CBDW' &&
                    record.ifMain == 1
                  "
                  :loading="sfLoading"
                  @click="adjustMinor"
                >调整会办单位</a-button
                >
              </a-space>
            </div> -->
          </div>

            <!-- 答复 根据承办子流程状态为2 -->
        <div v-if="tabIndex == 1 && ((record.status == 60 &&
          userData.authorities[0].authority == 'YAJY_XLW' && !(scenesStatus == '建议组负责人确认' ||
          scenesStatus == '交办小组确认'||scenesStatus == '编办确认' ||scenesStatus == '领导审定') &&
          !(scenesStatus == '处理调整' && record.ifObjection) && !(scenesStatus == '审核调整复核' && record.ifObjection) &&
          !(scenesStatus == '审核调整' && record.ifMinor) &&
          !(scenesStatus == '处理延期' && record.ifDelay)) ||
          (record.undertakeStatus == 2 &&
            userData.authorities[0].authority == 'YAJY_CBDW' && scenesStatus == '答复' && record.status <= 60 && record.ifMain == 0) ||
            (record.undertakeStatus == 2 &&
            userData.authorities[0].authority == 'YAJY_CBDW' && scenesStatus == '答复'  && record.ifMain == 1)||
          (scenesStatus == '答复修改' &&
            userData.authorities[0].authority == 'YAJY_CBDW' )) && !(this.mediaType == '其他建议')

          ">
          <div>
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col span="24">
                  <a-form-model-item label="经办人姓名" prop="jbrName">
                    <a-input v-model="resourceForm.jbrName" style="width: 85%"></a-input>
                    <a-button type="" style="margin-left: 10px" @click="showJbrTable(resourceForm)">选择经办人</a-button>
                  </a-form-model-item>
                </a-col>
                <a-col span="24">
                  <a-form-model-item label="经办人部门" prop="jbrDept">
                    <a-input v-model="resourceForm.jbrDept"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col span="24">
                  <a-form-model-item label="经办人职务" prop="jbrJob">
                    <a-input v-model="resourceForm.jbrJob"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col span="24">
                  <a-form-model-item label="经办人手机" prop="jbrPhone">
                    <a-input v-model="resourceForm.jbrPhone"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col span="24">
                  <a-form-model-item label="经办人办公电话" prop="jbrOph">
                    <a-input v-model="resourceForm.jbrOph"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col v-if="record.ifMain == 1 ||
                  userData.authorities[0].authority == 'YAJY_XLW'
                  " span="24">
                  <a-form-model-item label="答复类型" prop="dflb">
                    <a-select v-model="resourceForm.dflb">
                      <a-select-option value="A">A（已解决或基本解决的问题）</a-select-option>
                      <a-select-option value="B">B（列入年度计划解决的问题）</a-select-option>
                      <a-select-option value="C">C（列入规划逐步解决的问题）</a-select-option>
                      <a-select-option value="D">D（受法规、政策、财力等客观条件限制，难以实现）</a-select-option>
                      <!-- <!-- <a-select-option value="D"
                      >D（因条件限制或其他原因无法解决及作为参考的）</a-select-option
                    >
                    <a-select-option value="E">E（协办答复）</a-select-option> -->
                      -->
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col v-if="record.ifMain == 1 ||
                  userData.authorities[0].authority == 'YAJY_XLW'
                  " span="24">
                  <a-form-model-item label="沟通方式" prop="dffs">
                    <a-checkbox-group v-model="resourceForm.dffs">
                      <a-checkbox value="4">电话 短信 邮件 微信</a-checkbox>
                      <a-checkbox value="2">座谈会 调研</a-checkbox>
                      <a-checkbox value="3">代表表示无需见面沟通（需上传相关证据）</a-checkbox>
                    </a-checkbox-group>
                  </a-form-model-item>
                </a-col>
                <a-col v-if="(record.ifMain == 1 ||
                  userData.authorities[0].authority == 'YAJY_XLW') &&
                  resourceForm.dffs.includes('2')
                  " span="24">
                  <a-form-model-item label="上传座谈会调研证据">
                    <!-- 上传座谈会调研证据 入参字段不明 -->
                    <a-upload action="" accept=".docx,.doc,.pdf,accept,image/*,video/*,audio/*"
                      :remove="handleTextRemove8" :before-upload="() => {
                        return false;
                      }
                        " :file-list="fileTextList_8" @change="uploadChange($event, '8')">
                      <a-button :disabled="fileTextList_8.length == 1" type="primary">点击上传</a-button>
                      <div :hidden="fileTextList_8.length == 1">
                        请上传文档、图片、音视频，且附件大小不能超过10mb
                      </div>
                    </a-upload>
                  </a-form-model-item>
                </a-col>
                <a-col v-if="(record.ifMain == 1 ||
                  userData.authorities[0].authority == 'YAJY_XLW') &&
                  resourceForm.dffs.includes('3')
                  " span="24">
                  <a-form-model-item :colon="false">
                    <div slot="label">
                      <span style="color: #f5222d">*</span>
                      上传代表无需沟通证据：
                    </div>
                    <!-- 上传代表无需沟通证据 入参字段不明 -->
                    <a-upload action="" accept=".docx,.doc,.pdf,accept,image/*,video/*,audio/*"
                      :remove="handleTextRemove7" :before-upload="() => {
                        return false;
                      }
                        " :file-list="fileTextList_7" @change="uploadChange($event, '7')">
                      <a-button :disabled="fileTextList_7.length == 1" type="primary">点击上传</a-button>
                      <div :hidden="fileTextList_7.length == 1">
                        请上传文档、图片、音视频，且附件大小不能超过10mb
                      </div>
                    </a-upload>
                  </a-form-model-item>
                </a-col>
                <a-col v-if="record.ifMain == 1 ||
                  userData.authorities[0].authority == 'YAJY_XLW'
                  " span="24">
                  <a-form-model-item label="答复是否公开" prop="isPublic">
                    <a-radio-group v-model="resourceForm.isPublic">
                      <a-radio value="1">是</a-radio>
                      <a-radio value="0">否</a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                </a-col>
                <a-col span="24" v-if="record.ifMain == 1 ||
                  userData.authorities[0].authority == 'YAJY_XLW'
                  ">
                  <!--                  <a-form-model-item-->
                  <!--                    label="建议内容质量评分"-->
                  <!--                    prop="orgEvaluation"-->
                  <!--                  >-->
                  <!--                    <a-input-number-->
                  <!--                      v-model="resourceForm.orgEvaluation"-->
                  <!--                      placeholder="请输入分值"-->
                  <!--                      size="mini"-->
                  <!--                      :precision="1"-->
                  <!--                      ref="optionInputALL"-->
                  <!--                      :min="0"-->
                  <!--                      :max="10"-->
                  <!--                      :value="resourceForm.orgEvaluation"-->
                  <!--                      @change="changeorgEvaluation"-->
                  <!--                      style="width: 20%; margin-left: 15px"-->
                  <!--                    ></a-input-number>-->

                  <!--                    分 (评分范围:0-10分,可保留一位小数)-->
                  <!--                  </a-form-model-item>-->
                </a-col>
                <a-col v-if="(record.ifMain == 1 ||
                  userData.authorities[0].authority == 'YAJY_XLW') &&
                  resourceForm.isPublic === '0'
                  " span="24">
                  <a-form-model-item label="答复不公开理由" prop="remark">
                    <a-select v-model="resourceForm.remark" @change="showData()">
                      <a-select-option value="G">
                        （一）涉及国家秘密、工作秘密、商业秘密、个人隐私等信息的</a-select-option>
                      <a-select-option value="H">
                        （二）涉及公开后可能危及国家安全、公共安全、经济安全，以及可能引起重大负面舆情、造成混乱、影响社会稳定的</a-select-option>
                      <a-select-option value="I">
                        （三）涉及重大政治、外交、历史遗留问题等方面内容公开后可能导致不良后果的</a-select-option>
                      <a-select-option value="J">
                        （四）涉及国家事权和港澳事务不宜由地方回应的,</a-select-option>
                      <a-select-option value="K">
                        （五）涉及相关政策正在研究制定中不宜提前对外公布的</a-select-option>
                      <a-select-option value="L">
                        （六）涉及内容比较敏感的</a-select-option>
                      <a-select-option value="M">
                        （七）涉及行政机关内部事务以及履行行政管理职能形成的过程性信息的</a-select-option>
                      <a-select-option value="N">
                        （八）提出议案建议的代表要求不予公开或者公开后有可能使代表或者他人受到报复伤害的</a-select-option>
                      <a-select-option value="F">
                        （九）法律规定和工作要求的其他不予公开情形</a-select-option>
                    </a-select>
                    <!--<a-textarea v-model="resourceForm.remark"></a-textarea>-->
                  </a-form-model-item>
                </a-col>
                <a-col v-if="(record.ifMain == 1 ||
                  userData.authorities[0].authority == 'YAJY_XLW') &&
                  resourceForm.isPublic === '0' && resourceForm.remark == 'F'
                  " span="24">
                  <a-form-model-item label="其他内容" prop="remarkText">
                    <a-textarea v-model="resourceForm.remarkText"></a-textarea>
                  </a-form-model-item>
                </a-col>
                <a-col span="24">
                  <a-form-model-item :colon="false">
                    <div slot="label">
                      <span style="color: #f5222d">*</span>
                      答复上传：
                    </div>
                    <!-- 上传 -->
                    <a-upload action="" accept=".docx,.doc,.pdf,accept,image/*,video/*,audio/*" :remove="handleTextRemove"
                      :before-upload="beforeUpload" :file-list="fileTextList" @change="uploadChange($event, '2')">
                      <a-button :disabled="fileTextList.length == 1" type="primary">点击上传</a-button>
                      <div :hidden="fileTextList.length == 1">
                        请上传文档、图片、音视频，且附件大小不能超过10mb
                      </div>
                    </a-upload>
                  </a-form-model-item>
                </a-col>
                <a-col v-if="record.ifMain == 1 ||
                  userData.authorities[0].authority == 'YAJY_XLW'
                  " span="24">
                  <a-form-model-item v-if="hbDataSource.length > 0" label="政务协同">
                    <div v-for="item in hbDataSource" :key="item.minorUndertakeId">
                      <div>
                        <span>{{ item.minorOrgName }}</span>
                        <a-input-number v-model="item.score" :min="1" :max="100"
                          style="margin-left: 10px"></a-input-number>
                        <a-slider v-model="item.score" :min="1" :max="100" />
                      </div>
                    </div>
                  </a-form-model-item>
                  <a-col v-else span="12" push="4">
                    <p :style="{ marginBottom: '10px' }">暂无会办单位数据</p>
                  </a-col>
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button v-if="scenesStatus != '答复修改' &&
                ((record.undertakeStatus == 2 &&
                  userData.authorities[0].authority == 'YAJY_CBDW') ||
                  userData.authorities[0].authority == 'YAJY_XLW')
                " :loading="sfLoading" @click="
    replyData(
      'save',
      userData.authorities[0].authority == 'YAJY_XLW'
    )
    ">保存</a-button>
              <a-button v-if="scenesStatus != '答复修改' &&
                ((record.undertakeStatus == 2 &&
                  userData.authorities[0].authority == 'YAJY_CBDW') ||
                  userData.authorities[0].authority == 'YAJY_XLW')
                " :loading="sfLoading" @click="
    replyData(
      'submit',
      userData.authorities[0].authority == 'YAJY_XLW'
    )
    ">答复</a-button>
              <a-button v-if="scenesStatus == '答复修改'" :loading="sfLoading" @click="
                replyData(
                  'submit',
                  userData.authorities[0].authority == 'YAJY_XLW'
                )
                ">修改答复</a-button>

                <a-button v-if="scenesStatus != '答复修改' &&
                record.undertakeStatus == 2 &&
                userData.authorities[0].authority == 'YAJY_CBDW' && (this.detailsForm &&this.detailsForm.status == 0)"
                :loading="sfLoading" @click="openAdjustModal('unit')">申请调整</a-button>


              <!-- <a-button v-if="scenesStatus != '答复修改' &&
                record.undertakeStatus == 2 &&
                userData.authorities[0].authority == 'YAJY_CBDW' && record.ifMain == 1
                " :loading="sfLoading" @click="adjust('1')">申请调整</a-button> -->

              <!-- <a-button v-if="scenesStatus != '答复修改' &&
                record.undertakeStatus == 2 &&
                userData.authorities[0].authority == 'YAJY_CBDW'  && record.ifMain == 0
                " :loading="sfLoading" @click="adjust('0')">会办申请调整</a-button> -->
              <!-- <a-button v-if="scenesStatus != '答复修改' &&
                record.undertakeStatus == 2 &&
                userData.authorities[0].authority == 'YAJY_CBDW'
                " :loading="sfLoading" @click="postpone">申请延期</a-button> -->
                <!-- <a-button
                  v-if="
                    scenesStatus != '答复修改' &&
                    record.undertakeStatus == 2 &&
                    userData.authorities[0].authority == 'YAJY_CBDW' &&
                    record.ifMain == 1
                  "
                  :loading="sfLoading"
                  @click="adjustMinor"
                >调整会办单位</a-button
                > -->
            </a-space>
          </div>
        </div>
        <!--答复状态下 处理延期/调整 -->
        <div v-if="scenesStatus == '处理延期' || scenesStatus == '处理调整' || scenesStatus == '审核调整' ||scenesStatus == '审核调整复核'">
          <div>
            <standard-table row-key="undertakeId" :columns="columnsPostpone" :selected-rows.sync="selectedRowsPostpone"
              :data-source="dataSourcePostpone" :pagination="false">
              <div slot="action" slot-scope="{ text, record }">
                <a-button v-if="record.attName && record.attSuffix" type="link" @click="downFileDataDf(record, 6)">
                  {{ record.attName + "." + record.attSuffix }}
                </a-button>
              </div>
            </standard-table>
          </div>
          <!-- 处理延期 -->
          <div v-if="tabIndex == 1 && scenesStatus == '处理延期' && record.ifDelay" style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button @click="treatmentDelay">处理延期</a-button>
              <a-button @click="maintainAPrimaryOffice">维持原交办</a-button>
            </a-space>
          </div>
          <!-- 处理调整 -->
          <!-- ifObjection 判断是否处理调整 -->
          <div v-if="tabIndex == 1 && scenesStatus == '处理调整' && record.ifObjection" style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button @click="openUnitModalVisible">单位调整</a-button>
              <a-button @click="maintainAPrimaryOffice">维持原交办</a-button>
              <!-- <a-button @click="resetManage">重新交办</a-button> -->
            </a-space>
          </div>
          <!-- 审核会办单位调整 -->
          <div v-if="tabIndex == 1 && scenesStatus == '审核调整'  && record.ifMinor" style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button @click="openUnitModalInfoVisible">查看调整信息</a-button>
              <a-button @click="saveUnitMinor">审核通过</a-button>
              <a-button @click="returnUnitMinor">退回</a-button>
            </a-space>
          </div>
          <!-- 会办单位主动调整复核 -->
          <div v-if="tabIndex == 1 && scenesStatus == '审核调整复核' " style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button @click="openUnitReviseModalInfoVisible">查看调整信息</a-button>
              <a-button @click="saveReviseUnitMinor">复核通过</a-button>
              <a-button @click="returnUnitMinorRevise">退回</a-button>
            </a-space>
          </div>

          <!-- 延期/调整 弹出框 -->
          <a-modal :visible="visiblePostpone" width="50%" :title="modalTitle" @ok="submitDelay" @cancel="() => {
            visiblePostpone = false;
          }
            ">
            <a-form-model ref="resourceForm" :model="resourceForm" :rules="rules" :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col span="24">
                  <a-form-model-item label="描述" prop="comment">
                    <a-textarea v-model="resourceForm.comment" row="6"></a-textarea>
                  </a-form-model-item>
                </a-col>
                <a-col v-if="modalTitle == '处理延期'" span="24">
                  <a-form-model-item label="延期天数" prop="delayDay">
                    <a-input-number v-model="resourceForm.delayDay" :min="0"></a-input-number>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </a-modal>
          <!-- 单位调整 弹出框 -->
          <a-modal :visible="unitModalVisible" width="50%" title="单位调整" :body-style="{ height: '460px' }" ok-text="交办"
            @ok="saveUnitModal" @cancel="cancelUnitModal">
            <div :style="tabStyle">
              <a-space class="operator" v-if="scenesStatus != '审核调整'">
                <a-button type="primary" @click="openChange">新增</a-button>
                <a-button icon="delete" @click="unitModalVisibleDel">删除</a-button>
                <a-button type="" @click="changeIfMain">修改</a-button>
              </a-space>
              <standard-table row-key="orgCode" :columns="unitModalVisibleColumns"
                :selected-rows.sync="unitModalVisibleSelectedRows" :data-source="unitModalVisibleDataSource"
                :pagination="false" :scroll="{ x: 600, y: 300 }">
              </standard-table>
            </div>
          </a-modal>

          <a-modal :visible="unitModalInfoVisible" width="50%" title="会办单位调整信息" :body-style="{ height: '460px' }"
            ok-text="确定" @ok="cancelUnitInfoModal" @cancel="cancelUnitInfoModal">
            <div :style="tabStyle">
              <a-space class="operator" v-if="scenesStatus != '审核调整' && scenesStatus!='审核调整复核'">
                <a-button type="primary" @click="openChange">新增</a-button>
                <a-button icon="delete" @click="unitModalVisibleDel">删除</a-button>
                <a-button type="" @click="changeIfMain">修改</a-button>
              </a-space>
              <standard-table row-key="orgCode" :columns="unitModalVisibleColumns"
                :selected-rows.sync="unitModalVisibleSelectedRows" :data-source="unitModalVisibleDataSource"
                :pagination="false" :scroll="{ x: 600, y: 300 }">
              </standard-table>
            </div>
          </a-modal>

          <!-- 单位调整弹出框 修改主办/会办 -->
          <a-modal :visible="changeIfMainVisible" :title="changeIfMainTitle"
          @ok="saveChangeIfMain"
          @cancel="() => {
            changeIfMainVisible = false;
          }
            ">
            <a-form-model
            ref="resourceForm"
            :rules="rules"
            :model="resourceForm"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }">
              <a-form-model-item v-if="changeIfMainTitle == '添加承办单位'" label="选择承办单位">
                <div class="searchStyle">
                  <a-input v-model="changeIfMainForm.orgName" disabled>
                  </a-input>
                  <a-button type="primary" icon="search" @click="openUnitTable('单位调整')">
                  </a-button>
                </div>
              </a-form-model-item>
              <a-form-model-item label="设置承办类别">
                <a-select v-model="changeIfMainForm.ifmain">
                  <a-select-option key="1" :value="1">主办单位</a-select-option>
                  <a-select-option key="0" :value="0">会办单位</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-form-model>
          </a-modal>
        </div>
        <!-- 反馈 -->
        <!-- <div v-if="record.undertakeStatus == 3 && userName == record.headPer">
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button @click="openEvaluationModal">反馈</a-button>
            </a-space>
          </div>
        </div> -->

          <div v-if="tabIndex == 1 && record.undertakeStatus == 3 && record.status == 70
              && !(scenesStatus == '建议组负责人确认' ||scenesStatus == '交办小组确认'||
              scenesStatus == '编办确认' ||scenesStatus == '领导审定') ">
            <div style="text-align: center; margin: 10px 0">
              <a-space class="operator">
                <a-button @click="openEvaluationModal">反馈</a-button>
              </a-space>
            </div>
          </div>
        </div>
        <!-- 草稿 -->
        <div v-if="tabIndex == 1 && (record.status == 10 || record.status == 11) &&
          userData.userId == record.createUserId
          ">
          <div style="text-align: center; margin: 10px 0">
            <a-space class="operator">
              <a-button @click="formallySubmit">正式提交</a-button>
            </a-space>
          </div>
        </div>
        <!-- 步骤图 -->
<!--        <div>-->
<!--          <a-steps v-model="current">-->
<!--            <a-step disabled v-for="(step, index) in flowNodeDataSource" :key="index" :title="step.description" :description="step.description" />-->
<!--          </a-steps>-->
<!--        </div>-->

<!--        <a-tabs v-model="tabsKey" type="card" @change="changeTab">-->
<!--          <a-tab-pane key="1" tab="议案建议信息">-->
<!--            <div :style="tabStyle">-->
<!--              <a-descriptions bordered size="small">-->
<!--                <a-descriptions-item label="标题">{{-->
<!--                  record.proposalTitle-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="届次">{{-->
<!--                  record.periodDesc-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="意向承办单位">{{-->
<!--                  record.intentOrgName || ""-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="状态">{{-->
<!--                  record.status | filterStatus-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="当前经办人" :span="2">-->
<!--                  <div v-if="record.yajyOrgList && record.yajyOrgList.length > 0">-->
<!--                    <span v-for="(item, index) in record.yajyOrgList" :key="index">-->
<!--                      {{-->
<!--                        item.orgName +-->
<!--                        "(" +-->
<!--                        item.orgDispname +-->
<!--                        item.orgDispphone +-->
<!--                        ");"-->
<!--                      }}-->
<!--                    </span>-->
<!--                  </div>-->
<!--                  <div v-if="record.yajyOrgOperDTOList &&-->
<!--                    record.yajyOrgOperDTOList.length > 0-->
<!--                    ">-->
<!--                    <span v-if="record.yajyOrgOperDTOList[0].orgCode == '387'">-->
<!--                      <span>-->
<!--                        {{-->
<!--                          record.yajyOrgOperDTOList[0].orgName +-->
<!--                          "(" +-->
<!--                          record.yajyOrgOperDTOList[0].telNo +-->
<!--                          ")"-->
<!--                        }}-->
<!--                      </span>-->
<!--                      <br />-->
<!--                    </span>-->
<!--                    <span v-else>-->
<!--                      <span v-for="(item, index) in record.yajyOrgOperDTOList" :key="index">-->
<!--                        {{ item.operName + "(" + item.telNo + ")" }}-->
<!--                      </span>-->
<!--                    </span>-->
<!--                  </div>-->
<!--                  <div v-if="record.identityAllocationList && record.identityAllocationList.length > 0">-->
<!--                    <span v-for="(item, index) in record.identityAllocationList" :key="index">-->
<!--                      {{-->
<!--                        item.orgName +-->
<!--                        "(" +-->
<!--                        item.userName +-->
<!--                        item.mobile +-->
<!--                        ");"-->
<!--                      }}-->
<!--                    </span>-->
<!--                  </div>-->

<!--                </a-descriptions-item>-->
<!--                <a-descriptions-item label="建议号">{{-->
<!--                  record.proposalNum-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="议案建议类型">{{-->
<!--                  record.proposalType | filterProposalType-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="领衔代表">{{-->
<!--                  record.headPer-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="内容分类">{{-->
<!--                  record.proposalContentType | filterProposalContentType-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="办理情况">{{-->
<!--                  record.proposalHandle-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="所在代表团">{{-->
<!--                  record.deputyOrg-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="交办日期">{{-->
<!--                  record.handleDate-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="办理期限至">主办：{{-->
<!--                  majorLastTime ? majorLastTime.substring(0, 10) : ""-->
<!--                }}-->
<!--                  会办：{{-->
<!--                    minorLastTime ? minorLastTime.substring(0, 10) : ""-->
<!--                  }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="答复方式">{{-->
<!--                  record.codeHaveOrNo == "1"-->
<!--                  ? "书面"-->
<!--                  : record.codeHaveOrNo == "0"-->
<!--                    ? "网上"-->
<!--                    : ""-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="加强沟通">-->
<!--                  {{-->
<!--                    record.ifContect == "1"-->
<!--                    ? "需要见面座谈和调研"-->
<!--                    : record.ifContect == "0"-->
<!--                      ? "只需电话微信沟通"-->
<!--                      : record.ifContect == "2"-->
<!--                        ? "不需要沟通，直接答复"-->
<!--                        : record.ifContect == "3"-->
<!--                          ? "工作参考用，不需要正式书面答复"-->
<!--                          : ""-->
<!--                  }}-->
<!--                </a-descriptions-item>-->
<!--                <a-descriptions-item label="多年多次提出、尚未解决">{{-->
<!--                  record.overTheYearsNotResolved == "1"-->
<!--                  ? "是"-->
<!--                  : record.overTheYearsNotResolved == "0"-->
<!--                    ? "否"-->
<!--                    : record.overTheYearsNotResolved == "2"-->
<!--                      ? "未详"-->
<!--                      : ""-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="代表本人是否同意公开">{{-->
<!--                  record.ifPublice == "0"-->
<!--                  ? "不"-->
<!--                  : record.ifPublice == "1"-->
<!--                    ? "是"-->
<!--                    : record.ifPublice == "2"-->
<!--                      ? "已公开"-->
<!--                      : ""-->
<!--                }}</a-descriptions-item>-->

<!--                <a-descriptions-item label="附件列表" :span="2" v-if="mediaType != '其他建议'"><span-->
<!--                    v-if="record.existFile == true">附件：<a @click="downFileData('1', '')">附件下载</a></span>-->
<!--                </a-descriptions-item>-->
<!--                <a-descriptions-item label="代表工委是否公开意见" :span="1">{{-->
<!--                  record.examineXlwSugg == "0" || record.examineXlwSugg == null-->
<!--                  ? "未提出意见"-->
<!--                  : record.examineXlwSugg == "1"-->
<!--                    ? "建议公开"-->
<!--                    : record.examineXlwSugg == "2"-->
<!--                      ? "建议不公开"-->
<!--                      : ""-->
<!--                }}-->
<!--                </a-descriptions-item>-->
<!--                <a-descriptions-item label="建议纸" :span="2" v-if="this.mediaType != '其他建议'"><a-->
<!--                    @click="downMotion">下载建议纸</a>-->
<!--                </a-descriptions-item>-->
<!--                <a-descriptions-item label="研究室建议是否公开" :span="1">{{-->
<!--                  record.examineXcc == "0" || record.examineXcc == null-->
<!--                  ? "未提出意见"-->
<!--                  : record.examineXcc == "1"-->
<!--                    ? "建议公开"-->
<!--                    : record.examineXcc == "2"-->
<!--                      ? "建议不公开"-->
<!--                      : ""-->
<!--                }}-->
<!--                </a-descriptions-item>-->
<!--                <a-descriptions-item label="正文" :span="2">-->
<!--                  <a-space>-->
<!--                    <a @click="lookWord">查看正文</a>-->
<!--                    <a @click="downFileData('9', '')" v-if="this.mediaType != '其他建议'">下载正文附件</a>-->
<!--                    <a @click="resetFile" v-if="this.mediaType != '其他建议'">重新上传正文附件</a>-->
<!--                    &lt;!&ndash; <a @click="regenerateText">重新生成正文</a> &ndash;&gt;-->
<!--                  </a-space>-->
<!--                </a-descriptions-item>-->
<!--                <a-descriptions-item label="建议审核是否公开发布" :span="1">{{-->
<!--                  record.examineXlgw == "0"-->
<!--                  ? "未审核"-->
<!--                  : record.examineXlgw == "1"-->
<!--                    ? "审核公开"-->
<!--                    : record.examineXlgw == "2"-->
<!--                      ? "审核不公开"-->
<!--                      : ""-->
<!--                }}-->
<!--                </a-descriptions-item>-->
<!--              </a-descriptions>-->
<!--            </div>-->
<!--          </a-tab-pane>-->
<!--          <a-tab-pane key="2" tab="答复、反馈">-->
<!--            <div :style="tabStyle">-->
<!--              <a-descriptions bordered size="small">-->
<!--                <a-descriptions-item label="标题" :span="3">{{-->
<!--                  record.proposalTitle-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="建议号">{{-->
<!--                  record.proposalNum-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="类别">{{-->
<!--                  record.proposalType | filterProposalType-->
<!--                }}</a-descriptions-item>-->
<!--                <a-descriptions-item label="领衔代表">{{-->
<!--                  record.headPer-->
<!--                }}</a-descriptions-item>-->
<!--                &lt;!&ndash; 电话 短信 邮件 微信：0或1或4      2  座谈会  调研    3   代表表示无需见面沟通 &ndash;&gt;-->
<!--                <a-descriptions-item label="答复1沟通方式">{{-->
<!--                  record.dffs == "1" || record.dffs == "0" || record.dffs == "4"-->
<!--                  ? "电话 短信 邮件 微信"-->
<!--                  : record.dffs == "2"-->
<!--                    ? "座谈会 调研"-->
<!--                    : record.dffs == "3"-->
<!--                      ? "代表表示无需见面沟通"-->
<!--                      : ""-->
<!--                }}</a-descriptions-item>-->
<!--              </a-descriptions>-->
<!--              <a-descriptions v-if="majorUndertakeDfList.length > 0" title="主办单位"></a-descriptions>-->
<!--              <template v-for="item in majorUndertakeDfList" v-if="majorUndertakeDfList.length > 0">-->
<!--                <a-descriptions :key="item.undertakeId" size="small" bordered class="mt2"-->
<!--                  :column="{ xxl: 8, xl: 8, lg: 8, md: 4, sm: 4, xs: 1 }">-->
<!--                  <a-descriptions-item label="单位名称" :span="2">{{-->
<!--                    item.orgName-->
<!--                  }}</a-descriptions-item>-->
<!--                  <a-descriptions-item label="签收人" :span="4">{{-->
<!--                    item.signName-->
<!--                  }}</a-descriptions-item>-->
<!--                  <a-descriptions-item label="签收人手机" :span="2"><a>{{ item.signPhone }}</a></a-descriptions-item>-->
<!--                  <a-descriptions-item label="是否签收" :span="2">{{-->
<!--                    item.signTime ? item.signTime : "未签收"-->
<!--                  }}</a-descriptions-item>-->
<!--                  <a-descriptions-item label="经办人" :span="4">部门：{{ item.jbrDept }} 职务：{{ item.jbrJob }} 姓名：{{-->
<!--                    item.jbrName-->
<!--                  }}</a-descriptions-item>-->
<!--                  <a-descriptions-item label="经办人手机" :span="2"><a>{{ item.jbrPhone }}</a></a-descriptions-item>-->
<!--                </a-descriptions>-->
<!--                <template v-for="(itemdf, index) in item.dfList">-->
<!--                  <a-descriptions :key="itemdf.dfId" size="small" bordered-->
<!--                    :column="{ xxl: 8, xl: 8, lg: 8, md: 4, sm: 4, xs: 1 }">-->
<!--                    <a-descriptions-item :label="`第${index + 1}次答复`" :span="2">-->
<!--                      {{ itemdf.dfrq }}-->
<!--                    </a-descriptions-item>-->
<!--                    <a-descriptions-item label="答复类型" :span="2"><span>{{ itemdf.dflb }}</span>-->
<!--                    </a-descriptions-item>-->
<!--                    <a-descriptions-item label="答复公开" :span="2"><span>{{ itemdf.isPublic == "1" ? "是" : "否" }}</span>-->
<!--                    </a-descriptions-item>-->
<!--                    <a-descriptions-item label="附件名称" :span="2"><a v-if="itemdf.fujianList.length > 0"-->
<!--                        @click="downFileDataDf(itemdf, 2)">【查看答复附件】</a>-->
<!--                    </a-descriptions-item>-->
<!--                    <a-descriptions-item v-if="itemdf.isPublic == '0'" label="答复不公开理由" :span="8"><span>{{-->
<!--                      itemdf.remarkText }}</span>-->
<!--                    </a-descriptions-item>-->
<!--                    <a-descriptions-item v-if="itemdf.hisEvaluation" :label="`第${index + 1}次反馈`" :span="2">-->
<!--                      {{ itemdf.hisEvaluation.submittime }}-->
<!--                    </a-descriptions-item>-->
<!--                    <a-descriptions-item v-if="itemdf.hisEvaluation" label="反馈意见" :span="4"><span-->
<!--                        v-if="itemdf.hisEvaluation.score >= 80">满意</span>-->
<!--                      <span v-if="itemdf.hisEvaluation.score >= 60 &&-->
<!--                        itemdf.hisEvaluation.score < 80-->
<!--                        ">基本满意</span>-->
<!--                      <span v-if="itemdf.hisEvaluation.score < 60">不满意</span>-->
<!--                    </a-descriptions-item>-->
<!--                    <a-descriptions-item v-if="itemdf.hisEvaluation" label="反馈内容" :span="2"><a-->
<!--                        @click="viewFkEvaluation(itemdf.hisEvaluation)">【查看反馈内容】</a>-->
<!--                    </a-descriptions-item>-->
<!--                  </a-descriptions>-->
<!--                </template>-->
<!--              </template>-->
<!--              <a-descriptions v-if="minorUndertakeDfList.length > 0" title="会办单位"></a-descriptions>-->
<!--              <template v-for="item in minorUndertakeDfList" v-if="minorUndertakeDfList.length > 0">-->
<!--                <a-descriptions :key="item.undertakeId" size="small" bordered class="mt2"-->
<!--                  :column="{ xxl: 8, xl: 8, lg: 8, md: 4, sm: 4, xs: 1 }">-->
<!--                  <a-descriptions-item label="单位名称" :span="2">{{-->
<!--                    item.orgName-->
<!--                  }}</a-descriptions-item>-->
<!--                  <a-descriptions-item label="签收人" :span="4">{{-->
<!--                    item.signName-->
<!--                  }}</a-descriptions-item>-->
<!--                  <a-descriptions-item label="签收人手机" :span="2"><a>{{ item.signPhone }}</a></a-descriptions-item>-->
<!--                  <a-descriptions-item label="是否签收" :span="2">{{-->
<!--                    item.signTime ? item.signTime : "未签收"-->
<!--                  }}</a-descriptions-item>-->
<!--                  <a-descriptions-item label="经办人" :span="4">部门：{{ item.jbrDept }} 职务：{{ item.jbrJob }} 姓名：{{-->
<!--                    item.jbrName-->
<!--                  }}</a-descriptions-item>-->
<!--                  <a-descriptions-item label="经办人手机" :span="2"><a>{{ item.jbrPhone }}</a></a-descriptions-item>-->
<!--                </a-descriptions>-->
<!--                <template v-for="itemdf in item.dfList">-->
<!--                  <a-descriptions :key="itemdf.dfId" size="small" bordered-->
<!--                    :column="{ xxl: 8, xl: 8, lg: 8, md: 4, sm: 4, xs: 1 }">-->
<!--                    <a-descriptions-item label="已答复" :span="3">-->
<!--                      {{ itemdf.dfrq }}-->
<!--                    </a-descriptions-item>-->
<!--                    <a-descriptions-item label="附件名称" :span="5"><a v-if="itemdf.fujianList.length > 0"-->
<!--                        @click="downFileDataDf(itemdf, 2)">【查看答复附件】</a>-->
<!--                    </a-descriptions-item>-->
<!--                  </a-descriptions>-->
<!--                </template>-->
<!--              </template>-->
<!--            </div>-->
<!--          </a-tab-pane>-->
<!--          <a-tab-pane key="3" tab="提出代表">-->
<!--            <div :style="tabStyle">-->
<!--              &lt;!&ndash; v-if="-->
<!--              ((record.status == 10 || record.status < 20) &&-->
<!--                authority == 'YAJY_QRD') ||-->
<!--              authority == 'YAJY_XLW' ||-->
<!--              authority == 'YAJY_DBC'-->
<!--            " &ndash;&gt;-->
<!--              <a-space v-if="userData.authorities-->
<!--                ? record.status < 90 &&-->
<!--                (userData.authorities[0].authority == 'YAJY_QRD' ||-->
<!--                  userData.authorities[0].authority == 'YAJY_XLW' ||-->
<!--                  userData.authorities[0].authority == 'YAJY_DBC' ||-->
<!--                  userData.authorities[0].authority == 'I-XTGLY' ||-->
<!--                  userData.authorities[0].authority == 'YAJY_DB')-->
<!--                : false-->
<!--                " class="operator">-->
<!--                <a-button type="primary" icon="plus-circle" @click="showUserTable('提出')"-->
<!--                  v-if="this.mediaType != '其他建议'">新建</a-button>-->
<!--                <a-button icon="delete" @click="delJointlyList" v-if="this.mediaType != '其他建议'">删除</a-button>-->
<!--              </a-space>-->
<!--              <standard-table row-key="jointlyId" :columns="deputationColumns"-->
<!--                :selected-rows.sync="deputationSelectedRows" :data-source="deputationDataSource" :show-alert="userData.authorities-->
<!--                  ? record.status < 90 &&-->
<!--                  (userData.authorities[0].authority == 'YAJY_QRD' ||-->
<!--                    userData.authorities[0].authority == 'YAJY_XLW' ||-->
<!--                    userData.authorities[0].authority == 'YAJY_DBC' ||-->
<!--                    userData.authorities[0].authority == 'I-XTGLY' ||-->
<!--                    userData.authorities[0].authority == 'YAJY_DB')-->
<!--                  : false-->
<!--                  ">-->
<!--              </standard-table>-->
<!--            </div>-->
<!--          </a-tab-pane>-->
<!--          <a-tab-pane key="4" tab="承办单位">-->
<!--            <div :style="tabStyle">-->
<!--              <standard-table row-key="undertakeId" :columns="unitColumns" :data-source="unitDataSource">-->
<!--              </standard-table>-->
<!--            </div>-->
<!--          </a-tab-pane>-->
<!--          <a-tab-pane key="5" tab="流程跟踪">-->
<!--            <div :style="tabStyle">-->
<!--              &lt;!&ndash; <standard-table :columns="processColumns" row-key="historyId" :data-source="processDataSource">-->
<!--              </standard-table> &ndash;&gt;-->

<!--              &lt;!&ndash; 步骤条 &ndash;&gt;-->
<!--              <div class="step-container">-->
<!--                <div v-for="(step, index) in processDataSource" :key="step.historyId" class="step"-->
<!--                  :style="{ position: 'relative', marginBottom: index < processDataSource.length - 1 ? '20px' : '0' }">-->
<!--                  &lt;!&ndash; <div-->
<!--                    :class="[-->
<!--                      'step-circle',-->
<!--                      { 'step-active': index <= currentStep, 'step-completed': index < currentStep }-->
<!--                    ]"-->
<!--                  > &ndash;&gt;-->
<!--                  <div class="step-circle step-active step-completed">-->
<!--                    &lt;!&ndash; <span v-if="index <= currentStep" class="step-number">{{ index + 1 }}</span> &ndash;&gt;-->
<!--                  </div>-->

<!--                  <div class="step-content">-->
<!--                    &lt;!&ndash; replyType在最上面，居中显示 &ndash;&gt;-->
<!--                    <div class="step-reply-type">{{ step.replyType }}</div>-->
<!--                    <div style="margin-left:20px">-->
<!--                      <div class="step-reply-time">{{ step.replyTime }}</div>-->

<!--                      &lt;!&ndash; 时间、提交人、办理意见在一行内，用灰色背景包裹，使用竖线分隔 &ndash;&gt;-->
<!--                      <div class="step-info">-->
<!--                        &lt;!&ndash; 提交人、处理人 &ndash;&gt;-->
<!--                        <div class="info-item" style="width:20%">-->
<!--                          <span v-if="index == 0">-->
<!--                            <strong style="margin-right:10px">提交人:</strong> {{ step.reply_by_name }}-->
<!--                          </span>-->
<!--                          <span v-else>-->
<!--                            <strong style="margin-right:10px">处理人:</strong> {{ step.reply_by_name }}-->
<!--                          </span>-->
<!--                        </div>-->
<!--                        &lt;!&ndash; 岗位 &ndash;&gt;-->
<!--                        <div class="info-item" style="width:40%">-->
<!--                          <span>-->
<!--                            <strong style="margin-right:10px">岗位:</strong>-->
<!--                            暂无数据-->
<!--                          </span>-->
<!--                        </div>-->
<!--                        &lt;!&ndash; 办理意见 &ndash;&gt;-->
<!--                        <div class="info-item" style="width:40%">-->
<!--                          <span v-if="step.replyComment" class="comment-text"><strong style="margin-right:10px">办理意见:</strong>{{-->
<!--                            step.replyComment }}</span>-->
<!--                          <span v-else>-->
<!--                            <strong style="margin-right:10px">办理意见:</strong> &#45;&#45;&#45;&#45;&#45;&#45;-->
<!--                          </span>-->
<!--                        </div>-->
<!--                      </div>-->
<!--                    </div>-->
<!--                  </div>-->

<!--                  &lt;!&ndash; 连接线 &ndash;&gt;-->
<!--                  &lt;!&ndash; :style="{ height: getLineHeight(index) + 'px' }" &ndash;&gt;-->
<!--                  <div v-if="index < processDataSource.length - 1" class="step-line" style="height: 100%"></div>-->
<!--                </div>-->
<!--              </div>-->

<!--            </div>-->

<!--          </a-tab-pane>-->
<!--          <a-tab-pane key="6" tab="公文答复函">-->
<!--            <div :style="tabStyle">-->
<!--              <standard-table :columns="dfhColumns" row-key="dfhId" :data-source="dfhDataSource">-->
<!--              </standard-table>-->
<!--            </div>-->
<!--          </a-tab-pane>-->
<!--        </a-tabs>-->

        <commonUnitShuttleTable ref="commonUnitShuttleTable" :default-unit="defaultUnit" :unitType="unitType"
          :disableSelectedRowKeys="disableSelectedRowKeys" @emitUnitTable="getUnitTable"></commonUnitShuttleTable>
        <!-- 会办 -->
        <commonUnitShuttleTableHB ref="commonUnitShuttleTableHB" :default-unit="defaultUnitHB" :unitType="unitType"
          :disableSelectedRowKeys="disableSelectedRowKeysHB" @emitUnitTable="getUnitTableHB"></commonUnitShuttleTableHB>
        <commonUserTable ref="commonUserTable" :disableSelectedRowKeys="disableSelectedRowKeylist"
          @emitJoinTable="getJoinTable"></commonUserTable>
        <evaluationModal ref="evaluationModal" @emitClose="close"></evaluationModal>
        <!-- 重新上传正文附件 -->
        <a-modal class="small" :visible="fileVisible" title="重新上传正文附件" @ok="resetSubmit" @cancel="closeFileModal">
          <a-form-model>
            <a-form-model-item label="正文附件">
              <!-- 上传 -->
              <a-upload action="" accept=".docx,.doc" :remove="handleTextRemoveReset" :beforeUpload="beforeUploadReset"
                @change="uploadChangeReset($event, '9')" :fileList="fileTextListReset">
                <a-button :disabled="fileTextListReset.length == 1" type="primary">点击上传</a-button>
                <div>
                  <!-- :hidden="fileTextListReset.length == 1" -->
                  只能上传doc,docx文件，且不超过20mb
                </div>
              </a-upload>
            </a-form-model-item>
          </a-form-model>
        </a-modal>
        <!-- 申请延期 -->
        <a-modal :body-style="{}" :visible="delayVisible" title="申请延期" width="60%" @ok="adjustSubmit"
          @cancel="cancelModal">
          <a-form-model ref="delayForm" :model="delayForm" :rules="delayFormRules" :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }">
            <a-row>
              <a-col span="24">
                <a-form-model-item label="延期描述" prop="comment">
                  <a-textarea v-model="delayForm.comment" :rows="5"></a-textarea>
                </a-form-model-item>
              </a-col>
              <a-col span="24">
                <a-form-model-item label="延期天数" prop="delayDay">
                  <a-input-number v-model="delayForm.delayDay" :min="0"></a-input-number>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </a-modal>

        <!-- 调整会办单位 弹出框 -->
        <a-modal :visible="unitMinorModalVisible" width="50%" title="调整会办单位" :body-style="{ height: '460px' }"
          ok-text="调整" @ok="saveUnitMinorModal" @cancel="cancelUnitMinorModal">
          <div :style="tabStyle">
            <a-space class="operator">
              <a-button type="primary" @click="openChangeMinor">新增</a-button>
              <a-button icon="delete" @click="unitModalMinorVisibleDel">删除</a-button>
            </a-space>
            <standard-table row-key="orgCode" :columns="unitModalVisibleColumns"
              :selected-rows.sync="unitModalVisibleSelectedRows" :data-source="unitModalVisibleDataSource"
              :pagination="false" :scroll="{ x: 600, y: 300 }"
              >
            </standard-table>
          </div>
        </a-modal>
        <!-- 会办单位调整弹出框 修改会办 -->
        <a-modal :visible="changeIfMainMinorVisible" :title="changeIfMainTitle" @ok="saveChangeIfMainMinor" @cancel="() => {
          changeIfMainMinorVisible = false;
        }
          ">
          <a-form>
            <a-form-item v-if="changeIfMainTitle == '添加会办单位'" label="选择单位">
              <div class="searchStyle">
                <a-input v-model="changeIfMainForm.orgName" disabled>
                </a-input>
                <a-button type="primary" icon="search" @click="openUnitTable('单位调整')">
                </a-button>
              </div>
            </a-form-item>
          </a-form>
        </a-modal>

        <adjustListEdit ref="adjustListEdit" @close="close"></adjustListEdit>
        <a-modal v-model="fkVisible" title="反馈内容" @ok="closeFkVisible">
          <a-descriptions>
            <a-descriptions-item label="反馈评价" :span="3">{{
              fkEvaluation
            }}</a-descriptions-item>
            <a-descriptions-item label="反馈内容" :span="3">{{
              fkContent
            }}</a-descriptions-item>
          </a-descriptions>
        </a-modal>
        <jbrTable ref="jbrTable" :org-code="jbrTableOrgCode" @emitJbrTable="getJbrTable"></jbrTable>

        <a-modal :dialog-style="{ top: '-200px' }" :body-style="{ minHeight: '200px', overflowY: 'auto' }"
          :visible="iShow" title="办理意见详情" width="40%" :centered="true" :footer="null" @cancel="iShow = !iShow">
          {{ replyComment }}
        </a-modal>
      </a-spin>

      <!-- 公开意见-->
      <a-modal :visible.sync="dialogVisiblePublicComment"
               title="查看公开意见"
               :width="600"
               @cancel="
                () => {
                  dialogVisiblePublicComment = false;
                }
      ">
        <template slot="footer">
          <a-button type="default"
                    style="margin-right: 15px;"
                    @click="
            () => {
              dialogVisiblePublicComment = false;
            }
          ">取消</a-button>
        </template>
        <a-input
          type="textarea"
          v-model="publicComment"
          placeholder="无内容"
          :read-only="true"
          :disabled="true"
        />
      </a-modal>

      <!-- 公开意见复审环节修改-->
      <a-modal :visible.sync="dialogVisiblePublicCommentFs"
               title="修改公开意见"
               :width="500"
               @cancel="
                () => {
                  dialogVisiblePublicCommentFs = false;
                }
      ">
        <template slot="footer">
<!--          <a-button type="default"-->
<!--                    style="margin-right: 15px;"-->
<!--                    @click="-->
<!--            () => {-->
<!--              dialogVisiblePublicCommentFs = false;-->
<!--            }-->
<!--          ">取消</a-button>-->
          <a-button type="primary"
                    @click="fsPublicSubmit">修改意见</a-button>
        </template>
        <a-form-model-item
          label="是否公开"
          prop="public"
          :label-col="{ span: 5 }"
        >
          <a-radio-group
            v-model="resourceForm.xlwIfPublice"
            @change="changePublic(resourceForm.xlwIfPublice)"
          >
            <a-radio value="1"> 是 </a-radio>
            <a-radio value="2"> 否 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="出具意见" prop="comment" class="form-item" :label-col="{ span: 5 }">
        <a-input
          type="textarea"
          rows="6"
          style="width: 300px"
          v-model="resourceForm.publicComment"
          placeholder="无内容"
          :read-only="false"
          :disabled="false"
        />
        </a-form-model-item>
      </a-modal>

    </a-modal>

    <submitForCensorship ref="submitForCensorship" :procInstId="procInstId" @complete="handleComplete" @close="close" :flowId="flowId" :defaultNode="defaultNode"
                         :actTypeReturn="actTypeReturn" :flowName="flowName">
    </submitForCensorship>
  </div>
</template>
<script>
// 建议详情 显示详情
import EnterContent from "@/components/EnterContent/indexList.vue";
import commonUnitShuttleTable from "@/views/common/commonUnitShuttleTable.vue";
import submitForCensorship from '@/views/common/submitReturn';
import commonUnitShuttleTableHB from "@/views/common/commonUnitShuttleTableHB.vue";
import evaluationModal from "@/views/meeting/suggesting/evaluationModal.vue";
import commonUserTable from "@/views/common/commonUserTable.vue";
import jbrTable from "@/views/common/jbrTable.vue";
import { fileUpload } from "@/api/commonApi/file.js";
import adjustListEdit from "@/views/meeting/suggesting/adjustListEdit.vue";
import {
  getDicts,
} from "@/api/parameterManagement/data";
import {
  findBySession,
  updatePublicComment,
  auditReturnFalse,
  saveUnRegister,
  saveUnRegisterBh,
  doMinorSave,
  doReviseReviseSave,
  doMinorReturn,
  doMinorReviseReturn,
  proposalById,
  doAssign,
  doAssignBh,
  doAudit,
  doAuditBh,
  doYjsPublic,
  leaderCheckSug,
  doFirstCheckBh,
  findCommentByType,
  doFirstCheck,
  doClassify,
  doCheck,
  getJointlyList,
  addJointly,
  delJointly,
  againProduce,
  doUnRegister,
  doUnRegisterBh,
  AssignConfirm,
  AssignDbcConfirm,
  XlwConfirm,
  DbcConfirm,
  BbConfirm,
  updateSug,
  unPassRegister,
  getLocalHistory,
  getDfhData,
  getProposalUnder,
  doReply,
  getDelayUndertakeListById,
  doDelay,
  getReviseUndertakeListById,
  keepRevise,
  keepDelay,
  seeText,
  getUndertakeInfo,
  getReviseUndertakeInfo,
  doRevise,
  saveMinor,
  getJbrList,
  updateJbr,
  getUndertakeDfAndFeedbackList,
  againUploadTextFile,
  downloadProposal,
  saveCBDW,
  doSignedSave,
  getUndertakeStatus,
  getUndertakeDfInfo,
  applyDelay,
  findOpinionOption,
  isMeetingTime,
  deleteByAttId,
} from "@/api/myJob/myProposal.js";

import moment from "moment";
import { downFile, getUserData_yajy, downDfhFile } from "@/api/commonApi/file.js";
import { getAdjustMinorUndertakeListById,getReviseReviseUndertakeListById } from "../../api/myJob/myProposal";
import {getprocessApi,getAllFlowNode,getAllFlowNodeOther} from "../../api/yajyFlow/flowApi";
export default {
  components: {
    EnterContent,
    commonUnitShuttleTable,
    commonUnitShuttleTableHB,
    commonUserTable,
    evaluationModal,
    adjustListEdit,
    jbrTable,
    submitForCensorship,
  },

  watch: {
    // visible(val) {
    //   if (val) {
    //     this.getOneUser();
    //   }
    // },
    "resourceForm.text": {
      handler: function (val) {
        let TEXT = null;
        this.resourceForm.text.map((i, n) => {
          TEXT ? (TEXT += n + 1 + "." + i + "  ") : (TEXT = "1." + i + "  ");
        });
        // this.resourceForm.comment = TEXT
        if (this.resourceForm.text.toString().indexOf("其他") != -1) {
          this.isOtheryj = true;
          // this.resourceForm.comment += '(其他原因：)'
        } else {
          this.isOtheryj = false;
        }
      },
    },
  },
  filters: {
    filterStatus(text) {
      if (text === 10) {
        return "草稿";
      }
      if (text === 11) {
        return "联名中";
      }
      if (text === 20) {
        return "校核中";
      }
      if (text === 21) {
        return "分类中";
      }
      if (text === 22) {
        return "初审中";
      }
      if (text === 30 || text === 25) {
        return "复审中";
      }
      if (text === 33) {
        return "研究室出具意见中";
      }
      if (text === 40) {
        return "交办中";
      }
      if (text === 41) {
        return "不予立案审核中";
      }
      if (text === 42) {
        return "分办确认中";
      }
      if (text === 43) {
        return "分办二次确认中";
      }
      if (text === 45) {
        return "不予立案确认中";
      }
      if (text === 50) {
        return "签收中";
      }
      if (text === 60) {
        return "答复中";
      }
      if (text === 70) {
        return "待反馈";
      }
      if (text === 80) {
        return "已反馈";
      }
      if (text === 90) {
        return "办毕";
      }
    },
    filterProposalContentType(text) {
      if (text == "JYFL01") return "法制";
      if (text == "JYFL02") return "监察和司法";
      if (text == "JYFL03") return "经济";
      if (text == "JYFL04") return "城建环资";
      if (text == "JYFL05") return "农村农业";
      if (text == "JYFL06") return "教科文卫";
      if (text == "JYFL07") return "华侨外事民族宗教";
      if (text == "JYFL08") return "其他";
      if (text == "JYFL09") return "预算";
      if (text == "JYFL10") return "社会建设";
    },
    filterProposalType(text) {
      if (text == "1") return "大会议案";
      if (text == "2") return "大会建议";
      if (text == "3") return "闭会建议";
      if (text == "4") return "供参考建议";
    },
  },
  data() {
    var hongzixinxibiao = (rule, value, callback) => {
      if (
        this.resourceForm.orgEvaluation == "" ||
        this.resourceForm.orgEvaluation == undefined
      ) {
        callback(new Error("请输入建议内容质量评分"));
      } else {
        callback();
      }
    };
    return {
      //字典表单
      detailsForm: {status:''},
      selectedRowKeys: [], // 选中的行key
      originalUndertakeList: [], // 存储API返回的原始数据
      isRendered: false,
      stepsData: [
        {
          "historyId": "6bbe7b47a74989414c967dec9f729469",
          "proposalId": "e926ec2239cbf9972a0b76ee835daa76",
          "replyBy": "45019",
          "replyByCode": "1028",
          "replyStatus": 10,
          "replyTime": "2024-07-09 09:59:14",
          "replyType": "正式提交",
          "reply_by_name": "雷*威",
          "revived": "0"
        },
        {
          "historyId": "2cb41dceb0a858337bdfed1a288892aa",
          "jbrPhone": "100******00",
          "proposalId": "e926ec2239cbf9972a0b76ee835daa76",
          "replyBy": "48305",
          "replyByCode": "1028",
          "replyComment": "",
          "replyStatus": 20,
          "replyTime": "2024-12-06 10:32:47",
          "replyType": "校核通过",
          "reply_by_name": "越*员",
          "revived": "0"
        }
      ],
      tipMessage:"",
      isMeeting:"",
      mediaType: "",
      userName: null,
      OpinionOptionList: [
        "建议内容不属于广州市行政区域内事务或者行政区域职权范围。",
        "建议内容不够明确具体，请进一步加强调研，反映实际情况，分析问题原因，提出改进工作、解决问题、完善政策的具体措施。",
        "存在《广东省各级人民代表大会代表建议、批评和意见办理规定》第十条所规定的不应当作为代表建议提出的若干情形。",
        "代表本人要求退回。",
        "其他",
      ],
      current: 0,
      isOtheryj: false,
      isPublicDisable: false,
      listLoading: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      tabStyle: { maxHeight: "600px", overflowY: "auto" }, // 对话框中的 tab 内容高度及垂直滚动条设置
      visible: false,
      proposalId: "", //父组件直接赋值 议题id
      fkVisible: false,
      fkEvaluation: "",
      fkContent: "",
      procInstId: "",
      defaultNode:"",
      actTypeReturn:"",
      majorUndertakeDfList: [],
      minorUndertakeDfList: [],
      ProposalUndertakeDfhList: [],
      record: { unIfmainList: [], ifmainList: [] },
      iShow: false,
      replyComment: "",
      userData: {},
      publicComment:"",
      resourceForm: {
        meetName: "",
        meet: "",
        text: [],
        host: "",
        hostName: "",
        adjust: "",
        adjustName: "",
        publicComment:"",
        isPublic: "",
        ifPublice:"",
        xlwIfPublice:"1",
        historyId:undefined,
        yjsIfPublice:"1",
        xlwCheck: "1",
        subLeaderCheck:"1",
        leaderCheck:"1",
        zwSug:"",
        dbSug:"",
        dffs: [],
        jbrName: "",
        jbrDept: "",
        jbrJob: "",
        jbrPhone: "",
        jbrOph: "",
        dflb: null,
        orgCode: "",
        mainHandleLastTimeStr: "",
        minorHandleLastTimeStr: "",
        finalAssignTimeStr: "",
        contentType: "",
        remark: "",
        remarkText: null,
        jbxzIfPublic: "", // 交办小组公开意见：0 不公开 1公开
        jbxzPublic: "" // 出具公开意见
      },
      delayFormRules: {
        comment: [
          { required: true, message: "请输入延期描述", trigger: "blur" },
        ],
        delayDay: [
          { required: true, message: "请输入延期天数", trigger: "blur" },
        ],
      },
      rules: {
        jbrName: [
          { required: true, message: "请输入经办人姓名", trigger: "blur" },
        ],
        jbrDept: [
          { required: true, message: "请输入经办人部门", trigger: "blur" },
        ],
        jbrJob: [
          { required: true, message: "请输入经办人职务", trigger: "blur" },
        ],
        jbrPhone: [
          { required: true, message: "请输入经办人手机", trigger: "blur" },
        ],
        jbrOph: [
          { required: true, message: "请输入经办人办公电话", trigger: "blur" },
        ],
        dflb: [
          { required: true, message: "请输入答复类型", trigger: "change" },
        ],
        dffs: [
          { required: true, message: "请输入沟通方式", trigger: "change" },
        ],
        isPublic: [
          { required: true, message: "请输入答复是否公开", trigger: "change" },
        ],
        remark: [
          { required: true, message: "请选择答复不公开理由", trigger: "change" },
        ],
        remarkText: [
          { required: true, message: "请填写答复不公开其他理由", trigger: "blur" },
        ],
        zwSug: [
          {
            required: true,
            message: "请选择专委转供参考意见",
            trigger: "change",
          },
        ],
        public: [
          {
            required: true,
            message: "请选择是否公开",
            trigger: "blur",
          },
        ],
        contentType: [
          {
            required: true,
            message: "请选择内容分类",
            trigger: "change",
          },
        ],
        dbSug: [
          {
            required: true,
            message: "请选择代表转供参考意见",
            trigger: "change",
          },
        ],
        orgEvaluation: [
          {
            required: true,
            message: "请输入建议内容质量评分",
            trigger: "blur",
            validator: hongzixinxibiao,
          },
        ],
        jbxzIfPublic: [
          {
            required: true,
            message: "请选择交办小组公开意见",
            trigger: "change",
          },
        ],
        jbxzPublic: [
          {
            required: false,
            message: "请选择出具公开意见",
            trigger: "change",
            validator: (rule, value, callback) => {
              // 只有当选择"不公开"时才要求必填
              if (this.resourceForm.jbxzIfPublic === '0' && (!value || value === '')) {
                callback(new Error('请选择出具公开意见'));
              } else {
                callback();
              }
            }
          },
        ],
      },
      unitColumns: [
        // 承办单位表格头定义
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "ifmain",
          customRender: (text, record, index) =>
            text == 1 ? "主办单位" : "会办单位",
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "办理期限至",
          ellipsis: true,
          dataIndex: "handleLastTime",
          customRender: (text) => {
            return text ? text.substring(0, 10) : "";
          }
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
      ],
      unitDataSource: [
        // 承办单位表格数据
      ],
      processColumns: [
        // 流程跟踪表格头定义
        {
          title: "状态",
          ellipsis: true,
          dataIndex: "replyType",
        },
        {
          title: "处理人",
          ellipsis: true,
          dataIndex: "reply_by_name",
        },
        {
          title: "处理时间",
          ellipsis: true,
          dataIndex: "replyTime",
        },
        {
          title: "办理意见",
          ellipsis: true,
          dataIndex: "replyComment",
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#1890ff",
                  },
                  on: {
                    click: () => {
                      this.clickRowData(record);
                    },
                  },
                },
                record.replyComment
              ),
            ]);
          },
        },
      ],
      processDataSource: [
        // 流程跟踪数据
      ],
      flowNodeDataSource:[
        //流程节点数据
      ],
      dfhColumns: [
        // 公文答复函表格头定义
        {
          title: "案号",
          ellipsis: true,
          dataIndex: "proposalNum",
          width: 300,
        },
        {
          title: "标题",
          ellipsis: true,
          dataIndex: "dfhTitle",
          width: 600,
        },
        {
          title: "发文单位",
          ellipsis: true,
          dataIndex: "orgName",
          width: 200,
        },
        {
          title: "答复函",
          ellipsis: true,
          dataIndex: "dfhId",
          width: 400,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#1890ff",
                  },
                  on: {
                    click: () => {
                      //答复函附件类型 40
                      this.downFileDataDfh(record);
                    },
                  },
                },
                "下载"
              ),
            ]);
          },
        },
      ],
      dfhDataSource: [
        // 答复函数据
      ],
      deputationColumns: [
        // 提出代表表格头定义
        {
          title: "类别",
          ellipsis: true,
          dataIndex: "ifpublic",
          customRender: (text, record, index) => {
            if (text == 1) return "领衔";
            if (text == 0) return "联名";
          },
        },
        {
          title: "代表姓名",
          ellipsis: true,
          dataIndex: "name",
        },
        {
          title: "通讯地址",
          ellipsis: true,
          dataIndex: "unitAddress",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "unitPhone",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "mobilePhone",
        },
        {
          title: "Email",
          ellipsis: true,
          dataIndex: "",
        },
        {
          title: "邮政编码",
          ellipsis: true,
          dataIndex: "",
        },
        {
          title: "申请时间",
          ellipsis: true,
          dataIndex: "createTime",
        },
        {
          title: "反馈时间",
          ellipsis: true,
          dataIndex: "agreen",
        },
        {
          title: "联名状态",
          ellipsis: true,
          dataIndex: "ifagreen",
          customRender: (text, record, index) => {
            if (text == 1) return "同意";
            if (text == 0) return "不同意";
            if (text == null) return "未处理";
          },
        },
      ],
      // 提出列表table
      deputationDataSource: [],
      // 提出列表选中数据
      deputationSelectedRows: [],
      unitType: "", //单位类型
      returnValue: false,
      tabsKey: "1",
      activeTabKey: "basic",
      fileTextList: [], //答复附件
      fileUnRegisterList: [], //不予立案
      perType: "", //打开代表列表的场景
      scenesStatus: "", // 场景默认是 处理延期
      dataSourcePostpone: [], //处理延期数据
      selectedRowsPostpone: [], //处理延期数据-已选
      dialogVisiblePublicComment:false ,//公开意见弹窗
      dialogVisiblePublicCommentFs:false ,//公开意见弹窗-修改
      columnsPostpone: [
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "",
          customRender: (text, record, index) => {
            return record.ifmain == 1 ? "主办单位" : "会办单位";
          },
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "申请时间",
          ellipsis: true,
          dataIndex: "applyDelayTime",
        },
        {
          title: "延期时长",
          ellipsis: true,
          dataIndex: "delayTimeTo",
        },
        {
          title: "调整原因",
          ellipsis: true,
          dataIndex: "applyCommnent",
        },
        {
          title: "附件名称",
          scopedSlots: { customRender: "action" },
        },
      ], //处理延期数据/单位调整 - 表头
      unitModalVisibleColumns: [
        {
          title: "承办类别",
          ellipsis: true,
          dataIndex: "ifmain",
          customRender: (text, record, index) => {
            if (text == "1") return "主办单位";
            if (text == "0") return "会办单位";
          },
        },
        {
          title: "单位名称",
          ellipsis: true,
          dataIndex: "orgName",
        },
        {
          title: "联系人",
          ellipsis: true,
          dataIndex: "jbrName",
        },
        {
          title: "联系电话",
          ellipsis: true,
          dataIndex: "jbrOph",
        },
        {
          title: "手机",
          ellipsis: true,
          dataIndex: "jbrPhone",
        },
        {
          title: "签收状态",
          ellipsis: true,
          dataIndex: "status",
          customRender: (text, record, index) => {
            if (text == 0) return "调整中";
            if (text == 1) return "签收中";
            if (text == 2) return "答复中";
            if (text == 3) return "待反馈";
            if (text == 5) return "拟删除";
            if (text == 6) return "拟改变单位性质";
          },
        },
      ], //弹出的单位调整列表
      undertakeId: "", //子流程id
      visiblePostpone: false,
      keepType: "",
      showOperate: true, //是否显示操作 默认显示
      modalTitle: "处理延期", //延期/调整弹出框
      unitModalVisible: false, //单位调整弹出框
      unitModalVisibleSelectedRows: [], //单位调整已选数据
      unitModalVisibleDataSource: [], //单位调整列表
      oldunitModalVisibleDataSource: [], //原单位调整列表

      unitModalInfoVisible: false, //会办单位调整信息

      unitMinorModalVisible: false, //调整会办单位弹出框
      unitMinorModalVisibleSelectedRows: [], //调整会办单位已选数据
      unitMinorModalVisibleDataSource: [], //调整会办单位列表
      changeIfMainMinorVisible: false, //调整会办单位弹出框 修改主办/会办

      changeIfMainVisible: false, //单位调整弹出框 修改主办/会办
      fileVisible: false, //重新上传文件窗口开关
      fileTextListReset: [], //重新上传文件列表
      isFileReset: undefined,
      contentTypeList: [
        // 内容分类
        { id: "JYFL01", name: "法制" },
        { id: "JYFL02", name: "监察和司法" },
        { id: "JYFL09", name: "预算" },
        { id: "JYFL03", name: "经济" },
        { id: "JYFL04", name: "城建环资" },
        { id: "JYFL05", name: "农村农业" },
        { id: "JYFL06", name: "教科文卫" },
        { id: "JYFL07", name: "华侨外事民族宗教" },
        { id: "JYFL10", name: "社会建设" },
        { id: "JYFL08", name: "其他" },
      ],
        noPublicType:[
          {id: "1", name: "涉及国家秘密、工作秘密、商业秘密、个人隐私等信息的"},
          {id: "2", name: "涉及公开后可能危及国家安全、公共安全、经济安全，以及可能引起重大负面舆情、造成混乱、影响社会稳定的"},
          {id: "3", name: "涉及重大政治、外交、历史遗留问题等方面内容公开后可能导致不良后果的"},
          {id: "4", name: "涉及国家事权和港澳事务不宜由地方回应的"},
          {id: "5", name: "涉及相关政策正在研究制定中不宜提前对外公布的"},
          {id: "6", name: "涉及内容比较敏感的"},
          {id: "7", name: "涉及行政机关内部事务以及履行行政管理职能形成的过程性信息的"},
          {id: "8", name: "提出议案建议的代表要求不予公开或者公开后有可能使代表或者他人受到报复伤害的"},
          {id: "9", name: "法律规定和工作要求的其他不予公开情形"},
      ],
      jointlyId: "", //联名id
      undertakeStatus: false, //显示反馈的依据，获取子流程状态
      delayVisible: false,
      hbDataSource: [], //会办数据
      minorLastTime: "",
      majorLastTime: "",
      onlyMajorLastTime:"",
      onlyMinorLastTime:"",
      fileTextList_7: [],
      fileTextList_8: [],
      fileUnRegisterList: [],
      delayForm: { delayDay: "", comment: "" },
      defaultUnit: [],
      disableSelectedRowKeys: [],
      flowId: "",
      flowName: "",
      defaultUnitHB: [],
      disableSelectedRowKeysHB: [],
      // 申请调整
      changeIfMainTitle: "修改承办类别",
      changeIfMainForm: {},
      // 联名按钮loading
      lmLoading: false,
      // 交办按钮loading
      jbLoading: false,
      // 校核按钮loading
      jhLoading: false,
      // 审核按钮loading
      shLoading: false,
      // 答复按钮loading
      sfLoading: false,
      // 经办人orgCode
      jbrTableOrgCode: "",
      zwCKyj: false, //转为参考意见的转圈


      disableSelectedRowKeylist: [], //禁止添加的用户（已添加）
      isshowLian: false,
      // 附件上传
      action: process.env.VUE_APP_BASE_API + "/api/v1/bulletin/upload",
      fileData: {
        relId: "",
        type: 0,
        encrypted: true,
      },
      visible: false,
      ids: [],
      isBanMeet: false,
      // tab 导航栏的 key 值
      tabIndex: 1,
    };
  },
// 监听record的变化
  watch: {
    record: {
      handler() {
        this.isRendered = false;
        this.$nextTick(() => {
          this.isRendered = true;
        });
      },
      deep: true,
    },
  },
  created() {
    // this.getOpinionOption();
    if (sessionStorage.getItem("USERID")) {
      this.userName = JSON.parse(sessionStorage.getItem("USERID")).username;
      console.log("userName: " + this.userName);
    }
    console.log("userName: " + this.userName);
 // 调用 getDictData 方法获取字典数据
    this.getDictData().then(data => {
      //强复制
      this.detailsForm= JSON.parse(JSON.stringify(data[0]));
      console.log("字典数据加载完成:", this.detailsForm);
    }).catch(error => {
      console.error("获取字典数据失败:", error);
    });
  },
  methods: {
  // 交办小组公开意见变化处理
  onJbxzIfPublicChange(e) {
    // 当选择"公开"时，清空"出具公开意见"的值
    if (e.target.value === '1') {
      this.resourceForm.jbxzPublic = '';
    }
  },
  // 获取字典数据
  getDictData() {
    return getDicts('sys_DF_unit_adjustment').then((response) => {
      this.detailsForm = response.data.data;
      return this.detailsForm;
    });
  },
    //送审
    shenheIn(status) {
      console.log("this.proposalId-----", this.proposalId);
      console.log("this.record.status-----", this.record.status);
      //除开代表工委需要进行二次审核的预退回外其余默认退回选中校核中节点
      if(status=="firstCheck" || status=="doAssign" || status=="doAudit" || status=="unRegister" || status=="classify"){
        this.defaultNode = 20;
      }else if(status=="assginConfirm" || status=="assginDbcConfirm"){
        this.defaultNode = 40;
      } else {
        this.defaultNode = this.record.returnNode;
      }
      this.actTypeReturn = status;
      this.procInstId = this.proposalId;
      this.flowId = this.record.status;
      this.flowName = this.record.proposalTitle;
      this.$refs.submitForCensorship.visible = true;
      console.log("来到这---2180");
      // 调用组件的异步方法 获取数据
      this.$refs.submitForCensorship.getprocessFn().then(() => {

      }).catch(error => {
        console.error('调用 getprocessFn 出错:', error);
      });
    },
    //审核驳回
    auditReturnF(){
      let form = {};
      form.proposalId = this.proposalId;
      form.comment = this.resourceForm.comment || "";
      this.sfLoading = true;
      auditReturnFalse(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success(res.data.message);
          this.visible = false;
          this.fileUnRegisterList = [];
          this.close();
          this.sfLoading = false;
        } else {
          this.$message.error(res.data.message);
          this.sfLoading = false;
        }
      });
    },
    //送审发送
    handleComplete(data) {
    },

    // 不予立案 提交闭会
    putUnRegisterBh() {
      if (this.fileUnRegisterList.length === 0 && !this.resourceForm.remarkText) {
        return this.$message.error("请上传不予立案附件或填写理由！");
      }

      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
      this.$refs["resourceForm"].validate((valid) => {
        if (valid) {
          let hostData = [];
          let meetData = [];
          let undertakeList = [];
          this.resourceForm.host.map((item) => {
            hostData.push({
              ifmain: 1,
              orgCode: item.orgCode,
              orgName: item.orgName,
              proposalId: this.proposalId,
            });
          });
          undertakeList = hostData.concat(meetData);
          let form = {};
          form.undertakeList = undertakeList;
          form.proposalId = this.proposalId;
          form.comment = this.resourceForm.remarkText;
          if (
            this.resourceForm.fuJianIds != "" &&
            this.resourceForm.fuJianIds != undefined
          ) {
            form.fjId = this.resourceForm.fuJianIds.toString();
          }
          this.sfLoading = true;
          saveUnRegisterBh(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.visible = false;
              this.fileUnRegisterList = [];
              this.close();
              this.sfLoading = false;
            } else {
              this.$message.error(res.data.message);
              this.sfLoading = false;
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },

    // 不予立案 提交
    putUnRegister() {
      if (this.fileUnRegisterList.length === 0 && !this.resourceForm.remarkText) {
        return this.$message.error("请上传不予立案附件或填写理由！");
      }

      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
        this.$refs["resourceForm"].validate((valid) => {
        if (valid) {
          let hostData = [];
          let meetData = [];
          let undertakeList = [];
          this.resourceForm.host.map((item) => {
            hostData.push({
              ifmain: 1,
              orgCode: item.orgCode,
              orgName: item.orgName,
              proposalId: this.proposalId,
            });
          });
          undertakeList = hostData.concat(meetData);
          let form = {};
          form.undertakeList = undertakeList;
          form.proposalId = this.proposalId;
          form.comment = this.resourceForm.remarkText;
          if (
            this.resourceForm.fuJianIds != "" &&
            this.resourceForm.fuJianIds != undefined
          ) {
            form.fjId = this.resourceForm.fuJianIds.toString();
          }
          this.sfLoading = true;
          saveUnRegister(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.visible = false;
              this.fileUnRegisterList = [];
              this.close();
              this.sfLoading = false;
            } else {
              this.$message.error(res.data.message);
              this.sfLoading = false;
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 清除
    handleRemove(file, fileList) {
      this.fujian.forEach((item, index) => {
        if (item.path == file.url || item.id == file.id) {
          this.fujian.splice(index, 1);
        }
      });
      this.fileUrl.forEach((item, index) => {
        if (item.url == file.url) {
          this.fileUrl.splice(index, 1);
        }
      });
      if (this.fujian.length == 0) {
        this.noticeForm.fileUrl = null;
      }
      this.$baseMessage(`删除成功`, "success");
    },
    // 上传
    uploadChange({ file, fileList }) {
      this.fileUrl = fileList;
      this.$set(this.noticeForm, "fileUrl", URL.createObjectURL(file));
      // 上传
      var wj = new FormData();
      wj.append("file", file);
      instance_2({
        method: "post",
        url: "/bulletin/upload",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        data: wj,
      })
        .then((res) => {
          var regId = res.data.data[0].newName.split(".")[0];
          this.fujian.push({
            newName: res.data.data[0].newName,
            fileName: res.data.data[0].originName,
            id: regId,
            path: res.data.data[0].path,
            type: 2,
          });
          this.fileUrl = [];
          this.fujian.forEach((item) => {
            this.fileUrl.push({
              id: item.id,
              name: item.fileName,
              path: item.path,
              url: item.path,
              uid: "-1",
            });
          });

        })
        .catch((error) => { });
    },
    // 上传大小限制
    beforeAvatarUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      // var reg = /^.+(\/pdf)$/;
      var reg = /(pdf|ofd)$/i;
      const isPDF = reg.test(file.type);
      if (!isPDF) {
        this.$message.error("上传的文件格式只能是PDF和OFD");
      }
      if (isLt20M && isPDF) return false;
    },
    // 不予立案模块显示与隐藏
    refuseLian() {
      // 点击显示不予立案模块 反之隐藏
      this.isshowLian = !this.isshowLian;
    },
    // 计算竖线的高度
    getLineHeight(index) {
      const stepContentHeight = this.$refs[`step-content-${index}`]?.offsetHeight || 0;
      console.log(stepContentHeight)
      return stepContentHeight > 0 ? stepContentHeight + 30 : 90; // 默认最小长度
    },
    // 选择意见
    changeTEXT(val) {
      // let a = val.toString().indexOf('审核通过');
      // let b = val.toString().indexOf('审核不通过')
      // if (a > -1 && b > -1) {
      //   if (a > b) {
      //     this.resourceForm.text = this.resourceForm.text.filter((i) => { return i != '审核不通过' })
      //     return
      //   } else {
      //     this.resourceForm.text = this.resourceForm.text.filter((i) => { return i != '审核通过' })
      //     return
      //   }
      // }
    },
    // 获取多选的意见类型
    getOpinionOption() {
      findOpinionOption().then((res) => {
        if (res.data.code == "200") {
          let aa = res.data.data.split('"');
          for (let i = 0; i < aa.length; i++) {
            if (i % 2 != 0) {
              this.OpinionOptionList.push(aa[i]);
            }
          }
        }
      });
    },
    // 点击行
    clickRowData(record) {
      console.log("是否点击----");
      console.log(record,"内容为-------------")
      if (record.replyComment) {
        this.replyComment = record.replyComment;
        this.iShow = true;
      }
    },
    // 校核弹窗 关闭
    cancelReturn() {
      this.returnValue = false;
      this.resourceForm.comment = "";
    },
    // 正式提交
    formallySubmit() {
      this.$router.push({
        path: "/meSuggestv/addSuggestv",
        query: {
          showUser: false,
          proposalId: this.proposalId,
        },
      });
    },
    // 取消调整
    cancelUnitModal() {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "如果关闭当前页面，将会丢失已编辑的承办单位，确认要关闭?",
        onOk: () => {
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    //关闭查看会办单位调整信息框
    cancelUnitInfoModal() {
      this.unitModalInfoVisible = false;
      this.unitModalVisibleSelectedRows = [];
      this.unitModalVisibleDataSource = [];
    },
    // 取消会办单位调整
    cancelUnitMinorModal() {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "如果关闭当前页面，将会丢失已编辑的会办单位，确认要关闭?",
        onOk: () => {
          this.unitMinorModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    //调整会办单位
    adjustMinor() {
      if (this.record.ifMoreMain) {
        return this.$message.info("该建议存在多个主办无法进行会办单位调整！");
      }

      this.unitMinorModalVisible = true;
      getUndertakeInfo(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitModalVisibleDataSource = res.data.data;
          this.oldunitModalVisibleDataSource = JSON.parse(JSON.stringify(res.data.data));
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 申请调整
    adjust(type) {
      this.$refs.adjustListEdit.modalVisible = true;
      this.$refs.adjustListEdit.proposalId = this.proposalId;
      console.log(type,"type-------------")
      if(type==0){
        this.$refs.adjustListEdit.tipMessage = '提出不参与会办的，应事先与主办单位沟通，然后线上向主办单位发起调整会办单位申请。';
      }
      if(type==1){
        this.$refs.adjustListEdit.tipMessage = '不属于本单位职责的，提供明确文件依据后可申请调整主办单位。如需增减会办单位的，简要说明原因后可提申请调整会办单位。';
      }
    },
    openAdjustModal(value) {
      if(this.record.ifMain == 1){
        this.$refs.adjustListEdit.tipMessage = '不属于本单位职责的，提供明确文件依据后可申请调整主办单位。如需增减会办单位的，简要说明原因后可提申请调整会办单位。';
      }
      if(this.record.ifMain == 0){
        this.$refs.adjustListEdit.tipMessage = '提出不参与会办的，应事先与主办单位沟通，然后线上向主办单位发起调整会办单位申请。';
      }
      this.$refs.adjustListEdit.handleAdjustTypeChange(value);
      this.$refs.adjustListEdit.selectIsShow=true;
      this.$refs.adjustListEdit.modalVisible = true;
      this.$refs.adjustListEdit.proposalId = this.proposalId;
      this.$refs.adjustListEdit.record = this.record;
      this.$refs.adjustListEdit.iniselectOptions(this.record.ifMain,this.record.undertakeStatus);
    },
    // 申请延期 保存
    adjustSubmit() {
      let form = {};
      form.comment = this.delayForm.comment;
      form.delayDay = this.delayForm.delayDay;
      form.proposalId = this.proposalId;
      this.$refs["delayForm"].validate((valid) => {
        if (valid) {
          // 申请延期
          applyDelay(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.cancelModal();
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 关闭窗口
    cancelModal() {
      this.delayVisible = false;
      this.delayForm = { delayDay: "", comment: "" };
      this.delayFileTextList = [];
    },
    // 申请延期
    postpone() {
      this.delayVisible = true;
    },
    // 处理联名
    changeJoin(status) {
      this.lmLoading = true;
      let form = {};
      form.jointlyid = this.jointlyId;
      form.state = status;
      doSignedSave(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作完成");
          this.close();
        } else {
          this.$message.error(res.data.data);
        }
        this.lmLoading = false;
      });
    },
    // 下载建议纸
    downMotion() {
      let downFile = (res) => {
        let a = window.document.createElement("a");
        let resData = URL.createObjectURL(res.data);
        a.href = resData;
        let proposalNum = this.record.proposalNum
          ? this.record.proposalNum
          : "";
        let fileName = proposalNum + this.record.proposalTitle + "建议纸.docx";
        a.download = fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(resData);
      };
      downloadProposal({ id: this.proposalId, type: 10 }).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        let errmsg = "";
        if (res.headers.errmsg) {
          errmsg = this.$str2utf8(res.headers.errmsg);
          return this.$message.error(errmsg);
        } else {
          downFile(res);
        }
      });
    },

    //重新上传正文附件 关闭
    closeFileModal() {
      this.fileVisible = false;
      this.fileTextListReset = [];
      this.delayFileTextList = [];
      this.resourceForm = {
        meetName: "",
        meet: "",
        host: "",
        hostName: "",
        adjust: "",
        adjustName: "",
        isPublic: "",
        dffs: [],
        jbrName: "",
        jbrDept: "",
        jbrJob: "",
        jbrPhone: "",
        jbrOph: "",
      };
      this.fileTextList = [];
      this.fileTextList_7 = [];
      this.fileTextList_8 = [];
      this.fileUnRegisterList = [];
    },
    //重新上传正文附件 保存
    resetSubmit() {
    if(this.fileTextListReset.length >0){
      let form = {};
      form.proposalId = this.proposalId;
      form.type = "9";
      form.encrypted = false;
      form.file = this.resourceForm.file;
      againUploadTextFile(form).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.$message.success("操作完成");
          this.closeFileModal();
        } else {
          this.$message.error(res.data.data);
        }
      });
    } else {
      this.$message.error("请确认是否已上传文件")
    }
    },
    //重新上传正文附件 删除
    handleTextRemoveReset() {
      this.resourceForm.file = "";
      this.fileTextListReset = [];
      this.$baseMessage(`删除成功`, "success");
    },

    //重新上传正文附件 上传前
    beforeUploadReset(file) {
      console.log(file, "file");
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /^.+(docx|doc)$/;
      const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      if (!isDoc) {
        this.$message.error("上传的文件格式只能是doc或docx");
      }
      if (isLt20M && isDoc) {
        this.isFileReset = true;
        return false;
      } else {
        this.isFileReset = false;
        return true;
      }
    },
    //重新上传正文附件 上传操作
    uploadChangeReset(val, typeNum) {
      if (this.isFileReset) {
        // let formData = new FormData();
        // formData.append("file", val.file);
        // formData.append("type", typeNum);
        this.resourceForm.file = "";
        this.resourceForm.file = val.file;
        this.fileTextListReset = val.fileList;
      }
    },
    // 重新上传正文附件
    resetFile() {
      // 打开上传文件窗口
      this.fileVisible = true;
    },
    // 重新交办
    resetManage() {
      if (this.unitModalVisibleDataSource.length == 0) {
        return this.$message.error("承办单位不能为空");
      }
      let form = {};
      let undertakeList = [];
      this.unitModalVisibleDataSource.map((item) => {
        undertakeList.push({
          ifmain: item.ifmain,
          orgCode: item.orgCode,
          orgName: item.orgName,
          undertakeId: item.undertakeId || "",
          proposalId: this.proposalId,
        });
      });
      form.undertakeList = undertakeList;
      form.proposalId = this.proposalId;
      form.contentType = this.record.proposalContentType;
      form.proposalType = this.record.proposalType;
      doRevise(undertakeList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 单位调整弹出框 新增 打开
    openChange() {
      this.changeIfMainVisible = true;
      this.changeIfMainTitle = "添加承办单位";
    },

    openChangeMinor() {
      this.changeIfMainMinorVisible = true;
      this.changeIfMainTitle = "添加会办单位";
    },
    // 单位调整弹出框 删除
    unitModalVisibleDel() {
      if (this.unitModalVisibleSelectedRows.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.unitModalVisibleSelectedRows.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      // 单位调整-删除逻辑
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定删除？",
        onOk: () => {
          this.unitModalVisibleDataSource =
            this.unitModalVisibleDataSource.filter(
              (item) =>
                item.orgCode != this.unitModalVisibleSelectedRows[0].orgCode
            );
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 单位调整弹出框 修改主办/会办 保存
    saveChangeIfMain() {
      if (this.changeIfMainTitle == "修改承办类别") {
        this.unitModalVisibleSelectedRows.forEach((item) => {
          this.unitModalVisibleDataSource.map((v) => {
            if (v.orgCode == item.orgCode) {
              v.ifmain = this.changeIfMainForm.ifmain;
            }
          });
        });

        this.changeIfMainVisible = false;
        this.$forceUpdate();
        delete this.changeIfMainForm.ifmain;
        this.unitModalVisibleSelectedRows = [];
      } else {
        // 新增
        this.changeIfMainForm.rows.forEach((item) => {
          item.ifmain = this.changeIfMainForm.ifmain;
          item.orgName = item.orgName;
          item.jbrName = item.orgDispname;
          item.jbrPhone = item.orgDispphone;
          item.jbrOph = item.orgDisptelephone;
        });
        let orgCodeList = this.unitModalVisibleDataSource.map(
          (item) => item.orgCode
        );
        let rowsOrgCode = this.changeIfMainForm.rows.map(
          (item) => item.orgCode
        );
        for (let index = 0; index < rowsOrgCode.length; index++) {
          const element = rowsOrgCode[index];
          if (orgCodeList.includes(element)) {
            return this.$message.error("单位已存在！");
          }
        }

        this.unitModalVisibleDataSource =
          this.unitModalVisibleDataSource.concat(this.changeIfMainForm.rows);
        this.changeIfMainForm = {};
        this.changeIfMainVisible = false;
      }
    },
    // 单位调整弹出框 修改主办/会办
    changeIfMain() {
      if (this.unitModalVisibleSelectedRows.length == 0) {
        return this.$message.error("请选择数据");
      }
      this.changeIfMainVisible = true;
      this.changeIfMainTitle = "修改承办类别";
    },
    // 单位调整弹出框 确定保存
    saveUnitModal() {
      // +判断
      let sizeData = 0;
      let huiData = 0;
      this.unitModalVisibleDataSource.forEach((item) => {
        if (item.ifmain == 1) {
          sizeData++;
        } else {
          huiData++;
        }
      });
      console.log("🤗🤗🤗, sizeData =>", sizeData);
      if (this.modalDataSource && this.modalDataSource.length == 0) {
        return this.$message.info("请添加数据");
      }
      // if (sizeData > 2) {
      //   return this.$message.info("主办单位最多只能俩个！");
      // }
      if (sizeData >= 2 && huiData > 0) {
        return this.$message.info("该建议存在多主办单位请删除其他会办单位！");
      }
      if (sizeData == 0) {
        return this.$message.info("请选择主办单位！");
      }
      let form = {};
      let undertakeList = [];
      this.unitModalVisibleDataSource.map((item) => {
        undertakeList.push({
          ifmain: item.ifmain,
          orgCode: item.orgCode,
          orgName: item.orgName,
          undertakeId: item.undertakeId || "",
          proposalId: this.proposalId,
        });
      });
      form.undertakeList = undertakeList;
      form.proposalId = this.proposalId;
      form.contentType = this.record.proposalContentType;
      form.proposalType = this.record.proposalType;

      // 首先检查数据是否有变化
      const oldData = this.oldunitModalVisibleDataSource || []; // 原有的承办单位数据
      const newData = this.unitModalVisibleDataSource || []; // 新的承办单位数据
      console.log("🤗🤗🤗, oldData =>", oldData);
      console.log("🤗🤗🤗, newData =>", newData);
      // 比较新旧数据是否相同
      const isDataUnchanged = this.compareUnitData(oldData, newData);
      console.log("🤗🤗🤗, isDataUnchanged =>", isDataUnchanged)

      // 如果数据没有变化，直接返回
      if(!isDataUnchanged) {
        this.unitModalVisible = false;
        //跳出一个弹窗 说明数据未发生变化
        this.unitModalVisible = true;
        console.log("3448行")
        return this.$message.error("请对单位进行调整后再提交！");
      }
      doRevise(undertakeList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 会办单位调整审核通过
    saveUnitMinor() {
      let form = {};
      form.proposalId = this.proposalId;
      form.undertakeId = this.selectedRowsPostpone[0].undertakeId;
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      console.log("form.undertakeId", form.undertakeId);

      doMinorSave(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

        // 会办单位调整复核通过
      saveReviseUnitMinor() {
      let form = {};
      form.proposalId = this.proposalId;
      form.undertakeId = this.selectedRowsPostpone[0].undertakeId;
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      console.log("form.undertakeId", form.undertakeId);

      doReviseReviseSave(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

      // 会办单位主动调整复核退回
      returnUnitMinorRevise() {
      let form = {};
      form.proposalId = this.proposalId;
      form.undertakeId = this.selectedRowsPostpone[0].undertakeId;
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      console.log("form.undertakeId", form.undertakeId);

      doMinorReviseReturn(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },


    // 会办单位调整审核退回
    returnUnitMinor() {
      let form = {};
      form.proposalId = this.proposalId;
      form.undertakeId = this.selectedRowsPostpone[0].undertakeId;
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      console.log("form.undertakeId", form.undertakeId);

      doMinorReturn(form).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 调整会办单位弹出框 删除
    unitModalMinorVisibleDel() {
      if (this.unitModalVisibleSelectedRows.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.unitModalVisibleSelectedRows.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      // 单位调整-删除逻辑
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定删除？",
        onOk: () => {
          this.unitModalVisibleDataSource =
            this.unitModalVisibleDataSource.filter(
              (item) =>
                item.orgCode != this.unitModalVisibleSelectedRows[0].orgCode
            );
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },

    // 调整会办单位弹出框 修改会办 保存
    saveChangeIfMainMinor() {
      // 新增
      this.changeIfMainForm.rows.forEach((item) => {
        item.ifmain = 0;
        item.orgName = item.orgName;
        item.jbrName = item.orgDispname;
        item.jbrPhone = item.orgDispphone;
        item.jbrOph = item.orgDisptelephone;
      });
      let orgCodeList = this.unitModalVisibleDataSource.map(
        (item) => item.orgCode
      );
      let rowsOrgCode = this.changeIfMainForm.rows.map(
        (item) => item.orgCode
      );
      for (let index = 0; index < rowsOrgCode.length; index++) {
        const element = rowsOrgCode[index];
        if (orgCodeList.includes(element)) {
          return this.$message.error("单位已存在！");
        }
      }

      this.unitModalVisibleDataSource =
        this.unitModalVisibleDataSource.concat(this.changeIfMainForm.rows);
      this.changeIfMainForm = {};
      this.changeIfMainMinorVisible = false;

    },
    // 调整会办单位弹出框 确定保存
    saveUnitMinorModal() {
      // +判断
      let sizeData = 0;
      let huiData = 0;
      this.unitModalVisibleDataSource.forEach((item) => {
        if (item.ifmain == 1) {
          sizeData++;
        } else {
          huiData++;
        }
      });
      if (this.modalDataSource && this.modalDataSource.length == 0) {
        return this.$message.info("请添加数据");
      }
      // if (sizeData > 2) {
      //   return this.$message.info("主办单位最多只能俩个！");
      // }
      if (sizeData >= 2 && huiData > 0) {
        return this.$message.info("该建议存在多主办单位请删除其他会办单位！");
      }
      if (sizeData == 0) {
        return this.$message.info("请选择主办单位！");
      }
      let form = {};
      let undertakeList = [];
      this.unitModalVisibleDataSource.map((item) => {
        undertakeList.push({
          ifmain: item.ifmain,
          orgCode: item.orgCode,
          orgName: item.orgName,
          undertakeId: item.undertakeId || "",
          proposalId: this.proposalId,
        });
      });

      // 首先检查数据是否有变化
      const oldData = this.oldunitModalVisibleDataSource || []; // 原有的会办单位数据
      const newData = this.unitModalVisibleDataSource || []; // 新的会办单位数据
      // 比较新旧数据是否相同
      const isDataUnchanged = this.compareUnitData(oldData, newData);
      // 如果数据没有变化，直接返回
      if(!isDataUnchanged) {
        this.unitMinorModalVisible = false;
        //跳出一个弹窗 说明数据未发生变化
        this.unitMinorModalVisible = true;
        return this.$message.error("请对单位进行调整后再提交！");
      }

      form.undertakeList = undertakeList;
      form.proposalId = this.proposalId;
      form.contentType = this.record.proposalContentType;
      form.proposalType = this.record.proposalType;
      saveMinor(undertakeList).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("操作成功");
          this.unitMinorModalVisible = false;
          this.unitModalVisibleSelectedRows = [];
          this.unitModalVisibleDataSource = [];
          // 关闭
          this.close();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 新增一个比较数据的方法 true代表有变化
    compareUnitData(oldData, newData) {
      // 如果长度不同,说明数据有变化
      if(oldData.length !== newData.length) {
        return true;
      }
        // 对两个数组进行排序
      const sortedOldData = oldData.slice().sort((a, b) => a.orgCode.localeCompare(b.orgCode));
      const sortedNewData = newData.slice().sort((a, b) => a.orgCode.localeCompare(b.orgCode));
      //循环对比如果单位编码，承办性质不一样说明有变化，排除顺序问题
      for(let i = 0; i < sortedOldData.length; i++) {
        if(sortedOldData[i].orgCode !== sortedNewData[i].orgCode || sortedOldData[i].ifmain !== sortedNewData[i].ifmain) {
          return true;
        }
      }
      return false;
    },
    // 下载
    downFileDataDf(itemdf, type) {
      if (this.mediaType === "其他建议") {
        this.$message.error("抱歉，你没有该权限！");
      } else {
        let form = {};
        let attName = "";
        let attSuffix = "";
        if (type === 6) {
          form.relId = itemdf.undertakeId || itemdf.attId; //_______attId
          form.type = type; //应该是6待测试
          attName = itemdf.attName;
          attSuffix = itemdf.attSuffix;
        } else {
          itemdf.fujianList.forEach((item) => {
            if (item.attType === type) {
              form.relId = item.relId;
              form.type = item.attType;
              attName = item.attName;
              attSuffix = item.attSuffix;
            }
          });
        }
        console.log("🤗🤗🤗, form =>", form);
        downFile(form).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `${attName}.${attSuffix}`;

          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          window.URL.revokeObjectURL(res);
        });
      }
    },

    //下载不予立案附件
    downFileDataUnder(step, type) {
      if (this.mediaType === "其他建议") {
        this.$message.error("抱歉，你没有该权限！");
      } else {
        let form = {};
        let attName = "";
        let attSuffix = "";
        step.fujianList.forEach((item) => {
            if (item.attType === type) {
              form.relId = item.relId;
              form.type = item.attType;
              attName = item.attName;
              attSuffix = item.attSuffix;
            }
          });
        console.log("🤗🤗🤗, form =>", form);
        downFile(form).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          a.download = `${attName}.${attSuffix}`;

          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
          window.URL.revokeObjectURL(res);
        });
      }
    },


    //答复函下载
    downFileDataDfh(record) {
      console.log("🤗🤗🤗2458-------------- record =>", record)
      let form = { relIdList: [] };
      let name = "";
      form.relId = record.oaId;
      form.type = 40;
      if (record.fujianList.length > 1) {
        name = "附件" + ".zip"
      } else {
        name = record.fujianList[0].attName + "." + record.fujianList[0].attSuffix;
      }
      console.log("name--------------", name)
      console.log("🤗🤗🤗, form =>", form);
      downDfhFile(form).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = name;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },


    // 下载附件
    downFileData(type, relId) {
      let form = { relIdList: [] };
      if (type == 2 || type == 32) {
        form.relId = relId;
      } else {
        form.relId = this.proposalId;
      }
      let name = "";
      if (type == "1") {
        name = "附件.zip";
      }
      if (type == "2") {
        name = "答复附件.pdf";
      }
      if (type == "9") {
        name = "正文附件.docx";
      }
      if (type == "32") {
        name = "示例文件.pdf"
      }
      form.type = type;
      console.log("🤗🤗🤗, form =>", form);
      downFile(form).then((res) => {
        console.log(Object.getOwnPropertyNames(res.headers));
        console.log(
          "🤗🤗🤗, res =>",
          res,
          "headers",
          res.headers,
          res.headers?.["content-disposition"]
        );
        // let filename = res.headers?.['content-disposition']?.split(';')[1].split("filename=")[1];
        // if (type == "1" && filename) {
        //   let hz = filename?.substr(filename.lastIndexOf('.') + 1);
        //   name = "附件." + hz;
        // }
        if (type == "1") {
          let fjlis = this.record.attachmentList.filter((i) => i.attType == 1);
          console.log(fjlis, "fjlis");
          if (fjlis.length == 1) {
            name = "附件." + fjlis[0].attSuffix;
          }
        }

        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `${this.record.proposalTitle}${name}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(res);
      });
    },
    // 查看正文
    lookWord() {
      console.log("🤗🤗🤗, mediaType =>", this.mediaType);
      if (this.mediaType === "其他建议") {
        // 获取会议时间
        isMeetingTime({ meetingId: "" }).then((res) => {
          if (res.data.data == "当前时间不在大会期间") {
            this.$message.error("非大会期间不能查看正文内容!");
          } else {
            // 大会期间
            seeText(this.proposalId).then((res) => {
              if (res.data.code == 200 && res.data.data) {
                let url = this.GLOBAL.basePath_3 + "/" + res.data.data.url;
                window.open(url);
              } else {
                this.$message.error("请求出错!");
              }
            });
          }
        });
      } else {
        seeText(this.proposalId).then((res) => {
          if (res.data.code == 200 && res.data.data) {
            let url = this.GLOBAL.basePath_3 + "/" + res.data.data.url;
            window.open(url);
          } else {
            this.$message.error("请求出错!");
          }
        });
      }
    },
    // 打开反馈
    openEvaluationModal() {
      this.$refs.evaluationModal.proposalId = this.proposalId;
      this.$refs.evaluationModal.activeTab = "1";
      this.$refs.evaluationModal.getFeedUndertakeList();
      this.$refs.evaluationModal.modalVisible = true;
      console.log(this.record.undertakeStatus)
      console.log(this.record.headPer)
    },

    // 打开单位调整弹出框
    openUnitModalVisible() {
      this.unitModalVisible = true;
      getUndertakeInfo(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitModalVisibleDataSource = res.data.data;
          this.oldunitModalVisibleDataSource = JSON.parse(JSON.stringify(res.data.data));
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    openUnitModalInfoVisible() {
      this.unitModalInfoVisible = true;
      getUndertakeInfo(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitModalVisibleDataSource = res.data.data;
          this.oldunitModalVisibleDataSource = JSON.parse(JSON.stringify(res.data.data));
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 打开复核环节承办列表弹出框
    openUnitReviseModalInfoVisible() {
      this.unitModalInfoVisible = true;
      getReviseUndertakeInfo(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitModalVisibleDataSource = res.data.data;
          this.oldunitModalVisibleDataSource = JSON.parse(JSON.stringify(res.data.data));
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 维持原交办
    maintainAPrimaryOffice() {
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.visiblePostpone = true;
      this.modalTitle = "维持原交办";
    },
    // 处理延期/调整/维持原交办 -弹窗保存
    submitDelay() {
      let form = {};
      form.comment = this.resourceForm.comment || "";
      form.undertakeId = this.selectedRowsPostpone[0].undertakeId;
      if (this.modalTitle == "处理延期") {
        console.log("this.resourceForm.delayDay", this.resourceForm.delayDay);
        form.delayDay = this.resourceForm.delayDay;
        doDelay(form).then((res) => {
          if (res.data.code == 200) {
            this.$message.success("操作成功");
            this.resourceForm.comment = "";
            this.getASubflow();
            this.visiblePostpone = false;
          } else {
            this.$message.error(res.data.message);
          }
        });
      } else if (this.modalTitle == "维持原交办") {
        if (this.keepType == "revise") {
          keepRevise(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.resourceForm.comment = "";
              this.getAdjust();
              this.visiblePostpone = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        }
        if (this.keepType == "delay") {
          keepDelay(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.getASubflow();
              this.visiblePostpone = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        }
      }
      this.$nextTick(() => {
        this.visible = false;
      });
    },
    // 处理延期-事件
    treatmentDelay() {
      if (this.selectedRowsPostpone.length == 0) {
        return this.$message.error("请选择数据");
      }
      if (this.selectedRowsPostpone.length > 1) {
        return this.$message.error("只能选择一条数据");
      }
      this.visiblePostpone = true;
      this.modalTitle = "处理延期";
      // this.resourceForm.delayDay = "";
      // this.resourceForm.delayDay = Number(
      //   this.selectedRowsPostpone[0].delayTimeTo
      // );
      this.$set(
        this.resourceForm,
        "delayDay",
        this.selectedRowsPostpone[0].delayTimeTo
      );
    },
    // 处理延期-获取子流程 父组件调用
    getASubflow() {
      getDelayUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "delay";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 处理调整-获取子流程 父组件调用
    getAdjust() {
      getReviseUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "revise";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 处理调整-获取子流程 父组件调用
    getAdjustMinor() {
      console.log("------------进来这里")
      console.log("scenesStatus---", this.scenesStatus)
      getAdjustMinorUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "reviseMinor";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 获取复核调整会办单位流程信息（主办单位申请审核之后还需要后进行复核）
    getReviseReview() {
      console.log("------------进来这里")
      console.log("scenesStatus---", this.scenesStatus)
      getReviseReviseUndertakeListById(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.keepType = "reviseReview";
          this.dataSourcePostpone = res.data.data;
          this.undertakeId = res.data.data.undertakeId;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 上传之前
    beforeUpload(file, fileList) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10M!");
      }
      // var reg = /^.+(docx|doc)$/;
      // const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      // if (!isDoc) {
      //   this.$message.error("上传的文件格式只能是doc或docx");
      //   return;
      // }
      return false;
    },
    // 不予立案 上传删除
    handleRegisterRemove(file) {
      this.resourceForm.fuJianIds = [];
      this.fileUnRegisterList = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 答复 上传删除
    handleTextRemove(file) {
      if(this.resourceForm.fuJianIds[0]){
        console.log("***********",JSON.stringify(this.resourceForm.fuJianIds[0]))
        deleteByAttId(this.resourceForm.fuJianIds[0]).then((res) => {
          if (res.data.code == 200) {
            this.$baseMessage(`删除成功`, "success");
          } else {
            this.$message.error(res.data.message);
          }
        });
      }
      this.resourceForm.fuJianIds = [];
      this.fileTextList = [];
      // this.$baseMessage(`删除成功`, "success");
    },
    // 无需见面沟通证据 上传删除
    handleTextRemove7(file) {
      this.resourceForm.forumFile = [];
      this.fileTextList_7 = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 座谈会调研 上传删除
    handleTextRemove8(file) {
      this.resourceForm.communFile = [];
      this.fileTextList_8 = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 上传操作
    uploadChange(val, type) {
      // 1、议案附件2、回复的附件3、代表大会议附件
      // 4、常委会议附件5、市政府(两院)宙议附件6
      // 承单位退回附件7、代表无需沟通证据8、座
      // 谈会证据9、建议正文10、建议纸
      //不予立案附件  50
      if (val.file.status == "removed") return;
      let formData = new FormData();
      formData.append("file", val.file);
      formData.append("type", type);
      fileUpload(formData).then((res) => {
        if (type == "2") {
          // 附件
          this.resourceForm.fuJianIds = [];
          this.resourceForm.fuJianIds.push(res.data.data[0].attId);
          this.fileTextList = val.fileList;
        } else if (type == "7") {
          this.resourceForm.forumFile = [];
          this.resourceForm.forumFile.push(res.data.data[0].attId);
          this.fileTextList_7 = val.fileList;
        } else if (type == "8") {
          this.resourceForm.communFile = [];
          this.resourceForm.communFile.push(res.data.data[0].attId);
          this.fileTextList_8 = val.fileList;
        }else if (type == "50") {
          this.resourceForm.fuJianIds = [];
          this.resourceForm.fuJianIds.push(res.data.data[0].attId);
          this.fileUnRegisterList = val.fileList;
        }
      });
    },
    changeorgEvaluation(e) {
      this.resourceForm.orgEvaluation = e;
      this.$forceUpdate();
    },
    // 答复
    replyData(type, isXLW) {
      if (!this.fileTextList || this.fileTextList.length == 0) {
        return this.$message.error("请上传答复附件！");
      }
      if (
        (this.record.ifMain == 1 || isXLW) &&
        this.resourceForm.dffs.includes("3") &&
        (!this.fileTextList_7 || this.fileTextList_7.length == 0)
      ) {
        return this.$message.error("请上传代表无需沟通证据！");
      }
      if (!this.resourceForm.dffs.includes("3")) {
        this.resourceForm.forumFile = [];
        this.fileTextList_7 = [];
      }
      if (!this.resourceForm.dffs.includes("2")) {
        this.resourceForm.communFile = [];
        this.fileTextList_8 = [];
      }
      let forumFile = "";
      let communFile = "";
      this.$refs["resourceForm"].validate((valid) => {
        if (valid) {
          let jbrData = {};
          jbrData.jbrName = this.resourceForm.jbrName;
          jbrData.jbrDept = this.resourceForm.jbrDept;
          jbrData.jbrJob = this.resourceForm.jbrJob;
          jbrData.jbrOph = this.resourceForm.jbrOph;
          jbrData.jbrPhone = this.resourceForm.jbrPhone;
          jbrData.proposalId = this.proposalId;

          //先更新经办人信息
          updateJbr(jbrData).then((res) => {
            this.sfLoading = false;
            if (res.data.code != 200) {
              this.$message.error(res.data.message);
              return false;
            }
          });

          if (
            this.resourceForm.forumFile &&
            this.resourceForm.forumFile.length > 0
          ) {
            forumFile = this.resourceForm.forumFile.toString();
          }
          if (
            this.resourceForm.communFile &&
            this.resourceForm.communFile.length > 0
          ) {
            communFile = this.resourceForm.communFile.toString();
          }
          if (this.resourceForm.orgEvaluation > 10) {
            this.$message.error("建议内容质量评分分数不在评分范围内,请修改！");
            return;
          }
          let form = {};
          // form.dfr = this.userData.username;
          // form.dfrId = this.userData.userId;
          form.actType = type;
          form.dflb = this.resourceForm.dflb;
          form.dffs = this.resourceForm.dffs.toString();
          form.isPublic = this.resourceForm.isPublic;
          form.remark = this.resourceForm.remark;
          form.remarkText = this.resourceForm.remarkText;
          form.xtpfList = this.hbDataSource;
          form.dfId = this.resourceForm.dfId;
          form.orgEvaluation = this.resourceForm.orgEvaluation; //建议内容质量评分
          if (
            this.resourceForm.fuJianIds != "" &&
            this.resourceForm.fuJianIds != undefined
          ) {
            form.fuJianIds = this.resourceForm.fuJianIds.toString();
          }
          if (communFile != "" && communFile != undefined) {
            form.fuJianIds += "," + communFile;
          }
          if (forumFile != "" && forumFile != undefined) {
            form.fuJianIds += "," + forumFile;
          }
          this.sfLoading = true;
          doReply(form, this.proposalId).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.visible = false;
              this.fileTextList = [];
              this.close();
              this.sfLoading = false;
            } else {
              this.$message.error(res.data.message);
              this.sfLoading = false;
            }
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 重新生成正文
    // regenerateText() {
    //   againProduce(this.proposalId).then((res) => {
    //     console.log("重新生成正文🤗🤗🤗, res =>", res);
    //     if (res.data.code == 200) {
    //       this.$message.success(res.data.message);
    //     } else {
    //       this.$message.error(res.data.message);
    //     }
    //   });
    // },
    // 删除提出代表
    delJointlyList() {
      if (this.deputationSelectedRows.length == 0) {
        return this.$message.error("请选择数据！");
      }
      // 使用 this.$confirm 弹出确认对话框
      this.$confirm({
        title: '确认删除',
        content: '您确定要删除选中的数据吗？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          let jointlyIdsList = [];
          var allifpublic = false; // 是否为领衔代表
          this.deputationSelectedRows.map((item) => {
            if (item.ifpublic != "1") {
              jointlyIdsList.push({ jointlyIds: item.jointlyId });
            } else {
              allifpublic = true;
            }
          });

          delJointly(jointlyIdsList).then((res) => {
            if (res.data.code == 200) {
              if (allifpublic) {
                this.$message.success("领衔代表不能被删除");
              } else {
                this.$message.success(res.data.message);
              }

              this.deputationSelectedRows = [];
              this.getJointlyListData();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info('已取消删除');
        }
      });
    },
    // 接受提出代表
    getJoinTable(rows) {
      let alreadyDBli = []; //找出已添加用户数组
      let addlist = []; //可添加的代表
      let list = this.deputationDataSource.map((i) => i.operId);
      alreadyDBli = rows.filter((item) => list.includes(item.userId));
      addlist = rows.filter((item) => !list.includes(item.userId));
      // console.log(
      //   alreadyDBli,
      //   "alreadyDBli",
      //   list,
      //   "1",
      //   this.deputationDataSource
      // );
      // console.log(rows, "rows");
      // console.log(addlist, "addlist");
      // 判断当前选择人的类型
      if (this.perType == "提出") {
        let form = {
          operids: addlist.map((item) => item.userId).toString(),
          proposalId: this.proposalId,
        };
        // 新增
        addJointly(form).then((res) => {
          if (res.data.code == 200) {
            if (alreadyDBli.length != 0) {
              // 提示已经添加不可添加
              let nameli = alreadyDBli.map((i) => i.name);
              this.$message.success(`${nameli} 代表已经添加过了`);
            } else {
              this.$message.success(res.data.message);
            }
            // this.$message.success(res.data.message);
            this.getJointlyListData();
            this.disableSelectedRowKeylist = [];
          } else {
            this.$message.error(res.data.message);
          }
        });
      } else {
        console.log("🤗🤗🤗 接受提出代表-答复, rows =>", rows);
        this.resourceForm = Object.assign({}, this.resourceForm, rows[0]);
      }
    },
    // 打开提出代表进行新增/答复选择经办人
    showUserTable(type) {
      this.perType = type;
      // this.disableSelectedRowKeylist = this.deputationDataSource.map((i) => i.userId);
      this.$refs.commonUserTable.jointTableVisible = true;
    },
    // 打开选择经办人
    showJbrTable(form) {
      this.$refs.jbrTable.jbrTableVisible = true;
      // this.$refs.jbrTable.queryForm.jbrName = "";
      // this.$refs.jbrTable.queryForm.jbrPhone = "";
      this.jbrTableOrgCode = form.orgCode;
      // this.$refs.jbrTable.showDesc();
    },
    // 接受提出代表
    getJbrTable(row) {
      this.resourceForm.jbrName = row.jbrName;
      this.resourceForm.jbrDept = row.jbrDept;
      this.resourceForm.jbrJob = row.jbrJob;
      this.resourceForm.jbrOph = row.jbrOph;
      this.resourceForm.jbrPhone = row.jbrPhone;
    },
    changeOnTab(key){
      this.tabsKey = key;
    },
    // tab点击
    changeTab(key) {
      console.log("tab点击", key);
      this.tabIndex = key;
      // 获取提出代表
      // if (key == "3") {
      // this.getJointlyListData();
      // }
      // 流程跟踪
      // if (key == "5") {
      // this.getLocalHistoryData();
      // }
      // 获取议案建议的承办单位
      // if (key == "4") {
      // this.getProposalUndertakesData();
      // }
      // 获取反馈答复列表
      // if (key == "2") {
      // this.feedbackReply();
      // }
    },
    // 获取反馈答复列表、
    feedbackReply() {
      getUndertakeDfAndFeedbackList(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.unitDataSource = [];
          this.majorLastTime = "";
          this.minorLastTime = "";
          this.majorUndertakeDfList = [];
          this.minorUndertakeDfList = [];

          if (res.data.data != undefined && res.data.data.length > 0) {
            res.data.data.forEach((item) => {
              this.unitDataSource.push(item);
              // 主办单位
              if (item.ifmain == 1) {
                // 答复反馈tab 显示答复方式
                if (item.dfList) {
                  this.record.dffs = item.dfList[0].dffs;
                }
                this.majorLastTime = item.handleLastTime;
                this.majorUndertakeDfList.push(item);
              } else {
                if (
                  this.minorLastTime == "" ||
                  (this.minorLastTime != "" &&
                    moment(this.minorLastTime).valueOf() <
                    moment(item.handleLastTime).valueOf())
                ) {
                  this.minorLastTime = item.handleLastTime;
                }
                this.minorUndertakeDfList.push(item);
              }
            });
          }
        } else {
        }
        this.$forceUpdate();
      });
    },
    // feedbackReply() {
    //   getUndertakeDfAndFeedbackList(this.proposalId).then((res) => {
    //     if (res.data.code == 200) {
    //       this.unitDataSource = [];
    //       this.majorLastTime = "";
    //       this.minorLastTime = "";
    //       this.onlyMajorLastTime = "";
    //       this.onlyMinorLastTime = "";
    //       this.majorUndertakeDfList = [];
    //       this.minorUndertakeDfList = [];
    //
    //       if (res.data.data != undefined && res.data.data.length > 0) {
    //         console.log("res.data.data", res.data.data);
    //         res.data.data.forEach((item) => {
    //           this.unitDataSource.push(item);
    //           // 主办单位
    //           if (item.ifmain == 1) {
    //             // 答复反馈tab 显示答复方式
    //             if (item.dfList) {
    //               this.record.dffs = item.dfList[0].dffs;
    //             }
    //             this.majorLastTime = item.handleLastTime;
    //             console.log("userData.logDeptId",this.userData.logDeptId)
    //             console.log("item.orgCode",item.orgCode)
    //             if(this.userData.logDeptId.includes(item.orgCode)){
    //               this.onlyMajorLastTime = item.handleLastTime;
    //             }
    //             this.majorUndertakeDfList.push(item);
    //           } else {
    //             if (
    //               this.minorLastTime == "" ||
    //               (this.minorLastTime != "" &&
    //                 moment(this.minorLastTime).valueOf() <
    //                 moment(item.handleLastTime).valueOf())
    //             ) {
    //               this.minorLastTime = item.handleLastTime;
    //             }
    //             console.log("userData.logDeptId1",this.userData.logDeptId)
    //             console.log("item.orgCode1",item.orgCode)
    //             if(this.userData.logDeptId.includes(item.orgCode)){
    //             console.log("我是会办时间",item.handleLastTime)
    //               this.onlyMajorLastTime = item.handleLastTime;
    //             }
    //             this.minorUndertakeDfList.push(item);
    //           }
    //         });
    //         console.log("this.unitDataSource", this.unitDataSource);
    //         console.log("this.minorUndertakeDfList", this.minorUndertakeDfList);
    //       }
    //     } else {
    //     }
    //     this.$forceUpdate();
    //   });
    // },
    // 获取办理跟踪列表
    getLocalHistoryData() {
      getLocalHistory(this.proposalId).then((res) => {
        // console.log("获取办理跟踪列表🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.processDataSource = res.data.data;
          console.log(this.processDataSource)
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 获取流程节点数据
    getFlowNodeData(){
      if(this.record.status==41 || this.record.status==45){
        getAllFlowNodeOther(this.proposalId).then((res) => {
          if (res.data.code == 200) {
            this.flowNodeDataSource = res.data.data;
            console.log("this.record.status---3488----", this.record.status);
            const index = this.flowNodeDataSource.findIndex(item => item.suggStatus === this.record.status)
            console.log(index);

            if(this.record.status==25 || this.record.status==35){
              this.current = 4;
            } else {
              this.current = index;
            }
          }
        });
      }else {
        getAllFlowNode(this.proposalId).then((res) => {
          if (res.data.code == 200) {
            this.flowNodeDataSource = res.data.data;
            console.log("this.record.status---3488----", this.record.status);
            const index = this.flowNodeDataSource.findIndex(item => item.suggStatus === this.record.status)
            console.log(index);
            console.log(this.flowNodeDataSource[index],"---------index----");

            //领导审定卡环节
            // if(this.flowNodeDataSource[index] && this.flowNodeDataSource[index].showDec ==='领导审定'){
            //   this.current = 6;
            // }else
            if(this.record.status==42 || this.record.status==43){
              console.log("进来了吗");
              this.current = 5;
            }else
            if (this.record.status == 25 || this.record.status==35) {
              let node = this.flowNodeDataSource.find(item => item.showDec === '复审')
              console.log(node,"---------node----");
              this.current = node.sort;
            } else {
              this.current = index;
            }
            console.log(this.flowNodeDataSource)
          } else {
            this.$message.error(res.data.message);
          }
        });
      }
    },

    // 获取答复函数据
    getDfhDataList() {
      getDfhData(this.proposalId).then((res) => {
        // console.log("获取办理跟踪列表🤗🤗🤗, res =>", res);
        if (res.data.code == 200) {
          this.dfhDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 获取提出代表
    getJointlyListData() {
      getJointlyList(this.proposalId).then((res) => {
        if (res.data.code == 200) {
          this.deputationDataSource = res.data.data;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    //下载示例文件
    downLoadFile() {

    },
    // 提交校核 打开
    openModal(status) {
      this.returnValue = true;
      if (status == "pass") {
        this.returnValueTitle = "校核意见";
      } else {
        this.returnValueTitle = "退回意见";
      }
    },

    //闭会期间领导审定
    leaderCheckBh(status) {
      this.$refs["resourceForm"].validate((valid)=>{
        if (valid) {
          let form = {};
          form.proposalIds = this.proposalId;
          form.actType = status;
          form.xlwCheck = this.resourceForm.xlwCheck;
          form.subLeaderCheck = this.resourceForm.subLeaderCheck;
          form.leaderCheck = this.resourceForm.leaderCheck;
          form.xlwCheckComment = this.resourceForm.xlwCheckComment || "";
          form.subLeaderComment = this.resourceForm.subLeaderComment || "";
          form.leaderComment = this.resourceForm.leaderComment || "";
          leaderCheckSug(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment:"",
                xlwCheckComment: "",
                subLeaderComment: "",
                leaderComment: "",
              };
              this.returnValue = false;
              this.jhLoading = false;
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        }else {
          this.$message.error("请补充完数据！");
        }
      });
    },

    // 不予立案审核
    submitSug(status) {
      this.$refs["resourceForm"].validate((valid)=>{
        if (valid) {
          let form = {};
          form.proposalIds = this.proposalId;
          form.actType = status;
          form.zwSug = this.resourceForm.zwSug;
          form.dbSug = this.resourceForm.dbSug;
          form.dbcomment = this.resourceForm.dbComment || "";
          form.zwcomment = this.resourceForm.zwComment || "";
          form.zwReason = this.resourceForm.zwReason || "";
          form.dbReason = this.resourceForm.dbReason || "";
          updateSug(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment:"",
                dbcomment: "",
                zwcomment: "",
                dbReason: "",
                zwReason: "",
              };
              this.returnValue = false;
              this.jhLoading = false;
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        }else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    //代表工委确认不予立案
    submitToUnRegister(status) {
      let form = {};
      form.proposalIds = this.proposalId;
      form.comment = this.resourceForm.comment || "";
      form.actType = status;

      //提交校核 通过
      let content = "";
      if (status == "pass") {
        content = "审核通过后建议转供参考。";
      } else {
        content = "是否确定退回？";
      }
      //提交校核 退回
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: content,
        onOk: () => {
          this.jhLoading = true;
          doUnRegister(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment: "",
              };
              this.returnValue = false;
              this.jhLoading = false;
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },

    //代表工委确认不予立案闭会
    submitToUnRegisterBh(status) {
      this.$refs["resourceForm"].validate((valid)=>{
          if (valid) {
            let form = {};
            form.proposalIds = this.proposalId;
            form.comment = this.resourceForm.comment || "";
            form.actType = status;
            form.zwSug = this.resourceForm.zwSug;
            form.dbSug = this.resourceForm.dbSug;
            form.dbcomment = this.resourceForm.dbComment || "";
            form.zwcomment = this.resourceForm.zwComment || "";
            form.zwReason = this.resourceForm.zwReason || "";
            form.dbReason = this.resourceForm.dbReason || "";

            //提交校核 通过
            let content = "";
            if (status == "pass") {
              content = "审核通过后建议转供参考。";
            } else {
              content = "是否确定退回？";
            }
            //提交校核 退回
            this.$confirm({
              cancelText: "取消",
              okType: "danger",
              okText: "确定",
              title: "温馨提示",
              content: content,
              onOk: () => {
                this.jhLoading = true;
                doUnRegisterBh(form).then((res) => {
                  if (res.data.code == 200) {
                    this.$message.success(res.data.message);
                    this.resourceForm = {
                      meetName: "",
                      meet: "",
                      host: "",
                      hostName: "",
                      comment: "",
                      dbcomment: "",
                      zwcomment: "",
                      dbReason: "",
                      zwReason: "",
                    };
                    this.returnValue = false;
                    this.jhLoading = false;
                    this.close();
                  } else {
                    this.$message.error(res.data.message);
                  }
                });
              },
              onCancel: () => {
                this.$message.info("您已取消操作！");
              },
            });
          }else {
            this.$message.error("请补充完数据！");
          }
      });
    },

    //分办小组组长二次确认分办内容
    submitAssignConfirm(status) {
      let form = {};
      form.proposalIds = this.proposalId;
      form.comment = this.resourceForm.comment || "";
      //提交校核 通过
      let content = "";
      if (status == "pass") {
        content = "确认分办通过";
      }
      //提交校核 退回
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: content,
        onOk: () => {
          this.jhLoading = true;
          AssignConfirm(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment: "",
              };
              this.returnValue = false;
              this.jhLoading = false;
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    //分办组组长二次确认
    submitAssignDbcConfirm(status) {
      // 表单验证
      this.$refs.resourceForm.validate(valid => {
        if (!valid) {
          this.$message.error('请完善必填信息');
          return false;
        }

        let form = {};
        form.proposalIds = this.proposalId;
        form.comment = this.resourceForm.comment || "";
        form.jbxzIfPublic = this.resourceForm.jbxzIfPublic; // 交办小组公开意见
        form.jbxzPublic = this.resourceForm.jbxzPublic; // 出具公开意见

        //提交校核 通过
        let content = "";
        if (status == "pass") {
          content = "确认分办通过";
        }
        //提交校核 退回
        this.$confirm({
          cancelText: "取消",
          okType: "danger",
          okText: "确定",
          title: "温馨提示",
          content: content,
          onOk: () => {
            this.jhLoading = true;
            AssignDbcConfirm(form).then((res) => {
              if (res.data.code == 200) {
                this.$message.success(res.data.message);
                this.resourceForm = {
                  meetName: "",
                  meet: "",
                  host: "",
                  hostName: "",
                  comment: "",
                  jbxzIfPublic: "",
                  jbxzPublic: ""
                };
                this.returnValue = false;
                this.jhLoading = false;
                this.close();
              } else {
                this.$message.error(res.data.message);
              }
            });
          },
          onCancel: () => {
            this.$message.info("您已取消操作！");
          },
        });
      });
    },

    //分办小组组长二次确认分办内容
    submitXlwConfirm(status) {
      let form = {};
      form.proposalIds = this.proposalId;
      form.comment = this.resourceForm.comment || "";
      //提交校核 通过
      let content = "";
      if (status == "pass") {
        content = "确认签字";
      }
      //提交校核 退回
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: content,
        onOk: () => {
          this.jhLoading = true;
          XlwConfirm(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment: "",
              };
              this.returnValue = false;
              this.jhLoading = false;
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },

    //分办小组组长二次确认分办内容
    submitDbcConfirm(status) {
      let form = {};
      form.proposalIds = this.proposalId;
      form.comment = this.resourceForm.comment || "";
      //提交校核 通过
      let content = "";
      if (status == "pass") {
        content = "确认签字";
      }
      //提交校核 退回
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: content,
        onOk: () => {
          this.jhLoading = true;
          DbcConfirm(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment: "",
              };
              this.returnValue = false;
              this.jhLoading = false;
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },

    submitBbConfirm(status) {
      let form = {};
      form.proposalIds = this.proposalId;
      form.comment = this.resourceForm.comment || "";
      //提交校核 通过
      let content = "";
      if (status == "pass") {
        content = "确认签字";
      }
      //提交校核 退回
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: content,
        onOk: () => {
          this.jhLoading = true;
          BbConfirm(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment: "",
              };
              this.returnValue = false;
              this.jhLoading = false;
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },

    // 提交校核
    submitTheSubject(status) {
      let form = {};

      console.log(this.resourceForm);
      if(this.resourceForm.comment==" 其他" && !this.resourceForm.qt){
        this.$message.error("请填写其他原因");
        return;
      }

      this.returnValue = true;
      if (status == "pass") {
        this.returnValueTitle = "校核意见";
      } else {
        this.returnValueTitle = "退回意见";
      }
      form.proposalIds = this.proposalId;
      if (this.returnValueTitle == "退回意见") {
        form.actType = "return";
      } else {
        form.actType = "pass";
      }

      let TEXT = "";
      let isQT = false;
      let m = 1;


      console.log(this.resourceForm.text + "----------2884");
      if (this.resourceForm.text != null && this.resourceForm.text.length > 0) {
        console.log(this.resourceForm.text);
        //  不显示序号 20241219
        this.resourceForm.text.map((i, n) => {
          if (i != "其他") {
             TEXT ? (TEXT +=  i + "  ") : (TEXT = i + "  ");
          }
          if (i == "其他") {
            isQT = true;
          }
        });
      }


      this.resourceForm.comment =
        isQT && this.resourceForm.qt
          ? TEXT  + " 其他 原因：" + this.resourceForm.qt
          : isQT
          ? TEXT  + " 其他"
          : TEXT;
      console.log(this.resourceForm, "this.resourceForm");

      form.comment = this.resourceForm.comment || "";

      //提交校核 通过
      let content = "";
      if (this.returnValueTitle == "校核意见") {
        content = "请确认是否已按照文件格式要求调整建议正文附件的文件格式并重新上传？若已调整文件格式点击确定即校核通过。";
      } else {
        content = "是否确定退回？";
      }
      //提交校核 退回
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: content,
        onOk: () => {
          this.jhLoading = true;
          doCheck(form).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                comment: "",
              };
              this.returnValue = false;
              this.jhLoading = false;
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    //提交分类
    saveContentSubject(status) {
      if (!this.resourceForm.contentType) {
        this.$message.info("请选择内容分类");
        return
      }
      this.resourceForm.proposalIds = this.proposalId;
      let TEXT = null;
      let isQT = false;
      let m = 1;
      console.log(this.resourceForm.text + "----------2955");
      if (this.resourceForm.text != null && this.resourceForm.text.length > 0) {
        console.log(this.resourceForm.text);

        this.resourceForm.text.map((i, n) => {
          if (i != "其他") {
            TEXT ? (TEXT += m + "." + i + "  ") : (TEXT = "1." + i + "  ");
            m++;
          }
          if (i == "其他") {
            isQT = true;
          }
        });
      }

      this.resourceForm.comment =
        isQT && this.resourceForm.qt
          ? TEXT + m + ". 其他 原因：" + this.resourceForm.qt
          : isQT
            ? TEXT + m + ". 其他"
            : TEXT;
      console.log(this.resourceForm, "this.resourceForm");
      let tips = "pass" == status ? "是否确认分类？" : "是否确定退回？";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: tips,
        onOk: () => {
          this.shLoading = true;
          let dataForm = { ...this.resourceForm };
          delete dataForm.text;
          doClassify(dataForm).then((res) => {
            console.log("🤗🤗🤗, res =>", res);
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                text: [],
              };
              this.shLoading = false;
              this.close(); //11-1 改
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          // this.$message.info("您已取消操作！");
        },
      });
    },
    //查看公开意见
    handleComment(type){
      console.log("status--------------5576-----",this.status);

      // 如果是代表的出具意见，根据 record.dbPublic 的id查找对应的值
      if (type === 'db') {
        let noPublicTypeItem = this.noPublicType.find(item => item.id === this.record.dbPublic);
        this.publicComment = noPublicTypeItem ? noPublicTypeItem.name : '暂无出具意见';
        this.dialogVisiblePublicComment = true;
        return;
      }

      // 如果是交办小组的出具意见，根据 record.jbxzPublic 的id查找对应的值
      if (type === 'jbxz') {
        let noPublicTypeItem = this.noPublicType.find(item => item.id === this.record.jbxzPublic);
        this.publicComment = noPublicTypeItem ? noPublicTypeItem.name : '暂无出具意见';
        this.dialogVisiblePublicComment = true;
        return;
      }

      // 如果是代表工委的出具意见，根据 record.examineXlwSuggPublic 的id查找对应的值
      if (type === 'dbgw') {
        let noPublicTypeItem = this.noPublicType.find(item => item.id === this.record.examineXlwSuggPublic);
        this.publicComment = noPublicTypeItem ? noPublicTypeItem.name : '暂无出具意见';
        this.dialogVisiblePublicComment = true;
        return;
      }

      if (type === 'onlineyjs') {
        let noPublicTypeItem = this.noPublicType.find(item => item.id === this.record.examineYjsPublic);
        this.publicComment = noPublicTypeItem ? noPublicTypeItem.name : '暂无出具意见';
        this.dialogVisiblePublicComment = true;
        return;
      }

      this.resourceForm.proposalIds = this.proposalId;
      let dataForm = {
        type:type,
        proposalId:this.resourceForm.proposalIds,
      };
      findCommentByType(dataForm).then((res) => {
        if (res.data.code == 200) {
          console.log("data.data.status--------",res.data.data.status)
          console.log("type--------",type)
          if(res.data.data.status ==='30' && type==='xlw') {
            this.resourceForm.publicComment =res.data.data.replyComment;
            this.resourceForm.xlwIfPublice =res.data.data.xlwPublic;
            this.resourceForm.historyId = res.data.data.historyId;
            this.dialogVisiblePublicCommentFs = true;
          }else{
            this.publicComment =res.data.data.replyComment;
            this.resourceForm.xlwIfPublice =res.data.data.xlwPublic;
            this.resourceForm.historyId = res.data.data.historyId;
            this.dialogVisiblePublicComment = true;
          }
          this.shLoading = false;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    // 提交初审闭会
    fsPublicSubmit() {
      this.resourceForm.proposalIds = this.proposalId;
      let tips =  "是否确定修改？";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: tips,
        onOk: () => {
          this.shLoading = true;
          let dataForm = {
            ifPublice : this.resourceForm.xlwIfPublice,
            publicComment : this.resourceForm.publicComment || "",
            proposalIds : this.resourceForm.proposalIds,
            historyId : this.resourceForm.historyId,
          };
          delete dataForm.text;
          updatePublicComment(dataForm).then((res) => {
            console.log("🤗🤗🤗, res =>", res);
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                text: [],
              };
              this.shLoading = false;
              this.dialogVisiblePublicCommentFs = false;
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          // this.$message.info("您已取消操作！");
        },
      });
    },

    // 提交初审闭会
    submitFirstCheckBh(status) {
      if (!this.resourceForm.contentType) {
        return this.$message.error("请选择内容分类");
      }
      this.resourceForm.proposalIds = this.proposalId;
      this.resourceForm.actType = status;
      let tips = "pass" == status ? "是否确定初审通过？" : "是否确定退回？";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: tips,
        onOk: () => {
          this.shLoading = true;
          let dataForm = {
            ifPublice : this.resourceForm.xlwIfPublice,
            publicComment : this.resourceForm.publicComment || "",
            proposalIds : this.resourceForm.proposalIds,
            contentType : this.resourceForm.contentType,
            actType : this.resourceForm.actType,
          };
          delete dataForm.text;
          doFirstCheckBh(dataForm).then((res) => {
            console.log("🤗🤗🤗, res =>", res);
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                text: [],
              };
              this.shLoading = false;
              this.close(); //11-1 改
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          // this.$message.info("您已取消操作！");
        },
      });
    },


    //提交初审
    submitFirstCheck(status) {
      this.resourceForm.proposalIds = this.proposalId;
      this.resourceForm.actType = status;
      let tips = "pass" == status ? "是否确定初审通过？" : "是否确定退回？";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: tips,
        onOk: () => {
          this.shLoading = true;
          let dataForm = {...this.resourceForm};
          delete dataForm.text;
          doFirstCheck(dataForm).then((res) => {
            console.log("🤗🤗🤗, res =>", res);
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                text: [],
              };
              this.shLoading = false;
              this.close(); //11-1 改
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          // this.$message.info("您已取消操作！");
        },
      });
    },
    // 提交审核
    submitReview(status) {
      this.resourceForm.proposalIds = this.proposalId;
      this.resourceForm.actType = status;
      console.log(this.resourceForm, "this.resourceForm");
      let tips = "pass" == status ? "是否确定审核通过？" : "是否确定退回？";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: tips,
        onOk: () => {
          this.shLoading = true;
          let dataForm = { ...this.resourceForm };
          delete dataForm.text;
          doAudit(dataForm).then((res) => {
            console.log("🤗🤗🤗, res =>", res);
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                text: [],
              };
              this.shLoading = false;
              // 选联委 审核后显示交办
              // if (this.userData.authorities[0].authority == "YAJY_XLW") {
              //   this.showDesc();
              // } else {
              // this.close();
              // }
              this.close(); //11-1 改
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          // this.$message.info("您已取消操作！");
        },
      });
    },
    //闭会期间复审
    submitReviewBh(status) {
      this.resourceForm.proposalIds = this.proposalId;
      this.resourceForm.actType = status;
      console.log(this.resourceForm, "this.resourceForm");
      let tips = "pass" == status ? "是否确定审核通过？" : "是否确定退回？";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: tips,
        onOk: () => {
          this.shLoading = true;
          let dataForm = { ...this.resourceForm };
          delete dataForm.text;
          doAuditBh(dataForm).then((res) => {
            console.log("🤗🤗🤗, res =>", res);
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                text: [],
              };
              this.shLoading = false;
              this.close(); //11-1 改
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          // this.$message.info("您已取消操作！");
        },
      });
    },
    //研究室出具公开意见
    submitYjsPublic() {
      this.resourceForm.proposalIds = this.proposalId;
      let tips = "是否确定提交公开意见";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: tips,
        onOk: () => {
          this.shLoading = true;
          let dataForm = {
            ifPublice : this.resourceForm.yjsIfPublice,
            publicComment : this.resourceForm.publicComment || "",
            proposalIds : this.resourceForm.proposalIds,
          };
          delete dataForm.text;
          doYjsPublic(dataForm).then((res) => {
            console.log("🤗🤗🤗, res =>", res);
            if (res.data.code == 200) {
              this.$message.success(res.data.message);
              this.resourceForm = {
                meetName: "",
                meet: "",
                host: "",
                hostName: "",
                text: [],
              };
              this.shLoading = false;
              this.close(); //11-1 改
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          // this.$message.info("您已取消操作！");
        },
      });
    },

    // 关闭窗口
    close() {
      this.isBanMeet = false
      this.userName = this.record.headPer
      console.log(this.record.undertakeStatus)
      console.log(this.record.headPer)
      this.$emit("parentFetchData");
      this.visible = false;
      this.showOperate = true;
      // for (const key in this.$data) {
      //   if (!Array.isArray(this.$data[key])) {
      //     this[key] = this.$options.data.call(this)[key];
      //   }
      // }
      this.resourceForm.host = [];
      this.resourceForm.meet = [];
      this.resourceForm.mainHandleLastTimeStr = "";
      (this.resourceForm.minorHandleLastTimeStr = ""),
        (this.resourceForm = {
          meetName: "",
          meet: "",
          text: [],
          host: "",
          hostName: "",
          adjust: "",
          adjustName: "",
          isPublic: "",
          dffs: [],
          xlwCheck: "1",
          subLeaderCheck:"1",
          leaderCheck:"1",
          xlwIfPublice:"1",
          yjsIfPublice:"1",
          jbrName: "",
          jbrDept: "",
          jbrJob: "",
          jbrPhone: "",
          jbrOph: "",
          dflb: null,
          orgCode: "",
          mainHandleLastTimeStr: "",
          minorHandleLastTimeStr: "",
          finalAssignTimeStr:"",
          contentType: "",
        });
      this.$refs.adjustListEdit.modalVisible = false;
      this.$refs.evaluationModal.modalVisible = false;
      this.fileTextList_8 = [];
      this.fileTextList = [];
      this.fileTextList_7 = [];
      this.minorLastTime = "";
    },
    // 退回
    back() { },
    // 交办-提交交办
    manage() {
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
      if (this.resourceForm.hostName == this.resourceForm.meetName) {
        return this.$message.error("主办单位和会办单位不能相同");
      }
      console.log("主办单位长度", this.resourceForm.hostorgCode.length);
      console.log("会办单位长度", this.resourceForm.meetorgCode.length);
      // if(this.resourceForm.hostorgCode.length>2){
      //   return this.$message.error("主办单位最多只能俩个！");
      // }
      if (this.resourceForm.hostorgCode.length >= 2 && this.resourceForm.meetorgCode.length > 0) {
        return this.$message.error("该建议存在多主办单位请删除其他会办单位！");
      }

      console.log("🤗🤗🤗, this.resourceForm =>", this.resourceForm);
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定交办？",
        onOk: () => {
          let hostData = [];
          let meetData = [];
          this.resourceForm.host.map((item) => {
            hostData.push({
              ifmain: 1,
              orgCode: item.orgCode,
              orgName: item.orgName,
              proposalId: this.proposalId,
            });
          });
          if (this.resourceForm.meet.length > 0) {
            this.resourceForm.meet.map((item) => {
              meetData.push({
                ifmain: 0,
                orgCode: item.orgCode,
                orgName: item.orgName,
                proposalId: this.proposalId,
              });
            });
          }
          let undertakeList = [];
          undertakeList = hostData.concat(meetData);
          let newForm = {};
          newForm.undertakeList = undertakeList;
          newForm.proposalId = this.proposalId;
          newForm.contentType = this.resourceForm.contentType || "";
          newForm.proposalType = this.record.proposalType;
          newForm.minorHandleLastTimeStr =
            this.resourceForm.minorHandleLastTimeStr;
          newForm.mainHandleLastTimeStr =
            this.resourceForm.mainHandleLastTimeStr;
          newForm.ifPublice = this.resourceForm.ifPublice; //12-13
          doAssign(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("交办成功");
              // 交办后重新获取数据，显示答复 不要了
              // this.showDesc();
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
      });
    },
    //闭会期间交办
    manageBh() {
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
      if (this.resourceForm.hostName == this.resourceForm.meetName) {
        return this.$message.error("主办单位和会办单位不能相同");
      }
      console.log("主办单位长度", this.resourceForm.hostorgCode.length);
      console.log("会办单位长度", this.resourceForm.meetorgCode.length);

      if (this.resourceForm.hostorgCode.length >= 2 && this.resourceForm.meetorgCode.length > 0) {
        return this.$message.error("该建议存在多主办单位请删除其他会办单位！");
      }
      console.log("🤗🤗🤗, this.resourceForm =>", this.resourceForm);
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "是否确定交办？",
        onOk: () => {
          let hostData = [];
          let meetData = [];
          this.resourceForm.host.map((item) => {
            hostData.push({
              ifmain: 1,
              orgCode: item.orgCode,
              orgName: item.orgName,
              proposalId: this.proposalId,
            });
          });
          if (this.resourceForm.meet.length > 0) {
            this.resourceForm.meet.map((item) => {
              meetData.push({
                ifmain: 0,
                orgCode: item.orgCode,
                orgName: item.orgName,
                proposalId: this.proposalId,
              });
            });
          }
          let undertakeList = [];
          undertakeList = hostData.concat(meetData);
          let newForm = {};
          newForm.undertakeList = undertakeList;
          newForm.proposalId = this.proposalId;
          newForm.contentType = this.resourceForm.contentType || "";
          newForm.proposalType = this.record.proposalType;
          newForm.minorHandleLastTimeStr =
            this.resourceForm.minorHandleLastTimeStr;
          newForm.mainHandleLastTimeStr =
            this.resourceForm.mainHandleLastTimeStr;
          doAssignBh(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("交办成功");
              // 交办后重新获取数据，显示答复 不要了
              // this.showDesc();
              this.close();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
      });
    },

    //转为参考建议
    changeRefer() {
      if (!this.resourceForm.host) {
        return this.$message.error("主办单位不能为空");
      }
      let hostData = [];
      let meetData = [];
      this.resourceForm.host.map((item) => {
        hostData.push({
          ifmain: 1,
          orgCode: item.orgCode,
          orgName: item.orgName,
        });
      });
      if (this.resourceForm.meet.length > 0) {
        this.resourceForm.meet.map((item) => {
          meetData.push({
            ifmain: 0,
            orgCode: item.orgCode,
            orgName: item.orgName,
          });
        });
      }
      let undertakeList = [];
      undertakeList = hostData.concat(meetData);
      let newForm = {};
      newForm.undertakeList = undertakeList;
      newForm.proposalId = this.proposalId;
      newForm.contentType = this.resourceForm.contentType || "";
      newForm.proposalType = "4";
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: "确认要转为供参考建议？",
        onOk: () => {
          this.zwCKyj = true;
          doAssign(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("转为参考建议成功");
              this.visible = false;
              this.zwCKyj = false;
              this.close();
              Object.assign(this.$data, this.$options.data.call(this));
            } else {
              this.$message.error(res.data.message);
              this.zwCKyj = false;
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 交办-保存
    save() {
      this.jbLoading = true;
      let newForm = { params: {}, data: {} };
      newForm.params.contentType = this.resourceForm.contentType || "";
      newForm.params.proposalType = this.record.proposalType;
      newForm.params.proposalId = this.proposalId;
      newForm.params.mainHandleLastTimeStr =
        this.resourceForm.mainHandleLastTimeStr;
      newForm.params.minorHandleLastTimeStr =
        this.resourceForm.minorHandleLastTimeStr;
      newForm.params.ifPublice = this.resourceForm.ifPublice; //12-13
      if (!this.resourceForm.host) {
        this.jbLoading = false;
        return this.$message.error("主办单位不能为空");
      }
      let hostData = [];
      let meetData = [];
      this.resourceForm.host.map((item) => {
        hostData.push({
          ifmain: 1,
          orgCode: item.orgCode,
          orgName: item.orgName,
          proposalId: this.proposalId,
        });
      });
      if (this.resourceForm.meet.length > 0) {
        this.resourceForm.meet.map((item) => {
          meetData.push({
            ifmain: 0,
            orgCode: item.orgCode,
            orgName: item.orgName,
            proposalId: this.proposalId,
          });
        });
      }
      let data = [];
      data = hostData.concat(meetData);
      newForm.data = data;

      let conctentTS = "是否确定保存？";
      if (newForm.params.ifPublice == "0" && !this.isPublicDisable) {
        conctentTS = "选择不公开保存后不能修改,是否确定保存？";
      }
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "温馨提示",
        content: conctentTS,
        onOk: () => {
          saveCBDW(newForm).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("保存成功");
              this.jbLoading = false;
              this.close();
              this.resourceForm.host = [];
              this.resourceForm.meet = [];
            } else {
              this.jbLoading = false;
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.jbLoading = false;
          this.$message.info("您已取消操作！");
        },
      });
    },
    showData() {
      console.log("🤗🤗🤗, 下拉内容", this.resourceForm.remarkText);
      console.log("🤗🤗🤗,  下拉值 =>", this.resourceForm.remark);

      this.$forceUpdate()
    },
    // 打开单位table
    openUnitTable(type) {
      this.$refs.commonUnitShuttleTable.rightData = [];
      // 再次点击 设置默认值
      if (this.resourceForm[type]) {
        // this.defaultUnit = this.resourceForm[type].map((item) => item.orgCode);
      } else {
        this.defaultUnit = [];
      }
      this.unitType = type;
      if (type == "meet" && this.resourceForm.meet.length > 0) {
        if (this.resourceForm["meet"] && this.resourceForm["meet"].length > 0) {
          this.defaultUnitHB = this.resourceForm["meet"].map(
            (item) => item.orgCode
          );
        }
        // this.disableSelectedRowKeys = this.resourceForm.host.map((item) => item.orgCode);//传禁用选项
        this.disableSelectedRowKeysHB = this.resourceForm.hostorgCode; //传禁用选项
        setTimeout(() => {
          this.$refs.commonUnitShuttleTableHB.rightData =
            this.resourceForm["meet"];
          this.$refs.commonUnitShuttleTableHB.queryForm.orgName = ""; // 清空搜索条件
          this.$refs.commonUnitShuttleTableHB.unitTableVisible = true;
        }, 500);
      } else if (type == "host" && this.resourceForm.host.length > 0) {
        if (this.resourceForm["host"] && this.resourceForm["host"].length > 0) {
          this.defaultUnit = this.resourceForm["host"].map(
            (item) => item
          );
        }
        // this.disableSelectedRowKeys = this.resourceForm.meet.map((item) => { item.orgCode });//传禁用选项
        this.disableSelectedRowKeys = this.resourceForm.meetorgCode; //传禁用选项

        setTimeout(() => {
          this.$refs.commonUnitShuttleTable.rightData =
            this.resourceForm["host"];
          this.$refs.commonUnitShuttleTable.queryForm.orgName = ""; // 清空搜索条件
          this.$refs.commonUnitShuttleTable.unitTableVisible = true;
        }, 500);
      } else {
        this.disableSelectedRowKeys = [];
        this.$refs.commonUnitShuttleTable.unitTableVisible = true;
      }
    },
    // 接受单位
    getUnitTable(data) {
      // 交办选择主/会办单位
      // 深拷贝
      let rows = JSON.parse(JSON.stringify(data));
      if (this.unitType == "meet" || this.unitType == "host") {
        if (this.unitType == "host") {
        // 禁用 会办按钮
        // console.log(this.resourceForm[this.unitType].host)
        if(rows.length >= 2) {
          console.log('禁用了 浅')
          this.isBanMeet = true;
        } else {
          this.isBanMeet = false;

        }
        }
        let name = rows.map((item) => item.orgName).toString();
        let orgCode = rows.map((item) => item.orgCode);
        console.info("getUnitTable", rows);
        this.resourceForm[this.unitType] = rows;
        this.resourceForm[this.unitType + "Name"] = name;
        this.resourceForm[this.unitType + "orgCode"] = orgCode;
        console.info("getUnitTable", this.unitType, this.resourceForm);
        this.defaultUnit = rows.map((item) => item.orgCode);
        this.disableSelectedRowKeys = []; //清空禁用选项
      } else if (this.unitType == "单位调整") {
        console.log("🤗🤗🤗, rows =>", rows);
        this.changeIfMainForm.orgName = "";
        this.changeIfMainForm.orgName = rows
          .map((item) => item.orgName)
          .toString();
        console.log(this.changeIfMainForm.orgName)
        // 储存
        this.changeIfMainForm.rows = [];
        this.changeIfMainForm.rows = rows;
        this.changeIfMainVisible = true;
        this.$forceUpdate();
      }
    },
    getUnitTableHB(data) {
      // 会办单位
      if (this.unitType == "meet" || this.unitType == "host") {

        // 深拷贝
        let rows = JSON.parse(JSON.stringify(data));
        let name = rows.map((item) => item.orgName).toString();
        let orgCode = rows.map((item) => item.orgCode);
        console.info("getUnitTableHB", rows);
        this.resourceForm[this.unitType] = rows;
        this.resourceForm[this.unitType + "Name"] = name;
        this.resourceForm[this.unitType + "orgCode"] = orgCode;
        console.info("getUnitTableHB", this.unitType, this.resourceForm);
        this.defaultUnitHB = rows.map((item) => item.orgCode);
        this.disableSelectedRowKeysHB = []; //清空禁用选项
        // 禁用 会办按钮
        if (this.unitType == "host") {
          if(rows.length >= 2 ) {
            console.log('禁用了 深')
            this.isBanMeet = true;
          } else {
            this.isBanMeet = false;
          }
        }
      }
    },
    //选择是否公开
    changePublic(e) {
      this.$forceUpdate();
    },
    changeZwSug(e) {
      this.$forceUpdate();
    },
    changeDbSug(e) {
      this.$forceUpdate();
    },
    xlwCheck(e) {
      this.$forceUpdate();
    },
    subLeaderCheck(e) {
      this.$forceUpdate();
    },
    leaderCheck(e) {
      this.$forceUpdate();
    },

    addOneMonth(date) {
      const newDate = new Date(date);
      newDate.setMonth(newDate.getMonth() + 1);
      newDate.setDate(newDate.getDate() + 1);
      return newDate.toISOString().split('T')[0];
    },
    addThreeMonths(date) {
      const newDate = new Date(date);
      newDate.setMonth(newDate.getMonth() + 3);
      newDate.setDate(newDate.getDate() + 1);
      return newDate.toISOString().split('T')[0];
    },

    // 获取信息
    async showDesc() {
      // console.log("🤗🤗🤗, this.record.status =>", this.record.status);
      console.log("🤗🤗🤗syp,  this.resourceForm =>", this.resourceForm);
      this.listLoading = true;


      //根据当前届次交办截至时间来计算正式交办的办理期限
      await findBySession().then((res) => {
        if (res.data.code == 200) {
          if(this.isMeeting==1) {
            this.resourceForm.finalAssignTimeStr = res.data.data.current.assignDeadline;
            // 计算一个月后的日期
            this.resourceForm.minorHandleLastTimeStr = this.addOneMonth(this.resourceForm.finalAssignTimeStr);

            // 计算三个月后的日期
            this.resourceForm.mainHandleLastTimeStr = this.addThreeMonths(this.resourceForm.finalAssignTimeStr);
          }
        }
      });


      let userDataBool = await this.getOneUser();
      if (userDataBool) {
        let res = await proposalById({ proposalId: this.proposalId });
        if (res.data.code == 200) {
          this.record = res.data.data;
          // 回显是否公开
          this.resourceForm.ifPublice = this.record.ifPublice;
          if (this.resourceForm.ifPublice == "0") {
            this.isPublicDisable = true;
          }
          this.resourceForm.contentType = this.record.proposalContentType;
          // 答复时获取经办信息 userData 请求数据较慢拿不到
          if (
            this.userData.authorities[0].authority == "YAJY_XLW" ||
            (this.record.undertakeStatus == 2 &&
              this.userData.authorities[0].authority == "YAJY_CBDW") ||
            (this.scenesStatus == "答复修改" &&
              this.userData.authorities[0].authority == "YAJY_CBDW")
          ) {
            let form = {};
            form.proposalId = this.proposalId;
            if (this.scenesStatus == "答复修改") {
              form.isUpdate = "Y";
            }
            this.resourceForm.meetorgCode = null;
            this.resourceForm.hostorgCode = null;
            this.resourceForm.host = [];
            this.resourceForm.meet = [];
            this.disableSelectedRowKeys = [];
            this.defaultUnit = [];
            getUndertakeDfInfo(form).then((res) => {
              if (res.data.code == 200) {
                if (res.data.data.undertakeDf) {
                  this.resourceForm.dfId = res.data.data.undertakeDf.dfId;
                }
                //初始化经办人数据
                this.resourceForm.orgCode = res.data.data.undertake.orgCode;
                this.resourceForm.jbrName = res.data.data.undertake.jbrName;
                this.resourceForm.jbrDept = res.data.data.undertake.jbrDept;
                this.resourceForm.jbrJob = res.data.data.undertake.jbrJob;
                this.resourceForm.jbrPhone = res.data.data.undertake.jbrPhone;
                this.resourceForm.jbrOph = res.data.data.undertake.jbrOph;

                if (
                  res.data.data.undertakeDf != undefined &&
                  res.data.data.undertakeDf != null
                ) {
                  if (
                    res.data.data.undertakeDf.dflb != undefined ||
                    res.data.data.undertakeDf.dflb != null
                  ) {
                    this.resourceForm.dflb = res.data.data.undertakeDf.dflb;
                  }
                  if (
                    res.data.data.undertakeDf.dffs &&
                    res.data.data.undertakeDf.dffs.indexOf(",") >= 0
                  ) {
                    this.resourceForm.dffs =
                      res.data.data.undertakeDf.dffs.split(",");
                  }
                  if (
                    res.data.data.undertakeDf.dffs &&
                    res.data.data.undertakeDf.dffs.indexOf(",") < 0
                  ) {
                    this.resourceForm.dffs = res.data.data.undertakeDf.dffs;
                  }
                  this.resourceForm.isPublic =
                    res.data.data.undertakeDf.isPublic;
                  this.resourceForm.orgEvaluation =
                    res.data.data.undertakeDf.orgEvaluation;
                  this.resourceForm.remark = res.data.data.undertakeDf.remark;
                  this.resourceForm.remarkText = res.data.data.undertakeDf.remarkText;
                  this.$forceUpdate();
                }

                //政务协同
                if (
                  res.data.data.undertake.ifmain == 1 &&
                  res.data.data.xtpfList.length > 0
                ) {
                  this.hbDataSource = res.data.data.xtpfList;
                }

                //附件
                if (
                  res.data.data.fujianList != undefined &&
                  res.data.data.fujianList != null &&
                  res.data.data.fujianList.length > 0
                ) {
                  res.data.data.fujianList.forEach((item) => {
                    if (item.attType == 2) {
                      // 附件
                      this.resourceForm.fuJianIds = [];
                      this.resourceForm.fuJianIds.push(item.attId);
                      let file = {
                        uid: item.attId,
                        name: item.attName + "." + item.attSuffix,
                        status: "done",
                        url: "",
                      };
                      this.fileTextList = [];
                      this.fileTextList.push(file);
                    } else if (item.attType == 7) {
                      this.resourceForm.forumFile = [];
                      this.resourceForm.forumFile.push(item.attId);
                      let file = {
                        uid: item.attId,
                        name: item.attName + "." + item.attSuffix,
                        status: "done",
                        url: "",
                      };
                      this.fileTextList_7 = [];
                      this.fileTextList_7.push(file);
                    } else if (item.attType == 8) {
                      this.resourceForm.communFile = [];
                      this.resourceForm.communFile.push(item.attId);
                      let file = {
                        uid: item.attId,
                        name: item.attName + "." + item.attSuffix,
                        status: "done",
                        url: "",
                      };
                      this.fileTextList_8 = [];
                      this.fileTextList_8.push(file);
                    }
                  });
                }
                this.$forceUpdate();
              }
            });
          }
          //交办时获取已保存的承办单位数据
          if (this.record.status == 40) {
            this.listLoading = true;
            getProposalUnder(this.proposalId).then((res) => {
              console.log("获取存在的承办单位数据, res =>", res);
              if (res.data.code == 200) {
                let hostNames = [];
                let meetNames = [];
                let hostData = [];
                let meetData = [];
                let hostorgCode = [];
                let meetorgCode = [];
                let minorHandleLastTimeStr = "";
                let mainHandleLastTimeStr = "";
                this.resourceForm.hostName = "";
                this.resourceForm.meetName = "";
                this.resourceForm.host = "";
                this.resourceForm.meet = "";
                this.resourceForm.hostorgCode = [];
                this.resourceForm.meetorgCode = [];
                console.log("🤗🤗🤗, 我清空了吗 =>", this.resourceForm);
                if (res.data.data.length !== 0) {
                  res.data.data.forEach((item) => {
                    if (item.ifmain == 1) {
                      hostNames.push(item.orgName);
                      hostData.push({
                        orgCode: item.orgCode,
                        orgName: item.orgName,
                      });
                      hostorgCode.push(item.orgCode);
                      mainHandleLastTimeStr = item.handleLastTime
                        ? item.handleLastTime.slice(0, 10)
                        : "";
                    }
                    if (item.ifmain == 0) {
                      meetNames.push(item.orgName);
                      meetData.push({
                        orgCode: item.orgCode,
                        orgName: item.orgName,
                      });
                      meetorgCode.push(item.orgCode);
                      minorHandleLastTimeStr = item.handleLastTime
                        ? item.handleLastTime.slice(0, 10)
                        : "";
                    }
                  });
                  this.resourceForm["hostName"] = hostNames.join(",");
                  this.resourceForm["meetName"] = meetNames.join(",");
                  this.resourceForm.hostorgCode = hostorgCode;
                  this.resourceForm.meetorgCode = meetorgCode;
                  this.resourceForm.host = hostData;
                  this.resourceForm.meet = meetData;
                  this.resourceForm.mainHandleLastTimeStr = "";
                  this.resourceForm.mainHandleLastTimeStr =
                    mainHandleLastTimeStr;
                  this.resourceForm.minorHandleLastTimeStr = "";
                  this.resourceForm.minorHandleLastTimeStr =
                    minorHandleLastTimeStr;
                  this.$forceUpdate();
                  console.log(this.resourceForm, "this.resourceForm");
                }
                setTimeout(() => {
                  this.listLoading = false;
                }, 50);
              } else {
                this.$message.error(res.data.message);
              }
            });
          }
          //获取答复、反馈数据
          this.feedbackReply();

          //获取代表数据
          this.getJointlyListData();

          //获取流程跟踪数据
          this.getLocalHistoryData();
          //获取流程跟踪数据
          this.getDfhDataList();
          //获取流程节点数据
          this.getFlowNodeData();

          this.listLoading = false;
        } else {
          this.listLoading = false;

          this.$message.error(res.data.message);
        }
      }
      // 获取子流程状态值
      // this.getUndertakeStatusData();
    },
    // 获取登录人数据
    async getOneUser() {
      const res = await getUserData_yajy();
      if (res.data.code == 200) {
        // 获取登录人的权限
        this.userData = res.data.data;
        return true;
      } else {
        this.$message.error(res.data.message);
        return false;
      }
    },

    // 答复时获取经办信息
    getJbrData() {
      getJbr(this.proposalId).then((res) => {
        console.log("🤗🤗🤗, res =>答复时获取经办信息", res);
        if (res.data.code == 200) {
          this.resourceForm.jbrName = res.data.data[0].jbrName;
          this.resourceForm.jbrDept = res.data.data[0].jbrDept;
          this.resourceForm.jbrJob = res.data.data[0].jbrJob;
          this.resourceForm.jbrPhone = res.data.data[0].jbrPhone;
          this.resourceForm.jbrOph = res.data.data[0].jbrOph;
        }
      });
    },
    viewFkEvaluation(data) {
      if (data) {
        this.fkContent = data.gdjy;
        if (data.evaluationValue == 1) {
          this.fkEvaluation = "不满意";
        }
        if (data.evaluationValue == 2) {
          this.fkEvaluation = "基本满意";
        }
        if (data.evaluationValue == 3) {
          this.fkEvaluation = "满意";
        }
      }
      if (this.mediaType === '其他建议') {
        this.$message.error("抱歉，你没有该权限！");
      } else {
        this.fkVisible = true;
      }
    },
    // 关闭反馈窗口
    closeFkVisible() {
      this.fkContent = "";
      this.fkEvaluation = "";
      this.fkVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
/* 设置div的高度和边框 */

/* 建议公开信息标题样式 */
.section-header {
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  text-align: left;
  font-weight: bold;
  padding: 8px 11px;
  margin-bottom: -1px;
}

/* 设置div为弹性容器，水平和垂直方向都居中对齐内容   */
.tip {
  display: flex;
  justify-content: center;
  align-items: center;
}


.ant-form-item {
  display: flex;
  align-items: center;
}

/* 步骤条样式 */
.step-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 20px;
}

.step {
  display: flex;
  position: relative;
  width: 100%;
}

.step-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #ddd;
  /* 默认灰色 */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
  font-size: 16px;
  font-weight: bold;
  border: 2px solid #ddd;
  z-index: 2;
}

.step-active {
  background-color: #bd3124;
  /* 激活状态 */
  color: white;
  border-color: #bd3124;
}

.step-completed {
  background-color: #bd3124;
  /* 完成状态 */
  color: white;
  border-color: #bd3124;
}

.step-content {
  flex-grow: 1;
  width: 100%;
}

.step-reply-type {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  background-color: #f0f0f0;
}

.step-reply-time {
  margin-bottom: 10px;
}

.step-info {
  /*background-color: #f0f0f0;*/
  width: 95%;
  padding: 8px 8px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
}

.info-item {
  margin: 0 10px;
}

.info-item:not(:first-child):before {
  content: "|";
  margin-right: 20px;
  color: #d3d3d3;
}

.comment-text {
  display: inline-block;   /* 保证文本在一行内 */
  max-width: 94.5%;         /* 防止文本过长，设置最大宽度 */
  white-space: pre-wrap;     /* 换行 */
  overflow: hidden;        /* 隐藏超出区域的内容 */
  text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
  vertical-align: top;     /* 如果有其他元素，可以对齐 */
  width: 100%;             /* 确保文本区域占满容器宽度 */
  line-height: 1.5;
  height: 4.5em;
}
.text-container{
  width: 450px;
  height: 60px;
}

.step-line {
  width: 2px;
  background-color: #d52838;
  /* 竖线颜色 */
  position: absolute;
  top: 20px;
  left: 15px;
  transform: translateX(-50%);
  z-index: 1;
}

.form-item {
  display: flex;
  align-items: flex-start;  /* 保证标签和输入框左上角对齐 */
  margin-bottom: 16px;
}
.tip-x {
  /*font-weight: bold; !* 提示文字加粗 *!*/
  margin-bottom: 4px; /* 提示文字底部间距 */
  display: inline; /* 确保提示文字和标题文字在同一行 */
}
/*.title-container {*/
/*  display: flex;*/
/*  justify-content: space-between; !* 使“处理建议”靠左，tipMessage靠右 *!*/
/*  align-items: center; !* 垂直居中对齐 *!*/
/*  width: 100%;*/
/*}*/

.title-text {
  margin-right: 60px; /* 设置“处理建议”与提示文字之间的间距 */
}

.title-text {
  font-size: 20px;
}

.subtitle-text {
  font-size: 16px;
  display: block;
  margin-top: 5px;
}

.custom-steps {
  width: 90%;
  //::v-deep .ant-steps-item-icon {
  //   width: 22px;
  //   height: 22px;
  //   line-height: 22px;
  //   font-size: 12px;
  //   margin-right: 6px;
  //   }
   ::v-deep .ant-steps-item-title {
     font-size: 12px;
     //line-height: 22px;
     padding-right: 0px;
     }
   //::v-deep .ant-steps-item-tail {
   //  top: 11px;
   //  padding: 0 6px;
   //  }
   ::v-deep .ant-steps-item {
     padding-left: 0px;
     }
   ::v-deep .ant-steps-item-content {
     margin-top: 0;
     }
   ::v-deep .ant-steps {
     line-height: normal;
     }
}
.red-line {
  position: relative;
  padding-left: 15px; /* 根据需要调整 */
}

::v-deep .hbdwDfInfo {
  .ant-descriptions-view{
    .ant-descriptions-row{
      .ant-descriptions-item-label{
        width: 183px;
      }
    }
  }
}

::v-deep .hbdwDfInfo {
  .ant-descriptions-view{
    .ant-descriptions-row{
      .ant-descriptions-item-content{
        width: 305px;
      }
    }
  }
}

::v-deep .ant-descriptions-item-label  {
  width: 150px;
}

::v-deep .ant-descriptions-item-content  {
  width: 250px;
}
.red-line::before {
  content: '';
  position: absolute;
  left: 0;
  top: 31%;
  transform: translateY(-50%);
  width: 4px; /* 竖线的宽度 */
  height: 1em; /* 竖线的高度 */
  background-color: red; /* 竖线的颜色 */
}
.tip-message {
  flex: 1; /* 使tipMessage占据剩余空间并居中 */
  /*text-align: center; !* 居中文本 *!*/
  white-space: normal; /* 允许文本换行 */
  /*word-wrap: break-word; !* 允许长单词或 URL 地址换行到下一行 *!*/
}
</style>


