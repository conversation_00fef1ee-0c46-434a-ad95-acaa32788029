/**
 * 获取行政区县
 */
import request from "@/utils/requestTemp";
import { instance_3 } from "@/api/axiosRq";
export function getList(data) {
  return request({
    url: "/administrativeArea/list",
    method: "get",
    params: data,
  });
}

export function getListData(data) {
  return request({
    url: "/liaisonStation/listForAdmin",
    method: "get",
    params: data,
  });
}

export function getPermList(data) {
  return request({
    url: "/administrativeArea/permList",
    method: "get",
    params: data,
  });
}
