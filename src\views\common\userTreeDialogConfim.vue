<template>
  <el-dialog
    :title="userTreeDialogTitle"
    :visible.sync="userTreeDialogVisible"
    width="30%"
  >
    <a-row style="margin-bottom: 15px">
      <a-col :span="15">
        <el-input
          v-model="filterText"
          placeholder="输入关键字进行过滤"
          @input="$forceUpdate()"
        ></el-input>
      </a-col>
      <a-col :span="8" style="margin-left: 10px">
        <el-button icon="el-icon-search" @click="handleFilterText"
          >搜索</el-button
        >
      </a-col>
    </a-row>
    <el-tree
      ref="userTree"
      :data="userTreeData"
      :props="userTreeProps"
      node-key="uniqueId"
      show-checkbox
      :check-on-click-node="true"
      :default-checked-keys="userTreeDefaultKey"
      :default-expanded-keys="userTreeExpandedKey"
      :filter-node-method="filterNode"
      @check-change="userCheckChange"
      @node-expand="treeClickHandle"
    ></el-tree>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {getTreeOrderByXsbh} from "@/api/user";
import { instance_1 } from "@/api/axiosRq";
// import { getAllResourceIdentityUser as getUserTree } from "@/api/identity";
export default {
  props: {
    userTreeDialogTitle: {
      type: String,
      default: "选择关联人员",
    },
    rootOrgId: {
      type: String,
      default: "root",
    },
    showCheckbox: {
      type: Boolean,
      default: true,
    },
    selfPerm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      filterText: "",
      userTreeDialogVisible: false,
      treeCheckedKey: [],
      userTreeData: [],
      userTreeDefaultKey: [],
      userTreeExpandedKey: [],
      userTreeProps: {
        children: "children",
        label: "name",
      },

      isRadio: false,
    };
  },
  created() {
    this.initUserTree();
  },
  // watch: {
  //   filterText(val) {
  //     this.$refs.userTree.filter(val);
  //   },
  // },
  methods: {
    initUserTree() {
      let data = {
        tenantId: this.$tenantId,
        rootOrgId: this.rootOrgId,
      };
      if (this.selfPerm) {
        let params = { tenantId: data.tenantId, rootOrgId: data.rootOrgId };
        return instance_1({
          url: `/display/user/findOUTreeByRole`,
          method: "post",
          params,
          data: {
            userIds: data.userIds || [],
            sortOrder: data.sortOrder || "",
          },
        }).then((response) => {
          const res = response.data;
          if (res.code != "0000" && res.code != 200)
            return this.$message.error(res.msg);
          this.userTreeData = res.data.children;
        });
      } else {
        getTreeOrderByXsbh(data).then((res) => {
          this.userTreeData = res.data.children;
        });
      }
    },
    handleFilterText() {
      this.$refs.userTree.filter(this.filterText);
    },
    open(checkedData) {
      this.userTreeDialogVisible = true;

      //回选树节点
      if (checkedData) {
        //清空旧数据
        this.treeCheckedKey = [];
        this.userTreeDefaultKey = [];
        if (checkedData.length > 0) {
          checkedData.forEach((item) => {
            this.userTreeDefaultKey.push(item);
            this.treeCheckedKey.push(item);

            this.userTreeExpandedKey.push(item);
          });

          if (this.$refs.userTree != null) {
            this.$refs.userTree.setCheckedKeys(this.treeCheckedKey);
            let checkedNodes = this.$refs.userTree.getCheckedNodes(false, true);
            checkedNodes.forEach((item) => (item.expanded = true));
          }
        }
      }
    },
    userCheckChange(data, checked, indeterminate) {
      if (this.isRadio && checked) {
        this.$refs.userTree.setCheckedKeys([data.uniqueId]);
      }
    },
    confirm() {
      let checkedNodes = this.$refs.userTree.getCheckedNodes();
      this.$emit("confirm", checkedNodes);

      this.$refs.userTree.setCheckedKeys([]);

      this.treeCheckedKey = [];
      this.userTreeDefaultKey = [];

      this.userTreeDialogVisible = false;
    },

    close() {
      this.$refs.userTree.setCheckedKeys([]);
      this.treeCheckedKey = [];
      this.userTreeDefaultKey = [];
      this.HandleExpand();
      this.userTreeDialogVisible = false;
    },

    //事件触发全部收缩
    HandleExpand() {
      let that = this;
      let treeList = this.userTreeData;
      for (let i = 0; i < treeList.length; i++) {
        that.$refs.userTree.store.nodesMap[
          treeList[i].uniqueId
        ].expanded = false;
        this.closeChildren(
          that.$refs.userTree.store.nodesMap[treeList[i].uniqueId]
        );
      }
    },

    closeChildren(node) {
      if (this.hasChildren) {
        node.childNodes.forEach((ele) => {
          ele.expanded = false;
          if (this.hasChildren(ele)) {
            this.closeChildren(ele);
          }
        });
      }
    },

    hasChildren(node) {
      return node.childNodes != null;
    },

    treeClickHandle(data, node, tag) {
      tag.expanded = !tag.expanded;
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
  },
};
</script>
