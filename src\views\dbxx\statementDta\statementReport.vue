<template>
  <div  class="table-container">
    <div style="background-color: #ececec; padding: 20px;">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card title="代表总人数" :bordered="false">
            <p  class="num">2258</p>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="全国代表" :bordered="false">
            <p  class="num">231</p>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="省代表" :bordered="false">
            <p  class="num">360</p>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="市代表" :bordered="false">
            <p  class="num">110</p>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="区代表" :bordered="false">
            <p  class="num">442</p>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="乡镇代表" :bordered="false">
            <p  class="num">144</p>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
export default {
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "统计分析报表");
    this.$store.dispatch("navigation/breadcrumb2", "分析报表");
  },
};
</script>
<style lang="scss" scoped>
.num{
  @include add-size($font_size_16);
  margin-left: 20px;
}
 
</style>