<template>
  <div>
    <a-modal title="候选人确认分配" :visible.sync="visible" width="96%" @cancel="close">
      <a-row class="steps">
        <a-steps :current="0">
          <a-step title="第一步：分配"></a-step>
          <a-step title="第二步：初审" />
          <a-step title="第三步：终审" />
          <a-step title="分配表审核完成" />
        </a-steps>
      </a-row>
      <a-row class="formBox">
        <a-transfer :data-source="mockData" :target-keys="targetKeys" :show-select-all="false" @change="onChange">
          <template slot="children" slot-scope="{
              props: { direction, filteredItems, selectedKeys },
              on: { itemSelectAll, itemSelect },
            }">
            <!-- 左边 -->
            <a-form v-if="direction === 'left'" layout="inline" :form="leftForm" :label-col="{ span: 4 }"
              :wrapper-col="{ span: 16 }">
              <a-row>
                <a-col :span="12">
                  <a-form-item label="届次" style="width: 100%">
                    <a-input v-model="leftForm.meeting"></a-input>
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="姓名" style="width: 100%">
                    <a-input v-model="leftForm.name"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="推荐单位" style="width: 100%">
                    <a-input v-model="leftForm.unit"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item>
                    <a-button @click="search('left')">搜索</a-button>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
            <!-- 右边 -->
            <a-form v-if="direction === 'right'" layout="inline" :form="rightForm">
              <!-- <a-form-item label="届次">
                <a-input v-model="rightForm.meeting"></a-input>
              </a-form-item>
              <a-form-item label="选举单位">
                <a-input v-model="rightForm.name"></a-input>
              </a-form-item>
              <a-form-item label="姓名">
                <a-input v-model="rightForm.unit"></a-input>
              </a-form-item>
              <a-form-item label="推荐单位">
                <a-input v-model="rightForm.unit"></a-input>
              </a-form-item>
              <a-form-item>
                <a-button @click="search('right')">查询</a-button>
              </a-form-item>-->
            </a-form>

            <a-table :row-selection="
              getRowSelection({
                selectedKeys,
                itemSelectAll,
                itemSelect,
                filteredItems,
              })
            " :columns="direction === 'left' ? leftColumns : rightColumns" :data-source="filteredItems" size="small" />
          </template>
        </a-transfer>
      </a-row>
      <a-row slot="footer">
        <a-col :span="8" push="8">
          <a-space>
            <a-button>
              <a-icon type="check" />送审
            </a-button>
            <a-button @click="close">
              <a-icon type="arrow-down" />退出
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-modal>
    <a-modal :visible="selectElectionVisible" title="请选择选举单位" @ok="selectElectionFormOk"
      @cancel="selectElectionFormCancel">
      <a-form-model>
        <a-form-model-item label="选举单位">
          <a-select v-model="selectElectionForm.name">
            <a-select-option key="1" value="1"></a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
// 用于获取差异化后的新数组
import difference from "lodash/difference";
export default {
  data() {
    // 构造数据
    const mockData = [];
    for (let i = 0; i < 20; i++) {
      mockData.push({
        key: i.toString() + "1231",
        name: `假人${i + 1}`,
        sex: `男`,
        birthDay: "2020-20-20",
        job: "工作地点",
        workJob: "职业构成",
        unit: "推荐单位",
      });
    }
    return {
      mockData,
      rightForm: {},
      leftForm: {},
      leftColumns: [
        {
          title: "待分配候选人",
          align: "center",
          ellipsis: true,
          width: 180,
          dataIndex: "name",
        },
        {
          title: "性别",
          align: "center",
          ellipsis: true,
          width: 30,
          dataIndex: "sex",
        },
        {
          title: "出生日期",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "birthDay",
        },
        {
          title: "工作单位及职务",
          align: "center",
          ellipsis: true,
          dataIndex: "job",
        },
        {
          title: "职业构成",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "workJob",
        },
        {
          title: "推荐单位",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "unit",
        },
      ],
      rightColumns: [
        {
          title: "已分配候选人",
          align: "center",
          ellipsis: true,
          width: 180,
          dataIndex: "name",
        },
        {
          title: "性别",
          align: "center",
          ellipsis: true,
          width: 30,
          dataIndex: "sex",
        },
        {
          title: "出生日期",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "birthDay",
        },
        {
          title: "工作单位及职务",
          align: "center",
          ellipsis: true,
          dataIndex: "job",
        },
        {
          title: "职业构成",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "workJob",
        },
        {
          title: "推荐单位",
          align: "center",
          ellipsis: true,
          width: 120,
          dataIndex: "unit",
        },
      ],
      visible: false,
      selectElectionVisible: false,
      targetKeys: [], // 选中到右边的数据
      selectElectionForm: {},
    };
  },
  created() { },
  methods: {
    // 保存
    selectElectionFormOk() {
      // 保存单位
      this.targetKeys = [{ name: "111" }];
      this.selectElectionFormCancel();
    },
    // 取消
    selectElectionFormCancel() {
      this.selectElectionVisible = false;
    },
    // 搜索
    search(type) {
      console.log("🤗🤗🤗, type =>", type);
    },
    // 穿梭框选择
    async onChange(nextTargetKeys, direction, moveKeys) {
      console.log("🤗🤗🤗, nextTargetKeys =>", nextTargetKeys);
      this.targetKeys = nextTargetKeys;
      this.selectElectionVisible = true;
    },
    // 列表选择
    getRowSelection({
      selectedKeys,
      itemSelectAll,
      itemSelect,
      filteredItems,
    }) {
      console.log("🤗🤗🤗, filteredItems =>", filteredItems);
      return {
        onSelectAll(selected, selectedRows) {
          // 选中的数据
          const treeSelectedKeys = selectedRows.map(({ key }) => key);
          const diffKeys = selected
            ? difference(treeSelectedKeys, selectedKeys)
            : difference(selectedKeys, treeSelectedKeys);
          itemSelectAll(diffKeys, selected);
        },
        onSelect({ key }, selected) {
          itemSelect(key, selected);
        },
        selectedRowKeys: selectedKeys,
      };
    },
    // 关闭
    close() {
      this.visible = false;
      Object.assign(this.$data, this.$options.data());
    },
  },
};
</script>
<style scoped>
.formBox {
  padding: 20px;
}
</style>
