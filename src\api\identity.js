import request from "@/utils/request";
import qs from "qs";

export function getList(data) {
  return request({
    url: "/api/v1/meetingIdentity/getIdentityList",
    method: "post",
    data,
  });
}

export function getIdentityTypeList() {
  return request({
    url: "/api/v1/meetingIdentity/getIdentityTypeList",
    method: "post",
  });
}

export function getAllTypeList() {
  return request({
    url: "/api/v1/meetingIdentity/getAllIdentityType",
    method: "post",
  });
}

export function getIndentityUserList() {
  return request({
    url: "/api/v1/meetingIdentity/getIdentityUserList",
    method: "post",
  });
}

export function getRelList(data) {
  return request({
    url: "/api/v1/meetingIdentity/getUserIdentityList",
    method: "post",
    data,
  });
}

export function doSave(data) {
  return request({
    url: "/api/v1/meetingIdentity/addIdentity",
    method: "post",
    data,
  });
}

export function doSaveRel(data) {
  return request({
    url: "/api/v1/meetingIdentity/addUserIdentity",
    method: "post",
    data,
  });
}

export function doUpdate(data) {
  return request({
    url: "/api/v1/meetingIdentity/updateIdentity",
    method: "post",
    data,
  });
}

export function doUpdateRel(data) {
  return request({
    url: "/api/v1/meetingIdentity/updateUserIdentity",
    method: "post",
    data,
  });
}

export function doDelete(data) {
  return request({
    url: "/api/v1/meetingIdentity/delIdentity",
    method: "post",
    data,
  });
}

export function doDeleteRel(data) {
  return request({
    url: "/api/v1/meetingIdentity/delUserIdentity",
    method: "post",
    data,
  });
}

export function getAuthList(param) {
  return request({
    url: "/api/v1/meetingIdentityAuth/getAuthIdentityList/" + param,
    method: "post",
  });
}

export function doAuth(data) {
  return request({
    url: "/api/v1/meetingIdentityAuth/addIdentityAuth",
    method: "post",
    data,
  });
}

export function getAllResourceIdentityUser(data) {
  return request({
    url: "/api/v1/meetingIdentity/getAllResourceIdentityUser",
    method: "post",
    data,
  });
}

export function generateCode(data) {
  return request({
    url: "/api/v1/meetingIdentity/codeGenerate",
    method: "post",
    data,
  });
}

export function doRelOldData(param, data) {
  return request({
    url: "/api/v1/meetingIdentity/doRelOldData/" + param,
    method: "post",
    data,
  });
}
