<template>
    <div class="table-container">
        <a-modal title="分配代表团" :visible.sync="visible" width="70%" @cancel="close">
            <a-row class="formBox">
                <a-form-model ref="queryForm" :model="queryForm" layout="inline">
                    <a-col span="6">
                        <a-form-model-item label="届次">
                            <a-select v-model="queryForm.jcDm" allow-clear placeholder="请选择届次" style="width: 200px;">
                                <a-select-option v-for="item in jcDmList" :key="item.jcDm" :value="item.jcDm">{{
                                        item.levelName
                                }}</a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>

                    <a-col span="6">
                        <a-form-model-item label="姓名" prop="userName">
                            <a-input v-model="queryForm.userName" autocomplete="off" placeholder="请输入姓名" allow-clear
                                v-on:keyup.enter="search"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col span="6">
                        <a-form-model-item label="代表团" prop="delegacy">
                            <a-select v-model="queryForm.delegacy" allow-clear placeholder="请选择代表团"
                                style="width: 200px;">
                                <a-select-option v-for="item in delegacyList" :key="item.dbtDm" :value="item.dbtDm">{{
                                        item.dbtmc
                                }}</a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col span="6">
                        <a-button type="primary" @click="search">搜索</a-button>
                        <a-button style="margin-left: 12px;" class="pinkBoutton" @click="reset">重置</a-button>
                    </a-col>
                </a-form-model>
            </a-row>
            <a-row>
                <a-table class="directorySet-table" ref="table" :columns="columns" :pagination="pagination"
                    :row-selection="rowSelection" :data-source="dataSource" :scroll="{ x: 300, y: 0 }"></a-table>
            </a-row>
            <div slot="footer" class="dialog-footer">
                <a-button @click="closeAdd">取 消</a-button>
                <a-button @click="sonshen" v-if="Istitle == '分配代表团'">审 核</a-button>
                <!-- <a-button type="primary" @click="xuanze">选 择</a-button> -->
            </div>
        </a-modal>
        <!-- 流程弹出页 -->
        <submitForCensorship ref="submitForCensorship" :procInstId="procInstId" @complete="handleComplete" :ids="ids">
        </submitForCensorship>
    </div>
</template>

<script>
import submitForCensorship from '@/views/common/submitForCensorship';
import { getLevelListApi, getfindByIdupdateApi, getnewDbtdbListApi, getgetDelegationApi, getfpdbtsByIdApi, fpdbtsComplete } from "@/api/representativeElection/candidateApi.js";
import { gethxrqrbscompleteApi } from "@/api/xjgw";
import additionSignIn from "@/views/ballot/additionSignIn"
import { myPagination } from "@/mixins/pagination.js";
import StandardTable from "@/components/table/StandardTable";
export default {
    name: "RepresentativeForm",
    // 分配代表团
    components: { StandardTable, additionSignIn, submitForCensorship },
    // 引入分页器配置
    mixins: [myPagination],
    props: {
        neWid: {
            type: String,
        },
    },
    data() {
        return {
            Istitle: "",
            isShowis: true,
            newIDs: '',
            //ID
            procInstId: "",
            //IDS
            ids: [],
            visible: false,
            //获取届次
            jcDmList: [],
            //表单状态
            statesList: [],
            //补选单位
            xzqhDmList: [],
            //代表团
            delegacyList: [],
            //状态
            status: [
                { id: 1, name: "草稿" },
                { id: 2, name: "退回" },
                { id: 3, name: "待初审" },
                { id: 4, name: "待初审" },
                { id: 5, name: "待初审" },
                { id: 6, name: "待终审" },
                { id: 7, name: "已终审" },
                { id: 8, name: "已归档" },
            ],
            //单位
            be_primary: [
                { id: 0, name: "全部" },
                { id: 1, name: "解放军" },
                { id: 2, name: "越秀区" },
                { id: 3, name: "海珠区" },
                { id: 4, name: "荔湾区" },
                { id: 5, name: "天河区" },
                { id: 6, name: "白云区" },
                { id: 7, name: "黄埔区" },
                { id: 8, name: "花都区" },
                { id: 9, name: "番禺区" },
                { id: 10, name: "南沙区" },
                { id: 11, name: "从化区" },
                { id: 12, name: "增城区" },
            ],
            queryForm: {
                jcDm: '2',
                delegacy: undefined,
                userName: "",
                fpdbtId: "",
                pageNum: 1,
                pageSize: 10,
            },
            columns: [
                {
                    title: "当选代表",
                    align: "center",
                    width: 120,
                    ellipsis: true,
                    dataIndex: "DBXM",
                },
                {
                    title: "性别",
                    align: "center",
                    width: 90,
                    ellipsis: true,
                    dataIndex: "SEX",
                },
                {
                    title: "出生日期",
                    align: "center",
                    width: 180,
                    ellipsis: true,
                    dataIndex: "BIRTHDAY",
                },
                {
                    title: "代表团",
                    align: "center",
                    width: 200,
                    ellipsis: true,
                    dataIndex: "DBTMC",
                },

                {
                    title: "推荐单位",
                    align: "center",
                    width: 250,
                    ellipsis: true,
                    dataIndex: "MELYMC",
                },


            ],
            dataSource: [

            ],
            dataSourceList: [],
            selectedRows: [],
            isselectedRows: [],
        };
    },
    created() {
        // this.$store.dispatch("navigation/breadcrumb1", "候选人管理");
        // this.$store.dispatch("navigation/breadcrumb2", "补选结果");
        this.fetchData();
        // this.getFormStateListFn()
        this.getLevelListFn()
        this.getgetDelegation()
        // this.getfindByIdupdate()
    },
    computed: {

        //复选框
        rowSelection() {

        },

    },
    methods: {
        // 重置
        reset() {
            this.queryForm = {
                jcDm: this.jcDmList[0].jcDm,
                pageNum: 1,
                pageSize: 10,

            };
            this.pagination.current = 1;
            this.fetchData()
        },
        // 搜索
        search() {
            this.queryForm.pageNum = 1
            this.pagination.current = 1
            this.fetchData()

        },
        //选择
        xuanze() {

        },
        //取消
        closeAdd() {
            this.Istitle = ''
            this.visible = false
            this.ids = []
        },
        //关闭
        close() {
            // Object.assign(this.$data, this.$options.data());
            this.Istitle = ''
            this.visible = false
            this.ids = []
            this.$emit('handleClearId')
        },


        // 获取数据  回显
        fetchData() {
            if (this.visible == true) {
                console.log(this.newIDs);
                if (this.newIDs != '') {
                    console.log(this.newIDs);
                    let fpdbtId = this.newIDs
                    let params = {
                        fpdbtId,
                        jcDm: this.queryForm.jcDm,
                        userName: this.queryForm.userName,
                        delegacy: this.queryForm.delegacy,
                        pageNum: this.queryForm.pageNum,
                        pageSize: this.queryForm.pageSize,
                    }
                    getnewDbtdbListApi(params).then(res => {
                        console.log(res, 'resres');
                        if (res.data.code == 200) {
                            this.dataSource = res.data.rows
                            this.pagination.total = res.data.total;
                        }
                        //     console.log(res, 'wwwqqq');
                    })
                    //获取回显id
                    getfpdbtsByIdApi(fpdbtId).then(res => {
                        console.log(res, 'ididid');
                        if (res.data.code == '0000') {
                            this.ids.push(res.data.data.id)
                            this.procInstId = res.data.data.procInstId

                        }
                    })
                }
            }
        },
        //送审
        sonshen() {
            this.$refs.submitForCensorship.visible = true
        },
        //审核保存发送
        handleComplete(data) {
            // , { "tzlx": 0 }
            fpdbtsComplete(data).then(res => {
                if (res.data.code == '0000') {
                    this.$emit('handleClearId')
                    this.visible = false
                    this.ids = []
                    this.$refs.submitForCensorship.successComplete()
                    this.$message.success(res.data.msg)
                } else {
                    this.$message.error(res.data.msg)
                }
            })
        },
        //获取当前届次下拉数据列表
        async getLevelListFn() {
            const res = await getLevelListApi()
            if (res.data.code === "0000") {
                this.jcDmList = res.data.data
                // console.log(this.periods[0].jcDm, '00');
                this.queryForm.jcDm = this.jcDmList[0].jcDm//改过
            }
        },
        //获取代表团
        async getgetDelegation() {
            let res = await getgetDelegationApi()
            console.log(res, '代表团')
            if (res.data.code == "0000") {
                this.delegacyList = res.data.data
                // this.queryForm.delegacy = this.delegacyList[0].dbtDm
            }
        },


    },
    watch: {
        neWid: {
            // immediate: true,    // 立即执行handler里面的方法
            handler(val) {
                this.newIDs = this.neWid
                console.log('分配表');
                this.fetchData()
            }
        }
    }
};
</script>

<style>
</style>