<template>
  <div class="table-container">
    <a-row>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <SearchForm :value="listQuery" @onReset="reset" @onSearch="search">
          <template v-slot:topSearch>
            <DhJcCascade v-model="listQuery" allow-clear is-dbt :levelRoleMainIdentify="levelRoleMainIdentify"/>
          </template>
          <template v-slot:moreSearch>
            <AdminStreetStationCascade v-model="listQuery" allow-clear />

            <FormInput
              v-model="listQuery.userName"
              :label="'代表姓名'"
              allow-clear
              @enter="handleEnter"
            />

            <FormRangePicker
              v-model="listQuery"
              label="时间范围"
              start-prop="startTime"
              end-prop="endTime"
              allow-clear
            />

            <FormPicker
              v-model="listQuery.year"
              label="年度"
              type="year"
              allow-clear
            />

            <FormInput
              v-model="listQuery.contactName"
              :label="'联系人姓名'"
              allow-clear
              @enter="handleEnter"
            />
          </template>
        </SearchForm>
        <a-row style="margin: 5px 0px 10px 8px">
          <a-col :span="12" style="margin-bottom: 10px">
            <a-button
              v-show="isDb == 0"
              type="primary"
              style="margin-left: 12px"
              @click="onDetail"
              >新增</a-button
            >

            <a-button
              type="primary"
              style="margin-left: 12px"
              @click="downloadExcel"
              >下载模版</a-button
            >
            <div style="display: inline-block; margin-left: 12px">
              <a-upload
                name="file"
                accept=".xls"
                :multiple="true"
                :file-list="fileList"
                :before-upload="beforeUpload"
                @change="downImport"
              >
                <a-button type="primary"> 批量导入</a-button>
              </a-upload>
            </div>
            <a-button
              type="primary"
              style="margin-left: 12px"
              :loading="downloadLoading"
              @click="downloaData"
              >导出
            </a-button>
          </a-col>
        </a-row>
        <!-- table -->
        <a-spin :indicator="indicator" :spinning="listLoading">
          <!-- 有下拉的table需要  v-if="list.length > 0"  -->
          <a-table
            ref="table"
            :bordered="false"
            class="directorySet-table"
            size="small"
            :columns="columns"
            :pagination="pagination"
            :custom-row="clickRow"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
            :data-source="list"
            :row-key="
              (record, index) => {
                return record.id;
              }
            "
            :scroll="{ x: 300, y: 0 }"
          >
            <div
              slot="operation"
              slot-scope="text, record"
              class="operationStyle"
            >
              <span @click="onDetail(record, 'view')">查看</span>
              <span @click="onDetail(record, 'update')">编辑</span>
              <a-dropdown>
                <a class="dropdown-link" @click="(e) => e.preventDefault()">
                  更多
                  <a-icon type="down" />
                </a>
                <a-menu slot="overlay">
                  <a-menu-item>
                    <a @click="handedelete(record)">删除</a>
                  </a-menu-item>
                  <a-menu-item @click="onSyncAnnouncement(record)"
                    >同步公告</a-menu-item
                  >
                </a-menu>
              </a-dropdown>
            </div>
          </a-table>
        </a-spin>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import {addCommunityPlanExcel, downloadCommunityPlanExcel, ExportExcelData, getList,} from "@/api/communityschedule";

import {instance_1} from "@/api/axiosRq";
import SearchForm from "@/components/SearchForm/index";
import DhJcCascade from "@/components/DhJcCascade/index.vue";
import AdminStreetStationCascade from "@/components/AdminStreetStationCascade/index.vue";
import FormInput from "@/components/FormInput/index.vue";
import FormPicker from "@/components/FormPicker/index.vue";
import FormRangePicker from "@/components/FormRangePicker/index.vue";
import moment from "moment/moment";
import { DBLZ_DBJSQ } from "@/utils/enum/levelRoleMainIdentifyEnum";
import {message} from "ant-design-vue";

export default {
  components: {
    FormRangePicker,
    FormPicker,
    FormInput,
    AdminStreetStationCascade,
    DhJcCascade,
    SearchForm,
  },
  filters: {},
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBJSQ,
      isDb: 0,
      downloadLoading: false,
      list: [],
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        dhDm: undefined,
        jcDm: undefined,
        dbtId: undefined,
        administrativeAreaId: undefined,
        streetTownId: undefined,
        liaisonStationId: undefined,
        userName: undefined,
        startTime: "",
        endTime: "",
        year: "",
        contactName: "",
      },
      listLoading: false,
      //list-----
      selectRows: [],
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,

      selectedRowKeys: [], // 选择的key值数组
      indexNum: 1,

      // 列表
      columns: [
        {
          fixed: "left",
          title: "序号",
          key: "index",
          align: "center",
          width: 50,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        // {
        //   title: "届别",
        //   align: "center",
        //   width: 100,
        //   ellipsis: true,
        //   dataIndex: "sessionName",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        {
          title: "时间",
          align: "center",
          width: 150,
          ellipsis: true,
          dataIndex: "serverTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "年度",
          align: "center",
          width: 80,
          ellipsis: true,
          dataIndex: "year",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        // {
        //   title: "状态",
        //   align: "center",
        //   width: 120,
        //   ellipsis: true,
        //   dataIndex: "statusDesc",
        //   customRender: (text, record, index) => {
        //     return text || "/";
        //   },
        // },
        {
          title: "行政区划",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "administrativeAreaName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "乡镇街道",
          align: "center",
          width: 100,
          ellipsis: true,
          dataIndex: "streetTownName",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "联络站",
          align: "center",
          width: 340,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.liaisonStationName;
          },
        },
        {
          title: "代表姓名",
          align: "center",
          width: 120,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.membersName;
          },
        },
        {
          title: "联系人名称",
          align: "center",
          width: 120,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.contactName || "/";
          },
        },
        {
          title: "联系人电话",
          align: "center",
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.contactPhoneNumber || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 260,
          scopedSlots: { customRender: "operation" },
        },
      ],

      // 分页器设置
      pagination: {
        pageNo: 1,
        pageSize: 10, // 默认每页显示数量
        showSizeChanger: true, // 显示可改变每页数量
        showQuickJumper: true, // 显示跳转功能
        pageSizeOptions: ["10", "20", "30", "40", "50"], // 每页数量选项
        showTotal: (total) => `总共 ${total} 条`, // 显示总数
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize), // 改变每页数量时更新显示
        onChange: this.handleCurrentChange.bind(this), // 点击页码事件
        total: 0, // 总条数
        current: 0, // 当前页数
        buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
        size: "middle",
      },
      fileList: [],
      searchDebounced: _.debounce(this.search, 500),
      isKeepAlive: false,
    };
  },
  created() {
    // 判断是否是路由跳转过来
    // this.isDb = this.$route.query.isDb
    this.isDb = JSON.parse(sessionStorage.getItem("isDb"));
    this.fetchData();
  },
  activated() {
    if (!this.isKeepAlive) {
      this.isKeepAlive = true;
      return;
    }
    this.fetchData();
  },
  beforeDestroy() {
    console.log("销毁,........");
  },
  methods: {
    handleEnter() {
      this.searchDebounced();
    },
    // 删除
    async handedelete(recode) {
      await this.$baseConfirm("确认要删除吗?", null, () => {
        //
        instance_1({
          url: "/communitySchedule/delete", //接口
          method: "post",
          params: { id: recode.id },
        }).then((res) => {
          if (res.data.code != "0000" && res.data.code != 200)
            return this.$message.error(res.data.msg || "接口未知错误");

          this.$message.success("删除成功");
          this.fetchData();
        });
      });
    },
    clickRow(record) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            // this.handelEmodify( record.id)
          },
        },
      };
    },
    // 切换页数
    changePageSize(pageNum, pageSize) {
      this.listQuery.pageNum = pageNum;
      this.listQuery.pageSize = pageSize;
      this.pagination.pageSize = pageSize;
      this.pagination.current = pageNum;
      this.fetchData();
    },

    // 切换页码
    handleCurrentChange(pageNum, pageSize) {
      this.listQuery.pageNum = pageNum;
      this.pagination.current = pageNum;
      this.fetchData();
    },
    // 多选选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectRows = selectedRows;
    },
    //列表
    fetchData() {
      this.listLoading = true;
      getList({ ...this.listQuery, isDb: this.isDb }).then((response) => {
        response.rows.forEach((item) => {
          item.membersName = item.members.map((son) => son.name).toString();
        });
        this.list = response.rows;
        this.total = response.total;
        this.pagination.total = response.total;
        this.listLoading = false;
      });
    },
    handleSizeChange(val) {
      this.listQuery.size = val;
      this.fetchData();
    },
    onDetail(row, oper) {
      this.$router.push({
        path: "/community/intoTheCommunityPlanDetail",
        query: { id: row.id, oper },
      });
    },
    recursionChild(arr) {
      var res = [];
      arr.map((item) => {
        if (item.children && Array.isArray(item.children)) {
          // res = res.concat(this.recursionChild(item.children));
        } else {
          res.push({
            userId: item.userId,
            userName: item.fullName,
            postId: item.posId || item.postId,
          });
        }
      });
      return res;
    },
    details(row) {
      this.$router.push({
        path: "/representative/memberList",
        query: {
          communityScheduleId: row.id,
        },
      });
    },
    search() {
      this.listQuery.pageNum = 1;
      this.pagination.current = 1;
      this.fetchData();
    },
    //重置
    reset(value) {
      // 111
      this.listQuery = value;
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
    // 导出
    downloaData() {
      this.downloadLoading = true;
      const params = {
        ...this.listQuery,
        ids: this.selectedRowKeys.map((son) => son),
      };
      ExportExcelData(params).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `年度活动计划数据.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000);
      });
    },
    // 下载
    downloadExcel() {
      downloadCommunityPlanExcel(this.queryForm).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `进社区计划导入模板.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    // 上传文件限制 直接return
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },
    //导入计划表
    downImport({ file, fileList }) {
      // console.log(file);
      var data = new FormData();
      data.append("file", file);
      addCommunityPlanExcel(data).then((res) => {
        // console.log(res, "res");
        if (res.data.code != "0000") {
          return this.$message.error(res.data.msg);
        }
        if (res.data.code == "0000") {
          this.$baseMessage("上传成功", "success");
          this.fetchData();
        }
        // if (res.data.data.hxrdjbList.length == "0") {
        //   return this.$message.error(res.data.data.msg);
        // }
        // this.addForm = res.data.data.hxrdjbList
        //
        // res.data.data.hxrdjbList.forEach((el) => {
        //   console.log(el, "el");
        //   // this.composition.forEach((i,index)=>{
        //   // //   if(i.zygcmc==el.zygcmc){
        //   // // this.addForm.zygcDm=i.zygcDm;
        //   // //   }
        //   // })

        //   this.$message.success("上传成功");
        //   this.fetchData();
        // });
      });
    },
    onSyncAnnouncement(row) {
      const data = {
        content: row.mark,
        dateTime: row
          ? moment(row.serverTime).format("YYYY-MM-DD HH:mm:ss")
          : undefined,
        mobile: row.contactPhoneNumber,
        liaisonStationId: row.liaisonStationId,
        checkedList: row.attendMembers?.map((it) => it.userId),
      };
      sessionStorage.setItem("ActivitySchedule", JSON.stringify(data));
      sessionStorage.setItem("ActivitySchedulePreviousId", row.id);
      this.$router.push({
        path: "/BBS/BBS_ListNoticeDetail",
      });
    },
  },
};
</script>
